package cloud.demand.app.web.model.stat;

import cloud.demand.app.entity.demand.CdDemandVersionItemDO;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.function.Function;

@Data
public class ChangeDetailReq extends ChangeChartReq {

    /**
     * 对比分组(维度)
     * 取值：
     * yearMonth     需求年月
     * regionName    城市
     * instanceType  实例类型
     * instanceModel 实例规格
     */
    private List<String> compareGroups;

    /**
     * 聚合mapper
     */
    public static Function<CdDemandVersionItemDO, String> groupMapper(List<String> compareGroups) {
        return o -> {
            if (CollectionUtils.isEmpty(compareGroups)) {
                return "";
            }
            StringBuilder sb = new StringBuilder();
            if (compareGroups.contains("yearMonth")) {
                sb.append(o.getDemandYear()).append("-").append(o.getDemandMonth()).append(":");
            }
            if (compareGroups.contains("regionName")) {
                sb.append(o.getRegionName()).append(":");
            }
            if (compareGroups.contains("instanceType")) {
                sb.append(o.getInstanceType()).append(":");
            }
            if (compareGroups.contains("instanceModel")) {
                sb.append(o.getInstanceModel()).append(":");
            }
            return sb.toString();
        };
    }

}
