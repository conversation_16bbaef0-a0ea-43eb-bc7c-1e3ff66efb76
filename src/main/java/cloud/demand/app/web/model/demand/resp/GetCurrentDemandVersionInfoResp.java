package cloud.demand.app.web.model.demand.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;

@Data
public class GetCurrentDemandVersionInfoResp {
    private String demandVersion;
    private String name;
    private String desc;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandImportOpenDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandImportCloseDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandAckOpenDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandAckCloseDate;
    private String forecastFrom;
    private String forecastTo;

    public static GetCurrentDemandVersionInfoResp copy(GetCdDemandVersionResp resp) {
        GetCurrentDemandVersionInfoResp getCurrentDemandVersionInfoResp = new GetCurrentDemandVersionInfoResp();
        getCurrentDemandVersionInfoResp.setDemandVersion(resp.getDemandVersion());
        getCurrentDemandVersionInfoResp.setName(resp.getName());
        getCurrentDemandVersionInfoResp.setDesc(resp.getDesc());
        getCurrentDemandVersionInfoResp.setDemandImportOpenDate(resp.getDemandImportOpenDate());
        getCurrentDemandVersionInfoResp.setDemandImportCloseDate(resp.getDemandImportCloseDate());
        getCurrentDemandVersionInfoResp.setDemandAckOpenDate(resp.getDemandAckOpenDate());
        getCurrentDemandVersionInfoResp.setDemandAckCloseDate(resp.getDemandAckCloseDate());
        getCurrentDemandVersionInfoResp.setForecastFrom(resp.getForecastFromYear()
                + "-" + String.format("%02d", resp.getForecastFromMonth()));
        getCurrentDemandVersionInfoResp.setForecastTo(resp.getForecastToYear()
                + "-" + String.format("%02d", resp.getForecastToMonth()));
        return getCurrentDemandVersionInfoResp;
    }
}
