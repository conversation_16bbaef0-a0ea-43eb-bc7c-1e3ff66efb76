package cloud.demand.app.web.model.demand.req;

import cloud.demand.app.model.demand.DemandImportItem;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class DemandImportReq {

    @NotEmpty(message = "行业名不能为空")
    private String industry;
    @NotEmpty(message = "客户名不能为空")
    private String customerName;

    @NotEmpty(message = "需求不能为空")
    private List<DemandImportItem> item;
}
