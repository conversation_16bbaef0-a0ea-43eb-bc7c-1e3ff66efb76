package cloud.demand.app.web.model.demand.req;

import lombok.Data;

/**
 * 导出excel请求
 */
@Data
public class ExportDetailReq {

    /**分组id，必填，可以由下面3个参数代替*/
    private Long groupId;

    /**当分组id没有时，可以提供下面3个参数：版本，行业，客户名称；然后会统一转成groupId*/
    private String demandVersion;
    private String industry;
    private String customerName;

    /**需求阶段，参见DemandStageEnum，必填*/
    private Integer demandStage;

}
