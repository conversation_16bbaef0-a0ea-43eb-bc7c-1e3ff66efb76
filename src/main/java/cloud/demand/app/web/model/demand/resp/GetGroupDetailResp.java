package cloud.demand.app.web.model.demand.resp;

import cloud.demand.app.entity.demand.AckRecordChangeInfoJSON;
import cloud.demand.app.entity.demand.CdDemandVersionItemDO;
import cloud.demand.app.entity.demandvo.DemandVersionGroupDetailVO;
import cloud.demand.app.enums.DemandStageEnum;
import cloud.demand.app.enums.DemandVersionGroupStatusEnum;
import cloud.demand.app.web.model.demand.DemandItemDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 查询分组详情的返回体
 */
@Data
public class GetGroupDetailResp {

    /**需求版本*/
    private String demandVersion;
    /**分组id*/
    private Long groupId;

    /**行业*/
    private String industry;
    /**客户名称*/
    private String customerName;
    /**分组状态，特别的，当值为-1时表示分组不存在*/
    private Integer status;
    /**分组名称*/
    private String statusName;
    /**对应的版本状态*/
    private Integer versionStatus;

    /**原始需求*/
    private List<DemandItemDTO> originDemand;
    /**需求录入时间*/
    private Date submitTime;
    /**需求录入人员*/
    private String submitUser;

    /**共识需求*/
    private List<DemandItemDTO> ackDemand;
    /**共识时间*/
    private Date ackTime;

    /**原始需求总核心数*/
    private Integer totalOriginCoreNum;
    /**原始需求磁盘数，GB*/
    private Integer totalOriginDiskSize;
    /**原生需求系统盘磁盘数，GB*/
    private Integer totalOriginSystemDiskSize;
    /**共识总核心数*/
    private Integer totalAckCoreNum;
    /**共识总磁盘数，GB*/
    private Integer totalAckDiskSize;
    /**共识需求系统盘磁盘数，GB*/
    private Integer totalAckSystemDiskSize;

    /**每个月份的核心数变化*/
    private List<ChangeYearMonth> changeYearMonth;

    @Data
    public static class ChangeYearMonth {
        private Integer year;
        private Integer month;
        private Integer totalOriginCoreNum;
        private Integer totalAckCoreNum;
        private Integer totalChangeCoreNum;
    }

    /**按年月进行共识*/
    private List<AckYearMonth> ackYearMonth;

    @Data
    public static class AckYearMonth extends ChangeYearMonth {
        /**确认明细*/
        private List<AckDetailItem> ackDetail;
    }

    @Data
    public static class AckDetailItem {
        /**共识变化数量*/
        private Long changeNum;
        /**共识原因*/
        private String changeReason;
        /**备注信息*/
        private String remark;

        public static AckDetailItem from(AckRecordChangeInfoJSON infoJSON) {
            AckDetailItem ackDetailItem = new AckDetailItem();
            ackDetailItem.setChangeNum(infoJSON.getChangeNum());
            ackDetailItem.setChangeReason(infoJSON.getChangeReason());
            ackDetailItem.setRemark(infoJSON.getRemark());
            return ackDetailItem;
        }
    }

    public static GetGroupDetailResp from(DemandVersionGroupDetailVO detailVO) {
        GetGroupDetailResp resp = new GetGroupDetailResp();

        resp.setGroupId(detailVO.getId());
        resp.setDemandVersion(detailVO.getDemandVersion());
        resp.setIndustry(detailVO.getIndustry());
        resp.setCustomerName(detailVO.getCustomerName());
        resp.setStatus(detailVO.getStatus());
        resp.setStatusName(DemandVersionGroupStatusEnum.getNameById(detailVO.getStatus()));
        resp.setVersionStatus(detailVO.getVersion() != null ? detailVO.getVersion().getStatus() : null);

        // 原始需求
        resp.setSubmitTime(detailVO.getSubmitTime());
        resp.setSubmitUser(detailVO.getSubmitUser());
        List<CdDemandVersionItemDO> origins = ListUtils.filter(detailVO.getDemandItems(),
                o -> Objects.equals(o.getDemandStage(), DemandStageEnum.ORIGIN.getStageId()));
        resp.setOriginDemand(ListUtils.transform(origins, DemandItemDTO::from));

        // 共识需求
        resp.setAckTime(detailVO.getAckTime());
        List<CdDemandVersionItemDO> acks = ListUtils.filter(detailVO.getDemandItems(),
                o -> Objects.equals(o.getDemandStage(), DemandStageEnum.ACK.getStageId()));
        resp.setAckDemand(ListUtils.transform(acks, DemandItemDTO::from));

        // 数量
        resp.setTotalOriginCoreNum(NumberUtils.sum(origins, o -> o.getDemandCoreNum()).intValue());
        resp.setTotalOriginDiskSize(NumberUtils.sum(origins, o -> o.getDemandDiskSize()).intValue());
        resp.setTotalOriginSystemDiskSize(NumberUtils.sum(origins, o -> o.getDemandSystemDiskSize()).intValue());
        resp.setTotalAckCoreNum(NumberUtils.sum(acks, o -> o.getDemandCoreNum()).intValue());
        resp.setTotalAckDiskSize(NumberUtils.sum(acks, o -> o.getDemandDiskSize()).intValue());
        resp.setTotalAckSystemDiskSize(NumberUtils.sum(acks, o -> o.getDemandSystemDiskSize()).intValue());

        // 计算那月度算的数量差异
        Map<String, List<CdDemandVersionItemDO>> months = ListUtils.groupBy(
                detailVO.getDemandItems(), o -> o.getDemandYear() + "-" + o.getDemandMonth());
        List<ChangeYearMonth> changeYearMonth = ListUtils.transform(months.values(), o -> {
            ChangeYearMonth m = new ChangeYearMonth();
            m.setYear(o.get(0).getDemandYear());
            m.setMonth(o.get(0).getDemandMonth());
            m.setTotalOriginCoreNum(sumCore(origin(o)));
            m.setTotalAckCoreNum(sumCore(ack(o)));
            m.setTotalChangeCoreNum(m.getTotalAckCoreNum() - m.getTotalOriginCoreNum());
            return m;
        });
        ListUtils.sortAscNullLast(changeYearMonth, o -> o.getYear() + "-" + o.getMonth()); // 排个序
        resp.setChangeYearMonth(changeYearMonth);

        // 共识结果明细
        if (!CollectionUtils.isEmpty(detailVO.getAckRecord())) {
            resp.setAckYearMonth(ListUtils.transform(detailVO.getAckRecord(), o -> {
                AckYearMonth ackYearMonth = new AckYearMonth();
                ackYearMonth.setYear(o.getDemandYear());
                ackYearMonth.setMonth(o.getDemandMonth());

                // 直接从changeYearMonth拿值
                ChangeYearMonth cym = null;
                for (ChangeYearMonth c : changeYearMonth) {
                    if (Objects.equals(c.getYear(), o.getDemandYear()) &&
                            Objects.equals(c.getMonth(), o.getDemandMonth())) {
                        cym = c;
                        break;
                    }
                }
                if (cym != null) {
                    ackYearMonth.setTotalOriginCoreNum(cym.getTotalOriginCoreNum());
                    ackYearMonth.setTotalAckCoreNum(cym.getTotalAckCoreNum());
                    ackYearMonth.setTotalChangeCoreNum(cym.getTotalChangeCoreNum());
                } else {
                    ackYearMonth.setTotalOriginCoreNum(0);
                    ackYearMonth.setTotalAckCoreNum(0);
                    ackYearMonth.setTotalChangeCoreNum(0);
                }

                ackYearMonth.setAckDetail(ListUtils.transform(o.getChangeInfoJson(), AckDetailItem::from));
                return ackYearMonth;
            }));
            ListUtils.sortAscNullLast(resp.getAckYearMonth(), o -> o.getYear() + "-" + o.getMonth()); // 排个序
        }

        return resp;
    }

    private static List<CdDemandVersionItemDO> origin(List<CdDemandVersionItemDO> items) {
        return ListUtils.filter(items, o -> Objects.equals(o.getDemandStage(), DemandStageEnum.ORIGIN.getStageId()));
    }

    private static List<CdDemandVersionItemDO> ack(List<CdDemandVersionItemDO> items) {
        return ListUtils.filter(items, o -> Objects.equals(o.getDemandStage(), DemandStageEnum.ACK.getStageId()));
    }

    private static int sumCore(List<CdDemandVersionItemDO> items) {
        return NumberUtils.sum(items, CdDemandVersionItemDO::getDemandCoreNum).intValue();
    }
}
