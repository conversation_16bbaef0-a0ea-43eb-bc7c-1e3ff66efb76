package cloud.demand.app.entity.rrp;

import cloud.demand.app.entity.cloud_cmongo.MachineDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.Date;

@Data
@ToString
@Table("report_cmongo_machine_snapshot")
public class ReportCmongoMachineSnapshotDO {

    /** 自增主键<br/>Column: [machine_id] */
    @Column(value = "machine_id", isKey = true, isAutoIncrement = true)
    private Integer machineId;

    /** 数据切片日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** region 自建标识，方便查询<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** 腾讯regionid<br/>Column: [region_id] */
    @Column(value = "region_id")
    private Integer regionId;

    /** region中文名<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** machine ip<br/>Column: [machine_ip] */
    @Column(value = "machine_ip")
    private String machineIp;

    /** CPU总容量<br/>Column: [cpu] */
    @Column(value = "cpu")
    private Integer cpu;

    /** 内存总容量<br/>Column: [memory] */
    @Column(value = "memory")
    private Integer memory;

    /** 磁盘总容量<br/>Column: [disk] */
    @Column(value = "disk")
    private Integer disk;

    /** cpu剩余可分配容量<br/>Column: [cpu_free] */
    @Column(value = "cpu_free")
    private Integer cpuFree;

    /** 内存剩余可分配容量<br/>Column: [memory_free] */
    @Column(value = "memory_free")
    private Integer memoryFree;

    /** 磁盘剩余可分配容量<br/>Column: [disk_free] */
    @Column(value = "disk_free")
    private Integer diskFree;

    /** 当前机器实例数<br/>Column: [instances] */
    @Column(value = "instances")
    private Integer instances;

    /** 机器类型<br/>Column: [machine_type] */
    @Column(value = "machine_type")
    private Integer machineType;

    /** 判断是否为存活机器：1是，0否<br/>Column: [is_alive] */
    @Column(value = "is_alive")
    private Integer isAlive;

    /** 记录插入时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 记录更新时间<br/>Column: [update_time] */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    @Column(value = "worker_version")
    private String workerVersion;

    /** set id<br/>Column: [setid] */
    @Column(value = "setid")
    private Integer setid;

    @Column(value = "setname")
    private String setname;

    @Column(value = "idc_parent_id")
    private Integer idcParentId;

    public static ReportCmongoMachineSnapshotDO transform(MachineDO machineDO, String statTime){
        ReportCmongoMachineSnapshotDO reportCmongoMachineSnapshotDO = new ReportCmongoMachineSnapshotDO();
        reportCmongoMachineSnapshotDO.setStatTime(DateUtils.parseLocalDate(statTime));
        reportCmongoMachineSnapshotDO.setRegion(machineDO.getRegion());
        reportCmongoMachineSnapshotDO.setRegionId(machineDO.getRegionId());
        reportCmongoMachineSnapshotDO.setRegionName(machineDO.getRegionName());
        reportCmongoMachineSnapshotDO.setMachineIp(machineDO.getMachineIp());
        reportCmongoMachineSnapshotDO.setCpu(machineDO.getCpu());
        reportCmongoMachineSnapshotDO.setMemory(machineDO.getMemory());
        reportCmongoMachineSnapshotDO.setDisk(machineDO.getDisk());
        reportCmongoMachineSnapshotDO.setCpuFree(machineDO.getCpuFree());
        reportCmongoMachineSnapshotDO.setMemoryFree(machineDO.getMemoryFree());
        reportCmongoMachineSnapshotDO.setDiskFree(machineDO.getDiskFree());
        reportCmongoMachineSnapshotDO.setInstances(machineDO.getInstances());
        reportCmongoMachineSnapshotDO.setMachineType(machineDO.getMachineType());
        reportCmongoMachineSnapshotDO.setIsAlive(machineDO.getIsAlive());
        reportCmongoMachineSnapshotDO.setWorkerVersion(machineDO.getWorkerVersion());
        reportCmongoMachineSnapshotDO.setSetid(machineDO.getSetid());
        reportCmongoMachineSnapshotDO.setSetname(machineDO.getSetname());
        reportCmongoMachineSnapshotDO.setIdcParentId(machineDO.getIdcParentId());
        return reportCmongoMachineSnapshotDO;

    }

}