package cloud.demand.app.entity.rrp;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Data
@ToString
@Table("rrp_config")
public class RRPVersionDO {

    @Column(value = "id", isAutoIncrement = true)
    private Integer id;

    @Column(value = "startTime")
    private Date startTime;

    @Column(value = "endTime")
    private Date endTime;

    @Column(value = "required_month")
    private String requiredMonthText;

    @Column(value = "optional_month")
    private String optionalMonthText;


    /**
     * 自动延期的月份<br/>Column: [auto_extend_month]
     */
    @Column(value = "auto_extend_month")
    private String autoExtendMonth;

    @Column(value = "description")
    private String description;

    /**
     * 0是1否<br/>Column: [status]
     */
    @Column(value = "status")
    private Integer status;

    @Column(value = "updateTime")
    private Date updateTime;

    @Column(value = "plan_version")
    private String planVersion;

    /**
     * 是否通知产品录单人<br/>Column: [is_notify]
     */
    @Column(value = "is_notify")
    private Integer isNotify;

    /**
     * 是否自动根据执行量修复预测量<br/>Column: [is_fix_executed_num]
     */
    @Column(value = "is_fix_executed_num")
    private Integer isFixExecutedNum;

    @Column(value = "industry_version")
    private String industryVersion;

    public String getVersionDateStr(){
        return LocalDate.parse(planVersion.substring(2),  DateTimeFormatter.ofPattern("yyyyMMdd")).toString();
    }
}