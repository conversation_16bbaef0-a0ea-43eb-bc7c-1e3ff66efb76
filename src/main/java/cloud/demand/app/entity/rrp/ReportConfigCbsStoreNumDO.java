package cloud.demand.app.entity.rrp;

import cloud.demand.app.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 物理机cbs到tb数的映射
 */
@Data
@ToString
@Table("report_config_cbs_store_num")
public class ReportConfigCbsStoreNumDO extends BaseDO {

    /** 物理机机型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 折算后的tb数<br/>Column: [store_num] */
    @Column(value = "store_num")
    private BigDecimal storeNum;

}
