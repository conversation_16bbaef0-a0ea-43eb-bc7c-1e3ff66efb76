package cloud.demand.app.entity.rrp;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("product_info")
public class RRPDemandItemWithVersionDO {

    @Column(value = "id", isAutoIncrement = true)
    private Integer id;

    @Column(value = "flowId")
    private Integer flowId;

    @Column(value = "product")
    private String planProduct;

    @Column(value = "mod_business_type_name")
    private String moduleBusinessTypeName;

    @Column(value = "region")
    private String region;

    @Column(value = "zone")
    private String zone;

    @Column(value = "campus")
    private String campus;

    @Column(value = "deviceType")
    private String deviceType;

    @Column(value = "month")
    private String yearMonthText;

    @Column(value = "amount")
    private Integer amount;

    @Column(value = "proj_set_name")
    private String projSetName;

    @Column(value = "updateTime")
    private Date updateTime;

    @Column(value = "updator")
    private String updator;

    @Column(value = "backup")
    private String backup;

    @Column(value = "customer_name")
    private String customerName;

    @Column(value = "product_remark")
    private String productRemark;

    @Column(value = "resource_remark")
    private String resourceRemark;

    @Column(value = "curplan_adjust")
    private Integer curplanAdjust;

    @Column(value = "assigned_product")
    private String assignedProduct;

    @Column(value = "industry")
    private String industry;

    /**
     * 原因归类<br/>Column: [reason_type]
     */
    @Column(value = "reason_type")
    private String reasonType;

    /**
     * 原因细项<br/>Column: [reason]
     */
    @Column(value = "reason")
    private String reason;

    @Column(value = "version_id")
    private Integer versionId;

    @Column(value = "demand_type")
    private String demandType;

    @Column(value = "auto_append_id")
    private Long autoAppendId;

    @Column(value = "obs_project_type")
    private String obsProjectType;

    @Column(value = "week")
    private Integer week;

    public static String getUnionWhere() {
        return " (p.month, p.product, p.mod_business_type_name, "
                + "       p.region, p.zone, p.campus, "
                + "       p.deviceType, p.proj_set_name, "
                + "       p.industry, p.customer_name, "
                + "       p.reason_type, p.reason, p.demand_type, "
                + "       if(p.auto_append_id > 0, true, false), "
                + "       p.flowId, p.obs_project_type, p.week) ";
    }

    public Boolean isAutoAppend() {
        return autoAppendId != null && autoAppendId > 0;
    }

    public String strIsAutoAppend() {
        return String.valueOf(isAutoAppend());
    }

    public String matchRealTimeKey() {
        return String.join("^", yearMonthText,
                planProduct, moduleBusinessTypeName,
                region, zone, campus == null ? "" : campus, deviceType, projSetName,
                industry, customerName, reasonType, reason, demandType,
                strIsAutoAppend(), obsProjectType, String.valueOf(week));
    }

    private String takeAsSqlStr(String value) {
        if (value == null) {
            return null;
        }
        return "'" + value + "'";
    }

    public String sameVersionSqlPart() {
        return "(" + String.join(",", takeAsSqlStr(yearMonthText),
                takeAsSqlStr(planProduct), takeAsSqlStr(moduleBusinessTypeName),
                takeAsSqlStr(region), takeAsSqlStr(zone), takeAsSqlStr(campus),
                takeAsSqlStr(deviceType), takeAsSqlStr(projSetName),
                takeAsSqlStr(industry), takeAsSqlStr(customerName),
                takeAsSqlStr(reasonType), takeAsSqlStr(reason), takeAsSqlStr(demandType),
                strIsAutoAppend(),
                String.valueOf(flowId), takeAsSqlStr(obsProjectType), String.valueOf(week)) + ")";
    }

    public String matchRealSqlPart() {
        return "(" + String.join(",", takeAsSqlStr(yearMonthText),
                takeAsSqlStr(planProduct), takeAsSqlStr(moduleBusinessTypeName),
                takeAsSqlStr(region), takeAsSqlStr(zone), takeAsSqlStr(campus),
                takeAsSqlStr(deviceType), takeAsSqlStr(projSetName),
                takeAsSqlStr(industry), takeAsSqlStr(customerName),
                takeAsSqlStr(reasonType), takeAsSqlStr(reason), takeAsSqlStr(demandType),
                strIsAutoAppend(), takeAsSqlStr(obsProjectType), String.valueOf(week)) + ")";
    }
}
