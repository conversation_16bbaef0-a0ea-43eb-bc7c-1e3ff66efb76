package cloud.demand.app.entity.rrp;

// package a.b.c;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 采购单业务产品对应的规划产品策略表
 */
@Data
@ToString
@Table("report_config_purchase_product")
public class ReportConfigPurchaseProductDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    /** 业务分组<br/>Column: [biz_group] */
    @Column(value = "biz_group")
    private String bizGroup;

    /** 规划产品<br/>Column: [plan_product] */
    @Column(value = "plan_product")
    private String planProduct;

}