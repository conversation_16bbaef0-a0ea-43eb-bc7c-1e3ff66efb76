package cloud.demand.app.entity;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.util.Date;

@Data
public class BaseDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

}
