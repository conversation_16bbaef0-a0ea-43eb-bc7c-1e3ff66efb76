package cloud.demand.app.entity.ck_cloud_demand;

// package a.b.c;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("ads_tcres_demand_annual_excueted_report")
public class AdsTcresAnnualDemandReportDO {

    /** 分区键，每天一版本<br/>Column: [version] */
    @Column(value = "version")
    private String version;

    /** ym*/
    @Column(value = "p_ym")
    private String yearMonth;

    /** date*/
    @Column(value = "p_date")
    private String date;

    /** 大类<br/>Column: [dim_big_class] */
    @Column(value = "dim_big_class")
    private String dimBigClass;

    /** 产品大类<br/>Column: [dim_product_class] */
    @Column(value = "dim_product_class")
    private String dimProductClass;

    /** 设备类型<br/>Column: [dim_device_type] */
    @Column(value = "dim_device_type")
    private String dimDeviceType;

    /** 实例类型<br/>Column: [dim_instance_type] */
    @Column(value = "dim_instance_type")
    private String dimInstanceType;

    /** CPU平台<br/>Column: [dim_cpu_type] */
    @Column(value = "dim_cpu_type")
    private String dimCpuType;

    /** 网卡<br/>Column: [dim_net_type] */
    @Column(value = "dim_net_type")
    private String dimNetType;

    /** 地域类型<br/>Column: [dim_region] */
    @Column(value = "dim_region")
    private String dimRegion;

    /** 地域<br/>Column: [dim_region_class] */
    @Column(value = "dim_region_class")
    private String dimRegionClass;

    /** 行业<br/>Column: [dim_industry] */
    @Column(value = "dim_industry")
    private String dimIndustry;

    /** 客户<br/>Column: [dim_customer] */
    @Column(value = "dim_customer")
    private String dimCustomer;

    @Column(value = "dim_plan_product")
    private String dimPlanProduct;

    @Column(value = "dim_reason1")
    private String dimReason1;

    @Column(value = "dim_reason2")
    private String dimReason2;

    @Column(value = "dim_supply_way")
    private String dimSupplyWay;

    /** 指标<br/>Column: [p_index] */
    @Column(value = "p_index")
    private String pIndex;

    /** 部门<br/>Column: [num] */
    @Column(value = "num")
    private BigDecimal num;

    /** 产品<br/>Column: [cores] */
    @Column(value = "cores")
    private BigDecimal cores;

    /** 原始数据id（不同数据源。id可能会重复)<br/>Column: [ori_id] */
    @Column(value = "ori_id")
    private String oriId;

    @Column(value = "ori_data")
    private String oriData;

}