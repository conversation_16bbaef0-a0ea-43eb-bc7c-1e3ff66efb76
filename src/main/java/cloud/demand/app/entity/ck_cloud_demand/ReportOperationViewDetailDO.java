package cloud.demand.app.entity.ck_cloud_demand;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
@Table("report_operation_view_detail")
public class ReportOperationViewDetailDO {

    /** 数据日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 产品类型<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** 库存材料类型(好差呆)<br/>Column: [material_type] */
    @Column(value = "material_type")
    private String materialType;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域名<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域名<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区名<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 机型族<br/>Column: [device_family] */
    @Column(value = "device_family")
    private String deviceFamily;

    /** CPU规格<br/>Column: [cpu_category] */
    @Column(value = "cpu_category")
    private String cpuCategory;

    /** 设备逻辑核心数<br/>Column: [cpu_logic_core] */
    @Column(value = "cpu_logic_core")
    private Integer cpuLogicCore;

    /** 最新总库存<br/>Column: [inv_newest_total] */
    @Column(value = "inv_newest_total", insertValueScript = "0")
    private BigDecimal invNewestTotal;

    /** 最新线上库存<br/>Column: [inv_newest_online] */
    @Column(value = "inv_newest_online", insertValueScript = "0")
    private BigDecimal invNewestOnline;

    /** 最新线下库存<br/>Column: [inv_newest_offline] */
    @Column(value = "inv_newest_offline", insertValueScript = "0")
    private BigDecimal invNewestOffline;

    /** m-1月库存量<br/>Column: [inv_m_1] */
    @Column(value = "inv_m_1", insertValueScript = "0")
    private BigDecimal invM1;

    /** m-2月库存量<br/>Column: [inv_m_2] */
    @Column(value = "inv_m_2", insertValueScript = "0")
    private BigDecimal invM2;

    /** m-3月库存量<br/>Column: [inv_m_3] */
    @Column(value = "inv_m_3", insertValueScript = "0")
    private BigDecimal invM3;

    /** m-4月库存量<br/>Column: [inv_m_4] */
    @Column(value = "inv_m_4", insertValueScript = "0")
    private BigDecimal invM4;

    /** m-5月库存量<br/>Column: [inv_m_5] */
    @Column(value = "inv_m_5", insertValueScript = "0")
    private BigDecimal invM5;

    /** m-6月库存量<br/>Column: [inv_m_6] */
    @Column(value = "inv_m_6", insertValueScript = "0")
    private BigDecimal invM6;

    /** m-7月库存量<br/>Column: [inv_m_7] */
    @Column(value = "inv_m_7", insertValueScript = "0")
    private BigDecimal invM7;

    /** m-8月库存量<br/>Column: [inv_m_8] */
    @Column(value = "inv_m_8", insertValueScript = "0")
    private BigDecimal invM8;

    /** m-9月库存量<br/>Column: [inv_m_9] */
    @Column(value = "inv_m_9", insertValueScript = "0")
    private BigDecimal invM9;

    /** m-10月库存量<br/>Column: [inv_m_10] */
    @Column(value = "inv_m_10", insertValueScript = "0")
    private BigDecimal invM10;

    /** m-11月库存量<br/>Column: [inv_m_11] */
    @Column(value = "inv_m_11", insertValueScript = "0")
    private BigDecimal invM11;

    /** m-12月库存量<br/>Column: [inv_m_12] */
    @Column(value = "inv_m_12", insertValueScript = "0")
    private BigDecimal invM12;

    /** m-1月销售量<br/>Column: [sale_m_1] */
    @Column(value = "sale_m_1", insertValueScript = "0")
    private BigDecimal saleM1;

    /** m-2月销售量<br/>Column: [sale_m_2] */
    @Column(value = "sale_m_2", insertValueScript = "0")
    private BigDecimal saleM2;

    /** m-3月销售量<br/>Column: [sale_m_3] */
    @Column(value = "sale_m_3", insertValueScript = "0")
    private BigDecimal saleM3;

    /** m-4月销售量<br/>Column: [sale_m_4] */
    @Column(value = "sale_m_4", insertValueScript = "0")
    private BigDecimal saleM4;

    /** m-5月销售量<br/>Column: [sale_m_5] */
    @Column(value = "sale_m_5", insertValueScript = "0")
    private BigDecimal saleM5;

    /** m-6月销售量<br/>Column: [sale_m_6] */
    @Column(value = "sale_m_6", insertValueScript = "0")
    private BigDecimal saleM6;

    /** m-7月销售量<br/>Column: [sale_m_7] */
    @Column(value = "sale_m_7", insertValueScript = "0")
    private BigDecimal saleM7;

    /** m-8月销售量<br/>Column: [sale_m_8] */
    @Column(value = "sale_m_8", insertValueScript = "0")
    private BigDecimal saleM8;

    /** m-9月销售量<br/>Column: [sale_m_9] */
    @Column(value = "sale_m_9", insertValueScript = "0")
    private BigDecimal saleM9;

    /** m-10月销售量<br/>Column: [sale_m_10] */
    @Column(value = "sale_m_10", insertValueScript = "0")
    private BigDecimal saleM10;

    /** m-11月销售量<br/>Column: [sale_m_11] */
    @Column(value = "sale_m_11", insertValueScript = "0")
    private BigDecimal saleM11;

    /** m-12月销售量<br/>Column: [sale_m_12] */
    @Column(value = "sale_m_12", insertValueScript = "0")
    private BigDecimal saleM12;

    /** m-1周需求量<br/>Column: [demand_w_1] */
    @Column(value = "demand_w_1", insertValueScript = "0")
    private BigDecimal demandW1;

    /** m-2周需求量<br/>Column: [demand_w_2] */
    @Column(value = "demand_w_2", insertValueScript = "0")
    private BigDecimal demandW2;

    /** m-3周需求量<br/>Column: [demand_w_3] */
    @Column(value = "demand_w_3", insertValueScript = "0")
    private BigDecimal demandW3;

    /** m-4周需求量<br/>Column: [demand_w_4] */
    @Column(value = "demand_w_4", insertValueScript = "0")
    private BigDecimal demandW4;

    /** m-5周需求量<br/>Column: [demand_w_5] */
    @Column(value = "demand_w_5", insertValueScript = "0")
    private BigDecimal demandW5;

    /** m-6周需求量<br/>Column: [demand_w_6] */
    @Column(value = "demand_w_6", insertValueScript = "0")
    private BigDecimal demandW6;

    /** m-7周需求量<br/>Column: [demand_w_7] */
    @Column(value = "demand_w_7", insertValueScript = "0")
    private BigDecimal demandW7;

    /** m-8周需求量<br/>Column: [demand_w_8] */
    @Column(value = "demand_w_8", insertValueScript = "0")
    private BigDecimal demandW8;

    /** m-9周需求量<br/>Column: [demand_w_9] */
    @Column(value = "demand_w_9", insertValueScript = "0")
    private BigDecimal demandW9;

    /** m-10周需求量<br/>Column: [demand_w_10] */
    @Column(value = "demand_w_10", insertValueScript = "0")
    private BigDecimal demandW10;

    /** m-11周需求量<br/>Column: [demand_w_11] */
    @Column(value = "demand_w_11", insertValueScript = "0")
    private BigDecimal demandW11;

    /** m-12周需求量<br/>Column: [demand_w_12] */
    @Column(value = "demand_w_12", insertValueScript = "0")
    private BigDecimal demandW12;

    /** m-13周需求量<br/>Column: [demand_w_13] */
    @Column(value = "demand_w_13", insertValueScript = "0")
    private BigDecimal demandW13;

    public String getKey(){
        return productType + "-" + materialType + "-" + customhouseTitle + "-" +
                areaName + "-" + regionName + "-" + zoneName + "-" + deviceType;
    }

    public static ReportOperationViewDetailDO merge(ReportOperationViewDetailDO do1, ReportOperationViewDetailDO do2){
        ReportOperationViewDetailDO reportOperationViewDetailDO = new ReportOperationViewDetailDO();
        reportOperationViewDetailDO.setStatTime(do1.getStatTime());
        reportOperationViewDetailDO.setProductType(do1.getProductType());
        reportOperationViewDetailDO.setMaterialType(do1.getMaterialType());
        reportOperationViewDetailDO.setCustomhouseTitle(do1.getCustomhouseTitle());
        reportOperationViewDetailDO.setAreaName(do1.getAreaName());
        reportOperationViewDetailDO.setRegionName(do1.getRegionName());
        reportOperationViewDetailDO.setZoneName(do1.getZoneName());
        reportOperationViewDetailDO.setDeviceType(do1.getDeviceType());
        reportOperationViewDetailDO.setDeviceFamily(do1.getDeviceFamily());
        reportOperationViewDetailDO.setCpuCategory(do1.getCpuCategory());
        reportOperationViewDetailDO.setCpuLogicCore(do1.getCpuLogicCore());
        reportOperationViewDetailDO.setInvNewestTotal(add(do1.getInvNewestTotal(), do2.getInvNewestTotal()));
        reportOperationViewDetailDO.setInvNewestOnline(add(do1.getInvNewestOnline(), do2.getInvNewestOnline()));
        reportOperationViewDetailDO.setInvNewestOffline(add(do1.getInvNewestOffline(), do2.getInvNewestOffline()));
        reportOperationViewDetailDO.setInvM1(add(do1.getInvM1(), do2.getInvM1()));
        reportOperationViewDetailDO.setInvM2(add(do1.getInvM2(), do2.getInvM2()));
        reportOperationViewDetailDO.setInvM3(add(do1.getInvM3(), do2.getInvM3()));
        reportOperationViewDetailDO.setInvM4(add(do1.getInvM4(), do2.getInvM4()));
        reportOperationViewDetailDO.setInvM5(add(do1.getInvM5(), do2.getInvM5()));
        reportOperationViewDetailDO.setInvM6(add(do1.getInvM6(), do2.getInvM6()));
        reportOperationViewDetailDO.setInvM7(add(do1.getInvM7(), do2.getInvM7()));
        reportOperationViewDetailDO.setInvM8(add(do1.getInvM8(), do2.getInvM8()));
        reportOperationViewDetailDO.setInvM9(add(do1.getInvM9(), do2.getInvM9()));
        reportOperationViewDetailDO.setInvM10(add(do1.getInvM10(), do2.getInvM10()));
        reportOperationViewDetailDO.setInvM11(add(do1.getInvM11(), do2.getInvM11()));
        reportOperationViewDetailDO.setInvM12(add(do1.getInvM12(), do2.getInvM12()));
        reportOperationViewDetailDO.setSaleM1(add(do1.getSaleM1(), do2.getSaleM1()));
        reportOperationViewDetailDO.setSaleM2(add(do1.getSaleM2(), do2.getSaleM2()));
        reportOperationViewDetailDO.setSaleM3(add(do1.getSaleM3(), do2.getSaleM3()));
        reportOperationViewDetailDO.setSaleM4(add(do1.getSaleM4(), do2.getSaleM4()));
        reportOperationViewDetailDO.setSaleM5(add(do1.getSaleM5(), do2.getSaleM5()));
        reportOperationViewDetailDO.setSaleM6(add(do1.getSaleM6(), do2.getSaleM6()));
        reportOperationViewDetailDO.setSaleM7(add(do1.getSaleM7(), do2.getSaleM7()));
        reportOperationViewDetailDO.setSaleM8(add(do1.getSaleM8(), do2.getSaleM8()));
        reportOperationViewDetailDO.setSaleM9(add(do1.getSaleM9(), do2.getSaleM9()));
        reportOperationViewDetailDO.setSaleM10(add(do1.getSaleM10(), do2.getSaleM10()));
        reportOperationViewDetailDO.setSaleM11(add(do1.getSaleM11(), do2.getSaleM11()));
        reportOperationViewDetailDO.setSaleM12(add(do1.getSaleM12(), do2.getSaleM12()));
        reportOperationViewDetailDO.setDemandW1(add(do1.getDemandW1(), do2.getDemandW1()));
        reportOperationViewDetailDO.setDemandW2(add(do1.getDemandW2(), do2.getDemandW2()));
        reportOperationViewDetailDO.setDemandW3(add(do1.getDemandW3(), do2.getDemandW3()));
        reportOperationViewDetailDO.setDemandW4(add(do1.getDemandW4(), do2.getDemandW4()));
        reportOperationViewDetailDO.setDemandW5(add(do1.getDemandW5(), do2.getDemandW5()));
        reportOperationViewDetailDO.setDemandW6(add(do1.getDemandW6(), do2.getDemandW6()));
        reportOperationViewDetailDO.setDemandW7(add(do1.getDemandW7(), do2.getDemandW7()));
        reportOperationViewDetailDO.setDemandW8(add(do1.getDemandW8(), do2.getDemandW8()));
        reportOperationViewDetailDO.setDemandW9(add(do1.getDemandW9(), do2.getDemandW9()));
        reportOperationViewDetailDO.setDemandW10(add(do1.getDemandW10(), do2.getDemandW10()));
        reportOperationViewDetailDO.setDemandW11(add(do1.getDemandW11(), do2.getDemandW11()));
        reportOperationViewDetailDO.setDemandW12(add(do1.getDemandW12(), do2.getDemandW12()));
        reportOperationViewDetailDO.setDemandW13(add(do1.getDemandW13(), do2.getDemandW13()));
        return reportOperationViewDetailDO;

    }

    /**
     * 加法运算
     */
    private static BigDecimal add(BigDecimal... num){
        if (num == null){
            return BigDecimal.ZERO;
        }
        BigDecimal ret = BigDecimal.ZERO;
        for (BigDecimal bigDecimal : num) {
            ret = ret.add(bigDecimal == null ? BigDecimal.ZERO : bigDecimal);
        }
        return ret;
    }

}

