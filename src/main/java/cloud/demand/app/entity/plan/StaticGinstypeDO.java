package cloud.demand.app.entity.plan;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 机型配置信息
 */
@Data
@ToString
@Table("static_ginstype")
public class StaticGinstypeDO {

    /** 实例规格<br/>Column: [ginstype] */
    @Column(value = "ginstype", isKey = true)
    private String ginstype;

    /** CPU，单位：0.01核<br/>Column: [cpu] */
    @Column(value = "cpu")
    private Long cpu;

    /** 内存，单位：MB<br/>Column: [mem] */
    @Column(value = "mem")
    private Long mem;

    /** GPU，单位：vgpu卡数<br/>Column: [gpu] */
    @Column(value = "gpu")
    private Long gpu;

    /** fpga，单位：卡数<br/>Column: [fpga] */
    @Column(value = "fpga")
    private Long fpga;

    /** 本地盘数目<br/>Column: [storage_block] */
    @Column(value = "storage_block")
    private Long storageBlock;

    /** 网卡<br/>Column: [network_card] */
    @Column(value = "network_card")
    private Long networkCard;

    /** 带宽<br/>Column: [max_bandwidth] */
    @Column(value = "max_bandwidth")
    private Double maxBandwidth;

    /** 吞吐<br/>Column: [pps] */
    @Column(value = "pps")
    private Long pps;

    @Column(value = "frequency")
    private String frequency;

    @Column(value = "cpu_model_name")
    private String cpuModelName;

    @Column(value = "language")
    private String language;

    @Column(value = "remark")
    private String remark;

    @Column(value = "remark_en")
    private String remarkEn;

    /** 子机机型所属“科”。<br/>Column: [ginsfamily] */
    @Column(value = "ginsfamily")
    private String ginsfamily;

    /** 机器类型：-1-VSVPN等旧时代无instance_type子机，0-普通子机，1-CDH母机，本表中不可能出现1<br/>Column: [machine_type] */
    @Column(value = "machine_type")
    private Long machineType;

    /** 虚拟GPU与真实GPU的比值<br/>Column: [gpuratio] */
    @Column(value = "gpuratio")
    private Long gpuratio;

    /** 维护方式：\n0-手工维护。\n1-表示从gather.hourly_c_vm_static_instance_configsg获取。\n2-表示从gather.hourly_t_exclusive_host_rule获取。\n3-表示从gather.hourly_cvm_type_config。          \n目前没有定义其他值，可以认为其他值都是自动维护的<br/>Column: [maintenance_type] */
    @Column(value = "maintenance_type")
    private Long maintenanceType;

    /** 子机机型所属“科”中文名称。<br/>Column: [ginsfamily_name] */
    @Column(value = "ginsfamily_name")
    private String ginsfamilyName;

    /** 子机机型所属“门”<br/>Column: [ginsphylum] */
    @Column(value = "ginsphylum")
    private String ginsphylum;

    /** 子机机型所属“科”的对应的子机机型所属“门”的代次。0表示代次未知，或者子机机型所属“门”本身无代次概念。<br/>Column: [generation] */
    @Column(value = "generation")
    private Long generation;

    /** 上线日期<br/>Column: [birthday] */
    @Column(value = "birthday")
    private Date birthday;

    /** 每个GPU卡有多少GPU芯片数，如果是0，表示不是GPU机型<br/>Column: [gpu_chip_per_card] */
    @Column(value = "gpu_chip_per_card")
    private Long gpuChipPerCard;

    /** 子机机型所属“界”<br/>Column: [ginskingdom] */
    @Column(value = "ginskingdom")
    private String ginskingdom;

    /** CPU类别：INTEL、AMD<br/>Column: [cpu_type] */
    @Column(value = "cpu_type")
    private String cpuType;

    /** 子机机型所属“界”中文名称。<br/>Column: [ginskingdom_name] */
    @Column(value = "ginskingdom_name")
    private String ginskingdomName;

    /** 所属业务类型<br/>Column: [biztype] */
    @Column(value = "biztype")
    private String biztype;

    /** 所属业务类型名称<br/>Column: [biztype_name] */
    @Column(value = "biztype_name")
    private String biztypeName;

    /** 统计时间，参见《同步数据管理规范》<br/>Column: [stattime] */
    @Column(value = "stattime")
    private Date stattime;

    /** 同步时间，参见《指标数据管理规范》<br/>Column: [synctime] */
    @Column(value = "synctime")
    private Date synctime;

}