package cloud.demand.app.entity.shuttle;

import cloud.demand.app.common.config.DynamicProperties;
import cloud.demand.app.modules.cvmjxc.model.BusinessTypeDTO;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.json.JSON;
import lombok.Data;
import lombok.ToString;
import org.springframework.util.StringUtils;
import yunti.boot.exception.ITException;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 资源中台采购单
 */
@Data
@ToString
@Table("device_apply")
public class DeviceApplyDO {

    @Column(value = "id", isKey = true)
    private String id;

    @Column(value = "quotaId", isKey = true)
    private String quotaId;

    @Column(value = "sub_id", isKey = true)
    private String subId;

    @Column(value = "order_type", isKey = true)
    private Integer orderType;

    @Column(value = "setId")
    private String setId;

    @Column(value = "creator")
    private String creator;

    @Column(value = "dept")
    private String dept;

    @Column(value = "business1")
    private String business1;

    @Column(value = "business1Id")
    private Integer business1Id;

    @Column(value = "module_id")
    private String moduleId;

    @Column(value = "module_name")
    private String moduleName;

    @Column(value = "mod_business_type_name")
    private String modBusinessType;

    @Column(value = "zone_id")
    private Integer zoneId;

    @Column(value = "zone")
    private String zone;

    @Column(value = "business2")
    private String business2;

    @Column(value = "business2Id")
    private Integer business2Id;

    @Column(value = "business3")
    private String business3;

    @Column(value = "business3Id")
    private Integer business3Id;

    @Column(value = "plan_type")
    private Integer planType;

    @Column(value = "plan_type_name")
    private String planTypeName;

    @Column(value = "plan_dept_id")
    private Integer planDeptId;

    @Column(value = "plan_dept")
    private String planDept;

    @Column(value = "plan_product_id")
    private Integer planProductId;

    @Column(value = "plan_product")
    private String planProduct;

    @Column(value = "deviceType")
    private String deviceType;

    @Column(value = "num")
    private Integer num;

    @Column(value = "status")
    private Integer status;

    @Column(value = "createTime")
    private Date createTime;

    @Column(value = "region")
    private String region;

    @Column(value = "city")
    private String city;

    @Column(value = "purpose")
    private String purpose;

    @Column(value = "expect_delivery_date")
    private Date expectDeliveryDate;

    @Column(value = "product")
    private String product;

    @Column(value = "memo")
    private String memo;

    @Column(value = "deliveryTime")
    private Timestamp deliveryTime;

    @Column(value = "plan_month")
    private String planMonth;

    @Column(value = "plan_use")
    private String planUse;

    @Column(value = "industry")
    private String industry;

    @Column(value = "pur_reason")
    private String purReason;

    @Column("customer_name")
    private String customerName;

    @Column("customer_type")
    private String customerType;

    @Column(value = "totalNum")
    private Integer totalNum;

    @Column(value = "assetIds")
    private String assetIds;

    @Column(value = "DeptAuthTime")
    private Date deptAuthTime;

    @Column(value = "demand_type")
    private String demandType;

    @Column(value = "proj_set_name")
    private String obsProjectType;

    /**
     * 预计退回时间（短租字段）<br/>Column: [plan_return_date]
     */
    @Column(value = "plan_return_date")
    private LocalDate planReturnDate;

    /**
     * 关联的云霄预约单<br/>Column: [ppl_orders]
     */
    @Column(value = "ppl_orders")
    private String pplOrders;

    /**
     * 不是 DB 字段，只有 13周扣预测的时候用到
     */
    private Integer demandWeek;

    public static DeviceApplyDO copy(DeviceApplyDO source) {
        return JSON.clone(source);
    }

    private String takeAsSqlStr(String value) {
        if (value == null) {
            return null;
        }
        return "'" + value + "'";
    }

    public String matchRealTimeKey(Boolean isAutoAppend, String reasonType,
            String projectType) {
        return String.join("^", planMonth,
                planProduct, modBusinessType,
                region, city, zone, deviceType, projectType,
                industry, customerName, reasonType, purReason, demandType,
                String.valueOf(isAutoAppend), obsProjectType, String.valueOf(demandWeek));
    }

    public String matchRealSqlPart(Boolean isAutoAppend, Map<String, String> demandReasonConfigMap,
            List<BusinessTypeDTO> businessTypeDTOS) {
        String reasonType = demandReasonConfigMap.getOrDefault(purReason, "系统任务");
        String projSetName = "非自研上云";
        if (businessTypeDTOS != null && businessTypeDTOS.size() > 0) {
            for (BusinessTypeDTO businessTypeDTO : businessTypeDTOS) {
                if (businessTypeDTO.isZysy(planProduct, this.business1, this.business2, this.business3)) {
                    projSetName = "自研上云";
                    break;
                }
            }
        }
        return "(" + String.join(",", takeAsSqlStr(planMonth),
                takeAsSqlStr(planProduct), takeAsSqlStr(modBusinessType),
                takeAsSqlStr(region), takeAsSqlStr(city), takeAsSqlStr(zone),
                takeAsSqlStr(deviceType), takeAsSqlStr(projSetName),
                takeAsSqlStr(industry), takeAsSqlStr(customerName),
                takeAsSqlStr(reasonType), takeAsSqlStr(purReason), takeAsSqlStr(demandType),
                String.valueOf(isAutoAppend), takeAsSqlStr(obsProjectType), String.valueOf(demandWeek)) + ")";
    }

    public Object[] rQuerySqlArgs() {
        return new Object[]{planMonth, planProduct,
                deviceType};
    }

    public String rLevelKey(int level, Map<String, String> regionCountryMap,
                            Map<String, String> obsProjectType2MixProjectTypeMap) {
        String region = getRegion();
        if (DynamicProperties.rrpERPCountryConverterMap().containsKey(region)) {
            region = DynamicProperties.rrpERPCountryConverterMap().get(region);
        }
        String country = regionCountryMap.get(region);
        if (StringUtils.isEmpty(country)) {
            throw new ITException("region[" + region + "]找不到对应的地域类型");
        }
        if (country.equals("国内")) {
            country = "中国内地";
        } else {
            // 海外就用自己的region
            country = region;
        }
        // OBS项目类型要先混合
        String mixProjectType;
        // （kaijiazhang说：实际上不存在采购buff的采购单）如有：采购buff的采购单扣在其他项目下，实现采购buff的预测不被扣除
        if ("采购buff".equals(obsProjectType) || "采购BUFF".equals(obsProjectType)) {
            mixProjectType = "其他";
        } else {
            mixProjectType = obsProjectType2MixProjectTypeMap.getOrDefault(obsProjectType, obsProjectType);
        }
        List<String> parts = Lists.newArrayList(planMonth, planProduct, deviceType, country, mixProjectType);
        // level从小到大变松散
        switch (level) {
            case 0:
                parts.add(obsProjectType);
            case 1:
                parts.add(region);
            case 2:
                parts.add(city);
            case 3:
                parts.add(zone);
            case 4:
                parts.add(String.valueOf(demandWeek));
            case 5:
                parts.add(industry);
            case 6:
                parts.add(customerName);
            default:
                break;
        }
        return String.join("@", parts);
    }
}