package cloud.demand.app.entity.demand;


import cloud.demand.app.enums.TaskRunLogLevelEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import org.slf4j.MDC;

@NoArgsConstructor
@Data
@ToString
@Table("cd_task_run_log")
public class CdTaskRunLogDO {

    /** 自增 id<br/>Column: [id] */
    @Column(value = "id", isAutoIncrement = true)
    private Long id;

    /** 任务开始时的时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 任务名称<br/>Column: [task_name] */
    @Column(value = "task_name", maxStringLength = 255)
    private String taskName;

    /** 产生error的具体方法名称<br/>Column: [method_name] */
    @Column(value = "method_name", maxStringLength = 255)
    private String methodName;

    /** 任务具体报错信息<br/>Column: [error_msg] */
    @Column(value = "error_msg", maxStringLength = 1024, insertValueScript = "''")
    private String errorMsg;

    /** 错误级别<br/>Column: [error_level] */
    @Column(value = "error_level")
    private String errorLevel;

    /** 调用链 id<br/>Column: [trace_id] */
    @Column(value = "trace_id", maxStringLength = 100)
    private String traceId;

    public CdTaskRunLogDO(String taskName, String methodName, String errorMsg) {
        this(taskName, methodName, errorMsg, TaskRunLogLevelEnum.ERROR);
    }

    /**
     * 可以指定level的构造器
     */
    public CdTaskRunLogDO(String taskName, String methodName, String errorMsg, TaskRunLogLevelEnum level){
        this.taskName = taskName;
        this.methodName = methodName;
        this.errorMsg = errorMsg;
        this.errorLevel = TaskRunLogLevelEnum.getCodeByEnum(level);
        String traceId = MDC.get("requestId");
        this.traceId = traceId == null ? "" : traceId;
    }
}