package cloud.demand.app.entity.demandvo;

import cloud.demand.app.entity.demand.CdDemandAckRecordDO;
import cloud.demand.app.entity.demand.CdDemandVersionDO;
import cloud.demand.app.entity.demand.CdDemandVersionGroupDO;
import cloud.demand.app.entity.demand.CdDemandVersionItemDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

import java.util.List;

/**
 * 分组详情记录
 */
@Data
public class DemandVersionGroupDetailVO extends CdDemandVersionGroupDO {

    /**该分组下的需求明细*/
    @RelatedColumn(localColumn = "id", remoteColumn = "demand_group_id")
    private List<CdDemandVersionItemDO> demandItems;

    /**该分组的共识记录（多条）*/
    @RelatedColumn(localColumn = "id", remoteColumn = "demand_group_id")
    private List<CdDemandAckRecordDO> ackRecord;

    @RelatedColumn(localColumn = "demand_version", remoteColumn = "demand_version")
    private CdDemandVersionDO version;

}
