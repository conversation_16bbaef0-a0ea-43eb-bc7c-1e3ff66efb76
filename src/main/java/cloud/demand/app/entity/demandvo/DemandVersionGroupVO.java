package cloud.demand.app.entity.demandvo;

import cloud.demand.app.entity.demand.CdDemandVersionDO;
import cloud.demand.app.entity.demand.CdDemandVersionGroupDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

/**
 * 分组，附带版本信息
 */
@Data
public class DemandVersionGroupVO extends CdDemandVersionGroupDO {

    @RelatedColumn(localColumn = "demand_version", remoteColumn = "demand_version")
    private CdDemandVersionDO versionDO;

}
