package cloud.demand.app.common.excel.checker;

import cloud.demand.app.common.excel.core.ErrorMessage;
import cloud.demand.app.common.excel.core.ParseContext;
import cloud.demand.app.common.excel.core.checker.ExcelColumnValueChecker;
import java.util.List;

/**
 * 检查数值Range
 */
public class NumberColumnChecker implements ExcelColumnValueChecker {

    private final Integer start;

    private final Integer end;

    /**
     * 均为闭区间
     *
     * @param start
     * @param end
     */
    public NumberColumnChecker(Integer start, Integer end) {
        this.start = start;
        this.end = end;
    }

    @Override
    public void checkValue(int rowIndex, int columnIndex, String columnName, Object value, List<ErrorMessage> errors,
            ParseContext<?> context) {
        ErrorMessage message = ErrorMessage.makeErrorIfNumberNotInRange(rowIndex, columnIndex, columnName,
                value == null ? null : value.toString(), start, end);
        addNoneNullErrorMessage(errors, message);
    }

}
