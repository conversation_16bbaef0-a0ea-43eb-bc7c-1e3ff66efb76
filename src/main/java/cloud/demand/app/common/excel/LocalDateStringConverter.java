package cloud.demand.app.common.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import org.apache.commons.lang3.StringUtils;

public class LocalDateStringConverter implements Converter<LocalDate> {

    private String format;

    private DateTimeFormatter dateTimeFormatter;

    public LocalDateStringConverter() {
        init("yyyy-MM-dd");
    }

    public LocalDateStringConverter(String format) {
        init(format);
    }

    private void init(String format) {
        this.format = format;
        dateTimeFormatter = DateTimeFormatter.ofPattern(format);
    }

    @Override
    public Class supportJavaTypeKey() {
        return LocalDate.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public LocalDate convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
            GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return LocalDate.parse(value, dateTimeFormatter);
    }

    @Override
    public CellData convertToExcelData(LocalDate value, ExcelContentProperty contentProperty,
            GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null) {
            return new CellData<>(CellDataTypeEnum.STRING, "");
        }
        return new CellData<>(CellDataTypeEnum.STRING, dateTimeFormatter.format(value));
    }
}
