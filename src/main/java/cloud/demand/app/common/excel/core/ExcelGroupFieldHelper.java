package cloud.demand.app.common.excel.core;

import cloud.demand.app.common.excel.core.annotation.DotExcelField;
import cloud.demand.app.common.excel.core.checker.ExcelColumnValueChecker;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.exception.ExcelCommonException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 *  excel 组助手
 */
public class ExcelGroupFieldHelper {

    /** 组编码、excel字段信息映射 */
    private static final Map<String, List<ExcelFieldInfo>> GROUP_MAP = new ConcurrentHashMap<>();

    /** 扩展接口创建器 */
    private static ExtendInterfaceCreator CREATE_INTERFACE_INSTANCE = new ExtendInterfaceCreatorImpl();

    private static int modify = 0;

    /** 注册扩展接口创建器 */
    public static synchronized void registerExtendInterfaceCreator(ExtendInterfaceCreator instance) {
        if (instance == null) {
            return;
        }
        if (modify != 0) {
            throw new RuntimeException("has been registered before, can only register ExtendInterfaceCreator once. "
                    + "current:" + CREATE_INTERFACE_INSTANCE.getClass());
        }
        CREATE_INTERFACE_INSTANCE = instance;
        modify++;
    }

    /** 注册excel字段信息相关实体类 */
    public static synchronized void registerExcelFieldClass(Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            DotExcelField dotExcelField = field.getAnnotation(DotExcelField.class);
            if (dotExcelField == null) {
                continue;
            }
            ExcelFieldInfo info = createExcelFieldInfo(dotExcelField, field);
            for (String item : dotExcelField.group()) {
                List<ExcelFieldInfo> list = new ArrayList<>();
                if (GROUP_MAP.containsKey(item)) {
                    list = GROUP_MAP.get(item);
                    list.forEach(excelFieldInfo -> {
                        if (excelFieldInfo.getJavaFieldName().equals(info.getJavaFieldName())) {
                            throw new ExcelCommonException("group【" + item + "】has same JavaName exists");
                        }
                    });
                }
                list.add(info);
                GROUP_MAP.put(item, list);
            }
        }

    }

    private static ExcelFieldInfo createExcelFieldInfo(DotExcelField dotExcelField, Field field) {
        ExcelFieldInfo info = new ExcelFieldInfo();
        String fieldName = field.getName();
        info.setExcelColumnName(dotExcelField.excelColumnName() == null || "".equals(dotExcelField.excelColumnName().trim())
                ? fieldName : dotExcelField.excelColumnName());
        info.setJavaFieldName(dotExcelField.javaFieldName() == null || "".equals(dotExcelField.javaFieldName().trim())
                ? fieldName : dotExcelField.javaFieldName());
        info.setFieldClazz(field.getType());
        try {
            info.setConverter(getConvert(dotExcelField.converter()));
        } catch (Exception e) {
            throw new ExcelCommonException("Can not create instance for custom converter:" + dotExcelField.converter().getName());
        }
        if (dotExcelField.valueCheckers() != null && dotExcelField.valueCheckers().length > 0) {
            ExcelColumnValueChecker[] columnValueCheckers =
                    new ExcelColumnValueChecker[dotExcelField.valueCheckers().length];
            for (int i = 0; i < dotExcelField.valueCheckers().length; i++) {
                columnValueCheckers[i] = getColumnChecker(dotExcelField.valueCheckers()[i]);
            }
            info.setColumnValueChecker(columnValueCheckers);
        }
        return info;
    }

    public static List<ExcelFieldInfo> getByExcelGroup(ExcelGroup group) {
        if (group == null || group.getCode() == null) {
            return new ArrayList<>();
        }
        List<ExcelFieldInfo> result = GROUP_MAP.get(group.getCode());
        return result == null ? new ArrayList<>() : result;
    }

    public static Converter<?> getConvert(Class<? extends Converter> clazz) {
        check();
        if (clazz == null) {
            return null;
        }
        return CREATE_INTERFACE_INSTANCE.getConvert((Class<? extends Converter<?>>) clazz);
    }

    public static ExcelColumnValueChecker getColumnChecker(Class<? extends ExcelColumnValueChecker> clazz) {
        check();
        if (clazz == null) {
            return null;
        }
        return CREATE_INTERFACE_INSTANCE.getColumnChecker(clazz);
    }

    private static void check() {
        if (CREATE_INTERFACE_INSTANCE == null) {
            throw new IllegalArgumentException("not configured CREATE_INTERFACE_INSTANCE");
        }
    }
}
