package cloud.demand.app.common.excel.core.checker;

import cloud.demand.app.common.excel.core.ErrorMessage;
import cloud.demand.app.common.excel.core.ParseContext;
import java.util.List;

/**
 *  excel 行数据集校验器
 */
public interface ExcelRowDataAfterConvertChecker<T> extends Checker {

    /**
     *   校验行数据
     * @param rowIndex 行下标, 大于 0 ,从1开始
     * @param rowData 行数据
     * @param errors 校验结果
     * @param context 上下文信息，一般是在业务解析入口或解析过程中自定义添加的信息
     */
    <R extends T> void checkRowDataAfterConvert(int rowIndex, R rowData, List<ErrorMessage> errors, ParseContext<R> context);

}
