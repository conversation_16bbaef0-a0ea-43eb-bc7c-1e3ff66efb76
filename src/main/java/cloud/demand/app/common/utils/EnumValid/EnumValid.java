package cloud.demand.app.common.utils.EnumValid;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 此注解用于验证实体类的属性是否合法，在枚举内
 *
 * enumClass 枚举类
 * methodName 方法名（由于枚举有多个属性，这个指定通过哪个方法取值进行对比）
 * canNull 是否允许空，如果允许，如果是null也验证通过
 * message 错误信息
 */


@Constraint(validatedBy = EnumValidValidator.class)
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface EnumValid {
    boolean canNull() default false;
    Class<?> enumClass();
    String methodName();
    String message() default "非法的值";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};


}
