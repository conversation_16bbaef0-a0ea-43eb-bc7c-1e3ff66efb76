package cloud.demand.app.common.utils;

import com.pugwoo.wooutils.json.JSON;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import okhttp3.Call;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.nutz.lang.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import yunti.boot.config.DynamicProperty;

/**
 * 企业微信告警机器人
 *
 * <AUTHOR>
 * @since 2023/1/9 21:40
 */

public class AlarmRobotUtil {


    private static final Logger LG = LoggerFactory.getLogger(AlarmRobotUtil.class);

//    private static final String webHook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=79faf571-103c-4207-adf0-e58a1a919ca9";

    private static final Supplier<String> webHook =
            DynamicProperty.create("qiwei.webhook", "localhost");

    private static final Supplier<String> comdWebHook =
            DynamicProperty.create("qiwei.comd.webhook", "localhost");

    /**
     * for 研发用
     * @param method
     * @param msg
     * @param user
     * @param atAll
     * @return
     */
    public static boolean doAlarm(String method, String msg, List<String> user, boolean atAll) {
        String webHookUrl = webHook.get();
        if (Strings.isBlank(webHookUrl) || "localhost".equals(webHookUrl)) {
            return false;
        }
        Map<String, Object> req = new HashMap<>();
        req.put("msgtype", "markdown");
        Map<String, Object> content = new HashMap<>();
        msg = "method: " + method + "\n" + "detail: " + msg;
        content.put("content", msg);
        req.put("markdown", content);
        if (user != null && !user.isEmpty()) {
            content.put("mentioned_list", user);
        }
        if (atAll) {
            content.put("mentioned_mobile_list", "@all");
        }

        String reqStr = JSON.toJson(req);
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();
        okhttp3.Request request =
                new okhttp3.Request.Builder()
                        .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"), reqStr))
                        .url(webHookUrl)
                        .build();
        Call call = clientBuilder.build().newCall(request);
        try (Response response = call.execute()) {
            if (response.code() != 200) {
                String errMsg = "告警机器人发送消息响应错误，response code " + response.code();
                LG.error(errMsg);
                return false;
            }
            return true;
        } catch (Exception e) {
            LG.error("告警机器人发送消息失败, reason: {}", e.getMessage());
        }
        return false;
    }

    /**
     * for 云运管业务用
     * @param method
     * @param msg
     * @param user
     * @param atAll
     * @return
     */
    public static boolean doAlarmForComd(String method, String msg, List<String> user, boolean atAll) {
        String webHookUrl = comdWebHook.get();
        if (Strings.isBlank(webHookUrl) || "localhost".equals(webHookUrl)) {
            return false;
        }
        Map<String, Object> req = new HashMap<>();
        req.put("msgtype", "markdown");
        Map<String, Object> content = new HashMap<>();
        msg = "method: " + method + "\n" + "detail: " + msg;
        content.put("content", msg);
        req.put("markdown", content);
        if (user != null && !user.isEmpty()) {
            content.put("mentioned_list", user);
        }
        if (atAll) {
            content.put("mentioned_mobile_list", "@all");
        }

        String reqStr = JSON.toJson(req);
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();
        okhttp3.Request request =
                new okhttp3.Request.Builder()
                        .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"), reqStr))
                        .url(webHookUrl)
                        .build();
        Call call = clientBuilder.build().newCall(request);
        try (Response response = call.execute()) {
            if (response.code() != 200) {
                String errMsg = "告警机器人发送消息响应错误，response code " + response.code();
                LG.error(errMsg);
                return false;
            }
            return true;
        } catch (Exception e) {
            LG.error("告警机器人发送消息失败, reason: {}", e.getMessage());
        }
        return false;
    }


    public static boolean doAlarm(String method, String msg, List<String> user, boolean atAll, String robotWebhook) {
        if (Strings.isBlank(robotWebhook)) {
            return false;
        }
        Map<String, Object> req = new HashMap<>();
        req.put("msgtype", "markdown");
        Map<String, Object> content = new HashMap<>();
        msg = "method: " + method + "\n" + "detail: " + msg;
        content.put("content", msg);
        req.put("markdown", content);
        if (user != null && !user.isEmpty()) {
            content.put("mentioned_list", user);
        }
        if (atAll) {
            content.put("mentioned_mobile_list", "@all");
        }

        String reqStr = JSON.toJson(req);
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();
        okhttp3.Request request =
                new okhttp3.Request.Builder()
                        .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"), reqStr))
                        .url(robotWebhook)
                        .build();
        Call call = clientBuilder.build().newCall(request);
        try (Response response = call.execute()) {
            if (response.code() != 200) {
                String errMsg = "告警机器人发送消息响应错误，response code " + response.code();
                LG.error(errMsg);
                return false;
            }
            return true;
        } catch (Exception e) {
            LG.error("告警机器人发送消息失败, reason: {}", e.getMessage());
        }
        return false;
    }
}
