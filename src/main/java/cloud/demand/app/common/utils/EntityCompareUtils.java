package cloud.demand.app.common.utils;

import cn.hutool.core.util.ReflectUtil;
import com.pugwoo.wooutils.collect.ListUtils;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.nutz.lang.Strings;

public class EntityCompareUtils {

    /**
     *  根据{@link CompareField}注解来比较两个对象的字段值是否存在不同
     * @param data 待比较的对象
     * @param other 另一个待比较的对象
     * @return 返回 false 表示没有区别， 返回 true 表示存在区别
     */
    public static <T> boolean hasDifference(T data, T other) throws IllegalAccessException, InstantiationException {
        return hasDifference(data, other, null);
    }

    /**
     *  根据{@link CompareField}注解来比较两个对象的字段值是否存在不同
     * @param data 待比较的对象
     * @param other 另一个待比较的对象
     * @param group 比较的组名{@link CompareField#group()}；<br/>
     *              如果为空，表示只要有 {@link CompareField} 注解就进行比较；<br/>
     *              如果非空，则只比较{@link CompareField#group()} 包含此入参的字段 <br/>
     * @return 返回 false 表示没有区别， 返回 true 表示存在区别
     */
    public static <T> boolean hasDifference(T data, T other, String group) throws IllegalAccessException, InstantiationException {
        List<DifferenceInfo> res = getDifference(data, other, group);
        return ListUtils.isNotEmpty(res);
    }

    /**
     *  根据{@link CompareField}注解来比较两个对象的字段值，返回不相同的字段值信息
     * @param data 待比较的对象
     * @param other 另一个待比较的对象
     * @param group 比较的组名{@link CompareField#group()}；<br/>
     *              如果为空，表示只要有 {@link CompareField} 注解就进行比较；<br/>
     *              如果非空，则只比较{@link CompareField#group()} 包含此入参的字段 <br/>
     */
    public static <T> List<DifferenceInfo> getDifference(T data, T other, String group) throws IllegalAccessException, InstantiationException {
        List<DifferenceInfo> res = new ArrayList<>();
        if (data == null && other == null) {
            return res;
        }
        // 可能需要比较的字段
        Field[] fields = ReflectUtil.getFields(data == null ? other.getClass() : data.getClass());
        if (fields == null || fields.length < 1) {
            // 没有字段，不需要比较
            return res;
        }
        boolean notBlankInputGroup = Strings.isNotBlank(group);
        Map<Class<? extends ValueCompare>, ValueCompare> compareCache = new HashMap<>();
        for (Field field : fields) {
            CompareField compareField = field.getAnnotation(CompareField.class);
            if (compareField == null) {
                // 不需要比较的字段
                continue;
            }
            String[] groups = compareField.group();
            if (groups != null && groups.length > 0 && notBlankInputGroup) {
                boolean containsGroup = false;
                for (String s : groups) {
                    if (Objects.equals(s, group)) {
                        containsGroup = true;
                        break;
                    }
                }
                if (!containsGroup) {
                    // 如果不包含输入组，则当前字段无须比较
                    continue;
                }
            }
            Class<? extends ValueCompare> comparor = compareField.comparor();
            if (comparor == null) {
                comparor = DefaultValueCompare.class;
            }
            String fieldName = compareField.name();
            if (Strings.isBlank(fieldName)) {
                fieldName = field.getName();
            }
            // 生成比较器
            ValueCompare valueCompare = compareCache.get(comparor);
            if (valueCompare == null) {
                valueCompare = comparor.newInstance();
                compareCache.put(comparor, valueCompare);
            }

            field.setAccessible(true);
            Object dataValue = field.get(data);
            Object otherValue = field.get(other);

            boolean isSame = valueCompare.isEquals(dataValue, otherValue);
            if (!isSame) {
                // 值不相同
                res.add(new DifferenceInfo(fieldName, dataValue, otherValue));
            }
        }
        return res;
    }

    /**
     *  比较字段
     */
    @Target(ElementType.FIELD)
    @Retention(RetentionPolicy.RUNTIME)
    @Inherited
    public @interface CompareField {

        /**
         *  字段名称，为空时默认为字段java名
         */
        String name() default "";

        /**
         *  值比较器，默认值{@link DefaultValueCompare}
         */
        Class<? extends ValueCompare> comparor() default DefaultValueCompare.class;

        /**
         *  属于那个比较的组，当前比较字段可以用于哪些组进行比较，用于同一个类的一个字段在A场景需要进行比较，而在B场景却不用进行比较。<br/>
         *  没有组信息时表示当前字段通用于所有组
         */
        String[] group() default {};

    }

    /**
     *  值比较器
     */
    public interface ValueCompare {

        /**
         *  比较两个值是否相同
         */
        boolean isEquals(Object a, Object b);
    }

    /**
     *  使用 {@link Objects#equals(Object, Object)} 进行比较
     */
    public static class DefaultValueCompare implements ValueCompare {

        /**
         *  使用 {@link Objects#equals(Object, Object)} 进行比较
         */
        @Override
        public boolean isEquals(Object a, Object b) {
            return Objects.equals(a, b);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DifferenceInfo {

        private String fieldName;

        private Object dataValue;

        private Object otherDataValue;

    }

}
