package cloud.demand.app.common.config;

import com.alibaba.druid.pool.DruidDataSource;
import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.actuate.autoconfigure.health.CompositeHealthContributorConfiguration;
import org.springframework.boot.actuate.health.AbstractHealthIndicator;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.Health.Builder;
import org.springframework.boot.actuate.health.HealthContributor;
import org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.jdbc.metadata.CompositeDataSourcePoolMetadataProvider;
import org.springframework.boot.jdbc.metadata.DataSourcePoolMetadata;
import org.springframework.boot.jdbc.metadata.DataSourcePoolMetadataProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

@Configuration
public class HealthIndicatorConfig extends
        CompositeHealthContributorConfiguration<AbstractHealthIndicator, DataSource> implements InitializingBean {

    private final Collection<DataSourcePoolMetadataProvider> metadataProviders;
    private DataSourcePoolMetadataProvider poolMetadataProvider;

    public HealthIndicatorConfig(
            Map<String, DataSource> dataSources, ObjectProvider<DataSourcePoolMetadataProvider> metadataProviders) {
        this.metadataProviders = (Collection) metadataProviders.orderedStream().collect(Collectors.toList());
    }

    public void afterPropertiesSet() throws Exception {
        this.poolMetadataProvider = new CompositeDataSourcePoolMetadataProvider(this.metadataProviders);
    }

    @Bean
    @ConditionalOnMissingBean(
            name = {"dbHealthIndicator", "dbHealthContributor"}
    )
    public HealthContributor dbHealthContributor(Map<String, DataSource> dataSources) {
        return (HealthContributor) this.createContributor(dataSources);
    }

    protected AbstractHealthIndicator createIndicator(DataSource source) {
        return (AbstractHealthIndicator) (source instanceof AbstractRoutingDataSource
                ? new HealthIndicatorConfig.RoutingDataSourceHealthIndicator()
                : new CustomDBHealthIndicator(source, this.getValidationQuery(source)));
    }

    private String getValidationQuery(DataSource source) {
        DataSourcePoolMetadata poolMetadata = this.poolMetadataProvider.getDataSourcePoolMetadata(source);
        return poolMetadata != null ? poolMetadata.getValidationQuery() : null;
    }

    static class RoutingDataSourceHealthIndicator extends AbstractHealthIndicator {

        RoutingDataSourceHealthIndicator() {
        }

        protected void doHealthCheck(Builder builder) throws Exception {
            builder.unknown().withDetail("routing", true);
        }
    }

    @Slf4j
    static class CustomDBHealthIndicator extends DataSourceHealthIndicator {

        String objName = "";

        @Override
        public Health getHealth(boolean includeDetails) {

            long start = System.currentTimeMillis();
            Health health = super.getHealth(includeDetails);
            long end = System.currentTimeMillis();
            long cs = end - start;
            if (cs > 1000) {
                log.info("show-health-check db{}:  cost {}", objName, (end - start));
            }
            return health;
        }

        public CustomDBHealthIndicator() {
            super();
        }

        public CustomDBHealthIndicator(DataSource dataSource) {
            super(dataSource);
            if (dataSource instanceof DruidDataSource) {
                String jdbc = ((DruidDataSource) dataSource).getRawJdbcUrl();
                objName = jdbc.contains("?") ? jdbc.substring(0, jdbc.indexOf("?")) : jdbc;
            }
        }

        public CustomDBHealthIndicator(DataSource dataSource, String query) {
            super(dataSource, query);
            if (dataSource instanceof DruidDataSource) {
                String jdbc = ((DruidDataSource) dataSource).getRawJdbcUrl();
                objName = jdbc.contains("?") ? jdbc.substring(0, jdbc.indexOf("?")) : jdbc;
            }
        }
    }
}
