package cloud.demand.app.common.config;

import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.modules.report_proxy.component.DBHelperInfo;
import cloud.demand.app.modules.report_proxy.component.DBHelperInfoComponent;
import com.alibaba.druid.pool.DruidDataSource;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.ToDoubleFunction;
import javax.annotation.Resource;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

@Configuration
@ConditionalOnClass({SpringJdbcDBHelper.class, MeterRegistry.class})
@Slf4j
public class DBHelperMetricsConfiguration {
    
    @Resource
    private DBHelperInfoComponent dbHelperInfoComponent;
    
    private final MeterRegistry registry;

    public DBHelperMetricsConfiguration(MeterRegistry registry) {
        this.registry = registry;
    }

    @Autowired
    public void bindMetricsRegistryToDruidDataSources(Collection<SpringJdbcDBHelper> dbHelpers) throws SQLException {
//        if (EnvUtils.isLocalEnv()){
//            log.info("DEV env, skip register metrics to micrometer");
//            return;
//        }
        List<SpringJdbcDBHelper> helpers = new ArrayList<>(dbHelpers.size());
        for (SpringJdbcDBHelper dbHelper : dbHelpers) {
            if (dbHelper != null) {
                helpers.add(dbHelper);
            }
        }
        DBHelperCollector dbHelperCollector = new DBHelperCollector(helpers, registry);
        dbHelperCollector.register(dbHelperInfoComponent);
        log.info("finish register metrics to micrometer");
    }

    static class DBHelperCollector {
        private static final String LABEL = "dBHelper";

        private final List<SpringJdbcDBHelper> dbHelpers;

        private final MeterRegistry registry;

        public DBHelperCollector(List<SpringJdbcDBHelper> dbHelpers, MeterRegistry registry) {
            this.registry = registry;
            this.dbHelpers = dbHelpers;
        }

        public void register(DBHelperInfoComponent dbHelperInfoComponent) {
            if (ListUtils.isEmpty(this.dbHelpers)){
                log.info("No DBHelper found, skip register metrics to micrometer");
                return;
            }
            this.dbHelpers.forEach((dbHelper) -> {
                JdbcTemplate jdbcTemplate = dbHelper.getJdbcTemplate();
                DataSource dataSource = jdbcTemplate.getDataSource();
                
                if (dataSource instanceof DruidDataSource){
                    DruidDataSource druidDataSource = (DruidDataSource) dataSource;
                    
                    String labelName = druidDataSource.getName();
                    try {
                        DBHelperInfo dbHelperInfo = dbHelperInfoComponent.getDBHelperInfo(dbHelper);
                        labelName = dbHelperInfo.getBeanName();
                    }catch (Exception e){
                        log.error("Failed to get DBHelperInfo, skip register metrics to micrometer", e);
                    }
                    
                    // basic configurations
                    createGauge(druidDataSource, labelName, "druid_initial_size", "Initial size", (datasource) -> (double) druidDataSource.getInitialSize());
                    createGauge(druidDataSource, labelName, "druid_min_idle", "Min idle", datasource -> (double) druidDataSource.getMinIdle());
                    createGauge(druidDataSource, labelName, "druid_max_active", "Max active", datasource -> (double) druidDataSource.getMaxActive());

                    // connection pool core metrics
                    createGauge(druidDataSource, labelName, "druid_active_count", "Active count", datasource -> (double) druidDataSource.getActiveCount());
                    createGauge(druidDataSource, labelName, "druid_active_peak", "Active peak", datasource -> (double) druidDataSource.getActivePeak());
                    createGauge(druidDataSource, labelName, "druid_pooling_peak", "Pooling peak", datasource -> (double) druidDataSource.getPoolingPeak());
                    createGauge(druidDataSource, labelName, "druid_pooling_count", "Pooling count", datasource -> (double) druidDataSource.getPoolingCount());
                    createGauge(druidDataSource, labelName, "druid_wait_thread_count", "Wait thread count", datasource -> (double) druidDataSource.getWaitThreadCount());

                    // connection pool detail metrics
                    createGauge(druidDataSource, labelName, "druid_not_empty_wait_count", "Not empty wait count", datasource -> (double) druidDataSource.getNotEmptyWaitCount());
                    createGauge(druidDataSource, labelName, "druid_not_empty_wait_millis", "Not empty wait millis", datasource -> (double) druidDataSource.getNotEmptyWaitMillis());
                    createGauge(druidDataSource, labelName, "druid_not_empty_thread_count", "Not empty thread count", datasource -> (double) druidDataSource.getNotEmptyWaitThreadCount());

                    createGauge(druidDataSource, labelName, "druid_logic_connect_count", "Logic connect count", datasource -> (double) druidDataSource.getConnectCount());
                    createGauge(druidDataSource, labelName, "druid_logic_close_count", "Logic close count", datasource -> (double) druidDataSource.getCloseCount());
                    createGauge(druidDataSource, labelName, "druid_logic_connect_error_count", "Logic connect error count", datasource -> (double) druidDataSource.getConnectErrorCount());
                    createGauge(druidDataSource, labelName, "druid_physical_connect_count", "Physical connect count", datasource -> (double) druidDataSource.getCreateCount());
                    createGauge(druidDataSource, labelName, "druid_physical_close_count", "Physical close count", datasource -> (double) druidDataSource.getDestroyCount());
                    createGauge(druidDataSource, labelName, "druid_physical_connect_error_count", "Physical connect error count", datasource -> (double) druidDataSource.getCreateErrorCount());

                    // sql execution core metrics
                    createGauge(druidDataSource, labelName, "druid_error_count", "Error count", datasource -> (double) druidDataSource.getErrorCount());
                    createGauge(druidDataSource, labelName, "druid_execute_count", "Execute count", datasource -> (double) druidDataSource.getExecuteCount());
                    // transaction metrics
                    createGauge(druidDataSource, labelName, "druid_start_transaction_count", "Start transaction count", datasource -> (double) druidDataSource.getStartTransactionCount());
                    createGauge(druidDataSource, labelName, "druid_commit_count", "Commit count", datasource -> (double) druidDataSource.getCommitCount());
                    createGauge(druidDataSource, labelName, "druid_rollback_count", "Rollback count", datasource -> (double) druidDataSource.getRollbackCount());

                    // sql execution detail
                    createGauge(druidDataSource, labelName, "druid_prepared_statement_open_count", "Prepared statement open count", datasource -> (double) druidDataSource.getPreparedStatementCount());
                    createGauge(druidDataSource, labelName, "druid_prepared_statement_closed_count", "Prepared statement closed count", datasource -> (double) druidDataSource.getClosedPreparedStatementCount());
                    createGauge(druidDataSource, labelName, "druid_ps_cache_access_count", "PS cache access count", datasource -> (double) druidDataSource.getCachedPreparedStatementAccessCount());
                    createGauge(druidDataSource, labelName, "druid_ps_cache_hit_count", "PS cache hit count", datasource -> (double) druidDataSource.getCachedPreparedStatementHitCount());
                    createGauge(druidDataSource, labelName, "druid_ps_cache_miss_count", "PS cache miss count", datasource -> (double) druidDataSource.getCachedPreparedStatementMissCount());
                    createGauge(druidDataSource, labelName, "druid_execute_query_count", "Execute query count", datasource -> (double) druidDataSource.getExecuteQueryCount());
                    createGauge(druidDataSource, labelName, "druid_execute_update_count", "Execute update count", datasource -> (double) druidDataSource.getExecuteUpdateCount());
                    createGauge(druidDataSource, labelName, "druid_execute_batch_count", "Execute batch count", datasource -> (double) druidDataSource.getExecuteBatchCount());

                    // none core metrics, some are static configurations
                    createGauge(druidDataSource, labelName, "druid_max_wait", "Max wait", datasource -> (double) druidDataSource.getMaxWait());
                    createGauge(druidDataSource, labelName, "druid_max_wait_thread_count", "Max wait thread count", datasource -> (double) druidDataSource.getMaxWaitThreadCount());
                    createGauge(druidDataSource, labelName, "druid_login_timeout", "Login timeout", datasource -> (double) druidDataSource.getLoginTimeout());
                    createGauge(druidDataSource, labelName, "druid_query_timeout", "Query timeout", datasource -> (double) druidDataSource.getQueryTimeout());
                    createGauge(druidDataSource, labelName, "druid_transaction_query_timeout", "Transaction query timeout", datasource -> (double) druidDataSource.getTransactionQueryTimeout());
                }
            });
        }

        private void createGauge(DruidDataSource weakRef, String labelName,String metric, String help, ToDoubleFunction<DruidDataSource> measure) {
            Gauge.builder(metric, weakRef, measure)
                    .description(help)
                    .tag(LABEL, labelName)
                    .register(this.registry);
        }
    } 
}
