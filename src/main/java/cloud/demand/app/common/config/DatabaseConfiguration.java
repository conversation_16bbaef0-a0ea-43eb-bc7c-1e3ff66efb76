package cloud.demand.app.common.config;

import cloud.demand.app.common.utils.JsonUtils;
import cloud.demand.app.modules.report_proxy.interceptor.SelectDBHelperInterceptor;
import cloud.demand.app.modules.report_proxy.utils.ReportUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.enums.FeatureEnum;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.zaxxer.hikari.HikariDataSource;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import yunti.boot.data.Database;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;

@Configuration
public class DatabaseConfiguration {

    // ========================== ERP备库
    @Bean("erpBakDBHelper")
    DBHelper erpBakDBHelper(@Database("erpbak") JdbcTemplate demandJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(demandJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        return dbHelper;
    }

    @Bean("backupDBHelper")
    DBHelper backupDBHelper(@Database("backup") JdbcTemplate backupJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(backupJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // ========================== 新云需求预测数据：cloud_demand
    @Bean("demandDBHelper")
    DBHelper demandDBHelper(@Database("demand") JdbcTemplate demandJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(demandJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        return dbHelper;
    }

    // ========================== 新云需求预测数据：cloud_demand 1.4.5 orm版本
    @Bean("demand145DBHelper")
    DBHelper demand145DBHelper(@Database("demand") JdbcTemplate demandJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(demandJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        return dbHelper;
    }

    @Bean("cdCommonDbHelper")
    DBHelper cdCommonDbHelper(@Database("cdcommon") JdbcTemplate demandJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(demandJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        return dbHelper;
    }

    /**
     * clickhouse crp标准宽表
     */
    @Bean("ckStdCrpDBHelper")
    @Deprecated
    DBHelper ckStdCrpDBHelper(@Database("ckstdcrp") JdbcTemplate ckstdcrpTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(ckstdcrpTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.setDbHelperName("ckStdCrpDBHelper");
        return dbHelper;
    }

    /**
     * clickhouse crp标准宽表
     */
    @Bean("prodReadOnlyCkStdCrpDBHelper")
    DBHelper prodReadOnlyCkStdCrpDBHelper(@Database("readonlyckstdcrp") JdbcTemplate ckstdcrpTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(ckstdcrpTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.setDbHelperName("prodReadOnlyCkStdCrpDBHelper");
        return dbHelper;
    }

    /**
     * clickhouse crp标准宽表库备份库
     */
    @Bean("ckStdCrpSwapDBHelper")
    @Deprecated
    DBHelper ckStdCrpSwapDBHelper(@Database("ckstdcrpswap") JdbcTemplate ckstdcrpswapTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(ckstdcrpswapTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.setDbHelperName("ckStdCrpSwapDBHelper");
        return dbHelper;
    }

    // ========================== 云梯的需求预测和退回计划数据库 yunti_demand
    @Bean("yuntidemandDBHelper")
    DBHelper yuntidemandDBHelper(@Database("yuntidemand") JdbcTemplate yuntidemandJdbcTemplate) {
        return new SpringJdbcDBHelper(yuntidemandJdbcTemplate);
    }

    // =========================== 原星云资源中台数据库：rrp
    @Bean("rrpDBHelper")
    DBHelper rrpDBHelper(@Database("rrp") JdbcTemplate rrpJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(rrpJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        return dbHelper;
    }

    // =========================== 星云资源中台数据库：shuttle
    @Bean("shuttleDBHelper")
    DBHelper shuttleDBHelper(@Database("shuttle") JdbcTemplate shuttleJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(shuttleJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        return dbHelper;
    }

    // ========================== 原星云资源中台数据库：purchasereport
    @Bean("purchasereportDBHelper")
    DBHelper purchasereportDBHelper(@Database("purchasereport") JdbcTemplate purchasereportJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(purchasereportJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        return dbHelper;
    }

    // ========================== 云梯数据库：yunti
    @Bean("yuntiDBHelper")
    DBHelper yuntiDBHelper(@Database("yunti") JdbcTemplate yuntiJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(yuntiJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        return dbHelper;
    }

    // ========================== 云梯数据库线上备库：yunti
    @Bean("yuntiBakDBHelper")
    DBHelper yuntiBakDBHelper(@Database("yuntibak") JdbcTemplate yuntiBakJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(yuntiBakJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        return dbHelper;
    }

    // ========================== plan的端到端数据源
    @Bean("planDBHelper")
    DBHelper planDBHelper(@Database("plan") JdbcTemplate planJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(planJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        return dbHelper;
    }

    // ========================== plan的端到端数据源 - cbs
    @Bean("plancbsDBHelper")
    DBHelper plancbsDBHelper(@Database("plancbs") JdbcTemplate plancbsJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(plancbsJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        return dbHelper;
    }

    // ========================== plan的端到端数据源 - cbs
    @Bean("plancosDBHelper")
    DBHelper plancosDBHelper(@Database("plancos") JdbcTemplate plancosJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(plancosJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        return dbHelper;
    }

    // ========================== plan的端到端数据源 - cdb
    @Bean("plancdbDBHelper")
    DBHelper plancdbDBHelper(@Database("plancdb") JdbcTemplate plancdbJdbcTemplate) {
        return new SpringJdbcDBHelper(plancdbJdbcTemplate);
    }

    // ========================== crs产品的数据源
    @Bean("crsDBHelper")
    DBHelper crsDBHelper(@Database("crsdb") JdbcTemplate plancdbJdbcTemplate) {
        return new SpringJdbcDBHelper(plancdbJdbcTemplate);
    }

    // ========================== cmongo产品的数据源1
    @Bean("cmongoDBHelper")
    DBHelper cmongoDBHelper(@Database("cmongodb") JdbcTemplate plancdbJdbcTemplate) {
        return new SpringJdbcDBHelper(plancdbJdbcTemplate);
    }

    // ========================== cmongo产品的数据源2
    @Bean("cmongo2DBHelper")
    DBHelper cmongo2DBHelper(@Database("cmongodb2") JdbcTemplate plancdbJdbcTemplate) {
        return new SpringJdbcDBHelper(plancdbJdbcTemplate);
    }

    @Bean("plancdb2DBHelper")
    DBHelper plancdb2DBHelper(@Database("plancdb2") JdbcTemplate plancdb2JdbcTemplate) {
        return new SpringJdbcDBHelper(plancdb2JdbcTemplate);
    }

    // ========================== cloud matrix数据库
    @Bean("matrixDBHelper")
    DBHelper matrixDBHelper(@Database("matrix") JdbcTemplate matrixJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(matrixJdbcTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // ========================== erp resource数据库
    @Bean("resourcedbDBHelper")
    DBHelper resourcedbDBHelper(@Database("resourcedb") JdbcTemplate resourcedbJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(resourcedbJdbcTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    @Bean("obsDBHelper")
    DBHelper obsDBHelper(@Database("obsdb") JdbcTemplate obsJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(obsJdbcTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // ========================== erp res_plan需求预测数据库
    @Bean("resplanDBHelper")
    DBHelper resplanDBHelper(@Database("resplan") JdbcTemplate resplanJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(resplanJdbcTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }


    // ============================= new clickhouse cloud_demand
    @Bean("newckdemandDBHelper")
    @Deprecated
    DBHelper newckdemandDBHelper(@Database("newckdemand") JdbcTemplate newckdemandTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(newckdemandTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // ============================= clickhoues cubes
    @Bean("ckcubesDBHelper")
    DBHelper ckcubesDBHelper(@Database("ckcubes") JdbcTemplate ckcubesTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(ckcubesTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // ============================= clickhoues cubes
    @Bean("ckcubesdwDBHelper")
    DBHelper ckcubesdwDBHelper(@Database("ckcubesdw") JdbcTemplate ckcubesdwTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(ckcubesdwTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // ============================= clickhoues 只读
    @Bean("ckcrpreadDBHelper")
    DBHelper ckcrpreadDBHelper(@Database("ckcrpread") JdbcTemplate ckcrpreadTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(ckcrpreadTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("ckcrpreadDBHelper");
        return dbHelper;
    }

    // ============================= erp 只读备库
    @Bean("erpDBHelper")
    DBHelper erpDBHelper(@Database("erpbak") JdbcTemplate ckcubesdwTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(ckcubesdwTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // ============================= qcmdb
    @Bean("qcmdbDBHelper")
    DBHelper qcmdbDBHelper(@Database("qcmdb") JdbcTemplate qcmdbTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(qcmdbTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    /* crp 新ck cloud_demand 库 */
    @ConditionalOnProperty(value = "yunti.jdbc.ckcld.enabled", matchIfMissing = false)
    @Bean("ckcldDBHelper")
    DBHelper ckcldDBHelper(@Database("ckcld") JdbcTemplate ckcldTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(ckcldTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    /* crp 新ck std_crp 库 */
    @ConditionalOnProperty(value = "yunti.jdbc.ckcldstdcrp.enabled", matchIfMissing = false)
    @Bean("ckcldStdCrpDBHelper")
    DBHelper ckcldStdCrpDBHelper(@Database("ckcldstdcrp") JdbcTemplate ckcldStdCrpTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(ckcldStdCrpTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("ckcldStdCrpDBHelper");
        return dbHelper;
    }

    /* crp 新ck std_crp_swap 库 */
    @ConditionalOnProperty(value = "yunti.jdbc.ckcldstdcrpswap.enabled", matchIfMissing = false)
    @Bean("ckcldStdCrpSwapDBHelper")
    DBHelper ckcldStdCrpSwapDBHelper(@Database("ckcldstdcrpswap") JdbcTemplate ckcldStdCrpSwapTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(ckcldStdCrpSwapTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("ckcldStdCrpSwapDBHelper");
        return dbHelper;
    }

    /* crp 新ck yunti 库 */
    @ConditionalOnProperty(value = "yunti.jdbc.ckcldyunti.enabled", matchIfMissing = false)
    @Bean("ckcldyuntiDBHelper")
    DBHelper ckcldyuntiDBHelper(@Database("ckcldyunti") JdbcTemplate ckcldStdCrpSwapTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(ckcldStdCrpSwapTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("ckcldyuntiDBHelper");
        return dbHelper;
    }


    /**
     * crp 模型预测ck库，因为测试环境和生产ck版本不一致而临时搞的
     */
    @ConditionalOnProperty(value = "yunti.jdbc.ckforecaststdcrp.enabled", matchIfMissing = false)
    @Bean("ckForecastStdCrpDBHelper")
    DBHelper ckForecastStdCrpDBHelper(@Database("ckforecaststdcrp") JdbcTemplate ckForecastStdCrpTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(ckForecastStdCrpTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("ckForecastStdCrpDBHelper");
        return dbHelper;
    }

    /**
     * crp 模型预测ck库，因为测试环境和生产ck版本不一致而临时搞的swap
     */
    @ConditionalOnProperty(value = "yunti.jdbc.ckforecastswapstdcrp.enabled", matchIfMissing = false)
    @Bean("ckForecastStdCrpSwapDBHelper")
    DBHelper ckForecastStdCrpSwapDBHelper(@Database("ckforecastswapstdcrp") JdbcTemplate ckForecastStdCrpTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(ckForecastStdCrpTemplate);
        dbHelper.setInterceptors(ListUtils.newList(ReportUtils.getInterceptor(dbHelper)));
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("ckForecastStdCrpSwapDBHelper");
        return dbHelper;
    }


    /**
     * 由于zk压力过大导致ddl出现排队过久的情况，每个节点分别执行ddl
     */
    @SneakyThrows
    @Bean
    public List<DataSource> ckDataSources(@Value(value = "${yunti.jdbc.ckdemandcloud.urlList}") String urlListStr,
            @Value(value = "${yunti.jdbc.ckdemandcloud.urlList.username}") String username,
            @Value(value = "${yunti.jdbc.ckdemandcloud.urlList.password}") String password,
            @Value(value = "${yunti.jdbc.ckdemandcloud.urlList.driver}") String driver) {
        List<DataSource> ckDataSources = new ArrayList<>();
        List<String> urlList = JsonUtils.convertJsonToArrayEntry(urlListStr, String.class);
        for (String url : urlList) {
            HikariDataSource dataSource = new HikariDataSource();
            dataSource.setJdbcUrl(url);
            dataSource.setUsername(username);
            dataSource.setPassword(password);
            dataSource.setDriverClassName(driver);
            ckDataSources.add(dataSource);
        }
        return ckDataSources;
    }

    // ============================= plan-report
    @Bean("planReportDBHelper")
    DBHelper planReportDBHelper(@Database("planreportdb") JdbcTemplate planReportTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(planReportTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("planReportDBHelper");
        return dbHelper;
    }


    // ============================= gpu gpu产品 mysql只读库 对接人： arickyang 测试和生产用同一个
    @Bean("gpuDBHelper")
    DBHelper gpuDBHelper(@Database("gpu") JdbcTemplate planReportTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(planReportTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("gpuDBHelper");
        return dbHelper;
    }
}
