package cloud.demand.app.common.config;

import java.time.Duration;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateConfiguration {

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder restTemplateBuilder) {
        return restTemplateBuilder
                .setConnectTimeout(Duration.ofSeconds(DynamicProperties.restTemplateConnectTimeoutSeconds()))
                .setReadTimeout(Duration.ofSeconds(DynamicProperties.restTemplateReadTimeoutSeconds()))
                .build();
    }
}
