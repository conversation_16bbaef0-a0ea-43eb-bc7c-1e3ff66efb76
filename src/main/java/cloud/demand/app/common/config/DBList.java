package cloud.demand.app.common.config;

import cloud.demand.app.common.utils.SpringUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import java.util.List;
import java.util.function.Supplier;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import yunti.boot.config.DynamicProperty;

/**
 * <AUTHOR>
 */
@Slf4j
public class DBList {

    @Data
    @AllArgsConstructor
    public static class DBHelperWithClz<T> {

        Class<T> clz;
        DBHelper dbHelper;

        @SneakyThrows(value = ClassNotFoundException.class)
        public static <T> DBHelperWithClz<T> create(DBHelper dbHelper) {
            String className = Thread.currentThread().getStackTrace()[2].getClassName(); // 获取调用者的类名
            Class<T> callerClass = (Class<T>) Class.forName(className);
            return new DBHelperWithClz<>(callerClass, dbHelper);
        }

        public List<T> getAll() {
            return dbHelper.getAll(clz);
        }

        public T getRawOne(String sql, Object... args) {
            return dbHelper.getRawOne(clz, sql, args);
        }

        public T getOne(String postSql, Object... args) {
            return dbHelper.getOne(clz, postSql, args);
        }

    }

//***************************************** cloud_demand *************************************/


    /**
     * 获取原生的  JdbcTemplate
     *
     * @param dbHelper DBHelper
     * @return JdbcTemplate
     */
    public static JdbcTemplate getJdbcTemplate(DBHelper dbHelper) {
        return ((SpringJdbcDBHelper) dbHelper).getJdbcTemplate();
    }

    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#demandDBHelper
     */
    public static DBHelper demandDBHelper = SpringUtil.getBean("demandDBHelper", DBHelper.class);

    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#resourcedbDBHelper
     */
    public static DBHelper resourcedbDBHelper = SpringUtil.getBean("resourcedbDBHelper", DBHelper.class);

    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#ckcubesDBHelper
     */
    public static DBHelper ckCubesDBHelper = SpringUtil.getBean("ckcubesDBHelper", DBHelper.class);

    public static DBHelper newckdemandDBHelper = SpringUtil.getBean("newckdemandDBHelper", DBHelper.class);
    

    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#ckcrpreadDBHelper
     */
    public static DBHelper ckcrpreadDBHelper = SpringUtil.getBean("ckcrpreadDBHelper", DBHelper.class);

    /**
     * 新的数据源引用
     *
     * @see DatabaseConfiguration#ckcldStdCrpDBHelper(org.springframework.jdbc.core.JdbcTemplate)
     */
    public static DBHelper ckcldStdCrpDBHelper = SpringUtil.getBean("ckcldStdCrpDBHelper", DBHelper.class);


    /**
     * 新的数据源引用
     *
     * @see DatabaseConfiguration#prodReadOnlyCkStdCrpDBHelper(org.springframework.jdbc.core.JdbcTemplate)
     */
    public static DBHelper prodReadOnlyCkStdCrpDBHelper = SpringUtil.getBean("prodReadOnlyCkStdCrpDBHelper",
            DBHelper.class);


    /**
     * 新ck的yunti库
     *
     * @see DatabaseConfiguration#ckcldyuntiDBHelper(org.springframework.jdbc.core.JdbcTemplate)
     */
    public static DBHelper ckcldyuntiDBHelper = SpringUtil.getBean("ckcldyuntiDBHelper", DBHelper.class);


    /**
     * 新的数据源引用
     *
     * @see DatabaseConfiguration#ckcldDBHelper(org.springframework.jdbc.core.JdbcTemplate)
     */
    public static DBHelper ckcldDBHelper = SpringUtil.getBean("ckcldDBHelper", DBHelper.class);


    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#yuntiDBHelper
     */
    public static DBHelper yuntiDBHelper = SpringUtil.getBean("yuntiDBHelper", DBHelper.class);

    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#yuntidemandDBHelper
     */
    public static DBHelper yuntidemandDBHelper = SpringUtil.getBean("yuntidemandDBHelper", DBHelper.class);

    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#rrpDBHelper
     */
    public static DBHelper rrpDBHelper = SpringUtil.getBean("rrpDBHelper", DBHelper.class);

    public static final Supplier<String> subIPCkDBHelperSize = DynamicProperty.create("crp.subIPCkDBHelper.size", "4");

    public static int getSubIPCkDBHelperSize() {
        String s = subIPCkDBHelperSize.get();
        try {
            return Integer.parseInt(s);
        } catch (NumberFormatException e) {
            return 4;
        }
    }

}
