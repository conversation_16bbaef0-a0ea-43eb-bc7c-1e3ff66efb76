package cloud.demand.app.common.db_check;


import cloud.demand.app.common.utils.EnvUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.DBHelperContext;
import com.pugwoo.dbhelper.model.RunningSqlData;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.NetUtils;
import java.net.SocketException;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Strings;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import yunti.boot.config.DynamicProperty;

@Service
@Slf4j
public class SaveRunningSql {


    @Resource
    DBHelper cdCommonDbHelper;

    @Resource
    private Environment environment;


    // 创建一个包含3个线程的线程池
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(3);


    /**
     * 监听应用启动完成事件
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("start startScheduledTask");
        if (EnvUtils.isLocalEnv()) {
            return;
        }
        startScheduledTask();
    }

    /**
     * 启动定时任务
     */
    public void startScheduledTask() {
        // 每秒执行一次任务
        scheduler.scheduleAtFixedRate(this::saveRunningSql, 0, 1, TimeUnit.SECONDS);
    }

    private static String ipStr = null;

    @EventListener(ApplicationReadyEvent.class)
    public void getPodId() {
        try {
            ipStr = String.join(";", NetUtils.getIpv4IPs());
        } catch (SocketException ignore) {
            ipStr = "";
        }
    }

    private String getAppName() {
        return environment.getProperty("spring.application.name");
    }

    private static final Supplier<String> needSave =
            DynamicProperty.create("save-running-sql", "true");

//    @Scheduled(cron = "*/1 * * * * *") // 每秒执行一次
    public void saveRunningSql() {
        if (Strings.equals(needSave.get(), "false")) {
            return;
        }
        Collection<RunningSqlData> runningSql = DBHelperContext.getRunningSql();
        if (runningSql == null || runningSql.isEmpty()) {
            return;
        }
        RunningSqlDO runningSqlDO = new RunningSqlDO();
        runningSqlDO.setCreateTime(LocalDateTime.now());
        runningSqlDO.setRunningSql(JSON.toJson(runningSqlDO));
        runningSqlDO.setAppName(getAppName());
        runningSqlDO.setPodName(EnvUtils.getPodName());
        runningSqlDO.setPodIp(ipStr);
        runningSqlDO.setRunningSql(JSON.toJson(runningSql));
        cdCommonDbHelper.insert(runningSqlDO);
    }

}
