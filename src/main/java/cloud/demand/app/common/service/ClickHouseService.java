package cloud.demand.app.common.service;

import java.sql.SQLException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 由于zk压力过大导致ddl出现排队过久的情况，每个节点分别执行ddl
 */
@Service
@Slf4j
public class ClickHouseService {
//    @Resource(name = "ckDataSources")
//    List<DataSource> ckDataSources;

    public void dropPartition(String database, String tableName, String partition) throws SQLException {
//        String sql = StrUtil.format("ALTER TABLE {}.{} DROP PARTITION '{}'", database, tableName, partition);
//        log.info("每个节点分别执行此ddl = {}", sql);
//        for (DataSource dataSource : ckDataSources) {
//            try (Connection conn1 = dataSource.getConnection(); Statement stmt1 = conn1.createStatement()) {
//                stmt1.setQueryTimeout(1000);
//                stmt1.execute(sql);
//            }
//        }

    }

}
