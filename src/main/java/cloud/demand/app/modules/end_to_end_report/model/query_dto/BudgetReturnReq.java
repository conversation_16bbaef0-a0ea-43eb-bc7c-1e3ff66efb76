package cloud.demand.app.modules.end_to_end_report.model.query_dto;

import cloud.demand.app.modules.end_to_end_report.model.req.EndToEndReportQueryReq;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

@Data
public class BudgetReturnReq extends EndToEndReportQueryReq {

    public WhereSQL genCondition(){

        //  预算选了下面这些维度的筛选时直接返回空值
        if (ListUtils.isNotEmpty(this.getIndustryDept()) ||
                ListUtils.isNotEmpty(this.getCategory()) ||
                ListUtils.isNotEmpty(this.getAreaName()) ||
                ListUtils.isNotEmpty(this.getRegionName()) ||
                ListUtils.isNotEmpty(this.getZoneName())){
            return null;
        }

        WhereSQL condition = new WhereSQL();

        if (this.getBeginYearMonth() != null){
            condition.and("((budget_year > ?) or (budget_year = ? and budget_month >= ?))",
                    getBeginYear(), getBeginYear(), getBeginMonth());
        }
        if (this.getEndYearMonth() != null){
            condition.and("((budget_year < ?) or (budget_year = ? and budget_month <= ?))",
                    getEndYear(), getEndYear(), getEndMonth());
        }

        if (ListUtils.isNotEmpty(this.getDeviceType())){
            condition.and("device_type in (?)", this.getDeviceType());
        }

        return condition;
    }

    public static BudgetReturnReq transform(EndToEndReportQueryReq req){
        BudgetReturnReq budgetReturnReq = new BudgetReturnReq();
        budgetReturnReq.setBeginYearMonth(req.getBeginYearMonth());
        budgetReturnReq.setEndYearMonth(req.getEndYearMonth());
        budgetReturnReq.setProductType(req.getProductType());
        budgetReturnReq.setCategory(req.getCategory());
        budgetReturnReq.setIndustryDept(req.getIndustryDept());
        budgetReturnReq.setDeviceType(req.getDeviceType());
        budgetReturnReq.setInstanceType(req.getInstanceType());
        budgetReturnReq.setCustomhouseTitle(req.getCustomhouseTitle());
        budgetReturnReq.setAreaName(req.getAreaName());
        budgetReturnReq.setRegionName(req.getRegionName());
        budgetReturnReq.setZoneName(req.getZoneName());
        budgetReturnReq.setBeginYear(req.getBeginYear());
        budgetReturnReq.setBeginMonth(req.getBeginMonth());
        budgetReturnReq.setEndYear(req.getEndYear());
        budgetReturnReq.setEndMonth(req.getEndMonth());
        budgetReturnReq.setBeginDate(req.getBeginDate());
        budgetReturnReq.setEndDate(req.getEndDate());
        return budgetReturnReq;
    }

}
