package cloud.demand.app.modules.end_to_end_report.service.impl;

import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLogService;
import cloud.demand.app.modules.end_to_end_report.entity.EndToEndObsDeviceIncreaseDetailDO;
import cloud.demand.app.modules.end_to_end_report.entity.EndToEndObsDeviceReturnDetailDO;
import cloud.demand.app.modules.end_to_end_report.model.dto.ObsDeviceDataDTO;
import cloud.demand.app.modules.end_to_end_report.service.EndToEndReportGenDataService;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class EndToEndReportGenDataServiceImpl implements EndToEndReportGenDataService {

    @Value("${demand.OBS_BUDGET_ROOT_URL}")
    private String obsBudgetRootUrl;
    @Value("${demand.OBS_BUDGET_APP_NAME}")
    private String obsBudgetAppName;
    @Value("${demand.OBS_BUDGET_APP_SECRET}")
    private String obsBudgetAppSecret;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private TaskLogService taskLogService;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DictService dictService;
    @Resource
    private DBHelper resourcedbDBHelper;


    @Override
    @Transactional(transactionManager = "demandTransactionManager")
    public void genDeviceIncreaseData(Integer year) {
        if (year == null){
            year = DateUtils.getYear(new Date());
        }

        //  clean 已经存在的数据，全量覆盖
        boolean exist = demandDBHelper.isExist(EndToEndObsDeviceIncreaseDetailDO.class, "");
        if (exist){
            demandDBHelper.delete(EndToEndObsDeviceIncreaseDetailDO.class, "where 1 = 1 and Year = ?", year);
        }

        //  构建HTTP请求并发送
        String url = obsBudgetRootUrl + "/new-query-app/annual/annual.deviceIncrease/queryByThirdParty";
        ImmutableMap<String, Integer> params = ImmutableMap.of("Year", year, "pageNumber", 1, "pageSize", 1);
        HttpHeaders headers = new HttpHeaders();
        headers.add("APP_NAME", obsBudgetAppName);
        headers.add("APP_SECRET", obsBudgetAppSecret);
        HttpEntity<Map<String, Integer>> entity = new HttpEntity<>(params, headers);
        String rsp = restTemplate.postForObject(url, entity, String.class);

        try {
            //  解析请求，先请求1条拿到total字段进行分页
            ObsDeviceDataDTO parse = JSON.parse(rsp, ObsDeviceDataDTO.class);
            if (parse.getStatus() != 0 || !Objects.equals(parse.getMessage(), "查询成功")){
                //  解析Json失败
                taskLogService.genRunLog("queryObsBudgetData",
                        "queryDeviceIncreaseData", "解析JSON失败");
                return;
            }
            int total = parse.getData().getTotal();
            int size = 5000;    //  最多一次5000
            int pages = total / size + 1;   //  拿到全部页数
            for (int i = 1; i <= pages; i++) {
                params = ImmutableMap.of("Year", year, "pageNumber", i, "pageSize", size);
                entity = new HttpEntity<>(params, headers);
                ObsDeviceDataDTO curParse =
                        JSON.parse(restTemplate.postForObject(url, entity, String.class), ObsDeviceDataDTO.class);
                List<ObsDeviceDataDTO.Item> curData = curParse.getData().getData2();
                List<EndToEndObsDeviceIncreaseDetailDO> toBeInsert =
                        ListUtils.transform(curData, o -> ObsDeviceDataDTO.Item.transformIncrease(o));
                ListUtils.forEach(toBeInsert, o -> {
                    o.setCustomhouseTitle(
                            Objects.equals(dictService.getCountryChineseByCityName(o.getCityName()), "中国内地")? "境内" : "境外");
                    o.setComputeType(dictService.getDeviceGpuCard(o.getDeviceType()) > 0 ? "GPU" : "CPU");
                });
                demandDBHelper.insertBatchWithoutReturnId(toBeInsert);
            }
        } catch (Exception e){
            taskLogService.genRunLog("queryObsBudgetData",
                    "queryDeviceIncreaseData", "数据拉取失败");
            return;
        }
    }

    @Override
    public void genStockReturnDeviceDetail(Integer year) {
        if (year == null){
            year = DateUtils.getYear(new Date());
        }

        //  clean 已经存在的数据，全量覆盖
        boolean exist = demandDBHelper.isExist(EndToEndObsDeviceReturnDetailDO.class, "");
        if (exist){
            demandDBHelper.delete(EndToEndObsDeviceReturnDetailDO.class, "where 1 = 1 and Year = ?", year);
        }

        //  构建HTTP请求并发送
        String url = obsBudgetRootUrl + "/new-query-app/annual/annual.stockReturnDeviceDetail/queryByThirdParty";
        ImmutableMap<String, Integer> params = ImmutableMap.of("Year", year, "pageNumber", 1, "pageSize", 1);
        HttpHeaders headers = new HttpHeaders();
        headers.add("APP_NAME", obsBudgetAppName);
        headers.add("APP_SECRET", obsBudgetAppSecret);
        HttpEntity<Map<String, Integer>> entity = new HttpEntity<>(params, headers);
        String rsp = restTemplate.postForObject(url, entity, String.class);

        try {
            //  解析请求，先请求1条拿到total字段进行分页
            ObsDeviceDataDTO parse = JSON.parse(rsp, ObsDeviceDataDTO.class);
            if (parse.getStatus() != 0 || !Objects.equals(parse.getMessage(), "查询成功")){
                //  解析Json失败
                taskLogService.genRunLog("queryObsBudgetData",
                        "queryDeviceReturnData", "解析JSON失败");
                return;
            }
            int total = parse.getData().getTotal();
            int size = 5000;    //  最多一次5000
            int pages = total / size + 1;   //  拿到全部页数
            for (int i = 1; i <= pages; i++) {
                params = ImmutableMap.of("Year", year, "pageNumber", i, "pageSize", size);
                entity = new HttpEntity<>(params, headers);
                ObsDeviceDataDTO curParse =
                        JSON.parse(restTemplate.postForObject(url, entity, String.class), ObsDeviceDataDTO.class);
                List<ObsDeviceDataDTO.Item> curData = curParse.getData().getData2();
                List<EndToEndObsDeviceReturnDetailDO> toBeInsert =
                        ListUtils.transform(curData, o -> ObsDeviceDataDTO.Item.transformReturn(o));
                ListUtils.forEach(toBeInsert, o -> o.setCustomhouseTitle(
                        Objects.equals(dictService.getCountryChineseByCityName(o.getCityName()), "中国内地")? "境内" : "境外"));
                demandDBHelper.insertBatchWithoutReturnId(toBeInsert);
            }
        } catch (Exception e){
            taskLogService.genRunLog("queryObsBudgetData",
                    "queryDeviceReturnData", "数据拉取失败");
            return;
        }
    }

    @Override
    public void genStockReturnDeviceDiffDetail() {
    }


}
