package cloud.demand.app.modules.industry_cockpit.v3.model.req;

import cloud.demand.app.modules.industry_cockpit.auth.AuthCheckParam;
import cloud.demand.app.modules.industry_cockpit.v3.parse.ProductParse;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/8/15 14:23
 */
@Data
public class HistoricalScaleMonthlyAvgReq extends HistoricalScaleMonthlyReq {
    @NotNull(message = "产品不能为空")
    @SopReportWhere(parsers = {ProductParse.class})
    @AuthCheckParam(authField = "product")
    private String product;


}
