package cloud.demand.app.modules.industry_cockpit.v4.model.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/13 11:18
 */
@Data
public class SupplyDeliveryItemVO {
    // 行业筛选
    private List<String> industryDept;
    // 客户筛选
    private List<String> customerShortName;

    private List<String> customhouseTitle; // 境内外 支持多选

    // 国家筛选
    private List<String> countryName;
    // 地域筛选
    private List<String> regionName;
    // 可用区筛选
    private List<String> zoneName;
    // 机型族筛选
    private List<String> instanceGroup;
    // 机型筛选
    private List<String> instanceType;

    /**
     * 新旧下拉框，枚举：新机型，旧机型
     */
    private List<String> generationInstanceType;

    /**
     * 主力机型下拉框，枚举：主力机型，非主力机型
     */
    private List<String> mainInstanceTypeList;

    /**
     * 主力园区下拉框，枚举：主力园区，非主力园区
     */
    private List<String> mainZoneList;

    private OrderDemandVO orderDemand;

    private SupplyVO supply;

    private SupplyDeliveryGapVO supplyDeliveryGap;
}
