package cloud.demand.app.modules.industry_cockpit;

import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.common.utils.LoginUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pugwoo.wooutils.collect.ListUtils;
import yunti.boot.config.DynamicProperty;

import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

/** 动态变量 */
public class DynamicProperties {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final Supplier<List<String>> abTestUser = DynamicProperty.create("app.config.jsc.abtest.user","[]",
            e -> objectMapper.readValue(e, new TypeReference<List<String>>() {}));

    /** 是否运行 ab 测试 */
    public static List<String> getABTestUser() {
        return abTestUser.get();
    }

    /** 是否允许 abTest（在查询行业数据看板 v2 的时候，是否允许转 v3 查询） */
    public static boolean allowABTest() {
        if (EnvUtils.isLocalEnv()){
            return true;
        }
        List<String> abTestUser = getABTestUser();
        if (ListUtils.isEmpty(abTestUser)){
            return false;
        }
        if (abTestUser.contains("*")){
            return true;
        }
        String userName = LoginUtils.getUserName();
        for (String user : abTestUser) {
            if (Objects.equals(userName, user)) {
                return true;
            }
        }
        return false;
    }
}
