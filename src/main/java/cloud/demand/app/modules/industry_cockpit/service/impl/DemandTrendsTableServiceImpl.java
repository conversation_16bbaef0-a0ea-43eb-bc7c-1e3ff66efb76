package cloud.demand.app.modules.industry_cockpit.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.constant.IndustryCockpitConstant;
import cloud.demand.app.modules.industry_cockpit.entity.DemandTrendVO;
import cloud.demand.app.modules.industry_cockpit.model.IndustryCockpoitRequest;
import cloud.demand.app.modules.industry_cockpit.service.AuthService;
import cloud.demand.app.modules.industry_cockpit.service.DemandTrendsTableService;
import cloud.demand.app.modules.soe.enums.DateTypeEnum;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/5/11
 */
@Service
public class DemandTrendsTableServiceImpl implements DemandTrendsTableService {
    @Resource
    private DBHelper ckcldDBHelper;
    @Autowired
    @Qualifier("authServiceTxyScaleImpl")
    private AuthService authServiceTxyScaleImpl;
    @Autowired
    @Qualifier("authServicePplImpl")
    private AuthService authServicePplImpl;

    @Override
    public List<DemandTrendVO> getDemandTrendsTable(IndustryCockpoitRequest request, String demandType) {
        WhereSQL historyChangeWhere = authServiceTxyScaleImpl.getAuthWhereSql();
        WhereSQL predictionWhere = authServicePplImpl.getAuthWhereSql();
        WhereSQL predictionVersionWhere = authServicePplImpl.getAuthWhereSql();

        if (null == historyChangeWhere || null == predictionWhere || null == predictionVersionWhere) {
            return Collections.emptyList();
        }

        String historyChangeFixSql = "and industry_dept != '(空值)' and instance_type not like '%RM%' and instance_type not like '%RS%' and biz_range_type = '外部业务' and app_role in ('正常售卖', 'CDH', '预扣包', 'GOCP', 'CDZ') ";// and customer_tab_type != '中长尾客户'
        String predictionFixSql = "";
        if (IndustryCockpitConstant.INTERVENED.equals(request.getIsComd())) {
            // 已干预
            predictionFixSql = "and is_comd != 1 and ((industry_dept != '中长尾' AND source NOT IN ('FORECAST','APPLY_AUTO_FILL_LONGTAIL')) or (industry_dept = '中长尾' and source IN ('FORECAST')))";
        } else {
            // 未干预
            predictionFixSql = "and source in ('IMPORT','APPLY_AUTO_FILL','FORECAST') and demand_type in ('NEW', 'ELASTIC', 'RETURN')";
        }
        if ("CVM".equals(request.getProduct())) {
            historyChangeWhere.and("product in (?) ", "CVM");
        }
        int demandYear = StringUtils.isEmpty(request.getDemandYear()) ? LocalDate.now().getYear() : Integer.parseInt(request.getDemandYear());
        int year = 0;
        int month = 0;
        if (demandYear == LocalDate.now().getYear()) {
            // 当前年份
            year = LocalDate.now().getYear();
            month = LocalDate.now().getMonthValue();
        } else if(demandYear < LocalDate.now().getYear()){
            // 历史年份
            year = Integer.parseInt(request.getDemandYear());
            month = 12;
            // 历史年份只能看历史值
            request.setCurrentMonthShowHistory(true);
        }else {
            // 未来年
            year = Integer.parseInt(request.getDemandYear());
            month = 1;
        }

        LocalDate start = YearMonth.of(year, 1).atDay(1);
        LocalDate end = YearMonth.of(year, month).atDay(1).with(TemporalAdjusters.lastDayOfMonth());

        // 历史月份月末时间集合
        List<String> range = SoeCommonUtils.getRange(DateTypeEnum.YearMonth.getName(), start, end, LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).plusDays(-1));

        if(request.isCurrentMonthShowHistory()){
            if (demandYear == LocalDate.now().getYear()) {
                // 当前月展示历史值
                range.add(LocalDate.now().plusDays(-1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
            month = month + 1;
        }
        if(month > 12){
            predictionVersionWhere.and("1 = 0 ");
            predictionWhere.and("1 = 0 ");
        }else {
            String startBuyDate = LocalDate.of(year, month, 1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String endBuyDate = LocalDate.of(year, 12, 31).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            predictionWhere.and("begin_buy_date >= ? ", startBuyDate);
            predictionWhere.and("begin_buy_date <= ? ", endBuyDate);
            predictionVersionWhere.and("begin_buy_date >= ? ", startBuyDate);
            predictionVersionWhere.and("begin_buy_date <= ? ", endBuyDate);
        }


        historyChangeWhere.and("stat_time in (?) ", range);
        if(Objects.nonNull(request.getCustomerRange())){
            historyChangeWhere.and(" is_inner = ?  ", request.getCustomerRange());
            predictionWhere.and("uin_type = ?  ", request.getCustomerRange() == 1 ? 0 : 1);
            predictionVersionWhere.and("uin_type = ?  ", request.getCustomerRange() == 1 ? 0 : 1);
        }
        if ("CVM".equals(request.getProduct())) {
            predictionWhere.and("product in (?) ", "CVM&CBS");
        }

        if (!CollectionUtils.isEmpty(request.getIndustryDept())) {
            historyChangeWhere.and("industry_dept in (?)", request.getIndustryDept());
            predictionWhere.and("industry_dept in (?)", request.getIndustryDept());
        }
        if (!CollectionUtils.isEmpty(request.getWarZone())) {
            historyChangeWhere.and("crp_war_zone in (?)", request.getWarZone());
            predictionWhere.and("war_zone in (?)", request.getWarZone());
        }
        if (!CollectionUtils.isEmpty(request.getCustomerShortNames())) {
            historyChangeWhere.and("un_customer_short_name in (?)", request.getCustomerShortNames());
            predictionWhere.and("common_customer_short_name in (?)", request.getCustomerShortNames());
        }
        if (!CollectionUtils.isEmpty(request.getInstanceType())) {
            historyChangeWhere.and("instance_type in (?)", request.getInstanceType());
            predictionWhere.and("instance_type in (?)", request.getInstanceType());
        }
        if (!CollectionUtils.isEmpty(request.getCustomhouseTitle())) {
            historyChangeWhere.and("customhouse_title in (?)", request.getCustomhouseTitle());
            predictionWhere.and("customhouse_title in (?)", request.getCustomhouseTitle());
        }
        if (!CollectionUtils.isEmpty(request.getRegionName())) {
            historyChangeWhere.and("region_name in (?)", request.getRegionName());
            predictionWhere.and("region_name in (?)", request.getRegionName());
        }
        if (!CollectionUtils.isEmpty(request.getZoneName())) {
            historyChangeWhere.and("zone_name in (?)", request.getZoneName());
            predictionWhere.and("zone_name in (?)", request.getZoneName());
        }

        if (null == request.getVersionInfo()) {
            // 预测版本为空
            predictionVersionWhere.and("1 = 0 ");
        } else {
            IndustryCockpoitRequest.VersionInfo versionInfo = request.getVersionInfo();
            String versionCode = versionInfo.getVersionCode();
            String startMonth = versionInfo.getStartMonth();
            String endMonth = versionInfo.getEndMonth(request.getDemandYear());
            // 最新版排除指定版本的数据
            WhereSQL orWhereSQL = new WhereSQL();
            orWhereSQL.or("toYYYYMM(begin_buy_date) < ? ", startMonth);
            orWhereSQL.or("toYYYYMM(begin_buy_date) > ? ", endMonth);
            predictionWhere.and(orWhereSQL);
            predictionWhere.and(" year(begin_buy_date) = ? ", year);

            // 指定版本加上该批数据
            predictionVersionWhere.and("version_code = ? ", versionCode);
            predictionVersionWhere.and("toYYYYMM(begin_buy_date) >= ? ", startMonth);
            predictionVersionWhere.and("toYYYYMM(begin_buy_date) <= ? ", endMonth);
            predictionVersionWhere.and(" year(begin_buy_date) = ? ", year);
            if ("CVM".equals(request.getProduct())) {
                predictionVersionWhere.and("product in (?) ", "CVM&CBS");
            }
            if (!CollectionUtils.isEmpty(request.getIndustryDept())) {
                predictionVersionWhere.and("industry_dept in (?)", request.getIndustryDept());
            }
            if (!CollectionUtils.isEmpty(request.getWarZone())) {
                predictionVersionWhere.and("war_zone in (?)", request.getWarZone());
            }
            if (!CollectionUtils.isEmpty(request.getCustomerShortNames())) {
                predictionVersionWhere.and("common_customer_short_name in (?)", request.getCustomerShortNames());
            }
            if (!CollectionUtils.isEmpty(request.getInstanceType())) {
                predictionVersionWhere.and("instance_type in (?)", request.getInstanceType());
            }
            if (!CollectionUtils.isEmpty(request.getRegionName())) {
                predictionVersionWhere.and("region_name in (?)", request.getRegionName());
            }
            if (!CollectionUtils.isEmpty(request.getZoneName())) {
                predictionVersionWhere.and("zone_name in (?)", request.getZoneName());
            }
            if (!CollectionUtils.isEmpty(request.getCustomhouseTitle())) {
                predictionVersionWhere.and("customhouse_title in (?)", request.getCustomhouseTitle());
            }
        }

        String currentYearSql = ORMUtils.getSql("/sql/industry_cockpit/9_demand_trend_table.sql");
        currentYearSql = currentYearSql.replace("${HISTORY_FILTER}", historyChangeWhere.getSQL());
        currentYearSql = currentYearSql.replace("${PREDICTION_FILTER}", predictionWhere.getSQL());
        currentYearSql = currentYearSql.replace("${VERSION_PREDICTION_FILTER}", predictionVersionWhere.getSQL());
        currentYearSql = currentYearSql.replace("${queryRange}", request.getQueryRange());
        // 根据前端传的维度去做group by
        if (request.getDims().contains(IndustryCockpitConstant.INDUSTRY_DEPT)) {
            currentYearSql = currentYearSql.replaceAll("\\$\\{industry_dept_field}", "industry_dept, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{industry_dept_group}", ",industry_dept ");
        } else {
            currentYearSql = currentYearSql.replaceAll("\\$\\{industry_dept_field}", "'' as industry_dept, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{industry_dept_group}", " ");
        }
        if (request.getDims().contains(IndustryCockpitConstant.WAR_ZONE)) {
            currentYearSql = currentYearSql.replaceAll("\\$\\{war_zone_field}", "war_zone, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{war_zone_group}", ",war_zone ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{has_war_zone}", "1");
        } else {
            currentYearSql = currentYearSql.replaceAll("\\$\\{war_zone_field}", "'' as war_zone, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{war_zone_group}", " ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{has_war_zone}", "0");
        }
        if (request.getDims().contains(IndustryCockpitConstant.CUSTOMER_SHORT_NAME)) {
            currentYearSql = currentYearSql.replaceAll("\\$\\{customer_short_name_field}", "customer_short_name, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{customer_short_name_group}", ",customer_short_name ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{has_customer_short_name}", "1");
        } else {
            currentYearSql = currentYearSql.replaceAll("\\$\\{customer_short_name_field}", "'' as customer_short_name, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{customer_short_name_group}", " ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{has_customer_short_name}", "0");
        }
        if (request.getDims().contains(IndustryCockpitConstant.INSTANCE_TYPE)) {
            currentYearSql = currentYearSql.replaceAll("\\$\\{instance_type_field}", "instance_type, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{instance_type_group}", ",instance_type ");
        } else {
            currentYearSql = currentYearSql.replaceAll("\\$\\{instance_type_field}", "'' as instance_type, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{instance_type_group}", " ");
        }
        if (request.getDims().contains(IndustryCockpitConstant.REGION_NAME)) {
            currentYearSql = currentYearSql.replaceAll("\\$\\{region_name_field}", " region_name, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{region_name_group}", " ,region_name ");
        } else {
            currentYearSql = currentYearSql.replaceAll("\\$\\{region_name_field}", " '' as region_name, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{region_name_group}", " ");
        }
        if (request.getDims().contains(IndustryCockpitConstant.ZONE_NAME)) {
            currentYearSql = currentYearSql.replaceAll("\\$\\{zone_name_field}", "zone_name, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{zone_name_group}", ", zone_name");
        } else {
            currentYearSql = currentYearSql.replaceAll("\\$\\{zone_name_field}", " '' as zone_name, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{zone_name_group}", " ");
        }

        currentYearSql = currentYearSql.replace("${PREDICTION_FIX_WHERE}", predictionFixSql);
        currentYearSql = currentYearSql.replace("${HISTORY_CHANGE_FIX_WHERE}", historyChangeFixSql);

        currentYearSql = currentYearSql.replaceAll("\\$\\{demand_type\\}", demandType);
        currentYearSql = currentYearSql.replaceAll("\\$\\{mergeInstanceType}", request.getMergeInstanceType());

        // 参数合并
        Object[] historyParams = historyChangeWhere.getParams();
        Object[] predictionParams = predictionWhere.getParams();
        Object[] predictionVersionParams = predictionVersionWhere.getParams();
        Object[] paramAll = new Object[historyParams.length + predictionParams.length + predictionVersionParams.length];
        System.arraycopy(historyParams, 0, paramAll, 0, historyParams.length);
        System.arraycopy(predictionParams, 0, paramAll, historyParams.length, predictionParams.length);
        System.arraycopy(predictionVersionParams, 0, paramAll, historyParams.length + predictionParams.length, predictionVersionParams.length);

        // 获取今年的预测增量
        List<DemandTrendVO> demandTrendVO = ckcldDBHelper.getRaw(DemandTrendVO.class, currentYearSql, paramAll);

        return demandTrendVO;
    }
}
