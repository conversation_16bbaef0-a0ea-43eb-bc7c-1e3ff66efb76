package cloud.demand.app.modules.industry_cockpit.service.impl;

import com.pugwoo.dbhelper.sql.WhereSQL;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/11
 */
@Service
public class AuthServiceProductTypeWithWarZoneNameImpl extends AuthServiceCommonImpl {
    @Override
    void buildWarZoneWhere(WhereSQL whereSQL, List<String> warZone) {
        whereSQL.and("war_zone_name in (?) ", warZone);
    }
    @Override
    void buildProductWhere(WhereSQL whereSQL, List<String> product) {
        whereSQL.and("product_type in (?) ", product);
    }
}
