package cloud.demand.app.modules.industry_cockpit.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 服务水平排行榜
 *
 * <AUTHOR>
 * @since 2024/4/22
 */
@Data
public class ServiceLevelRankingListTableResponse {

    private List<Item> data;

    @Data
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class Item {
        private String IndustryDept; // 行业
        private String customerShortName; // 客户
        private BigDecimal overallServiceLevel; // 整体服务水平
        private BigDecimal expectedServiceLevel; // 预测内服务水平
        private BigDecimal unexpectedServiceLevel; // 预测外服务水平
    }

}
