package cloud.demand.app.modules.industry_cockpit.model;

import cloud.demand.app.modules.industry_cockpit.entity.DemandTrendVO;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 需求趋势 表格 响应
 *
 * <AUTHOR>
 * @since 2024/4/22
 */
@Data
public class DemandTrendsTableResponse {

    private Integer count;
    private List<ResultData> data;

    @Data
    public static class ResultData {
        private String industryDept; // 行业部门
        private String warZone; // 战区
        private String customerShortName; // 客户简称
        private String instanceType; // 实例类型
        private String regionName; //  地域
        private String zoneName; // 可用区
        private ResultDataValue currentVersionData; // 当前指定的版本数据
        private ResultDataValue compareVersionData; // 对比的版本数据
        private ResultDataValue lastYearVersionData; // 上一年的实现数据
        private ResultDataValue yearOnYearRationData; // 同比数据
    }

    @Data
    public static class ResultDataValue {
        private BigDecimal janValue;
        private BigDecimal febValue;
        private BigDecimal marValue;
        private BigDecimal aprValue;
        private BigDecimal mayValue;
        private BigDecimal junValue;
        private BigDecimal julValue;
        private BigDecimal augValue;
        private BigDecimal sepValue;
        private BigDecimal octValue;
        private BigDecimal novValue;
        private BigDecimal decValue;
        private BigDecimal q1Value;
        private BigDecimal q2Value;
        private BigDecimal q3Value;
        private BigDecimal q4Value;
        private BigDecimal totalValue;

        public static ResultDataValue getResultDataValue(DemandTrendVO sourceData) {
            ResultDataValue resultDataValue = new ResultDataValue();
            resultDataValue.setJanValue(sourceData.getJanCores());
            resultDataValue.setFebValue(sourceData.getFebCores());
            resultDataValue.setMarValue(sourceData.getMarCores());
            resultDataValue.setAprValue(sourceData.getAprCores());
            resultDataValue.setMayValue(sourceData.getMayCores());
            resultDataValue.setJunValue(sourceData.getJunCores());
            resultDataValue.setJulValue(sourceData.getJulCores());
            resultDataValue.setAugValue(sourceData.getAugCores());
            resultDataValue.setSepValue(sourceData.getSepCores());
            resultDataValue.setOctValue(sourceData.getOctCores());
            resultDataValue.setNovValue(sourceData.getNovCores());
            resultDataValue.setDecValue(sourceData.getDecCores());
            resultDataValue.setQ1Value(sourceData.getQ1Cores());
            resultDataValue.setQ2Value(sourceData.getQ2Cores());
            resultDataValue.setQ3Value(sourceData.getQ3Cores());
            resultDataValue.setQ4Value(sourceData.getQ4Cores());
            resultDataValue.setTotalValue(sourceData.getTotalCores());
            return resultDataValue;
        }

    }

    /**
     * 处理结果集
     *
     * @param comparedemandTrendVO 对比或者同比数据
     * @return 结果数据
     */
    private static ResultDataValue handlerResultDataValue(DemandTrendVO comparedemandTrendVO) {
        if (Objects.isNull(comparedemandTrendVO)) {
            return null;
        }
        return ResultDataValue.getResultDataValue(comparedemandTrendVO);
    }

    private static ResultDataValue handlerResultDataValue(ResultDataValue currentVersionData, ResultDataValue lastYearVersionData) {
        ResultDataValue resultDataValue = new ResultDataValue();
        if (null != lastYearVersionData) {
            BigDecimal janValue = null == lastYearVersionData.getJanValue() || BigDecimal.ZERO.compareTo(lastYearVersionData.getJanValue()) == 0 ? BigDecimal.ZERO : (currentVersionData.getJanValue().subtract(lastYearVersionData.getJanValue())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getJanValue(), 2, RoundingMode.HALF_UP);
            BigDecimal febValue = null == lastYearVersionData.getFebValue() || BigDecimal.ZERO.compareTo(lastYearVersionData.getFebValue()) == 0 ? BigDecimal.ZERO : (currentVersionData.getFebValue().subtract(lastYearVersionData.getFebValue())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getFebValue(), 2, RoundingMode.HALF_UP);
            BigDecimal marValue = null == lastYearVersionData.getMarValue() || BigDecimal.ZERO.compareTo(lastYearVersionData.getMarValue()) == 0 ? BigDecimal.ZERO : (currentVersionData.getMarValue().subtract(lastYearVersionData.getMarValue())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getMarValue(), 2, RoundingMode.HALF_UP);
            BigDecimal aprValue = null == lastYearVersionData.getAprValue() || BigDecimal.ZERO.compareTo(lastYearVersionData.getAprValue()) == 0 ? BigDecimal.ZERO : (currentVersionData.getAprValue().subtract(lastYearVersionData.getAprValue())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getAprValue(), 2, RoundingMode.HALF_UP);
            BigDecimal mayValue = null == lastYearVersionData.getMayValue() || BigDecimal.ZERO.compareTo(lastYearVersionData.getMayValue()) == 0 ? BigDecimal.ZERO : (currentVersionData.getMayValue().subtract(lastYearVersionData.getMayValue())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getMayValue(), 2, RoundingMode.HALF_UP);
            BigDecimal junValue = null == lastYearVersionData.getJunValue() || BigDecimal.ZERO.compareTo(lastYearVersionData.getJunValue()) == 0 ? BigDecimal.ZERO : (currentVersionData.getJunValue().subtract(lastYearVersionData.getJunValue())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getJunValue(), 2, RoundingMode.HALF_UP);
            BigDecimal julValue = null == lastYearVersionData.getJulValue() || BigDecimal.ZERO.compareTo(lastYearVersionData.getJulValue()) == 0 ? BigDecimal.ZERO : (currentVersionData.getJulValue().subtract(lastYearVersionData.getJulValue())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getJulValue(), 2, RoundingMode.HALF_UP);
            BigDecimal augValue = null == lastYearVersionData.getAugValue() || BigDecimal.ZERO.compareTo(lastYearVersionData.getAugValue()) == 0 ? BigDecimal.ZERO : (currentVersionData.getAugValue().subtract(lastYearVersionData.getAugValue())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getAugValue(), 2, RoundingMode.HALF_UP);
            BigDecimal sepValue = null == lastYearVersionData.getSepValue() || BigDecimal.ZERO.compareTo(lastYearVersionData.getSepValue()) == 0 ? BigDecimal.ZERO : (currentVersionData.getSepValue().subtract(lastYearVersionData.getSepValue())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getSepValue(), 2, RoundingMode.HALF_UP);
            BigDecimal octValue = null == lastYearVersionData.getOctValue() || BigDecimal.ZERO.compareTo(lastYearVersionData.getOctValue()) == 0 ? BigDecimal.ZERO : (currentVersionData.getOctValue().subtract(lastYearVersionData.getOctValue())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getOctValue(), 2, RoundingMode.HALF_UP);
            BigDecimal novValue = null == lastYearVersionData.getNovValue() || BigDecimal.ZERO.compareTo(lastYearVersionData.getNovValue()) == 0 ? BigDecimal.ZERO : (currentVersionData.getNovValue().subtract(lastYearVersionData.getNovValue())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getNovValue(), 2, RoundingMode.HALF_UP);
            BigDecimal decValue = null == lastYearVersionData.getDecValue() || BigDecimal.ZERO.compareTo(lastYearVersionData.getDecValue()) == 0 ? BigDecimal.ZERO : (currentVersionData.getDecValue().subtract(lastYearVersionData.getDecValue())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getDecValue(), 2, RoundingMode.HALF_UP);
            BigDecimal q1Value = null == lastYearVersionData.getQ1Value() || BigDecimal.ZERO.compareTo(lastYearVersionData.getQ1Value()) == 0 ? BigDecimal.ZERO : (currentVersionData.getQ1Value().subtract(lastYearVersionData.getQ1Value())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getQ1Value(), 2, RoundingMode.HALF_UP);
            BigDecimal q2Value = null == lastYearVersionData.getQ2Value() || BigDecimal.ZERO.compareTo(lastYearVersionData.getQ2Value()) == 0 ? BigDecimal.ZERO : (currentVersionData.getQ2Value().subtract(lastYearVersionData.getQ2Value())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getQ2Value(), 2, RoundingMode.HALF_UP);
            BigDecimal q3Value = null == lastYearVersionData.getQ3Value() || BigDecimal.ZERO.compareTo(lastYearVersionData.getQ3Value()) == 0 ? BigDecimal.ZERO : (currentVersionData.getQ3Value().subtract(lastYearVersionData.getQ3Value())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getQ3Value(), 2, RoundingMode.HALF_UP);
            BigDecimal q4Value = null == lastYearVersionData.getQ4Value() || BigDecimal.ZERO.compareTo(lastYearVersionData.getQ4Value()) == 0 ? BigDecimal.ZERO : (currentVersionData.getQ4Value().subtract(lastYearVersionData.getQ4Value())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getQ4Value(), 2, RoundingMode.HALF_UP);
            BigDecimal totalValue = null == lastYearVersionData.getTotalValue() || BigDecimal.ZERO.compareTo(lastYearVersionData.getTotalValue()) == 0 ? BigDecimal.ZERO : (currentVersionData.getTotalValue().subtract(lastYearVersionData.getTotalValue())).multiply(BigDecimal.valueOf(100)).divide(lastYearVersionData.getTotalValue(), 2, RoundingMode.HALF_UP);

            resultDataValue.setJanValue(janValue);
            resultDataValue.setFebValue(febValue);
            resultDataValue.setMarValue(marValue);
            resultDataValue.setAprValue(aprValue);
            resultDataValue.setMayValue(mayValue);
            resultDataValue.setJunValue(junValue);
            resultDataValue.setJulValue(julValue);
            resultDataValue.setAugValue(augValue);
            resultDataValue.setSepValue(sepValue);
            resultDataValue.setOctValue(octValue);
            resultDataValue.setNovValue(novValue);
            resultDataValue.setDecValue(decValue);
            resultDataValue.setQ1Value(q1Value);
            resultDataValue.setQ2Value(q2Value);
            resultDataValue.setQ3Value(q3Value);
            resultDataValue.setQ4Value(q4Value);
            resultDataValue.setTotalValue(totalValue);
        }
        return resultDataValue;
    }

    /**
     * 解析返回报文
     *
     * @param predictVersionDemandTrendsTable  预测数据
     * @param comparedVersionDemandTrendsTable 对比数据
     * @param lastYearDemandTrendsTable        同比上年的数据
     * @return 返回报文
     */
    public static DemandTrendsTableResponse parse(List<DemandTrendVO> predictVersionDemandTrendsTable,
                                                  final List<DemandTrendVO> comparedVersionDemandTrendsTable,
                                                  final List<DemandTrendVO> lastYearDemandTrendsTable) {
        Map<String, DemandTrendVO> comparedVersionDemandTrendsTableMap = comparedVersionDemandTrendsTable.stream()
                .collect(Collectors.toMap(DemandTrendVO::getKey, Function.identity(), (k1, k2) -> k1));
        Map<String, DemandTrendVO> lastYearDemandTrendsTableMap = lastYearDemandTrendsTable.stream()
                .collect(Collectors.toMap(DemandTrendVO::getKey, Function.identity(), (k1, k2) -> k1));

        DemandTrendsTableResponse result = new DemandTrendsTableResponse();
        result.setCount(predictVersionDemandTrendsTable.size());
        result.setData(predictVersionDemandTrendsTable.stream().map(item -> {
            String industryDept = StringUtils.isEmpty(item.getIndustryDept()) ? "" : item.getIndustryDept();
            String warZone = StringUtils.isEmpty(item.getWarZone()) ? "" : item.getWarZone();
            String customerShortName = StringUtils.isEmpty(item.getCustomerShortName()) ? "" : item.getCustomerShortName();
            String instanceType = StringUtils.isEmpty(item.getInstanceType()) ? "" : item.getInstanceType();
            String regionName = StringUtils.isEmpty(item.getRegionName()) ? "" : item.getRegionName();
            String zoneName = StringUtils.isEmpty(item.getZoneName()) ? "" : item.getZoneName();

            ResultDataValue currentVersionData = ResultDataValue.getResultDataValue(item);

            ResultData resultData = new ResultData();
            resultData.setIndustryDept(industryDept);
            resultData.setWarZone(warZone);
            resultData.setCustomerShortName(customerShortName);
            resultData.setInstanceType(instanceType);
            resultData.setRegionName(regionName);
            resultData.setZoneName(zoneName);
            resultData.setCurrentVersionData(currentVersionData);

            // 处理对比版本
            ResultDataValue compareVersionData = handlerResultDataValue(comparedVersionDemandTrendsTableMap.get(item.getKey()));
            resultData.setCompareVersionData(compareVersionData);
            // 处理同比版本
            ResultDataValue lastYearVersionData = handlerResultDataValue(lastYearDemandTrendsTableMap.get(item.getKey()));
            resultData.setLastYearVersionData(lastYearVersionData);
            // 计算同比数据
            ResultDataValue yearOnYearRationData = handlerResultDataValue(currentVersionData, lastYearVersionData);
            resultData.setYearOnYearRationData(yearOnYearRationData);

            return resultData;
        }).collect(Collectors.toList()));
        return result;
    }
}
