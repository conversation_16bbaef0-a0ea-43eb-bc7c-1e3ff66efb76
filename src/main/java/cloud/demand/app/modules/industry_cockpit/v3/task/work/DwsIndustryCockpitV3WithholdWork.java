package cloud.demand.app.modules.industry_cockpit.v3.task.work;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.industry_cockpit.v3.entity.DwdTxyAppidInfoCfDO;
import cloud.demand.app.modules.industry_cockpit.v3.entity.DwsIndustryCockpitV3WithholdDfDO;
import cloud.demand.app.modules.industry_cockpit.v3.service.IndustryCockpitV3DictService;
import cloud.demand.app.modules.industry_cockpit.v3.task.process.DwsIndustryCockpitV3WithholdProcess;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.SimpleTaskEnum;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.work.AbstractSopWork;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/26 11:11
 */
@Service
@Slf4j(topic = "行业驾驶舱-V3-预扣-dsm数据：")
public class DwsIndustryCockpitV3WithholdWork extends AbstractSopWork<SimpleCommonTask> {

    @Resource
    private DwsIndustryCockpitV3WithholdProcess process;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private DBHelper planDBHelper;

    @Resource
    private IndustryCockpitV3DictService industryCockpitV3DictService;

    @Resource
    private DictService dictService;


    @Override
    public ISopProcess<SimpleCommonTask> getTask() {
        return process;
    }

    @Override
    public ITaskEnum getEnum() {
        return SimpleTaskEnum.INDUSTRY_COCKPIT_V3_WITHHOLD_DWS;
    }

    @TaskLog(taskName = "DwsIndustryCockpitV3WithholdWork")
    @Scheduled(fixedRate = 30 * 1000)
    @Override
    public void work() {
        super.work();
    }


    @Override
    public void doWork(SimpleCommonTask simpleCommonTask) {
        String version = simpleCommonTask.getVersion();
        // 需要用到TaskLog，所以用bean取调用
        SpringUtil.getBean(DwsIndustryCockpitV3WithholdWork.class).genData(version);
    }

    @TaskLog(taskName = "industryCockpitV3/whithhold@DWS")
    public void genData(String statTime) {
        // 查询数据
        String sql = ORMUtils.getSql("/sql/industry_cockpit/v3/8_sync_withhold_data.sql");
        List<DwsIndustryCockpitV3WithholdDfDO> dataList = planDBHelper.getRaw(DwsIndustryCockpitV3WithholdDfDO.class, sql, statTime+" 00:00:00");
        // 清洗数据
        clean(dataList);
        // 删除分区
        CkDBUtils.delete(ckcldStdCrpDBHelper, statTime, DwsIndustryCockpitV3WithholdDfDO.class);
        // 添加
        log.info(" size :" + dataList.size());
        CkDBUtils.saveBatch(ckcldStdCrpDBHelper, dataList);
    }

    private void clean(List<DwsIndustryCockpitV3WithholdDfDO> dataList) {
        Map<String,String> instanceTypeGpuCardTypeMap = industryCockpitV3DictService.getInstanceTypeGpuCardTypeMapping();
        List<Long> appIdList = dataList.stream().map(DwsIndustryCockpitV3WithholdDfDO::getAppId).distinct().collect(Collectors.toList());
        Map<Long,DwdTxyAppidInfoCfDO> appIdMap = industryCockpitV3DictService.getAllDwdTxyAppidInfoCfDOList(appIdList);

        // 获取通用客户简称
        List<IndustryDemandIndustryWarZoneDictDO> customerConfig = dictService.queryEnableIndustryWarZoneCustomerConfig();
        // 过滤非空客户且启用的
        customerConfig = customerConfig.stream().filter(item ->
                StringUtils.isNotBlank(item.getCustomerName())
                        && StringUtils.isNotBlank(item.getCommonCustomerName())
                        && BooleanUtils.isTrue(item.getIsEnable())).collect(Collectors.toList());
        Map<String,IndustryDemandIndustryWarZoneDictDO> customerMap = customerConfig.stream().collect(Collectors.toMap(IndustryDemandIndustryWarZoneDictDO::getCustomerName, Function.identity(),(k1, k2) -> k1));

        for(DwsIndustryCockpitV3WithholdDfDO item : dataList){
            String gpuCordType = instanceTypeGpuCardTypeMap.getOrDefault(item.getInstanceType(), StdUtils.EMPTY_STR);
            item.setGpuCardType(gpuCordType);
            DwdTxyAppidInfoCfDO dwdTxyAppidInfoCfDO = appIdMap.get(item.getAppId());
            if(Objects.nonNull(dwdTxyAppidInfoCfDO)){
                item.setIndustryDept(dwdTxyAppidInfoCfDO.getIndustryDept());
                item.setIsInner(dwdTxyAppidInfoCfDO.getUinType() == 0 ? 1 : 0);
                item.setUin(dwdTxyAppidInfoCfDO.getUin());
                item.setCustomerType(dwdTxyAppidInfoCfDO.getCustomerType());
                IndustryDemandIndustryWarZoneDictDO customer = customerMap.get(dwdTxyAppidInfoCfDO.getCustomerShortName());
                if(Objects.nonNull(customer)){
                    item.setCrpWarZone(customer.getWarZoneName());
                    item.setUnCustomerShortName(customer.getCommonCustomerName());
                }else {
                    item.setCrpWarZone(dwdTxyAppidInfoCfDO.getWarZone());
                    item.setUnCustomerShortName(dwdTxyAppidInfoCfDO.getCustomerShortName());
                }
                item.setWarZone(dwdTxyAppidInfoCfDO.getWarZone());
                item.setCustomerShortName(dwdTxyAppidInfoCfDO.getCustomerShortName());
            }
        }
    }

    /**
     * 每天凌晨8点执行一次
     */
    @Scheduled(cron = "0 0 8 * * ? ")
    public void initTask() {
        initTask(null);
    }

    /**
     * 初始化任务
     */
    public void initTask(LocalDate statTime) {
        if (statTime == null) {
            //昨天
            statTime = LocalDate.now().plusDays(-1);
        }
        SimpleCommonTask simpleCommonTask = new SimpleCommonTask();
        simpleCommonTask.setVersion(DateUtils.format(statTime));
        process.initTask(simpleCommonTask, getEnum());
    }
}
