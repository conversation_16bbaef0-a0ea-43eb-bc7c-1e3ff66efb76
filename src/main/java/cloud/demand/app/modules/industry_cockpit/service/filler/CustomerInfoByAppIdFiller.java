package cloud.demand.app.modules.industry_cockpit.service.filler;

public interface CustomerInfoByAppIdFiller {

    /** customer_uin */
    String provideAppId();



    /** 客户uin */
    void fillUin(String uin);

    /** 客户名称 */
    void fillCustomerName(String customerName);



    /** 客户简称 */
    void fillCustomerShortName(String customerShortName);

    /** 战区 */
    void fillPanShiWarZone(String panShiWarZone);

    /** 行业部门 */
    void fillIndustryDept(String industryDept);

    void fillIsInner(Integer isInner);

    void fillUinType(Integer uinType);

    void fillCustomerType(Integer customerType);

}
