package cloud.demand.app.modules.industry_cockpit.model;

import cloud.demand.app.modules.industry_cockpit.entity.ChartDataItemVO;
import cloud.demand.app.modules.industry_cockpit.entity.ScaleChangeVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 净增规模预测 图表
 *
 * <AUTHOR>
 * @since 2024/4/22
 */
@Data
public class NetGrowthForecastChartResponse {
    private String year;
    private BigDecimal predictedCores; // 净增核心数
    @JsonIgnore
    private BigDecimal predictedYearOnYearRatioValue; // 同比
    private String predictedYearOnYearRatio; // 同比

    private BigDecimal predictedYearOnYearChange; // 净增同比23年变化
    private String predictedActualAchievementRatio; // 实际达成占比
    @JsonIgnore
    private BigDecimal predictedActualAchievementRatioValue; // 实际达成占比

    private BigDecimal newOrElasticCores; // 新增弹性净增核心数
    @JsonIgnore
    private BigDecimal newOrElasticYearOnYearRatioValue; // 同比
    private String newOrElasticYearOnYearRatio; // 同比

    private BigDecimal newOrElasticYearOnYearChange; // 新增&弹性同比23年变化
    private String newOrElasticActualAchievementRatio; // 实际达成
    @JsonIgnore
    private BigDecimal newOrElasticActualAchievementRatioValue; // 实际达成

    private BigDecimal returnCores; // 退回净增核心数
    @JsonIgnore
    private BigDecimal returnYearOnYearRatioValue; // 同比
    private String returnYearOnYearRatio; // 同比

    private BigDecimal returnYearOnYearChange; // 退回同比23年变化
    private String returnActualAchievementRatio; // 实际达成
    @JsonIgnore
    private BigDecimal returnActualAchievementRatioValue; // 实际达成

    private List<ChartItem> data; // 图表数据

    @Data
    public static class ChartItem {
        private String dataMonth;
        private BigDecimal value;

        public ChartItem() {
        }

        public ChartItem(String dataMonth, BigDecimal value) {
            this.dataMonth = dataMonth;
            this.value = value;
        }
    }

    public void parseYear(IndustryCockpoitRequest request) {
        String demandYear = request.getDemandYear();
        if (StringUtils.isEmpty(demandYear)) {
            this.setYear(String.valueOf(LocalDate.now().getYear()));
        } else {
            this.setYear(demandYear);
        }
    }

    public void parseChartItemData(List<ChartDataItemVO> dailyInventoryBillingScaleData, List<ChartDataItemVO> monthlyPredictionNetIncreaseData) {
        List<ChartItem> historyChartItem = new ArrayList<>();
        BigDecimal lastestDataValue = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(dailyInventoryBillingScaleData)) {
            historyChartItem = dailyInventoryBillingScaleData.stream().map(item -> {
                String dataName = item.getDataName();
                BigDecimal dataValue = item.getDataValue();
                return new ChartItem(dataName, dataValue);
            }).collect(Collectors.toList());

            ChartDataItemVO lastestData = dailyInventoryBillingScaleData.stream().sorted(Comparator.comparing(ChartDataItemVO::getDataName)).collect(Collectors.toList()).get(dailyInventoryBillingScaleData.size() - 1);
            lastestDataValue = lastestData.getDataValue();
        }

        List<ChartItem> predictionChartItem = new LinkedList<>();
        if (!CollectionUtils.isEmpty(monthlyPredictionNetIncreaseData)) {
            for (ChartDataItemVO chartDataItemVO : monthlyPredictionNetIncreaseData) {
                // 数据月份
                String dataMonth = chartDataItemVO.getDataName();
                BigDecimal reduceDataValue = monthlyPredictionNetIncreaseData.stream()
                        .filter(item -> Integer.parseInt(item.getDataName()) <= Integer.parseInt(dataMonth))
                        .map(ChartDataItemVO::getDataValue)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal predictionData = lastestDataValue.add(reduceDataValue);

                LocalDate startDate = LocalDate.parse(dataMonth + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
                LocalDate endDate = startDate.plusMonths(1);
                for (LocalDate date = startDate; date.isBefore(endDate); date = date.plusDays(1)) {
                    predictionChartItem.add(new ChartItem(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), predictionData));
                }
            }
        }
        predictionChartItem = predictionChartItem.stream().sorted(Comparator.comparing(ChartItem::getDataMonth)).collect(Collectors.toList());

        historyChartItem.addAll(predictionChartItem);
        this.setData(historyChartItem);
    }

    /**
     * 计算环同比数据
     *
     * @param lastYearChangeData                 去年的实际达成
     * @param currentYearChangeData              今年的实际达成
     * @param currentYearPredictionDataFromNowOn 今年除去已达成的月份后剩余的预测数据
     */
    public void calculateYearOnYearRatio(List<ScaleChangeVO> lastYearChangeData, List<ScaleChangeVO> currentYearChangeData,
                                         List<ScaleChangeVO> currentYearPredictionDataFromNowOn) {
        ScaleChangeVO lastYearChangeDataItem = ListUtils.isEmpty(lastYearChangeData)?  new ScaleChangeVO(): lastYearChangeData.get(0);
        ScaleChangeVO currentYearChangeDataItem = ListUtils.isEmpty(currentYearChangeData)?  new ScaleChangeVO(): currentYearChangeData.get(0);
        ScaleChangeVO currentYearPredictionDataFromNowOnItem = ListUtils.isEmpty(currentYearPredictionDataFromNowOn)?  new ScaleChangeVO(): currentYearPredictionDataFromNowOn.get(0);

        BigDecimal currentPredictionNetCores = currentYearChangeDataItem.getNetCores().add(currentYearPredictionDataFromNowOnItem.getNetCores());
        BigDecimal currentPredictionIncreaseCores = currentYearChangeDataItem.getIncreaseCores().add(currentYearPredictionDataFromNowOnItem.getIncreaseCores());
        BigDecimal currentPredictionReturnCores = currentYearChangeDataItem.getReturnCores().add(currentYearPredictionDataFromNowOnItem.getReturnCores());

        BigDecimal predictedYearOnYearChange = currentPredictionNetCores.subtract(lastYearChangeDataItem.getNetCores());
        BigDecimal newOrElasticYearOnYearChange = currentPredictionIncreaseCores.subtract(lastYearChangeDataItem.getIncreaseCores());
        BigDecimal returnYearOnYeaChange = currentPredictionReturnCores.subtract(lastYearChangeDataItem.getReturnCores());

        BigDecimal predictedYearOnYearRatioValue = lastYearChangeDataItem.getNetCores().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                BigDecimal.valueOf(100).multiply(predictedYearOnYearChange).divide(lastYearChangeDataItem.getNetCores(), 2, RoundingMode.HALF_UP);
        BigDecimal newOrElasticYearOnYearRatioValue = lastYearChangeDataItem.getIncreaseCores().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                BigDecimal.valueOf(100).multiply(newOrElasticYearOnYearChange).divide(lastYearChangeDataItem.getIncreaseCores(), 2, RoundingMode.HALF_UP);
        BigDecimal returnYearOnYearRatioValue = lastYearChangeDataItem.getReturnCores().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                BigDecimal.valueOf(100).multiply(returnYearOnYeaChange).divide(lastYearChangeDataItem.getReturnCores(), 2, RoundingMode.HALF_UP);
        BigDecimal predictedActualAchievementRatio = currentPredictionNetCores.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                BigDecimal.valueOf(100).multiply(currentYearChangeDataItem.getNetCores().divide(currentPredictionNetCores, 2, RoundingMode.HALF_UP));
        BigDecimal newOrElasticActualAchievementRatio = currentPredictionIncreaseCores.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                BigDecimal.valueOf(100).multiply(currentYearChangeDataItem.getIncreaseCores().divide(currentPredictionIncreaseCores, 2, RoundingMode.HALF_UP));
        BigDecimal returnActualAchievementRatio = currentPredictionReturnCores.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                BigDecimal.valueOf(100).multiply(currentYearChangeDataItem.getReturnCores().divide(currentPredictionReturnCores, 2, RoundingMode.HALF_UP));

        this.setPredictedYearOnYearRatio(predictedYearOnYearRatioValue + "%");
        this.setPredictedYearOnYearRatioValue(predictedYearOnYearRatioValue);
        this.setPredictedYearOnYearChange(predictedYearOnYearChange);
        this.setNewOrElasticYearOnYearRatio(newOrElasticYearOnYearRatioValue + "%");
        this.setNewOrElasticYearOnYearRatioValue(newOrElasticYearOnYearRatioValue);
        this.setNewOrElasticYearOnYearChange(newOrElasticYearOnYearChange);
        this.setReturnYearOnYearRatio(returnYearOnYearRatioValue + "%");
        this.setReturnYearOnYearRatioValue(returnYearOnYearRatioValue);
        this.setReturnYearOnYearChange(returnYearOnYeaChange);
        this.setPredictedCores(currentPredictionNetCores);
        this.setNewOrElasticCores(currentPredictionIncreaseCores);
        this.setReturnCores(currentPredictionReturnCores);

        this.setPredictedActualAchievementRatio(predictedActualAchievementRatio + "%");
        this.setPredictedActualAchievementRatioValue(predictedActualAchievementRatio);

        this.setNewOrElasticActualAchievementRatio(newOrElasticActualAchievementRatio + "%");
        this.setNewOrElasticActualAchievementRatioValue(newOrElasticActualAchievementRatio);

        this.setReturnActualAchievementRatio(returnActualAchievementRatio + "%");
        this.setReturnActualAchievementRatioValue(returnActualAchievementRatio);

    }
}
