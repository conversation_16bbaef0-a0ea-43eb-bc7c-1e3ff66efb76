package cloud.demand.app.modules.industry_cockpit.v4.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.process.IndustryCockpitQueryProcessFactory;
import cloud.demand.app.modules.industry_cockpit.process.enums.IndustryCockpitQueryProcessEnum;
import cloud.demand.app.modules.industry_cockpit.process.item.SupplyDeliveryIndexItem;
import cloud.demand.app.modules.industry_cockpit.v4.entity.SupplyDeliveryDfDO;
import cloud.demand.app.modules.industry_cockpit.v4.model.excel.ExportSupplyDeliveryVO;
import cloud.demand.app.modules.industry_cockpit.v4.model.excel.SupplyDeliveryModel;
import cloud.demand.app.modules.industry_cockpit.v4.model.req.SupplyDeliveryParamTypeReq;
import cloud.demand.app.modules.industry_cockpit.v4.model.req.SupplyDeliveryReq;
import cloud.demand.app.modules.industry_cockpit.v4.model.resp.SupplyDeliveryResp;
import cloud.demand.app.modules.industry_cockpit.v4.service.SupplyDeliveryService;
import cloud.demand.app.modules.soe.dto.item.ReportMapIndexItem;
import cloud.demand.app.modules.sop_util.process.QueryProcess;
import cloud.demand.app.modules.sop_util.process.QueryProcessInstance;
import cloud.demand.app.modules.sop_util.utils.CommonUtils;
import cloud.demand.app.web.model.common.StreamDownloadBean;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static cloud.demand.app.common.config.DBList.ckcldStdCrpDBHelper;

/**
 * <AUTHOR>
 * @since 2025/2/14 14:25
 */
@Service
@Slf4j
public class SupplyDeliveryServiceImpl implements SupplyDeliveryService {

    @Resource
    private IndustryCockpitQueryProcessFactory factory;

    @Override
    public SupplyDeliveryResp queryReport(SupplyDeliveryReq req) {
        SupplyDeliveryResp ret = new SupplyDeliveryResp();
        // step 1：流程查询工厂查询对应流程数据
        if(StringUtils.isNotEmpty(req.getStartStatTime())) {
            SupplyDeliveryReq reqCopy = JSON.clone(req);
            reqCopy.setStatTime(req.getStartStatTime());
            QueryProcess process = factory.getProcess(IndustryCockpitQueryProcessEnum.SUPPLY_DELIVERY.getName());
            ret.setStartData((List<ReportMapIndexItem>) process.process(ImmutableMap.of("req", reqCopy)));
        }
        if(StringUtils.isNotEmpty(req.getEndStatTime())) {
            SupplyDeliveryReq reqCopy = JSON.clone(req);
            reqCopy.setStatTime(req.getEndStatTime());
            QueryProcess process = factory.getProcess(IndustryCockpitQueryProcessEnum.SUPPLY_DELIVERY.getName());
            ret.setEndData((List<ReportMapIndexItem>) process.process(ImmutableMap.of("req", reqCopy)));
        }
        return ret;
    }

    @Override
    public ResponseEntity<InputStreamResource> excel(SupplyDeliveryReq req) {
        List<SupplyDeliveryIndexItem> allData = new ArrayList<>();
        if(StringUtils.isNotEmpty(req.getStartStatTime())) {
            SupplyDeliveryReq reqCopy = JSON.clone(req);
            reqCopy.setStatTime(req.getStartStatTime());
            QueryProcess process = factory.getProcess(IndustryCockpitQueryProcessEnum.SUPPLY_DELIVERY_EXCEL.getName());
            QueryProcessInstance instance = process.processWithInstance(ImmutableMap.of("req", reqCopy));
            allData.addAll(CommonUtils.toChildren(instance.getStepCacheData().values().stream().flatMap(List::stream).collect(Collectors.toList())));
        }
        if(StringUtils.isNotEmpty(req.getEndStatTime())) {
            SupplyDeliveryReq reqCopy = JSON.clone(req);
            reqCopy.setStatTime(req.getEndStatTime());
            QueryProcess process = factory.getProcess(IndustryCockpitQueryProcessEnum.SUPPLY_DELIVERY_EXCEL.getName());
            QueryProcessInstance instance = process.processWithInstance(ImmutableMap.of("req", reqCopy));
            allData.addAll(CommonUtils.toChildren(instance.getStepCacheData().values().stream().flatMap(List::stream).collect(Collectors.toList())));
        }

        List<SupplyDeliveryModel> models = SupplyDeliveryModel.build(allData);
        return toExcel(req.getDims(), models, req.getStartStatTime(), req.getEndStatTime());
    }


    @Override
    public List<String> queryParams(SupplyDeliveryParamTypeReq req) {
        if (StringUtils.isBlank(req.getParamType())) {
            return new ArrayList<>();
        }
        String column = ORMUtils.getColumnByFieldName(SupplyDeliveryDfDO.class, req.getParamType());
        if (column == null) {
            return new ArrayList<>();
        }
        String statTime = req.getStatTime();
        if (StringUtils.isBlank(statTime)) {
            statTime = ckcldStdCrpDBHelper.getRawOne(String.class, "select max(stat_time) from std_crp.dws_supply_delivery_df");
        }
        String sql = "select distinct ${paramType} from std_crp.dws_supply_delivery_df where stat_time = ? ";

        return ckcldStdCrpDBHelper.getRaw(String.class, sql.replace("${paramType}", column), statTime );

    }

    /** 导出 */
    @SneakyThrows
    private ResponseEntity<InputStreamResource> toExcel(List<String> dims,List<SupplyDeliveryModel> models,String startTime,String endStatTime) {
        ExportSupplyDeliveryVO exportData = ExportSupplyDeliveryVO.build(dims, models, startTime, endStatTime);
        ByteArrayInputStream in;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            EasyExcel.write(out)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .head(exportData.getHeadList()).sheet("供应进度")
                    .doWrite(exportData.getDataList());
            in = new ByteArrayInputStream(out.toByteArray());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ITException("导出excel模版失败");
        }

        String filename = "供应进度" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8) + ".xlsx";
        return new StreamDownloadBean(filename, in);
    }
}
