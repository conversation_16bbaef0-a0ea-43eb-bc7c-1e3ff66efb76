package cloud.demand.app.modules.industry_cockpit.v3.entity;

import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.industry_cockpit.service.InstanceGroupFiller;
import cloud.demand.app.modules.industry_cockpit.service.filler.CountryNameFiller;
import cloud.demand.app.modules.industry_cockpit.service.filler.CustomerInfoByAppIdFiller;
import cloud.demand.app.modules.industry_cockpit.service.filler.GpuTypeFiller;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.SyncDwsLogicalTagsWithholdVO;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.CommonCustomerShortNameFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.WarZoneByCustomerShortNameFiller;
import com.alibaba.fastjson.JSONObject;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.mvel2.MVEL;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Data
@ToString
@Table("dws_logical_tags_withhold_df")
public class DwsLogicalTagsWithholdDfDO implements CountryNameFiller, CustomerInfoByAppIdFiller, CommonCustomerShortNameFiller, WarZoneByCustomerShortNameFiller, GpuTypeFiller, InstanceGroupFiller {

    /**
     * 分区键，代表数据版本<br/>Column: [stat_time]
     */
    @Column(value = "stat_time")
    private String statTime;

    /**
     * 所属业务：CVM、CPU<br/>Column: [biz_type]
     */
    @Column(value = "biz_type")
    private String bizType;

    /**
     * 预扣分类：逻辑区、标签预扣<br/>Column: [withhold_type]
     */
    @Column(value = "withhold_type")
    private String withholdType;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept")
    private String industryDept;

    /**
     * CRP战区<br/>Column: [crp_war_zone]
     */
    @Column(value = "crp_war_zone")
    private String crpWarZone;

    /**
     * 战区<br/>Column: [war_zone]
     */
    @Column(value = "war_zone")
    private String warZone;

    /**
     * 通用客户简称<br/>Column: [un_customer_short_name]
     */
    @Column(value = "un_customer_short_name")
    private String unCustomerShortName;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /**
     * 客户名称<br/>Column: [customer_name]
     */
    @Column(value = "customer_name")
    private String customerName;

    /**
     * appId<br/>Column: [app_id]
     */
    @Column(value = "app_id")
    private Long appId;

    /**
     * uin<br/>Column: [uin]
     */
    @Column(value = "uin")
    private Long uin;

    /** 客户范围：1-内部,0-外部<br/>Column: [is_inner] */
    @Column(value = "is_inner")
    private Integer isInner;

    @Column(value = "customer_type")
    private Integer customerType;// 客户类型，枚举：0-个人 1-企业  2-政府 3-组织 99 未知 -1未知


    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 国家<br/>Column: [country_name]
     */
    @Column(value = "country_name")
    private String countryName;

    /**
     * 区域<br/>Column: [area_name]
     */
    @Column(value = "area_name")
    private String areaName;

    /**
     * 地域<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 可用区<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 可用区ID<br/>Column: [zone_id]
     */
    @Column(value = "zone_id")
    private String zoneId;

    /**
     * 母机IP<br/>Column: [host_ip]
     */
    @Column(value = "host_ip")
    private String hostIp;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @Column(value = "instance_type")
    private String instanceType;

    /**
     * 实例族<br/>Column: [instance_family]
     */
    @Column(value = "instance_family")
    private String instanceFamily;


    /**
     * GPU卡型<br/>Column: [gpu_card_type]
     */
    @Column(value = "gpu_card_type")
    private String gpuCardType;

    /**
     * 母机投放类型<br/>Column: [cvm_type]
     */
    @Column(value = "cvm_type")
    private String cvmType;

    /**
     * 母机标准机型<br/>Column: [cvm_standard_type]
     */
    @Column(value = "cvm_standard_type")
    private String cvmStandardType;

    /**
     * 逻辑区appmask<br/>Column: [app_mask]
     */
    @Column(value = "app_mask")
    private String appMask;

    /**
     * CMDB四级模块id<br/>Column: [bsi_id]
     */
    @Column(value = "bsi_id")
    private String bsiId;

    /**
     * 库存标签<br/>Column: [stack_label]
     */
    @Column(value = "stack_label")
    private String stackLabel;

    /**
     * 总核心数<br/>Column: [cpu_core_total]
     */
    @Column(value = "cpu_core_total")
    private BigDecimal cpuCoreTotal;

    /**
     * 好料核心数<br/>Column: [cpu_core_good]
     */
    @Column(value = "cpu_core_good")
    private BigDecimal cpuCoreGood;

    /**
     * 差料核心数<br/>Column: [cpu_core_bad]
     */
    @Column(value = "cpu_core_bad")
    private BigDecimal cpuCoreBad;

    /**
     * 呆料核心数<br/>Column: [cpu_core_idle]
     */
    @Column(value = "cpu_core_idle")
    private BigDecimal cpuCoreIdle;

    /**
     * 逻辑区核心数<br/>Column: [cpu_core_logical]
     */
    @Column(value = "cpu_core_logical")
    private BigDecimal cpuCoreLogical;

    /**
     * 标签核心数<br/>Column: [cpu_core_tag]
     */
    @Column(value = "cpu_core_tag")
    private BigDecimal cpuCoreTag;

    /**
     * 逻辑区信息<br/>Column: [logical_info]
     */
    @Column(value = "logical_info")
    private String logicalInfo;

    /**
     * 标签信息<br/>Column: [tag_info]
     */
    @Column(value = "tag_info")
    private String tagInfo;

    public static DwsLogicalTagsWithholdDfDO transform(SyncDwsLogicalTagsWithholdVO vo) {

        DwsLogicalTagsWithholdDfDO ret = new DwsLogicalTagsWithholdDfDO();
        BeanUtils.copyProperties(vo, ret);

        String logicalInfo = vo.getLogicalInfo();
        String tagInfo = vo.getTagInfo();
        if (StringUtils.contains(logicalInfo, "周转")) {
            ret.setWithholdType("逻辑区");
            //获取appId
            Map<String, Object> data = JSONObject.parseObject(logicalInfo, Map.class);
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                // 创建 MVEL 上下文并绑定变量
                Map<String, Object> context = new HashMap<>();
                context.put("data", data);
                context.put("key", entry.getKey()); // 动态键名变量
                boolean contains = MVEL.evalToBoolean("data[key].name contains '【周转】'", context);
                if (contains) {
                    // 创建 MVEL 上下文并绑定变量
                    Integer firstAppId = (Integer) MVEL.eval("data[key].appid[0]", context);
                    ret.setAppId(firstAppId.longValue());
                    ret.setCpuCoreLogical(vo.getCpuCoreTotal());
                    ret.setCpuCoreTag(BigDecimal.ZERO);
                    break;
                }
            }
        } else if (StringUtils.contains(tagInfo, "HOST_RESERVED")) {
            ret.setWithholdType("标签预扣");
            //获取appId
            Map<String, Object> data = JSONObject.parseObject(tagInfo, Map.class);
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                if (StringUtils.contains(entry.getKey(), "HOST_RESERVED")) {
                    // 创建 MVEL 上下文并绑定变量
                    Map<String, Object> context = new HashMap<>();
                    context.put("data", data);
                    context.put("key", entry.getKey()); // 动态键名变量
                    Integer firstAppId = (Integer) MVEL.eval("data[key][0]", context);
                    ret.setAppId(firstAppId.longValue());
                    ret.setCpuCoreLogical(BigDecimal.ZERO);
                    ret.setCpuCoreTag(vo.getCpuCoreTotal());
                    break;
                }

            }
        } else {
            ret.setWithholdType(StdUtils.EMPTY_STR);
        }
        return ret;

    }

    @Override
    public String provideRegion() {
        return null;
    }

    @Override
    public String provideRegionName() {
        return this.regionName;
    }

    @Override
    public void fillCountryName(String countryName) {
        this.countryName = countryName;
    }

    @Override
    public String provideAppId() {
        return this.appId.toString();
    }

    @Override
    public void fillUin(String uin) {
        this.uin = Long.valueOf(uin);
    }

    @Override
    public void fillCustomerName(String customerName) {
        this.customerName = customerName;
    }

    @Override
    public void fillCustomerShortName(String customerShortName) {
        this.customerShortName = customerShortName;
    }

    @Override
    public void fillPanShiWarZone(String panShiWarZone) {
        this.warZone = panShiWarZone;
    }

    @Override
    public void fillIndustryDept(String industryDept) {
        this.industryDept = industryDept;
    }

    @Override
    public void fillIsInner(Integer isInner) {
        this.isInner = isInner;
    }

    @Override
    public void fillUinType(Integer uinType) {

    }

    @Override
    public void fillCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    @Override
    public String provideCustomerShortName() {
        return this.customerShortName;
    }

    @Override
    public void fillWarZone(String warZone) {
        this.crpWarZone = warZone;
    }

    @Override
    public String provideIndustryDept() {
        return this.industryDept;
    }

    @Override
    public void fillCommonCustomerShortName(String commonCustomerShortName) {
        this.unCustomerShortName = commonCustomerShortName;
    }

    @Override
    public String provideInstanceType() {
        return this.instanceType;
    }

    @Override
    public void fillInstanceGroup(String instanceGroup) {
        this.instanceFamily = instanceGroup;
    }

    @Override
    public void fillGpuType(String gpuType) {
        this.gpuCardType = gpuType;
    }
}