package cloud.demand.app.modules.industry_cockpit.service.impl;

import com.pugwoo.dbhelper.sql.WhereSQL;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/11
 */
@Service
public class AuthServiceIndustryDeptImpl extends AbstractAuthServiceImpl {
    @Override
    void buildAllIndustryWhere(WhereSQL whereSQL) {
        whereSQL.and("industry_dept in (?) ", "总计");
    }

    @Override
    void buildIndustryWhere(WhereSQL whereSQL, List<String> industry) {
        whereSQL.and("industry_dept in (?)", industry);
    }
}
