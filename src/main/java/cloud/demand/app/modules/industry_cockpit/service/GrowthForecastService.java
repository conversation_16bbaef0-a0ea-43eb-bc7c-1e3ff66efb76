package cloud.demand.app.modules.industry_cockpit.service;

import cloud.demand.app.modules.industry_cockpit.model.IndustryCockpoitRequest;

import java.util.List;

/**
 * 增量规模预测 服务定义
 *
 * <AUTHOR>
 * @since 2024/4/25
 */
public interface GrowthForecastService<T> {
    /**
     * 获取增量数据
     *
     * @param request   查询条件
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 增量数据
     */
    List<T> getGrowthData(IndustryCockpoitRequest request, String startDate, String endDate);
}
