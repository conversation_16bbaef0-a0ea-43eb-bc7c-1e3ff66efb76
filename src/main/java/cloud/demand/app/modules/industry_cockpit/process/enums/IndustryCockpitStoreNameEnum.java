package cloud.demand.app.modules.industry_cockpit.process.enums;

import cloud.demand.app.modules.sop_util.enums.IStoreNameEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 仓库枚举
 */
@Getter
@AllArgsConstructor
public enum IndustryCockpitStoreNameEnum implements IStoreNameEnum  {
    // =============== 初始化 ================
    supply_delivery_init("supply_delivery_init", "参数初始化"),
    // =============== 订单数据 ================
    order_demand("order_demand", "订单(共识需求)"),

    // =============== 供应数据 ================
    supply_match_satisfy("supply_match_satisfy", "供应@大盘满足"),

    supply_match_buy("supply_match_buy", "供应@采购满足"),

    supply_match_move("supply_match_move", "供应@搬迁满足"),

    ;
    private final String name;
    private final String desc;
}
