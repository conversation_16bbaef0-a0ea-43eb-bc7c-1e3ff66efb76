package cloud.demand.app.modules.industry_cockpit.v3.model.resp;

import cloud.demand.app.modules.industry_cockpit.v3.model.vo.KeyValueItemVO;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/14 17:41
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OverviewCircularGraphResp {
    private List<KeyValueItemVO> itemList = ListUtils.newArrayList();
    private int total;

}
