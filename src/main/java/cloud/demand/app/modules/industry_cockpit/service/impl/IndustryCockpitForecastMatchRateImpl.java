package cloud.demand.app.modules.industry_cockpit.service.impl;

import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.industry_cockpit.constant.IndustryCockpitConstant;
import cloud.demand.app.modules.industry_cockpit.entity.TargetVO;
import cloud.demand.app.modules.industry_cockpit.model.CurrentMonthForecastMatchRateResp;
import cloud.demand.app.modules.industry_cockpit.model.ForecastMatchRateDetailResp;
import cloud.demand.app.modules.industry_cockpit.model.ForecastMatchRateItem;
import cloud.demand.app.modules.industry_cockpit.model.IndustryCockpitForecastMatchRateRequest;
import cloud.demand.app.modules.industry_cockpit.model.IndustryDemandManagementReportResp;
import cloud.demand.app.modules.industry_cockpit.model.TargetAccuracyRateResp;
import cloud.demand.app.modules.industry_cockpit.service.IndustryCockpitForecastMatchRateService;
import cloud.demand.app.modules.industry_cockpit.service.TargetService;
import cloud.demand.app.modules.mrpv2.DynamicProperties;
import cloud.demand.app.modules.mrpv2.domain.ForecastMatchRateReq;
import cloud.demand.app.modules.mrpv2.domain.ForecastMatchRateResp;
import cloud.demand.app.modules.mrpv2.service.CloudResourceOperatorService;
import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.mrpv2.utils.PageQueryUtils;
import cloud.demand.app.modules.soe.service.SoeCommonService;
import cloud.demand.app.web.model.common.StreamDownloadBean;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/25 12:23
 */
@Service
@Slf4j
public class IndustryCockpitForecastMatchRateImpl implements IndustryCockpitForecastMatchRateService {

    @Resource
    private CloudResourceOperatorService cloudResourceOperatorService;

    @Resource
    private SoeCommonService commonService;

    @Resource
    private MrpV2DictService mrpV2DictService;

    @Resource
    private TargetService targetService;


    @Override
    public IndustryDemandManagementReportResp queryIndustryDemandManagementReport(IndustryCockpitForecastMatchRateRequest req) {
        IndustryCockpitForecastMatchRateRequest cloneReq = JSON.clone(req);
        if (StringUtils.equals(req.getAccuracyCaliber(), IndustryCockpitConstant.CORE_DAYS)) {
            cloneReq.setAccuracyCaliber(IndustryCockpitConstant.MONTHLY_AVG);
        }
        List<ForecastMatchRateResp.Item> itemList = queryForecastMatchRate(cloneReq);

        List<String> bigCustomerShortName = mrpV2DictService.getBigCustomerShortName();

        return IndustryDemandManagementReportResp.builder(itemList,bigCustomerShortName, req);
    }

    @Override
    public CurrentMonthForecastMatchRateResp queryCurrentMonthForecastMatchRate(IndustryCockpitForecastMatchRateRequest req) {


        List<ForecastMatchRateItem> list = getForecastMatchRateItemList(req);


        return CurrentMonthForecastMatchRateResp.builder(list, req);
    }

    @Override
    public ForecastMatchRateDetailResp queryForecastMatchRateDetail(IndustryCockpitForecastMatchRateRequest req) {
        if(allowABTest()){
            IndustryCockpitForecastMatchRateRequest.Choose choose = req.getChoose();
            choose.setProjectType(IndustryCockpitConstant.MAIN_PROJECT);
        }
        List<ForecastMatchRateItem> list = getForecastMatchRateItemList(req);

        return ForecastMatchRateDetailResp.builder(list, req);
    }

    @Override
    public ResponseEntity<InputStreamResource> exportExcel(IndustryCockpitForecastMatchRateRequest req) {
        if(allowABTest()) {
            IndustryCockpitForecastMatchRateRequest.Choose choose = req.getChoose();
            choose.setProjectType(IndustryCockpitConstant.MAIN_PROJECT);
        }
        List<ForecastMatchRateItem> list = getForecastMatchRateItemList(req);
        ByteArrayInputStream in;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            ExcelWriter writer = EasyExcel.write(out).excelType(ExcelTypeEnum.XLSX).build();

            WriteSheet tempSheet = EasyExcel
                    .writerSheet("驾驶舱-预测准确率明细")
                    .head(ForecastMatchRateItem.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            writer.write(list, tempSheet);

            writer.finish();
            in = new ByteArrayInputStream(out.toByteArray());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ITException("导出excel模版失败");
        }

        String filename = "驾驶舱-预测准确率明细 excel-" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8) + ".xlsx";
        return new StreamDownloadBean(filename, in);
    }

    @Override
    public TargetAccuracyRateResp getTargetAccuracyRateData(IndustryCockpitForecastMatchRateRequest req) {
        List<TargetVO> list = targetService.getTargetVOList(req.getStartYearMonth(), req.getEndYearMonth(), req.getDemandType());
        return new TargetAccuracyRateResp(list, list.size());
    }

    private List<ForecastMatchRateItem> getForecastMatchRateItemList(IndustryCockpitForecastMatchRateRequest req) {
        //月切
        IndustryCockpitForecastMatchRateRequest monthlySliceReq = JSON.clone(req);
        monthlySliceReq.setAccuracyCaliber(IndustryCockpitConstant.MONTHLY_SLICE);
        //月均
        IndustryCockpitForecastMatchRateRequest monthlyReq = JSON.clone(req);
        monthlyReq.setAccuracyCaliber(IndustryCockpitConstant.MONTHLY_AVG);

        List<List<ForecastMatchRateResp.Item>> allList = PageQueryUtils.waitAll(30,
                () -> queryForecastMatchRate(monthlySliceReq),
                () -> queryForecastMatchRate(monthlyReq));

        List<String> bigCustomerShortName = mrpV2DictService.getBigCustomerShortName();

        return ForecastMatchRateItem.transform(allList.get(0), allList.get(1), bigCustomerShortName);
    }

    private List<ForecastMatchRateResp.Item> queryForecastMatchRate(IndustryCockpitForecastMatchRateRequest req) {
        if(ListUtils.isEmpty(req.getDims()) && ListUtils.isEmpty(req.getNextDims())){
            req.setNextDims(ListUtils.newArrayList(IndustryCockpitConstant.PROJECT_TYPE));
        }else {
            if(!ListUtils.contains(req.getDims(), (o) -> StringUtils.equals(o, IndustryCockpitConstant.PROJECT_TYPE)) &&
                    !ListUtils.contains(req.getNextDims(), (o) -> StringUtils.equals(o, IndustryCockpitConstant.PROJECT_TYPE))){
                req.setNextDims(ListUtils.newArrayList(IndustryCockpitConstant.PROJECT_TYPE));
            }
        }
        // 因行业数据底表不支持国家，所以需要转换成区域
        if (ListUtils.isNotEmpty(req.getCountryName())) {
            List<String> regionNameList = commonService.getRegion2Country().stream()
                    .filter(o -> req.getCountryName().contains(o.getCountryName()))
                    .map(o -> o.getRegionName()).collect(Collectors.toList());
            req.setRegionName(ListUtils.intersection(req.getRegionName(), regionNameList));
        }
        ForecastMatchRateReq forecastMatchRateReq;
        if(allowABTest()){
            forecastMatchRateReq = req.createForecastMatchRateReqByMeNew();
        }else {
            forecastMatchRateReq = req.createForecastMatchRateReqByMe();
        }

        List<ForecastMatchRateResp.Item> ret = cloudResourceOperatorService.queryForecastMatchRate(forecastMatchRateReq);

        ret = ret.stream()
                .filter(o -> !StringUtils.equals(o.getIndustryDept(),"(空值)") && !StringUtils. equals(o.getIndustryDept(),"中长尾"))
                .filter(o -> !StringUtils.equals(o.getProjectType(),"不参与"))
                .collect(Collectors.toList());

        return ret;
    }

    /** 是否运行 ab 测试，即开发能看到新版本，其他人看不到 */
    private boolean allowABTest() {
        if (EnvUtils.isLocalEnv()){
            return true;
        }
        List<String> abTestUser = DynamicProperties.getABTestUser();
        if (ListUtils.isEmpty(abTestUser)){
            return false;
        }
        if (abTestUser.contains("*")){
            return true;
        }
        String userName = LoginUtils.getUserName();
        for (String user : abTestUser) {
            if (Objects.equals(userName, user)) {
                return true;
            }
        }
        return false;
    }

}
