package cloud.demand.app.modules.industry_cockpit.v3.model.req;

import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import com.pugwoo.wooutils.json.JSON;
import lombok.Data;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/14 11:46
 */
@Data
public class LineChartReq extends IndustryCockpitV3HistoricalReq {

    @SopReportWhere(sql = "stat_time >= ?")
    private LocalDate startStatTime = LocalDate.now().plusYears(-1).with(TemporalAdjusters.firstDayOfYear());

    @SopReportWhere(sql = "stat_time < ?")
    private LocalDate endStatTime = LocalDate.now();

    public static List<LineChartReq> splitByStatTime(LineChartReq req) {
        List<LineChartReq> reqList = new ArrayList<>();
        LocalDate startStatTime = req.getStartStatTime();
        LocalDate endStatTime = req.getEndStatTime();
        while (!startStatTime.isAfter(endStatTime)) {
            LocalDate tempDate = startStatTime.plusMonths(12);
            LineChartReq tempReq = JSON.clone(req);
            if (!tempDate.isAfter(endStatTime)) {
                tempReq.setStartStatTime(startStatTime);
                tempReq.setEndStatTime(tempDate);
            } else {
                tempReq.setStartStatTime(startStatTime);
                tempReq.setEndStatTime(endStatTime);
            }
            reqList.add(tempReq);
            startStatTime = tempDate;
        }
        return reqList;
    }
}
