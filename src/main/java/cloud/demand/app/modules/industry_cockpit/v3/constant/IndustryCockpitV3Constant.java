package cloud.demand.app.modules.industry_cockpit.v3.constant;

/**
 * <AUTHOR>
 * @since 2024/8/13 16:50
 */
public class IndustryCockpitV3Constant {

    public static final String GPU = "GPU";

    public static final String CPU = "CPU";

    public static final String BARE_METAL_NAME = "裸金属";

    public static final String CVM = "CVM";
    public static final String CVM_CBS = "CVM&CBS";

    public static final String GPU_BARE_METAL_CVM = "GPU(裸金属&CVM)";

    public static final String LOWER_CVM = "cvm";
    public static final String BARE_METAL = "baremetal";

    public static final String OTHER = "其他";

    public static final String BILL_NUM = "计费用量";
    public static final String SERVICE_NUM = "服务用量";


    public static final String MONTHLY_AVG_CHANG = "月均变化量";

    public static final String MONTHLY_SLICE_CHANG = "月切变化量";

    public static final String MONTHLY_SLICE_SCALE = "月末切片规模";

    public static final String MONTHLY_AVG_SCALE = "月均规模";

    public static final String SPECIFICATIONS = "specifications";

    public static final String INSTANCE_MODEL = "instanceModel";

    // “中长期预测录入过的客户简称”以及“13周预测录入过的客户简称”以及“名单客户的客户简称”单独展示，其他客户合并客户简称叫“其他客户”
    public static final String OTHER_CUSTOMER = "其他客户";
}
