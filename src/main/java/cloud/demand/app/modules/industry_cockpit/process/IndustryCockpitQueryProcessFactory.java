package cloud.demand.app.modules.industry_cockpit.process;

import cloud.demand.app.modules.industry_cockpit.process.enums.IndustryCockpitQueryProcessEnum;
import cloud.demand.app.modules.industry_cockpit.process.enums.IndustryCockpitStoreNameEnum;
import cloud.demand.app.modules.industry_cockpit.process.enums.SupplyDeliveryIndexEnum;
import cloud.demand.app.modules.soe.process.utils.SoeStepUtils;
import cloud.demand.app.modules.sop_util.process.QueryProcess;
import cloud.demand.app.modules.sop_util.process.QueryProcessBuilder;
import cloud.demand.app.modules.sop_util.process.StoreFactory;
import cloud.demand.app.modules.sop_util.process.utils.StepUtils;
import cloud.demand.app.modules.sop_util_v2.process.utils.SopUtilV2StepUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/2/13 19:53
 */
@Component
@Slf4j
public class IndustryCockpitQueryProcessFactory {
    /**
     * 进程集合
     */
    private Map<String, QueryProcess> processMap;

    private final SopUtilV2StepUtils sopUtilV2StepUtils = new SopUtilV2StepUtils();

    /**
     * 仓库
     */
    @Resource
    private StoreFactory storeFactory;

    /**
     * 工具集合
     */
    private final SoeStepUtils utils = new SoeStepUtils();

    @PostConstruct
    public void init() {
        processMap = new HashMap<>();
        register();
    }

    /**
     * 通过注册的进程名获取查询进程
     */
    public QueryProcess getProcess(String name) {
        return processMap.get(name);
    }

    private void register() {
        processMap.put(IndustryCockpitQueryProcessEnum.SUPPLY_DELIVERY.getName(), registerSupplyDelivery());
        processMap.put(IndustryCockpitQueryProcessEnum.SUPPLY_DELIVERY_EXCEL.getName(), registerSupplyDeliveryExcel());
    }

    private QueryProcess registerSupplyDelivery() {
        QueryProcessBuilder builder = initBuilder();
        // 添加步骤
        sopUtilV2StepUtils.buildStep(builder, SupplyDeliveryIndexEnum.values());
        return builder.build();
    }

    private QueryProcess registerSupplyDeliveryExcel() {
        QueryProcessBuilder builder = initBuilder();
        // 添加步骤
        builder.addStep(utils.buildSimpleStep(SupplyDeliveryIndexEnum.order_demand,null));
        builder.addStep(utils.buildSimpleStep(SupplyDeliveryIndexEnum.supply_match_satisfy,null));
        builder.addStep(utils.buildSimpleStep(SupplyDeliveryIndexEnum.supply_match_buy,null));
        builder.addStep(utils.buildSimpleStep(SupplyDeliveryIndexEnum.supply_match_move,null));
        return builder.build();
    }

    /**
     * 初始化
     */
    private QueryProcessBuilder initBuilder() {
        QueryProcessBuilder builder = new QueryProcessBuilder(storeFactory);

        // 填充初始化参数
        builder.addStep(StepUtils.buildInit("STEP-INIT", IndustryCockpitStoreNameEnum.supply_delivery_init.getName()));

        return builder;
    }
}
