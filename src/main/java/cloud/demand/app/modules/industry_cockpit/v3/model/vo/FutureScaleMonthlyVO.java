package cloud.demand.app.modules.industry_cockpit.v3.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/8/15 11:19
 */
@Data
public class FutureScaleMonthlyVO implements ICommonGroupKey{

    @ExcelProperty(value = "行业部门",index = 0)
    @Column("any_industry_dept")
    private String industryDept;//行业部门

    @ExcelProperty(value = "战区",index = 1)
    @Column("any_war_zone")
    private String warZone;//战区

    @ExcelProperty(value = "客户简称",index = 2)
    @Column("any_customer_short_name")
    private String customerShortName;//客户简称

    @ExcelProperty(value = "实例类型",index = 3)
    @Column("any_instance_type")
    private String instanceType;//实例类型

    @ExcelProperty(value = "实例族",index = 4)
    @Column("any_instance_family")
    private String instanceFamily;//实例族

    @ExcelProperty(value = "GPU卡类型",index = 5)
    @Column("any_gpu_card_type")
    private String gpuCardType;//GPU卡类型

    @ExcelProperty(value = "境内外",index = 6)
    @Column("any_customhouse_title")
    private String customhouseTitle;//境内外

    @ExcelProperty(value = "地域",index = 7)
    @Column("any_region_name")
    private String regionName;//地域

    @ExcelProperty(value = "可用区",index = 8)
    @Column("any_zone_name")
    private String zoneName;//可用区

    @ExcelProperty(value = "年月",index = 9)
    @Column("year_month")
    private String yearMonth;//年月

    @ExcelProperty(value = "核数",index = 10)
    @Column("all_amount")
    private BigDecimal amount;//

    @Override
    public String getDemandType() {
        return null;
    }
}
