package cloud.demand.app.modules.industry_cockpit.v3.model.req;

import cloud.demand.app.modules.industry_cockpit.auth.AuthCheckParam;
import cloud.demand.app.modules.industry_cockpit.v3.enums.ProductEnum;
import cloud.demand.app.modules.industry_cockpit.v3.parse.ProductForMonthlySliceParse;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import com.google.common.base.Objects;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/15 14:23
 */
@Data
public class HistoricalScaleMonthlySliceReq extends HistoricalScaleMonthlyReq {

    @NotNull(message = "产品不能为空")
    @SopReportWhere(parsers = {ProductForMonthlySliceParse.class})
    @AuthCheckParam
    private String product;

    /** 智慧行业一部忽略 客户类型 = 个人 */
    @SopReportWhere(useSqlWhenNullOrFalse = false,sql = "((origin_industry_dept = '智慧行业一部' and customer_type != 1) or origin_industry_dept != '智慧行业一部')")
    private Boolean ignoreIndustryDeptOnePersonal;

    public static HistoricalScaleMonthlySliceReq transform(IndustryCockpitV3FutureReq req,boolean export) {
        HistoricalScaleMonthlySliceReq retReq = new HistoricalScaleMonthlySliceReq();
        retReq.setProduct(req.getProduct());
        retReq.setUnit(req.getUnit());
        retReq.setQueryRange(req.getQueryRange());
        retReq.setCustomerRange(req.getCustomerRange());
        retReq.setCustomerType(req.getCustomerType());
        retReq.setAppRole(req.getAppRole());
        retReq.setDisDims(req.getDisDims());

        retReq.setIndustryDept(req.getIndustryDept());
        retReq.setWarZone(req.getWarZone());
        retReq.setCustomerShortName(req.getCustomerShortName());
        retReq.setCombinedCustomerShortName(req.getCombinedCustomerShortName()); // 合并客户名称
        retReq.setIgnoreIndustryDeptOnePersonal(req.getIgnoreIndustryDeptOnePersonal()); // 智慧行业一部忽略 客户类型 = 个人
        retReq.setUin(req.getUin());
        retReq.setNotUin(req.getNotUin());
        retReq.setNotAppId(req.getNotAppId());
        if (Objects.equal(req.getProduct(), ProductEnum.PAAS.getName()) &&
        Objects.equal(req.getPaasProduct(), Ppl13weekProductTypeEnum.EKS.getName())){ // PAAS 产品 EKS 剔除 uin：3034334170、2252646423
            // EKS 剔除 uin：3034334170、2252646423
            List<String> uin = ListUtils.newList("3034334170","2252646423");
            if(ListUtils.isNotEmpty(req.getNotUin())){
                req.getNotUin().addAll(uin);
            }else {
                req.setNotUin(uin);
            }
        }
        retReq.setAppId(req.getAppId());

        retReq.setCustomhouseTitle(req.getCustomhouseTitle());
        retReq.setRegionName(req.getRegionName());
        retReq.setZoneName(req.getZoneName());
        retReq.setInstanceType(req.getInstanceType());
        retReq.setInstanceFamily(req.getInstanceFamily());
        retReq.setGpuCardType(req.getGpuCardType());

        retReq.setStatTime(req.getStatTime());

        retReq.setDims(req.getDims());
        retReq.setDemandType(req.getDemandType());
        retReq.setExport(export);

        return retReq;
    }

}
