package cloud.demand.app.modules.industry_cockpit.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.constant.IndustryCockpitConstant;
import cloud.demand.app.modules.industry_cockpit.entity.RankVO;
import cloud.demand.app.modules.industry_cockpit.model.IndustryCockpoitRequest;
import cloud.demand.app.modules.industry_cockpit.model.PlanAccuracyRankingListTableResponse;
import cloud.demand.app.modules.industry_cockpit.service.AuthService;
import cloud.demand.app.modules.industry_cockpit.service.RankListingService;
import cloud.demand.app.modules.industry_cockpit.service.WarZoneCustomerService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 预测变化量数据
 *
 * <AUTHOR>
 * @since 2024/4/26
 */
@Service
public class RankListingServicePredictionChangeImpl implements RankListingService {

    @Resource
    private DBHelper ckcldDBHelper;

    @Autowired
    @Qualifier("authServiceCommonImpl")
    private AuthService commonAuthServiceImpl;
    @Autowired
    @Qualifier("authServiceCvmCpuProductWithoutWarZoneImpl")
    private AuthService cvmCpuProductWithoutWarZoneAuthServiceImpl;
    @Autowired
    @Qualifier("authServiceCvmCbsProductImpl")
    private AuthService cvmCbsProductAuthServiceImpl;
    @Autowired
    private WarZoneCustomerService warZoneCustomerService;

    @Override
    public List<PlanAccuracyRankingListTableResponse.Item> getRankListingData(IndustryCockpoitRequest request, String demandType, String industryType) {

        Boolean isCustomerModule = IndustryCockpitConstant.CUSTOMER.equals(industryType);

//        WhereSQL monthLastWhereSql = commonAuthServiceImpl.getAuthWhereSql();
        WhereSQL monthLastWhereSql = new WhereSQL(); // 不做权限控制，这里需要查全部行业，并在上层过滤
        if (null == monthLastWhereSql) {
            return Collections.emptyList();
        }
        // 权限(如果是全行业，这里只对行业部门做鉴权，在上层控制，存空集合即可)
        if (ListUtils.isNotEmpty(request.getAuthedProduct())) {
            monthLastWhereSql.and("product in (?) ", request.getAuthedProduct());
        }
        if (ListUtils.isNotEmpty(request.getAuthedCustomerShortName())) {
            monthLastWhereSql.and("un_customer_short_name in (?) ", request.getAuthedCustomerShortName());
        }
        if (ListUtils.isNotEmpty(request.getAuthedWarZoneName())) {
            monthLastWhereSql.and("crp_war_zone in (?) ", request.getAuthedWarZoneName());
        }
        if (ListUtils.isNotEmpty(request.getAuthedIndustryDept())) {
            monthLastWhereSql.and("industry_dept in (?) ", request.getAuthedIndustryDept());
        }

        // 产品
        if ("CVM".equals(request.getProduct())) {
            monthLastWhereSql.and("product in (?) ", "CVM");
        }
        // 年月
        String dataMonth = request.getDataMonth();
        LocalDate currentLocalDate = LocalDate.parse(dataMonth + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate nextLocalDate = currentLocalDate.plusMonths(1);

        LocalDate now = LocalDate.now();
        if (now.format(DateTimeFormatter.ofPattern("yyyyMM")).equals(currentLocalDate.format(DateTimeFormatter.ofPattern("yyyyMM")))) {
            // 如果是看最新的月份 那么就是T-1日
            monthLastWhereSql.and(" stat_time in (?) ", now.minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        } else {
            monthLastWhereSql.and(" stat_time in (?) ", nextLocalDate.minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }

        // 行业
        if (!CollectionUtils.isEmpty(request.getIndustryDept())) {
            monthLastWhereSql.and("industry_dept in (?) ", request.getIndustryDept());
        }
        monthLastWhereSql.and("industry_dept not in (?) ", Arrays.asList("个人", "内部业务部","(空值)"));
        monthLastWhereSql.and(" project_type in (?) ", ListUtils.newArrayList("重点项目"));
        // 客户
        if (!CollectionUtils.isEmpty(request.getCustomerShortNames())) {
            monthLastWhereSql.and("un_customer_short_name in (?) ", request.getCustomerShortNames());
        }
        // 境内外
        if (!CollectionUtils.isEmpty(request.getCustomhouseTitle())) {
            monthLastWhereSql.and("customhouse_title in (?) ", request.getCustomhouseTitle());
        }
        // 实例类型

        if (!CollectionUtils.isEmpty(request.getInstanceType())) {
            if(StringUtils.equals(request.getMergeInstanceType(),"是")) {
                monthLastWhereSql.and("un_instance_type in (?) ", request.getInstanceType());
            }else {
                monthLastWhereSql.and("instance_type in (?) ", request.getInstanceType());
            }
        }

        // 地域
        if (!CollectionUtils.isEmpty(request.getRegionName())) {
            monthLastWhereSql.and("region_name in (?) ", request.getRegionName());
        }
        // 可用区
        if (!CollectionUtils.isEmpty(request.getZoneName())) {
            monthLastWhereSql.and("zone_name in (?) ", request.getZoneName());
        }
        // 战区 月均表没有战区
        if (!CollectionUtils.isEmpty(request.getWarZone())) {
            monthLastWhereSql.and("crp_war_zone in (?) ", request.getWarZone());
        }
        // 客户范围
        if (Objects.nonNull(request.getCustomerRange())) {
            monthLastWhereSql.and(" is_inner = ?  ", request.getCustomerRange());
        }

        String sql = ORMUtils.getSql("/sql/industry_cockpit/11_rank.sql");
        sql = sql.replace("${month_last_where}", monthLastWhereSql.getSQL().replaceAll("WHERE", " and "));

        sql = sql.replaceAll("\\$\\{accuracy_caliber}", request.getAccuracyCaliber());
        sql = sql.replaceAll("\\$\\{demand_type}", demandType);
        sql = sql.replace("${queryRange}", request.getQueryRange());
        if (isCustomerModule) {
            sql = sql.replaceAll("\\$\\{month_customer_short_name_field}", "if(project_type =  '重点项目',un_customer_short_name,'uin:0') as customer_short_name, ");
            sql = sql.replaceAll("\\$\\{customer_short_name_result_field}", "customer_short_name, ");
            sql = sql.replaceAll("\\$\\{customer_short_name_group_by}", " ,customer_short_name");
            sql = sql.replaceAll("\\$\\{month_customer_short_name_group_by}", " ,project_type,un_customer_short_name");
        } else {
            sql = sql.replaceAll("\\$\\{month_customer_short_name_field}", " ");
            sql = sql.replaceAll("\\$\\{customer_short_name_result_field}", " '' as customer_short_name, ");
            sql = sql.replaceAll("\\$\\{customer_short_name_group_by}", " ");
            sql = sql.replaceAll("\\$\\{month_customer_short_name_group_by}", " ");
        }

        if (isCustomerModule) {
            sql = sql.replaceAll("\\$\\{war_zone_name_field}", "crp_war_zone as war_zone_name, "); // 行业驾驶舱的战区就是月切的crp战区
            sql = sql.replaceAll("\\$\\{war_zone_name_result_field}", "war_zone_name, ");
            sql = sql.replaceAll("\\$\\{war_zone_name_group_by}", ",war_zone_name ");
        } else {
            sql = sql.replaceAll("\\$\\{war_zone_name_field}", " ");
            sql = sql.replaceAll("\\$\\{war_zone_name_result_field}", "'' as war_zone_name, ");
            sql = sql.replaceAll("\\$\\{war_zone_name_group_by}", " ");
        }

        if (request.isCustomerDetail()) {
            sql = sql.replaceAll("\\$\\{region_name_field}", "region_name as region_name , ");
            sql = sql.replaceAll("\\$\\{region_name_result_field}", "region_name, ");
            sql = sql.replaceAll("\\$\\{region_name_group_by}", ",region_name ");

            sql = sql.replaceAll("\\$\\{instance_type_field}", "instance_type as instance_type, ");
            sql = sql.replaceAll("\\$\\{instance_type_result_field}", "instance_type, ");
            sql = sql.replaceAll("\\$\\{instance_type_group_by}", ",instance_type ");
        } else {
            sql = sql.replaceAll("\\$\\{region_name_field}", " ");
            sql = sql.replaceAll("\\$\\{region_name_result_field}", " '' as region_name, ");
            sql = sql.replaceAll("\\$\\{region_name_group_by}", " ");

            sql = sql.replaceAll("\\$\\{instance_type_field}", " ");
            sql = sql.replaceAll("\\$\\{instance_type_result_field}", "'' as instance_type, ");
            sql = sql.replaceAll("\\$\\{instance_type_group_by}", " ");
        }

        Object[] monthLastWhereParam = monthLastWhereSql.getParams();

        Object[] params = new Object[monthLastWhereParam.length];

        System.arraycopy(monthLastWhereParam, 0, params, 0, monthLastWhereParam.length);

            List<RankVO> rankVoData = ckcldDBHelper.getRaw(RankVO.class, sql, params);

        List<PlanAccuracyRankingListTableResponse.Item> rankVoResultData = rankVoData.stream().map(item -> {
            PlanAccuracyRankingListTableResponse.Item dataItem = new PlanAccuracyRankingListTableResponse.Item();
            dataItem.setIndustryDept(item.getIndustryDept());

            if (isCustomerModule) {
                dataItem.setCustomerShortName(item.getCustomerShortName());
                dataItem.setWarZone(item.getWarZoneName());
            }

            dataItem.setPredictionAccuracy(BigDecimal.ZERO);
            dataItem.setPredictedQuantity(BigDecimal.ZERO);
            dataItem.setBillVariationMonthlyAvg(BigDecimal.ZERO);
            dataItem.setBillVariationMonthlyAvgSlice(item.getAllBillChangeMonthLast());
            if(request.isCustomerDetail()){
                dataItem.setRegionName(item.getRegionName());
                dataItem.setInstanceType(item.getInstanceType());
            }
            return dataItem;
        }).collect(Collectors.toList());

        return rankVoResultData;
    }
}
