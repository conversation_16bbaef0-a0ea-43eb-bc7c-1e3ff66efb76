package cloud.demand.app.modules.industry_cockpit.v3.service.impl;

import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.IInstanceFamilyDimReq;
import cloud.demand.app.modules.industry_cockpit.v3.service.FieldFillService;
import cloud.demand.app.modules.industry_cockpit.v3.service.HistoricalScaleMonthlyService;
import java.util.List;
import javax.annotation.Resource;

public abstract class AbstractHistoricalScaleMonthlyService implements HistoricalScaleMonthlyService {
    @Resource
    protected FieldFillService fieldFillService;
    @Resource
    protected DictService dictService;

    /** 填充实例族 */
    public String fillInstanceFamily(IInstanceFamilyDimReq req, String sql, List<String> fieldNames){
        return fieldFillService.fillInstanceFamilyDim(req, sql, fieldNames);
    }
}
