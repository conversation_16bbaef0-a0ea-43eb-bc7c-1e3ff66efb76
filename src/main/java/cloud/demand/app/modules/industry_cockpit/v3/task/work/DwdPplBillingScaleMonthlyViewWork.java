package cloud.demand.app.modules.industry_cockpit.v3.task.work;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.industry_cockpit.v3.entity.DwdPplBillingScaleMonthlyViewDO;
import cloud.demand.app.modules.industry_cockpit.v3.entity.DwdTxyAppidInfoCfDO;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.DwdTxyAppidInfoCfReq;
import cloud.demand.app.modules.industry_cockpit.v3.service.IndustryCockpitV3DictService;
import cloud.demand.app.modules.industry_report.entity.CkPplBillingScaleMonthlyDO;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.SimpleTaskEnum;
import cloud.demand.app.modules.mrpv2.task.process.SimpleCommonTaskProcess;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.work.AbstractSopWork;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/26 11:11
 */
@Service
@Slf4j(topic = "行业驾驶舱-V3-月均计费规模：")
public class DwdPplBillingScaleMonthlyViewWork extends AbstractSopWork<SimpleCommonTask> {

    @Resource
    private SimpleCommonTaskProcess process;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    private IndustryCockpitV3DictService industryCockpitV3DictService;

    @Resource
    private DictService dictService;


    @Override
    public ISopProcess<SimpleCommonTask> getTask() {
        return process;
    }

    @Override
    public ITaskEnum getEnum() {
        return SimpleTaskEnum.PPL_BILLING_SCALE_MONTHLY_VIEW;
    }

    @TaskLog(taskName = "DwdPplBillingScaleMonthlyViewWork")
    @Scheduled(fixedRate = 30 * 1000)
    @Override
    public void work() {
        super.work();
    }


    @Override
    public void doWork(SimpleCommonTask simpleCommonTask) {
        String version = simpleCommonTask.getVersion();
        // 需要用到TaskLog，所以用bean取调用
        SpringUtil.getBean(DwdPplBillingScaleMonthlyViewWork.class).genData(version);
    }

    @TaskLog(taskName = "industryCockpitV3/pplBillingScaleMonthly@DWD")
    public void genData(String statTime) {
        boolean isExist = ckcldDBHelper.isExist(CkPplBillingScaleMonthlyDO.class, "where stat_time = ?", statTime);
        if (!isExist) {
            return;
        }

        // 查询数据
        String sql = ORMUtils.getSql("/sql/industry_cockpit/v3/11_sync_ppl_billing_scale_monthly_data.sql");
        List<DwdPplBillingScaleMonthlyViewDO> dataList = ckcldDBHelper.getRaw(DwdPplBillingScaleMonthlyViewDO.class, sql, statTime);
        if(ListUtils.isEmpty(dataList)){
            return;
        }
        // 清洗数据
        clean(dataList);
        // 删除分区
        CkDBUtils.delete(ckcldStdCrpDBHelper, statTime, DwdPplBillingScaleMonthlyViewDO.class);
        // 添加
        log.info(" size :" + dataList.size());
        CkDBUtils.saveBatch(ckcldStdCrpDBHelper, dataList);
    }

    private void clean(List<DwdPplBillingScaleMonthlyViewDO> dataList) {
        Map<String,String> instanceTypeGpuCardTypeMap = industryCockpitV3DictService.getInstanceTypeGpuCardTypeMapping();

        // 获取InnerAppId
        DwdTxyAppidInfoCfReq dwdTxyAppidInfoCfReq = new DwdTxyAppidInfoCfReq().setUinType(ListUtils.newArrayList(0));
        List<Long> innerAppIdList = industryCockpitV3DictService.getAllDwdTxyAppidInfoCfDOList(dwdTxyAppidInfoCfReq)
                .stream().map(DwdTxyAppidInfoCfDO::getAppid).collect(Collectors.toList());

        dwdTxyAppidInfoCfReq = new DwdTxyAppidInfoCfReq().setCustomerType(ListUtils.newArrayList(1,2,3,99,-1));
        Map<Long,Integer> uinCustomerTypeMap = industryCockpitV3DictService.getAllDwdTxyAppidInfoCfDOList(dwdTxyAppidInfoCfReq)
                .stream().collect(Collectors.toMap(DwdTxyAppidInfoCfDO::getUin,DwdTxyAppidInfoCfDO::getCustomerType, (k1, k2) -> k1));

        // 获取通用客户简称
        List<IndustryDemandIndustryWarZoneDictDO> customerConfig = dictService.queryEnableIndustryWarZoneCustomerConfig().stream().filter(item ->
                StringUtils.isNotBlank(item.getCustomerName())
                        && StringUtils.isNotBlank(item.getCommonCustomerName())
                        && BooleanUtils.isTrue(item.getIsEnable())).collect(Collectors.toList());

        Map<String,IndustryDemandIndustryWarZoneDictDO> customerMap = customerConfig.stream().collect(Collectors.toMap(IndustryDemandIndustryWarZoneDictDO::getCustomerName, Function.identity(),(k1, k2) -> k1));

        List<String> industryDeptList = dataList.stream().map(item -> item.getIndustryDept()).distinct().collect(Collectors.toList());
        for(String industryDept : industryDeptList){
            DwdTxyAppidInfoCfReq tempReq =new DwdTxyAppidInfoCfReq().setIndustryDept(ListUtils.newArrayList(industryDept));
            if(StringUtils.equals(industryDept,"智慧行业一部 ")){
                tempReq.setNonWarZone(ListUtils.newArrayList("中长尾个人"));
            }else {
                tempReq.setNonWarZone(ListUtils.newArrayList("(空值)"));
            }
            Map<Long,DwdTxyAppidInfoCfDO> appInfoMap = industryCockpitV3DictService.getAllDwdTxyAppidInfoCfDOList(dwdTxyAppidInfoCfReq)
                    .stream().collect(Collectors.toMap(DwdTxyAppidInfoCfDO::getAppid, Function.identity(), (k1, k2) -> k1));
            for(DwdPplBillingScaleMonthlyViewDO item : dataList) {
                if(StringUtils.equals(item.getIndustryDept(),industryDept)){
                    continue;
                }
                if (appInfoMap.containsKey(item.getAppId())) {
                    item.setWarZone(appInfoMap.get(item.getAppId()).getCustomerShortName());
                    item.setUnCustomerShortName(appInfoMap.get(item.getAppId()).getCustomerShortName());
                }
            }
        }
        for(DwdPplBillingScaleMonthlyViewDO item : dataList){
            String gpuCordType = instanceTypeGpuCardTypeMap.getOrDefault(item.getInstanceType(), StdUtils.EMPTY_STR);
            item.setGpuCardType(gpuCordType);

            if(innerAppIdList.contains(item.getAppId())){
                item.setIsInner(1);
            }else {
                item.setIsInner(0);
            }

            IndustryDemandIndustryWarZoneDictDO customer = customerMap.get(item.getCustomerShortName());
            if(Objects.nonNull(customer)){
                item.setCrpWarZone(customer.getWarZoneName());
                item.setUnCustomerShortName(customer.getCommonCustomerName());
            }
            if(item.getCustomerType() == -1 && uinCustomerTypeMap.containsKey(item.getUin())){
                item.setCustomerType(uinCustomerTypeMap.get(item.getUin()));
            }
        }
    }

    /**
     * 每天凌晨8点执行一次
     */
    @Scheduled(cron = "0 0 10 * * ? ")
    public void initTask() {
        initTask(null);
    }

    /**
     * 初始化任务
     */
    public void initTask(LocalDate statTime) {
        if (statTime == null) {
            statTime = LocalDate.now();
        }
        statTime = statTime.with(TemporalAdjusters.firstDayOfMonth());
        //昨天
        LocalDate firstDayOfMonth = statTime.with(TemporalAdjusters.firstDayOfMonth());
        if(!firstDayOfMonth.equals(statTime)){
            // 不是当月第一天
            return;
        }
        SimpleCommonTask simpleCommonTask = new SimpleCommonTask();
        simpleCommonTask.setVersion(DateUtils.format(statTime));
        process.initTask(simpleCommonTask, getEnum());
    }
}
