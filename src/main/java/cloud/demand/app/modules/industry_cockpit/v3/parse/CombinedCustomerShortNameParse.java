package cloud.demand.app.modules.industry_cockpit.v3.parse;

import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.industry_cockpit.v3.constant.IndustryCockpitV3Constant;
import cloud.demand.app.modules.industry_cockpit.v3.service.IndustryCockpitV3DictService;
import cloud.demand.app.modules.sop_return.frame.where.IWhereParser;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder.SopWhere;
import com.google.common.base.Objects;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/** 合并客户简称 */
public class CombinedCustomerShortNameParse implements IWhereParser {

    @Override
    public void parse(WhereContent content, SopWhere sopWhere, Object t) {
        String columnValue = sopWhere.getColumnValue();
        Object v = sopWhere.getV();
        if (v instanceof List){
            List<String> list = (List<String>) v;
            if (ListUtils.isEmpty(list)){
                return;
            }
            // 是否有其他客户
            boolean hasOther = list.stream().anyMatch(item-> Objects.equal(item, IndustryCockpitV3Constant.OTHER_CUSTOMER));
            if (hasOther){
                // 包含其他则用排除法
                IndustryCockpitV3DictService bean = SpringUtil.getBean(IndustryCockpitV3DictService.class);
                List<String> showCustomerShortName = bean.getShowCustomerShortName();
                Set<String> set = new HashSet<>(list);
                showCustomerShortName.removeIf(set::contains);
                content.andNotIn(columnValue, showCustomerShortName);
            }else {
                content.andIn(columnValue, list);
            }
        }
    }

}
