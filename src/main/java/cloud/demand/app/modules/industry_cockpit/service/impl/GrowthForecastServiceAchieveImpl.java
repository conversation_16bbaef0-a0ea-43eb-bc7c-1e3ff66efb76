package cloud.demand.app.modules.industry_cockpit.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.entity.ScaleChangeVO;
import cloud.demand.app.modules.industry_cockpit.model.IndustryCockpoitRequest;
import cloud.demand.app.modules.industry_cockpit.service.AuthService;
import cloud.demand.app.modules.industry_cockpit.service.GrowthForecastService;
import cloud.demand.app.modules.soe.enums.DateTypeEnum;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * 增量规模预测 - 已达成 实现
 *
 * <AUTHOR>
 * @since 2024/4/25
 */
@Service
public class GrowthForecastServiceAchieveImpl implements GrowthForecastService<ScaleChangeVO> {

    @Resource
    private DBHelper ckcldDBHelper;

    @Autowired
    @Qualifier("authServiceTxyScaleImpl")
    private AuthService authServiceTxyScaleImpl;

    @Override
    public List<ScaleChangeVO> getGrowthData(IndustryCockpoitRequest request, String startDate, String endDate) {

        WhereSQL commonWhereSql = authServiceTxyScaleImpl.getAuthWhereSql();
        if (null == commonWhereSql) {
            return Collections.emptyList();
        }

        String historyChangeFixSql = " and industry_dept != '(空值)' and instance_type not like '%RM%' and instance_type not like '%RS%' and biz_range_type = '外部业务' and app_role in ('正常售卖', 'CDH', '预扣包', 'GOCP', 'CDZ') "; // and customer_tab_type != '中长尾客户'
        if ("CVM".equals(request.getProduct())) {
            commonWhereSql.and("product in (?) ", "CVM");
        }

        List<String> range = SoeCommonUtils.getRange(DateTypeEnum.YearMonth.getName(), LocalDate.parse(startDate), LocalDate.parse(endDate), LocalDate.now().plusDays(-1));

        commonWhereSql.and("stat_time in (?) ", range);

        if (!CollectionUtils.isEmpty(request.getIndustryDept())) {
            commonWhereSql.and("industry_dept in (?) ", request.getIndustryDept()); // 行业
        }

        if (!CollectionUtils.isEmpty(request.getWarZone())) {
            commonWhereSql.and("crp_war_zone in (?) ", request.getWarZone()); // 战区
        }

        if (!CollectionUtils.isEmpty(request.getCustomerShortNames())) {
            commonWhereSql.and("un_customer_short_name in (?) ", request.getCustomerShortNames()); // 客户
        }

        if (!CollectionUtils.isEmpty(request.getCustomhouseTitle())) {
            commonWhereSql.and("customhouse_title in (?) ", request.getCustomhouseTitle()); // 境内外
        }

        if (!CollectionUtils.isEmpty(request.getInstanceType())) {
            if (!StringUtils.isEmpty(request.getMergeInstanceType()) && "是".equals(request.getMergeInstanceType())) {
                commonWhereSql.and("un_instance_type in (?) ", request.getInstanceType()); // 实例类型
            } else {
                commonWhereSql.and("instance_type in (?) ", request.getInstanceType()); // 实例类型
            }
        }

        if (!CollectionUtils.isEmpty(request.getRegionName())) {
            commonWhereSql.and("region_name in (?) ", request.getRegionName()); // 地域
        }

        if (!CollectionUtils.isEmpty(request.getZoneName())) {
            commonWhereSql.and("zone_name in (?) ", request.getZoneName()); // 可用区
        }

        String lastYearSql = ORMUtils.getSql("/sql/industry_cockpit/5_actual_billing_scala_change_last_year.sql");
        lastYearSql = lastYearSql.replace("${FIX_WHERE}", historyChangeFixSql);
        lastYearSql = lastYearSql.replace("${FILTER}", commonWhereSql.getSQL());
        lastYearSql = lastYearSql.replace("${mergeInstanceType}", request.getMergeInstanceType());
        // 获取上一年的实际规模变化量
        return ckcldDBHelper.getRaw(ScaleChangeVO.class, lastYearSql, commonWhereSql.getParams());
    }
}
