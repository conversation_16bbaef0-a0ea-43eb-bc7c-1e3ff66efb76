package cloud.demand.app.modules.industry_cockpit.v3.model.resp;

import cloud.demand.app.modules.industry_cockpit.v3.model.req.SummaryWithholdDetailTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.SummaryWithholdLineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.SummaryWithholdDetailItemVO;
import cloud.demand.app.modules.industry_resource_month_report.utils.ReportUtils;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import yunti.boot.exception.BizException;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/26 16:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SummaryWithholdDetailResp {

    private List<SummaryWithholdDetailItemVO> detailItemList;
    private int count;

    public static SummaryWithholdDetailResp builder(List<SummaryWithholdDetailItemVO> list, SummaryWithholdDetailTableReq req) {
        if(Objects.isNull(req.getTopN()) || req.getTopN() > list.size()) {
            return new SummaryWithholdDetailResp(list, list.size());
        }
        SummaryWithholdDetailItemVO other = new SummaryWithholdDetailItemVO();
        for (String dim : req.getDims()) {
            try {
                Field field = SummaryWithholdDetailItemVO.class.getDeclaredField(dim);
                field.setAccessible(true);
                field.set(other, ReportUtils.OTHER);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                throw new BizException("维度不存在 或 字段访问异常");
            }
        }
        other.setOtherRaw(true);

        List<SummaryWithholdDetailItemVO> topN = ReportUtils.getTopN(list, req.getTopN(), other, (o1, o2) -> {
            o1.setNormalWithholdNum(SoeCommonUtils.addWithNull(o1.getNormalWithholdNum(), o2.getNormalWithholdNum()));
            o1.setNormalWithholdNumLe14(SoeCommonUtils.addWithNull(o1.getNormalWithholdNumLe14(), o2.getNormalWithholdNumLe14()));
            o1.setNormalWithholdNumGt14(SoeCommonUtils.addWithNull(o1.getNormalWithholdNumGt14(), o2.getNormalWithholdNumGt14()));
            o1.setElasticityWithholdNum(SoeCommonUtils.addWithNull(o1.getElasticityWithholdNum(), o2.getElasticityWithholdNum()));
            o1.setTotalNormalWithholdNum(SoeCommonUtils.addWithNull(o1.getTotalNormalWithholdNum(), o2.getTotalNormalWithholdNum()));
            o1.setLogicalWithholdNum(SoeCommonUtils.addWithNull(o1.getLogicalWithholdNum(), o2.getLogicalWithholdNum()));
            o1.setTagsWithholdNum(SoeCommonUtils.addWithNull(o1.getTagsWithholdNum(), o2.getTagsWithholdNum()));
            o1.setTotalWithholdNum(SoeCommonUtils.addWithNull(o1.getTotalWithholdNum(), o2.getTotalWithholdNum()));
        });
        return new SummaryWithholdDetailResp(topN,topN.size());
    }

    public static SummaryWithholdDetailResp builder(List<SummaryWithholdDetailItemVO> list, SummaryWithholdLineChartReq req) {
        List<SummaryWithholdDetailItemVO> itemList = ListUtils.newList();
        if (ListUtils.isEmpty(list)) {
            return new SummaryWithholdDetailResp(itemList, itemList.size());
        }

        // 排除topN
        List<SummaryWithholdDetailItemVO> excludeWithholdItems = req.getExcludeWithholdItems();
        list = list.stream().filter(item ->  !ListUtils.contains(excludeWithholdItems,
                o -> StringUtils.equals(o.getGroupKey(),item.getGroupKey()))).collect(Collectors.toList());
        Map<String,List<SummaryWithholdDetailItemVO>> group = ListUtils.groupBy(list, SummaryWithholdDetailItemVO::getStatTime);

        List<SummaryWithholdDetailItemVO> ret = ListUtils.newArrayList();
        for (Map.Entry<String,List<SummaryWithholdDetailItemVO>> entry : group.entrySet()) {
            List<SummaryWithholdDetailItemVO> entryValue = entry.getValue();
            SummaryWithholdDetailItemVO item = new SummaryWithholdDetailItemVO();
            item.setStatTime(entry.getKey());
            item.setNormalWithholdNum(entryValue.stream().map(SummaryWithholdDetailItemVO::getNormalWithholdNum).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setNormalWithholdNumLe14(entryValue.stream().map(SummaryWithholdDetailItemVO::getNormalWithholdNumLe14).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setNormalWithholdNumGt14(entryValue.stream().map(SummaryWithholdDetailItemVO::getNormalWithholdNumGt14).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setElasticityWithholdNum(entryValue.stream().map(SummaryWithholdDetailItemVO::getElasticityWithholdNum).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setTotalNormalWithholdNum(entryValue.stream().map(SummaryWithholdDetailItemVO::getTotalNormalWithholdNum).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setLogicalWithholdNum(entryValue.stream().map(SummaryWithholdDetailItemVO::getLogicalWithholdNum).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setTagsWithholdNum(entryValue.stream().map(SummaryWithholdDetailItemVO::getTagsWithholdNum).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setTotalWithholdNum(entryValue.stream().map(SummaryWithholdDetailItemVO::getTotalWithholdNum).reduce(BigDecimal.ZERO,BigDecimal::add));
            ret.add(item);
        }
        ret.sort(Comparator.comparing(SummaryWithholdDetailItemVO::getStatTime));

        return new SummaryWithholdDetailResp(ret,ret.size());
    }
}
