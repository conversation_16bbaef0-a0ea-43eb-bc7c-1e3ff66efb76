package cloud.demand.app.modules.industry_cockpit.v3.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.v3.entity.DwdPplBillingScaleMonthlyViewDO;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.HistoricalScaleMonthlyReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.HistoricalScaleMonthlyVO;
import cloud.demand.app.modules.industry_cockpit.v3.service.HistoricalScaleMonthlyService;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/21 10:32
 */
@Service
@Slf4j
public class HistoricalScaleMonthlyAvgServiceImpl extends AbstractHistoricalScaleMonthlyService {

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Override
    public List<HistoricalScaleMonthlyVO> getHistoricalScaleMonthly(HistoricalScaleMonthlyReq req) {

       return ListUtils.newArrayList();
    }

    @Override
    public List<HistoricalScaleMonthlyVO> getHistoricalScaleMonthlyForView(HistoricalScaleMonthlyReq req) {
        SopWhereBuilder sopWhereBuilder = new SopWhereBuilder(req, DwdPplBillingScaleMonthlyViewDO.class);
        ORMUtils.WhereContent whereContent = sopWhereBuilder.where();

        String sql = ORMUtils.getSql("/sql/industry_cockpit/v3/14_historical_scale_monthly_avg_view.sql");
        List<String> fieldNames = Arrays.stream(DwdPplBillingScaleMonthlyViewDO.class.getDeclaredFields()).map(item -> item.getName()).collect(Collectors.toList());

        {
            // 填充实例族
            sql = fillInstanceFamily(req, sql, fieldNames);
        }

        sql = SimpleSqlBuilder.buildDims(sql, new HashSet<>(fieldNames), req.getDims());

        sql = SimpleSqlBuilder.doReplace(sql, "unit", req.getUnit());
        sql = SimpleSqlBuilder.doReplace(sql, "queryRange", req.getQueryRange());
        sql = SimpleSqlBuilder.doReplace(sql, "where", whereContent.getSql());

        sql = req.replaceHaving(sql);

        return ckcldStdCrpDBHelper.getRaw(HistoricalScaleMonthlyVO.class, sql, whereContent.getParams());

    }


}
