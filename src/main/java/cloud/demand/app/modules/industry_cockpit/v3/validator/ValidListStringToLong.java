package cloud.demand.app.modules.industry_cockpit.v3.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

// 自定义校验注解
@Target({PARAMETER, FIELD})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = ListStringToLongValidator.class)
public @interface ValidListStringToLong {
    String message() default "列表中的只能为数字类型";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}