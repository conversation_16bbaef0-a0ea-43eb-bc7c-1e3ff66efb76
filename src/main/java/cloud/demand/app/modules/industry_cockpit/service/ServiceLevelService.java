package cloud.demand.app.modules.industry_cockpit.service;

import cloud.demand.app.modules.industry_cockpit.entity.ServiceLevelChartVO;
import cloud.demand.app.modules.industry_cockpit.entity.ServiceLevelMonthOnMonthVO;
import cloud.demand.app.modules.industry_cockpit.model.IndustryCockpoitRequest;

import java.util.List;

/**
 * 服务水平服务接口定义
 *
 * <AUTHOR>
 * @since 2024/4/25
 */
public interface ServiceLevelService {

    /**
     * 获取服务水平用于计算环比结果
     *
     * @param requestBody 查询条件
     * @return 服务水平
     */
    List<ServiceLevelMonthOnMonthVO> getServiceLevelMonthOnMonth(IndustryCockpoitRequest requestBody);

    /**
     * 获取服务水平的图表数据
     *
     * @param requestBody 查询条件
     * @return 图表数据集合
     */
    List<ServiceLevelChartVO> getServiceLevelChart(IndustryCockpoitRequest requestBody);

    /**
     * 从订单获取服务水平的图表数据
     *
     * @param requestBody 查询条件
     * @return 图表数据集合
     */
    List<ServiceLevelChartVO> getServiceLevelChartFromOrder(IndustryCockpoitRequest requestBody);

}
