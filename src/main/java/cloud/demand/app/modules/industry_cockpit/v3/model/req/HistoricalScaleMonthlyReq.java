package cloud.demand.app.modules.industry_cockpit.v3.model.req;

import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.modules.industry_cockpit.auth.AuthCheckParam;
import cloud.demand.app.modules.industry_cockpit.v3.constant.IndustryCockpitV3Constant;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/15 10:32
 */
@Data
public class HistoricalScaleMonthlyReq extends IndustryCockpitV3HistoricalReq implements IInstanceFamilyDimReq {
    private List<String> dims;// 维度:industryDept、warZone【crpWarZone】、customerShortName【unCustomerShortName】、instanceType、gpuCardType、regionName、zoneName

    private List<String> disDims = ListUtils.newArrayList(
            "industryDept","customerShortName","instanceType","zoneName"
    ); // 去重维度；默认去重industryDept、customerShortName、instanceType、zoneName，其他可选：showCustomerShortName，warZone，gpuCardType，regionName

    /** 去重维度 + 维度 */
    public List<String> getDisAndDims(){
        return ListUtils.union(this.getDims(),this.getDisDims());
    }

    @SopReportWhere(sql = " origin_industry_dept in (?) ")
    @AuthCheckParam(authField = "industry")
    private List<String> industryDept; // 行业部门 支持多选

    @NotNull(message = "统计周期开始月分不能为空")
    private String startYearMonth;// 开始月份

    @NotNull(message = "统计周期结束月分不能为空")
    private String endYearMonth;// 结束月份

    private String demandType; // 需求类型: 新增、退回、净增

    private String originStartYearMonth;

    private String originEndYearMonth;

    private boolean export = false;

    @SopReportWhere
    private List<String> statTime;

    public void setYearMonth(String caliber) {
        this.originStartYearMonth = startYearMonth;
        this.originEndYearMonth = endYearMonth;
        if (StringUtils.equals(caliber, IndustryCockpitV3Constant.MONTHLY_AVG_CHANG) || StringUtils.equals(caliber, IndustryCockpitV3Constant.MONTHLY_SLICE_CHANG)) {
            String lastYearStartYearMonth = LocalDate.now().plusYears(-1).with(TemporalAdjusters.firstDayOfYear()).format(DateTimeFormatter.ofPattern("yyyy-MM"));
            ;
            String currentMonthYearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            if (startYearMonth.compareTo(lastYearStartYearMonth) > 0) {
                startYearMonth = lastYearStartYearMonth;
            }
            if (endYearMonth.compareTo(currentMonthYearMonth) < 0) {
                endYearMonth = currentMonthYearMonth;
            }
        }
    }

    public List<String> getFirstDayOfMonthList() {
        if (StringUtils.isEmpty(this.getEndYearMonth()) || StringUtils.isEmpty(this.getStartYearMonth())) {
            return ListUtils.newArrayList(Constant.EMPTY_DATE);
        }
        List<String> yearMonthList = DateUtils.listYearMonth(this.getStartYearMonth(), this.getEndYearMonth())
                .stream().map(o -> SoeCommonUtils.getYearMonth(o.getYear(), o.getMonth()))
                .collect(Collectors.toList());
        return yearMonthList.stream().map(o -> o + "-01").collect(Collectors.toList());
    }

    public List<String> getLastDayOfMonthList() {
        if (StringUtils.isEmpty(this.getEndYearMonth()) || StringUtils.isEmpty(this.getStartYearMonth())) {
            return ListUtils.newArrayList(Constant.EMPTY_DATE);
        }
        LocalDate startDate = LocalDate.parse(this.getStartYearMonth() + "-01");
        LocalDate endDate = LocalDate.parse(this.getEndYearMonth() + "-01", DateTimeFormatter.ISO_LOCAL_DATE).with(TemporalAdjusters.lastDayOfMonth());
        LocalDate maxDate = LocalDate.now().plusDays(-1).isBefore(endDate) ? LocalDate.now().plusDays(-1) : endDate;
        return SoeCommonUtils.getRange(startDate, endDate, maxDate);
    }

    public String replaceHaving(String sql) {
        String having = " having abs(all_amount) > 0 ";
        if (!export && ListUtils.contains(this.getDims(), item -> StringUtils.equals(item, "customerShortName"))) {
            if (StringUtils.equals(this.getUnit(), "核数")) {
                having = " having abs(all_amount) >= 100 ";
            } else {
                having = " having abs(all_amount) > 0 ";
            }
            if (ListUtils.isNotEmpty(this.getCustomerShortName())) {
                having = having + " or customer_short_name in ('" + StringUtils.join(this.getCustomerShortName(), "','") + "')";
            }
        }
        sql = SimpleSqlBuilder.doReplace(sql, "having", having);
        return sql;
    }


    public String replaceAnyHaving(String sql) {
        String having = " having abs(all_amount) > 0 ";
        if (!export && ListUtils.contains(this.getDims(), item -> StringUtils.equals(item, "customerShortName"))) {
            if (StringUtils.equals(this.getUnit(), "核数")) {
                having = " having abs(all_amount) >= 100 ";
            } else {
                having = " having abs(all_amount) > 0 ";
            }
            if (ListUtils.isNotEmpty(this.getCustomerShortName())) {
                having = having + " or any_customer_short_name in ('" + StringUtils.join(this.getCustomerShortName(), "','") + "')";
            }
        }
        sql = SimpleSqlBuilder.doReplace(sql, "having", having);
        return sql;
    }
}
