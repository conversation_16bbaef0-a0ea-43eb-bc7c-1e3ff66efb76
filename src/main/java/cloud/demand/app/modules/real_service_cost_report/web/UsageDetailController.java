package cloud.demand.app.modules.real_service_cost_report.web;

import cloud.demand.app.modules.real_service_cost_report.model.RealServiceCostParamsTypeReq;
import cloud.demand.app.modules.real_service_cost_report.model.UsageDetailResp;
import cloud.demand.app.modules.real_service_cost_report.model.UsageDetailVO;
import cloud.demand.app.modules.real_service_cost_report.model.UsageReportReq;
import cloud.demand.app.modules.real_service_cost_report.service.UsageDetailService;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/28 15:05
 */
@JsonrpcController("real-service-cost/detail")
public class UsageDetailController {

    @Resource
    private UsageDetailService usageDetailService;

    @RequestMapping
    public UsageDetailResp queryUsageReport(@JsonrpcParam UsageReportReq req) {

        List<UsageDetailVO> daList = usageDetailService.queryUsageReport(req);

        return new UsageDetailResp(daList);
    }

    @RequestMapping
    public List<String> queryParams(@JsonrpcParam @Valid RealServiceCostParamsTypeReq req) {
        return usageDetailService.queryParams(req);
    }

    @RequestMapping
    public ResponseEntity<InputStreamResource> exportDetailData(@JsonrpcParam UsageReportReq req) {
        List<String> dims = ListUtils.newArrayList("yearMonth", "product", "industryDept", "demandType", "customerShortName",
                "customerName", "customerUin", "instanceType", "city");
        req.setDims(dims);
        return usageDetailService.exportDetailData(req);
    }
}
