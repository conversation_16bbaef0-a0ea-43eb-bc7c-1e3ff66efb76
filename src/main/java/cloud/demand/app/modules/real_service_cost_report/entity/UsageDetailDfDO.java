package cloud.demand.app.modules.real_service_cost_report.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 真实服务成本-明细表
 */
@Data
@ToString
@Table("std_crp.report_real_service_usage_detail_df")
public class UsageDetailDfDO {

    /** 切片时间<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private String statTime;

    /** 月份<br/>Column: [data_month] */
    @Column(value = "year_month")
    private String yearMonth;

    /** 产品，默认CVM<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 需求类型<br/>Column: [demand_type] */
    @Column(value = "demand_type")
    private String demandType;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 客户UIN<br/>Column: [customer_uin] */
    @Column(value = "customer_uin")
    private String customerUin;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 城市<br/>Column: [city] */
    @Column(value = "city")
    private String city;

    /** A/月均核<br/>Column: [bill_month_avg_a] */
    @Column(value = "bill_month_avg_a")
    private BigDecimal billMonthAvgA;

    /** B（未履约闲置核心数)<br/>Column: [b_monthly_usage] */
    @Column(value = "b_monthly_usage")
    private BigDecimal monthlyUsageB;

    /** D（订单如期退回再周转核心数)<br/>Column: [d_monthly_usage] */
    @Column(value = "d_monthly_usage")
    private BigDecimal monthlyUsageD;

    /** E（订单提前退回再周转核心数)<br/>Column: [e_monthly_usage] */
    @Column(value = "e_monthly_usage")
    private BigDecimal monthlyUsageE;

    /** E1（有报备的订单提前退回再周转核心数)<br/>Column: [e1_monthly_usage] */
    @Column(value = "e1_monthly_usage")
    private BigDecimal monthlyUsageE1;

    /** E2（无报备的订单提前退回再周转核心数)<br/>Column: [e2_monthly_usage] */
    @Column(value = "e2_monthly_usage")
    private BigDecimal monthlyUsageE2;

    /** 订单如期退回/月均核<br/>Column: [total_sch_resource_size] */
    @Column(value = "total_sch_resource_size")
    private BigDecimal totalSchResourceSize;

    /** 订单提前退回/月均核<br/>Column: [total_pre_resource_size] */
    @Column(value = "total_pre_resource_size")
    private BigDecimal totalPreResourceSize;

}