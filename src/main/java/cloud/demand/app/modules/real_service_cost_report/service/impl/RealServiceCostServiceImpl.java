package cloud.demand.app.modules.real_service_cost_report.service.impl;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.mrpv2.domain.ForecastMatchRateReq;
import cloud.demand.app.modules.mrpv2.domain.ForecastMatchRateResp;
import cloud.demand.app.modules.mrpv2.enums.ProjectTypeEnum;
import cloud.demand.app.modules.mrpv2.service.CloudResourceOperatorService;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionDto;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.real_service_cost_report.entity.PredictedCoreNumberDo;
import cloud.demand.app.modules.real_service_cost_report.entity.ProdTypeDo;
import cloud.demand.app.modules.real_service_cost_report.entity.ReportRscCustomerDO;
import cloud.demand.app.modules.real_service_cost_report.entity.ReportRscIndustryDO;
import cloud.demand.app.modules.real_service_cost_report.entity.ReportRscMonthDO;
import cloud.demand.app.modules.real_service_cost_report.model.MatchRateResp;
import cloud.demand.app.modules.real_service_cost_report.model.PredictedCoreNumberResp;
import cloud.demand.app.modules.real_service_cost_report.model.RealServiceCostReq;
import cloud.demand.app.modules.real_service_cost_report.model.RealServiceCostResp;
import cloud.demand.app.modules.real_service_cost_report.service.RealServiceCostService;
import cloud.demand.app.modules.soe.dto.resp.ErrorMessage;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.web.model.common.StreamDownloadBean;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.Nullable;
import org.nutz.lang.Strings;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static cloud.demand.app.modules.real_service_cost_report.model.MatchIndustryDept.getMatchIndustryDept;

@Service
public class RealServiceCostServiceImpl implements RealServiceCostService {

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DBHelper ckStdCrpDBHelper;

    /**
     * 鉴权
     */
    @Resource
    protected PermissionService permissionService;

    @Resource
    private CloudResourceOperatorService cloudResourceOperatorService;

    @Override
    public List<RealServiceCostResp> queryRealServiceCostDetail(RealServiceCostReq req) {

        boolean authentication = authentication(req);
        if (!authentication) {
            return new ArrayList<>();
        }

        List<String> dims = req.getDims();
        if (Strings.isBlank(req.getIndustryDept())) {
            throw new WrongWebParameterException("行业名称为空");
        }
        if (Strings.isBlank(req.getCustomerShortName())) {
            throw new WrongWebParameterException("客户名称为空");
        }
        String startYearMonth = req.getStartYearMonth().replace("-", "");
        String endYearMonth = req.getEndYearMonth().replace("-", "");
        String staticsCategory = req.getStaticsCategory();
        String industryName = req.getIndustryDept();
        String customerShortName = req.getCustomerShortName();
        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        whereContent.addAnd("current.data_month >= ? and current.data_month <= ?", startYearMonth, endYearMonth);
        whereContent.addAnd("current.statics_category = ?", staticsCategory);

        List<String> authIndustryDept = req.getAuthIndustryDept();
        if (authIndustryDept != null && !authIndustryDept.isEmpty()) {
            if (industryName.equals("全部行业")) {
                industryName = authIndustryDept.get(0);
            } else if (!authIndustryDept.contains(industryName)){
                throw new BizException("无权访问" + industryName + "数据");
            }
        }

        Set<String> matchIndustryDept = getMatchIndustryDept();

        // 按维度进行查询，共有三个维度 yearmonth、industry、custom
        // 由于数据无法聚合，所以部分查询虽然时第一维度，但实际查询时是按第二维度（例如指定行业时，年月维度也应该返回第二维度的作为聚合结果）
        if (dims.size() == 1 && industryName.equals("全部行业") && customerShortName.equals("全部客户")) {
            String monthSql = ORMUtils.getSql("/sql/real_service_cost_report/get_month_rsc_report.sql") + whereContent.getSql();
            List<ReportRscMonthDO> monthDOList = demandDBHelper.getRaw(ReportRscMonthDO.class, monthSql, whereContent.getParams());
            return ListUtils.transform(monthDOList, RealServiceCostResp::transform);
        } else if ((dims.size() == 2 && customerShortName.equals("全部客户")) || (dims.size() == 1 && !industryName.equals("全部行业") && customerShortName.equals("全部客户"))) {

            if (Strings.isBlank(industryName)) {
                throw new WrongWebParameterException("行业名称不能为空");
            } else if (!industryName.equals("全部行业")) {
                whereContent.addAnd("current.industry_route_name = ?", industryName);
            }
            String industrySql = ORMUtils.getSql("/sql/real_service_cost_report/get_industry_rsc_report.sql");
            industrySql = industrySql + whereContent.getSql();
            List<ReportRscIndustryDO> industryList = demandDBHelper.getRaw(ReportRscIndustryDO.class, industrySql, whereContent.getParams());

            // 在行业库和客户库中，会有一些记录的行业名称为null，对其进行处理
            return industryList.stream().filter(item -> matchIndustryDept.contains(item.getIndustryDept())).map((item) -> {
                String industryRouteName = item.getIndustryDept();
                if (Strings.isBlank(industryRouteName)) {
                    item.setIndustryDept("未分类行业");
                }
                return RealServiceCostResp.transform(item);
            }).collect(Collectors.toList());

        } else if (dims.size() == 3 || dims.size() == 2 || dims.size() == 1) {
            if (!industryName.equals("全部行业")) {
                whereContent.addAnd("current.industry_route_name = ?", industryName);
            }
            if (!customerShortName.equals("全部客户")) {
                whereContent.addAnd("current.customer_short_name = ?", customerShortName);
            }
            String customSql = ORMUtils.getSql("/sql/real_service_cost_report/get_custom_rsc_report.sql") + whereContent.getSql();
            List<ReportRscCustomerDO> customerList = demandDBHelper.getRaw(ReportRscCustomerDO.class, customSql, whereContent.getParams());
            List<RealServiceCostResp> transform = customerList.stream().filter(item -> matchIndustryDept.contains(item.getIndustryDept())).map((item) -> {
                String industryRouteName = item.getIndustryDept();
                if (Strings.isBlank(industryRouteName)) {
                    item.setIndustryDept("未分类行业");
                }
                return RealServiceCostResp.transform(item);
            }).collect(Collectors.toList());

            // 特定客户对应某个月可能会查到多条数据（无法聚合），需要进行处理
            if (dims.size() == 1) {
                Map<String, List<RealServiceCostResp>> groupBy = ListUtils.groupBy(transform, RealServiceCostResp::getYearMonth);
                List<RealServiceCostResp> respList = new ArrayList<>();
                for (Map.Entry<String, List<RealServiceCostResp>> entry : groupBy.entrySet()) {
                    String key = entry.getKey(); // 获取键，即年月
                    List<RealServiceCostResp> value = entry.getValue(); // 获取值，即对应的RealServiceCostResp列表
                    if (value.size() > 1) {
                        RealServiceCostResp realServiceCostResp = new RealServiceCostResp();
                        realServiceCostResp.setYearMonth(key);
                        respList.add(realServiceCostResp);
                    } else {
                        respList.addAll(value);
                    }
                }
                return respList;
            }
            return transform;
        } else {
            throw new WrongWebParameterException("维度参数错误");
        }
    }

    @Override
    public List<MatchRateResp> queryMatchRate(RealServiceCostReq req) {

        List<RealServiceCostResp> respList = queryRealServiceCostDetail(req);
        if (respList.isEmpty()) {
            return new ArrayList<>();
        }

        String startYearMonth = req.getStartYearMonth().replace("-", "");
        String endYearMonth = req.getEndYearMonth().replace("-", "");
        String staticsCategory = req.getStaticsCategory();
        String industryName = req.getIndustryDept();
        String customerShortName = req.getCustomerShortName();
        List<String> dims = req.getDims();
        List<String> matchRateDims = new ArrayList<>();
        startYearMonth = getPreviousMonth(startYearMonth);
        endYearMonth = endYearMonth.substring(0, 4) + "-" + endYearMonth.substring(4);

        List<String> authIndustryDept = req.getAuthIndustryDept();
        if (authIndustryDept != null && !authIndustryDept.isEmpty()) {
            if (industryName.equals("全部行业")) {
                industryName = authIndustryDept.get(0);
            } else if (!authIndustryDept.contains(industryName)){
                throw new BizException("无权访问" + industryName + "数据");
            }
        }

        ForecastMatchRateReq forecastMatchRateReq = new ForecastMatchRateReq();
        forecastMatchRateReq.setProduct("CVM");
        forecastMatchRateReq.setProjectType(ListUtils.newArrayList(ProjectTypeEnum.KEY_PROJECT.getName()));
        forecastMatchRateReq.setDemandType("新增&弹性");
        forecastMatchRateReq.setStartYearMonth(startYearMonth);
        forecastMatchRateReq.setEndYearMonth(endYearMonth);
        forecastMatchRateReq.setIsIntervention(false);
        forecastMatchRateReq.setIsCommonInstanceType(true);

        Map<String, List<String>> matchMap = new HashMap<>();

        // 实际查询的维度
        int selectFlag;

        if (dims.size() == 1 && industryName.equals("全部行业") && customerShortName.equals("全部客户")) {
            selectFlag = 1;
            forecastMatchRateReq.setDims(matchRateDims);
        } else if ((dims.size() == 2 && customerShortName.equals("全部客户")) || (dims.size() == 1 && !industryName.equals("全部行业") && customerShortName.equals("全部客户"))) {
            selectFlag = 2;
            matchRateDims.add("industryDept");
            forecastMatchRateReq.setDims(matchRateDims);
            if (!industryName.equals("全部行业")) {
                forecastMatchRateReq.setIndustryDept(Collections.singletonList(industryName));
            }
        } else if (dims.size() == 3 || dims.size() == 2 || dims.size() == 1) {
            selectFlag = 3;
            matchRateDims.add("industryDept");
            matchRateDims.add("customerShortName");
            forecastMatchRateReq.setDims(matchRateDims);
            if (!industryName.equals("全部行业")) {
                forecastMatchRateReq.setIndustryDept(Collections.singletonList(industryName));
            }
            if (!customerShortName.equals("全部客户")) {
                forecastMatchRateReq.setCustomerShortName(Collections.singletonList(customerShortName));
            }
        } else {
            selectFlag = -1;
        }
        List<ForecastMatchRateResp.Item> itemList = new ArrayList<>();
        if (staticsCategory.equals("全量机型")) {
            itemList.addAll(cloudResourceOperatorService.queryForecastMatchRate(forecastMatchRateReq));
        } else if (staticsCategory.equals("增量机型")) {
            List<ProdTypeDo> typeDoList = demandDBHelper.getAll(ProdTypeDo.class);
            Map<String, List<ProdTypeDo>> stringListMap = ListUtils.groupBy(typeDoList, ProdTypeDo::getExpirationDate);
            for (String key : stringListMap.keySet()) {
                List<ProdTypeDo> prodTypeList = stringListMap.get(key); // 获取对应的List<ProdTypeDo>
                ArrayList<String> list = new ArrayList<>();
                for (ProdTypeDo prodTypeDo : prodTypeList) {
                    list.add(prodTypeDo.getInstanceType());
                }
                matchMap.put(key, list);
            }
            // 起始月份
            YearMonth start = YearMonth.of(Integer.parseInt(startYearMonth.substring(0, 4)), Integer.parseInt(startYearMonth.substring(5)));
            YearMonth end = YearMonth.of(Integer.parseInt(endYearMonth.substring(0, 4)), Integer.parseInt(endYearMonth.substring(5)));
            String from = String.valueOf(start);
            String to = String.valueOf(start);
            String quarter = determineQuarter(from);
            // 遍历月份,增量的情况下根据所属的不同季度，对应的增量机型不同，所以需要多次查询
            for (YearMonth current = start; !current.isAfter(end); current = current.plusMonths(1)) {
                String currentYearMonth = String.valueOf(current);
                String determineQuarter = determineQuarter(String.valueOf(currentYearMonth));
                if (!determineQuarter.equals(quarter)) {
                    forecastMatchRateReq.setStartYearMonth(from);
                    forecastMatchRateReq.setEndYearMonth(to);
                    if (matchMap.containsKey(quarter)) {
                        forecastMatchRateReq.setInstanceType(matchMap.get(quarter));
                        List<ForecastMatchRateResp.Item> list = cloudResourceOperatorService.queryForecastMatchRate(forecastMatchRateReq);
                        if (!list.isEmpty()) {
                            itemList.addAll(list);
                        }
                    }
                    from = currentYearMonth;
                    quarter = determineQuarter;
                }
                to = currentYearMonth;
            }
            if (matchMap.containsKey(quarter)) {
                forecastMatchRateReq.setStartYearMonth(from);
                forecastMatchRateReq.setEndYearMonth(to);
                forecastMatchRateReq.setInstanceType(matchMap.get(quarter));
                List<ForecastMatchRateResp.Item> list = cloudResourceOperatorService.queryForecastMatchRate(forecastMatchRateReq);
                if (!list.isEmpty()) {
                    itemList.addAll(list);
                }
            }
        }

        Map<String, BigDecimal> map = itemList.stream().collect(Collectors.toMap(
                item -> {
                    String yearMonth = item.getYearMonth();
                    if (selectFlag == 1) {
                        return yearMonth;
                    } else if (selectFlag == 2) {
                        return yearMonth + item.getIndustryDept();
                    } else {
                        return yearMonth + item.getIndustryDept() + item.getCustomerShortName();
                    }
                },
                ForecastMatchRateResp.Item::getCoreByDayMatchRate
        ));

        return respList.stream().map(data -> {
            MatchRateResp matchRateResp = new MatchRateResp();
            String key = getString(selectFlag, data.getYearMonth(), data);
            matchRateResp.setYearMonth(data.getYearMonth());
            matchRateResp.setIndustryDept(data.getIndustryDept());
            matchRateResp.setCustomerShortName(data.getCustomerShortName());
            if (map.containsKey(key)) {
                BigDecimal number = map.get(key);
                BigDecimal divide = number.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);
                matchRateResp.setCoreByDayMatchRate(divide);
            }
            if (!Strings.isBlank(data.getPreYearMonth())) {
                matchRateResp.setPreYearMonth(data.getPreYearMonth());
                String preKey = getString(selectFlag, data.getPreYearMonth(), data);
                if (map.containsKey(preKey)) {
                    BigDecimal preNumber = map.get(preKey);
                    BigDecimal preDivide = preNumber.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);
                    matchRateResp.setPreCoreByDayMatchRate(preDivide);
                }
            }
            return matchRateResp;
        }).collect(Collectors.toList());
    }

    @Override
    public List<PredictedCoreNumberResp> queryPredictedCoreNumber(RealServiceCostReq req) {

        List<RealServiceCostResp> respList = queryRealServiceCostDetail(req);
        if (respList.isEmpty()) {
            return new ArrayList<>();
        }

        String startYearMonth = req.getStartYearMonth().replace("-", "");
        String endYearMonth = req.getEndYearMonth().replace("-", "");
        String staticsCategory = req.getStaticsCategory();
        String industryName = req.getIndustryDept();
        String customerShortName = req.getCustomerShortName();
        List<String> dims = req.getDims();

        List<String> authIndustryDept = req.getAuthIndustryDept();
        if (authIndustryDept != null && !authIndustryDept.isEmpty()) {
            if (industryName.equals("全部行业")) {
                industryName = authIndustryDept.get(0);
            } else if (!authIndustryDept.contains(industryName)){
                throw new BizException("无权访问" + industryName + "数据");
            }
        }

        String preSql = "select instance_type, common_customer_short_name as customer_short_name, industry_dept as industry_dept, " +
                "sum(total_core) as core_num, formatDateTime(begin_buy_date, '%Y-%m') as `year_month` from std_crp.dws_crp_ppl_item_version_newest_cf";
        String tailSql = " group by customer_short_name, industry_dept, win_rate, gpu_type, `year_month`, instance_type having core_num != 0";
        String startTime = getPreviousMonth(startYearMonth) + "-01";
        YearMonth yearMonth = YearMonth.of(Integer.parseInt(endYearMonth.substring(0, 4)), Integer.parseInt(endYearMonth.substring(4)));
        LocalDate localDate = yearMonth.atEndOfMonth();
        String endTime = localDate.toString();
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("source in ('IMPORT','APPLY_AUTO_FILL','FORECAST')");
        whereSQL.and("begin_buy_date >= ? and begin_buy_date <= ?", startTime, endTime);
        if (!industryName.equals("全部行业")) {
            whereSQL.and("industry_dept = ?", industryName);
        }
        if (!customerShortName.equals("全部客户")) {
            whereSQL.and("customer_short_name = ?", customerShortName);
        }
        whereSQL.and("demand_type = 'RETURN'");
        whereSQL.and("product in ('CVM&CBS')");
        String sql = preSql + whereSQL.getSQL() + tailSql;
        List<PredictedCoreNumberDo> coreNumberDoList = ckStdCrpDBHelper.getRaw(PredictedCoreNumberDo.class, preSql + whereSQL.getSQL() + tailSql, whereSQL.getParams());
        if (staticsCategory.equals("增量机型")) {
            Map<String, Set<String>> matchMap = matchProdType();
            List<PredictedCoreNumberDo> list = new ArrayList<>();
            for (PredictedCoreNumberDo predictedCoreNumberDo : coreNumberDoList) {
                String determineQuarter = determineQuarter(predictedCoreNumberDo.getYearMonth());
                if (matchMap.containsKey(determineQuarter) && matchMap.get(determineQuarter).contains(predictedCoreNumberDo.getInstanceType())) {
                    list.add(predictedCoreNumberDo);
                }
            }
            coreNumberDoList = list;
        }
        int selectFlag;
        if (dims.size() == 1 && industryName.equals("全部行业") && customerShortName.equals("全部客户")) {
            selectFlag = 1;
        } else if ((dims.size() == 2 && customerShortName.equals("全部客户")) || (dims.size() == 1 && !industryName.equals("全部行业") && customerShortName.equals("全部客户"))) {
            selectFlag = 2;
        } else if (dims.size() == 3 || dims.size() == 2 || dims.size() == 1) {
            selectFlag = 3;
        } else {
            selectFlag = -1;
        }

        HashMap<String, Integer> map = new HashMap<>();
        for (PredictedCoreNumberDo predictedCoreNumberDo : coreNumberDoList) {
            String key = null;
            String time = predictedCoreNumberDo.getYearMonth();
            if (selectFlag == 1) {
                key = time;
            } else if (selectFlag == 2) {
                key = time + predictedCoreNumberDo.getIndustryDept();
            } else {
                key = time + predictedCoreNumberDo.getIndustryDept() + predictedCoreNumberDo.getCustomerShortName();
            }
            map.put(key, map.getOrDefault(key, 0) + predictedCoreNumberDo.getCoreNum());
        }

        return respList.stream().map(data -> {
            PredictedCoreNumberResp predictedCoreNumberResp = new PredictedCoreNumberResp();
            String key = getString(selectFlag, data.getYearMonth(), data);
            predictedCoreNumberResp.setYearMonth(data.getYearMonth());
            predictedCoreNumberResp.setIndustryDept(data.getIndustryDept());
            predictedCoreNumberResp.setCustomerShortName(data.getCustomerShortName());
            if (map.containsKey(key)) {
                predictedCoreNumberResp.setReturnPredictedCoreNumber(map.get(key));
            }
            if (!Strings.isBlank(data.getPreYearMonth())) {
                predictedCoreNumberResp.setPreYearMonth(data.getPreYearMonth());
                String preKey = getString(selectFlag, data.getPreYearMonth(), data);
                if (map.containsKey(preKey)) {
                    predictedCoreNumberResp.setPreReturnPredictedCoreNumber(map.get(preKey));
                }
            }
            return predictedCoreNumberResp;
        }).collect(Collectors.toList());
    }

    @Nullable
    private static String getString(int selectFlag, String data, RealServiceCostResp data1) {
        String preKey = null;
        if (selectFlag == 1) {
            preKey = data.substring(0, 4) + "-" + data.substring(4);
        } else if (selectFlag == 2) {
            preKey = data.substring(0, 4) + "-" + data.substring(4) + data1.getIndustryDept();
        } else if (selectFlag == 3) {
            preKey = data.substring(0, 4) + "-" + data.substring(4) + data1.getIndustryDept() + data1.getCustomerShortName();
        }
        return preKey;
    }

    // 导出excel
    @Override
    public ResponseEntity<InputStreamResource> excel(RealServiceCostReq req) {

        List<RealServiceCostResp> excelData = queryRealServiceCostDetail(req);
        List<MatchRateResp> matchRateRespList = queryMatchRate(req);
        List<PredictedCoreNumberResp> predictedCoreNumberRespList = queryPredictedCoreNumber(req);

        Map<String, BigDecimal[]> matchRateMap = new HashMap<>();
        Map<String, int[]> predictedCoreNumberMap = new HashMap<>();
        for (MatchRateResp matchRateResp : matchRateRespList) {
            String key = matchRateResp.getYearMonth() + matchRateResp.getIndustryDept() + matchRateResp.getCustomerShortName();
            BigDecimal[] bigDecimals = new BigDecimal[2];
            if (matchRateResp.getCoreByDayMatchRate() != null) {
                bigDecimals[0] = matchRateResp.getCoreByDayMatchRate();
            }
            if (matchRateResp.getPreCoreByDayMatchRate() != null) {
                bigDecimals[1] = matchRateResp.getPreCoreByDayMatchRate();
            }
            matchRateMap.put(key, bigDecimals);
        }
        for (PredictedCoreNumberResp predictedCoreNumberResp : predictedCoreNumberRespList) {
            String key = predictedCoreNumberResp.getYearMonth() + predictedCoreNumberResp.getIndustryDept() + predictedCoreNumberResp.getCustomerShortName();
            int[] arr = new int[2];
            if (predictedCoreNumberResp.getReturnPredictedCoreNumber() != null) {
                arr[0] = predictedCoreNumberResp.getReturnPredictedCoreNumber();
            }
            if (predictedCoreNumberResp.getPreReturnPredictedCoreNumber() != null) {
                arr[1] = predictedCoreNumberResp.getPreReturnPredictedCoreNumber();
            }
            predictedCoreNumberMap.put(key, arr);
        }

        excelData.forEach(data -> {
            if (!data.getIndustryDept().equals("未分类行业")) {
                String key = data.getYearMonth() + data.getIndustryDept() + data.getCustomerShortName();
                BigDecimal[] matchRateArray = matchRateMap.get(key);
                int[] predictedCoreNumberArray = predictedCoreNumberMap.get(key);
                if (matchRateArray != null && matchRateArray.length >= 2) {
                    data.setCoreByDayMatchRate(matchRateArray[0]);
                    data.setPreCoreByDayMatchRate(matchRateArray[1]);
                }
                if (predictedCoreNumberArray != null && predictedCoreNumberArray.length >= 2) {
                    data.setReturnPredictedCoreNumber(predictedCoreNumberArray[0]);
                    data.setPreReturnPredictedCoreNumber(predictedCoreNumberArray[1]);
                }
            }
        });

        ByteArrayInputStream in;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            // 使用EasyExcel写入数据
            EasyExcel.write(out, RealServiceCostResp.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("客户维度")
                    .doWrite(excelData);

            in = new ByteArrayInputStream(out.toByteArray());
        } catch (IOException e) {
            throw new RuntimeException("Error while writing Excel file", e);
        }

        String fileName = "行业/通路-机型-客户报表" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8) + ".xlsx";
        return new StreamDownloadBean(fileName, in);
    }

    // 导入增量主力机型并更新数据库
    @Transactional("demandTransactionManager")
    @Override
    public List<ErrorMessage> uploadProdTypeExcel(MultipartFile file) {

        List<ErrorMessage> ret = new ArrayList<>();

        boolean authentication = uploadAuthentication();
        if (!authentication) {
            return ret;
        }

        List<ProdTypeDo> saveData = SoeCommonUtils.parseExcel(file, ProdTypeDo.class, 1, 0);

        if (ListUtils.isEmpty(saveData)) {
            return new ArrayList<>();
        }

        for (int i = 0; i < saveData.size(); i++) {
            int index = i + 2;
            String expirationDate = saveData.get(i).getExpirationDate();
            if (!expirationDate.matches("\\d{4}Q[1-4]")) {
                ret.add(new ErrorMessage(index, 1, "有效期", String.format("有效期类型错误【%s】，应为季度类型", expirationDate)));
            }
            saveData.get(i).setInstanceType(saveData.get(i).getInstanceType().toLowerCase());
        }

        if (ListUtils.isNotEmpty(ret)) {
            return ret;
        }

        String sql = "TRUNCATE TABLE real_service_cost_increment_instance_type";
        demandDBHelper.executeRaw(sql);

        for (ProdTypeDo saveDatum : saveData) {
            saveDatum.setInstanceType(saveDatum.getInstanceType().toUpperCase());
        }

        demandDBHelper.insert(saveData);
        return ret;
    }

    // 导出增量主力机型Excel
    @Override
    public ResponseEntity<InputStreamResource> downLoadProdTypeExcel() {

        List<ProdTypeDo> excelData = demandDBHelper.getAll(ProdTypeDo.class);
        ByteArrayInputStream in;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            // 使用EasyExcel写入数据
            EasyExcel.write(out, ProdTypeDo.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("增量主力机型清单")
                    .doWrite(excelData);

            in = new ByteArrayInputStream(out.toByteArray());
        } catch (IOException e) {
            throw new RuntimeException("Error while writing Excel file", e);
        }

        String fileName = "增量主力机型" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8) + ".xlsx";
        return new StreamDownloadBean(fileName, in);
    }

    /*
     * 当查询增量机型时，查询增量机型清单，便于后续聚合
     * */
    public Map<String, Set<String>> matchProdType() {
        Map<String, Set<String>> map = new HashMap<>();
        List<ProdTypeDo> typeDoList = demandDBHelper.getAll(ProdTypeDo.class);
        typeDoList.forEach(data -> {
            String instanceType = data.getInstanceType();
            String expirationDate = data.getExpirationDate();
            if (!map.containsKey(expirationDate)) {
                HashSet<String> set = new HashSet<>();
                set.add(instanceType);
                map.put(expirationDate, set);
            } else {
                map.get(expirationDate).add(instanceType);
            }
        });
        return map;
    }

    // 月份转季度
    public String determineQuarter(String dateStr) {
        int year = Integer.parseInt(dateStr.substring(0, 4)); // 提取年份
        int month = Integer.parseInt(dateStr.substring(5, 7)); // 提取月份

        String quarter;
        if (month >= 1 && month <= 3) {
            quarter = "Q1";
        } else if (month >= 4 && month <= 6) {
            quarter = "Q2";
        } else if (month >= 7 && month <= 9) {
            quarter = "Q3";
        } else {
            quarter = "Q4";
        }

        return year + quarter; // 返回格式化的季度字符串
    }

    // 获取上一个月
    public String getPreviousMonth(String dateStr) {
        // 分割字符串获取年份和月份
        int year = Integer.parseInt(dateStr.substring(0, 4));
        int month = Integer.parseInt(dateStr.substring(4));
        int lastMonthYear = year;
        int lastMonth = month - 1;
        if (lastMonth == 0) {
            lastMonth = 12;
            lastMonthYear--;
        }
        return String.format("%04d-%02d", lastMonthYear, lastMonth);
    }

    private boolean authentication(RealServiceCostReq req) {
        // 获取用户名
        String userNameWithSystem = LoginUtils.getUserNameWithSystem();
        // 判断是不是管理员
        Boolean adminUserOrNot = permissionService.checkIsAdmin(userNameWithSystem);
        // 是admin或者不需要鉴权直接跳过
        if (adminUserOrNot) {
            return true;
        }
        // 如果不是管理员 才需要判断是否有权限看数据
        UserPermissionDto permission = permissionService.getPermissionByUserAndRole(IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode(), userNameWithSystem);
        if (permission == null) {
            throw new BizException("无权访问，请联系【kaijiazhang，oliverychen】配置【行业数据关注人】角色权限");
        }
        // 不是全部战区不允许访问
        if (BooleanUtils.isNotTrue(permission.getIsAllWarZone())){
            throw new BizException("用户权限不足，请联系【kaijiazhang，oliverychen】配置【全部战区】权限");
        }
        // 非全行业的做过滤
        if (BooleanUtils.isNotTrue(permission.getIsAllIndustry())){
            List<String> industry = permission.getIndustry();
            req.setAuthIndustryDept(industry); // 鉴权的行业
        }

        return true;
    }

    private boolean uploadAuthentication() {
        // 获取用户名
        String userNameWithSystem = LoginUtils.getUserNameWithSystem();
        // 判断是不是管理员
        Boolean adminUserOrNot = permissionService.checkIsAdmin(userNameWithSystem);
        // 是admin或者不需要鉴权直接跳过
        if (adminUserOrNot) {
            return true;
        }
        // 如果不是管理员 才需要判断是否有权限看数据
        UserPermissionDto permission = permissionService.getPermissionByUserAndRole(IndustryDemandAuthRoleEnum.PPL_COMD_INTERFACE.getCode(), userNameWithSystem);
        if (permission == null) {
            throw new BizException("无权访问，请联系配置【13周预测确认人】角色权限");
        }
        // 不是全部战区不允许访问
        if (BooleanUtils.isNotTrue(permission.getIsAllWarZone())){
            throw new BizException("用户权限不足，请联系【kaijiazhang，oliverychen】配置【全部战区】权限");
        }
        // 不是全部行业不允许访问
        if (BooleanUtils.isNotTrue(permission.getIsAllIndustry())){
            throw new BizException("用户权限不足，请联系【kaijiazhang，oliverychen】配置【全部行业】权限");
        }
        // 不是全部客户不允许访问
        if (BooleanUtils.isNotTrue(permission.getIsAllCustomer())){
            throw new BizException("用户权限不足，请联系【kaijiazhang，oliverychen】配置【全部客户】权限");
        }

        return true;
    }

    @Override
    public List<String> queryYearMonth() {
        return demandDBHelper.getRaw(String.class, "select distinct data_month from real_service_cost_report_prod_type order by data_month desc");
    }
}
