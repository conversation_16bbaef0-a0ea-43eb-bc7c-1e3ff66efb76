package cloud.demand.app.modules.real_service_cost_report.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Table("dws_crp_ppl_item_version_newest_cf")
@Data
public class PredictedCoreNumberDo {

    @Column(value = "year_month")
    private String yearMonth;

    @Column(value = "industry_dept")
    private String industryDept;

    @Column(value = "customer_short_name")
    private String customerShortName;

    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "core_num")
    private Integer coreNum;
}
