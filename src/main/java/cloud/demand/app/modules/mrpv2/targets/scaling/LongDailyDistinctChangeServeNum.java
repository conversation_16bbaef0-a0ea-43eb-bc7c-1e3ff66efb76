package cloud.demand.app.modules.mrpv2.targets.scaling;

import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;

import java.util.List;
import java.util.Map;

public class LongDailyDistinctChangeServeNum extends DailyDistinctChangeServeNum {

    public static String staticTargetName() {
        return "daily_distinct_long_change_serve_num";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }

}
