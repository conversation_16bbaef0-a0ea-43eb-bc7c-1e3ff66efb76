package cloud.demand.app.modules.mrpv2.entity;

// package a.b.c;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.Date;
import java.util.Map;
import java.util.Set;

@Data
@ToString
@Table("daily_mrp_v2_data_describe")
public class DailyMrpV2DataDescribeDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 是否为最新版本表，该字段用来判断是从哪个表写入，以及当查询时stat_time为空时默认查询的列 */
    @Column(value = "is_latest")
    private Integer isLatest;

    /** 对应表名，最开始的表为 daily_mrp_v2_data */
    @Column(value = "table_name")
    private String tableName;

    /** 该表的最大版本号，如果是最新版本表，这里为空 */
    @Column(value = "max_stat_time")
    private LocalDate maxStatTime;

    /** 所以版本记录，这里需要查is_latest为1的才是全的 */
    @Column(value = "stat_time_list", isJSON = true,insertValueScript = "[]")
    private Set<String> statTimeList;

    @Column(value = "year_month_list", isJSON = true,insertValueScript = "[]")
    private Set<String> yearMonthList;
    @Column(value = "year_q_list", isJSON = true,insertValueScript = "[]")
    private Set<String> yearQList;
    @Column(value = "has_gen_target_list", isJSON = true,insertValueScript = "[]")
    private Set<String> hasGenTargetList;

    /** 最后成功的版本时间 */
    @Column(value = "latest_success_stat_time")
    private LocalDate latestSuccessStatTime;

    @Column(value = "target_gen_time_map", isJSON = true,insertValueScript = "{}")
    private Map<String, Date> targetGenTimeMap;
}