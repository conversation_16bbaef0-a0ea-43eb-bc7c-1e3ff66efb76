package cloud.demand.app.modules.mrpv2.targets.forecast_rate;

import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;

/**
 * 532 新版核天准确率
 */
public class V2AvgForecastMatchRateCoreByDay extends AvgForecastMatchRate {

    public static String staticTargetName() {
        return "v2_avg_forecast_match_rate_core_by_day";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }

    @Override
    public String targetDisplay() {
        return "532新版";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "预测准确率-核天",
                new RspFieldHeader("", "预测指标", null));
    }
}
