package cloud.demand.app.modules.mrpv2.targets.forecast_rate;

import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.DynamicTargetTemplate;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.targets.ppl.ForecastCoreByDayNum;
import cloud.demand.app.modules.mrpv2.targets.scaling.BillCoreByDayNum;
import cloud.demand.app.modules.mrpv2.targets.scaling.ServeCoreByDayNum;
import cloud.demand.app.modules.mrpv2.web.QueryTableReq;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Lang;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.Map.Entry;

public class ForecastMatchRateCoreByDay extends DynamicTargetTemplate {

    public static String staticTargetName() {
        return "forecast_match_rate_core_by_day";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }

    @Override
    public String targetDisplay() {
        return "预测准确率-核天";
    }

    @Override
    public RspFieldHeader header()  {
        return new RspFieldHeader("", "预测指标", null);
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }

    @Override
    public List<TargetTemplate> complexNode() {
        return Lang.list(new LatestForecastMatchRateCoreByDay(),new V2AvgForecastMatchRateCoreByDay());
    }

    @Override
    public void dynamicFieldToData(Collection<Map> data, QueryTableReq req, Boolean reGroupToOrigin) {
        super.dynamicFieldToData(data, req, reGroupToOrigin);
        List<String> searchTimeList = req.getSearchTimeList();
//        String[] versions = new String[]{"latest", "bench", "avg", "v2_avg"};
        String[] versions = new String[]{"latest","v2_avg"};
        // 先按原来的透视字段分组
        List<String> originSelectFields = req.getOriginSelectFields();
        Map<String, List<Map>> dataMap = new HashMap<>();
        for (Map m : data) {
            StringBuilder keySB = new StringBuilder();
            for (String os : originSelectFields) {
                keySB.append(m.get(os));
                keySB.append("@");
            }
            String key;
            if (CollectionUtils.isEmpty(originSelectFields)) {
                key = "";
            } else {
                key = keySB.substring(0, keySB.length() - 1);
            }
            List<Map> list = dataMap.computeIfAbsent(key, k -> new ArrayList<>());
            list.add(m);
        }
        Map<String, BigDecimal> totalForecastNumMap = new HashMap<>();
        Map<String, BigDecimal> totalExecutedNumMap = new HashMap<>();
        // 先算总执行量和总预测量
        for (Entry<String, List<Map>> entry : dataMap.entrySet()) {
            for (String version : versions) {
                for (Map m : entry.getValue()) {
                    String bizType = (String) m.get("bizType");
                    if (StringUtils.isBlank(bizType) || bizType.equals(Constant.EMPTY_VALUE)) {
                        // 必须要透视到业务类别
                        continue;
                    }
                    for (String timeKey : searchTimeList) {
                        Object maxCoreByDayNum = m.get(version + "_" + ForecastCoreByDayNum.staticTargetName() + "_max@" + timeKey);
                        Object minCoreByDayNum = m.get(version + "_" + ForecastCoreByDayNum.staticTargetName() + "_min@" + timeKey);

                        BigDecimal forecastCoreByDayNumMax = toBigDecimal(maxCoreByDayNum);
                        BigDecimal forecastCoreByDayNumMin = toBigDecimal(minCoreByDayNum);

                        if (forecastCoreByDayNumMax == null) {
                            forecastCoreByDayNumMax = BigDecimal.ZERO;
                        }
                        if (forecastCoreByDayNumMax.compareTo(BigDecimal.ZERO) < 0) {
                            forecastCoreByDayNumMax = forecastCoreByDayNumMax.negate();
                        }
                        if (forecastCoreByDayNumMin == null) {
                            forecastCoreByDayNumMin = BigDecimal.ZERO;
                        }
                        if (forecastCoreByDayNumMin.compareTo(BigDecimal.ZERO) < 0) {
                            forecastCoreByDayNumMin = forecastCoreByDayNumMin.negate();
                        }

                        String staticTargetNamePrefix = "month_";
                        if (req.getBillingScaleDistinct()) {
                            staticTargetNamePrefix += "distinct_";
                        } else {
                            staticTargetNamePrefix += "not_distinct_";
                        }

                        Object value;
                        if ("外部行业".equals(bizType)) {
                            value = m.get(staticTargetNamePrefix + BillCoreByDayNum.staticTargetName() + "@" + timeKey);
                        } else if ("内领业务".equals(bizType)) {
                            value = m.get(staticTargetNamePrefix + ServeCoreByDayNum.staticTargetName() + "@" + timeKey);
                        } else {
                            continue;
                        }
                        BigDecimal executedNum = toBigDecimal(value);
                        if (executedNum == null) {
                            executedNum = BigDecimal.ZERO;
                        }
                        if (executedNum.compareTo(BigDecimal.ZERO) < 0) {
                            executedNum = executedNum.negate();
                        }

                        String key = entry.getKey() + "@"
                                + version + "@" + timeKey;
                        BigDecimal totalForecastNum = totalForecastNumMap.getOrDefault(key,
                                BigDecimal.ZERO);
                        totalForecastNum = totalForecastNum.add(forecastCoreByDayNumMax).add(forecastCoreByDayNumMin);
                        totalForecastNumMap.put(key, totalForecastNum);

                        BigDecimal totalExecutedNum = totalExecutedNumMap.getOrDefault(key,
                                BigDecimal.ZERO);
                        totalExecutedNum = totalExecutedNum.add(executedNum);
                        totalExecutedNumMap.put(key, totalExecutedNum);
                    }
                }
            }
        }

        for (Entry<String, List<Map>> entry : dataMap.entrySet()) {
            for (String version : versions) {
                for (Map m : entry.getValue()) {
                    String bizType = (String) m.get("bizType");
                    if (StringUtils.isBlank(bizType) || bizType.equals(Constant.EMPTY_VALUE)) {
                        // 必须要透视到业务类别
                        continue;
                    }

                    // 不算总计
                    int timeListLength = searchTimeList.size() - 1;
                    BigDecimal timeListRate = BigDecimal.ONE.divide(BigDecimal.valueOf(timeListLength), 8, BigDecimal.ROUND_HALF_UP);
                    BigDecimal totalForecastMatchRate = BigDecimal.ZERO;

                    for (String timeKey : searchTimeList) {
                        if (timeKey.equals("总计")) {
                            continue;
                        }

                        Object maxCoreByDayNum = m.get(version + "_" + ForecastCoreByDayNum.staticTargetName() + "_max@" + timeKey);
                        Object minCoreByDayNum = m.get(version + "_" + ForecastCoreByDayNum.staticTargetName() + "_min@" + timeKey);

                        BigDecimal forecastCoreByDayNumMax = toBigDecimal(maxCoreByDayNum);
                        BigDecimal forecastCoreByDayNumMin = toBigDecimal(minCoreByDayNum);
                        Boolean negate = null;

                        if (forecastCoreByDayNumMax == null) {
                            forecastCoreByDayNumMax = BigDecimal.ZERO;
                        }
                        if (forecastCoreByDayNumMax.compareTo(BigDecimal.ZERO) < 0) {
                            forecastCoreByDayNumMax = forecastCoreByDayNumMax.negate();
                            negate = true;
                        } else if (forecastCoreByDayNumMax.compareTo(BigDecimal.ZERO) > 0) {
                            negate = false;
                        }

                        if (forecastCoreByDayNumMin == null) {
                            forecastCoreByDayNumMin = BigDecimal.ZERO;
                        }
                        if (forecastCoreByDayNumMin.compareTo(BigDecimal.ZERO) < 0) {
                            forecastCoreByDayNumMin = forecastCoreByDayNumMin.negate();
                            negate = true;
                        } else if (forecastCoreByDayNumMin.compareTo(BigDecimal.ZERO) > 0 && negate == null) {
                            negate = false;
                        }

                        String staticTargetNamePrefix = "month_";
                        if (req.getBillingScaleDistinct()) {
                            staticTargetNamePrefix += "distinct_";
                        } else {
                            staticTargetNamePrefix += "not_distinct_";
                        }

                        Object value;
                        if ("外部行业".equals(bizType)) {
                            value = m.get(staticTargetNamePrefix + BillCoreByDayNum.staticTargetName() + "@" + timeKey);
                        } else if ("内领业务".equals(bizType)) {
                            value = m.get(staticTargetNamePrefix + ServeCoreByDayNum.staticTargetName() + "@" + timeKey);
                        } else {
                            continue;
                        }
                        BigDecimal executedNum = toBigDecimal(value);
                        if (executedNum == null) {
                            executedNum = BigDecimal.ZERO;
                        }
                        // 本行的准确率
                        BigDecimal matchRate = null;
                        if (executedNum.compareTo(BigDecimal.ZERO) < 0) {
                            executedNum = executedNum.negate();
                            // 如果上面是正数
                            if (negate != null && !negate) {
                                matchRate = BigDecimal.ZERO;
                            }
                        } else if (executedNum.compareTo(BigDecimal.ZERO) > 0) {
                            // 如果上面是负数
                            if (negate != null && negate) {
                                matchRate = BigDecimal.ZERO;
                            }
                        }

                        if (matchRate == null) {
                            if (executedNum.compareTo(BigDecimal.ZERO) == 0) {
                                if (forecastCoreByDayNumMin.compareTo(BigDecimal.ZERO) == 0) {
                                    matchRate = BigDecimal.ONE;
                                } else {
                                    matchRate = BigDecimal.ZERO;
                                }
                            } else if (forecastCoreByDayNumMax.compareTo(executedNum) >= 0 && forecastCoreByDayNumMin.compareTo(executedNum) <= 0) {
                                matchRate = BigDecimal.ONE;
                            } else if (forecastCoreByDayNumMin.compareTo(executedNum) > 0 && forecastCoreByDayNumMin.compareTo(BigDecimal.ZERO) != 0) {
                                matchRate = executedNum.divide(forecastCoreByDayNumMin, 8, RoundingMode.HALF_UP);
                            } else if (forecastCoreByDayNumMax.compareTo(executedNum) < 0 && forecastCoreByDayNumMax.compareTo(BigDecimal.ZERO) != 0) {
                                matchRate = forecastCoreByDayNumMax.divide(executedNum, 8, RoundingMode.HALF_UP);
                            } else {
                                matchRate = BigDecimal.ZERO;
                            }
                        }

                        if (reGroupToOrigin) {
                            // 本行的权重
                            BigDecimal numRate;
                            BigDecimal totalForecastNum = totalForecastNumMap.getOrDefault(entry.getKey() + "@"
                                            + version + "@" + timeKey,
                                    BigDecimal.ZERO);
                            BigDecimal totalExecutedNum = totalExecutedNumMap.getOrDefault(entry.getKey() + "@"
                                            + version + "@" + timeKey,
                                    BigDecimal.ZERO);
                            BigDecimal forecastNum = forecastCoreByDayNumMax.add(forecastCoreByDayNumMin);

                            if (req.getRateSumFunction().equals("执行+需求预测量")) {
                                BigDecimal totalNum = totalForecastNum.add(totalExecutedNum);
                                if (totalNum.compareTo(BigDecimal.ZERO) == 0) {
                                    numRate = BigDecimal.ZERO;
                                } else {
                                    BigDecimal num = forecastNum.add(executedNum);
                                    numRate = num.divide(totalNum, 4, RoundingMode.HALF_UP);
                                }
                            } else if (req.getRateSumFunction().equals("执行量")) {
                                if (totalExecutedNum.compareTo(BigDecimal.ZERO) == 0) {
                                    numRate = BigDecimal.ZERO;
                                } else {
                                    numRate = executedNum.divide(totalExecutedNum, 8, RoundingMode.HALF_UP);
                                }
                            } else {
                                if (totalForecastNum.compareTo(BigDecimal.ZERO) == 0) {
                                    numRate = BigDecimal.ZERO;
                                } else {
                                    numRate = forecastNum.divide(totalForecastNum, 8, RoundingMode.HALF_UP);
                                }
                            }
                            BigDecimal partRate = numRate.multiply(matchRate);
                            totalForecastMatchRate = totalForecastMatchRate.add(partRate.multiply(timeListRate));
                            m.put(version + "_" + staticTargetName() + "@" + timeKey, partRate);
                        } else {
                            totalForecastMatchRate = totalForecastMatchRate.add(matchRate.multiply(timeListRate));
                            m.put(version + "_" + staticTargetName() + "@" + timeKey,
                                    String.format("%.2f%%", Constant.b100.multiply(matchRate)));
                        }
                    }
                    if (reGroupToOrigin) {
                        m.put(version + "_" + staticTargetName() + "@总计", totalForecastMatchRate);
                    } else {
                        m.put(version + "_" + staticTargetName() + "@总计",
                                String.format("%.2f%%", Constant.b100.multiply(totalForecastMatchRate)));
                    }
                }
            }
        }

    }
}
