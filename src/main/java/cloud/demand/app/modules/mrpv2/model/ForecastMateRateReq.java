package cloud.demand.app.modules.mrpv2.model;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.enums.GenerationEnum;
import cloud.demand.app.modules.mrpv2.enums.VersionEnum;
import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.mrpv2.web.QueryTableReq;
import cloud.demand.app.modules.mrpv3.dto.req.MrpV3ReportReq;
import cloud.demand.app.modules.mrpv3.enums.MrpV3DimFieldEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewFittingPlotCommonReq;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cn.hutool.core.collection.CollectionUtil;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.CollectionUtils;

import java.time.YearMonth;
import java.util.*;

/** 准确率查询入参 */
@Data
public class ForecastMateRateReq {

    /** 版本列表 {@link VersionEnum} */
    private List<String> version;

    /** 是否有境内外维度 */
    private Boolean hasCustomhouseTitle;

    /** 是否有新旧机型维度 */
    private Boolean hasGenerationInstanceType;

    /** 起始年月 */
    private String startYearMonth;

    /** 结束年月 */
    private String endYearMonth;

    /** 是否有地域维度 */
    private Boolean hasRegionNameDim;

    /** 是否有实例类型维度 */
    private Boolean hasInstanceTypeDim;

    /** 地域类型集合 */
    private List<String> regionNames;

    /** 实例类型集合 */
    private List<String> instanceTypes;

    /** 参数转换 */
    @SneakyThrows
    public static Map<String,QueryRateViewFittingPlotCommonReq> transForm(ForecastMateRateReq req){
        Map<String,QueryRateViewFittingPlotCommonReq> ret = new HashMap<>();
        for (String vs : req.getVersion()) {
            QueryRateViewFittingPlotCommonReq item = new QueryRateViewFittingPlotCommonReq();
            item.setStartYearMonth(YearMonth.parse(req.getStartYearMonth()));
            item.setEndYearMonth(YearMonth.parse(req.getEndYearMonth()));
            item.setRegionNames(req.getRegionNames());
            item.setGinsFamilies(req.getInstanceTypes());
            VersionEnum versionNum = VersionEnum.getByName(vs);
            if (versionNum == null){
                throw new Exception(String.format("版本枚举找不到：【%s】", vs));
            }
            item.setPredictIndex(versionNum.getRateEnum().getCode());
            item.setPlotType(getPlotType(req));
            ret.put(vs,item);
        }
        return ret;
    }


    public static QueryRateViewFittingPlotCommonReq.PlotType getPlotType(ForecastMateRateReq req){
        boolean regionNameDim = BooleanUtils.isTrue(req.getHasRegionNameDim());
        boolean instanceTypeDim = BooleanUtils.isTrue(req.getHasInstanceTypeDim());
        if (!regionNameDim && !instanceTypeDim){
            return QueryRateViewFittingPlotCommonReq.PlotType.ACCURACY_TREND;
        }else if (regionNameDim && instanceTypeDim){
            return QueryRateViewFittingPlotCommonReq.PlotType.REGION_MODEL_RATIO;
        }else if (regionNameDim){
            return QueryRateViewFittingPlotCommonReq.PlotType.REGION_RATIO;
        }else {
            return QueryRateViewFittingPlotCommonReq.PlotType.MODEL_RATIO;
        }
    }

    public static ForecastMateRateReq transForm(QueryTableReq req,String customhouseTitle,String generationInstanceType){
        ForecastMateRateReq rateReq = new ForecastMateRateReq();
        MrpV2DictService dict = SpringUtil.getBean(MrpV2DictService.class);
        // 实例类型
        rateReq.setInstanceTypes(ObjectUtils.defaultIfNull(req.getInstanceTypes(),new ArrayList<>()));
        List<String> ignoreInstanceTypes = ObjectUtils.defaultIfNull(req.getIgnoreInstanceTypes(),new ArrayList<>());
        List<String> instanceTypes = rateReq.getInstanceTypes();
        if (generationInstanceType != null){
            List<String> newGenerationInstanceType = dict.getNewGenerationInstanceType();
            if (Objects.equals(generationInstanceType, GenerationEnum.NEW.getName())){
                if (ListUtils.isEmpty(instanceTypes)){
                    instanceTypes = newGenerationInstanceType;
                }else {
                    instanceTypes = (List<String>) CollectionUtil.intersection(instanceTypes,newGenerationInstanceType);
                    if (ListUtils.isEmpty(instanceTypes)){
                        instanceTypes = new ArrayList<>();
                        instanceTypes.add("无数据");
                    }
                }
            }else {
                ignoreInstanceTypes.addAll(newGenerationInstanceType);
            }
        }
        // 剔除忽略的实例类型
        if (ListUtils.isNotEmpty(ignoreInstanceTypes)){

            // 为空则获取所有实例类型
            if (ListUtils.isEmpty(instanceTypes)){
                instanceTypes = dict.getAllInstanceType();
                rateReq.setInstanceTypes(instanceTypes);
            }
            for (String ignoreInstanceType : ignoreInstanceTypes) {
                instanceTypes.remove(ignoreInstanceType);
            }
        }

        rateReq.setInstanceTypes(instanceTypes);

        // 地域
        List<String> originRegionName = req.getRegionNames();

        List<String> regionNames;
        List<String> country = req.getCountry();
        if (req.getChoose() != null && req.getChoose().getRegionType() != null){
            country = ListUtils.newArrayList(req.getChoose().getRegionType());
        }
        // 境内外处理
        country = customhouseTitle == null ? country : ListUtils.newArrayList(customhouseTitle);
        List<String> customerRegionNames = null;
        // 如果只有境内或者境外，这里特殊处理一下
        if (ListUtils.isNotEmpty(country)){
            Set<String> collect = new HashSet<>(country);
            if (collect.size() == 1){
                DictService bean = SpringUtil.getBean(DictService.class);
                String next = collect.iterator().next();
                customerRegionNames = bean.getRegionNameByCustomhouseTitle(next);
            }
        }
        // 合并
        if (ListUtils.isEmpty(originRegionName)){
            regionNames = customerRegionNames;
        }else if (ListUtils.isEmpty(customerRegionNames)){
            regionNames = originRegionName;
        }else {
            regionNames = (List<String>) CollectionUtil.intersection(originRegionName,customerRegionNames);
        }
        // 如果境内外筛选和地域冲突，这设置 regionName 为空值
        if (ListUtils.isEmpty(regionNames) && ListUtils.isNotEmpty(originRegionName)){
            regionNames = new ArrayList<>();
            regionNames.add(Constant.EMPTY_VALUE);
        }

        rateReq.setRegionNames(ObjectUtils.defaultIfNull(regionNames,new ArrayList<>()));
        // 版本
        List<String> versionName = VersionEnum.desc2Name(req.getVersion());

        if (CollectionUtils.isEmpty(versionName)){
            versionName = VersionEnum.allName();
        }

        rateReq.setVersion(versionName);
        // 起始年月
        rateReq.setStartYearMonth(req.getTimePeriod().getStart());
        // 结束年月
        rateReq.setEndYearMonth(req.getTimePeriod().getEnd());

        // 是否有实例类型维度
        rateReq.setHasInstanceTypeDim(req.getOriginSelectFields().contains("ginsFamily"));

        // 是否有地域维度
        rateReq.setHasRegionNameDim(req.getOriginSelectFields().contains("regionName"));

        return rateReq;
    }

    /**
     * 转换成预测率请求
     * @param req 行业数据看板v3请求
     * @param version 版本集合
     * @param customhouseTitle 指定境内外
     * @param generationInstanceType 指定新旧机型
     * @return
     */
    public static ForecastMateRateReq transForm(MrpV3ReportReq req,List<String> version,String customhouseTitle,String generationInstanceType){
        ForecastMateRateReq rateReq = new ForecastMateRateReq();
        MrpV2DictService dict = SpringUtil.getBean(MrpV2DictService.class);
        // 合并实例类型
        List<String> unInstanceType = req.getUnInstanceType();
        if (ListUtils.isNotEmpty(unInstanceType)){
            List<String> instanceType = new ArrayList<>();

            Map<String, List<String>> unInstanceTypeMap = dict.getUnInstanceTypeMap();
            for (String type : unInstanceType) {
                List<String> strings = unInstanceTypeMap.get(type);
                if (ListUtils.isNotEmpty(strings)){
                    instanceType.addAll(strings);
                }
            }
            unInstanceType = instanceType;
        }
        // 实例类型
        List<String> instanceType = req.getInstanceType();
        if (ListUtils.isNotEmpty(unInstanceType) && ListUtils.isNotEmpty(instanceType)){
            instanceType = (List<String>) CollectionUtil.intersection(unInstanceType,instanceType);
            if (ListUtils.isEmpty(instanceType)){
                instanceType = new ArrayList<>();
                instanceType.add("无数据");
            }
        }else if (ListUtils.isNotEmpty(unInstanceType)){
            instanceType = unInstanceType;
        }

        if (SoeCommonUtils.isNotBlank(generationInstanceType)){
            List<String> newGenerationInstanceType = dict.getNewGenerationInstanceType();
            if (Objects.equals(generationInstanceType, GenerationEnum.NEW.getName())){
                if (ListUtils.isEmpty(instanceType)){
                    instanceType = newGenerationInstanceType;
                }else {
                    instanceType = (List<String>) CollectionUtil.intersection(instanceType,newGenerationInstanceType);
                    if (ListUtils.isEmpty(instanceType)){
                        instanceType = new ArrayList<>();
                        instanceType.add("无数据");
                    }
                }
            }else {
                List<String> allInstanceType = dict.getAllInstanceType();
                Set<String> removeLs = new HashSet<>(newGenerationInstanceType);
                List<String> ls = allInstanceType.stream().filter(item->!removeLs.contains(item)).collect(Collectors.toList());
                if (ListUtils.isEmpty(instanceType)){
                    instanceType = ls;
                }else {
                    instanceType = (List<String>) CollectionUtil.intersection(instanceType,ls);
                    if (ListUtils.isEmpty(instanceType)){
                        instanceType = new ArrayList<>();
                        instanceType.add("无数据");
                    }
                }
            }
        }


        rateReq.setInstanceTypes(instanceType);
        // 地域
        List<String> originRegionName = req.getRegionName();
        List<String> regionNames;
        // 境内外处理
        List<String> country = SoeCommonUtils.isNotBlank(customhouseTitle) ? ListUtils.newArrayList(customhouseTitle) : req.getCustomhouseTitle();
        List<String> customerRegionNames = null;
        // 如果只有境内或者境外，这里特殊处理一下
        if (ListUtils.isNotEmpty(country)){
            Set<String> collect = new HashSet<>(country);
            if (collect.size() == 1){
                DictService bean = SpringUtil.getBean(DictService.class);
                String next = collect.iterator().next();
                customerRegionNames = bean.getRegionNameByCustomhouseTitle(next);
            }
        }
        // 合并
        if (ListUtils.isEmpty(originRegionName)){
            regionNames = customerRegionNames;
        }else if (ListUtils.isEmpty(customerRegionNames)){
            regionNames = originRegionName;
        }else {
            regionNames = (List<String>) CollectionUtil.intersection(originRegionName,customerRegionNames);
        }
        // 如果境内外筛选和地域冲突，这设置 regionName 为空值
        if (ListUtils.isEmpty(regionNames) && ListUtils.isNotEmpty(originRegionName)){
            regionNames = new ArrayList<>();
            regionNames.add(Constant.EMPTY_VALUE);
        }
        rateReq.setRegionNames(ObjectUtils.defaultIfNull(regionNames,new ArrayList<>()));
        rateReq.setStartYearMonth(req.getStartYearMonth());
        rateReq.setEndYearMonth(req.getEndYearMonth());

        // 是否有实例类型维度
        List<String> dims = req.getDims();
        if (ListUtils.isNotEmpty(dims)){
            rateReq.setHasInstanceTypeDim(dims.contains(MrpV3DimFieldEnum.instanceType.name()));
            // 是否有地域维度
            rateReq.setHasRegionNameDim(dims.contains(MrpV3DimFieldEnum.regionName.name()));
        }

        rateReq.setVersion(version);

        return rateReq;
    }
}
