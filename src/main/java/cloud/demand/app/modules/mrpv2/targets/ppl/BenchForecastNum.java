package cloud.demand.app.modules.mrpv2.targets.ppl;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.modules.industry_report.service.util.SQLS;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.entity.OdsTxyAppidInfoCfShortDO;
import cloud.demand.app.modules.mrpv2.entity.TailPplForecastDO;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.utils.MyMapUtils;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionStatusEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class BenchForecastNum extends TargetTemplate {

    @Override
    public String targetName() {
        return "bench_forecast_num";
    }

    @Override
    public String targetDisplay() {
        return "基准版";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "预测量",
                new RspFieldHeader("", "需求计划", null));
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        String yearMonthStart = (String) shares.get("year_month_start");
        String sql = loadSql();

        List<PplVersionDO> pplVersionDOS = loadAllPplVersionDOs();
        shares.put("pplVersionDOS", pplVersionDOS);
        List<YearMonth> allYearMonths = loadAllYearMonth(pplVersionDOS);
        shares.put("allYearMonths", allYearMonths);
        Map<String, List<String>> rawBenchVersionMap = monthAddIncreaseMonthVersion(-3, pplVersionDOS, allYearMonths);
        shares.put("m-3BenchVersionMap", rawBenchVersionMap);
        if (CollectionUtils.isEmpty(rawBenchVersionMap)) {
            // 如果无版本数据，返回空
            return null;
        }
        Map benchVersionMap = new HashMap<>();
        for (Entry<String, List<String>> entry : rawBenchVersionMap.entrySet()) {
            benchVersionMap.put(entry.getKey(), entry.getValue().get(0));
        }
        WhereSQL whereSQL = new WhereSQL();
        versionSQL(whereSQL, benchVersionMap, allYearMonths);
        sql = sql.replaceAll("\\$\\{FILTER}", SQLS.whereToAnd(whereSQL.getSQL()));
        Object[] one = whereSQL.getParams();
        // SQL片段替换了5次，所以params要重复4次，手动加4次
        whereSQL.and("", one);
        whereSQL.and("", one);
        whereSQL.and("", one);
        whereSQL.and("", one);

        whereSQL.and("", yearMonthStart);
        MyMapUtils.putTargetGenTime(shares, "基准版预测量");

        List<MrpV2DataDO> result = demandDBHelper.getRaw(MrpV2DataDO.class, sql, whereSQL.getParams());

        Set<String> nameSet = result.stream().map(MrpV2DataDO::getCustomerShortName)
                .filter(s -> !StringUtils.isBlank(s) && !s.equals(Constant.EMPTY_VALUE))
                .collect(Collectors.toSet());
        List<List<String>> names = ListUtils.groupByNum(nameSet, 5000);
        Map<String, String> nameMap = new HashMap<>();
        Map<String, String> shortNameMap = new HashMap<>();
        for (List<String> name : names) {
            List<OdsTxyAppidInfoCfShortDO> infoDOS = DBList.ckcldStdCrpDBHelper.getAll(OdsTxyAppidInfoCfShortDO.class,
                    "where customer_name in (?) or customer_short_name in (?)", name, name);
            for (OdsTxyAppidInfoCfShortDO ddo : infoDOS) {
                if (!StringUtils.isBlank(ddo.getCustomerName()) && !Constant.EMPTY_VALUE.equals(ddo.getCustomerName())) {
                    nameMap.put(ddo.getCustomerName(), ddo.getUin() == -1 ? "0" : ddo.getUin().toString());
                }
                if (!StringUtils.isBlank(ddo.getCustomerShortName()) && !Constant.EMPTY_VALUE.equals(ddo.getCustomerShortName())) {
                    shortNameMap.put(ddo.getCustomerShortName(), ddo.getUin() == -1 ? "0" : ddo.getUin().toString());
                }
            }
        }

        for (MrpV2DataDO r : result) {
            if (StringUtils.isBlank(r.getUin()) || r.getUin().equals(Constant.EMPTY_VALUE) || "0".equals(r.getUin())) {
                String uin = nameMap.get(r.getCustomerShortName());
                if (uin == null) {
                    uin = shortNameMap.getOrDefault(r.getCustomerShortName(), "0");
                }
                r.setUin(uin);
            }
        }

        List<TailPplForecastDO> m3 = (List<TailPplForecastDO>) shares.get("tail-m-3");
        if (!CollectionUtils.isEmpty(m3)) {
            result.addAll(TailPplForecastDO.copy(m3));
        }
        return result;
    }

    private List<PplVersionDO> loadAllPplVersionDOs() {
        return demandDBHelper.getAll(PplVersionDO.class,
                "where status = ?", PplVersionStatusEnum.DONE.getCode());
    }

    public static List<YearMonth> loadAllYearMonth(List<PplVersionDO> pplVersionDOS) {
        List<YearMonth> yearMonths = new ArrayList<>();
        Set<String> yearMonthDateStr = new HashSet<>();
        for (PplVersionDO pplVersionDO : pplVersionDOS) {
            List<YearMonth> t = DateUtils.listYearMonth(pplVersionDO.getDemandBeginYear(), pplVersionDO.getDemandBeginMonth(),
                    pplVersionDO.getDemandEndYear(), pplVersionDO.getDemandEndMonth());
            for (YearMonth tt : t) {
                if (!yearMonthDateStr.contains(tt.toDateStr())) {
                    yearMonths.add(tt);
                    yearMonthDateStr.add(tt.toDateStr());
                }
            }
        }
        ListUtils.sortAscNullLast(yearMonths, YearMonth::toDateStr);
        return yearMonths;
    }

    public static Map<String, List<String>> monthAddIncreaseMonthVersion(int addMonth, List<PplVersionDO> pplVersionDOS,
            List<YearMonth> yearMonths) {
        // 找到每个月份的+a月
        Map<String, YearMonth> ym2BenchMap = new HashMap<>();
        for (YearMonth yearMonth : yearMonths) {
            ym2BenchMap.put(yearMonth.toDateStr(), YearMonth.addMonth(yearMonth, addMonth));
        }
        // 知道每个月的+a月后，就可以匹配到+a月的版本列表
        Map<String, List<String>> bench2VersionMap = new HashMap<>();
        Pattern p;
        for (Entry<String, YearMonth> entry : ym2BenchMap.entrySet()) {
            YearMonth benchYearMonth = entry.getValue();
            String matchYm = entry.getKey();
            String dateStr = benchYearMonth.toDateStr();
            p = Pattern.compile(
                    "((" + benchYearMonth.toNoDashDateStr() + ")+|("
                            + dateStr + ")+|(" + benchYearMonth.getYear() +
                            "-" + benchYearMonth.getMonth() + ")+|(" + benchYearMonth.getYear() +
                            "" + benchYearMonth.getMonth() + ")+)");
            for (PplVersionDO pplVersionDO : pplVersionDOS) {
                Matcher m = p.matcher(pplVersionDO.getVersionCode());
                if (m.find()) {
                    // 除了正则匹配，还要月份范围匹配
                    List<YearMonth> versionMonthRange = DateUtils.listYearMonth(
                            pplVersionDO.getDemandBeginYear(), pplVersionDO.getDemandBeginMonth(),
                            pplVersionDO.getDemandEndYear(), pplVersionDO.getDemandEndMonth());
                    for (YearMonth ym : versionMonthRange) {
                        if (matchYm.equals(ym.toDateStr())) {
                            List<String> list = bench2VersionMap.computeIfAbsent(dateStr, k -> new ArrayList<>());
                            list.add(pplVersionDO.getVersionCode());
                            break;
                        }
                    }
                }
            }
        }
        Map<String, List<String>> result = new HashMap<>();
        for (String ym : ym2BenchMap.keySet()) {
            String benchYm = ym2BenchMap.get(ym).toDateStr();
            List<String> versionCodeList = bench2VersionMap.get(benchYm);
            if (!CollectionUtils.isEmpty(versionCodeList)) {
                // 升序，版本号从早到晚
                ListUtils.sortAscNullLast(versionCodeList, Function.identity());
                result.put(ym, versionCodeList);
            }
        }
        return result;
    }

    public static WhereSQL versionSQL(WhereSQL whereSQL, Map<String, Object> yearMonthVersion, List<YearMonth> yearMonths) {
        Set<String> yearMonthStrSet = yearMonths.stream().map(YearMonth::toDateStr).collect(Collectors.toSet());
        if (yearMonthVersion == null || yearMonthVersion.isEmpty()) {
            List<Object[]> yearMonthArgs = new ArrayList<>();
            for (YearMonth yearMonth : yearMonths) {
                if (!yearMonthStrSet.contains(yearMonth.toDateStr())) {
                    continue;
                }
                yearMonthArgs.add(new Object[]{yearMonth.getYear(), yearMonth.getMonth()});
            }
            whereSQL.and(" (year(i.begin_buy_date), month(i.begin_buy_date)) in (?) ", yearMonthArgs);
            log.info(" (year(i.begin_buy_date), month(i.begin_buy_date)) in (?) : {}", JSON.toJson(yearMonthArgs));
        } else {
            List<Object[]> params = new ArrayList<>();
            for (Entry<String, Object> e : yearMonthVersion.entrySet()) {
                if (!yearMonthStrSet.contains(e.getKey())) {
                    continue;
                }
                String[] a = e.getKey().split("-");
                Object value = e.getValue();
                if (value instanceof String) {
                    params.add(new Object[]{Integer.parseInt(a[0]), Integer.parseInt(a[1]),
                            e.getValue()});
                } else if (value instanceof List) {
                    List list = (List) value;
                    for (Object o : list) {
                        // o 实际上是版本号
                        params.add(new Object[]{Integer.parseInt(a[0]), Integer.parseInt(a[1]),
                                o});
                    }
                }
            }
            whereSQL.and(" (year(i.begin_buy_date), month(i.begin_buy_date), g.version_code) in (?) ", params);
            log.info(" (year(i.begin_buy_date), month(i.begin_buy_date), g.version_code) in (?) : {}", JSON.toJson(params));
        }
        return whereSQL;
    }
}
