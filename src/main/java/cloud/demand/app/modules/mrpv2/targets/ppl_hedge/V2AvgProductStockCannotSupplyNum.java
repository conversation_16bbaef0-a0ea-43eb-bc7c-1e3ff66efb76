package cloud.demand.app.modules.mrpv2.targets.ppl_hedge;

import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import java.util.List;
import java.util.Map;

public class V2AvgProductStockCannotSupplyNum extends AvgProductStockCannotSupplyNum {

    @Override
    public String targetName() {
        return "v2_avg_product_stock_cannot_supply_num";
    }

    @Override
    public String targetDisplay() {
        return "532新版";
    }
}
