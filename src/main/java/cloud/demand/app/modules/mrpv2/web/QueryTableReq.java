package cloud.demand.app.modules.mrpv2.web;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.DynamicProperties;
import cloud.demand.app.modules.mrpv2.enums.ForecastStatus;
import cloud.demand.app.modules.mrpv2.enums.OriginIndexEnum;
import cloud.demand.app.modules.mrpv2.enums.ProjectTypeEnum;
import cloud.demand.app.modules.mrpv2.model.MrpV2Cache;
import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.mrpv2.targets.forecast_rate.ForecastMatchRate;
import cloud.demand.app.modules.mrpv2.targets.forecast_rate.ForecastMatchRateCoreByDay;
import cloud.demand.app.modules.mrpv2.targets.forecast_rate.origin.OriginForecastMatchRate;
import cloud.demand.app.modules.mrpv2.targets.forecast_rate.origin.OriginForecastMatchRateCoreByDay;
import cloud.demand.app.modules.mrpv2.targets.ppl.LongForecastNum;
import cloud.demand.app.modules.mrpv2.targets.ppl.origin.OriginLongForecastNum;
import cloud.demand.app.modules.mrpv2.targets.scaling.CurBillNum;
import cloud.demand.app.modules.mrpv2.targets.scaling.LongChangeBillNum;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.Pattern;
import java.time.LocalDate;
import java.util.*;
import yunti.boot.exception.BizException;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class QueryTableReq {

    /** 新客户分类 */
    private List<String> newCustomerType;

    private List<String> orderType;

    /** 项目类型 */
    private List<String> projectType;

    /** 销售通路 */
    private List<String> incomeSalesDesc;

    /** 子产品 */
    private List<String> productClass;

    /** 是否为新客户订单：这里会影响底层订单字段，true：order_type,false:old_order_type */
    private Boolean isNewOrderType;

    /**
     * 是否是中长尾最新算法，0：否，1：是，-1(默认0和-1)
     */
    private List<Integer> isNewCategory = Arrays.asList(0, -1);

    /**
     * 是否包含黑名单，0：原始，1：黑名单处理，-1：非黑名单
     */
    private List<Integer> isBlack = Arrays.asList(1, -1);

    @Pattern(regexp = "原始预测|云运管干预", message = "预测状态只能填写[原始预测]或[云运管干预]")
    private String forecastStatus;

    /**
     * 版本：最新版,基准版,532版,532新版
     */
    private List<String> version;
    // 查询的数据指标列表
    private List<String> queryTargets;

    /**
     * 是否为人工干预532新版核天准确率，默认为查七彩石配置
     */
    private Boolean isInterfered = DynamicProperties.defIsInterfered();

    /** 采购机型 */
    private Boolean isPurchaseInstanceType; // 是否为采购机型

    /** 新旧机型 */
    private Boolean isNewInstanceType; // 是否为新机型

    private Boolean isBigCustomer; // 是否为大客户

    private List<String> crpWarZoneName; // CRP战区

    private List<String> unCustomerShortName; // 通用客户简称

    /**
     * 缓存
     */
    private MrpV2Cache cache;
    // 筛选项
    private List<String> industryDepts;
    private List<String> country;
    private List<String> regionNames;
    private List<String> zoneNames;

    private List<String> ignoreZoneNames; // 剔除的可用区
    private List<String> instanceTypes;

    private List<String> ignoreInstanceTypes; // 剔除的机型
    private List<String> customerInsideOrOutsides;
    private List<String> demandTypes;

    /** 是否模糊客户简称，（模糊+不区分大小写），只有页面才会需要模糊查询，其他关联接口视情况选择 */
    private Boolean fuzzyCustomerShortName;

    private List<String> customerShortNames;

    /** 战区 */
    private List<String> warZoneName;

    /** 客户uin */
    private List<String> uins;

    /** 忽略 uins 集合 */
    private List<String> ignoreUins;

    /** 剔除客户简称 */
    private List<String> ignoreCustomerShortName;
    /** 剔除通用客户简称 */
    private List<String> ignoreUnCustomerShortName;

    private List<String> customerGroups;
    private List<String> bizTypes;
    private List<String> gpuCardTypes;
    private List<String> gpuTypes;
    private Boolean mainInstanceFamilyAndZone;

    /** 主力机型 */
    private Boolean mainInstanceFamily;

    /** 主力园区 */
    private Boolean mainZone;
    /**
     * 外部行业
     */
    private List<String> billAppRole;
    /**
     * 内部业务
     */
    private List<String> serviceAppleRole;
    @Pattern(regexp = "执行\\+需求预测量|执行量|需求预测量", message = "数据加权模式只能填写[执行+需求预测量]或[执行量]或[需求预测量]")
    private String rateSumFunction = "执行+需求预测量";
    @Pattern(regexp = "日切片|月均切片", message = "规模表模式只能填写[日切片]或[月均切片]")
    private String billingScaleMode = "月均切片";
    private Boolean billingScaleDistinct = true;
    @Pattern(regexp = "CVM|GPU|裸金属", message = "产品只能填写[CVM]或[GPU]")
    private String product = "CVM";
    private Boolean isRendering;
    private TimePeriod timePeriod;
    private List<String> searchTimeList;
    private List<String> targetsList;
    // 当前透视情况，非空
    //@NotEmpty(message = "透视字段不能为空")
    private List<String> selectFields = new ArrayList<>();

    /** 下一级查询维度 */
    private List<String> nextSelectFields = new ArrayList<>();

    private List<String> originSelectFields;
    // 分页情况
    private Page page = new Page();
    // 当前展开的情况
    private Choose choose = new Choose();
    // 数据日期
    private LocalDate statTime;
    // 数值排序列
    private String dataSortColName;
    // 数值排序列排序规则
    @Pattern(regexp = "asc|desc", message = "数值排序列排序规则只能为asc或desc")
    private String dataSortRule = "desc";
    private Boolean abTest = true;
    /**
     * 是否合并机型
     */
    private Boolean commonInstanceType = false;

    /** 采购机型下拉框，枚举：采购机型，非采购机型 */
    private List<String> purchaseInstanceType;

    public Boolean isPurchase(){
        if (ListUtils.isEmpty(purchaseInstanceType) || (purchaseInstanceType.contains("采购机型") && purchaseInstanceType.contains("非采购机型"))){
            return null;
        }
        return purchaseInstanceType.contains("采购机型");
    }

    /** 新旧下拉框，枚举：新机型，旧机型 */
    private List<String> generationInstanceType;

    public Boolean isNew(){
        if (ListUtils.isEmpty(generationInstanceType) || (generationInstanceType.contains("新机型") && generationInstanceType.contains("旧机型"))){
            return null;
        }
        return generationInstanceType.contains("新机型");
    }

    /** 主力机型下拉框，枚举：主力机型，非主力机型 */
    private List<String> mainInstanceTypeList;

    /** 主力园区下拉框，枚举：主力园区，非主力园区 */
    private List<String> mainZoneList;

    public Boolean isMainInstance(){
        if (ListUtils.isEmpty(mainInstanceTypeList) || (mainInstanceTypeList.contains("主力机型") && mainInstanceTypeList.contains("非主力机型"))){
            return null;
        }
        return mainInstanceTypeList.contains("主力机型");
    }

    public Boolean isMainZone(){
        if (ListUtils.isEmpty(mainZoneList) || (mainZoneList.contains("主力园区") && mainZoneList.contains("非主力园区"))){
            return null;
        }
        return mainZoneList.contains("主力园区");
    }


    public List<String> getOriginQueryTargets() {
        Set<String> ret = new LinkedHashSet<>();
        if (Objects.equals(forecastStatus, ForecastStatus.origin.getName()) && ListUtils.isNotEmpty(queryTargets)) {
            // 干预前后转换
            Map<String, String> map = OriginIndexEnum.current2Origin();
            List<String> temp = new ArrayList<>();
            for (String queryTarget : getNewCategoryTargets()) {
                temp.add(map.getOrDefault(queryTarget, queryTarget));
            }
            ret.addAll(temp);
        } else {
            ret.addAll(getNewCategoryTargets());
        }
        return new ArrayList<>(ret);
    }

    /**
     * 新版中长尾算法需要计费存量数据，以及从日规模和月规模表来的执行数据
     */
    public List<String> getNewCategoryTargets() {
        if (CollectionUtils.isEmpty(queryTargets)) {
            return queryTargets;
        }
        if (needForecastStockNum()) {
            List<String> ret = new ArrayList<>(queryTargets);
            if (needForecastStockNum()) {
                // 根据干预前后选择数据(用来计算准确率)
                if (Objects.equals(ForecastStatus.origin.getName(), forecastStatus)) {
                    ret.add(OriginLongForecastNum.staticTargetName());
                } else {
                    ret.add(LongForecastNum.staticTargetName());
                }
                ret.add(LongChangeBillNum.staticTargetName());
            }
            return ret;
        }
        return queryTargets;
    }

    // 新版中长尾需要计算该数据
    public boolean needCurBillNum() {
        return !CollectionUtils.isEmpty(isNewCategory) && isNewCategory.contains(1) && !queryTargets.contains(
                CurBillNum.staticTargetName());
    }

    // 包含弹性数据以及预测数据（新版中长尾），不需要判定原始，这里处理在原始之前 - 方法失效（目标场景是中长尾准确率计算，已经被弃用，所以返回 false）
    public boolean needForecastStockNum() {
        return false && !CollectionUtils.isEmpty(isNewCategory) && isNewCategory.contains(1) &&
                (queryTargets.contains(ForecastMatchRate.staticTargetName()) || queryTargets.contains(
                        OriginForecastMatchRate.staticTargetName()));
    }

    public boolean needForecastMatchRateCoreDay() {
        return BooleanUtils.isTrue(isInterfered)
                && choose != null
                && (Objects.equals(choose.getOrderType(), "头部客户") || Objects.equals(choose.getProjectType(), ProjectTypeEnum.KEY_PROJECT.getName()))
                && (queryTargets.contains(ForecastMatchRateCoreByDay.staticTargetName()) ||
                queryTargets.contains(OriginForecastMatchRateCoreByDay.staticTargetName()));
    }

    public boolean needLongScaleNum() {
        return !CollectionUtils.isEmpty(isNewCategory) && isNewCategory.contains(1);
    }

    public void fillWhereSQL(WhereSQL whereSQL, boolean demandTypeFilter) {

        if (ListUtils.isNotEmpty(orderType)){
            boolean isOldOrderType = BooleanUtils.isNotTrue(this.getIsNewOrderType());
            if (isOldOrderType){
                whereSQL.and("old_order_type in (?)", orderType);
            }else{
                whereSQL.and("order_type in (?)", orderType);
            }
        }

        if (!CollectionUtils.isEmpty(industryDepts)) {
            whereSQL.and("industry_dept in (?)", industryDepts);
        }
        if (!CollectionUtils.isEmpty(country)) {
            whereSQL.and("region_type in (?)", country);
        }
        if (!CollectionUtils.isEmpty(regionNames)) {
            whereSQL.and("region_name in (?)", regionNames);
        }
        if (!CollectionUtils.isEmpty(zoneNames)) {
            whereSQL.and("zone_name in (?)", zoneNames);
        }
        if (!CollectionUtils.isEmpty(ignoreZoneNames)) {
            whereSQL.and("zone_name not in (?)", ignoreZoneNames);
        }
        if (!CollectionUtils.isEmpty(instanceTypes)) {
            whereSQL.and("gins_family in (?)", instanceTypes);
        }
        if (!CollectionUtils.isEmpty(ignoreInstanceTypes)) {
            whereSQL.and("gins_family not in (?)", ignoreInstanceTypes);
        }

        if (!CollectionUtils.isEmpty(newCustomerType)) {
            whereSQL.and("new_customer_type in (?)", newCustomerType);
        }

        if (!CollectionUtils.isEmpty(projectType)) {
            whereSQL.and("project_type in (?)", projectType);
        }

        if (!CollectionUtils.isEmpty(productClass)) {
            whereSQL.and("product_class in (?)", productClass);
        }

        // 是否为大客户
        if (BooleanUtils.isTrue(isBigCustomer)){
            // 大客户集合
            List<String> bigCustomerShortName = SpringUtil.getBean(MrpV2DictService.class).getBigCustomerShortName();
            // 如果有才限制，避免没有配置采购机型，页面没有数据
            if (ListUtils.isNotEmpty(bigCustomerShortName)){
                whereSQL.and("customer_short_name in (?)",bigCustomerShortName);
            }
        }

        // CRP战区过滤
        if (ListUtils.isNotEmpty(crpWarZoneName)){
            whereSQL.and("crp_war_zone_name in (?)", crpWarZoneName);
        }

        // 通用客户简称过滤
        if (ListUtils.isNotEmpty(unCustomerShortName)){
            whereSQL.and("un_customer_short_name in (?)", unCustomerShortName);
        }

        if (!CollectionUtils.isEmpty(customerInsideOrOutsides)) {
            whereSQL.and("customer_inside_or_outside in (?)", customerInsideOrOutsides);
        }

        if (!CollectionUtils.isEmpty(customerShortNames)) {
            if (BooleanUtils.isTrue(fuzzyCustomerShortName)){
                // 模糊查询
                WhereSQL customerWhere = new WhereSQL();
                for (String customerShortName : customerShortNames) {
                    customerWhere.or("customer_short_name ilike ?",StringUtils.join("%",StringUtils.lowerCase(customerShortName),"%"));
                }
                whereSQL.and(customerWhere);
            }else {
                // 精准查询
                whereSQL.and("customer_short_name in (?)",
                        customerShortNames);
            }
        }

        if (!CollectionUtils.isEmpty(uins)){
            whereSQL.and("uin in (?)",
                    uins);
        }

        // 剔除 uins
        if (!CollectionUtils.isEmpty(ignoreUins)){
            if (ListUtils.isNotEmpty(uins)){
                throw new BizException("客户UIN 和 剔除客户UIN 不能同时过滤");
            }
            whereSQL.and("uin not in (?)",
                    ignoreUins);
        }

        // 战区过滤
        if (!CollectionUtils.isEmpty(warZoneName)){
            whereSQL.and("war_zone_name in (?)",warZoneName);
        }

        if (!CollectionUtils.isEmpty(customerGroups)) {
            whereSQL.and("customer_group in (?)", customerGroups);
        }
        if (!CollectionUtils.isEmpty(demandTypes) && demandTypeFilter) {
            // 这里查弹性的时候要包含新增&弹性的数据
            if (demandTypes.contains("弹性") && !demandTypes.contains("新增")) {
                whereSQL.and("(demand_type in (?) or (is_add_or_ela = 0 and demand_type != '退回'))", demandTypes);
            } else {
                whereSQL.and("demand_type in (?)", demandTypes);
            }
        }
        if (!CollectionUtils.isEmpty(isNewCategory)) {
            whereSQL.and("is_new_category in (?)", isNewCategory);
        }
        if (!CollectionUtils.isEmpty(bizTypes)) {
            whereSQL.and("biz_type in (?)", bizTypes);
        }
        if (!CollectionUtils.isEmpty(gpuCardTypes)) {
            whereSQL.and("gpu_card_type in (?)", gpuCardTypes);
        }
        if (!CollectionUtils.isEmpty(gpuTypes)) {
            whereSQL.and("gpu_type in (?)", gpuTypes);
        }
        if (!CollectionUtils.isEmpty(isBlack)) {
            whereSQL.and("is_black in (?)", isBlack);
            List<String> blackUinList = DynamicProperties.getBlackUinList();
            if (ListUtils.isNotEmpty(blackUinList) && isBlack.size() == 2 && isBlack.contains(1) && isBlack.contains(-1)){
                whereSQL.and("uin not in (?)", blackUinList);
            }
        }

        // appRole处理
        fillAppRole(whereSQL);

        if (this.isRendering != null) {
            if (this.isRendering) {
                whereSQL.and("gins_family like (?) or gins_family like (?)", "RS%", "RM%");
            } else {
                whereSQL.and("gins_family not like (?) and gins_family not like (?)", "RS%", "RM%");
            }
        }
    }

    /**
     * @param whereSQL appRole处理
     */
    private void fillAppRole(WhereSQL whereSQL) {
        WhereSQL temp = new WhereSQL();
        temp.or("app_role = ?", Constant.EMPTY_VALUE);
        if (ListUtils.isNotEmpty(billAppRole)) {
            temp.or("(app_role in (?) and biz_type = '外部行业')", billAppRole);
        } else {
            // 保底处理
            temp.or("(app_role in (?) and biz_type = '外部行业')", Arrays.asList(
                    "正常售卖", "CDH", "预扣包", "GOCP", "CDZ"
            ));
        }
        if (ListUtils.isNotEmpty(serviceAppleRole)) {
            temp.or("(app_role in (?) and biz_type = '内领业务')", serviceAppleRole);
        } else {
            // 保底处理
            temp.or("(app_role in (?) and biz_type = '内领业务')", Arrays.asList(
                    "CNAS",
                    "CSS",
                    "EKS",
                    "EMR",
                    "NON_CCDB_OTHER",
                    "USE_FOR_30",
                    "VPCGW",
                    "VPNGW",
                    "YUNTI",
                    "官网领用",
                    "算力"
            ));
        }
        whereSQL.and(temp);
    }

    public boolean needDemandTypeGroup() {
        // 不需要了，现在中长尾的逻辑和头部一致，不需要做特殊处理（之前计算中长尾准确率需要用不去重覆盖去重的，现在准确率不自己算了，所以都是 false）
        return false && !CollectionUtils.isEmpty(getIsNewCategory())
                && getIsNewCategory().contains(1)
                && getChoose() != null
                && Objects.equals(getChoose().getOrderType(), "中长尾");
    }

    /**
     * 是否需要中长尾预测准确率净增清洗
     */
    public boolean needCleanLongRate() {
        return !CollectionUtils.isEmpty(getIsNewCategory())
                && getIsNewCategory().contains(1)
                && getChoose() != null
                && Objects.equals(getChoose().getOrderType(), "中长尾")
                && isDiffAdd();
    }

    public boolean isLongRate() {
        return Objects.equals("CVM",getProduct())
                && !CollectionUtils.isEmpty(getIsNewCategory())
                && getIsNewCategory().contains(1)
                && getChoose() != null
                && Objects.equals(getChoose().getOrderType(), "中长尾")
                && (queryTargets.contains(ForecastMatchRate.staticTargetName()) || queryTargets.contains(
                OriginForecastMatchRate.staticTargetName()));
    }


    /**
     * 是否为净增
     */
    public boolean isDiffAdd() {
        // 如果按照需求类型分组则不是净增
        if (ListUtils.isNotEmpty(getSelectFields()) && getSelectFields().contains("demandType")) {
            return false;
        }
        // 指标不包含准确率的直接忽略
        if (!(queryTargets.contains(ForecastMatchRate.staticTargetName())
                || queryTargets.contains(OriginForecastMatchRate.staticTargetName()))) {
            return false;
        }

        List<String> dt = getDemandTypes();
        if (ListUtils.isEmpty(dt)) {
            return true;
        }
        // 需求类型包含退回及其其他视为净增
        return dt.contains("退回") && (dt.contains("新增") || dt.contains("弹性"));
    }

    /**
     * 默认填充空值
     */
    public void fillDemandType() {
        if (ListUtils.isNotEmpty(demandTypes) && !demandTypes.contains(Constant.EMPTY_VALUE)) {
            demandTypes.add(Constant.EMPTY_VALUE);
        }
    }

    /**
     * 字段默认值填充
     */
    public void fillFields() {
        fillDemandType();
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Page {

        private int pageNo = 1;
        private int pageSize = 100;

        public int toOffset() {
            return (pageNo - 1) * pageSize;
        }
    }

    @Data
    public static class Choose {

        /**
         * 订单类型<br/>Column: [order_type]
         */
        private String orderType;

        /**
         * 业务分类<br/>Column: [biz_type]
         */
        private String bizType;

        /**
         * 行业/产品(也叫行业/部门)<br/>Column: [industry_or_product]
         */
        private String industryOrProduct;

        /**
         * 境内外
         */
        private String regionType;

        /**
         * 地域<br/>Column: [region_name]
         */
        private String regionName;

        /**
         * 可用区<br/>Column: [zone_name]
         */
        private String zoneName;

        /**
         * 实例类型<br/>Column: [gins_family]
         */
        private String ginsFamily;
        private List<String> ginsFamilies = null;

        /**
         * 客户内外部属性<br/>Column: [customer_inside_or_outside]
         */
        private String customerInsideOrOutside;

        /**
         * 客户类型<br/>Column: [customer_type]
         */
        private String customerType;

        /**
         * 战区名<br/>Column: [war_zone_name]
         */
        private String warZoneName;

        /**
         * 客户集团<br/>Column: [customer_group]
         */
        private String customerGroup;

        /**
         * 客户简称<br/>Column: [customer_short_name]
         */
        private String customerShortName;

        /** 通用客户简称 */
        private String unCustomerShortName;

        private String crpWarZoneName;

        private String demandType;

        private String incomeSalesDesc;

        /**
         * gpu卡型
         */
        private String gpuCardType;

        /**
         * gpu类型
         */
        private String gpuType;

        private Integer isNewCategory;

        private Integer isBlack;

        private String instanceTypeGeneration;

        private String newCustomerType;
        private String projectType;
        private String productClass;

        public void fillWhereSQL(QueryTableReq req,WhereSQL whereSQL, boolean demandTypeFilter) {
            if (orderType != null) {
                boolean isNewOrderType = BooleanUtils.isTrue(req.getIsNewOrderType());
                if (isNewOrderType){
                    whereSQL.and("order_type = ?", orderType);
                }else {
                    whereSQL.and("old_order_type = ?", orderType);
                }
            }
            if (bizType != null) {
                whereSQL.and("biz_type = ?", bizType);
            }
            if (isNewCategory != null) {
                whereSQL.and("is_new_category = ?", isNewCategory);
            }
            if (industryOrProduct != null) {
                whereSQL.and("industry_or_product = ?", industryOrProduct);
            }
            if (regionName != null) {
                whereSQL.and("region_name = ?", regionName);
            }
            if (regionType != null) {
                whereSQL.and("region_type = ?", regionType);
            }
            if (zoneName != null) {
                whereSQL.and("zone_name = ?", zoneName);
            }
            if (ginsFamily != null) {
                if (CollectionUtils.isEmpty(ginsFamilies)) {
                    whereSQL.and("gins_family = ?", ginsFamily);
                } else {
                    whereSQL.and("gins_family in (?)", ginsFamilies);
                }
            }

            if (newCustomerType != null){
                whereSQL.and("new_customer_type = ?", newCustomerType);
            }

            if (projectType != null){
                whereSQL.and("project_type = ?", projectType);
            }

            if (productClass != null){
                whereSQL.and("product_class = ?", productClass);
            }

            if (instanceTypeGeneration != null){
                whereSQL.and("instance_type_generation = ?", instanceTypeGeneration);
            }

            if (customerInsideOrOutside != null) {
                whereSQL.and("customer_inside_or_outside = ?", customerInsideOrOutside);
            }
            if (customerType != null) {
                whereSQL.and("customer_type = ?", customerType);
            }
            if (warZoneName != null) {
                whereSQL.and("war_zone_name = ?", warZoneName);
            }
            if (crpWarZoneName != null){
                whereSQL.and("crp_war_zone_name = ?", crpWarZoneName);
            }
            if (customerGroup != null) {
                whereSQL.and("customer_group = ?", customerGroup);
            }
            if (customerShortName != null) {
                whereSQL.and("customer_short_name = ?", customerShortName);
            }
            if (unCustomerShortName != null){
                whereSQL.and("un_customer_short_name = ?", unCustomerShortName);
            }
            if (demandType != null && demandTypeFilter) {
                whereSQL.and("demand_type = ?", demandType);
            }
            if (gpuType != null) {
                whereSQL.and("gpu_type = ?", gpuType);
            }
            if (gpuCardType != null) {
                whereSQL.and("gpu_card_type = ?", gpuCardType);
            }
        }

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TimePeriod {

        // YYYY-MM 或 YYYY-Qn
        private String start;
        private String end;

    }

}
