package cloud.demand.app.modules.mrpv2.utils;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplConfigProductEnumDO;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

public class PaasUtils {

    /** 替换sql中的${PAAS} */
    public static String sqlReplacePAAS(String sql){
        if (StringUtils.isBlank(sql)){
            return sql;
        }
        MrpV2DictService bean = SpringUtil.getBean(MrpV2DictService.class);
        List<PplConfigProductEnumDO> paas = bean.queryAllProductEnum("PAAS", true);
        String replaceStr = "''";
        if (ListUtils.isNotEmpty(paas)){
            List<String> paasProductNames = paas.stream().map(item -> "'" + item.getProductName() + "'").collect(Collectors.toList());
            replaceStr = StringUtils.join(paasProductNames, ",");
        }
        sql = sql.replace("${PAAS}",replaceStr);
        return sql;
    }
}
