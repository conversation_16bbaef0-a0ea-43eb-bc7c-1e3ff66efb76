package cloud.demand.app.modules.mrpv2.targets.forecast_rate.origin;

import cloud.demand.app.modules.mrpv2.targets.forecast_rate.V2AvgNormalForecastExecutedRate;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;

public class OriginV2AvgNormalForecastExecutedRate extends V2AvgNormalForecastExecutedRate {

    public static String staticTargetName() {
        return "origin_v2_avg_normal_forecast_executed_rate";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "预测内执行占比(原始)",
                new RspFieldHeader("", "预测指标", null));
    }
    @Override
    public String targetName() {
        return staticTargetName();
    }
}
