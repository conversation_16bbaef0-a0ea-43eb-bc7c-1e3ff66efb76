package cloud.demand.app.modules.mrpv2.service;

import cloud.demand.app.modules.mrpv2.domain.CoreDayChangeCreate;
import cloud.demand.app.modules.mrpv2.domain.CoreDayChangeUpdate;
import cloud.demand.app.modules.mrpv2.entity.MrpV2ForecastMatchRateCoreDayChangeDO;
import cloud.demand.app.modules.mrpv2.model.MatchRateCacheReq;
import cloud.demand.app.modules.mrpv2.model.MatchRateCacheRes;
import cloud.demand.app.modules.mrpv2.model.MatchRateQueryReq;
import com.pugwoo.dbhelper.model.PageData;

import java.util.List;

/** 核天准确率干涉接口 */
public interface ForecastMatchRateCoreDayService {

    /** 查询干涉列表 */
    public PageData<MrpV2ForecastMatchRateCoreDayChangeDO> page(MatchRateQueryReq req);

    public List<MrpV2ForecastMatchRateCoreDayChangeDO> queryWithRate(MatchRateQueryReq req);

    /** 查询不带分页，且不查询原始预测值 */
    public List<MrpV2ForecastMatchRateCoreDayChangeDO> query(MatchRateQueryReq req);

    /** 查询准确率原始值 */
    public MatchRateCacheRes queryRate(MatchRateCacheReq req);

    /** 创建干涉信息 */

    public void create(CoreDayChangeCreate create);

    /** 更新干涉信息 */

    public void update(CoreDayChangeUpdate update);

    /** 更新干涉状态 */
    public void updateStatus(Long id,boolean open);

    /** 删除干涉信息 */

    public void delete(List<Long> id);
}
