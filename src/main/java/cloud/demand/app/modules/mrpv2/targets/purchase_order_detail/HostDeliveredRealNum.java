package cloud.demand.app.modules.mrpv2.targets.purchase_order_detail;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;

public class HostDeliveredRealNum extends BaseTemplate{

    @Override
    public String targetName() {
        return "host_delivered_real_num";
    }

    @Override
    public String targetDisplay() {
        return "产品采购到货量(交付月)";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "供应计划", null);
    }

    @Override
    public String sqlTemplate() {
        return ORMUtils.getSql("/sql/mrp/purchase/host_delivered_real_num.sql");
    }
}
