package cloud.demand.app.modules.mrpv2.targets.forecast_rate;

import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.DynamicTargetTemplate;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import java.util.List;
import java.util.Map;

public class V2AvgForecastMatchRate extends AvgForecastMatchRate {

    public static String staticTargetName() {
        return "v2_avg_forecast_match_rate";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }

    @Override
    public String targetDisplay() {
        return "532新版";
    }

}
