package cloud.demand.app.modules.mrpv2.targets.forecast_rate;

import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.DynamicTargetTemplate;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import java.util.List;
import java.util.Map;

public class BenchForecastMatchRate extends DynamicTargetTemplate {

    public static String staticTargetName() {
        return "bench_forecast_match_rate";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }

    @Override
    public String targetDisplay() {
        return "基准版";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "预测准确率",
                new RspFieldHeader("", "预测指标", null));
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }


}
