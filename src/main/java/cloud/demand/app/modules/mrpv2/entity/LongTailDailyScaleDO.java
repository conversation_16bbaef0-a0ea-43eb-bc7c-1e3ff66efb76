package cloud.demand.app.modules.mrpv2.entity;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/** 中长尾日规模 */
@Data
public class LongTailDailyScaleDO {

    @Column("order_type")
    private String orderType;

    @Column("data_product")
    private String dataProduct;

    @Column("any_project_type")
    private String projectType;

    @Column("any_product_class")
    private String productClass;

    @Column("biz_type")
    private String bizType;

    @Column("gins_family")
    private String ginsFamily;

    @Column("gpu_type")
    private String gpuType;

    @Column("gpu_card_type")
    private String gpuCardType;

    @Column("industry_or_product")
    private String industryOrProduct;

    @Column("customer_type")
    private String customerType;

    @Column("any_region_name")
    private String regionName;


    @Column("any_war_zone_name")
    private String warZoneName;

    @Column("any_customer_group")
    private String customerGroup;

    @Column("customer_short_name")
    private String customerShortName;

    @Column("un_customer_short_name")
    private String unCustomerShortName;

    @Column("year_month")
    private String yearMonth;

    @Column("add_change_bill_core")
    private BigDecimal addChangeBillCore;

    @Column("add_change_service_core")
    private BigDecimal addChangeServiceCore;

    @Column("return_change_bill_core")
    private BigDecimal returnChangeBillCore;

    @Column("return_change_service_core")
    private BigDecimal returnChangeServiceCore;

    @Column("net_change_bill_core")
    private BigDecimal netChangeBillCore;

    @Column("net_change_service_core")
    private BigDecimal netChangeServiceCore;

}
