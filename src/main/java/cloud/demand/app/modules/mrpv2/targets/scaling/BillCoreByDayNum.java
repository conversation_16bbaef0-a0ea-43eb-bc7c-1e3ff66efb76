package cloud.demand.app.modules.mrpv2.targets.scaling;

import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;

import java.util.List;
import java.util.Map;

/**
 * 计费用量核天
 */
public class BillCoreByDayNum extends ScaleCoreByDayNum {

    public static String staticTargetName() {
        return "bill_core_by_day_num";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }

    @Override
    public String targetDisplay() {
        return "计费核天量";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "购买执行", null);
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }

    @Override
    public TargetTemplate monthNotDistinct() {
        return new MonthNotDistinctBillCoreByDayNum();
    }

    @Override
    public TargetTemplate monthDistinct() {
        return new MonthDistinctBillCoreByDayNum();
    }
}
