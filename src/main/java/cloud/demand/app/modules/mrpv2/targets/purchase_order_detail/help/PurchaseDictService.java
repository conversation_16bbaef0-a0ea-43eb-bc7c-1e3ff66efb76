package cloud.demand.app.modules.mrpv2.targets.purchase_order_detail.help;

import cloud.demand.app.entity.plan.StaticModuleDO;
import cloud.demand.app.entity.yunti.CloudDemandCsigResourceViewCategoryDO;
import cloud.demand.app.entity.yunti.YuntiStategyZoneDO;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.util.List;

public interface PurchaseDictService {
    public List<YuntiStategyZoneDO> getZoneInfo();

    public List<StaticModuleDO> getModuleInfo();

    /** 获取全年需求看板ERP订单 oriId 到 campus 的映射 */

    public List<DwdOrderApplyDO> getDwdOrderApply(String versionDate);

    public List<CloudDemandCsigResourceViewCategoryDO> getProductCategory();


    @Data
    public static class DwdOrderApplyDO {

        @Column("ori_id")
        private String oriId;

        @Column("campus_name")
        private String campusName;
    }
}
