package cloud.demand.app.modules.mrpv2.targets.ppl_hedge;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import java.util.List;
import java.util.Map;
import org.nutz.lang.Lang;

public class LatestProductHostForecastNum extends TargetTemplate {

    @Override
    public String targetName() {
        return "latest_product_host_forecast_num";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "物理机预测量",
                new RspFieldHeader("", "供应计划", null));
    }

    @Override
    public String targetDisplay() {
        return "最新版";
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }
}
