package cloud.demand.app.modules.mrpv2.targets.scaling;

import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import java.util.List;
import java.util.Map;

public class CurBillNum extends ScaleNum {

    public static String staticTargetName() {
        return "cur_bill_num";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }

    @Override
    public String targetDisplay() {
        return "计费存量";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "购买执行", null);
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }

    @Override
    public TargetTemplate dailyNotDistinct() {
        return new DailyNotDistinctCurBillNum();
    }

    @Override
    public TargetTemplate monthAvgNotDistinct() {
        return new MonthAvgNotDistinctCurBillNum();
    }

    @Override
    public TargetTemplate dailyDistinct() {
        return new DailyNotDistinctCurBillNum();
    }

    @Override
    public TargetTemplate monthAvgDistinct() {
        return new MonthAvgNotDistinctCurBillNum();
    }
}
