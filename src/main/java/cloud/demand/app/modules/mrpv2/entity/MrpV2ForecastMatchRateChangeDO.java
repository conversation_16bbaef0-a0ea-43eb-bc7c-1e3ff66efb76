package cloud.demand.app.modules.mrpv2.entity;

import cloud.demand.app.modules.mrpv2.enums.CoreDayDimTypeEnum;
import cloud.demand.app.modules.mrpv2.enums.DemandTypeEnum;
import cloud.demand.app.modules.mrpv2.model.MatchRateCacheReq;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.function.BiConsumer;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.ObjectUtils;
import yunti.boot.exception.ITException;

/**
 * 532月均准确率干涉表
 */
@Data
@ToString
@Table("mrpv2_forecast_match_rate_change")
public class MrpV2ForecastMatchRateChangeDO extends MrpV2ForecastMatchRateCoreDayChangeDO {

    /** 转换为查询原始准确率的req */
    public static MatchRateCacheReq transForm(IDimCoreDayChangeInfo info,boolean isOrigin, CoreDayDimTypeEnum dimTypeEnum){
        MatchRateCacheReq ret = new MatchRateCacheReq();
        ret.setIndustryDept(info.getIndustryDeptList());
        ret.setDemandType(info.getDemandTypeList());
        ret.setYearMonth(info.getYearMonth());
        ret.setIsOrigin(isOrigin);
        ret.setDimType(dimTypeEnum);
        if (dimTypeEnum!=null && dimTypeEnum.equals(CoreDayDimTypeEnum.IndustryAndCustomer)){
            ret.setCustomerShortName(info.getCustomerShortNameList());
        }
        return ret;
    }

    /** 设置矫正后的准确率 */
    public static void setAfterMatchRate(MrpV2ForecastMatchRateChangeDO item){
        if (item == null){
            return;
        }
        // 客户维度
        BigDecimal originUp = ObjectUtils.defaultIfNull(item.getCustomerOriginUp(),BigDecimal.ZERO);
        BigDecimal currentUp = ObjectUtils.defaultIfNull(item.getCustomerCurrentUp(),BigDecimal.ZERO);
        BigDecimal beforeOriginMatchRate = ObjectUtils.defaultIfNull(item.getBeforeCustomerOriginMatchRate(),BigDecimal.ZERO);
        BigDecimal beforeCurrentMatchRate = ObjectUtils.defaultIfNull(item.getBeforeCustomerCurrentMatchRate(),BigDecimal.ZERO);
        item.setAfterCustomerOriginMatchRate(originUp.add(beforeOriginMatchRate));
        item.setAfterCustomerCurrentMatchRate(currentUp.add(beforeCurrentMatchRate));

        // 行业维度
        originUp = ObjectUtils.defaultIfNull(item.getIndustryOriginUp(),BigDecimal.ZERO);
        currentUp = ObjectUtils.defaultIfNull(item.getIndustryCurrentUp(),BigDecimal.ZERO);
        beforeOriginMatchRate = ObjectUtils.defaultIfNull(item.getBeforeIndustryOriginMatchRate(),BigDecimal.ZERO);
        beforeCurrentMatchRate = ObjectUtils.defaultIfNull(item.getBeforeIndustryCurrentMatchRate(),BigDecimal.ZERO);
        item.setAfterIndustryOriginMatchRate(originUp.add(beforeOriginMatchRate));
        item.setAfterIndustryCurrentMatchRate(currentUp.add(beforeCurrentMatchRate));
    }

    public static void checkAfterMatchRateThrow(MrpV2ForecastMatchRateChangeDO item){
        checkAfterMatchRate(item,(matchRate,info)->{
            throw new ITException(String.format("矫正准确率异常：维度信息：【%s】,矫正后准确率：【%s】",info, matchRate));
        });
    }

    /** 检查矫正后的准确率是否高于100%或者低于0% */
    public static void checkAfterMatchRate(MrpV2ForecastMatchRateChangeDO item, BiConsumer<BigDecimal, String> consumer){
        if (item == null){
            return;
        }
        BigDecimal customerOriginMatchRate = item.getAfterCustomerOriginMatchRate();
        BigDecimal customerCurrentMatchRate = item.getAfterCustomerCurrentMatchRate();
        BigDecimal industryOriginMatchRate = item.getAfterIndustryOriginMatchRate();
        BigDecimal industryCurrentMatchRate = item.getAfterIndustryCurrentMatchRate();

        List<Tuple2<BigDecimal, String>> tuple2s = ListUtils.newArrayList(
                new Tuple2<>(customerOriginMatchRate, "客户维度一行业预测准确率"),
                new Tuple2<>(customerCurrentMatchRate, "客户维度一云运管干预准确率"),
                new Tuple2<>(industryOriginMatchRate, "行业维度-行业预测准确率"),
                new Tuple2<>(industryCurrentMatchRate, "行业维度-云运管干预准确率"));

        BigDecimal _100 = new BigDecimal("100");

        for (Tuple2<BigDecimal, String> tuple2 : tuple2s) {
            BigDecimal matchRate = tuple2._1();
            String info = tuple2._2();
            if (matchRate ==null || matchRate.compareTo(BigDecimal.ZERO) < 0 || matchRate.compareTo(_100) >= 0){
                consumer.accept(matchRate,info);
            }
        }
    }

    @Override
    public List<String> getIndustryDeptList() {
        return ListUtils.newList(getIndustryDept());
    }

    @Override
    public List<String> getCustomerShortNameList() {
        return ListUtils.newList(getCustomerShortName());
    }

    @Override
    public List<String> getDemandTypeList() {
        return DemandTypeEnum.getDemandTypeList(getDemandType());
    }

    public void setDemandTypeList(List<String> demandTypeList){
        this.setDemandType(DemandTypeEnum.getDemandType(demandTypeList));
    }

}
