package cloud.demand.app.modules.mrpv2.domain;

import cloud.demand.app.modules.mrpv2.entity.MrpV2ForecastMatchRateCoreDayChangeDO;
import cloud.demand.app.modules.mrpv2.enums.CoreDayStatusEnum;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

@Data
public class CoreDayChangeUpdate extends CoreDayChangeCommon{
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    public static MrpV2ForecastMatchRateCoreDayChangeDO transForm(CoreDayChangeUpdate update){
        MrpV2ForecastMatchRateCoreDayChangeDO changeDO = CoreDayChangeCommon.transForm(update);
        changeDO.setId(update.getId());
        return changeDO;
    }
}
