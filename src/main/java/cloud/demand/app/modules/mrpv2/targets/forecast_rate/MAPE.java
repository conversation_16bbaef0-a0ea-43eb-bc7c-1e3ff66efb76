package cloud.demand.app.modules.mrpv2.targets.forecast_rate;

import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.entity.CurrentIndex;
import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.DynamicTargetTemplate;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.targets.ppl.ForecastNum;
import cloud.demand.app.modules.mrpv2.targets.scaling.ChangeBillNum;
import cloud.demand.app.modules.mrpv2.targets.scaling.ChangeServeNum;
import cloud.demand.app.modules.mrpv2.web.QueryTableReq;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Lang;
import org.springframework.util.CollectionUtils;

public class MAPE extends DynamicTargetTemplate {

    public static String staticTargetName() {
        return "mape";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }

    @Override
    public String targetDisplay() {
        return "MAPE偏差率";
    }

    @Override
    public RspFieldHeader header()  {
        return new RspFieldHeader("", "预测指标", null);
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }

    @Override
    public List<TargetTemplate> complexNode() {
        return Lang.list(new LatestMAPE(),
                new BenchMAPE(), new AvgMAPE(), new V2AvgMAPE());
    }

    @Override
    public void dynamicFieldToData(Collection<Map> data, QueryTableReq req, Boolean reGroupToOrigin) {
        super.dynamicFieldToData(data, req, reGroupToOrigin);
        List<String> searchTimeList = req.getSearchTimeList();
        String[] versions = new String[]{"latest", "bench", "avg", "v2_avg"};
        // 先按原来的透视字段分组
        List<String> originSelectFields = req.getOriginSelectFields();
        Map<String, List<Map>> dataMap = new HashMap<>();
        for (Map m : data) {
            StringBuilder keySB = new StringBuilder();
            for (String os : originSelectFields) {
                keySB.append(m.get(os));
                keySB.append("@");
            }
            String key;
            if (CollectionUtils.isEmpty(originSelectFields)) {
                key = "";
            } else {
                key = keySB.substring(0, keySB.length() - 1);
            }
            List<Map> list = dataMap.computeIfAbsent(key, k -> new ArrayList<>());
            list.add(m);
        }
        Map<String, BigDecimal> totalForecastNumMap = new HashMap<>();
        Map<String, BigDecimal> totalExecutedNumMap = new HashMap<>();
        // 先算总执行量和总预测量
        for (Entry<String, List<Map>> entry : dataMap.entrySet()) {
            for (String version : versions) {
                for (Map m : entry.getValue()) {
                    String bizType = (String) m.get("bizType");
                    if (StringUtils.isBlank(bizType) || bizType.equals(Constant.EMPTY_VALUE)) {
                        // 必须要透视到业务类别
                        continue;
                    }
                    for (String timeKey : searchTimeList) {

                        Object value = m.get(version + "_" + ForecastNum.staticTargetName() + "@" + timeKey);
                        BigDecimal forecastNum = toBigDecimal(value);
                        if (forecastNum == null) {
                            forecastNum = BigDecimal.ZERO;
                        }
                        if (forecastNum.compareTo(BigDecimal.ZERO) < 0) {
                            forecastNum = forecastNum.negate();
                        }

                        String staticTargetNamePrefix;
                        if (req.getBillingScaleMode().equals("月均切片")) {
                            staticTargetNamePrefix = "month_avg_";
                        } else {
                            staticTargetNamePrefix = "daily_";
                        }
                        if (req.getBillingScaleDistinct()) {
                            staticTargetNamePrefix += "distinct_";
                        } else {
                            staticTargetNamePrefix += "not_distinct_";
                        }
                        if ("外部行业".equals(bizType)) {
                            value = m.get(staticTargetNamePrefix + ChangeBillNum.staticTargetName() + "@" + timeKey);
                        } else if ("内领业务".equals(bizType)) {
                            value = m.get(staticTargetNamePrefix + ChangeServeNum.staticTargetName() + "@" + timeKey);
                        } else {
                            continue;
                        }
                        BigDecimal executedNum = toBigDecimal(value);
                        if (executedNum == null) {
                            executedNum = BigDecimal.ZERO;
                        }
                        if (executedNum.compareTo(BigDecimal.ZERO) < 0) {
                            executedNum = executedNum.negate();
                        }

                        String key = entry.getKey() + "@"
                                + version + "@" + timeKey;
                        BigDecimal totalForecastNum = totalForecastNumMap.getOrDefault(key,
                                BigDecimal.ZERO);

                        totalForecastNum = totalForecastNum.add(forecastNum);
                        totalForecastNumMap.put(key, totalForecastNum);

                        BigDecimal totalExecutedNum = totalExecutedNumMap.getOrDefault(key,
                                BigDecimal.ZERO);

                        totalExecutedNum = totalExecutedNum.add(executedNum);
                        totalExecutedNumMap.put(key, totalExecutedNum);
                    }
                }
            }
        }

        for (Entry<String, List<Map>> entry : dataMap.entrySet()) {
            for (String version : versions) {
                for (Map m : entry.getValue()) {
                    String bizType = (String) m.get("bizType");
                    if (StringUtils.isBlank(bizType) || bizType.equals(Constant.EMPTY_VALUE)) {
                        // 必须要透视到业务类别
                        continue;
                    }

                    // 不算总计
                    int timeListLength = searchTimeList.size() - 1;
                    BigDecimal timeListRate = BigDecimal.ONE.divide(BigDecimal.valueOf(timeListLength), 8, BigDecimal.ROUND_HALF_UP);
                    BigDecimal totalForecastMatchRate = BigDecimal.ZERO;

                    for (String timeKey : searchTimeList) {
                        if (timeKey.equals("总计")) {
                            continue;
                        }

                        Object value = m.get(version + "_" + ForecastNum.staticTargetName() + "@" + timeKey);
                        BigDecimal forecastNum = toBigDecimal(value);
                        if (forecastNum == null) {
                            forecastNum = BigDecimal.ZERO;
                        }
                        Boolean negate = null;
                        if (forecastNum.compareTo(BigDecimal.ZERO) < 0) {
                            forecastNum = forecastNum.negate();
                            negate = true;
                        } else if (forecastNum.compareTo(BigDecimal.ZERO) > 0) {
                            negate = false;
                        }

                        String staticTargetNamePrefix;
                        if (req.getBillingScaleMode().equals("月均切片")) {
                            staticTargetNamePrefix = "month_avg_";
                        } else {
                            staticTargetNamePrefix = "daily_";
                        }
                        if (req.getBillingScaleDistinct()) {
                            staticTargetNamePrefix += "distinct_";
                        } else {
                            staticTargetNamePrefix += "not_distinct_";
                        }
                        if ("外部行业".equals(bizType)) {
                            value = m.get(staticTargetNamePrefix + ChangeBillNum.staticTargetName() + "@" + timeKey);
                        } else if ("内领业务".equals(bizType)) {
                            value = m.get(staticTargetNamePrefix + ChangeServeNum.staticTargetName() + "@" + timeKey);
                        } else {
                            continue;
                        }
                        BigDecimal executedNum = toBigDecimal(value);
                        if (executedNum == null) {
                            executedNum = BigDecimal.ZERO;
                        }
                        // 本行的准确率
                        BigDecimal matchRate = null;
                        if (executedNum.compareTo(BigDecimal.ZERO) < 0) {
                            // 如果上面是正数
                            if (negate != null && !negate) {
                                matchRate = BigDecimal.ZERO;
                            }
                            executedNum = executedNum.negate();
                        } else if (executedNum.compareTo(BigDecimal.ZERO) > 0) {
                            // 如果上面是负数
                            if (negate != null && negate) {
                                matchRate = BigDecimal.ZERO;
                            }
                        }
                        if (matchRate == null) {
                            if (executedNum.compareTo(BigDecimal.ZERO) == 0) {
                                if (forecastNum.compareTo(BigDecimal.ZERO) == 0) {
                                    matchRate = BigDecimal.ZERO;
                                } else {
                                    matchRate = Constant.b3;
                                }
                            } else {
                                Double r = Math.abs(forecastNum.subtract(executedNum).divide(executedNum, 4, RoundingMode.HALF_UP).doubleValue());
                                if (r.compareTo(0.0) < 0) {
                                    matchRate = BigDecimal.ZERO;
                                } else if (r.compareTo(3.0) > 0) {
                                    matchRate = Constant.b3;
                                } else {
                                    matchRate = BigDecimal.valueOf(r);
                                }
                            }
                        }

                        if (reGroupToOrigin) {
                            // 本行的权重
                            BigDecimal numRate;
                            BigDecimal totalForecastNum = totalForecastNumMap.getOrDefault(entry.getKey() + "@"
                                            + version + "@" + timeKey,
                                    BigDecimal.ZERO);
                            BigDecimal totalExecutedNum = totalExecutedNumMap.getOrDefault(entry.getKey() + "@"
                                            + version + "@" + timeKey,
                                    BigDecimal.ZERO);
                            if (req.getRateSumFunction().equals("执行+需求预测量")) {
                                BigDecimal totalNum = totalForecastNum.add(totalExecutedNum);
                                if (totalNum.compareTo(BigDecimal.ZERO) == 0) {
                                    numRate = BigDecimal.ZERO;
                                } else {
                                    BigDecimal num = forecastNum.add(executedNum);
                                    numRate = num.divide(totalNum, 4, RoundingMode.HALF_UP);
                                }
                            } else if (req.getRateSumFunction().equals("执行量")) {
                                if (totalExecutedNum.compareTo(BigDecimal.ZERO) == 0) {
                                    numRate = BigDecimal.ZERO;
                                } else {
                                    numRate = executedNum.divide(totalExecutedNum, 4, RoundingMode.HALF_UP);
                                }
                            } else {
                                if (totalForecastNum.compareTo(BigDecimal.ZERO) == 0) {
                                    numRate = BigDecimal.ZERO;
                                } else {
                                    numRate = forecastNum.divide(totalForecastNum, 4, RoundingMode.HALF_UP);
                                }
                            }
                            BigDecimal partRate = numRate.multiply(matchRate);
                            totalForecastMatchRate = totalForecastMatchRate.add(partRate.multiply(timeListRate));
                            m.put(version + "_" + staticTargetName() + "@" + timeKey, partRate);
                        } else {
                            totalForecastMatchRate = totalForecastMatchRate.add(matchRate.multiply(timeListRate));
                            m.put(version + "_" + staticTargetName() + "@" + timeKey,
                                    String.format("%.2f%%", Constant.b100.multiply(matchRate)));
                        }
                    }
                    if (reGroupToOrigin) {
                        m.put(version + "_" + staticTargetName() + "@总计", totalForecastMatchRate);
                    } else {
                        m.put(version + "_" + staticTargetName() + "@总计",
                                String.format("%.2f%%", Constant.b100.multiply(totalForecastMatchRate)));
                    }
                }
            }
        }

    }
}
