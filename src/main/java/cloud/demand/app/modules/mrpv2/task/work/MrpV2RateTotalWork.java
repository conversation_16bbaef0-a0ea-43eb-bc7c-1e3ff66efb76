package cloud.demand.app.modules.mrpv2.task.work;

import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.mrpv2.entity.MrpV2ForecastTotalRateDO;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.MrpV2RateTotalCustomhouseTitleEnum;
import cloud.demand.app.modules.mrpv2.enums.MrpV2RateTotalDemandTypeEnum;
import cloud.demand.app.modules.mrpv2.enums.ProjectTypeEnum;
import cloud.demand.app.modules.mrpv2.enums.SimpleTaskEnum;
import cloud.demand.app.modules.mrpv2.service.DailyMrpV2DataDescribeService;
import cloud.demand.app.modules.mrpv2.service.QueryDataService;
import cloud.demand.app.modules.mrpv2.task.process.SimpleCommonTaskProcess;
import cloud.demand.app.modules.mrpv2.web.QueryTableReq;
import cloud.demand.app.modules.mrpv2.web.QueryTableRsp;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.work.AbstractSopWork;
import com.alibaba.fastjson.JSON;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.lang.reflect.UndeclaredThrowableException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/** 总准确率存表任务（给jiyongcheng用） */
@Service
@Slf4j(topic = "总准确率存表任务：")
public class MrpV2RateTotalWork extends AbstractSopWork<SimpleCommonTask> {
    @Resource
    private SimpleCommonTaskProcess process;

    /** 行业数据看板 */
    @Resource
    private QueryDataService queryDataService;

    /** 预测准确率写的库 */
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private DailyMrpV2DataDescribeService describeService;


    @Override
    public ISopProcess<SimpleCommonTask> getTask() {
        return process;
    }

    @Override
    public ITaskEnum getEnum() {
        return SimpleTaskEnum.MRP_V2_RATE_TOTAL;
    }

    // 两分钟一次
    @Scheduled(fixedRate = 120 * 1000)
    @Override
    public void work() {
        super.work();
    }

    /** 9:40生成 */
    @Scheduled(cron = "0 40 9 * * ?")
    public void initForecastRateTask(){
        initTask(null);
    }

    /** 初始化任务 */
    public void initTask(LocalDate statTime){
        if (statTime == null){
            statTime = LocalDate.now();
        }
        SimpleCommonTask simpleCommonTask = new SimpleCommonTask();
        simpleCommonTask.setVersion(DateUtils.format(statTime));
        process.initTask(simpleCommonTask,getEnum());
    }

    @Override
    public void doWork(SimpleCommonTask task) {
        // version 即 切片时间 （格式：yyyy-MM-dd）
        String version = task.getVersion();
        doGenerateForecastRate(LocalDate.parse(version));
    }

    public void doGenerateForecastRate(LocalDate time){
        // 不传切片时间默认当前时间
        final LocalDate statTime = time == null? LocalDate.now():time;
        String nowDate = DateUtils.format(statTime);
        StopWatch stopWatch = new StopWatch("生成预测准确率总表");
        // step0：清理切片数据
        stopWatch.start(String.format("清理切片数据,切片日期：【%s】", nowDate));
        CkDBUtils.doDelete(ckcldStdCrpDBHelper, nowDate, MrpV2ForecastTotalRateDO.class, "std_crp");
        stopWatch.stop();
        List<MrpV2ForecastTotalRateDO> saveData = new ArrayList<>();

        for (MrpV2RateTotalDemandTypeEnum demandTypeEnum : MrpV2RateTotalDemandTypeEnum.values()) {
            for (MrpV2RateTotalCustomhouseTitleEnum customhouseTitleEnum : MrpV2RateTotalCustomhouseTitleEnum.values()) {
                saveData.addAll(getSaveDate(stopWatch,statTime,demandTypeEnum,customhouseTitleEnum));
            }
        }

        // step4：存表
        stopWatch.start(String.format("准确率存表,切片日期：【%s】，记录数：【%s】", nowDate,saveData.size()));
        ckcldStdCrpDBHelper.insertBatchWithoutReturnId(saveData);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
    }

    private List<MrpV2ForecastTotalRateDO> getSaveDate(StopWatch stopWatch, LocalDate statTime,
            MrpV2RateTotalDemandTypeEnum demandType,
            MrpV2RateTotalCustomhouseTitleEnum customhouseTitleEnum){
        if (demandType == null){
            throw new ITException("需求类型不能为空");
        }
        if (customhouseTitleEnum == null){
            throw new ITException("境内外不能为空");
        }
        String demandTypeName = demandType.getName();
        List<MrpV2ForecastTotalRateDO> saveData = new ArrayList<>();
        // step1：获取范围内头部准确率
        stopWatch.start(String.format("获取范围内头部准确率，切片时间：【%s】，需求类型：【%s】，境内外：【%s】",statTime,demandTypeName, customhouseTitleEnum.getName()));
        Map<String, MrpV2ForecastTotalRateDO> headRateMap = getForecastRate(statTime,demandType,customhouseTitleEnum);
        log.info("头部准确率集合：\n" + JSON.toJSON(new TreeMap<>(headRateMap)));
        stopWatch.stop();
        // step2：获取范围内中长尾准确率
        stopWatch.start(String.format("获取范围内中长尾准确率，切片时间：【%s】，需求类型：【%s】", statTime,demandTypeName));
        Map<String, MrpV2ForecastTotalRateDO> longTailRateMap = new HashMap<>();
        log.info("中长尾准确率集合：\n" + JSON.toJSON(new TreeMap<>(longTailRateMap)));
        stopWatch.stop();
        // step3：计算总准确率（头部 100% + 中长尾 0%，如果只有头部或者中长尾则不加权）
        stopWatch.start(String.format("计算总准确率（头部 1 + 中长尾 0，如果只有头部或者中长尾则不加权），切片时间：【%s】，需求类型：【%s】，境内外：【%s】", statTime,demandTypeName,customhouseTitleEnum.getName()));
        BigDecimal heatRate = new BigDecimal("1");
        BigDecimal longRate = new BigDecimal("0");
        headRateMap.forEach((k,v)->{
            MrpV2ForecastTotalRateDO remove = longTailRateMap.remove(k);
            MrpV2ForecastTotalRateDO item = new MrpV2ForecastTotalRateDO();
            item.setStatTime(statTime);
            item.setDemandType(demandType.getName());
            item.setCustomhouseTitle(customhouseTitleEnum.getName());
            item.setYearMonth(k);
            item.setBenchForecastMatchRate(ObjectUtils.defaultIfNull(v.getBenchForecastMatchRate(),BigDecimal.ZERO));
            item.setAvgForecastMatchRate(ObjectUtils.defaultIfNull(v.getAvgForecastMatchRate(),BigDecimal.ZERO));
            item.setV2AvgForecastMatchRate(ObjectUtils.defaultIfNull(v.getV2AvgForecastMatchRate(),BigDecimal.ZERO));
            item.setLatestForecastMatchRate(ObjectUtils.defaultIfNull(v.getLatestForecastMatchRate(),BigDecimal.ZERO));
            if (remove!=null){
                BigDecimal latestForecastMatchRate = ObjectUtils.defaultIfNull(remove.getLatestForecastMatchRate(),BigDecimal.ZERO);
                BigDecimal benchForecastMatchRate = ObjectUtils.defaultIfNull(remove.getBenchForecastMatchRate(),BigDecimal.ZERO);
                BigDecimal avgForecastMatchRate = ObjectUtils.defaultIfNull(remove.getAvgForecastMatchRate(),BigDecimal.ZERO);
                BigDecimal v2AvgForecastMatchRate = ObjectUtils.defaultIfNull(remove.getV2AvgForecastMatchRate(),BigDecimal.ZERO);
                if (latestForecastMatchRate.compareTo(BigDecimal.ZERO)!=0){
                    BigDecimal headV = item.getLatestForecastMatchRate().multiply(heatRate);
                    if (headV.compareTo(BigDecimal.ZERO)!=0){
                        item.setLatestForecastMatchRate(headV.add(latestForecastMatchRate.multiply(longRate)).setScale(2, RoundingMode.HALF_UP));
                    }else {
                        item.setLatestForecastMatchRate(latestForecastMatchRate);
                    }
                }
                if (benchForecastMatchRate.compareTo(BigDecimal.ZERO)!=0){
                    BigDecimal headV = item.getBenchForecastMatchRate().multiply(heatRate);
                    if (headV.compareTo(BigDecimal.ZERO)!=0){
                        item.setBenchForecastMatchRate(headV.add(benchForecastMatchRate.multiply(longRate)).setScale(2, RoundingMode.HALF_UP));
                    }else {
                        item.setBenchForecastMatchRate(benchForecastMatchRate);
                    }

                }
                if (avgForecastMatchRate.compareTo(BigDecimal.ZERO)!=0){
                    BigDecimal headV = item.getAvgForecastMatchRate().multiply(heatRate);
                    if (headV.compareTo(BigDecimal.ZERO)!=0){
                        item.setAvgForecastMatchRate(headV.add(avgForecastMatchRate.multiply(longRate)).setScale(2, RoundingMode.HALF_UP));
                    }else {
                        item.setAvgForecastMatchRate(avgForecastMatchRate);
                    }
                }
                if (v2AvgForecastMatchRate.compareTo(BigDecimal.ZERO)!=0){
                    BigDecimal headV = item.getV2AvgForecastMatchRate().multiply(heatRate);
                    if (headV.compareTo(BigDecimal.ZERO)!=0){
                        item.setV2AvgForecastMatchRate(headV.add(v2AvgForecastMatchRate.multiply(longRate)).setScale(2, RoundingMode.HALF_UP));
                    }else {
                        item.setV2AvgForecastMatchRate(v2AvgForecastMatchRate);
                    }
                }
            }
            saveData.add(item);
        });
        if (ListUtils.isNotEmpty(longTailRateMap)){
            longTailRateMap.forEach((k,v)->{
                MrpV2ForecastTotalRateDO item = new MrpV2ForecastTotalRateDO();
                item.setStatTime(statTime);
                item.setYearMonth(k);
                item.setDemandType(demandType.getName());
                item.setCustomhouseTitle(customhouseTitleEnum.getName());
                item.setBenchForecastMatchRate(ObjectUtils.defaultIfNull(v.getBenchForecastMatchRate(),BigDecimal.ZERO));
                item.setAvgForecastMatchRate(ObjectUtils.defaultIfNull(v.getAvgForecastMatchRate(),BigDecimal.ZERO));
                item.setV2AvgForecastMatchRate(ObjectUtils.defaultIfNull(v.getV2AvgForecastMatchRate(),BigDecimal.ZERO));
                item.setLatestForecastMatchRate(ObjectUtils.defaultIfNull(v.getLatestForecastMatchRate(),BigDecimal.ZERO));
            });
        }
        stopWatch.stop();
        return saveData;
    }

    /**
     * 获取预测准确率，key：年月，value：准确率
     * */
    private Map<String, MrpV2ForecastTotalRateDO> getForecastRate(LocalDate statTime,
            MrpV2RateTotalDemandTypeEnum demandTypeEnum,
            MrpV2RateTotalCustomhouseTitleEnum customhouseTitleEnum){
        QueryTableReq req = getReq(statTime, demandTypeEnum,customhouseTitleEnum);
        QueryTableRsp rsp;
        try {
            rsp = queryDataService.queryTable(req, false);
        }catch (UndeclaredThrowableException e) {
            throw new ITException(e.getCause());
        }
        Collection<Map> data = rsp.getData();
        Map<String, MrpV2ForecastTotalRateDO> ret = new HashMap<>();
        Map<String,Object> map = data.iterator().next();
        if (ListUtils.isNotEmpty(map)){
            String[] indexes = {"bench_forecast_match_rate","latest_forecast_match_rate","avg_forecast_match_rate","v2_avg_forecast_match_rate"};
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                for (String index : indexes) {
                    // 指标匹配，不看总计
                    if (key.startsWith(index) && !key.contains("总计")){
                        String[] split = key.split("@");
                        String yearMonth = split[1];
                        MrpV2ForecastTotalRateDO item = ret.computeIfAbsent(yearMonth, temp -> new MrpV2ForecastTotalRateDO());
                        item.setWithIndex(index,parsePercentage((String) value));
                        break;
                    }
                }
            }
        }
        return ret;
    }

    /** 百分百字符串转BigDecimal */
    private BigDecimal parsePercentage(String percentage){
        if (StringUtils.isBlank(percentage)){
            return BigDecimal.ZERO;
        }
        return new BigDecimal(percentage.replace("%",""));
    }

    /** 获取请求体（头部和中长尾的请求不同，这里入参isHead控制） */
    private QueryTableReq getReq(LocalDate statTime,MrpV2RateTotalDemandTypeEnum demandTypeEnum,MrpV2RateTotalCustomhouseTitleEnum customhouseTitleEnum){
        if (demandTypeEnum == null){
            throw new ITException("需求类型不能为空");
        }
        QueryTableReq req = new QueryTableReq();
        req.setProductClass(ListUtils.newArrayList(Ppl13weekProductTypeEnum.CVM.getName()));
        req.setIsNewOrderType(true);
        req.setProduct("CVM");
        req.setSelectFields(ListUtils.newArrayList());
        req.setNextSelectFields(ListUtils.newArrayList("projectType"));
        req.setQueryTargets(ListUtils.newArrayList("forecast_match_rate"));
        List<String> demandType = demandTypeEnum.getDemandType().get();
        List<String> customhouseTitle = customhouseTitleEnum.getCustomhouse().get();
        req.setDemandTypes(demandType);
        req.setCountry(customhouseTitle);
        req.setIsNewCategory(ListUtils.newArrayList(1,-1));
        req.setIsBlack(ListUtils.newArrayList(1,-1));
        req.setBizTypes(ListUtils.newArrayList("外部行业","(空值)"));
        req.setForecastStatus("云运管干预");
        req.setBillingScaleMode("月均切片");
        req.setBillAppRole(ListUtils.newArrayList("CDH", "正常售卖", "预扣包", "GOCP", "CDZ"));
        req.setServiceAppleRole(ListUtils.newArrayList("CNAS","CSS","EKS","EMR","NON_CCDB_OTHER", "USE_FOR_30","VPCGW","VPNGW","YUNTI","官网领用","算力"));
        QueryTableReq.TimePeriod timePeriod = new QueryTableReq.TimePeriod();
        // 时间范围从：23年到现在
        timePeriod.setStart("2023-01");
        YearMonth yearMonth =  statTime!=null? YearMonth.of(statTime.getYear(),statTime.getMonthValue()):YearMonth.now();
        timePeriod.setEnd(yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        req.setTimePeriod(timePeriod);
        req.setIsRendering(false);
        req.setRateSumFunction("执行量");
        req.setCommonInstanceType(true);
        return req;
    }
}
