package cloud.demand.app.modules.mrpv2.model;

import cloud.demand.app.modules.mrpv2.entity.MrpV2ForecastMatchRateCoreDayChangeDO;
import com.pugwoo.dbhelper.annotation.Column;
import java.util.Date;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ForecastMatchRateCoreDayInfoList {

    /** 行业部门 */
    private String industryDept;

    /** 客户简称 */
    private String customerShortName;

    /** 干预前上涨 */
    private BigDecimal originUp;

    /** 干预后上涨 */
    private BigDecimal currentUp;

    /** 是否原始准确率，true：原始准确率，false，运营干预后准确率 */
    private Boolean isOrigin;

    /** 干涉前准确率 */
    private BigDecimal beforeMatchRate;

    /** 干涉后准确率 */
    private BigDecimal afterMatchRate;

    /** 矫正类型 {@link cloud.demand.app.modules.mrpv2.enums.CoreDayChangeTypeEnum} */
    private String changeType;

    /** 矫正原因 */
    private String changeResult;

    /** 行业预测准确率提升-行业部门<br/>Column: [origin_up] */
    private BigDecimal industryOriginUp;

    /** 运管干预预测准确率提升-行业部门<br/>Column: [current_up] */
    private BigDecimal industryCurrentUp;

    /** 行业预测准确率提升<br/>Column: [origin_up] */
    private BigDecimal customerOriginUp;

    /** 运管干预预测准确率提升<br/>Column: [current_up] */
    private BigDecimal customerCurrentUp;

    private String updateUser;

    private Date updateTime;


    public static ForecastMatchRateCoreDayInfoList transForm(MrpV2ForecastMatchRateCoreDayChangeDO changeDO, boolean needCustomer) {
        ForecastMatchRateCoreDayInfoList ret = new ForecastMatchRateCoreDayInfoList();
        ret.setIndustryDept(changeDO.getIndustryDept());
        ret.setCustomerShortName(changeDO.getCustomerShortName());
        ret.setChangeResult(changeDO.getChangeResult());
        ret.setChangeType(changeDO.getChangeType());
        if (needCustomer){
            ret.setCurrentUp(changeDO.getCustomerCurrentUp());
            ret.setOriginUp(changeDO.getCustomerOriginUp());
        }else {
            ret.setCurrentUp(changeDO.getIndustryCurrentUp());
            ret.setOriginUp(changeDO.getIndustryOriginUp());
        }
        ret.setIndustryOriginUp(changeDO.getIndustryOriginUp());
        ret.setIndustryCurrentUp(changeDO.getIndustryCurrentUp());
        ret.setCustomerOriginUp(changeDO.getCustomerOriginUp());
        ret.setCustomerCurrentUp(changeDO.getCustomerCurrentUp());
        ret.setUpdateTime(changeDO.getUpdateTime());
        ret.setUpdateUser(changeDO.getUpdateUser());
        return ret;
    }
}
