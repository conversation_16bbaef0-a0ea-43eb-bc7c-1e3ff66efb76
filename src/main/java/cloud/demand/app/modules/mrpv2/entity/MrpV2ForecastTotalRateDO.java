package cloud.demand.app.modules.mrpv2.entity;


import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import teglib.shaded.org.checkerframework.checker.units.qual.C;
import yunti.boot.exception.ITException;

import java.math.BigDecimal;
import java.time.LocalDate;

/** 总准确率 */
@Data
@Table("daily_mrp_v2_forecast_total_rate")
public class MrpV2ForecastTotalRateDO {

    /** 切片日期 */
    @Column("stat_time")
    private LocalDate statTime;

    /** 年月,格式：yyyy-MM */
    @Column("year_month")
    private String yearMonth;

    /** 需求类型 {@link cloud.demand.app.modules.mrpv2.enums.MrpV2RateTotalDemandTypeEnum} */
    @Column("demand_type")
    private String demandType;

    /** 境内外 {@link cloud.demand.app.modules.mrpv2.enums.MrpV2RateTotalCustomhouseTitleEnum} */
    @Column("customhouse_title")
    private String customhouseTitle;

    /** 532版准确率 */
    @Column("avg_forecast_match_rate")
    private BigDecimal avgForecastMatchRate;

    /** 532新版准确率 */
    @Column("v2_avg_forecast_match_rate")
    private BigDecimal v2AvgForecastMatchRate;

    /** 基准版准确率 */
    @Column("bench_forecast_match_rate")
    private BigDecimal benchForecastMatchRate;

    /** 最新版准确率 */
    @Column("latest_forecast_match_rate")
    private BigDecimal latestForecastMatchRate;

    public void setWithIndex(String index,BigDecimal val){
        if (StringUtils.isBlank(index)){
            throw new ITException("准确率指标名称不能为空");
        }
        switch (index){
            case "avg_forecast_match_rate": setAvgForecastMatchRate(val);break;
            case "v2_avg_forecast_match_rate": setV2AvgForecastMatchRate(val);break;
            case "bench_forecast_match_rate": setBenchForecastMatchRate(val);break;
            case "latest_forecast_match_rate": setLatestForecastMatchRate(val);break;
            default:
                throw new ITException(String.format("该准确率指标不存在：【%s】", index));
        }
    }

}
