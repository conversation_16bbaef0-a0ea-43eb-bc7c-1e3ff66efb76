package cloud.demand.app.modules.mrpv2.targets.ppl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.mrpv2.entity.TailPplForecastDO;

import java.util.List;
import java.util.Map;

public class LongStdCrpBenchForecastNum extends LongStdCrpForecastNum {

    @Override
    public String targetName() {
        return "bench_long_forecast_num";
    }

    @Override
    public String targetDisplay() {
        return "基准版";
    }

    @Override
    public List<TailPplForecastDO> loadTailPplForecastDO(Map<String, Object> shares, String yearMonthStart) {
        return (List<TailPplForecastDO>) shares.get("tail-m-3");
    }
}
