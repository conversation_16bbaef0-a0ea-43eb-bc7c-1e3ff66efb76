package cloud.demand.app.modules.mrpv2.targets.scaling;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.entity.DailyMrpV2DataMap;
import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.enums.IsNewCategoryEnum;
import cloud.demand.app.modules.mrpv2.enums.NewCustomerTypeEnum;
import cloud.demand.app.modules.mrpv2.enums.ProjectTypeEnum;
import cloud.demand.app.modules.mrpv2.model.clean.ICleanBizType;
import cloud.demand.app.modules.mrpv2.service.CleanService;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.utils.MyMapUtils;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.lang.DateUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Lang;
import org.springframework.util.CollectionUtils;

@Slf4j
public class DailyNotDistinctScaleTemplate extends TargetTemplate {
    private final DBHelper ckcldStdCrpDBHelper = DBList.ckcldStdCrpDBHelper;

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "购买执行", null);
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }

    @Override
    public String targetName() {
        return "daily_not_distinct_scale_num";
    }

    @Override
    public String targetDisplay() {
        return null;
    }

    @Override
    public List<TargetTemplate> complexNode() {
        return Lang.list(
                new DailyNotDistinctChangeBillNum(),
                new DailyNotDistinctChangeServeNum(),
                new DailyNotDistinctCurBillNum(),
                new DailyNotDistinctCurServeNum(),
                new LongDailyNotDistinctChangeBillNum(),
                new LongDailyNotDistinctChangeServeNum(),
                new LongDailyNotDistinctCurBillNum(),
                new LongDailyNotDistinctCurServeNum()
                );
    }

    private void longMTargetSet(DDO ddo, DailyMrpV2DataMap o, Boolean isBill) {
        if (isBill == null) {
            o.putTarget(LongDailyNotDistinctChangeBillNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffBillNum());
            o.putTarget(LongDailyNotDistinctChangeServeNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffServeNum());
        } else if (isBill) {
            o.putTarget(LongDailyNotDistinctChangeBillNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffBillNum());
        } else {
            o.putTarget(LongDailyNotDistinctChangeServeNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffServeNum());
        }
    }

    private void mTargetSet(DDO ddo, DailyMrpV2DataMap o, Boolean isBill) {
        if (isBill == null) {
            o.putTarget(DailyNotDistinctChangeBillNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffBillNum());
            o.putTarget(DailyNotDistinctChangeServeNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffServeNum());
        } else if (isBill) {
            o.putTarget(DailyNotDistinctChangeBillNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffBillNum());
        } else {
            o.putTarget(DailyNotDistinctChangeServeNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffServeNum());
        }
    }



    private void mTargetSet(TailDDO ddo, DailyMrpV2DataMap o, Boolean isBill,boolean isNewCategory) {
        if (isNewCategory){
            if (isBill == null) {
                o.putTarget(LongDailyNotDistinctChangeBillNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffBillNum());
                o.putTarget(LongDailyNotDistinctChangeServeNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffServeNum());
            } else if (isBill) {
                o.putTarget(LongDailyNotDistinctChangeBillNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffBillNum());
            } else {
                o.putTarget(LongDailyNotDistinctChangeServeNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffServeNum());
            }
        }else {
            if (isBill == null) {
                o.putTarget(DailyNotDistinctChangeBillNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffBillNum());
                o.putTarget(DailyNotDistinctChangeServeNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffServeNum());
            } else if (isBill) {
                o.putTarget(DailyNotDistinctChangeBillNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffBillNum());
            } else {
                o.putTarget(DailyNotDistinctChangeServeNum.staticTargetName(), ddo.getYearMonth(), ddo.getDiffServeNum());
            }
        }
    }


    private void splitToMapCur(TailDDO ddo, Map<String, DailyMrpV2DataMap> resultMap) {
        splitToMapCur(ddo,resultMap,false);
    }

    private void splitToMapCur(TailDDO ddo, Map<String, DailyMrpV2DataMap> resultMap,boolean isNewCategory) {
        splitToMapCur(ddo,"新增",resultMap,isNewCategory);
    }

    private void splitToMapCur(TailDDO ddo, String demandType, Map<String, DailyMrpV2DataMap> resultMap,boolean isNewCategory) {
        String key = ddo.key(demandType);
        DailyMrpV2DataMap o = resultMap.get(key);
        if (o == null) {
            o = TailDDO.copy(ddo,isNewCategory);
            o.setDemandType(demandType);
            resultMap.put(key, o);
        }
        if (isNewCategory){
            o.setTarget(LongDailyNotDistinctCurBillNum.staticTargetName(), ddo.getYearMonth(), ddo.getStockBillNum());
            o.setTarget(LongDailyNotDistinctCurServeNum.staticTargetName(), ddo.getYearMonth(), ddo.getStockServeNum());
        }else {
            o.setTarget(DailyNotDistinctCurBillNum.staticTargetName(), ddo.getYearMonth(), ddo.getStockBillNum());
            o.setTarget(DailyNotDistinctCurServeNum.staticTargetName(), ddo.getYearMonth(), ddo.getStockServeNum());
        }
    }

    private void longSplitToMapCur(DDO ddo, Map<String, DailyMrpV2DataMap> resultMap) {
        String key = ddo.key("新增");
        DailyMrpV2DataMap o = resultMap.get(key);
        if (o == null) {
            o = DDO.copy(ddo);
            o.setDemandType("新增");
            resultMap.put(key, o);
        }
        o.putTarget(LongDailyNotDistinctCurBillNum.staticTargetName(), ddo.getYearMonth(), ddo.getStockBillNum());
        o.putTarget(LongDailyNotDistinctCurServeNum.staticTargetName(), ddo.getYearMonth(), ddo.getStockServeNum());
    }

    private void splitToMapCur(DDO ddo, Map<String, DailyMrpV2DataMap> resultMap) {
        String key = ddo.key("新增");
        DailyMrpV2DataMap o = resultMap.get(key);
        if (o == null) {
            o = DDO.copy(ddo);
            o.setDemandType("新增");
            resultMap.put(key, o);
        }
        o.putTarget(DailyNotDistinctCurBillNum.staticTargetName(), ddo.getYearMonth(), ddo.getStockBillNum());
        o.putTarget(DailyNotDistinctCurServeNum.staticTargetName(), ddo.getYearMonth(), ddo.getStockServeNum());
    }

    private void splitToMap(DDO ddo, String demandType, Map<String, DailyMrpV2DataMap> resultMap,
                            Boolean isBill) {
        splitToMap(ddo, demandType, resultMap, isBill,false);
    }

    private void splitToMap(DDO ddo, String demandType, Map<String, DailyMrpV2DataMap> resultMap,
            Boolean isBill,Boolean isLongTail) {
        String key = ddo.key(demandType);
        DailyMrpV2DataMap o = resultMap.get(key);
        if (o == null) {
            o = DDO.copy(ddo);
            o.setDemandType(demandType);
            resultMap.put(key, o);
        }
        mTargetSet(ddo, o, isBill);
//        if (isLongTail){
//            // 中长尾数据
//            longMTargetSet(ddo, o, isBill);
//        }else {
//            mTargetSet(ddo, o, isBill);
//        }
    }

    private void splitToMap(TailDDO ddo, String demandType, Map<String, DailyMrpV2DataMap> resultMap,
            Boolean isBill) {
        splitToMap(ddo,demandType, resultMap,isBill,false );
    }

    private void splitToMap(TailDDO ddo, String demandType, Map<String, DailyMrpV2DataMap> resultMap,
                            Boolean isBill,boolean isNewCategory) {
        String key = ddo.key(demandType);
        DailyMrpV2DataMap o = resultMap.get(key);
        if (o == null) {
            o = TailDDO.copy(ddo,isNewCategory);
            o.setDemandType(demandType);
            resultMap.put(key, o);
        }
        mTargetSet(ddo, o, isBill,isNewCategory);
    }


    @Override
    public Map<String, DailyMrpV2DataMap> loadComplexData(Map<String, Object> shares) {
        LocalDate date = (LocalDate) shares.get("date");
        String yearMonthEnd = DateUtils.format(date, "yyyy-MM");
        String endStatTime = ckcldStdCrpDBHelper.getRawOne(String.class,
                "select stat_time from dwd_txy_scale_df "
                        + "where stat_time >= ? "
                        + "order by stat_time desc", yearMonthEnd + "-01");

        String yearMonthStart = (String) shares.get("year_month_start");
        List<YearMonth> yms = cloud.demand.app.common.utils.DateUtils.listYearMonth(yearMonthStart, yearMonthEnd);
        List<String> queryStatTimes = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        for (int i = 0; i < yms.size(); i++) {
            if (i == yms.size() - 1) {
                continue;
            }
            YearMonth ym = yms.get(i);
            calendar.set(ym.getYear(), ym.getMonth() - 1, 1);
            queryStatTimes.add(ym.toDateStr() + "-" + calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        }
        queryStatTimes.add(endStatTime);

        String sql = loadSql();
        Map<String, DailyMrpV2DataMap> resultMap = new HashMap<>();

        CleanService bean = SpringUtil.getBean(CleanService.class);

        MyMapUtils.putTargetGenTime(shares, "月切片变化量");
        for (long ii = 0; ii < queryStatTimes.size(); ii++) {
            String statTime = queryStatTimes.get((int) ii);
            long start = System.currentTimeMillis();
            List<DDO> rawResult = ckcldStdCrpDBHelper.getRaw(DDO.class,
                    sql, statTime, statTime, statTime, statTime);
            log.info("mrp d scale {} count: {}", statTime, rawResult.size());
            if (!CollectionUtils.isEmpty(rawResult)) {
                for (long jj = 0; jj < rawResult.size(); jj++) {
                    DDO ddo = rawResult.get((int) jj);
                    bean.clean(ddo);
                    // 如果不是中长尾客户，才写入diff信息
                    // 中长尾客户的信息由另一个diff生成
                    if ("中长尾".equals(ddo.getOrderType())
                            && Objects.equals(ddo.getProjectType(), ProjectTypeEnum.NORMAL_PROJECT.getName())
                            && Objects.equals(ddo.getCustomerType(),Constant.TAIL_CUSTOMER)
                            && "CVM".equals(ddo.getDataProduct())) {
                        ddo.setIsNewCategory(IsNewCategoryEnum.YEAS.getCode());
                        ddo.setCustomerType(Constant.TAIL_CUSTOMER);
//                        ddo.setIndustryDept(Constant.EMPTY_VALUE);
                        ddo.setTempZoneName(Constant.EMPTY_VALUE);
                        ddo.setUin("0");
                    }
                    if (ddo.diffBillNum.compareTo(BigDecimal.ZERO) > 0) {
                        splitToMap(ddo, "新增", resultMap, true);
                    } else if (ddo.diffBillNum.compareTo(BigDecimal.ZERO) < 0) {
                        splitToMap(ddo, "退回", resultMap, true);
                    }
                    if (ddo.diffServeNum.compareTo(BigDecimal.ZERO) > 0) {
                        splitToMap(ddo, "新增", resultMap, false);
                    } else if (ddo.diffServeNum.compareTo(BigDecimal.ZERO) < 0) {
                        splitToMap(ddo, "退回", resultMap, false);
                    }
                    // 存量
                    splitToMapCur(ddo, resultMap);
                }
            }
            log.info("mrp d scale {} end: {}", statTime, System.currentTimeMillis() - start);
        }

        return resultMap;
    }

    @Data
    @Table("virtual_table")
    public static class TailDDO {

        /** 订单类型<br/>Column: [order_type] */
        @Column(value = "order_type")
        private String orderType;

        /** 业务分类<br/>Column: [biz_type] */
        @Column(value = "biz_type")
        private String bizType;

        /** 地域类型<br/>Column: [region_type] */
        @Column(value = "region_type")
        private String regionType;

        /** 地域<br/>Column: [region_name] */
        @Column(value = "region_name")
        private String regionName;

        /** 可用区<br/>Column: [zone_name] */
        @Column(value = "zone_name")
        private String zoneName;

        /** 实例类型<br/>Column: [gins_family] */
        @Column(value = "gins_family")
        private String ginsFamily;

        /** 客户类型<br/>Column: [customer_type] */
        @Column(value = "customer_type")
        private String customerType;

        private String demandType;

        @Column(value = "data_product")
        private String dataProduct;

        @Column(value = "year_month")
        private String yearMonth;

        @Column(value = "diff_bill_num")
        private BigDecimal diffBillNum;

        @Column(value = "diff_serve_num")
        private BigDecimal diffServeNum;

        @Column("stock_bill_num")
        private BigDecimal stockBillNum;

        @Column("stock_serve_num")
        private BigDecimal stockServeNum;

        private Integer isAddOrEla;

        public String key(String demandType) {
            return String.join("@", null, getBizType(), null,
                    null, getRegionName(), getZoneName(),
                    getGinsFamily(), null, demandType, getDataProduct(), getCustomerType(),
                    null, null, null);
        }

        public static DailyMrpV2DataMap copy(TailDDO ddo,boolean isNewCategory) {
            DailyMrpV2DataMap dailyMrpV2DataMap = new DailyMrpV2DataMap();
            dailyMrpV2DataMap.setNewCustomerType(NewCustomerTypeEnum.getNameByBol(false));
            dailyMrpV2DataMap.setIsNewCategory(IsNewCategoryEnum.getCode(isNewCategory));
            dailyMrpV2DataMap.setOrderType(ddo.getOrderType());
            dailyMrpV2DataMap.setBizType(ddo.getBizType());
            dailyMrpV2DataMap.setRegionType(ddo.getRegionType());
            dailyMrpV2DataMap.setRegionName(ddo.getRegionName());
            dailyMrpV2DataMap.setZoneName(ddo.getZoneName());
            dailyMrpV2DataMap.setGinsFamily(ddo.getGinsFamily());
            dailyMrpV2DataMap.setCustomerType(ddo.getCustomerType());
            dailyMrpV2DataMap.setDemandType(ddo.getDemandType());
            dailyMrpV2DataMap.setDataProduct(ddo.getDataProduct());
            dailyMrpV2DataMap.setIsAddOrEla(ddo.getIsAddOrEla());
            return dailyMrpV2DataMap;
        }
    }

    @Data
    @Table("virtual_table")
    public static class DDO implements ICleanBizType {

        /** 订单类型<br/>Column: [order_type] */
        @Column(value = "order_type")
        private String orderType;

        /** 业务分类<br/>Column: [biz_type] */
        @Column(value = "r_biz_type")
        private String bizType;

        /** 新客户分类 */
        @Column("new_customer_type")
        private String newCustomerType;

        /** 项目类型 */
        @Column("project_type")
        private String projectType;

        /** 产品 */
        @Column("product_class")
        private String productClass;


        /** 行业/产品(也叫行业/部门)<br/>Column: [industry_or_product] */
        @Column(value = "industry_or_product")
        private String industryOrProduct;

        /** 行业部门<br/>Column: [industry_dept] */
        @Column(value = "industry_dept")
        private String industryDept;

        /** 地域类型<br/>Column: [region_type] */
        @Column(value = "region_type")
        private String regionType;

        /** 地域<br/>Column: [region_name] */
        @Column(value = "region_name")
        private String regionName;

        /** 可用区<br/>Column: [zone_name] */
        @Column(value = "zone_name")
        private String zoneName;

        /** 实例类型<br/>Column: [gins_family] */
        @Column(value = "gins_family")
        private String ginsFamily;

        /** uin<br/>Column: [uin] */
        @Column(value = "uin")
        private String uin;

        /** 客户内外部属性<br/>Column: [customer_inside_or_outside] */
        @Column(value = "customer_inside_or_outside")
        private String customerInsideOrOutside;

        /** 客户类型<br/>Column: [customer_type] */
        @Column(value = "r_customer_type")
        private String customerType;

        /** 战区名<br/>Column: [war_zone_name] */
        @Column(value = "war_zone_name")
        private String warZoneName;

        /** 客户集团<br/>Column: [customer_group] */
        @Column(value = "customer_group")
        private String customerGroup;

        /** 客户简称<br/>Column: [customer_short_name] */
        @Column(value = "customer_short_name")
        private String customerShortName;

        /** 客户名称<br/>Column: [customer_name] */
        @Column(value = "customer_name")
        private String customerName;

        /** 客户个人类型, 个人/企业<br/>Column: [customer_name] */
        @Column(value = "customer_person_type")
        private String customerPersonType;

        @Column("app_role")
        private String appRole;

        private String demandType;

        private Integer isNewCategory;

        private String tempZoneName;

        @Column(value = "data_product")
        private String dataProduct;

        @Column(value = "year_month")
        private String yearMonth;

        @Column("diff_bill_num")
        private BigDecimal diffBillNum;
        @Column("diff_serve_num")
        private BigDecimal diffServeNum;
        @Column("stock_bill_num")
        private BigDecimal stockBillNum;
        @Column("stock_serve_num")
        private BigDecimal stockServeNum;

        public String key(String demandType) {
            return String.join("@", getOrderType(), getBizType(), getNewCustomerType(),getProjectType(), getProductClass(), getIndustryOrProduct(),
                    getIndustryDept(), getRegionName(), getZoneName(),
                    getGinsFamily(), getUin(), demandType, getDataProduct(), getCustomerType(),
                    getCustomerName(), getCustomerShortName(), getWarZoneName(),getAppRole());
        }

        public static DailyMrpV2DataMap copy(DDO ddo) {
            DailyMrpV2DataMap dailyMrpV2DataMap = new DailyMrpV2DataMap();
            dailyMrpV2DataMap.setOrderType(ddo.getOrderType());
            dailyMrpV2DataMap.setBizType(ddo.getBizType());
            dailyMrpV2DataMap.setNewCustomerType(ddo.getNewCustomerType()); // 新客户分类
            dailyMrpV2DataMap.setProjectType(ddo.getProjectType()); // 项目类型
            dailyMrpV2DataMap.setProductClass(ddo.getProductClass()); // 产品
            dailyMrpV2DataMap.setIndustryOrProduct(ddo.getIndustryOrProduct());
            dailyMrpV2DataMap.setIndustryDept(ddo.getIndustryDept());
            dailyMrpV2DataMap.setRegionType(ddo.getRegionType());
            dailyMrpV2DataMap.setRegionName(ddo.getRegionName());
            dailyMrpV2DataMap.setZoneName(ddo.getZoneName());
            dailyMrpV2DataMap.setGinsFamily(ddo.getGinsFamily());
            dailyMrpV2DataMap.setUin(ddo.getUin());
            dailyMrpV2DataMap.setCustomerInsideOrOutside(ddo.getCustomerInsideOrOutside());
            dailyMrpV2DataMap.setCustomerPersonType(ddo.getCustomerPersonType());
            dailyMrpV2DataMap.setCustomerType(ddo.getCustomerType());
            dailyMrpV2DataMap.setWarZoneName(ddo.getWarZoneName());
            dailyMrpV2DataMap.setCustomerGroup(ddo.getCustomerGroup());
            dailyMrpV2DataMap.setCustomerShortName(ddo.getCustomerShortName());
            dailyMrpV2DataMap.setCustomerName(ddo.getCustomerName());
            dailyMrpV2DataMap.setDemandType(ddo.getDemandType());
            dailyMrpV2DataMap.setDataProduct(ddo.getDataProduct());
            dailyMrpV2DataMap.setIsNewCategory(ddo.getIsNewCategory());
            dailyMrpV2DataMap.setAppRole(ddo.getAppRole());
            if (StringUtils.isNotBlank(ddo.getTempZoneName())){
                dailyMrpV2DataMap.setTempZoneName(ddo.getTempZoneName());
            }
            return dailyMrpV2DataMap;
        }
    }
}
