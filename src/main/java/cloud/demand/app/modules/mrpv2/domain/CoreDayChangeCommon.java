package cloud.demand.app.modules.mrpv2.domain;

import cloud.demand.app.modules.mrpv2.entity.MrpV2ForecastMatchRateCoreDayChangeDO;
import cloud.demand.app.modules.mrpv2.enums.CoreDayStatusEnum;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CoreDayChangeCommon {
    /** 行业部门<br/>Column: [industry_dept] */
    @NotBlank(message = "行业部门不能为空")
    private String industryDept;

    /** 客户简称<br/>Column: [customer_short_name] */
    @NotBlank(message = "客户简称不能为空")
    private String customerShortName;

    /** 年月，格式：yyyy-MM<br/>Column: [year_montrh] */
    @NotBlank(message = "年月能为空")
    private String yearMonth;

    /** 需求类型 */
    @NotBlank(message = "需求类型只能为新增，弹性或者退回")
    private String demandType;

    /** 行业预测准确率提升<br/>Column: [origin_up] */
    @NotNull(message = "行业预测准确率提升不能为空")
    @DecimalMax(value = "100",message = "行业预测准确率提升不能高于100")
    @DecimalMin(value = "0",message = "行业预测准确率提升不能低于于0")
    private BigDecimal industryOriginUp;

    /** 运管干预预测准确率提升<br/>Column: [current_up] */
    @NotNull(message = "运管干预预测准确率提升不能为空")
    @DecimalMax(value = "100",message = "运管干预预测准确率提升不能高于100")
    @DecimalMin(value = "0",message = "运管干预预测准确率提升不能低于于0")
    private BigDecimal industryCurrentUp;

    /** 行业预测准确率提升<br/>Column: [origin_up] */
    @NotNull(message = "行业预测准确率提升不能为空")
    @DecimalMax(value = "100",message = "行业预测准确率提升不能高于100")
    @DecimalMin(value = "0",message = "行业预测准确率提升不能低于于0")
    private BigDecimal customerOriginUp;

    /** 运管干预预测准确率提升<br/>Column: [current_up] */
    @NotNull(message = "运管干预预测准确率提升不能为空")
    @DecimalMax(value = "100",message = "运管干预预测准确率提升不能高于100")
    @DecimalMin(value = "0",message = "运管干预预测准确率提升不能低于于0")
    private BigDecimal customerCurrentUp;

    /** 干涉类型：机型引导，可用区引导，节奏引导，跨月矫正，其他<br/>Column: [change_type] */
    @Column(value = "change_type")
    @Pattern(regexp = "机型引导|可用区引导|节奏引导|跨月矫正|其他", message = "干涉类型只能为机型引导|可用区引导|节奏引导|跨月矫正|其他")
    private String changeType;

    /** 干涉原因<br/>Column: [change_result] */
    @NotBlank(message = "干涉原因不能为空")
    @Column(value = "change_result")
    private String changeResult;

    public static MrpV2ForecastMatchRateCoreDayChangeDO transForm(CoreDayChangeCommon common){
        if (common == null){
            return null;
        }
        MrpV2ForecastMatchRateCoreDayChangeDO ret = new MrpV2ForecastMatchRateCoreDayChangeDO();
        ret.setChangeType(common.getChangeType());
        ret.setStatus(CoreDayStatusEnum.OPEN.getName());
        ret.setChangeResult(common.getChangeResult());
        ret.setYearMonth(common.getYearMonth());
        ret.setCustomerShortName(common.getCustomerShortName());
        ret.setIndustryDept(common.getIndustryDept());
        ret.setDemandType(common.getDemandType());
        ret.setIndustryOriginUp(common.getIndustryOriginUp());
        ret.setIndustryCurrentUp(common.getIndustryCurrentUp());
        ret.setCustomerOriginUp(common.getCustomerOriginUp());
        ret.setCustomerCurrentUp(common.getCustomerCurrentUp());
        return ret;
    }
}
