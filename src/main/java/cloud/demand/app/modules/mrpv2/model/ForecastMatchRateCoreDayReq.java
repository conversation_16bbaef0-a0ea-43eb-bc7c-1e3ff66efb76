package cloud.demand.app.modules.mrpv2.model;

import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.enums.CoreDayDimTypeEnum;
import cloud.demand.app.modules.mrpv2.web.QueryTableReq;
import cloud.demand.app.modules.mrpv3.dto.req.MrpV3ReportReq;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
public class ForecastMatchRateCoreDayReq {

    /** 起始年月 */
    private String startYearMonth;

    /** 结束年月 */
    private String endYearMonth;

    /** 需求类型 */
    private List<String> demandType;

    /** 行业部门 */
    private List<String> industryDept;

    /** 客户简称 */
    private List<String> customerShortName;

    /** 预测状态 {@link cloud.demand.app.modules.mrpv2.enums.ForecastStatus */
    private String forecastStatus;

    private String dimType;

    public static ForecastMatchRateCoreDayReq transForm(MrpV3ReportReq req,String forecastStatus,boolean isNewOrderType,boolean needCustomer) {
        ForecastMatchRateCoreDayReq coreDayReq = new ForecastMatchRateCoreDayReq();
        List<String> demandType = req.getDemandType();
        demandType = demandType == null ? null : demandType.stream().filter(item-> !Objects.equals(Constant.EMPTY_VALUE,item)).collect(Collectors.toList());;
        List<String> industryDept = req.getIndustryDept();
        List<String> customerShortName = req.getCustomerShortName();

        coreDayReq.setDemandType(demandType);
        coreDayReq.setForecastStatus(forecastStatus);
        coreDayReq.setIndustryDept(industryDept);
        coreDayReq.setCustomerShortName(customerShortName);
        String start = req.getStartYearMonth();
        String end = req.getEndYearMonth();
        // 新客户分类最小 2024-07，老客户分类最大 2024-06
        if (isNewOrderType){
            start = SoeCommonUtils.max(start,"2024-07");
        }else {
            end = SoeCommonUtils.min(end,"2024-06");
        }
        coreDayReq.setEndYearMonth(end);
        coreDayReq.setStartYearMonth(start);
        coreDayReq.setDimType(CoreDayDimTypeEnum.getName(needCustomer));
        return coreDayReq;
    }

    public static ForecastMatchRateCoreDayReq transForm(QueryTableReq req,boolean needCustomer) {
        ForecastMatchRateCoreDayReq coreDayReq = new ForecastMatchRateCoreDayReq();
        List<String> demandTypes = req.getDemandTypes();
        demandTypes = demandTypes == null ? null : demandTypes.stream().filter(item-> !Objects.equals(Constant.EMPTY_VALUE,item)).collect(Collectors.toList());;
        List<String> industryDepts = req.getIndustryDepts();
        List<String> customerShortNames = req.getCustomerShortNames();

        // choose锁定信息处理
        if (req.getChoose()!=null){
            QueryTableReq.Choose choose = req.getChoose();
            String industryOrProduct = choose.getIndustryOrProduct();
            if (industryOrProduct!=null){
                industryDepts = Collections.singletonList(industryOrProduct);
            }
            String customerShortName = choose.getCustomerShortName();
            if (customerShortName!=null){
                customerShortNames = Collections.singletonList(customerShortName);
            }
        }

        coreDayReq.setDemandType(demandTypes);
        coreDayReq.setForecastStatus(req.getForecastStatus());
        coreDayReq.setIndustryDept(industryDepts);
        coreDayReq.setCustomerShortName(customerShortNames);
        String start = req.getTimePeriod().getStart();
        String end = req.getTimePeriod().getEnd();
        boolean isNewOrderType = BooleanUtils.isTrue(req.getIsNewOrderType()); // 是否为新客户分类
        // 新客户分类最小 2024-07，老客户分类最大 2024-06
        if (isNewOrderType){
            start = SoeCommonUtils.max(start,"2024-07");
        }else {
            end = SoeCommonUtils.min(end,"2024-06");
        }
        coreDayReq.setEndYearMonth(end);
        coreDayReq.setStartYearMonth(start);
        coreDayReq.setDimType(CoreDayDimTypeEnum.getName(needCustomer));
        return coreDayReq;
    }
}
