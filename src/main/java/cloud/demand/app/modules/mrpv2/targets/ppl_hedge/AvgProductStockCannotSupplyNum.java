package cloud.demand.app.modules.mrpv2.targets.ppl_hedge;

import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import java.util.List;
import java.util.Map;

public class AvgProductStockCannotSupplyNum extends TargetTemplate {

    @Override
    public String targetName() {
        return "avg_product_stock_cannot_supply_num";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "云产品库存无法满足量",
                new RspFieldHeader("", "供应计划", null));
    }

    @Override
    public String targetDisplay() {
        return "532版";
    }


    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }
}
