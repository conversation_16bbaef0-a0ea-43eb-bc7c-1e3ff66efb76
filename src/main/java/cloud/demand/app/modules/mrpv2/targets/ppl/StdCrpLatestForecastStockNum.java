package cloud.demand.app.modules.mrpv2.targets.ppl;

import cloud.demand.app.modules.mrpv2.entity.TailPplForecastDO;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;

import java.util.List;
import java.util.Map;

public class StdCrpLatestForecastStockNum extends StdCrpForecastStockNum{
    @Override
    public String targetName() {
        return "latest_forecast_stock_num";
    }

    @Override
    public String targetDisplay() {
        return "最新版";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "预测存量(弹性)",
                new RspFieldHeader("", "需求计划", null));
    }

    @Override
    public List<TailPplForecastDO> loadTailPplForecastDO(Map<String, Object> shares, String yearMonthStart) {
        return loadTailForecastAndPutShares(shares, yearMonthStart).get(1);
    }
}
