package cloud.demand.app.modules.mrpv2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/** 新客户分类 */
@Getter
@AllArgsConstructor
public enum NewCustomerTypeEnum {

    HEADER("头部客户"),

    LONG_TAIL("中长尾"),


    ;

    private final String name;


    /**
     * 通过 bol 获取客户分类名称
     * @param isHeader 是否为头部
     * @return 客户分类
     */
    public static String getNameByBol(boolean isHeader){
        return isHeader? HEADER.getName() : LONG_TAIL.getName();
    }
}
