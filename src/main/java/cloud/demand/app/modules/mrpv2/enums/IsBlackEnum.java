package cloud.demand.app.modules.mrpv2.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum IsBlackEnum {
    ORIGIN(0,"黑名单处理原始数据"),
    BLACK(1,"黑名单处理之后的数据"),
    NOT_BLACK(-1,"非黑名单,不用处理")
    ;

    private final Integer code;

    private final String name;

    public static String getName(Integer code){
        if (code == null){
            return NOT_BLACK.getName();
        }
        for (IsBlackEnum value : values()) {
            if (value.code.equals(code)){
                return value.getName();
            }
        }
        return NOT_BLACK.getName();
    }
}
