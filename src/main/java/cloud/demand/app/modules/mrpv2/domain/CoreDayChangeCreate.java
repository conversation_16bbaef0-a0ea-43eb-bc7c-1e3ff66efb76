package cloud.demand.app.modules.mrpv2.domain;

import cloud.demand.app.modules.mrpv2.entity.MrpV2ForecastMatchRateCoreDayChangeDO;
import cloud.demand.app.modules.mrpv2.enums.CoreDayStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CoreDayChangeCreate extends CoreDayChangeCommon{
    public static MrpV2ForecastMatchRateCoreDayChangeDO transForm(CoreDayChangeCreate create){
        return CoreDayChangeCommon.transForm(create);
    }
}
