package cloud.demand.app.modules.mrpv2.alert.entity;

import cloud.demand.app.modules.mrpv2.alert.domain.AlterRes;
import cloud.demand.app.modules.mrpv2.alert.util.ValidateUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class AbstractAlter implements IAlter{
    private Boolean isOk;
    private List<String> errors;

    @Override
    public boolean isOk() {
        if (isOk == null){
            errors = ValidateUtil.valid(this);
            isOk = errors == null;
        }
        return isOk;
    }


    @Override
    public String getMsg() {
        return isOk()? AlterRes.SUCCESS: StringUtils.join(errors,"\n");
    }
}
