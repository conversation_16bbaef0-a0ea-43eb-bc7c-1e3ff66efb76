package cloud.demand.app.modules.mrpv2.targets.scaling;

import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import java.util.List;
import java.util.Map;

public class CurServeNum extends ScaleNum {

    public static String staticTargetName() {
        return "cur_serve_num";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }

    @Override
    public String targetDisplay() {
        return "服务存量";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "购买执行", null);
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }

    @Override
    public TargetTemplate dailyNotDistinct() {
        return new DailyNotDistinctCurServeNum();
    }

    @Override
    public TargetTemplate monthAvgNotDistinct() {
        return new MonthAvgNotDistinctCurServeNum();
    }

    @Override
    public TargetTemplate dailyDistinct() {
        return new DailyNotDistinctCurServeNum();
    }

    @Override
    public TargetTemplate monthAvgDistinct() {
        return new MonthAvgNotDistinctCurServeNum();
    }
}
