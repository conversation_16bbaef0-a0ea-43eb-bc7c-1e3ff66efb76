package cloud.demand.app.modules.mrpv2.task.work;

import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.mrpv2.domain.ForecastMatchRateReq;
import cloud.demand.app.modules.mrpv2.domain.ForecastMatchRateResp;
import cloud.demand.app.modules.mrpv2.entity.DailyMrpV2ForecastCustomerRateDO;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.MrpV2RateTotalDemandTypeEnum;
import cloud.demand.app.modules.mrpv2.enums.SimpleTaskEnum;
import cloud.demand.app.modules.mrpv2.service.CloudResourceOperatorService;
import cloud.demand.app.modules.mrpv2.service.QueryDataService;
import cloud.demand.app.modules.mrpv2.task.process.SimpleCommonTaskProcess;
import cloud.demand.app.modules.soe.enums.DefaultFlagEnum;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.work.AbstractSopWork;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/** 客户级别准确率存表任务 */
@Service
@Slf4j(topic = "客户级别准确率存表任务：")
public class MrpV2RateCustomerWork extends AbstractSopWork<SimpleCommonTask> {
    @Resource
    private SimpleCommonTaskProcess process;

    /** 提供给 行业公有云资源运营驾驶舱 的服务 */
    @Resource
    private CloudResourceOperatorService cloudResourceOperatorService;

    /** 预测准确率写的库 */
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Override
    public ISopProcess<SimpleCommonTask> getTask() {
        return process;
    }

    @Override
    public ITaskEnum getEnum() {
        return SimpleTaskEnum.MRP_V2_RATE_CUSTOMER;
    }

    // 两分钟一次
    @Scheduled(fixedRate = 120 * 1000)
    @Override
    public void work() {
        super.work();
    }

    /** 9:40生成 */
    @Scheduled(cron = "0 40 9 * * ?")
    public void initForecastRateTask(){
        initTask(null);
    }

    /** 初始化任务 */
    public void initTask(LocalDate statTime){
        if (statTime == null){
            statTime = LocalDate.now();
        }
        SimpleCommonTask simpleCommonTask = new SimpleCommonTask();
        simpleCommonTask.setVersion(DateUtils.format(statTime));
        process.initTask(simpleCommonTask,getEnum());
    }

    @Override
    public void doWork(SimpleCommonTask task) {
        // version 即 切片时间 （格式：yyyy-MM-dd）
        String version = task.getVersion();
        generateForecastRate(LocalDate.parse(version));
    }

    /** 客户级别准确率存表 */
    public void generateForecastRate(LocalDate time){
        if (time == null){
            time = LocalDate.now();
        }
        String version = DateUtils.format(time);
        // step1：清理切片版本数据
        CkDBUtils.delete(ckcldStdCrpDBHelper,version,DailyMrpV2ForecastCustomerRateDO.class);
        // step2：遍历写入ck，需求类型 x 3 & 干预 x 2
        for (MrpV2RateTotalDemandTypeEnum value : MrpV2RateTotalDemandTypeEnum.values()) { // 需求类型
            for (DefaultFlagEnum isInterventionEnum : DefaultFlagEnum.values()) { // 干预前后（0：干预前，1：干预后）
                for (DefaultFlagEnum isCommonInstanceTypeEnum : DefaultFlagEnum.values()) { // 是否合并机型
                    doGenerateForecastRate(time,value.getName(),isCommonInstanceTypeEnum.getBol(),isInterventionEnum.getBol());
                }
            }
        }
    }

    /**
     * 生成客户级别的准率率（维度：行业部门 @ 客户简称 @ 地域 @ 实例类型）
     * @param statTime 切片时间
     * @param demandType 需求类型 {@link MrpV2RateTotalDemandTypeEnum}
     * @param isCommonInstanceType 是否合并机型 {@link DefaultFlagEnum}
     * @param isIntervention 是否干预 {@link DefaultFlagEnum}
     */
    private void doGenerateForecastRate(LocalDate statTime,String demandType,boolean isCommonInstanceType,boolean isIntervention){
        if (statTime == null){
            statTime = LocalDate.now(); // 默认当天
        }
        String version = DateUtils.format(statTime);
        // step1：获取准确率，调用驾驶舱接口，底层是行业数据看板接口
        List<ForecastMatchRateResp.Item> forecastData = getForecastData(statTime, demandType, isCommonInstanceType, isIntervention);
        // 生产环境不能为空
        if (ListUtils.isEmpty(forecastData) && EnvUtils.isProduction()){
            throw new ITException(String.format("获取客户维度行业数据看板准率集合为空，切片时间：【%s】", version));
        }
        // step2：封装实体类
        List<DailyMrpV2ForecastCustomerRateDO> saveData = new ArrayList<>();
        for (ForecastMatchRateResp.Item forecastDatum : forecastData) {
            DailyMrpV2ForecastCustomerRateDO saveItem = DailyMrpV2ForecastCustomerRateDO.transform(forecastDatum);
            saveItem.setStatTime(statTime);
            saveItem.setProductType(ProductTypeEnum.CVM.getCode());
            saveItem.setIsIntervention(DefaultFlagEnum.getCodeByBol(isIntervention));
            saveItem.setDemandType(demandType);
            saveItem.setIsCommonInstanceType(DefaultFlagEnum.getCodeByBol(isCommonInstanceType));
            saveData.add(saveItem);
        }
        // step3：写入CK
        log.info(String.format("写入ck，切片时间：【%s】，size：【%s】", version,saveData.size()));
        for (List<DailyMrpV2ForecastCustomerRateDO> oneBatchData : ListUtils.groupByNum(saveData, 50000)) {
            ckcldStdCrpDBHelper.insertBatchWithoutReturnId(oneBatchData);
        }
    }

    /** 获取准确率数据 */
    private List<ForecastMatchRateResp.Item> getForecastData(LocalDate statTime, String demandType, boolean isCommonInstanceType, boolean isIntervention){
        ForecastMatchRateReq req = new ForecastMatchRateReq();
        req.setStatTime(statTime);
        req.setStartYearMonth("2024-01"); // 默认从24年开始
        req.setEndYearMonth(statTime.format(DateTimeFormatter.ofPattern("yyyy-MM"))); // 切片日期所在年月
        req.setProduct(ProductTypeEnum.CVM.getCode()); // 默认CVM
        req.setDims(ListUtils.newArrayList( // 维度，需要四个维度数据做top查询，提供给驾驶舱使用
                "industryDept", // 行业部门
                "warZoneName", // 战区
                "customerShortName", // 客户简称
                "regionName", // 地域
                "instanceType" // 实例类型
        ));
        req.setDemandType(demandType); // 需求类型
        req.setIsIntervention(isIntervention); // 是否干预
        req.setIsCommonInstanceType(isCommonInstanceType); // 是否合并机型
        req.setCustomhouseTitle(ListUtils.newArrayList("境内","境外"));

        return cloudResourceOperatorService.queryForecastMatchRateWithOutCache(req); // 不带缓存查询
    }

}
