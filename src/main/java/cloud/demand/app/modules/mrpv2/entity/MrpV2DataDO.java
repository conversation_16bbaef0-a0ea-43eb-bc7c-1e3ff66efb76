package cloud.demand.app.modules.mrpv2.entity;


// package a.b.c;

import java.math.BigDecimal;

import cloud.demand.app.modules.mrpv2.model.clean.AbstractCleanDemand;
import cloud.demand.app.modules.mrpv2.model.clean.ICleanDemand;
import cloud.demand.app.modules.mrpv2.model.clean.ICleanWarZone;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("virtual_table")
public class MrpV2DataDO extends AbstractCleanDemand implements ICleanWarZone {

    // 匹配大宽表的key字段 开始

    /** 订单类型<br/>Column: [order_type] */
    @Column(value = "order_type")
    private String orderType;

    /** 业务分类<br/>Column: [biz_type] */
    @Column(value = "biz_type")
    private String bizType;

    /** 行业/产品(也叫行业/部门)<br/>Column: [industry_or_product] */
    @Column(value = "industry_or_product")
    private String industryOrProduct;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 地域类型<br/>Column: [region_type] */
    @Column(value = "region_type")
    private String regionType;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 实例类型<br/>Column: [gins_family] */
    @Column(value = "gins_family")
    private String ginsFamily;

    /** uin<br/>Column: [uin] */
    @Column(value = "uin")
    private String uin;

    /** 客户内外部属性<br/>Column: [customer_inside_or_outside] */
    @Column(value = "customer_inside_or_outside")
    private String customerInsideOrOutside;

    /** 客户类型<br/>Column: [customer_type] */
    @Column(value = "customer_type")
    private String customerType;

    /** 战区名<br/>Column: [war_zone_name] */
    @Column(value = "war_zone_name")
    private String warZoneName;

    /** 客户集团<br/>Column: [customer_group] */
    @Column(value = "customer_group")
    private String customerGroup;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 客户个人类型, 个人/企业<br/>Column: [customer_name] */
    @Column(value = "customer_person_type")
    private String customerPersonType;

    /** 需求类型<br/>Column: [demand_type] */
    @Column(value = "demand_type")
    private String demandType;

    @Column(value = "data_product")
    private String dataProduct;

    /** 产品 */
    @Column(value = "product_class")
    private String productClass;

    /** 毛刺 */
    @Column(value = "is_spike")
    private Integer isSpike;

    private String appRole;

    private Integer isAddOrEla;

    private Integer isNewCategory;

    private Integer isBlack;

    private String tempZoneName;


    // 匹配大宽表的key字段 结束

    // 时间及数值字段 开始

    @Column(value = "year_month")
    private String yearMonth;
    @Column(value = "num")
    private BigDecimal num;
    // 时间及数值字段 结束

    public String key() {
        // 适当去掉一些关联出来的字段，如境内外，uin关联的客户信息
        return String.join("@", orderType, bizType, industryOrProduct, industryDept,getProductClass(),getNewCustomerType(),getProjectType(),
                regionName, zoneName, ginsFamily, uin, demandType, dataProduct, customerType,
                customerName, customerShortName, warZoneName);
    }

    public String withTimeKey() {
        return String.join("@", key(), yearMonth);
    }

    public static MrpV2DataDO copy(MrpV2DataDO other) {
        MrpV2DataDO mrpV2DataDO = new MrpV2DataDO();
        mrpV2DataDO.setOrderType(other.getOrderType());
        mrpV2DataDO.setBizType(other.getBizType());
        mrpV2DataDO.setIndustryOrProduct(other.getIndustryOrProduct());
        mrpV2DataDO.setIndustryDept(other.getIndustryDept());
        mrpV2DataDO.setRegionType(other.getRegionType());
        mrpV2DataDO.setRegionName(other.getRegionName());
        mrpV2DataDO.setZoneName(other.getZoneName());
        mrpV2DataDO.setGinsFamily(other.getGinsFamily());
        mrpV2DataDO.setUin(other.getUin());
        mrpV2DataDO.setCustomerInsideOrOutside(other.getCustomerInsideOrOutside());
        mrpV2DataDO.setCustomerType(other.getCustomerType());
        mrpV2DataDO.setWarZoneName(other.getWarZoneName());
        mrpV2DataDO.setCustomerGroup(other.getCustomerGroup());
        mrpV2DataDO.setCustomerShortName(other.getCustomerShortName());
        mrpV2DataDO.setCustomerName(other.getCustomerName());
        mrpV2DataDO.setCustomerPersonType(other.getCustomerPersonType());
        mrpV2DataDO.setDemandType(other.getDemandType());
        mrpV2DataDO.setDataProduct(other.getDataProduct());
        mrpV2DataDO.setYearMonth(other.getYearMonth());
        mrpV2DataDO.setNum(other.getNum());
        mrpV2DataDO.setTempZoneName(other.getTempZoneName());
        mrpV2DataDO.setIsBlack(other.getIsBlack());
        mrpV2DataDO.setIsAddOrEla(other.getIsAddOrEla());
        mrpV2DataDO.setAppRole(other.getAppRole());
        mrpV2DataDO.setNewCustomerType(other.getNewCustomerType()); // 新客户分类
        mrpV2DataDO.setProductClass(other.getProductClass()); // 产品
        mrpV2DataDO.setProjectType(other.getProjectType()); // 项目类型
        return mrpV2DataDO;
    }
}
