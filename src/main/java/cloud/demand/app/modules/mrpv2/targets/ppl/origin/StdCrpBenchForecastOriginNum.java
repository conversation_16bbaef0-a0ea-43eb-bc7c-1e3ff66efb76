package cloud.demand.app.modules.mrpv2.targets.ppl.origin;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.mrpv2.targets.ppl.StdCrpBenchForecastNum;
import cloud.demand.app.modules.mrpv2.utils.PaasUtils;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;

public class StdCrpBenchForecastOriginNum extends StdCrpBenchForecastNum {
    @Override
    public String targetName() {
        return "origin_"+super.targetName();
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "预测量(原始)",
                new RspFieldHeader("", "需求计划", null));
    }

    @Override
    public String loadSql() {
        return PaasUtils.sqlReplacePAAS(ORMUtils.getSql("/sql/mrp/origin_bench_forecast_num_std_crp.sql"));
    }
}
