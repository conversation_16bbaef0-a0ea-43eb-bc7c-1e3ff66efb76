package cloud.demand.app.modules.mrpv2.targets.scaling;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.entity.DailyMrpV2DataMap;
import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.enums.NewCustomerTypeEnum;
import cloud.demand.app.modules.mrpv2.enums.ProjectTypeEnum;
import cloud.demand.app.modules.mrpv2.model.clean.*;
import cloud.demand.app.modules.mrpv2.service.CleanService;
import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.utils.MyMapUtils;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.nutz.lang.Lang;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 核天执行量
 */
@Slf4j
public class MonthNotDistinctScaleCoreByDayNum extends TargetTemplate {
    private final DBHelper ckcldStdCrpDBHelper = DBList.ckcldStdCrpDBHelper;

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "购买执行", null);
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }

    @Override
    public String targetName() {
        return "month_not_distinct_scale_core_by_day_num";
    }

    @Override
    public String targetDisplay() {
        return null;
    }

    @Override
    public List<TargetTemplate> complexNode() {
        return Lang.list(new MonthNotDistinctBillCoreByDayNum(), new MonthNotDistinctServeCoreByDayNum());
    }

    @Override
    public Map<String, DailyMrpV2DataMap> loadComplexData(Map<String, Object> shares) {
        String yearMonthStart = (String) shares.get("year_month_start");

        MrpV2DictService mrpV2DictService = SpringUtil.getBean(MrpV2DictService.class); // 策略表

        CleanService cleanService = SpringUtil.getBean(CleanService.class);  // 清洗类

        // 大客户简称名单集合
        List<String> bigCustomerShortName = mrpV2DictService.getBigCustomerShortName();
        // 全部长尾客户毛刺 uin名单集合
        List<String> allLongTailBurrUin = mrpV2DictService.getAllLongTailBurrUin();

        // 读取 sql，并从数据库中获取数据
        String sql = loadSql();
        MyMapUtils.putTargetGenTime(shares, "执行核天量");
        List<DDO> all = ckcldStdCrpDBHelper.getRaw(DDO.class, sql, bigCustomerShortName, allLongTailBurrUin, yearMonthStart);
        Map<String, DailyMrpV2DataMap> result = new HashMap<>();

        for (DDO ddo : all) {

            // 清洗数据
            cleanService.clean(ddo);

            // 只看重点和非中长尾客户
            if (ProjectTypeEnum.KEY_PROJECT.getName().equals(ddo.getProjectType())
            || !"中长尾客户".equals(ddo.getCustomerType() )) {
                if (ddo.billCoreByDayNum.compareTo(BigDecimal.ZERO) > 0) {
                    splitToMap(ddo, "新增", result, true);
                } else if (ddo.billCoreByDayNum.compareTo(BigDecimal.ZERO) < 0) {
                    splitToMap(ddo, "退回", result, true);
                }
                if (ddo.serveCoreByDayNum.compareTo(BigDecimal.ZERO) > 0) {
                    splitToMap(ddo, "新增", result, false);
                } else if (ddo.serveCoreByDayNum.compareTo(BigDecimal.ZERO) < 0) {
                    splitToMap(ddo, "退回", result, false);
                }
            }
        }

        return result;
    }

    private void mTargetSet(DDO ddo, DailyMrpV2DataMap o, Boolean isBill) {
        if (isBill == null) {
            o.putTarget(MonthNotDistinctBillCoreByDayNum.staticTargetName(), ddo.getYearMonth(), ddo.getBillCoreByDayNum());
            o.putTarget(MonthNotDistinctServeCoreByDayNum.staticTargetName(), ddo.getYearMonth(), ddo.getServeCoreByDayNum());
        } else if (isBill) {
            o.putTarget(MonthNotDistinctBillCoreByDayNum.staticTargetName(), ddo.getYearMonth(), ddo.getBillCoreByDayNum());
        } else {
            o.putTarget(MonthNotDistinctServeCoreByDayNum.staticTargetName(), ddo.getYearMonth(), ddo.getServeCoreByDayNum());
        }
    }

    private void splitToMap(DDO ddo, String demandType, Map<String, DailyMrpV2DataMap> resultMap,
                            Boolean isBill) {
        String key = ddo.key(demandType);
        DailyMrpV2DataMap o = resultMap.get(key);
        if (o == null) {
            o = DDO.copy(ddo);
            o.setDemandType(demandType);
            resultMap.put(key, o);
        }
        mTargetSet(ddo, o, isBill);
    }

    @Data
    @Table("virtual_table")
    public static class DDO implements ICleanOrderType, ICleanNewCustomerType, ICleanProjectType, ICleanProductClass, ICleanLongTailBurr {
        // r_biz_type industry_or_product industry_dept region_type region_name zone_name gins_family uin customer_inside_or_outside r_customer_type war_zone_name customer_group customer_short_name
        //       customer_name customer_person_type customer_person_type year_month
        // 匹配大宽表的key字段 开始

        /** 订单类型<br/>Column: [order_type] */
        @Column(value = "order_type")
        private String orderType;

        @Column("r_biz_type")
        private String bizType;

        /** 行业/产品(也叫行业/部门)<br/>Column: [industry_or_product] */
        @Column(value = "industry_or_product")
        private String industryOrProduct;

        /** 行业部门<br/>Column: [industry_dept] */
        @Column(value = "industry_dept")
        private String industryDept;

        /** 境内外<br/>Column: [region_type] */
        @Column(value = "region_type")
        private String regionType;

        /** 地域<br/>Column: [region_name] */
        @Column(value = "region_name")
        private String regionName;

        /** 可用区<br/>Column: [zone_name] */
        @Column(value = "zone_name")
        private String zoneName;

        /** 实例类型<br/>Column: [gins_family] */
        @Column(value = "gins_family")
        private String ginsFamily;

        /** uin<br/>Column: [uin] */
        @Column(value = "uin")
        private String uin;

        @Column(value = "customer_inside_or_outside")
        private String customerInsideOrOutside;

        @Column(value = "r_customer_type")
        private String customerType;

        @Column(value = "war_zone_name")
        private String warZoneName;

        @Column(value = "customer_group")
        private String customerGroup;

        @Column(value = "customer_short_name")
        private String customerShortName;

        @Column(value = "customer_name")
        private String customerName;

        @Column(value = "customer_person_type")
        private String customerPersonType;

        @Column(value = "data_product")
        private String dataProduct;



        // 匹配大宽表的key字段 结束

        // 时间及数值字段 开始

        @Column(value = "year_month")
        private String yearMonth;

        // demand type 根据值的正负确定，正为新增，负为退回
        private String demandType;
        // 时间及数值字段 结束

        @Column("bill_core_by_day_num")
        private BigDecimal billCoreByDayNum;
        @Column("serve_core_by_day_num")
        private BigDecimal serveCoreByDayNum;

        /** 新客户分类 */
        private String newCustomerType;

        private Boolean isBurr;

        private String projectType;

        private String productClass;

        public String key(String demandType) {
            // 适当去掉一些关联出来的字段，如境内外，uin关联的客户信息
            return String.join("@", orderType, bizType, industryOrProduct, industryDept,
                    regionName, zoneName, ginsFamily, uin, demandType, dataProduct, customerType,
                    customerName, customerShortName, warZoneName,getNewCustomerType(),getProjectType(),getProductClass());
        }

        public static DailyMrpV2DataMap copy(DDO other) {
            DailyMrpV2DataMap mrpV2DataDO = new DailyMrpV2DataMap();
            mrpV2DataDO.setOrderType(other.getOrderType());
            mrpV2DataDO.setBizType(other.getBizType());
            mrpV2DataDO.setNewCustomerType(other.getNewCustomerType()); // 都是头部
            mrpV2DataDO.setProjectType(other.getProjectType()); // 都是重点
            mrpV2DataDO.setProductClass(other.getProductClass()); // CVM：CVM&CBS，GPU：(空值)
            mrpV2DataDO.setIndustryOrProduct(other.getIndustryOrProduct());
            mrpV2DataDO.setIndustryDept(other.getIndustryDept());
            mrpV2DataDO.setRegionType(other.getRegionType());
            mrpV2DataDO.setRegionName(other.getRegionName());
            mrpV2DataDO.setZoneName(other.getZoneName());
            mrpV2DataDO.setGinsFamily(other.getGinsFamily());
            mrpV2DataDO.setUin(other.getUin());
            mrpV2DataDO.setCustomerInsideOrOutside(other.getCustomerInsideOrOutside());
            mrpV2DataDO.setCustomerType(other.getCustomerType());
            mrpV2DataDO.setWarZoneName(other.getWarZoneName());
            mrpV2DataDO.setCustomerGroup(other.getCustomerGroup());
            mrpV2DataDO.setCustomerShortName(other.getCustomerShortName());
            mrpV2DataDO.setCustomerName(other.getCustomerName());
            mrpV2DataDO.setCustomerPersonType(other.getCustomerPersonType());
            mrpV2DataDO.setDemandType(other.getDemandType());
            mrpV2DataDO.setDataProduct(other.getDataProduct());
            return mrpV2DataDO;
        }

        @Override
        public String getProduct() {
            return dataProduct;
        }

        @Override
        public String getAppRole() {
            return null;
        }

        @Override
        public String getCustomerUin() {
            return uin;
        }

        @Override
        public String getInstanceType() {
            return ginsFamily;
        }
    }
}
