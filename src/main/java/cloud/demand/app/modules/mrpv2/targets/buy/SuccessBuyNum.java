package cloud.demand.app.modules.mrpv2.targets.buy;

import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import java.util.List;
import java.util.Map;

public class SuccessBuyNum extends TargetTemplate {

    public static String staticTargetName() {
        return "success_buy_num";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }

    @Override
    public String targetDisplay() {
        return "请求成功数";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "API成功率", null);
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }
}
