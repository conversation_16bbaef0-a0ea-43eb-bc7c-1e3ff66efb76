package cloud.demand.app.modules.mrpv2.enums;

import cloud.demand.app.modules.mrpv2.entity.IForecastMatchRateCoreDay;
import cloud.demand.app.modules.mrpv2.entity.MrpV2ForecastMatchRateCoreDayChangeDO;
import cloud.demand.app.modules.mrpv2.service.CacheService;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum SetMatchRateEnum {

    /** 行业原始准确率 */
    IndustryOrigin(true,(item,service)->
            item.setBeforeIndustryOriginMatchRate(service.getCoreDay(MrpV2ForecastMatchRateCoreDayChangeDO.transForm(item,true, CoreDayDimTypeEnum.Industry)))),

    /** 行业云干预准确率 */
    IndustryCurrent(true,(item,service)->
            item.setBeforeIndustryCurrentMatchRate(service.getCoreDay(MrpV2ForecastMatchRateCoreDayChangeDO.transForm(item,false, CoreDayDimTypeEnum.Industry)))),

    /** 客户原始准确率 */
    CustomerOrigin(true,(item,service)->
            item.setBeforeCustomerOriginMatchRate(service.getCoreDay(MrpV2ForecastMatchRateCoreDayChangeDO.transForm(item,true, CoreDayDimTypeEnum.IndustryAndCustomer)))),

    /** 客户云干预准确率 */
    CustomerCurrent(true,(item,service)->
            item.setBeforeCustomerCurrentMatchRate(service.getCoreDay(MrpV2ForecastMatchRateCoreDayChangeDO.transForm(item,false, CoreDayDimTypeEnum.IndustryAndCustomer)))),

    ;
    private final boolean enable;
    private final BiConsumer<IForecastMatchRateCoreDay, CacheService> setFunc;

    /** 允许启用的准确率集合 */
    public final static List<SetMatchRateEnum> matchRateEnumList =
            Arrays.stream(SetMatchRateEnum.values()).filter(SetMatchRateEnum::isEnable).collect(Collectors.toList());

}
