package cloud.demand.app.modules.sop_review.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import cloud.demand.app.modules.sop_return.utils.SopSelectCaseBuilder;
import cloud.demand.app.modules.sop_review.entity.BasSopReviewCoreComponents;
import cloud.demand.app.modules.sop_review.entity.SopDemandAndReturnDO;
import cloud.demand.app.modules.sop_review.entity.dws.DwsSopReviewReportHolidayDO;
import cloud.demand.app.modules.sop_review.enums.BgProductAliasType;
import cloud.demand.app.modules.sop_review.enums.ComponentsFieldEnum;
import cloud.demand.app.modules.sop_review.enums.fields.SopReviewFieldsEnum;
import cloud.demand.app.modules.sop_review.model.req.SopReviewDataCommonReq;
import cloud.demand.app.modules.sop_review.model.req.SopReviewDataDeviceReq;
import cloud.demand.app.modules.sop_review.model.req.SopReviewDataOriginReq;
import cloud.demand.app.modules.sop_review.process.item.SopReviewExcelItem;
import cloud.demand.app.modules.sop_review.process.item.SopReviewItem;
import cloud.demand.app.modules.sop_review.service.ComponentsFieldService;
import cloud.demand.app.modules.sop_review.service.SopGetDataService;
import cloud.demand.app.modules.sop_review.service.SopReviewDictService;
import cloud.demand.app.modules.sop_review.utils.SopReviewUtils;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class SopGetDataServiceImpl implements SopGetDataService {

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private DBHelper prodReadOnlyCkStdCrpDBHelper;


    @Resource
    private SopReviewDictService dictService;


    @Override
    public List<SopDemandAndReturnDO> getSopDemandAndReturn(String version) {
        String sql = ORMUtils.getSql("/sql/sop_review/sop_demand_return_data.sql");
        SimpleSqlBuilder sqlBuilder = new SimpleSqlBuilder();
        sqlBuilder.addParam("version",version);
        sql = sqlBuilder.build(sql);
        return ckcldStdCrpDBHelper.getRaw(SopDemandAndReturnDO.class,sql);
    }

    @Override
    public List<SopReviewItem> getOriginDemand(SopReviewDataOriginReq req) {
        SopWhereBuilder sopWhereBuilder = new SopWhereBuilder(req, DwsSopReviewReportHolidayDO.class);
        ORMUtils.WhereContent where = sopWhereBuilder.where();
        where.addAnd("resource_type != '物理机' or business_type != '自研上云'"); // 原始报表看 cvm 和物理机剔除自研上云

        return getData(req,where);
    }

    @Override
    public List<SopReviewItem> getDeviceDemand(SopReviewDataDeviceReq req) {
        SopWhereBuilder sopWhereBuilder = new SopWhereBuilder(req, DwsSopReviewReportHolidayDO.class);
        ORMUtils.WhereContent where = sopWhereBuilder.where();
        where.addAnd("resource_type != 'CVM'"); // 公司物理机需求报表只看物理机
        List<SopReviewItem> data = getData(req, where); // 核心部件处理
        // 是否包含核心部件，并且首个 dim 为核心部件
        List<String> dims = req.getDims();
        boolean hasCoreComponents = ListUtils.isNotEmpty(dims)
                && dims.contains(SopReviewFieldsEnum.coreComponents.name());
        hasCoreComponents = hasCoreComponents || (
                ListUtils.isNotEmpty(dims)
                        && dims.contains(SopReviewFieldsEnum.componentsField.name())
                );

        // 是否为 合并核心部件
        boolean isCombine = BooleanUtils.isTrue(req.getCombineCoreComponents());

        // 核心部件处理（该属性为多状态属性，同一行多状态要 copy 多份）
        if (hasCoreComponents){
            List<SopReviewItem> newData = new ArrayList<>(data.size());
            BasSopReviewCoreComponents[] entityArray = ComponentsFieldEnum.getEntityArray();
            boolean hasCoreComponentsFilter = ListUtils.isNotEmpty(req.getCoreComponents());
            Set<String> coreSet = hasCoreComponentsFilter ? new HashSet<>(req.getCoreComponents()) : null;
            for (SopReviewItem datum : data) {
                Long coreComponentsBit = datum.getCoreComponentsBit();
                String enumBitStr = datum.getEnumBitStr();
                // 核心部件资源量获取 func
                Map<String, Function<SopReviewItem, BigDecimal>> getFunMap = Arrays.stream(ComponentsFieldEnum.values())
                        .collect(Collectors.toMap(ComponentsFieldEnum::getName, ComponentsFieldEnum::getGetNumber));
                List<Tuple2<String,String>> showLabels = ComponentsFieldEnum.getShowLabel(entityArray, coreComponentsBit,enumBitStr);
                // 只有一个匹配的 key or 没有匹配的 key
                if (ListUtils.isEmpty(showLabels) || isCombine){
                    String coreComponents = null;
                    String componentsField = null;
                    String enumValue = null;
                    if (ListUtils.isNotEmpty(showLabels)){
                        componentsField = showLabels.stream().map(Tuple2::_1).collect(Collectors.joining(";"));
                        enumValue = showLabels.stream().map(Tuple2::_2).collect(Collectors.joining(";"));
                        coreComponents = showLabels.stream().map(item->String.join("@", item._1, item._2)).collect(Collectors.joining(";"));
                    }
                    datum.setCoreComponents(coreComponents);
                    datum.setComponentsField(componentsField);
                    datum.setEnumValue(enumValue);
                    newData.add(datum);
                    continue;
                }
                for (int i = 0; i < showLabels.size(); i++) {
                    Tuple2<String, String> tuple2 = showLabels.get(i);
                    String coreComponents = String.join("@",tuple2._1,tuple2._2);
                    // 不包含核心部件则忽略
                    if (hasCoreComponentsFilter && !coreSet.contains(coreComponents)){
                        continue;
                    }
                    Function<SopReviewItem, BigDecimal> func = getFunMap.get(tuple2._1);
                    if (i == 0){
                        datum.setComponentsField(tuple2._1);
                        datum.setEnumValue(tuple2._2);
                        datum.setCoreComponents(coreComponents);
                        datum.setCoreComponentNum(func == null? BigDecimal.ZERO: func.apply(datum));
                        newData.add(datum);
                    }else {
                        SopReviewItem copy = datum.copy();
                        copy.setComponentsField(tuple2._1);
                        copy.setEnumValue(tuple2._2);
                        copy.setCoreComponentNum(func == null? BigDecimal.ZERO: func.apply(datum));
                        copy.setCoreComponents(coreComponents);
                        newData.add(copy);
                    }
                }
            }
            data = newData;
        }

        return data;
    }

    @Override
    public List<SopReviewItem> getOverallDemand(SopReviewDataOriginReq req) {
        SopWhereBuilder sopWhereBuilder = new SopWhereBuilder(req, DwsSopReviewReportHolidayDO.class);
        ORMUtils.WhereContent where = sopWhereBuilder.where();
        return getData(req,where);
    }

    private List<SopReviewItem> getData(SopReviewDataCommonReq req, ORMUtils.WhereContent where) {
        List<String> dims = req.getDims();
        Boolean hasDiskAbbr = req.getHasDiskAbbr();
        Boolean hasSsdAbbr = req.getHasSsdAbbr();

        // 是否有 ssd 处理（为 null 即是自定义）
        if (hasSsdAbbr!= null){
            if (BooleanUtils.isTrue(hasSsdAbbr)){
                where.addAnd("(disk_abbr != '(空值)' or disk2_abbr != '(空值)')");
            }else {
                where.addAnd("disk_abbr = '(空值)' and disk2_abbr = '(空值)'");
            }
        }

        Boolean isCoreComponents = req.getIsCoreComponents();
        if (isCoreComponents != null){
            if (BooleanUtils.isTrue(isCoreComponents)){
                where.addAnd("core_components_bit != 0");
            }else {
                where.addAnd("core_components_bit = 0");
            }
        }

        // 核心部件处理
        List<String> coreComponents = req.getCoreComponents();
        if (ListUtils.isNotEmpty(coreComponents)){
            Map<String, List<Long[]>> showLabel2BitMap = ComponentsFieldEnum.getShowLabel2BitMap();
            List<Long[]> bitList = new ArrayList<>();
            for (String coreComponent : coreComponents) {
                List<Long[]> longs = showLabel2BitMap.get(coreComponent);
                if (ListUtils.isNotEmpty(longs)){
                    bitList.addAll(longs);
                }
            }
            if (ListUtils.isNotEmpty(bitList)){
                ORMUtils.WhereContent coreComponentsWhere = new ORMUtils.WhereContent();
                for (Long[] aLong : bitList) {
                    coreComponentsWhere.addOr(String.format(
                            "(bitAnd(core_components_bit, %s) != 0 and bitAnd(toInt64(SUBSTRING(enum_bit_str,%s,19)), %s) != 0)"
                    , aLong[0], SopReviewUtils.getBitStrStart(aLong[0]) + 1, aLong[1]));
                }
                where.addAnd(coreComponentsWhere);
            }
        }

        // 是否有 磁盘 处理（为 null 即是自定义）
        if (hasDiskAbbr != null){
            if (BooleanUtils.isTrue(hasDiskAbbr)){
                where.addAnd("(ssd_abbr != '(空值)' or ssd2_abbr != '(空值)' or ssd3_abbr != '(空值)')");
            }else {
                where.addAnd("ssd_abbr = '(空值)' and ssd2_abbr = '(空值)' and ssd3_abbr = '(空值)'");
            }
        }

        Map<String, String> bgAliasMap = dictService.getBgProductAliasMap(BgProductAliasType.BG);
        Map<String, String> bigProductAliasMap = dictService.getBgProductAliasMap(BgProductAliasType.PRODUCT);
        String businessRangeSql = "if(business_type = '自研上云','自研上云',if(business_type = '自研业务',${bg_alias},${big_product_alias}))";
        String bgCase = SopSelectCaseBuilder.build("custom_bg_name", "custom_bg_name", bgAliasMap);
        String bigProductCase = SopSelectCaseBuilder.build("product_big_class", "product_big_class", bigProductAliasMap);
        businessRangeSql = SimpleSqlBuilder.doReplace(businessRangeSql,"bg_alias",bgCase);
        businessRangeSql = SimpleSqlBuilder.doReplace(businessRangeSql,"big_product_alias",bigProductCase);

        boolean isExcel = BooleanUtils.isTrue(req.getIsExcel());
        String sql = isExcel?ORMUtils.getSql("/sql/sop_review/sop_review_report_query_excel.sql"):
                ORMUtils.getSql("/sql/sop_review/sop_review_report_query.sql");
        Class<?> retClass = isExcel?SopReviewExcelItem.class: SopReviewItem.class;
        sql = SimpleSqlBuilder.buildDims(sql, SopReviewFieldsEnum.getFieldNames(),dims);
        sql = SimpleSqlBuilder.doReplace(sql,"business_range",businessRangeSql);
        sql = SimpleSqlBuilder.doReplace(sql,"where",where.getSql());
        return (List<SopReviewItem>) prodReadOnlyCkStdCrpDBHelper.getRaw(retClass,sql,where.getParams());
    }
}
