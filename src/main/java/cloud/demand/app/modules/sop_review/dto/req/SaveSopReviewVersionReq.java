package cloud.demand.app.modules.sop_review.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class SaveSopReviewVersionReq {
    private Long id;
    /**
     * 13周范围
     */
    @NotNull(message = "13周范围不能为空")
    private String chineseYearMonth;

    /**
     * CVM需求版本号
     */
    @NotNull(message = "CVM需求版本号不能为空")
    private String cvmDemandVersion;

    /**
     * CVM需求切片时间
     */
    @NotNull(message = "CVM需求切片时间不能为空")
    private Date cvmDemandTime;

    /**
     * CVM需求版本号
     */
    @NotNull(message = "CVM需求版本号不能为空")
    private String deviceDemandVersion;

    /**
     * 物理机需求切片时间
     */
    @NotNull(message = "物理机需求切片时间不能为空")
    private Date deviceDemandTime;

    /**
     * CVM退回版本号
     */
    @NotNull(message = "CVM退回版本号不能为空")
    private String cvmReturnVersion;

    /**
     * CVM退回切片时间
     */
    @NotNull(message = "CVM退回切片时间不能为空")
    private Date cvmReturnTime;

    /**
     * 物理机退回版本号
     */
    @NotNull(message = "物理机退回版本号不能为空")
    private String deviceReturnVersion;

    /**
     * 物理机需求切片时间
     */
    @NotNull(message = "物理机需求切片时间不能为空")
    private Date deviceReturnTime;

}
