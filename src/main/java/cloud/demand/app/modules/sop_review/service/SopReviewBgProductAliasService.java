package cloud.demand.app.modules.sop_review.service;

import cloud.demand.app.modules.soe.service.BasVersionCommonService;
import cloud.demand.app.modules.sop_review.entity.BasSopReviewBgProductAliasDO;
import cloud.demand.app.modules.sop_review.enums.BgProductAliasType;

import java.util.List;

/** 事业群，产品大类别名 */
public interface SopReviewBgProductAliasService extends BasVersionCommonService<BasSopReviewBgProductAliasDO> {
    public List<BasSopReviewBgProductAliasDO> getByType(BgProductAliasType type);


}
