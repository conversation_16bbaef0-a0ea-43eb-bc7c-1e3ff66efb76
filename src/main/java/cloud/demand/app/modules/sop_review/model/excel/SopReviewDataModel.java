package cloud.demand.app.modules.sop_review.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/** sop 需求评审导出数据 */
@Data
public class SopReviewDataModel {

    @ExcelProperty(value = "需求年月",index = 0)
    private String indexYearMonth;

    @ExcelProperty(value = "业务类型",index = 1)
    private String businessType;

    @ExcelProperty(value = "业务范围",index = 2)
    private String businessRange;

    @ExcelProperty(value = "资源类型",index = 3)
    private String resourceType;

    @ExcelProperty(value = "部门",index = 4)
    private String deptName;

    @ExcelProperty(value = "规划产品",index = 5)
    private String planProductName;

    @ExcelProperty(value = "项目类型",index = 6)
    private String obsProjectType;

    @ExcelProperty(value = "CPU/GPU",index = 7)
    private String computeType;

    @ExcelProperty(value = "设备类型",index = 8)
    private String deviceType;

    @ExcelProperty(value = "物理机机型族",index = 9)
    private String deviceFamily;

    @ExcelProperty(value = "CVM 机型族",index = 10)
    private String instanceFamily;

    @ExcelProperty(value = "实例类型",index = 11)
    private String instanceType;

    @ExcelProperty(value = "实例规格",index = 12)
    private String instanceModel;

    @ExcelProperty(value = "CVM资源池类型",index = 13)
    private String resourcePoolType;

    @ExcelProperty(value = "GPU卡型",index = 14)
    private String gpuAbbr;

    @ExcelProperty(value = "国内外",index = 15)
    private String customhouseTitle;

    @ExcelProperty(value = "国家",index = 16)
    private String countryName;

    @ExcelProperty(value = "城市",index = 17)
    private String cityName;

    /** cpu型号<br/>Column: [cpu_abbr] */
    @ExcelProperty(value = "cpu型号",index = 18)
    private String cpuAbbr;

    /** 内存容量<br/>Column: [memory_volume] */
    @ExcelProperty(value = "内存容量",index = 19)
    private Integer memoryVolume;

    /** 硬盘1缩写<br/>Column: [disk_abbr] */
    @ExcelProperty(value = "硬盘1缩写",index = 20)
    private String diskAbbr;

    /** 硬盘2缩写<br/>Column: [disk2_abbr] */
    @ExcelProperty(value = "硬盘2缩写",index = 21)
    private String disk2Abbr;

    /** 网卡缩写<br/>Column: [nic_abbr] */
    @ExcelProperty(value = "网卡缩写",index = 22)
    private String nicAbbr;

    /** SSD1缩写<br/>Column: [ssd_abbr] */
    @ExcelProperty(value = "SSD1缩写",index = 23)
    private String ssdAbbr;

    /** SSD2缩写<br/>Column: [ssd2_abbr] */
    @ExcelProperty(value = "SSD2缩写",index = 24)
    private String ssd2Abbr;

    /** SSD3缩写<br/>Column: [ssd3_abbr] */
    @ExcelProperty(value = "SSD3缩写",index = 25)
    private String ssd3Abbr;


    /** 需求已执行 */
    @ExcelProperty(value = "申领已执行（台）",index = 26)
    private BigDecimal demandExecutedNum;

    @ExcelProperty(value = "申领已执行（核）",index = 27)
    private BigDecimal demandExecutedCoreNum;

    @ExcelProperty(value = "申领已执行（卡）",index = 28)
    private BigDecimal demandExecutedGpuNum;

    @ExcelProperty(value = "申领已执行（COS/CBS逻辑容量）",index = 29)
    private BigDecimal demandExecutedLogicCapacity;

    /** 需求未执行 */
    @ExcelProperty(value = "申领未执行（台）",index = 30)
    private BigDecimal demandNotExecutedNum;

    @ExcelProperty(value = "申领未执行（核）",index = 31)
    private BigDecimal demandNotExecutedCoreNum;

    @ExcelProperty(value = "申领未执行（卡）",index = 32)
    private BigDecimal demandNotExecutedGpuNum;

    @ExcelProperty(value = "申领未执行（COS/CBS逻辑容量）",index = 33)
    private BigDecimal demandNotExecutedLogicCapacity;

    /** 退回已执行 */
    @ExcelProperty(value = "退回已执行（台）",index = 34)
    private BigDecimal returnExecutedNum;

    @ExcelProperty(value = "退回已执行（核）",index = 35)
    private BigDecimal returnExecutedCoreNum;

    @ExcelProperty(value = "退回已执行（卡）",index = 36)
    private BigDecimal returnExecutedGpuNum;

    @ExcelProperty(value = "退回已执行（COS/CBS逻辑容量）",index = 37)
    private BigDecimal returnExecutedLogicCapacity;

    /** 退回未执行 */
    @ExcelProperty(value = "退回未执行（台）",index = 38)
    private BigDecimal returnNotExecutedNum;

    @ExcelProperty(value = "退回未执行（核）",index = 39)
    private BigDecimal returnNotExecutedCoreNum;

    @ExcelProperty(value = "退回未执行（卡）",index = 40)
    private BigDecimal returnNotExecutedGpuNum;

    @ExcelProperty(value = "退回未执行（COS/CBS逻辑容量）",index = 41)
    private BigDecimal returnNotExecutedLogicCapacity;
}
