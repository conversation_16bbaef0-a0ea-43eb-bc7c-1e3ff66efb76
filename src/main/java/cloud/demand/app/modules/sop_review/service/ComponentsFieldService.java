package cloud.demand.app.modules.sop_review.service;

import cloud.demand.app.modules.sop_review.model.clean.ICleanCoreComponentsBit;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/** 部件字段 */
public interface ComponentsFieldService {
    public List<String> getEnumValues(); // 获取枚举值
    public boolean hasEnumValues(); // 是否有枚举值

    public String[] getEnumValue(ICleanCoreComponentsBit bit); // 获取 value

    public default boolean check(String value){  // 校验
        if (StringUtils.isBlank(value)){
            return false;
        }
        if (!hasEnumValues()){
            return true;
        }
        List<String> enumValues = getEnumValues();
        if (ListUtils.isEmpty(enumValues)){
            return false;
        }
        return enumValues.contains(value);
    }
}
