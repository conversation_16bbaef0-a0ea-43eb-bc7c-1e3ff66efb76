package cloud.demand.app.modules.sop_review.entity.dws;

import cloud.demand.app.modules.sop_review.enums.fields.ISopReviewField;
import cloud.demand.app.modules.sop_review.model.clean.ICleanSopReview;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/** 节假周查询，这里用的年月是节假年月，但是字段还是 index_year_month,只能用来查询，不能写入 */
@Data
@ToString
@Table("dws_sop_review_report")
public class DwsSopReviewReportHolidayDO {
    /** 分区键，代表数据版本<br/>Column: [version] */
    @Column(value = "version")
    private String version;

    /** 数据版本生成时间<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDateTime statTime;

    /** 设备数<br/>Column: [num] */
    @Column(value = "num")
    private BigDecimal num;

    /** 物理机台（cvm 需要转，物理机等价 num） 策略表：【obs_budget_roll_adjust_cvm_append_device】 */
    @Column(value = "device_num")
    private BigDecimal deviceNum;

    /** 核心数<br/>Column: [core_num] */
    @Column(value = "core_num")
    private BigDecimal coreNum;

    /** 卡数<br/>Column: [gpu_num] */
    @Column(value = "gpu_num")
    private BigDecimal gpuNum;

    /** 是否CA<br/>Column: [is_ca] */
    @Column(value = "is_ca")
    private Integer isCa;

    /** 指标<br/>Column: [index] */
    @Column(value = "index")
    private String index;

    /** 业务类型，云业务; 自研业务;obs业务类型=云业务 or obs事业群名称=CSIG云与智慧产业事业群 or obs部门=云架平自研上云，架构平台部<br/>Column: [business_type] */
    @Column(value = "business_type")
    private String businessType;

    /** 业务范围，策略表配置：bas_sop_review_business_range<br/>Column: [business_range] */
    @Column(value = "business_range")
    private String businessRange;

    /** 指标年月 - 用节假年月 <br/>Column: [index_year_month] */
    @Column(value = "holiday_year_month")
    private String indexYearMonth;

    /** 资源类型<br/>Column: [res_type] */
    @Column(value = "resource_type")
    private String resourceType;

    /** 资源池类型<br/>Column: [res_pool_type] */
    @Column(value = "resource_pool_type")
    private String resourcePoolType;

    /** 项目类型<br/>Column: [obs_project_type] */
    @Column(value = "obs_project_type")
    private String obsProjectType;

    /** BG名<br/>Column: [bg_name] */
    @Column(value = "bg_name")
    private String bgName;

    /** 自定义BG名<br/>Column: [custom_bg_name] */
    @Column(value = "custom_bg_name")
    private String customBgName;

    /** 部门名<br/>Column: [dept_name] */
    @Column(value = "dept_name")
    private String deptName;

    /** 规划产品名<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** 国内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 国家<br/>Column: [country_name] */
    @Column(value = "country_name")
    private String countryName;

    /** 城市：可同时表示物理机城市，也可以表示腾讯云 region<br/>Column: [city_name] */
    @Column(value = "city_name")
    private String cityName;

    /** 物理机机型族<br/>Column: [device_family] */
    @Column(value = "device_family")
    private String deviceFamily;

    /** 物理机设备类型<br/>Column: [phy_device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** cvm 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** cvm 实例规格<br/>Column: [instance_model] */
    @Column(value = "instance_model")
    private String instanceModel;

    /** cvm 实例规格<br/>Column: [instance_family] */
    @Column(value = "instance_family")
    private String instanceFamily;

    /** 计算类型: CPU,GPU<br/>Column: [compute_type] */
    @Column(value = "compute_type")
    private String computeType;

    /** 大类<br/>Column: [big_class] */
    @Column(value = "big_class")
    private String bigClass;

    /** 产品大类<br/>Column: [product_big_class] */
    @Column(value = "product_big_class")
    private String productBigClass;

    /** obs业务类型<br/>Column: [obs_business_type] */
    @Column(value = "obs_business_type")
    private String obsBusinessType;

    /** cpu型号<br/>Column: [cpu_abbr] */
    @Column(value = "cpu_abbr")
    private String cpuAbbr;

    /** 内存容量<br/>Column: [memory_volume] */
    @Column(value = "memory_volume")
    private Integer memoryVolume;

    /** 硬盘1缩写<br/>Column: [disk_abbr] */
    @Column(value = "disk_abbr")
    private String diskAbbr;

    /** 硬盘2缩写<br/>Column: [disk2_abbr] */
    @Column(value = "disk2_abbr")
    private String disk2Abbr;

    /** 网卡缩写<br/>Column: [nic_abbr] */
    @Column(value = "nic_abbr")
    private String nicAbbr;

    /** SSD1缩写<br/>Column: [ssd_abbr] */
    @Column(value = "ssd_abbr")
    private String ssdAbbr;

    /** SSD2缩写<br/>Column: [ssd2_abbr] */
    @Column(value = "ssd2_abbr")
    private String ssd2Abbr;

    /** SSD3缩写<br/>Column: [ssd3_abbr] */
    @Column(value = "ssd3_abbr")
    private String ssd3Abbr;

    /** GPU卡缩写<br/>Column: [gpu_abbr] */
    @Column(value = "gpu_abbr")
    private String gpuAbbr;

}
