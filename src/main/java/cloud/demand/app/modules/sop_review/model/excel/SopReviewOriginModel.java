package cloud.demand.app.modules.sop_review.model.excel;

import cloud.demand.app.modules.soe.anno.ExcelSheet;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_review.dto.req.SopReviewCommonReq;
import cloud.demand.app.modules.sop_review.enums.SopReviewIndexEnum;
import cloud.demand.app.modules.sop_util.process.QueryProcessInstance;
import cloud.demand.app.modules.sop_util.process.model.IIndexData;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import java.util.List;
import java.util.Map;

/** 原始需求看板excel */
@Data
public class SopReviewOriginModel {

//    @ExcelSheet(value = "过滤条件",modelClass = SopReviewReqModel.class)
//    private List<SopReviewReqModel> params;

    @ExcelSheet(value = "原始需求@当前版本",modelClass = SopReviewDataModel.class)
    private List<SopReviewDataModel> curData;

    @ExcelSheet(value = "原始需求@上个版本",modelClass = SopReviewDataModel.class)
    private List<SopReviewDataModel> preData;

    public void buildData(QueryProcessInstance instance, SopReviewCommonReq req) {
        Map<String, List<IIndexData>> stepCacheData = instance.getStepCacheData();
        if (ListUtils.isEmpty(stepCacheData)){
            return;
        }
        SoeCommonUtils.setModel(stepCacheData,this, ListUtils.newList(
                SopReviewIndexEnum.ORIGIN_CUR_DATA.getName(),
                SopReviewIndexEnum.ORIGIN_PRE_DATA.getName()));
    }
}
