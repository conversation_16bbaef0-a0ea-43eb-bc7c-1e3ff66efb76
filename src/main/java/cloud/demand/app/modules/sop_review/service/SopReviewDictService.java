package cloud.demand.app.modules.sop_review.service;

import cloud.demand.app.modules.sop.http.domain.one_table.TechnicalClassItem;
import cloud.demand.app.modules.sop_review.dto.req.SaveSopReviewVersionReq;
import cloud.demand.app.modules.sop_review.dto.resp.BasBusinessRange;
import cloud.demand.app.modules.sop_review.dto.resp.BasSopReviewVersion;
import cloud.demand.app.modules.sop_review.dto.resp.DiskVolumeResp;
import cloud.demand.app.modules.sop_review.entity.BasProductDeviceLogicCapacityDO;
import cloud.demand.app.modules.sop_review.entity.BasSopReviewVersionDO;
import cloud.demand.app.modules.sop_review.enums.BgProductAliasType;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface SopReviewDictService {

    /** 通过 sop 版本获取采购预测版本（采购预测版本就是 sop 底数的 version） */
    List<String> getPurchaseVersion(List<String> sopVersion);

    /** 获取版本列表 */
    List<BasSopReviewVersion> getVersionList();

    /** 获取版本映射，当前版本--->上个版本 */
    public Map<String,String> getVersionCur2PreMap();


    void saveSopReviewVersion(SaveSopReviewVersionReq req);

    /**
     * 根据类型获取名称和别名的映射关系
     * @param type
     * @return Map<String, String>
     */
    Map<String, String> getBgProductAliasMap(BgProductAliasType type);

    /**
     * 保存sop评审版本
     */
    void syncSopReviewVersion();

    List<BasSopReviewVersionDO> listSopReviewVersionList();

    List<BasSopReviewVersionDO> getSopReviewVersionInfo(List<String> version);


    List<BasBusinessRange> getBusinessRangeList();

    /** 获取逻辑容量 map */
    Map<String, BigDecimal> getLogicCapacityMap();

    /** 获取逻辑容量 */
    List<BasProductDeviceLogicCapacityDO> getLogicCapacity();

    /** 获取技术分类 */
    List<TechnicalClassItem> getTechnicalClass();

    /** 磁盘规格 */
    List<DiskVolumeResp> getDiskVolume();

}
