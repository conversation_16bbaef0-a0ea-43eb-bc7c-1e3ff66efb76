package cloud.demand.app.modules.sop_review.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/** 指标类型 */
@AllArgsConstructor
@Getter
public enum SopReviewIndexType {
    TOTAL("全量需求",null),

    EXECUTED("已执行",new SopDataIndexEnum[]{SopDataIndexEnum.SOP_DEMAND_EXECUTED,SopDataIndexEnum.SOP_RETURN_EXECUTED}),

    NOT_EXECUTED("未执行",new SopDataIndexEnum[]{SopDataIndexEnum.SOP_DEMAND_NOT_EXECUTED,SopDataIndexEnum.SOP_RETURN_NOT_EXECUTED}),

    ;
    private final String name;

    private final SopDataIndexEnum[] sopDataIndexEnum; // null 表示所有

    public static SopReviewIndexType getByName(String name){
        for (SopReviewIndexType value : values()) {
            if (Objects.equals(name,value.getName())){
                return value;
            }
        }
        return null;
    }


}
