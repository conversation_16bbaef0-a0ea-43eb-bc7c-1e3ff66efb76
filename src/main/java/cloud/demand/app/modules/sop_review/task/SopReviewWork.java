package cloud.demand.app.modules.sop_review.task;

import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.SimpleTaskEnum;
import cloud.demand.app.modules.mrpv2.task.process.SimpleCommonTaskProcess;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.work.AbstractSopWork;
import cloud.demand.app.modules.sop_review.entity.SopDemandAndReturnDO;
import cloud.demand.app.modules.sop_review.entity.dws.DwsSopReviewReportDO;
import cloud.demand.app.modules.sop_review.service.SopGetDataService;
import cloud.demand.app.modules.sop_review.service.SopReviewCleanService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class SopReviewWork extends AbstractSopWork<SimpleCommonTask> {

    @Resource
    private SimpleCommonTaskProcess process;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private SopReviewCleanService cleanService;

    @Resource
    private SopGetDataService getDataService;

    @Override
    public ISopProcess<SimpleCommonTask> getTask() {
        return process;
    }

    @Override
    public ITaskEnum getEnum() {
        return SimpleTaskEnum.SOP_DEMAND_REVIEW;
    }

    /** 30s 一次 */
    @Scheduled(fixedRate = 120 * 1000)
    @Override
    public void work() {
        super.work();
    }

    @Override
    public void doWork(SimpleCommonTask task) {
        genData(task.getVersion());
    }

    public void genData(String version){
        // step2: 查询 SOP 需求退回数据
        List<DwsSopReviewReportDO> saveData = new ArrayList<>(transform(getDataService.getSopDemandAndReturn(version)));
        // 只有生产才抛出异常
        if (EnvUtils.isProduction() && ListUtils.isEmpty(saveData)){
            throw new ITException(String.format("SOP 需求退回数据为空,version:[%s]", version));
        }
        // step4: 删掉当前分区
        CkDBUtils.delete(ckcldStdCrpDBHelper,version, DwsSopReviewReportDO.class);
        // step5：写入数据
        CkDBUtils.saveBatch(ckcldStdCrpDBHelper,saveData);
    }

    private List<DwsSopReviewReportDO> transform(List<SopDemandAndReturnDO> data){
        List<DwsSopReviewReportDO> ret = new ArrayList<>();
        if (ListUtils.isEmpty(data)){
            return ret;
        }
        LocalDateTime now = LocalDateTime.now();
        // step3: 清洗 + 转换
        for (SopDemandAndReturnDO datum : data) {
            // 核数和台数都为 0 的过滤
            if (SoeCommonUtils.isNullOrZone(datum.getNum()) && SoeCommonUtils.isNullOrZone(datum.getCoreNum())){
                continue;
            }
            DwsSopReviewReportDO item = transform(datum);
            item.setStatTime(now);
            cleanService.clean(item);
            ret.add(item);
        }
        return ret;
    }

    private DwsSopReviewReportDO transform(SopDemandAndReturnDO item){
        DwsSopReviewReportDO dwsSopReviewReportDO = new DwsSopReviewReportDO();
        dwsSopReviewReportDO.setVersion(item.getVersion());
        dwsSopReviewReportDO.setNum(item.getNum());
        dwsSopReviewReportDO.setCoreNum(item.getCoreNum());
        dwsSopReviewReportDO.setIsCa(item.getIsCa());
        dwsSopReviewReportDO.setIndex(item.getIndex());
        dwsSopReviewReportDO.setBusinessType(item.getBusinessType());
        dwsSopReviewReportDO.setIndexYearMonth(item.getIndexYearMonth());
        dwsSopReviewReportDO.setHolidayYearMonth(item.getHolidayYearMonth());
        dwsSopReviewReportDO.setResourceType(item.getResType());
        dwsSopReviewReportDO.setResourcePoolType(item.getResPoolType());
        dwsSopReviewReportDO.setObsBusinessType(item.getObsBusinessType());
        dwsSopReviewReportDO.setObsProjectType(item.getObsProjectType());
        dwsSopReviewReportDO.setBgName(item.getBgName());
        dwsSopReviewReportDO.setCustomBgName(item.getCustomBgName());
        dwsSopReviewReportDO.setDeptName(item.getDeptName());
        dwsSopReviewReportDO.setPlanProductName(item.getPlanProductName());
        dwsSopReviewReportDO.setCustomhouseTitle(item.getCustomhouseTitle());
        dwsSopReviewReportDO.setCountryName(item.getCountryName());
        dwsSopReviewReportDO.setCityName(item.getCityName());
        dwsSopReviewReportDO.setDeviceFamily(item.getPhyDeviceFamily());
        dwsSopReviewReportDO.setDeviceType(item.getPhyDeviceType());
        dwsSopReviewReportDO.setInstanceType(item.getCvmGinsFamily());
        dwsSopReviewReportDO.setInstanceModel(item.getCvmGinsType());
        dwsSopReviewReportDO.setInstanceFamily(item.getCvmGinsKingdom());
        return dwsSopReviewReportDO;

    }
}
