package cloud.demand.app.modules.sop_review.model.req;

import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import cloud.demand.app.modules.sop_review.dto.req.SopReviewDeviceReq;
import lombok.Data;

import java.util.List;

@Data
public class SopReviewDataDeviceReq extends SopReviewDataCommonReq{
    public static SopReviewDataDeviceReq transform(SopReviewDeviceReq req){
        SopReviewDataDeviceReq ret = new SopReviewDataDeviceReq();
        ret.setStartYearMonth(SopDateUtils.getYearMonth(req.getStartYearMonth()));
        ret.setIgnoreCloudMig(req.getIgnoreCloudMig());
        ret.setPlanProductName(req.getPlanProductName());
        ret.setEndYearMonth(SopDateUtils.getYearMonth(req.getEndYearMonth()));
        ret.setBusinessType(req.getBusinessType());
        ret.setBusinessRange(req.getBusinessRange());
        ret.setComputeType(req.getComputeType());
        ret.setCustomhouseTitle(req.getCustomhouseTitle());
        ret.setCountryName(req.getCountryName());
        ret.setRegionName(req.getRegionName());
        ret.setPhyDeviceType(req.getPhyDeviceType());
        ret.setObsProjectType(req.getObsProjectType());
        ret.setCpuAbbr(req.getCpuAbbr());
        ret.setMemoryVolume(req.getMemoryVolume());
        ret.setGpuAbbr(req.getGpuAbbr());
        ret.setNicAbbr(req.getNicAbbr());
        ret.setDiskAbbr(req.getDiskAbbr());
        ret.setDiskVolume(req.getDiskVolume());
        ret.setSsdAbbr(req.getSsdAbbr());
        ret.setHasDiskAbbr(req.getHasDiskAbbr());
        ret.setHasSsdAbbr(req.getHasSsDAbbr());
        ret.setIgnoreDeptName(req.getIgnoreDeptName());
        ret.setDims(req.getGroupBy());
        ret.setIsCoreComponents(req.getIsCoreComponents());
        ret.setTechnicalClass(req.getTechnicalClass());
        ret.setIntellectNetworkCardType(req.getIntellectNetworkCardType());
        ret.setCoreComponents(req.getCoreComponents());
        ret.setCpuVender(req.getCpuVender());
        return ret;


    }
}
