package cloud.demand.app.modules.sop_review.process.item;

import cloud.demand.app.modules.soe.dto.item.BigDecimalItem;
import cloud.demand.app.modules.soe.dto.item.IExtObject;
import cloud.demand.app.modules.soe.dto.item.ReportItem;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.entity.clean.BusinessRange;
import cloud.demand.app.modules.sop.enums.SopBusinessType;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import cloud.demand.app.modules.sop_review.entity.SopReviewChangeReasonDO;
import cloud.demand.app.modules.sop_review.entity.SopReviewChangeReasonV2DO;
import cloud.demand.app.modules.sop_review.entity.YuntiDemandReviewMemoDO;
import cloud.demand.app.modules.sop_review.enums.MemoTypeEnum;
import cloud.demand.app.modules.sop_review.enums.fields.IBusinessRange;
import cloud.demand.app.modules.sop_review.enums.fields.IBusinessType;
import cloud.demand.app.modules.sop_review.enums.fields.IComputeType;
import cloud.demand.app.modules.sop_review.enums.fields.IDeptName;
import cloud.demand.app.modules.sop_review.enums.fields.IIndexYearMonth;
import cloud.demand.app.modules.sop_review.enums.fields.IPlanProductName;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 原因 */
@Data
public class SopReviewReasonItem extends SopReviewAbstractReportData implements
        IIndexYearMonth,
        IBusinessRange,
        IBusinessType,
        IDeptName,
        IComputeType,
        IPlanProductName {

    private Long id;

    private String curVersion;

    private String preVersion;

    private String yearMonth;

    private String yearMonthType;

    private String businessType;

    private String computeType;

    private String businessRange;

    private String planProductName;

    private String deptName;

    private Integer showSopFlag;

    private String reasonType;

    private String reason;

    private BigDecimal changeNum;

    private BigDecimal changeCoreNum;

    private BigDecimal changeGpuNum;

    private BigDecimal changeLogicCapacity;

    private String updateUser;

    private Date updateTime;

    private Integer order;

    private Boolean isFromYunti; // 是否来自云梯(自研周需求评审)

    private Integer casVersion;

    @Override
    public BigDecimal getValue_() {
        return new BigDecimalItem(changeNum,new BigDecimal[]{changeCoreNum, changeGpuNum, changeLogicCapacity});
    }
    @Override
    public String getIndexYearMonth() {
        return yearMonth;
    }

    public static SopReviewReasonItem transform(SopReviewChangeReasonDO changeReasonDO) {
        SopReviewReasonItem sopReviewReasonItem = new SopReviewReasonItem();
        sopReviewReasonItem.setId(changeReasonDO.getId());
        sopReviewReasonItem.setCurVersion(changeReasonDO.getCurVersion());
        sopReviewReasonItem.setPreVersion(changeReasonDO.getPreVersion());
        sopReviewReasonItem.setYearMonth(changeReasonDO.getYearMonth());
        sopReviewReasonItem.setBusinessType(changeReasonDO.getBusinessType());
        sopReviewReasonItem.setBusinessRange(changeReasonDO.getBusinessRange());
        sopReviewReasonItem.setPlanProductName(changeReasonDO.getPlanProductName());
        sopReviewReasonItem.setDeptName(changeReasonDO.getDeptName());
        sopReviewReasonItem.setReason(changeReasonDO.getReason());
        sopReviewReasonItem.setChangeNum(changeReasonDO.getChangeNum());
        sopReviewReasonItem.setChangeCoreNum(changeReasonDO.getChangeCoreNum());
        sopReviewReasonItem.setChangeGpuNum(changeReasonDO.getChangeGpuNum());
        sopReviewReasonItem.setChangeLogicCapacity(changeReasonDO.getChangeLogicCapacity());
        sopReviewReasonItem.setUpdateUser(changeReasonDO.getUpdateUser());
        sopReviewReasonItem.setCasVersion(changeReasonDO.getCasVersion());
        if (changeReasonDO instanceof SopReviewChangeReasonV2DO){
            SopReviewChangeReasonV2DO v2 = ((SopReviewChangeReasonV2DO)changeReasonDO);
            sopReviewReasonItem.setComputeType(v2.getComputeType());
            sopReviewReasonItem.setReasonType(v2.getReasonType());
            sopReviewReasonItem.setShowSopFlag(v2.getShowSopFlag());
            sopReviewReasonItem.setYearMonthType(v2.getYearMonthType());
            sopReviewReasonItem.setUpdateTime(v2.getUpdateTime());
            sopReviewReasonItem.setOrder(v2.getOrder());
            sopReviewReasonItem.setIsFromYunti(false);
        }
        return sopReviewReasonItem;
    }

    public static SopReviewReasonItem transform(YuntiDemandReviewMemoDO changeReasonDO,Map<String, String> bgAliasMap) {
        SopReviewReasonItem sopReviewReasonItem = new SopReviewReasonItem();
        sopReviewReasonItem.setId(null);
        sopReviewReasonItem.setCurVersion(null);
        sopReviewReasonItem.setPreVersion(null);
        sopReviewReasonItem.setYearMonth(null);
        sopReviewReasonItem.setBusinessType(SopBusinessType.SELF.getDesc());
        sopReviewReasonItem.setBusinessRange(bgAliasMap.getOrDefault(changeReasonDO.getBgName(),changeReasonDO.getBgName()));
        sopReviewReasonItem.setPlanProductName(null);
        sopReviewReasonItem.setDeptName(null);
        sopReviewReasonItem.setReason(changeReasonDO.getMemo());
        sopReviewReasonItem.setChangeNum(BigDecimal.ZERO);
        sopReviewReasonItem.setChangeCoreNum(BigDecimal.ZERO);
        sopReviewReasonItem.setChangeGpuNum(BigDecimal.ZERO);
        sopReviewReasonItem.setChangeLogicCapacity(BigDecimal.ZERO);
        sopReviewReasonItem.setUpdateUser(changeReasonDO.getCreator());
        sopReviewReasonItem.setCasVersion(0);
        sopReviewReasonItem.setComputeType(null);
        sopReviewReasonItem.setReasonType(changeReasonDO.getMemoReasonType());
        sopReviewReasonItem.setShowSopFlag(changeReasonDO.getShowSopFlag());
        sopReviewReasonItem.setYearMonthType(MemoTypeEnum.getByStatus(changeReasonDO.getMemoType()));
        if (changeReasonDO.getUpdateTime() != null){
            sopReviewReasonItem.setUpdateTime(DateUtils.parse(DateUtils.formatDate(changeReasonDO.getUpdateTime())));
        }
        sopReviewReasonItem.setOrder(changeReasonDO.getOrder());
        sopReviewReasonItem.setIsFromYunti(true);
        return sopReviewReasonItem;
    }

    @Override
    public Map<String, IExtObject> getExt() {
        Map<String, IExtObject> ret = new HashMap<>();
        ret.put("reason", new ReasonExtObject(ListUtils.newList(this.toReasonItem())));
        return ret;
    }

    public ReasonItem toReasonItem() {
        ReasonItem ret = new ReasonItem();
        ret.setId(id);
        ret.setYearMonth(yearMonth);
        ret.setYearMonthType(yearMonthType);
        ret.setBusinessType(businessType);
        ret.setComputeType(computeType);
        ret.setBusinessRange(businessRange);
        ret.setPlanProductName(planProductName);
        ret.setDeptName(deptName);
        ret.setShowFlag(showSopFlag);
        ret.setReasonType(reasonType);
        ret.setReason(reason);
        ret.setChangeNum(changeNum);
        ret.setChangeCoreNum(changeCoreNum);
        ret.setChangeGpuNum(changeGpuNum);
        ret.setUpdateUser(updateUser);
        ret.setUpdateTime(updateTime);
        ret.setOrder(order);
        ret.setIsFromYunti(isFromYunti);
        ret.setCasVersion(casVersion);
        return ret;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ReasonExtObject implements IExtObject {

        private List<ReasonItem> items;

        @Override
        public IExtObject merge(IExtObject object, BiFunction<BigDecimal, BigDecimal, BigDecimal> func) {
            ReasonExtObject ret = new ReasonExtObject();
            List<ReasonItem> retItem = new ArrayList<>();
            if (ListUtils.isNotEmpty(items)){
                retItem.addAll(items);
            }
            ReasonExtObject other = (ReasonExtObject) object;
            if (other != null && ListUtils.isNotEmpty(other.getItems())){
                retItem.addAll(other.getItems());
            }
            ret.setItems(retItem);
            return ret;
        }

        @Override
        public IExtObject copy() {
            ReasonExtObject ret = new ReasonExtObject();
            if (items != null){
                ret.setItems(new ArrayList<>(items));
            }
            return ret;
        }

        @Override
        public void clean() {
            this.setItems(null);
        }

        @Override
        public void flush(ReportItem reportItem) {
            Map<String, IExtObject> ext = reportItem.getExt();
            if (ext != null){
                ReasonExtObject extObject = (ReasonExtObject)ext.get("reason");
                if (extObject != null){
                    this.setItems(extObject.getItems());
                }
            }
        }
    }

    @Data
    public static class ReasonItem {
        private Long id;
        private String yearMonth;
        private String yearMonthType;
        private String computeType;
        private String businessType;
        private String businessRange;
        private String planProductName;
        private String deptName;
        private Integer showFlag;
        private String reasonType;
        private String reason;
        private BigDecimal changeNum;
        private BigDecimal changeCoreNum;
        private BigDecimal changeGpuNum;
        private String updateUser;
        private Date updateTime;
        private Boolean isFromYunti;
        private Integer order;
        private Integer casVersion;
    }
}
