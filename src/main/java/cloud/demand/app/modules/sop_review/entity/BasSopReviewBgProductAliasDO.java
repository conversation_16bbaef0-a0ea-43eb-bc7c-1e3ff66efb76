package cloud.demand.app.modules.sop_review.entity;

import cloud.demand.app.modules.soe.entitiy.dict.BasVersionCommonDO;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("bas_sop_review_bg_product_alias")
public class BasSopReviewBgProductAliasDO extends BasVersionCommonDO {
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    @ExcelIgnore
    private Integer id;

    @ExcelProperty("类型")
    @Column("type")
    private String type;// 类型：BG / PRODUCT

    @ExcelProperty("原始名称")
    @Column("name")
    private String name; // 原始名称

    @ExcelProperty("自定义别名")
    @Column("alias")
    private String alias; // 别名
}
