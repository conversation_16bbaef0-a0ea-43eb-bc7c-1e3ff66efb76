package cloud.demand.app.modules.sop_review.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@AllArgsConstructor
@Getter
public enum BgProductAliasType {
    BG("BG", "自定义事业群"),
    PRODUCT("PRODUCT", "产品大类"),
    ;

    private final String type;
    private final String name;

    public static BgProductAliasType getEnumByName(String name){
        if (StringUtils.isBlank(name)){
            return null;
        }
        for (BgProductAliasType value : values()) {
            if (Objects.equals(value.getName(),name)){
                return value;
            }
        }
        return null;
    }

    public static BgProductAliasType getEnumByType(String type){
        if (StringUtils.isBlank(type)){
            return null;
        }
        for (BgProductAliasType value : values()) {
            if (Objects.equals(value.getType(),type)){
                return value;
            }
        }
        return null;
    }
}
