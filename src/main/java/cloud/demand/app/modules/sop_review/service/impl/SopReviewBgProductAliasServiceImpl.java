package cloud.demand.app.modules.sop_review.service.impl;

import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.soe.dto.resp.ErrorMessage;
import cloud.demand.app.modules.soe.model.excel.IExcelSheetModel;
import cloud.demand.app.modules.soe.model.excel.SimpleModel;
import cloud.demand.app.modules.soe.service.impl.BasVersionCommonServiceImpl;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_review.entity.BasSopReviewBgProductAliasDO;
import cloud.demand.app.modules.sop_review.enums.BgProductAliasType;
import cloud.demand.app.modules.sop_review.service.SopReviewBgProductAliasService;
import cloud.demand.app.modules.sop_review.service.SopReviewCommonService;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class SopReviewBgProductAliasServiceImpl extends BasVersionCommonServiceImpl<BasSopReviewBgProductAliasDO> implements SopReviewBgProductAliasService {
    @Resource
    private SopReviewCommonService commonService;

    @Override
    public String getBasName() {
        return "业务范围别名";
    }

    @Override
    public void iterationData(BasSopReviewBgProductAliasDO excelDatum) {
        BgProductAliasType typeByName = BgProductAliasType.getEnumByName(excelDatum.getType());
        if (typeByName == null){
            throw new ITException(String.format("类型错误，请导出模版：%s",excelDatum.getType()));
        }
        excelDatum.setType(typeByName.getType());
    }

    @Override
    public List<ErrorMessage> checkFile(List<BasSopReviewBgProductAliasDO> excelData) {
        List<ErrorMessage> ret = new ArrayList<>();
        Set<String> cloudBgName = new HashSet<>(ObjectUtils.defaultIfNull(commonService.getSelfCustomBgName(), new ArrayList<>()));
        Set<String> productBigClass = new HashSet<>(ObjectUtils.defaultIfNull(commonService.getProductBigClass(), new ArrayList<>()));
        for (int i = 0; i < excelData.size(); i++) {
            int index = i + 1;
            BasSopReviewBgProductAliasDO item = excelData.get(i);
            String type = item.getType(); // excel 导入的时候是 name，这里要转成 type
            BgProductAliasType typeByName = BgProductAliasType.getEnumByName(type);
            if (typeByName == null) {
                ret.add(new ErrorMessage(index, 1, "类型", "类型名称错误：【自定义事业群|产品大类】"));
                continue;
            }
            // 不校验原始名称（可能是一下脏数据，需要统一改成别名展示）
//            Set<String> checkSet = typeByName == BgProductAliasType.BG ? cloudBgName : productBigClass;
            String name = item.getName();
//            if (!checkSet.contains(name)) {
//                ret.add(new ErrorMessage(index, 2, "原始名称", "原始名称错误，请导出模版查看枚举"));
//            }
            if (StringUtils.isBlank(name)) {
                ret.add(new ErrorMessage(index, 2, "原始名称", "原始名称错误，不能为空值"));
            }

            String alias = item.getAlias();
            if (StringUtils.isBlank(alias)) {
                ret.add(new ErrorMessage(index, 3, "自定义别名", "自定义别名错误，不能为空值"));
            }
        }
        return ret;
    }

    @Override
    public List<BasSopReviewBgProductAliasDO> getByType(BgProductAliasType type) {
        return getDBHelper().getAll(BasSopReviewBgProductAliasDO.class, "where default_flag = '1' and type = ?", type.getType());
    }

    @Override
    public ResponseEntity<InputStreamResource> templateExcel() {
        List<BasSopReviewBgProductAliasDO> list = ObjectUtils.defaultIfNull(getList(),new ArrayList<>());
        if (ListUtils.isEmpty(list)) {
            BasSopReviewBgProductAliasDO bg = new BasSopReviewBgProductAliasDO();
            bg.setType(BgProductAliasType.BG.getName());
            bg.setName("模版-bg");
            bg.setAlias("模版-bg");
            list.add(bg);
            BasSopReviewBgProductAliasDO product = new BasSopReviewBgProductAliasDO();
            product.setType(BgProductAliasType.PRODUCT.getName());
            product.setName("模版-product");
            product.setAlias("模版-product");
            list.add(product);
        }else {
            // 类型转换
            for (BasSopReviewBgProductAliasDO aliasDO : list) {
                BgProductAliasType typeByName = BgProductAliasType.getEnumByType(aliasDO.getType());
                if (typeByName == null){
                    throw new ITException(String.format("类型错误，【%s】", aliasDO.getType()));
                }
                aliasDO.setType(typeByName.getName());
            }
        }
        List<IExcelSheetModel> modelList = new ArrayList<>();
        modelList.add(new IExcelSheetModel() {
            @Override
            public String getSheetName() {
                return getBasName();
            }

            @Override
            public List<?> getData() {
                return list;
            }
        });
        modelList.add(new IExcelSheetModel() {
            @Override
            public String getSheetName() {
                return "类型";
            }

            @Override
            public List<?> getData() {
                return SimpleModel.build(ListUtils.newList("自定义事业群", "产品大类"));
            }
        });
        List<String> cloudCustomBgName = commonService.getSelfCustomBgName();
        modelList.add(new IExcelSheetModel() {
            @Override
            public String getSheetName() {
                return "自定义事业群";
            }

            @Override
            public List<?> getData() {
                return SimpleModel.build(cloudCustomBgName);
            }
        });
        List<String> productBigClass = commonService.getProductBigClass();
        modelList.add(new IExcelSheetModel() {
            @Override
            public String getSheetName() {
                return "产品大类";
            }

            @Override
            public List<?> getData() {
                return SimpleModel.build(productBigClass);
            }
        });
        return SoeCommonUtils.toExcelEntity(modelList, getBasName());
    }
}
