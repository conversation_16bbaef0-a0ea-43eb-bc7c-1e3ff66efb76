package cloud.demand.app.modules.rrp_new_feature.model;

import lombok.Data;

@Data
public class CompareDemandOverViewResultDTO {

    private String yearMonth;
    private Long erp;
    private Long xy;
    private Long diff;

    public static CompareDemandOverViewResultDTO copy(CompareDemandDetailResultDTO compareDemandDetailResultDTO) {
        CompareDemandOverViewResultDTO compareDemandOverViewResultDTO = new CompareDemandOverViewResultDTO();
        compareDemandOverViewResultDTO.setYearMonth(compareDemandDetailResultDTO.getYearMonth());
        compareDemandOverViewResultDTO.setErp(compareDemandDetailResultDTO.getErp());
        compareDemandOverViewResultDTO.setXy(compareDemandDetailResultDTO.getXy());
        compareDemandOverViewResultDTO.setDiff(compareDemandDetailResultDTO.getDiff());
        return compareDemandOverViewResultDTO;

    }
}
