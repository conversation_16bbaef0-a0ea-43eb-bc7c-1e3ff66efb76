package cloud.demand.app.modules.sop.domain.http;

import cloud.demand.app.modules.sop.deserializer.SopLocalDateDeserializer;
import cloud.demand.app.modules.sop.deserializer.SopStringDeserializer;
import cloud.demand.app.modules.sop.entity.clean.OriIndexDate;
import cloud.demand.app.modules.sop.entity.clean.OriModZoneInfo;
import cloud.demand.app.modules.sop.entity.clean.OriProductInfo;
import cloud.demand.app.modules.sop.entity.clean.OriZoneCity;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;

@Data
public class SopServerReturnResList implements OriProductInfo, OriModZoneInfo, OriZoneCity, OriIndexDate {
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String resourceType;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String resourcePool;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String projectType;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String businessType;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String customBgName;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String deptName;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String planProductName;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String areaType;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String country;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String city;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String campus;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String module;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String deviceFamily;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String instanceType;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String instanceModel;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String deviceType;

    @JsonDeserialize(using = SopLocalDateDeserializer.class)
    private LocalDate date;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String isCloudReturn;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String assetCode;

    private Integer year;


    private Integer month;


    private Integer week;

    private Integer isHedging;

    private Integer isValidHedging;

    private BigDecimal amount;

    private BigDecimal exeAmount;

    private BigDecimal nonAmount;

    private BigDecimal cpuExeAmount;

    /** 退回标签 */
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String allocationMemo;

    /** 退回原始标签 */
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String initAllocationMemo;

    /** 退回原因分类 */
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String reasonClass;

    /** 退回设备年限 */
    private Integer returnUsedYear;

    private Integer id;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String version;

    @JsonDeserialize(using = SopLocalDateDeserializer.class)
    private LocalDate dateVersion;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String bgName;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String orderId;

    @Override
    public String getZone() {
        return null;
    }
}
