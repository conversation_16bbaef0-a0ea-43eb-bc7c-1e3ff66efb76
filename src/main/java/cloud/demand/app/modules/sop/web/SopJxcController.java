package cloud.demand.app.modules.sop.web;

import cloud.demand.app.modules.mrpv2.utils.PageQueryUtils;
import cloud.demand.app.modules.mrpv2.utils.SecurityFiberSupplier;
import cloud.demand.app.modules.soe.model.dict.YearWeek;
import cloud.demand.app.modules.sop.domain.ReturnT;
import cloud.demand.app.modules.sop.domain.report.QueryReportCommonReq;
import cloud.demand.app.modules.sop.domain.report.QueryReportCommonResourceReq;
import cloud.demand.app.modules.sop.domain.report.QueryReportItemResp;
import cloud.demand.app.modules.sop.domain.report.QueryReportParamsReq;
import cloud.demand.app.modules.sop.domain.report.VersionDTO;
import cloud.demand.app.modules.sop.entity.dict.DockerGpuCategoryStrategyDO;
import cloud.demand.app.modules.sop.service.CommonDbHelper;
import cloud.demand.app.modules.sop.service.other.SopAllYearDemandService;
import cloud.demand.app.modules.sop.service.task.init.TaskInitService;
import cloud.demand.app.modules.sop.service.web.SopJxcReportService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.RateLimit;
import com.pugwoo.wooutils.redis.RedisLimitPeriodEnum;
import erp.base.fiber.support.dispatcher.FiberSupplier;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.SneakyThrows;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.security.CurrentUser;
import yunti.boot.security.TofUser;
import yunti.boot.util.YuntiUtils;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@JsonrpcController("/sop/jxc")
public class SopJxcController {

    @Resource
    SopJxcReportService sopJxcReportService;
    @Resource
    TaskInitService taskInitService;
    @Resource
    private SopAllYearDemandService sopAllYearDemandService;
    @Resource
    CommonDbHelper commonDbHelper;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Data
    public static class SleepReq{
        private int sleep;
        private int thread;
    }

    @SneakyThrows
    @RequestMapping
    public String sleepEachRow(@JsonrpcParam SleepReq req){
        CountDownLatch latch = new CountDownLatch(req.getThread());
        List<Exception> errList = new ArrayList<>();
        for (int i = 0; i < req.getThread(); i++) {
            int finalI = i;
            PageQueryUtils.fiberTaskExecutor.submit(new SecurityFiberSupplier() {
                @Override
                public void consume() {
                    System.out.println(Thread.currentThread().getName() + " : start - " + finalI);
                    try{
                        ckcldStdCrpDBHelper.getRaw(Integer.class,"select sleepEachRow(1) from system.numbers limit " + req.getSleep());
                    }catch (Exception e){
                        errList.add(e);
                    }
                    System.out.println(Thread.currentThread().getName() + " : end - " + finalI);
                    latch.countDown();
                }
            });
        }
        latch.await();
        if (ListUtils.isNotEmpty(errList)){
            throw new RuntimeException(errList.get(0).getMessage());
        }
        return "done";
    }

    /** 获取默认版本的 gpu 信息 */
    @RequestMapping
    public Map<String, DockerGpuCategoryStrategyDO> getDefaultVersionGpuInfo(){
        return commonDbHelper.getDefaultVersionGpuInfo();
    }

    @RequestMapping
    public Object deliveryTask(@Valid @JsonrpcParam Version req, BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        taskInitService.deliveryTask(req.version);
        return true;
    }

    @RequestMapping
    public Object batchDeliveryTask(@Valid @JsonrpcParam ListString req, BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        req.version.forEach(v -> {
            taskInitService.deliveryTask(v);
        });
        return true;
    }

    /** SOP 全年需求任务初始化 */
    @RequestMapping
    public ReturnT<String> batchDeliveryAllDemandTask(@Valid @JsonrpcParam ListString req){
        req.version.forEach(v -> taskInitService.deliveryAllDemandTask(v));
        return ReturnT.ok();
    }

    /** SOP 全年需求任务执行 */
    @RequestMapping
    public ReturnT<String> doAllDemandTask(@Valid @JsonrpcParam ListString req){
        for (String yearWeek : req.getVersion()) {
            sopAllYearDemandService.generateAllYearDemand(yearWeek);
        }
        return ReturnT.ok();
    }

    @RequestMapping
    public ReturnT<List<YearWeek>> checkAllYearDemand(@Valid @JsonrpcParam ListString req){
        List<String> version = req.getVersion();
        if (ListUtils.isEmpty(version) || version.size() != 2){
            throw new IllegalArgumentException("参数错误");
        }
        return ReturnT.ok(sopAllYearDemandService.checkAllYearDemand(version.get(0),version.get(1)));
    }

    @RequestMapping
    public List<VersionDTO> listVersion() {
        return sopJxcReportService.listVersion();
    }

    @RequestMapping
    public List<String> queryParams(@JsonrpcParam QueryReportCommonReq req) {
        Assert.notNull(req.getEndVersion(), "getEndVersion is null");
        Assert.notNull(req.getStartVersion(), "getStartVersion is null");
        Assert.notNull(req.getStartYearMonth(), "getStartMonth is null");
        Assert.notNull(req.getEndYearMonth(), "getEndMonth is null");

        return sopJxcReportService.queryParams(req);
    }

    @RequestMapping
    public List<String> queryParamsByResource(@JsonrpcParam QueryReportParamsReq req) {
        Assert.notEmpty(req.getVersionList(), "getVersionList is null");
        Assert.notNull(req.getStartYearMonth(), "getStartMonth is null");
        Assert.notNull(req.getEndYearMonth(), "getEndMonth is null");

        return sopJxcReportService.queryParamsByResource(req);
    }

    /** 不区分CVM还是物理机的查询 */
    @Deprecated
    @RequestMapping
    public QueryReportItemResp queryReportItem(@JsonrpcParam QueryReportCommonReq req) {
        Assert.notNull(req.getEndVersion(), "getEndVersion is null");
        Assert.notNull(req.getStartVersion(), "getStartVersion is null");
        Assert.notNull(req.getStartYearMonth(), "getStartMonth is null");
        Assert.notNull(req.getEndYearMonth(), "getEndMonth is null");

        return sopJxcReportService.queryReportItem(req);
    }

    /** cvm跟物理机查不同版本 */
    @RequestMapping
    public QueryReportItemResp queryReportItemByResource(@JsonrpcParam QueryReportCommonResourceReq req) {
        Assert.notNull(req.getEndVersionCvm(), "getEndVersionCvm is null");
        Assert.notNull(req.getStartVersionCvm(), "getStartVersionCvm is null");
        Assert.notNull(req.getEndVersionDevice(), "getEndVersionDevice is null");
        Assert.notNull(req.getStartVersionDevice(), "getStartVersionDevice is null");

        Assert.notNull(req.getCommonReq(), "getCommonReq is null");
        Assert.notNull(req.getCommonReq().getStartYearMonth(), "getStartMonth is null");
        Assert.notNull(req.getCommonReq().getEndYearMonth(), "getEndMonth is null");
        return sopJxcReportService.queryReportItemByResource(req);
    }

    @RateLimit(limitPeriod = RedisLimitPeriodEnum.SECOND, limitCount = 10, customExceptionMessage = "请求受限，请稍后再试")
    @RequestMapping
    public ResponseEntity<InputStreamResource> exportByResource(@JsonrpcParam QueryReportCommonResourceReq req){
        Assert.notNull(req.getEndVersionCvm(), "getEndVersionCvm is null");
        Assert.notNull(req.getStartVersionCvm(), "getStartVersionCvm is null");
        Assert.notNull(req.getEndVersionDevice(), "getEndVersionDevice is null");
        Assert.notNull(req.getStartVersionDevice(), "getStartVersionDevice is null");

        Assert.notNull(req.getCommonReq(), "getCommonReq is null");
        Assert.notNull(req.getCommonReq().getStartYearMonth(), "getStartMonth is null");
        Assert.notNull(req.getCommonReq().getEndYearMonth(), "getEndMonth is null");
        return sopJxcReportService.exportByResource(req);
    }

    @Data
    static class Version {
        @NotEmpty
        private String version;
    }

    @Data
    static class ListString {
        @NotEmpty
        private List<String> version;
    }
}
