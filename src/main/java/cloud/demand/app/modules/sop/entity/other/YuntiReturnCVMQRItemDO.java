package cloud.demand.app.modules.sop.entity.other;

import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportReturnCvmDO;
import cloud.demand.app.modules.sop.enums.FlagType;
import cloud.demand.app.modules.sop.enums.ResourceType;
import cloud.demand.app.modules.sop.enums.ReturnExecStatus;
import cloud.demand.app.modules.sop.enums.SopResourcePool;
import cloud.demand.app.modules.sop.enums.codeVersion.SopCodeVersionEnum;
import cloud.demand.app.modules.sop_return.enums.CoreType;
import cloud.demand.app.modules.sop_return.enums.GenerationType;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/** 云梯退回的QR单 */
@Data
public class YuntiReturnCVMQRItemDO {
    /** item_id<br/>Column: [item_id] */
    @Column(value = "item_id")
    private Long itemId;

    /** order_id<br/>Column: [order_id] */
    @Column(value = "order_id")
    private String orderId;

    /** 事业群名称<br/>Column: [bg_name] */
    @Column(value = "bg_name")
    private String bgName;

    /** 部门名称<br/>Column: [dept_name] */
    @Column(value = "dept_name")
    private String deptName;

    /** 规划产品名称<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** 项目名称<br/>Column: [project_name] */
    @Column(value = "project_name")
    private String projectName;

    /** 需求经理评估可退回的日期<br/>Column: [plan_time] */
    @Column(value = "plan_time")
    private LocalDate planTime;

    /** 0 - 自研池\n1 - 公有池\n<br/>Column: [resource_pool_type] */
    @Column(value = "resource_pool_type")
    private Integer resourcePoolType;

    /** 核心类型，1小核心2中核心3大核心<br/>Column: [core_type] */
    @Column(value = "core_type")
    private Integer coreType;

    /** 国家名称<br/>Column: [country_name] */
    @Column(value = "country_name")
    private String countryName;

    /** 区域名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 城市名称<br/>Column: [city_name] */
    @Column(value = "city_name")
    private String cityName;

    /** 是否主流机型 1.是 2.否<br/>Column: [generation_type] */
    @Column(value = "generation_type")
    private Integer generationType;

    /** 机型族<br/>Column: [device_family_name] */
    @Column(value = "device_family_name")
    private String deviceFamilyName;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 实例规格<br/>Column: [instance_model] */
    @Column(value = "instance_model")
    private String instanceModel;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 自定义事业群<br/>Column: [custom_bg] */
    @Column(value = "custom_bg_name")
    private String customBgName;

    /** 台数 */
    @Column(value = "num")
    private BigDecimal num;

    /** 核心数 */
    @Column(value = "core_num")
    private BigDecimal coreNum;

    public static DwdSopReportReturnCvmDO transform(YuntiReturnCVMQRItemDO item) {
        DwdSopReportReturnCvmDO ret = new DwdSopReportReturnCvmDO();
        ret.setNum(item.getNum());
        ret.setCoreNum(item.getCoreNum());
        ret.setIsCa(FlagType.NO.getCode());
        ret.setIsExecuted(FlagType.YES.getCode());
        ret.setCoreType(CoreType.getNameByCode(item.getCoreType()));
        ret.setGenerationType(GenerationType.getNameByCode(item.getGenerationType()));
        ret.setIndexDate(item.getPlanTime());
        ret.setResType(ResourceType.CVM.getName());
        ret.setResPoolType(SopResourcePool.getDesc(String.valueOf(item.getResourcePoolType())));
        ret.setObsProjectType(item.getProjectName());
        ret.setBgName(item.getBgName());
        ret.setCustomBgName(item.getCustomBgName());
        ret.setDeptName(item.getDeptName());
        ret.setPlanProductName(item.getPlanProductName());
        ret.setCountryName(item.getCountryName());
        ret.setCityName(item.getCityName());
        ret.setTxyZoneName(item.getZoneName());
        ret.setCvmGinsFamily(item.getInstanceType());
        ret.setCvmGinsType(item.getInstanceModel());
        ret.setPhyDeviceType(item.getDeviceFamilyName());
        ret.setIsHedge(FlagType.NO.getCode());
        ret.setHasHedged(FlagType.NO.getCode());
        ret.setOrderId(item.getOrderId());
        ret.setItemId(item.getItemId()==null?null:String.valueOf(item.getItemId()));
        ret.setReturnExecStatus(ReturnExecStatus.FINISH.getName());
        ret.setCodeVersion(SopCodeVersionEnum.V1.getCode());
        return ret;
    }
}
