package cloud.demand.app.modules.sop.alert.raw;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.sop.alert.entity.SopCoreNumAlterDo;
import cloud.demand.app.modules.sop.alert.entity.SopQueryParam;
import com.pugwoo.dbhelper.DBHelper;
import java.util.List;

public class SourceDeviceReturnExeRaw extends AbstractSourceRaw<SopCoreNumAlterDo> {


    public SourceDeviceReturnExeRaw(SopQueryParam param) {
        super(param);
    }

    @Override
    public List<SopCoreNumAlterDo> transForm(List<SopCoreNumAlterDo> list) {
        return list;
    }


    @Override
    public List<SopCoreNumAlterDo> getRaw() {
        return getDbHelper().getRaw(getDbClass(),getSql(),
                param.getCloudBgName(),param.getVersion(),param.getStartDate()+ " 00:00:00");
    }

    @Override
    public DBHelper getDbHelper() {
        return DBList.ckCubesDBHelper;
    }


    @Override
    public String getSql() {
        return ORMUtils.getSql("/sql/sop/alert/source_return_device_executed.sql");
    }

    @Override
    public Class<SopCoreNumAlterDo> getDbClass() {
        return SopCoreNumAlterDo.class;
    }
}
