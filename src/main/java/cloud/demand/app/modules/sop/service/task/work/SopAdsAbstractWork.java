package cloud.demand.app.modules.sop.service.task.work;

import cloud.demand.app.modules.sop.entity.task.SopAdsTaskDO;
import cloud.demand.app.modules.sop.service.task.process.SopAdsProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.work.AbstractSopWork;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;

public abstract class SopAdsAbstractWork extends AbstractSopWork<SopAdsTaskDO> {
    @Resource
    protected SopAdsProcess process;

    /** 任务记录db操作类 */
    @Override
    public ISopProcess<SopAdsTaskDO> getTask() {
        return process;
    }

    @Override
    public ITaskEnum getEnum() {
        return task();
    }

    /** 任务名称枚举 */
    public abstract ITaskEnum task();

    /** 定义循环时间（1分钟一次） */
    @Scheduled(fixedRate = 60 * 1000)
    @Override
    public void work() {
        super.work();
    }
}
