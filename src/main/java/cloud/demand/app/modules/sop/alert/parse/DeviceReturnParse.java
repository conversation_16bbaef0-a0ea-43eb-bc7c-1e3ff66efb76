package cloud.demand.app.modules.sop.alert.parse;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.sop.alert.entity.SopQueryParam;
import cloud.demand.app.modules.sop.alert.raw.SourceDeviceReturnExeRaw;
import cloud.demand.app.modules.sop.alert.raw.SourceDeviceReturnNonExeRaw;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;

public class DeviceReturnParse extends AbstractSopCoreNumParse{


    public DeviceReturnParse(DBHelper dbHelper, SopQueryParam queryParam) {
        super(dbHelper, ListUtils.of(
                new SourceDeviceReturnExeRaw(queryParam),
                new SourceDeviceReturnNonExeRaw(queryParam)
        ), queryParam);
    }

    @Override
    protected String getSql() {
        return ORMUtils.getSql("/sql/sop/alert/sop_return_device.sql");
    }
}
