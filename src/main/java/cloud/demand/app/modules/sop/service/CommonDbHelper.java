package cloud.demand.app.modules.sop.service;


import cloud.demand.app.entity.plan.StaticGinstypeDO;
import cloud.demand.app.entity.resource.ServerPartsExtendedInfoPDO;
import cloud.demand.app.modules.sop.domain.report.VersionDTO;
import cloud.demand.app.modules.sop.entity.dict.DockerGpuCategoryStrategyDO;
import cloud.demand.app.modules.sop.entity.other.DemandForecastVersionNumberDO;
import cloud.demand.app.modules.sop.enums.Constant;
import cloud.demand.app.modules.sop.enums.DynamicProperties;
import cloud.demand.app.modules.sop.enums.ResourceType;
import cloud.demand.app.modules.sop.enums.SopBusinessType;
import cloud.demand.app.modules.sop.util.TwoMap;
import cloud.demand.app.modules.sop_return.service.impl.SopReturnCommonServiceImpl;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.MultiKeyMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基础清洗字段类
 * 1. 物理机获取核心数
 * 2. 物理机获取容量
 * 3. 物理机设备类型获取机型族
 * 4. 虚拟机获取核心数
 * 5. 虚拟机获取容量
 * 6. 虚拟机实例规格获取实例类型
 * 7. 通过规划产品获取部门/事业群/自定义事业群
 * 7.1. 规划产品-部门-事业群
 * 7.2. 规划产品-自定义事业群
 * 8. CMDB 物理机房体系 - 地区信息
 * 9. 腾讯云 云上园区体系 – 地区信息
 * 参考文档 <a href="https://iwiki.woa.com/pages/viewpage.action?pageId=4008554930">...</a>
 */
@Service
public class CommonDbHelper {

    @Resource
    DBHelper erpBakDBHelper;
    @Resource
    DBHelper demandDBHelper;
    @Resource
    private DBHelper resourcedbDBHelper;
    @Resource
    private DBHelper cdCommonDbHelper;

    @Resource
    private DBHelper yuntiDBHelper;
    @Resource
    private DBHelper yuntidemandDBHelper;
    @Resource
    private DBHelper obsDBHelper;
    @Resource
    private DBHelper matrixDBHelper;

    //    @PostConstruct
    public void init() {
//        this.getCvmCpuCoreMap();
//        this.getCvmCapacityMap();
//        Map<String, String> cvmFamilyMap = this.getCvmFamilyMap();
//        this.getDeviceCpuCoreMap();
//        Map<String, String> deviceFamilyMap = this.getDeviceFamilyMap();
//        this.getDeviceCapacityMap();
//            getBusinessType();
//        TwoMap<Integer, String, String> productCustomMap = this.getProductCustomMap();
//        TwoMap<Integer, String, ProductDeptBgInfo> productInfoMap = this.getProductInfoMap(null, null);
//        this.getModZoneInfoMap(null,null);
//        this.getZoneCityMap(null,null);
//        this.getCountry(null, null);
    }

    /**
     * 获取 CA 需求id列表
     * */
    public List<Long> getCADemandIdList(LocalDate dateVersion){
        String sql = "select distinct a.demand_id\n" +
                "        from yunti_demand.cvm_demand_for_hedging a\n" +
                "        left join yunti_demand.cvm_demand_record_for_hedging b on a.demand_id = b.demand_id and a.dateVersion = b.dateVersion\n" +
                "        where a.dateVersion = ? and b.order_id like 'QA%' and deleted = 0;";
        return demandDBHelper.getRaw(Long.class, sql, dateVersion);
    }

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 300)
    public List<VersionDTO> listVersion() {
        List<DemandForecastVersionNumberDO> doList = erpBakDBHelper.getAll(DemandForecastVersionNumberDO.class,
                "where ver_num >= ? and sop_hide = 0 order by insert_time desc", DynamicProperties.getMinVersion());
        return doList.stream()
                .map(VersionDTO::copy)
                .collect(Collectors.toList());
    }

    /**
     * 退回的最小版本
     */
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 300)
    public List<VersionDTO> listReturnVersion() {
        List<DemandForecastVersionNumberDO> doList = erpBakDBHelper.getAll(DemandForecastVersionNumberDO.class,
                "where ver_num >= ? and sop_hide = 0 order by insert_time desc",
                DynamicProperties.getMinReturnVersion());
        return doList.stream()
                .map(VersionDTO::copy)
                .collect(Collectors.toList());
    }

    public LocalDateTime getDateTimeFormVersion(String version) {
        DemandForecastVersionNumberDO ddo = erpBakDBHelper.getOne(DemandForecastVersionNumberDO.class,
                "where ver_num = ?", Long.parseLong(version)); // 字段类型匹配
        if (ddo == null) {
            throw new ITException("找不到采购预测版本" + version);
        }
        return DateUtils.toLocalDateTime(ddo.getInsertTime());
    }

    public LocalDate getStatTimeFromVersion(String version) {
        DemandForecastVersionNumberDO ddo = erpBakDBHelper.getOne(DemandForecastVersionNumberDO.class,
                "where ver_num = ?", Long.parseLong(version)); // 字段类型匹配
        if (ddo == null) {
            throw new ITException("找不到采购预测版本" + version);
        }
        return DateUtils.toLocalDate(ddo.getInsertTime());
    }

    /**
     * 获取 gpu 分类
     * 1. 版本取 t_resource.server_parts_extended_info default_flag = 1
     * 2. 设备类型重复的取 create_time最新的
     */
    @HiSpeedCache(expireSecond = 300, continueFetchSecond = 3600)
    public Map<String,DockerGpuCategoryStrategyDO> getDefaultVersionGpuInfo(){
        String sql = "select tr.* from (select device_type ,device_version from t_resource.server_parts_extended_info \n"
                + "      where default_flag = 1) tl left join t_resource.docker_gpu_category_strategy tr on tl.device_type = tr.device_code\n"
                + "      and tl.device_version = tr.version\n"
                + "      where tr.gpu_app_category != '未归类' and gpu_app_category != 'None'\n"
                + "      order by tr.create_time desc";
        // 如果有重复的，取最新 create_time的数据
        List<DockerGpuCategoryStrategyDO> raw = erpBakDBHelper.getRaw(DockerGpuCategoryStrategyDO.class, sql);
        Map<String,DockerGpuCategoryStrategyDO> ret = new HashMap<>();
        for (DockerGpuCategoryStrategyDO strategyDO : raw) {
            String deviceCode = strategyDO.getDeviceCode();
            if (!ret.containsKey(deviceCode)){
                ret.put(deviceCode, strategyDO);
            }
        }
        return ret;
    }

    /**
     * 获取物理机的cpu核数 物理机类型-cpu核数
     */
    @HiSpeedCache(expireSecond = 300, continueFetchSecond = 3600, cloneReturn = false)
    public Map<String, Integer> getDeviceCpuCoreMap() {
        List<ServerPartsExtendedInfoPDO> all = resourcedbDBHelper.getAll(ServerPartsExtendedInfoPDO.class,
                "where default_flag=1");
        return ListUtils.toMap(all, ServerPartsExtendedInfoPDO::getDeviceType,
                ServerPartsExtendedInfoPDO::getCpuLogicCore);
    }

    /**
     * 获取设备容量 物理机类型-容量
     */
    @HiSpeedCache(expireSecond = 300, continueFetchSecond = 3600, cloneReturn = false)
    public Map<String, DeviceCapacity> getDeviceCapacityMap() {
        List<DeviceCapacity> types = resourcedbDBHelper.getRaw(DeviceCapacity.class,
                "select NAME, UnitName, LogicNum from bas_stratege_device_type");
        return ListUtils.toMap(types, DeviceCapacity::getName, e -> {
            if (StringUtils.isBlank(e.getUnitName())) {
                e.setUnitName("(空值)");
            }
            return e;
        });
    }

    /**
     * 物理机设备类型获取机型族 物理机类型-机型族
     */
    @HiSpeedCache(expireSecond = 300, continueFetchSecond = 3600, cloneReturn = false)
    public Map<String, String> getDeviceFamilyMap() {
        List<DeviceFamilyName> types = resourcedbDBHelper.getRaw(DeviceFamilyName.class,
                "select NAME, DeviceFamilyName from bas_stratege_device_type");
        return types.stream()
                .filter((item) -> StringUtils.isNotBlank(item.getDeviceFamilyName()) && StringUtils.isNotBlank(
                        item.getName()))
                .collect(Collectors.toMap(DeviceFamilyName::getName, DeviceFamilyName::getDeviceFamilyName));

    }


    /**
     * 获取cvm的cup核数 实例规格-cpu核数
     */
    @HiSpeedCache(expireSecond = 300, continueFetchSecond = 3600, cloneReturn = false)
    public Map<String, Integer> getCvmCpuCoreMap() {
        List<StaticGinstypeDO> types = cdCommonDbHelper.getAll(StaticGinstypeDO.class);
        return ListUtils.toMap(types, StaticGinstypeDO::getGinstype, (item) -> (int) (item.getCpu() / 100));
    }

    /**
     * 获取cvm的cup核数 实例类型-cpu核数(取最小核数)
     */
    @HiSpeedCache(expireSecond = 300, continueFetchSecond = 3600, cloneReturn = false)
    public Map<String, StaticGinstypeDO> getCvmCpuCoreByTypeMap() {
        List<StaticGinstypeDO> types = cdCommonDbHelper.getAll(StaticGinstypeDO.class);
        Map<String, List<StaticGinstypeDO>> map = new HashMap<>();
        for (StaticGinstypeDO type : types) {
            map.computeIfAbsent(type.getGinsfamilyName(), item -> new ArrayList<>()).add(type);
        }
        Map<String, StaticGinstypeDO> ret = new HashMap<>();
        for (List<StaticGinstypeDO> value : map.values()) {
            value.stream().min(Comparator.comparing(StaticGinstypeDO::getCpu)).ifPresent(staticGinstypeDO ->
                    ret.put(staticGinstypeDO.getGinsfamilyName(), staticGinstypeDO));
        }
        return ret;
    }

    /**
     * 获取cvm的容量 实例类型-容量
     * 需要有实例规格的名字：如S5.MEDIUM2，作为入参
     * 从 MYSQL 表：obs3.bas_obs_cloud_cvm_type，获取 CvmInstanceModel等于入参的行记录，再判断实例族CvmInstanceGroup是什么类型，取什么表字段
     * 实例族包含：高 IO、大数据字样的，取 DiskBlockNum；含 GPU 的，取 GpuAmount；其他的取记录的核心数。
     */
    @HiSpeedCache(expireSecond = 300, cloneReturn = false)
    public Map<String, CvmCapacity> getCvmCapacityMap() {
        List<CvmCapacity> types = obsDBHelper.getRaw(CvmCapacity.class,
                "select CvmInstanceModel,CvmInstanceGroup, DiskBlockNum, GpuAmount from bas_obs_cloud_cvm_type");
        return ListUtils.toMap(types, CvmCapacity::getCvmInstanceModel, (item) -> item);
    }

    /**
     * 虚拟机实例规格获取实例类型 实例规格-实例类型
     */
    @HiSpeedCache(expireSecond = 300, cloneReturn = false)
    public Map<String, CvmFamilyName> getCvmFamilyMap() {
        List<CvmFamilyName> types = obsDBHelper.getRaw(CvmFamilyName.class,
                "select CvmInstanceModel, CvmInstanceType,CvmInstanceGroup from bas_obs_cloud_cvm_type");
        return types.stream()
                .filter((item) -> StringUtils.isNotBlank(item.getGinsFamilyName()) && StringUtils.isNotBlank(
                        item.getGinsType()))
                .collect(Collectors.toMap(CvmFamilyName::getGinsType, item -> item));
    }

    @HiSpeedCache(expireSecond = 300, cloneReturn = false)
    public Map<String, String> getCvmKingdomMap() {
        Map<String, CvmFamilyName> cvmFamilyMap = getCvmFamilyMap();
        Map<String, String> ret = new HashMap<>();
        for (CommonDbHelper.CvmFamilyName value : cvmFamilyMap.values()) {
            ret.put(value.getGinsFamilyName(), value.getGinskingdomName());
        }
        return ret;
    }

    /**
     * 虚拟机实例规格获取物理机设备类型 CVM实例规格-物理机设备类型
     */
    @HiSpeedCache(expireSecond = 300, cloneReturn = false)
    public Map<String, String> getDeviceTypeByCvm() {
        List<SopReturnCommonServiceImpl.CvmInstanceInfo> raw = obsDBHelper.getRaw(
                SopReturnCommonServiceImpl.CvmInstanceInfo.class,
                "select HostDeviceClass,CvmInstanceGroup ,CvmInstanceType ,CvmInstanceModel ,CoreType from bas_obs_cloud_cvm_type");

        raw = raw.stream().filter(item -> StringUtils.isNotBlank(item.getCvmInstanceModel()) &&
                StringUtils.isNotBlank(item.getHostDeviceClass())).collect(Collectors.toList());
        return ListUtils.toMap(raw, SopReturnCommonServiceImpl.CvmInstanceInfo::getCvmInstanceModel,
                SopReturnCommonServiceImpl.CvmInstanceInfo::getHostDeviceClass);
    }

    /**
     * 规划产品-部门
     */
    @HiSpeedCache(expireSecond = 300, cloneReturn = false)
    public TwoMap<Integer, String, ProductDeptBgInfo> getProductInfoMap() {
        String sbr = "select pp.PlanProductId, pp.PlanProductName, d.DeptId, d.DeptName\n" +
                " from bas_obs_plan_product pp\n" +
                "         left join bas_obs_business_dept d on d.DeptId = pp.VirtualDeptId\n" +
                " where pp.EnableFlag = 1 and d.EnableFlag = 1";
        List<ProductDeptBgInfo> types = obsDBHelper.getRaw(ProductDeptBgInfo.class, sbr);
        TwoMap<Integer, String, ProductDeptBgInfo> ret = new TwoMap<>();
        for (ProductDeptBgInfo type : types) {
            ret.put(type.getPlanProductId(), type.getPlanProductName(), type);
        }
        return ret;
    }

    /**
     * 规划产品-老部门
     */
    @HiSpeedCache(expireSecond = 300, cloneReturn = false)
    public TwoMap<Integer, String, ProductDeptBgInfo> getOldProductInfoMap() {
        String sbr = "select pp.PlanProductId, pp.PlanProductName, d.DeptId, d.DeptName\n" +
                "from bas_obs_plan_product pp\n" +
                "         left join bas_obs_business_dept d on d.DeptId = pp.DeptId\n" +
                " where pp.EnableFlag = 1 and d.EnableFlag = 1 ";
        List<ProductDeptBgInfo> types = obsDBHelper.getRaw(ProductDeptBgInfo.class, sbr);
        TwoMap<Integer, String, ProductDeptBgInfo> ret = new TwoMap<>();
        for (ProductDeptBgInfo type : types) {
            ret.put(type.getPlanProductId(), type.getPlanProductName(), type);
        }
        return ret;
    }

    /**
     * 部门-事业群
     */
    @HiSpeedCache(expireSecond = 300, cloneReturn = false)
    public TwoMap<Integer, String, DeptInfo> getDeptInfo() {
        String sbr = "select d.DeptId, d.DeptName, bg.BgId, bg.BgName\n" +
                "from bas_obs_business_dept d\n" +
                "left join bas_obs_bg bg on bg.BgId = d.BgId\n";
        List<DeptInfo> types = obsDBHelper.getRaw(DeptInfo.class, sbr);
        TwoMap<Integer, String, DeptInfo> ret = new TwoMap<>();
        for (DeptInfo type : types) {
            ret.put(type.getDeptId(), type.getDeptName(), type);
        }
        return ret;
    }

    /**
     * 事业群-业务类型
     */
    @HiSpeedCache(expireSecond = 300, cloneReturn = false)
    public Map<String, String> getBusinessType() {
        List<BgInfo> types = obsDBHelper.getRaw(BgInfo.class, "select BgName, BgType from bas_obs_bg");
        return types.stream()
                .filter((item) -> item.getBgType() != null && StringUtils.isNotBlank(item.getBgName()))
                .collect(Collectors.toMap(BgInfo::getBgName, (item) -> SopBusinessType.getByType(item.bgType)));
    }


    /**
     * 部门+事业群-自定义事业群
     */
    @HiSpeedCache(expireSecond = 300, cloneReturn = false)
    public MultiKeyMap<String, String> getProductCustomMap() {
        List<ProductCustomBg> types = yuntiDBHelper.getRaw(ProductCustomBg.class,
                "select dept_name,bg_name,custom_bg from yunti_demand_custom_bg");
        MultiKeyMap<String, String> ret = new MultiKeyMap<>();
        for (ProductCustomBg type : types) {
            if (StringUtils.isNotBlank(type.getCustomBg())) {
                ret.put(type.getDeptName(), type.getBgName(), type.getCustomBg());
                // 保底用事业群或者部门查，保证自定义事业群有数据
                ret.put(Constant.NULL, type.getBgName(), type.getCustomBg());
                ret.put(type.getDeptName(), Constant.NULL, type.getCustomBg());
            }
        }
        return ret;
    }

    @HiSpeedCache(expireSecond = 300, cloneReturn = false)
    public MultiKeyMap<String, String> getBigFamilyMap() {
        List<SopUtilBigFamilyMappingDO> data = demandDBHelper.getAll(SopUtilBigFamilyMappingDO.class);
        MultiKeyMap<String, String> ret = new MultiKeyMap<>();
        for (SopUtilBigFamilyMappingDO d : data) {
            ret.put(d.type, d.name, d.bigFamily);
        }
        // 空值处理为其他
        ret.put(ResourceType.CVM.getName(),Constant.EMPTY_VALUE_STR,Constant.OTHER_VALUE_STR);
        // 空值处理为其他
        ret.put(ResourceType.DEVICE.getName(),Constant.EMPTY_VALUE_STR,Constant.OTHER_VALUE_STR);
        // null处理为其他
        ret.put(ResourceType.CVM.getName(),null,Constant.OTHER_VALUE_STR);
        // null处理为其他
        ret.put(ResourceType.DEVICE.getName(),null,Constant.OTHER_VALUE_STR);
        return ret;
    }

    /** 物理机机型族/cvm机型族->大类 */
    @HiSpeedCache(expireSecond = 300, cloneReturn = false,keyScript = "args[0]")
    public Map<String,String> getBigFamilyMap(String resType){
        if (StringUtils.isBlank(resType)){
            return null;
        }
        List<SopUtilBigFamilyMappingDO> all = demandDBHelper.getAll(SopUtilBigFamilyMappingDO.class, "where type = ?", resType);
        Map<String,String> ret = new HashMap<>();
        if (ListUtils.isEmpty(all)){
            return ret;
        }
        for (SopUtilBigFamilyMappingDO mappingDO : all) {
            mappingDO.setBigFamily(StringUtils.deleteWhitespace(mappingDO.getBigFamily()));
            mappingDO.setName(StringUtils.deleteWhitespace(mappingDO.getName()));
        }
        for (SopUtilBigFamilyMappingDO mappingDO : all) {
            ret.put(mappingDO.getName(),mappingDO.getBigFamily());
        }
        // 空值处理为其他
        ret.put(Constant.EMPTY_VALUE_STR,Constant.OTHER_VALUE_STR);
        // null处理为其他
        ret.put(null,Constant.OTHER_VALUE_STR);
        return ret;
    }
    @HiSpeedCache(expireSecond = 300, continueFetchSecond = 600)
    public List<CustomBgByProduct> gePlanProductInfo(){
        return yuntiDBHelper.getRaw(CustomBgByProduct.class,
                "select custom_bg ,bg_name, dept_name,plan_product_name from yunti.yunti_demand_custom_bg where deleted = 0");
    }

    @HiSpeedCache(expireSecond = 300, cloneReturn = false)
    public Map<Integer, String> getZoneInfoMap() {
        List<ZoneInfo> zoneInfoList = resourcedbDBHelper.getRaw(ZoneInfo.class,
                "select ZoneId,ZoneName from bas_dis_zone");
        Map<Integer, String> ret = new HashMap<>();
        for (ZoneInfo zoneInfo : zoneInfoList.stream().
                filter(zoneInfo -> StringUtils.isNotBlank(zoneInfo.getZoneName())).
                collect(Collectors.toList())) {
            ret.put(zoneInfo.getZoneId(), zoneInfo.getZoneName());
        }
        return ret;
    }

    @HiSpeedCache(expireSecond = 300, cloneReturn = false)
    public Map<String, String> getZoneCodeInfoMap() {
        List<ZoneCodeInfo> zoneInfoList = yuntiDBHelper.getRaw(ZoneCodeInfo.class,
                "select zone,zone_name from yunti.yunti_stategy_zone");
        Map<String, String> ret = new HashMap<>();
        for (ZoneCodeInfo zoneInfo : zoneInfoList.stream().
                filter(zoneInfo -> StringUtils.isNotBlank(zoneInfo.getZoneName())).
                collect(Collectors.toList())) {
            ret.put(zoneInfo.getZoneName(), zoneInfo.getZoneCode());
        }
        return ret;
    }

    /**
     * CMDB 物理机房体系 - 地区信息
     */
    public MultiKeyMap<String, ModZoneInfo> getModZoneInfoMap(Set<String> modules, Set<String> campusList) {
        StringBuilder sbr = new StringBuilder();
        sbr.append("select m.ModName as module,\n" +
                "       c.Name as campus,\n" +
                "       z.ZoneName,\n" +
                "       z.CityName,\n" +
                "       r.Name as region,\n" +
                "       r.CountryName,\n" +
                "       if(r.CountryName = '中国内地', '国内', '海外') as areaType \n" +
                "from bas_dis_module m \n" +
                "left join bas_dis_zone c on m.SzoneId = c.Id \n" +
                "left join bas_dis_parent_zone z on c.ZoneId = z.ZoneId \n" +
                "left join bas_dis_region r on z.AreaId = r.Id\n");
        if (!CollectionUtils.isEmpty(modules) || !CollectionUtils.isEmpty(campusList)) {
            sbr.append("where  ");
            if (!CollectionUtils.isEmpty(modules)) {
                sbr.append("m.ModName in ('");
                sbr.append(StringUtils.join(modules, "','"));
                sbr.append("')");
                if (!CollectionUtils.isEmpty(campusList)) {
                    sbr.append(" or c.Name in ('");
                    sbr.append(StringUtils.join(campusList, "','"));
                    sbr.append("')");
                }
            } else if (!CollectionUtils.isEmpty(campusList)) {
                sbr.append("c.Name in ('");
                sbr.append(StringUtils.join(campusList, "','"));
                sbr.append("')");
            }
        }
        List<ModZoneInfo> types = resourcedbDBHelper.getRaw(ModZoneInfo.class, sbr.toString());
        MultiKeyMap<String, ModZoneInfo> ret = new MultiKeyMap<>();
        for (ModZoneInfo type : types) {
            ret.put(type.getCampus(), type.getModule(), type);
            // 留一个保底值（如果没有module进行匹配则用campus的数据）
            ret.put(type.getCampus(), Constant.NULL, type);
        }
        return ret;
    }

    /**
     * 腾讯云 云上园区体系 – 地区信息
     */
    public TwoMap<String, String, ZoneRegionInfo> getZoneCityMap(Set<String> zoneList, Set<String> cityList) {
        StringBuilder sbr = new StringBuilder();
        sbr.append("select zone_name,\n" +
                "       city,\n" +
                "       region_name, \n" +
                "       if(inland = 0, '国内', '海外') as areaType \n" +
                "from yunti_stategy_zone\n");
        if (!CollectionUtils.isEmpty(zoneList) || !CollectionUtils.isEmpty(cityList)) {
            sbr.append("where  ");
            if (!CollectionUtils.isEmpty(zoneList)) {
                sbr.append("zone_name in ('");
                sbr.append(StringUtils.join(zoneList, "','"));
                sbr.append("')");
                if (!CollectionUtils.isEmpty(cityList)) {
                    sbr.append(" or city in ('");
                    sbr.append(StringUtils.join(cityList, "','"));
                    sbr.append("')");
                }
            } else if (!CollectionUtils.isEmpty(cityList)) {
                sbr.append("city in ('");
                sbr.append(StringUtils.join(cityList, "','"));
                sbr.append("')");
            }
        }
        List<ZoneRegionInfo> types = yuntiDBHelper.getRaw(ZoneRegionInfo.class, sbr.toString());
        TwoMap<String, String, CommonDbHelper.ZoneRegionInfo> ret = new TwoMap<>();
        for (ZoneRegionInfo type : types) {
            ret.put(type.getZoneName(), type.getCityName(), type);
        }
        return ret;
    }

    /**
     * 通过zone或者city找国家
     */
    public TwoMap<String, String, String> getCountry(Set<String> zoneList, Set<String> cityList) {
        StringBuilder sbr = new StringBuilder();
        sbr.append("select CityName,ZoneName,CountryChinese \n" +
                "from bas_cmdb_city\n");
        if (!CollectionUtils.isEmpty(zoneList) || !CollectionUtils.isEmpty(cityList)) {
            sbr.append("where  ");
            if (!CollectionUtils.isEmpty(zoneList)) {
                sbr.append("ZoneName in ('");
                sbr.append(StringUtils.join(zoneList, "','"));
                sbr.append("')");
                if (!CollectionUtils.isEmpty(cityList)) {
                    sbr.append(" or CityName in ('");
                    sbr.append(StringUtils.join(cityList, "','"));
                    sbr.append("')");
                }
            } else if (!CollectionUtils.isEmpty(cityList)) {
                sbr.append("CityName in ('");
                sbr.append(StringUtils.join(cityList, "','"));
                sbr.append("')");
            }
        }
        List<CityZoneCountry> raw = resourcedbDBHelper.getRaw(CityZoneCountry.class, sbr.toString());
        TwoMap<String, String, String> map = new TwoMap<>();
        for (CityZoneCountry cityZoneCountry : raw) {
            map.put(cityZoneCountry.getZoneName(), cityZoneCountry.getCityName(), cityZoneCountry.getCountryChinese());
        }
        return map;
    }

    // =============== 实体类 ================

    @Data
    public static class ZoneInfo {

        @Column("ZoneId")
        private Integer zoneId;

        @Column("ZoneName")
        private String zoneName;

    }

    @Data
    public static class ZoneCodeInfo {

        @Column("zone")
        private String zoneCode;

        @Column("zone_name")
        private String zoneName;

    }

    @Data
    public static class DeviceCapacity {

        @Column("NAME")
        private String name;

        @Column("LogicNum")
        private Integer logicNum;

        @Column("UnitName")
        private String unitName;
    }

    @Data
    public static class DeviceFamilyName {

        @Column("NAME")
        private String name;

        @Column("DeviceFamilyName")
        private String deviceFamilyName;
    }

    @Data
    public static class CvmCapacity {

        @Column("CvmInstanceModel")
        private String cvmInstanceModel;

        @Column("CvmInstanceGroup")
        private String cvmInstanceGroup;

        @Column("DiskBlockNum")
        private BigDecimal diskBlockNum;

        @Column("GpuAmount")
        private BigDecimal gpuAmount;
    }

    @Data
    public static class CvmFamilyName {

        @Column("CvmInstanceModel")
        private String ginsType;

        @Column("CvmInstanceType")
        private String ginsFamilyName;

        @Column("CvmInstanceGroup")
        private String ginskingdomName;
    }

    @Data
    public static class ProductDeptBgInfo {

        @Column("PlanProductId")
        private Integer planProductId;

        @Column("planProductName")
        private String planProductName;

        @Column("DeptId")
        private Integer deptId;

        @Column("DeptName")
        private String deptName;

    }


    @Data
    public static class DeptInfo {

        @Column("DeptId")
        private Integer deptId;

        @Column("DeptName")
        private String deptName;

        @Column("BgId")
        private Integer bgId;

        @Column("BgName")
        private String bgName;

    }

    @Data
    public static class BgInfo {

        @Column("BgName")
        private String bgName;

        @Column("BgType")
        private Integer bgType;
    }

    @Data
    public static class ProductCustomBg {

        @Column("dept_name")
        private String deptName;

        @Column("bg_name")
        private String bgName;

        @Column("custom_bg")
        private String customBg;

    }

    @Data
    public static class CustomBgByProduct {

        @Column("plan_product_name")
        private String planProductName;

        @Column("bg_name")
        private String bgName;

        @Column("dept_name")
        private String deptName;
        @Column("custom_bg")
        private String customBg;

    }

    @Data
    public static class ModZoneInfo {

        @Column("module")
        private String module;

        @Column("campus")
        private String campus;

        @Column("ZoneName")
        private String zoneName;

        @Column("CityName")
        private String cityName;

        @Column("region")
        private String region;

        @Column("CountryName")
        private String countryName;
        @Column("areaType")
        private String areaType;


    }


    @Data
    public static class ZoneRegionInfo {

        @Column("zone_name")
        private String zoneName;

        @Column("city")
        private String cityName;

        @Column("region_name")
        private String region;

        @Column("areaType")
        private String areaType;
    }

    @Data
    public static class CityZoneCountry {

        @Column("CityName")
        private String cityName;

        @Column("ZoneName")
        private String zoneName;

        @Column("CountryChinese")
        private String countryChinese;
    }

    @Data
    @ToString
    @Table("sop_util_big_family_mapping")
    public static class SopUtilBigFamilyMappingDO {

        /**
         * 机型族<br/>Column: [name]
         */
        @Column(value = "name")
        private String name;

        /**
         * 大类<br/>Column: [big_family]
         */
        @Column(value = "big_family")
        private String bigFamily;

        /**
         * 类型：物理机或CVM<br/>Column: [type]
         */
        @Column(value = "type")
        private String type;
    }
}
