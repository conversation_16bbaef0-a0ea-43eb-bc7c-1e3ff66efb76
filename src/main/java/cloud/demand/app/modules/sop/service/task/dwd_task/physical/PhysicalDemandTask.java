package cloud.demand.app.modules.sop.service.task.dwd_task.physical;

import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.sop.domain.http.SopServerDemandResList;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportDemandDeviceDO;
import cloud.demand.app.modules.sop.enums.Constant;
import cloud.demand.app.modules.sop.enums.FlagType;
import cloud.demand.app.modules.sop.enums.SelfToCloudType;
import cloud.demand.app.modules.sop.enums.SopResourcePool;
import cloud.demand.app.modules.sop.enums.codeVersion.SopCodeVersionEnum;
import cloud.demand.app.modules.sop.enums.codeVersion.version.SopCodeVersionDwdEnum;
import cloud.demand.app.modules.sop.enums.codeVersion.version.SopDoProcessType;
import cloud.demand.app.modules.sop.enums.task.DwdTaskEnum;
import cloud.demand.app.modules.sop.service.task.dwd_task.DWDTaskService;
import cloud.demand.app.modules.sop.service.task.dwd_task.physical.custom_ods.PhysicalDemandOdsTask;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop.util.DataTransformUtil;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 物理机需求预测 DWD 接口
 */
@Service
@Slf4j
public class PhysicalDemandTask extends DWDTaskService {

    @Qualifier("physicalDemandOdsTaskV1Impl")
    @Resource
    PhysicalDemandOdsTask physicalDemandOdsTaskV1;

    @Qualifier("physicalDemandOdsTaskV2Impl")
    @Resource
    PhysicalDemandOdsTask physicalDemandOdsTaskV2;


    @Override
    public DwdTaskEnum task() {
        return DwdTaskEnum.PHYSICAL_NEW_DEMAND;
    }

    @SneakyThrows
    @Override
    // 这个 taskLog 是唯一没法抽象的东西，只能自己写好这个字符串，规则："sopCreateDWDData@" + 类名
    @TaskLog(taskName = "sopCreateDWDData@PhysicalNewDemandTask")
    public void createDWDData(String version) {

        StopWatch stopWatch = new StopWatch("物理机需求预测");
        stopWatch.start("1. 情况ck版本分区数据");
        delete(version, DwdSopReportDemandDeviceDO.class);
        stopWatch.stop();
        stopWatch.start("2. 请求上游api获取退回数据");

        LocalDate dateVersion = commonDbHelper.getStatTimeFromVersion(version);
        AtomicLong startId = new AtomicLong(CkDBUtils.startId());
        LocalDateTime statTime = DateUtils.toLocalDateTime(new Date());

        List<String> sopVersionCode = SopCodeVersionEnum.getCodeList();
        for (String versionCode : sopVersionCode) {
            doProcessWithCodeVersion(version,dateVersion,startId,statTime,versionCode);
        }
    }

    private void doProcessWithCodeVersion(String version,
                                          LocalDate dateVersion,
                                          AtomicLong startId,
                                          LocalDateTime statTime,
                                          String codeVersion) {
        //已执行
        doProcess(version, true, dateVersion, startId, statTime, codeVersion);
        //未执行
        doProcess(version, false, dateVersion, startId, statTime, codeVersion);
    }

    private void doProcess(String version,
                           boolean isExecuted,
                           LocalDate indexDate,
                           AtomicLong startId,
                           LocalDateTime statTime,
                           String codeVersion) {

        // 检查是否需要执行
        SopDoProcessType sopDoProcessType = SopCodeVersionDwdEnum.getDoProcessType(task(), codeVersion, isExecuted);

        if (sopDoProcessType == SopDoProcessType.notDo){
            return;
        }

        // 获取转换后的代码版本
        String saveCodeVersion = sopDoProcessType.getGetCodeVersion().apply(codeVersion);

        SopCodeVersionEnum v_code = SopCodeVersionEnum.getDefaultByCode(codeVersion);

        PhysicalDemandOdsTask task = (v_code == SopCodeVersionEnum.V1)?physicalDemandOdsTaskV1:physicalDemandOdsTaskV2;

        String className = task.getClass().getName();

        log.info("使用的是采购预测{}的结果表",
                className.contains("1") ? "1.0" : className.contains("2") ? "2.0" : "其他");
        List<SopServerDemandResList> body;
        if (isExecuted) {
            body = task.offerExecutedNum(version, getStartYear());
        } else {
            body = task.offerNonExecutedNum(version, getStartYear());
        }
        if (!CollectionUtils.isEmpty(body)) {
            // 初始化本地清洗映射结合(可追加)
            initLocalMap(body);
            List<DwdSopReportDemandDeviceDO> saveData = getSaveData(version, isExecuted, indexDate, startId, statTime,
                    body,saveCodeVersion);
            insert(saveData);
        }
    }

    private List<DwdSopReportDemandDeviceDO> getSaveData(String version,
                                                         boolean isExecuted,
                                                         LocalDate indexDate,
                                                         AtomicLong startId,
                                                         LocalDateTime statTime,
                                                         List<SopServerDemandResList> body,
                                                         String codeVersion) {
        if (isExecuted) {
            return body.stream()
                    .filter((item) -> DataTransformUtil.isPositive(item.getExeAmount()))
                    .map((item) -> transform(item, version, isExecuted, startId, statTime,codeVersion))
                    .collect(Collectors.toList());
        } else {
            return ListUtils.transform(body,
                    (item) -> transform(item, version, isExecuted, startId, statTime,codeVersion));
        }
    }

    private DwdSopReportDemandDeviceDO transform(SopServerDemandResList data,
                                                 String version,
                                                 boolean isExec,
                                                 AtomicLong startId,
                                                 LocalDateTime statTime,
                                                 String codeVersion) {
        DwdSopReportDemandDeviceDO resultDO = new DwdSopReportDemandDeviceDO();

        ods(data, resultDO);
        resultDO.setCodeVersion(codeVersion);
        resultDO.setVersion(version);
        resultDO.setId(startId.getAndIncrement());
        resultDO.setStatTime(statTime);
        resultDO.setNum(ObjectUtils.defaultIfNull(isExec ? data.getExeAmount() : data.getNonAmount(), BigDecimal.ZERO));
        // 默认isCa为否
        resultDO.setIsCa(FlagType.NO.getCode());
        resultDO.setDeptName(data.getDeptName());
        resultDO.setBusinessType(data.getBusinessType());
        resultDO.setModBizType(StringUtils.isBlank(data.getModBizType()) ? Constant.EMPTY_VALUE_STR: data.getModBizType());
        resultDO.setObsBusinessType(data.getBusinessType());
        SopDateUtils.dateTransform(data, resultDO);
        resultDO.setResType(data.getResourceType());
        resultDO.setResPoolType(SopResourcePool.getDesc(data.getResourcePool()));
        resultDO.setObsProjectType(data.getProjectType());
        resultDO.setPlanProductName(data.getPlanProductName());
        resultDO.setCustomhouseTitle(data.getAreaType());
        resultDO.setCountryName(data.getCountry());
        resultDO.setCityName(data.getCity());
        resultDO.setCmdbCampusName(data.getCampus());
        resultDO.setCmdbModuleName(data.getModule());
        resultDO.setPhyDeviceType(data.getDeviceType());
        resultDO.setPhyDeviceFamily(data.getDeviceFamily());
        resultDO.setIsHedge(data.getIsHedging());
        resultDO.setHasHedged(data.getIsValidHedging());
        // 设置是否自研上云（仅限物理机需求）
        resultDO.setSelfToCloudType(SelfToCloudType.getNameByBol(
                Objects.equals(resultDO.getObsProjectType(), Constant.SELF_2_CLOUD_PROJECT_NAME)));
        resultDO.setJsonText(null);

        //判断是否执行
        if (isExec) {
            resultDO.setIsExecuted(FlagType.YES.getCode());
        } else {
            resultDO.setIsExecuted(FlagType.NO.getCode());
        }

        // clean以下字段：DeviceFamily  CoreNum  Capacity  BgName  CustomBgName  DeptName
        cleaning(resultDO);

        // 以下字段没有值，使用默认值
        //resultDO.setTxyZoneName();

        return resultDO;
    }

    private void ods(SopServerDemandResList data, DwdSopReportDemandDeviceDO dwd) {
        dwd.setOdsDate(data.getDate());
        dwd.setOdsYear(data.getYear());
        dwd.setOdsMonth(data.getMonth());
        dwd.setOdsPlanProductName(data.getPlanProductName());
        // bg name 暂时缺失
        dwd.setOdsMarketId(data.getDemandForecastMarketId()); // 这个字段暂时存原始预测切片的需求 id
        dwd.setOdsRequireDate(data.getRequireDate());
        dwd.setOdsOrderId(data.getOrderId());
    }
}
