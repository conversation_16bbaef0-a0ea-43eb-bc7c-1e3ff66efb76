package cloud.demand.app.modules.sop.service.impl;

import cloud.demand.app.modules.mrpv2.task.process.SimpleCommonTaskProcess;
import cloud.demand.app.modules.sop.anno.OperationLog;
import cloud.demand.app.modules.sop.util.TaskUtil;
import com.pugwoo.wooutils.collect.ListUtils;
import cs.easily.tp.wx.domian.WxPushAttachment;
import cs.easily.tp.wx.domian.WxPushAttachment.Action;
import cs.easily.tp.wx.domian.WxPushAttachment.Attachment;
import cs.easily.tp.wx.service.WxProcessService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class WxProcessServiceImpl implements WxProcessService {

    @Resource
    private SimpleCommonTaskProcess process;

    @OperationLog(namespace = "CLOUD-DEMAND-APP", name = "WxProcess")
    @Override
    public void process(String msg) {

    }

    @OperationLog(namespace = "CLOUD-DEMAND-APP", name = "WxProcessAttachment", keyScript = "args[0].attachment.callbackId")
    @Override
    public void process(WxPushAttachment xml) {
        Attachment attachment = xml.getAttachment();
        if (attachment != null){
            String callbackId = attachment.getCallbackId();
            List<Action> actions = attachment.getActions();
            if (ListUtils.isNotEmpty(actions)){
                Action action = actions.get(0);
                int i = callbackId.lastIndexOf("=");
                String idStr = callbackId.substring(i + 1);
                Long id = Long.valueOf(idStr);
                process.updateStatus(ListUtils.newArrayList(id), action.getValue(),null);
                TaskUtil.simpleAlertError(String.format("操作成功：任务 id：【%s】，任务状态：【%s】，操作人：【%s】", id, action.getValue(),"<@" + xml.getFrom().getAlias() + ">"));
            }
        }
    }
}
