package cloud.demand.app.modules.sop.anno;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/** 操作日志 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {
    /** 命名空间 */
    String namespace() default "#";

    /** 操作名称 */
    String name();

    /** 是否记录操作详细，是的话会把 OperationBuilder 信息存到 ThreadLocal 中 */
    boolean hasDetail() default false;

    /** 操作 key 的表达式脚本  */
    String keyScript() default "";
}
