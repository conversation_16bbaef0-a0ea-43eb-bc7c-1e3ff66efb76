package cloud.demand.app.modules.sop.util;

import cloud.demand.app.modules.sop.enums.Constant;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.Getter;


/**
 * 两个key对应同一个v集合
 * @param <K1>  key1类型
 * @param <K2>  key2类型
 * @param <V>   value类型
 */

@Getter
public class TwoMap<K1,K2,V> {
    private final Map<K1,V> map1;
    private final Map<K2,V> map2;

    public TwoMap(){
        map1 = new HashMap<>();
        map2 = new HashMap<>();
    }

    public TwoMap(Map<K1,V> map1,Map<K2,V> map2){
        this.map1 = map1;
        this.map2 = map2;
    }

    /**
     * kv设值
     * @param k1 key1
     * @param k2 key2
     * @param v value
     */
    public void put(K1 k1,K2 k2,V v){
        if (k1 != null){
            map1.put(k1,v);
        }
        if (k2 != null){
            map2.put(k2,v);
        }
    }


    /**
     * 集合合并
     * @param twoMap 被合并集合
     */
    public void putAll(TwoMap<K1,K2,V> twoMap){
        if (twoMap == null){return;}
        this.map1.putAll(twoMap.map1);
        this.map2.putAll(twoMap.map2);
    }


    /**
     * 通过key1或者key2获取value
     * @param k1 key1
     * @param k2 key2
     * @return value
     */
    public V get(K1 k1,K2 k2){
        if ((k1 == null || Objects.equals(Constant.EMPTY_VALUE_STR,k1)) && (k2 == null || Objects.equals(Constant.EMPTY_VALUE_STR,k2)) ){
            return null;
        }
        V ret = null;
        if (k1 != null && !Objects.equals(Constant.EMPTY_VALUE_STR,k1)){
            ret = map1.get(k1);
        }
        return ret == null? map2.get(k2): ret;
    }

    /**
     * key1获取value
     * @param k1 key1
     * @return value
     */
    public V get1(K1 k1){
        return map1.get(k1);
    }


    /**
     * key2获取value
     * @param k2 key2
     * @return value
     */
    public V get2(K2 k2){
        return map2.get(k2);
    }


    /**
     * 通过key1获取value或者默认值
     * @param k1 key1
     * @param def 默认值
     * @return 命中或者默认值
     */
    public V get1OrDefault(K1 k1,V def){
        return map1.getOrDefault(k1,def);
    }

    /**
     * 通过key2获取value或者默认值
     * @param k2 key2
     * @param def 默认值
     * @return 命中或者默认值
     */
    public V get2OrDefault(K2 k2,V def){
        return map2.getOrDefault(k2,def);
    }
}
