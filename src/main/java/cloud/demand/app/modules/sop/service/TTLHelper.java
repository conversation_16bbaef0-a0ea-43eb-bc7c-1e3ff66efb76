package cloud.demand.app.modules.sop.service;


import cloud.demand.app.common.config.DBList;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportDemandCvmDO;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportDemandDeviceDO;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportHedgingDO;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportInventoryEndAvailableDO;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportInventoryUnavailableDO;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportPgDO;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportProcurementDeviceDO;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportReplacementDeviceDO;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportReturnCvmDO;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportReturnDeviceDO;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportTransferDeviceDO;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportTransformationDeviceDO;
import cloud.demand.app.modules.sop.entity.dws.DwsSopReportMifDO;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import com.pugwoo.dbhelper.DBHelper;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import yunti.boot.config.DynamicProperty;

/** 淘汰策略，在规矩时间删除指定版本数据（dwd层+dws层，不对ads层进行淘汰） */
@Slf4j
@Service
public class TTLHelper {

    /** 是否开启淘汰策略，true：开启，false：关闭 ,默认关闭*/
    private final Supplier<String> enable = DynamicProperty.create("sop.ttl.enable","false");

    /** 保留数量：保留最久多少个版本的dwd+dws数据，默认最久三次版本 */
    private final Supplier<String> retain = DynamicProperty.create("sop.ttl.retain","3");

    /** 版本白名单：dwd层+dws层禁止删除的版本，字符串，用,号分割，默认空 */
    private final Supplier<String> white = DynamicProperty.create("sop.ttl.white","");

    @Resource
    protected DBHelper ckcldStdCrpDBHelper;


    private boolean getEnable(){
        return Objects.equals(enable.get().trim(),"true");
    }

    private int getRetain(){
        int ret = 0;
        try{
            ret = Integer.parseInt(retain.get().trim());
        }catch (Exception ignore){}
        return Math.max(ret,0);
    }

    private List<String> getWhite(){
        String whiteStr = white.get();
        List<String> ret = new ArrayList<>();
        if (StringUtils.isBlank(whiteStr)){
            return ret;
        }
        String[] split = whiteStr.split("[,，]");
        for (String str : split) {
            if (StringUtils.isNotBlank(str)){
                ret.add(str);
            }
        }
        return ret;
    }

    /**
     * 执行ttl
     */
    public void ttl(){
        // 参数准备，保留数量，版本白名单，是否启用
        int retainNum = getRetain();
        List<String> whiteList = getWhite();
        boolean enableFlag = getEnable();
        log.info("执行ttl：动态参数 --> 是否启用: [{}], 保留数量：[{}], 版本白名单：[{}]",enableFlag,retainNum,StringUtils.join(whiteList,","));

        if (!enableFlag){
            return;
        }
        // 获取已完成的记录
        List<String> ttlVersionList = getTTLVersionList(retainNum, whiteList);
        // 过滤在dwd层为空的版本
        ttlVersionList = filterEmptyCount(ttlVersionList);
        log.info("执行ttl：待删除版本 --> size: [{}], versions：[{}]",ttlVersionList.size(),StringUtils.join(ttlVersionList,","));
        // 执行删除
        if (!CollectionUtils.isEmpty(ttlVersionList)){
            ttlVersionList.forEach(this::doTtl);
        }
    }


    /**
     * 返回需要删除的version集合
     * @param retainNum 保留个数
     * @param whiteList 版本白名单
     * @return 需要删除的version集合
     */
    private List<String> getTTLVersionList(int retainNum,List<String> whiteList){
        List<String> raw;
        if (CollectionUtils.isEmpty(whiteList)){
            raw = DBList.demandDBHelper.getRaw(String.class,
                    "select ads.version from sop_ads_task ads where ads.status = 'FINISH' \n" +
                            "and not exists (select 1 from sop_ads_task temp_das where temp_das.version = ads.version and temp_das.status != 'FINISH')\n" +
                            "and not exists (select 1 from sop_dws_task temp_dws where temp_dws.version = ads.version and temp_dws.status != 'FINISH')\n" +
                            "and not exists (select 1 from sop_dwd_task temp_dwd where temp_dwd.version = ads.version and temp_dwd.status != 'FINISH')\n" +
                            "group by ads.version\n" +
                            "order by ads.version desc\n");
        }else {
            raw = DBList.demandDBHelper.getRaw(String.class,
                    "select ads.version from sop_ads_task ads where ads.status = 'FINISH' \n" +
                            "and not exists (select 1 from sop_ads_task temp_das where temp_das.version = ads.version and temp_das.status != 'FINISH')\n" +
                            "and not exists (select 1 from sop_dws_task temp_dws where temp_dws.version = ads.version and temp_dws.status != 'FINISH')\n" +
                            "and not exists (select 1 from sop_dwd_task temp_dwd where temp_dwd.version = ads.version and temp_dwd.status != 'FINISH')\n" +
                            "and ads.version not in (?)\n" +
                            "group by ads.version\n" +
                            "order by ads.version desc\n",whiteList);
        }
        if (raw.size() <= retainNum){
            return new ArrayList<>();
        }
        return raw.subList(retainNum,raw.size());
    }


    /**
     * 过滤dwd层记录数为空的版本
     * @param ttlVersionList 待删除的记录
     * @return 返回记录数不为空的版本
     */
    private List<String> filterEmptyCount(List<String> ttlVersionList){
        if (CollectionUtils.isEmpty(ttlVersionList)){
            return ttlVersionList;
        }
        return ckcldStdCrpDBHelper.getRaw(String.class,
                "select version from dws_sop_report_mif where version in (?) group by version having count(0) > 0",
                ttlVersionList);
    }


    /**
     * 执行删除
     * @param version 待删除的版本
     */
    private void doTtl(String version){
        for (Class<?> table : getTables()) {
            CkDBUtils.delete(ckcldStdCrpDBHelper,version,table);
        }
    }
    
    public List<Class<?>> getTables(){
        return Arrays.asList(
                // dwd层
                // cvm需求
                DwdSopReportDemandCvmDO.class,
                // 物理机需求
                DwdSopReportDemandDeviceDO.class,
                // 对冲
                DwdSopReportHedgingDO.class,
                // 期末可用
                DwdSopReportInventoryEndAvailableDO.class,
                // 期末不可用
                DwdSopReportInventoryUnavailableDO.class,
                // 采购净预测
                DwdSopReportPgDO.class,
                // 采购物理机
                DwdSopReportProcurementDeviceDO.class,
                // 物理机置换
                DwdSopReportReplacementDeviceDO.class,
                // cvm退回
                DwdSopReportReturnCvmDO.class,
                // 物理机退回
                DwdSopReportReturnDeviceDO.class,
                // 物理机转移
                DwdSopReportTransferDeviceDO.class,
                // 物理机改造
                DwdSopReportTransformationDeviceDO.class,
                // dws层
                DwsSopReportMifDO.class
        );
    }
}
