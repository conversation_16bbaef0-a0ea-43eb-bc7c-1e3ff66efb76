package cloud.demand.app.modules.sop.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

// 业务类型
@Getter
@AllArgsConstructor
public enum SopUtilBusinessType {
    SELF(1, "自研"),
    CLOUD(2, "公有云"),
    ;
    private final Integer type;
    private final String desc;

    /** 根据自定义事业群获取sop工具业务类型 */
    public static String getByCustomBg(String customBgName) {
        if (StringUtils.isBlank(customBgName) || Objects.equals(customBgName,Constant.EMPTY_VALUE_STR)){
            return Constant.EMPTY_VALUE_STR;
        }
        return Objects.equals(customBgName,Constant.CSIG_BG_NAME)? CLOUD.getDesc() : SELF.getDesc();
    }
}
