package cloud.demand.app.modules.sop.service.orderid.impl;

import cloud.demand.app.modules.sop.service.orderid.YuntiOrderIdService;
import cloud.demand.app.modules.sop.service.orderid.entity.YuntiReturnPlanCvmItemRecordDO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.stereotype.Service;
import org.thymeleaf.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class YuntiOrderIdServiceImpl implements YuntiOrderIdService {

    @Resource
    private DBHelper yuntidemandDBHelper;

    @Override
    public Map<Long, String> cvmItem2OrderMap(List<Long> itemIds) {

        List<YuntiReturnPlanCvmItemRecordDO> all = yuntidemandDBHelper.getAll(YuntiReturnPlanCvmItemRecordDO.class, "where deleted = 0 and order_type = 'PLAN_ORDER' and item_id in (?)", itemIds);

        Map<Long, List<YuntiReturnPlanCvmItemRecordDO>> longListMap = ListUtils.groupBy(all, YuntiReturnPlanCvmItemRecordDO::getItemId);

        Map<Long,String> ret = new HashMap<>();

        for (Map.Entry<Long, List<YuntiReturnPlanCvmItemRecordDO>> entry : longListMap.entrySet()) {
            ret.put(entry.getKey(), StringUtils.join(entry.getValue().stream().map(YuntiReturnPlanCvmItemRecordDO::getOrderId).collect(Collectors.toSet()), ","));
        }

        return ret;
    }
}
