package cloud.demand.app.modules.sop.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import yunti.boot.exception.ITException;

@Getter
@AllArgsConstructor
public enum ExecutedType {
    EXECUTED(1,1,"已执行"),

    RUNNING(3,1,"已执行"),
    NOT_EXECUTED(2,0,"未执行");

    ;

    /** 上游给的code，对应dataType */
    private final int code;

    /** sop本地存的数据库标识 */
    private final int localCode;

    private final String name;

    public static boolean isExec(ExecutedType type){
        if (type == null){
            throw new ITException("ExecutedType执行状态码为空");
        }
        return !type.equals(ExecutedType.NOT_EXECUTED);
    }

    public static String getNameByLocalCode(int code){
        for (ExecutedType value : values()) {
            if (value.localCode == code){
                return value.name;
            }
        }
        return null;
    }
}
