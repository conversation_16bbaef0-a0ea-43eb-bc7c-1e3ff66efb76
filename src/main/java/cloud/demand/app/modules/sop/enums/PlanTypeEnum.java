package cloud.demand.app.modules.sop.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PlanTypeEnum {
    /**
     * 计划内
     */
    IN_PLAN("IN_PLAN", "计划内"),
    /**因需求调整而导致的需求值变化*/
    IN_ELASTICITY("IN_ELASTICITY", "弹性内"),
    /**手工进行调整*/
    OUT_ELASTICITY("OUT_ELASTICITY", "计划外"),
    ;

    private final String code;
    private final String name;



    public static PlanTypeEnum getByCode(String code) {
        for (PlanTypeEnum e : PlanTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (PlanTypeEnum e : PlanTypeEnum.values()) {
            if (Objects.equals(name, e.getName())) {
                return e.getCode();
            }
        }
        return "";
    }

    public static String getNameByCode(String code) {
        PlanTypeEnum byCode = getByCode(code);
        if (byCode == null){
            return "";
        }
        return byCode.getName();
    }
}
