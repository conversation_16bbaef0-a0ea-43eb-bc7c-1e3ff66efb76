package cloud.demand.app.modules.sop.http.domain.lab;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class PerformanceTrackReq {

    /**
     * 是否统计全量订单 包括满足方式评估、交付供应
     */
    private Boolean isAllOrder = Boolean.FALSE;

    /**
     * 履约机型模糊
     * instanceType / instanceGroup
     */
    @NotBlank
    private String statisticalCaliber;

    /**
     * 履约区域模糊
     * regionName / countryName / zoneName
     * */
    @NotBlank
    private String areaStatisticalCaliber;


    /**
     * 聚合维度 默认为yearMonth
     */
    @NotNull
    private List<String> dims = new ArrayList<>(Arrays.asList("yearMonth"));

    /**
     * 剔除未到截止时间的订单
     */
    private Boolean filterNotEndOrder = Boolean.FALSE;


    private List<String> product;

    /**
     * 订单策略，CVM
     * */
    private List<String> orderCategory;

    /** 内部，外部 */
    private List<String> bizType;

    private List<String> orderType;

    private List<String> industryDept;

    private List<String> warZone;


    private List<String> appId;

    private List<String> commonCustomerName;

    private List<String> regionName;

    private List<String> zoneName;

    private List<String> customhouseTitle;

    private List<String> billTypeName;

    private List<String> orderNodeCode;

    private List<String> instanceType = new ArrayList<>();

    private List<String> instanceGroup = new ArrayList<>();

    /**
     * 是否新机型
     */
    private Boolean isNewInstanceType;

    private String startYearMonth;

    private String endYearMonth;


}
