package cloud.demand.app.modules.sop.service.orderid.entity;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * yunti_return_plan_cvm_item_record   item
 */
@Data
@ToString
@Table("yunti_return_plan_cvm_item_record")
public class YuntiReturnPlanCvmItemRecordDO {

    /** id<br/>Column: [id] */
    @Column(value = "id")
    private Long id;

    /** 删除标记<br/>Column: [deleted] */
    @Column(value = "deleted")
    private Boolean deleted;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private Date createTime;

    /** 更新时间<br/>Column: [update_time] */
    @Column(value = "update_time")
    private Date updateTime;

    /** 对应需求id<br/>Column: [item_id] */
    @Column(value = "item_id")
    private Long itemId;

    /** 计划类型， 追加，调整<br/>Column: [source_type] */
    @Column(value = "source_type")
    private String sourceType;

    /** 来源单据类型（计划单和执行单）<br/>Column: [order_type] */
    @Column(value = "order_type")
    private String orderType;

    /** 来源单据id<br/>Column: [order_id] */
    @Column(value = "order_id")
    private String orderId;

    /** 实例数变化值<br/>Column: [change_cvm_amount] */
    @Column(value = "change_cvm_amount")
    private BigDecimal changeCvmAmount;

    /** cpu核数变化值<br/>Column: [change_core_amount] */
    @Column(value = "change_core_amount")
    private BigDecimal changeCoreAmount;

    /** 变化后实例数变化值<br/>Column: [after_cvm_amount] */
    @Column(value = "after_cvm_amount")
    private BigDecimal afterCvmAmount;

    /** 变化后cpu核数<br/>Column: [after_core_amount] */
    @Column(value = "after_core_amount")
    private BigDecimal afterCoreAmount;

    /** 变更描述信息<br/>Column: [desc] */
    @Column(value = "desc")
    private String desc;

    @Column(value = "operator")
    private String operator;

    /** 是否提前执行<br/>Column: [is_ahead_apply] */
    @Column(value = "is_ahead_apply")
    private Boolean isAheadApply;
}
