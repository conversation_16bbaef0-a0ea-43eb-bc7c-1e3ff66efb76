package cloud.demand.app.modules.sop.http.domain.lab;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class PerformanceTrackItem {

    private String groupKey;

    /**
     * 需求年月<br/>Column: [year_month]
     */
    private String yearMonth;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    private String industryDept;

    /**
     * 战区<br/>Column: [war_zone]
     */
    private String warZone;

    /**
     * 通用客户简称<br/>Column: [common_customer_name]
     */
    private String commonCustomerName;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    private String customerShortName;

    /**
     * 产品<br/>Column: [product]
     */
    private String product;

    /**
     * 应用角色<br/>Column: [app_role]
     */
    private String appRole;

    /**
     * 机型族<br/>Column: [instance_type]
     */
    private String instanceType;

    /**
     * 机型族<br/>Column: [instance_group]
     */
    private String instanceGroup;

    /**
     * 是否新机型
     */
    private Boolean isNewInstanceType;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    private String customhouseTitle;

    /**
     * 地域<br/>Column: [region]
     */
    private String region;

    /**
     * 地域名称<br/>Column: [region_name]
     */
    private String regionName;

    /**
     * 可用区<br/>Column: [zone]
     */
    private String zone;

    /**
     * 可用区名称<br/>Column: [zone_name]
     */
    private String zoneName;


    /**
     * 所有订单（包含满足方式评估、交付供应）-需求总核心数<br/>Column: [all_order_demand_total_Core]
     */
    private Long allOrderDemandTotalCore;

    /**
     * 需求总核心数<br/>Column: [demand_total_core]
     */
    private Long demandTotalCore;

    /**
     * 满足核心数<br/>Column: [satisfy_core]
     */
    private Long satisfyCore;

    /**
     * 实际购买核心<br/>Column: [buy_total_core]
     */
    private BigDecimal buyTotalCore = new BigDecimal(0);


    /**
     * 履约率
     */
    private BigDecimal buyRate;

    /**
     * 满足量占比
     */
    private BigDecimal satisfyCoreRate;

    /**
     * 满足量基层数据
     */
    private BigDecimal satisfyCoreBaseRate;

    /**
     * 未履约核心数
     */
    private BigDecimal notBuyTotalCore;


    private List<PerformanceTrackItem> childItem;

}
