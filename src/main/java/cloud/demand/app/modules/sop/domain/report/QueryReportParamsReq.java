package cloud.demand.app.modules.sop.domain.report;


import cloud.demand.app.modules.sop_yunti.util.SopYuntiUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QueryReportParamsReq extends QueryReportCommonReq{
    /** cvm的起始时间 */
    @NotEmpty(message = "版本集合不能为空")
    private List<String> versionList;

    @Override
    public boolean hasYuntiVersion() {
        // 如果没有versionList，则继承原有逻辑
        if (ListUtils.isEmpty(versionList)){
            return super.hasYuntiVersion();
        }
        for (String version : versionList) {
            if (SopYuntiUtils.isYuntiVersion(version)){
                return true;
            }
        }
        return false;
    }
}
