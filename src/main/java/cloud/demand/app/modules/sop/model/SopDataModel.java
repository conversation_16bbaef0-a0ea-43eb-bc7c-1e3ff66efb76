package cloud.demand.app.modules.sop.model;

import cloud.demand.app.modules.sop.domain.report.QueryReportItemResp;
import cloud.demand.app.modules.sop.domain.report.QueryReportItemResp.IndexItem;
import cloud.demand.app.modules.sop.enums.FlagType;
import com.alibaba.excel.annotation.ExcelProperty;
import java.math.BigDecimal;
import lombok.Data;

/** sop 导出数据 */
@Data
public class SopDataModel {
    /**
     * 资源类型<br/>Column: [res_type]
     */
    @ExcelProperty(value = "资源类型", index = 0)
    private String resType;

    /**
     * 资源池类型<br/>Column: [res_pool_type]
     */
    @ExcelProperty(value = "资源池类型", index = 1)
    private String resPoolType;

    /**
     * 项目类型<br/>Column: [obs_project_type]
     */
    @ExcelProperty(value = "项目类型", index = 2)
    private String obsProjectType;

    /**
     * BG名<br/>Column: [bg_name]
     */
    @ExcelProperty(value = "源事业群", index = 3)
    private String bgName;

    /**
     * 自定义BG名<br/>Column: [custom_bg_name]
     */
    @ExcelProperty(value = "自定义事业群", index = 4)
    private String customBgName;

    /**
     * 部门名<br/>Column: [dept_name]
     */
    @ExcelProperty(value = "部门名", index = 5)
    private String deptName;

    /**
     * 原始部门名<br/>Column: [custom_dept_name]
     */
    @ExcelProperty(value = "老部门名", index = 6)
    private String oldDeptName;

    /**
     * 规划产品名<br/>Column: [plan_product_name]
     */
    @ExcelProperty(value = "规划产品名", index = 7)
    private String planProductName;

    /**
     * 国内外<br/>Column: [customhouse_title]
     */
    @ExcelProperty(value = "国内外", index = 8)
    private String customhouseTitle;

    /**
     * 国家<br/>Column: [country_name]
     */
    @ExcelProperty(value = "国家", index = 9)
    private String countryName;

    /**
     * 城市：可同时表示物理机城市，也可以表示腾讯云 region<br/>Column: [city_name]
     */
    @ExcelProperty(value = "城市/地域", index = 10)
    private String cityName;

    /**
     * cmdb campus<br/>Column: [cmdb_campus_name]
     */
    @ExcelProperty(value = "campus", index = 11)
    private String cmdbCampusName;

    /**
     * cmdb module<br/>Column: [cmdb_module_name]
     */
    @ExcelProperty(value = "module", index = 12)
    private String cmdbModuleName;

    /**
     * 腾讯云 zone<br/>Column: [txy_zone_name]
     */
    @ExcelProperty(value = "可用区", index = 13)
    private String txyZoneName;

    /**
     * cvm 实例类型<br/>Column: [cvm_gins_family]
     */
    @ExcelProperty(value = "虚拟机实例类型", index = 14)
    private String cvmGinsFamily;

    /**
     * cvm 实例规格<br/>Column: [cvm_gins_type]
     */
    @ExcelProperty(value = "虚拟机实例规格", index = 15)
    private String cvmGinsType;

    /**
     * cvm 机型族<br/>Column: [cvm_gins_kingdom]
     */
    @ExcelProperty(value = "虚拟机机型族", index = 16)
    private String cvmGinsKingdom;

    /**
     * 物理机机型族<br/>Column: [phy_device_family]
     */
    @ExcelProperty(value = "物理机机型族", index = 17)
    private String phyDeviceFamily;

    /**
     * 物理机设备类型<br/>Column: [phy_device_type]
     */
    @ExcelProperty(value = "物理机设备类型", index = 18)
    private String phyDeviceType;

    /**
     * 是否参与对冲，0 否 1 是<br/>Column: [is_hedge]
     */
    @ExcelProperty(value = "是否参与对冲", index = 19)
    private String isHedge;

    /**
     * 是否有效对冲，0 否 1 是<br/>Column: [has_hedged]
     */
    @ExcelProperty(value = "是否有效对冲", index = 20)
    private String hasHedged;

    /**
     * 业务类型
     */
    @ExcelProperty(value = "业务类型", index = 21)
    private String businessType;

    /**
     * obs业务
     */
    @ExcelProperty(value = "obs业务分类", index = 22)
    private String obsBusinessType;

    /**
     * 自研上云标识(自研上云or非自研上云)
     */
    @ExcelProperty(value = "是否自研上云", index = 23)
    private String selfToCloudType;

    /**
     * 指标名称
     * */
    @ExcelProperty(value = "指标名称", index = 24)
    private String name;

    /**
     * 台数
     * */
    @ExcelProperty(value = {"开始版本","台数"}, index = 25)
    private BigDecimal startNum;

    /**
     * 核数
     * */
    @ExcelProperty(value = {"开始版本","核数"}, index = 26)
    private BigDecimal startCoreNum;

    /**
     * 容量
     * */
    @ExcelProperty(value = {"开始版本","容量"}, index = 27)
    private BigDecimal startCapacity;

    /**
     * 台数
     * */
    @ExcelProperty(value = {"结束版本","台数"}, index = 28)
    private BigDecimal endNum;

    /**
     * 核数
     * */
    @ExcelProperty(value = {"结束版本","核数"}, index = 29)
    private BigDecimal endCoreNum;

    /**
     * 容量
     * */
    @ExcelProperty(value = {"结束版本","容量"}, index = 30)
    private BigDecimal endCapacity;

    /** 容量单位 */
    @ExcelProperty(value = "容量单位", index = 31)
    private String capacityUnit;

    public static SopDataModel transform(QueryReportItemResp.Data datum, IndexItem indexItem) {
        SopDataModel ret = new SopDataModel();
        ret.setResType(datum.getResType());
        ret.setResPoolType(datum.getResPoolType());
        ret.setObsProjectType(datum.getObsProjectType());
        ret.setBgName(datum.getBgName());
        ret.setCustomBgName(datum.getCustomBgName());
        ret.setDeptName(datum.getDeptName());
        ret.setOldDeptName(datum.getOldDeptName());
        ret.setPlanProductName(datum.getPlanProductName());
        ret.setCustomhouseTitle(datum.getCustomhouseTitle());
        ret.setCountryName(datum.getCountryName());
        ret.setCityName(datum.getCityName());
        ret.setCmdbCampusName(datum.getCmdbCampusName());
        ret.setCmdbModuleName(datum.getCmdbModuleName());
        ret.setTxyZoneName(datum.getTxyZoneName());
        ret.setCvmGinsFamily(datum.getCvmGinsFamily());
        ret.setCvmGinsType(datum.getCvmGinsType());
        ret.setCvmGinsKingdom(datum.getCvmGinsKingdom());
        ret.setPhyDeviceFamily(datum.getPhyDeviceFamily());
        ret.setPhyDeviceType(datum.getPhyDeviceType());
        ret.setIsHedge(FlagType.getDesc(datum.getIsHedge()));
        ret.setHasHedged(FlagType.getDesc(datum.getHasHedged()));
        ret.setBusinessType(datum.getBusinessType());
        ret.setObsBusinessType(datum.getObsBusinessType());
        ret.setSelfToCloudType(datum.getSelfToCloudType());
        ret.setName(indexItem.getName());
        ret.setStartNum(indexItem.getStartNum());
        ret.setStartCoreNum(indexItem.getStartCoreNum());
        ret.setStartCapacity(indexItem.getStartCapacity());
        ret.setEndNum(indexItem.getEndNum());
        ret.setEndCoreNum(indexItem.getEndCoreNum());
        ret.setEndCapacity(indexItem.getEndCapacity());
        ret.setCapacityUnit(datum.getCapacityUnit());
        return ret;
    }
}
