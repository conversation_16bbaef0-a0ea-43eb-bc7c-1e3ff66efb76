package cloud.demand.app.modules.sop.util;

import org.slf4j.MDC;

public class MDCUtil {

    private static final String traceIdKey = "requestId";

    public static String appendTraceId(String splitStr, Object otherObj) {
        return appendTraceId(splitStr, String.valueOf(otherObj));
    }

    public static String appendTraceId(String splitStr, String otherStr) {
        String prevTraceId = MDC.get(traceIdKey);
        String newTraceId = prevTraceId;
        if (prevTraceId == null) {
            newTraceId = "";
        }
        newTraceId = newTraceId + splitStr + otherStr;
        MDC.put(traceIdKey, newTraceId);
        return prevTraceId;
    }

    public static void setTraceId(String traceId) {
        if (traceId != null) {
            MDC.put(traceIdKey, traceId);
        } else {
            MDC.remove(traceIdKey);
        }
    }

    public static String getTraceId(){
        return MDC.get(traceIdKey);
    }
}
