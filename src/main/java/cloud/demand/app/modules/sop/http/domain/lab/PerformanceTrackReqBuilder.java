package cloud.demand.app.modules.sop.http.domain.lab;

import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

public class PerformanceTrackReqBuilder {
    private List<String> industryDept;
    private String startYearMonth;
    private String endYearMonth;
    private List<String> customhouseTitle;
    private boolean isDefault;

    public PerformanceTrackReqBuilder industryDept(List<String> industryDept){
        this.industryDept = industryDept;
        return this;
    }

    public PerformanceTrackReqBuilder startYearMonth(String startYearMonth){
        this.startYearMonth = startYearMonth;
        return this;
    }

    public PerformanceTrackReqBuilder endYearMonth(String endYearMonth){
        this.endYearMonth = endYearMonth;
        return this;
    }

    public PerformanceTrackReqBuilder customhouseTitle(List<String> customhouseTitle){
        this.customhouseTitle = customhouseTitle;
        return this;
    }

    public PerformanceTrackReqBuilder isDefault(boolean isDefault){
        this.isDefault = isDefault;
        return this;
    }

    public PerformanceTrackReq build(){
        PerformanceTrackReq req = new PerformanceTrackReq();
        req.setIndustryDept(industryDept);
        req.setStartYearMonth(startYearMonth);
        req.setEndYearMonth(endYearMonth);
        req.setCustomhouseTitle(customhouseTitle);
        if (isDefault){
            buildDefault(req);
        }
        return req;
    }

    private void buildDefault(PerformanceTrackReq req){
        req.setOrderCategory(ListUtils.newList(Ppl13weekProductTypeEnum.CVM.getCode()));
        req.setBizType(ListUtils.newList("外部"));
        req.setOrderType(ListUtils.newList("新增"));
        req.setStatisticalCaliber("instanceGroup");
        req.setAreaStatisticalCaliber("regionName");
        // 默认剔除未到结束时间的订单
        req.setFilterNotEndOrder(true);
    }

}
