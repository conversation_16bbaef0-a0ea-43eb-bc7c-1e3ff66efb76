package cloud.demand.app.modules.sop.enums;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/** 节假维度替换 */
@AllArgsConstructor
@Getter
public enum HolidayFieldEnum {
    year("indexYear","holidayYear"),
    month("indexMonth","holidayMonth"),
    week("indexWeek","holidayWeek"),
    yearMonth("indexYearMonth","holidayYearMonth"),
    ;

    private final String indexName;

    private final String holidayName;

    /** 指标转节假 */
    public static Map<String,String> getIndexToHoliday(){
        Map<String,String> ret = new HashMap<>();
        for (HolidayFieldEnum value : values()) {
            ret.put(value.getIndexName(),value.getHolidayName());
        }
        return ret;
    }

    /** 替换指标为节假 */
    public static List<String> replaceHoliday(List<String> indexNames){
        if (ListUtils.isEmpty(indexNames)){
            return indexNames;
        }
        Map<String, String> indexToHoliday = getIndexToHoliday();
        return indexNames.stream().map(item-> indexToHoliday.getOrDefault(item,item)).collect(Collectors.toList());
    }

    public static String replaceHoliday(String indexName){
        if (StringUtils.isEmpty(indexName)){
            return indexName;
        }
        Map<String, String> indexToHoliday = getIndexToHoliday();
        return indexToHoliday.getOrDefault(indexName,indexName);
    }
}
