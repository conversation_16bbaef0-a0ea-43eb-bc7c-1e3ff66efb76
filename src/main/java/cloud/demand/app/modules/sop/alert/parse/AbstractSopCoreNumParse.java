package cloud.demand.app.modules.sop.alert.parse;

import cloud.demand.app.modules.mrpv2.alert.entity.AbstractAlter;
import cloud.demand.app.modules.mrpv2.alert.parse.AbstractAlterParse;
import cloud.demand.app.modules.sop.alert.entity.SopCoreNumAlterDo;
import cloud.demand.app.modules.sop.alert.entity.SopCoreNumDiffAlterDo;
import cloud.demand.app.modules.sop.alert.entity.SopQueryParam;
import cloud.demand.app.modules.sop.alert.raw.AbstractSourceRaw;
import cloud.demand.app.modules.sop.enums.ExecutedType;
import cloud.demand.app.modules.sop.enums.SopBusinessType;
import com.pugwoo.dbhelper.DBHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.MultiKeyMap;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public abstract class AbstractSopCoreNumParse extends AbstractAlterParse<List<SopCoreNumDiffAlterDo>> {
    private final DBHelper dbHelper;

    private final List<AbstractSourceRaw<?>> sourceRaw;

    protected SopQueryParam queryParam;

    public AbstractSopCoreNumParse(DBHelper dbHelper, List<AbstractSourceRaw<?>> sourceRaw,
                                   SopQueryParam queryParam) {
        this.dbHelper = dbHelper;
        this.sourceRaw = sourceRaw;
        this.queryParam = queryParam;
    }


    @Override
    protected DBHelper getDbHelper() {
        return dbHelper;
    }

    @Override
    public List<? extends AbstractAlter> preParse(List<? extends AbstractAlter> raw) {
        List<SopCoreNumDiffAlterDo> ret = new ArrayList<>();
        MultiKeyMap<String,SopCoreNumDiffAlterDo> map = new MultiKeyMap<>();

        List<SopCoreNumAlterDo> crpRaw = (List<SopCoreNumAlterDo>)raw;
        for (SopCoreNumAlterDo alter : crpRaw) {
            SopCoreNumDiffAlterDo item = transForm(alter,true);
            ret.add(item);
            map.put(item.getBusinessType(),item.getIsExecuted(),item);
            // 自研业务转内部业务
            if (item.getBusinessType().equals(SopBusinessType.SELF.getDesc())){
                map.put("内部业务",item.getIsExecuted(),item);
            }
        }
        // 需要2（云+自研）*2（已执行+未执行）= 4条数据
        if (crpRaw.size() < 4){
            String[] businessTypeArr = new String[]{SopBusinessType.CLOUD.getDesc(),SopBusinessType.SELF.getDesc()};
            Integer[] isExecutedArr = new Integer[]{ExecutedType.EXECUTED.getLocalCode(),ExecutedType.NOT_EXECUTED.getLocalCode()};
            for (String businessType : businessTypeArr) {
                for (Integer isExecuted : isExecutedArr) {
                    Optional<SopCoreNumAlterDo> first = crpRaw.stream().filter(item ->
                            item.getBusinessType().equals(businessType)
                                    && item.getIsExecuted().equals(isExecuted)).findFirst();
                    if (!first.isPresent()){
                        SopCoreNumDiffAlterDo item = transForm(new SopCoreNumAlterDo(businessType, BigDecimal.ZERO, isExecuted), true);
                        ret.add(item);
                        map.put(item.getBusinessType(),item.getIsExecuted(),item);
                    }
                }
            }
        }
        for (AbstractSourceRaw<?> abstractSourceRaw : this.sourceRaw) {
            List<SopCoreNumAlterDo> sourceRaw = abstractSourceRaw.getAlterRaw();
            if (!CollectionUtils.isEmpty(sourceRaw)){
                for (SopCoreNumAlterDo alter : sourceRaw) {
                    SopCoreNumDiffAlterDo crpItem = map.get(alter.getBusinessType(), ExecutedType.getNameByLocalCode(alter.getIsExecuted()));
                    if (crpItem == null){
                        SopCoreNumDiffAlterDo item = transForm(alter,false);
                        ret.add(item);
                    }else {
                        crpItem.setSourceCoreNum(ObjectUtils.defaultIfNull(crpItem.getSourceCoreNum(),BigDecimal.ZERO)
                                .add(ObjectUtils.defaultIfNull(alter.getCoreNum(),BigDecimal.ZERO)));
                        crpItem.setDiffCoreNum(crpItem.getCrpCoreNum()
                                .subtract(crpItem.getSourceCoreNum()));
                        if (crpItem.getCrpCoreNum().equals(BigDecimal.ZERO)){
                            crpItem.setDiffCoreNumPercent(BigDecimal.valueOf(100));
                        }else {
                            crpItem.setDiffCoreNumPercent(crpItem.getDiffCoreNum().divide(crpItem.getCrpCoreNum(),4, RoundingMode.CEILING));
                        }
                    }
                }
            }
        }
        return ret;
    }

    public SopCoreNumDiffAlterDo transForm(SopCoreNumAlterDo alterDo,boolean isCrp){
        SopCoreNumDiffAlterDo ret = new SopCoreNumDiffAlterDo();
        ret.setVersion(queryParam.getVersion());
        ret.setVersionDate(queryParam.getVersionDate());
        ret.setBusinessType(alterDo.getBusinessType());
        if (ret.getBusinessType().equals("内部业务")){
            ret.setBusinessType(SopBusinessType.SELF.getDesc());
        }
        if (isCrp){
            ret.setCrpCoreNum(alterDo.getCoreNum());
            ret.setSourceCoreNum(BigDecimal.ZERO);
            ret.setDiffCoreNum(alterDo.getCoreNum());
            ret.setDiffCoreNumPercent(BigDecimal.valueOf(100));
        }else {
            ret.setCrpCoreNum(BigDecimal.ZERO);
            ret.setSourceCoreNum(alterDo.getCoreNum());
            ret.setDiffCoreNum(ret.getSourceCoreNum().negate());
            ret.setDiffCoreNumPercent(BigDecimal.valueOf(100));
        }
        ret.setIsExecuted(ExecutedType.getNameByLocalCode(alterDo.getIsExecuted()));

        return ret;
    }

    @Override
    public List<? extends AbstractAlter> getRaw() {
        return getDbHelper().getRaw(getAlterClass(), getSql(),queryParam.getVersion(),queryParam.getStartDate());
    }

    @Override
    protected Class<? extends AbstractAlter> getAlterClass() {
        return SopCoreNumAlterDo.class;
    }

}
