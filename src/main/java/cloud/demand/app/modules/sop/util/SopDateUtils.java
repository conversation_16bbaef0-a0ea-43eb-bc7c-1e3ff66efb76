package cloud.demand.app.modules.sop.util;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.common.service.entity.ResPlanHolidayWeekDO;
import cloud.demand.app.modules.common.service.impl.DictServiceImpl;
import cloud.demand.app.modules.sop.entity.clean.IndexDate;
import cloud.demand.app.modules.sop.entity.clean.OriIndexDate;
import cloud.demand.app.modules.sop.entity.clean.FirstYearMonth;
import com.pugwoo.wooutils.lang.DateUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.TemporalUnit;
import java.time.temporal.WeekFields;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;


/** 日期格式工具类
 * 1. dateVersion -- 转为yyyy-MM-dd格式
 * 2. dateRange -- 转为yyyyMMdd格式
 * 3. getStartTime -- 获取一年的起始日期
 * 4. getEndTime -- 获取一年的结束日期
 * 5. getWeekOfYear -- 获取一年内的第几个星期
 * 6. getYearMonth -- 获取日期的年月(yyyy-MM格式)
 * 7. getYear -- 获取年
 * 8. getMonth -- 获取月
 * 9. fixNumber -- 不满10左填充0
 */
public class SopDateUtils {

    public static LocalDateTime verison2LocalDateTime(String version){
        Date date = new Date(Long.parseLong(version) * 1000);
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return instant.atZone(zoneId).toLocalDateTime();
    }

    // 两个数据源，1没有拿到从2拿
    public static void dateTransform(OriIndexDate ori,OriIndexDate ori2, IndexDate date) {
        dateTransform(ori,date);
        if (date.getIndexDate() == null){
            dateTransform(ori2,date);
        }
    }

    public static void dateTransform(OriIndexDate ori, IndexDate date){
        if (ori != null && date != null){
            LocalDate indexDate = ori.getDate();
            if (indexDate != null){
                date.setIndexDate(indexDate);
                // YearMonth接口会优先年月数据，场景：规划时间 > 需求时间，比如 2023年需求规划到2024年，这时的年月用的是2024-mm，而非2023-mm
                if (date instanceof FirstYearMonth && ori.getYear()!=null && ori.getMonth() != null){
                    // 优先年月的数据，没有从index_date计算
                    date.setIndexYear(ori.getYear());
                    date.setIndexMonth(ori.getMonth());
                    date.setIndexYearMonth(getYearMonth(ori.getYear(),ori.getMonth()));
                    // week为null或者0则通过indexDate取
                    date.setIndexWeek(DataTransformUtil.defaultIfNullOrZero(ori.getWeek(),getWeekOfYear(indexDate)));
                }else {
                    date.setIndexYearMonth(getYearMonth(indexDate));
                    date.setIndexYear(getYear(indexDate));
                    date.setIndexMonth(getMonth(indexDate));
                    date.setIndexWeek(getWeekOfYear(indexDate));
                }
            }else if (ori.getMonth() != null && ori.getMonth() != 0 && ori.getYear() != null && ori.getYear() != 0){
                date.setIndexYear(ori.getYear());
                date.setIndexMonth(ori.getMonth());
                date.setIndexDate(LocalDate.of(ori.getYear(),ori.getMonth(),1));
                date.setIndexYearMonth(getYearMonth(date.getIndexDate()));
                // week为null或者0则通过indexDate取
                date.setIndexWeek(DataTransformUtil.defaultIfNullOrZero(ori.getWeek(),getWeekOfYear(date.getIndexDate())));
            }
        }
    }

    /** 切片时间格式 */
    public static String dateVersion(LocalDate date){
        if (date == null){
            return "";
        }
        return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /** 开始结束时间格式 */
    public static String dateRange(LocalDate date){
        if (date == null){
            return "";
        }
        return date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /** 获取切片的年第一天 */
    public static String getStartTime(LocalDate date){
        if (date == null){
            return "";
        }
        return dateRange(date.with(TemporalAdjusters.firstDayOfYear()));
    }

    /** 获取切片的年最后一天 */
    public static String getEndTime(LocalDate date){
        if (date == null){
            return "";
        }
        return dateRange(date.with(TemporalAdjusters.lastDayOfYear()));
    }

    /** 获取时间的年内第几个星期 */
    public static int getWeekOfYear(LocalDate localDate){
        if (localDate == null){
            return -1;
        }
        Integer weekOfYearWithTable = getWeekOfYearWithTable(localDate);
        if (weekOfYearWithTable != null){
            return weekOfYearWithTable;
        }
        // 2023-01-01 是去年的52周，算法周不可分割
        return localDate.get(WeekFields.ISO.weekOfWeekBasedYear());
        // 2023-01-01 是今年1周，算法年不可分割
//        return localDate.get(WeekFields.SUNDAY_START.weekOfYear());
    }

    public static String plus(String date, int amountToAdd, TemporalUnit unit){
        LocalDate _date = LocalDate.parse(date, DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate _newDate = _date.plus(amountToAdd, unit);
        return _newDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

    public static String beforeDate(String date){
        return plus(date,-1,ChronoUnit.DAYS);
    }

    public static String beforeDate(LocalDate date){
        if (date == null){
            return null;
        }
        return date.plus(-1, ChronoUnit.DAYS).format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

    public static LocalDate toLocalDate(Date date){
        if (date==null){
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static LocalDate toLocalDate(String date){
        if (StringUtils.isBlank(date)){
            return null;
        }
        return LocalDate.parse(date,DateTimeFormatter.ISO_LOCAL_DATE);
    }

    /** 不带横岗转带横岗（yyyyMMdd -> yyyy-MM-dd） */
    public static String dateWithHeng(String date){
        if (StringUtils.isBlank(date) || date.length() != 8){
            return date;
        }
        StringBuilder sbr = new StringBuilder(date);
        sbr.insert(6,"-");
        sbr.insert(4,"-");
        return sbr.toString();
    }

   private static final ThreadLocal<List<ResPlanHolidayWeekDO>> weekCache = new ThreadLocal<>();

    public static Integer getWeekOfYearWithTable(LocalDate localDate){
        if (localDate == null){
            localDate = LocalDate.now();
        }
        String format = localDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
        List<ResPlanHolidayWeekDO> weekList = weekCache.get();
        if (weekList == null){
            weekList = SpringUtil.getBean(DictServiceImpl.class).getAllHolidayWeekInfos();
            weekCache.set(weekList);
        }
        Optional<ResPlanHolidayWeekDO> first = weekList.stream()
                .filter(item -> item.getStart().compareTo(format) <= 0 && item.getEnd().compareTo(format) >= 0)
                .findFirst();
        return first.map(ResPlanHolidayWeekDO::getWeek).orElse(null);
    }

    public static String getYearMonthWithTable(LocalDate localDate){
        if (localDate == null){
            localDate = LocalDate.now();
        }
        String format = localDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
        List<ResPlanHolidayWeekDO> weekList = weekCache.get();
        if (weekList == null){
            weekList = SpringUtil.getBean(DictServiceImpl.class).getAllHolidayWeekInfos();
            weekCache.set(weekList);
        }
        Optional<ResPlanHolidayWeekDO> first = weekList.stream()
                .filter(item -> item.getStart().compareTo(format) <= 0 && item.getEnd().compareTo(format) >= 0)
                .findFirst();
        return first.map(item-> getYearMonth(item.getYear(),item.getMonth())).orElse(null);
    }

    // =============== 获取年月日 ================
    public static LocalDate getYearMonthDay(Date date){
        if (date == null){
            return null;
        }
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        return LocalDate.of(instance.get(Calendar.YEAR),instance.get(Calendar.MONTH) + 1,instance.get(Calendar.DAY_OF_MONTH));
    }

    // =============== 获取年月 ================
    public static String getYearMonth(Date date){
        if (date==null){
            return null;
        }
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        return getYearMonth(instance.get(Calendar.YEAR),instance.get(Calendar.MONTH) + 1);
    }

    public static String getYearMonth(LocalDate date){
        if (date==null){
            return null;
        }
        return getYearMonth(date.getYear(),date.getMonthValue());
    }

    public static String getYearMonth(YearMonth date){
        if (date==null){
            return null;
        }
        return getYearMonth(date.getYear(),date.getMonthValue());
    }

    public static String getYearMonth(int year,int month){
        return year + "-" + fixNumber(month);
    }

    public static int getYear(LocalDate localDate){
        return localDate == null ? -1: localDate.getYear();
    }

    public static int getMonth(LocalDate localDate){
        return localDate == null ? -1: localDate.getMonthValue();
    }


    /** 修改日期为月第一天,失败原路返回 */
    public static String getMonthFirstDay(String versionDate) {
        return versionDate.length() == 10? (versionDate.substring(0,8) + "01"):versionDate;
    }

    public static String getHolidayMonthFirstDay(String versionDate){
        LocalDate localDate = DateUtils.parseLocalDate(versionDate);
        int year = localDate.getYear();
        LocalDate with = localDate.with(WeekFields.ISO.getFirstDayOfWeek());
        int year1 = with.getYear();
        if (year != year1){
            LocalDate localDate1 = with.plusDays(3);
            int year2 = localDate1.getYear();
            if (year2 == year){
                return getYearMonth(year,1) + "-01";
            }else {
                return getYearMonth(year1,12) + "-01";
            }
        }else {
            int monthValue = with.getMonthValue();
            while(true){
                LocalDate localDate1 = with.plusDays(-7);
                if (localDate1.getMonthValue()!= monthValue){
                    break;
                }
                with = localDate1;
            }
            return DateUtils.formatDate(with);
        }
    }

    public static String fixNumber(int num){
        if (num < 10 && num >= 0){
            return "0" + num;
        }
        return "" + num;
    }

}
