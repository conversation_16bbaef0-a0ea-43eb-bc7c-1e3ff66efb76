package cloud.demand.app.modules.sop.service.custom_grab_ads;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.modules.sop.domain.report.QueryReportCommonReq;
import cloud.demand.app.modules.sop.entity.ads.AdsSopReportMifDO;
import cloud.demand.app.modules.sop.entity.ads.AdsSopReportMifSumVO;
import cloud.demand.app.modules.sop.entity.ads.AdsSopReportOriginMifDO;
import com.pugwoo.dbhelper.DBHelper;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
public class CustomStartInventoryServiceImpl implements CustomGrabAdsService {

    @Resource
    DBHelper ckcrpreadDBHelper;

    private WhereContent deviceWhereSql(QueryReportCommonReq req) {
        WhereContent whereContent = new WhereContent();
        // 固定部分
        whereContent.addAnd("v.is_enable = 1 and (v.class1 in ('常规-空闲', '已预约-生产过程料', '常规-待清理')"
                + " or (v.class1 = '预留-不可用' and v.class2 = '预留-业务调度'))");
        // 日期YYYYMMDD格式
        whereContent.addAnd("v.data_date = ?", (req.getStartYearMonth() + "-01").replace("-", ""));
        // 其他筛选
        addWhereFromReq(req, whereContent);
        return whereContent;
    }

    private WhereContent cvmWhereSql(QueryReportCommonReq req) {
        WhereContent whereContent = new WhereContent();
        // 固定部分
        whereContent.addAnd("(v.class1 = '常规-空闲CVM' or (v.class1 = '占用-预留CVM' and v.class2 = '业务预留'))");
        // 日期YYYY-MM-DD格式
        whereContent.addAnd("v.data_date = ?", req.getStartYearMonth() + "-01");
        // 其他筛选
        addWhereFromReq(req, whereContent);
        return whereContent;
    }

    private String replaceWhereContentToSql(String sql, WhereContent deviceWhere, WhereContent cvmWhere) {
        sql = sql.replace("${DEVICE_SQL}", deviceWhere.getSql());
        sql = sql.replace("${CVM_SQL}", cvmWhere.getSql());
        return sql;
    }

    private Object[] combineParams(WhereContent deviceWhere, WhereContent cvmWhere) {
        Object[] dp = deviceWhere.getParams();
        Object[] cp = cvmWhere.getParams();
        Object[] rp = new Object[dp.length + cp.length];
        System.arraycopy(dp, 0, rp, 0, dp.length);
        System.arraycopy(cp, 0, rp, dp.length, cp.length);
        return rp;
    }

    private void addWhereFromReq(QueryReportCommonReq req, WhereContent whereContent) {
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getResType, req.getResType());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getObsProjectType, req.getObsProjectType());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getResPoolType, req.getResPoolType());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getBgName, req.getBgName());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getCustomBgName, req.getCustomBgName());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getDeptName, req.getDeptName());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getOldDeptName, req.getOldDeptName());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getPlanProductName, req.getPlanProductName());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getCustomhouseTitle, req.getCustomhouseTitle());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getCountryName, req.getCountryName());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getCityName, req.getCityName());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getCmdbCampusName, req.getCmdbCampusName());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getCmdbModuleName, req.getCmdbModuleName());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getTxyZoneName, req.getTxyZoneName());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getPhyDeviceFamily, req.getPhyDeviceFamily());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getPhyDeviceType, req.getPhyDeviceType());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getCvmGinsFamily, req.getCvmGinsFamily());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getCvmGinsType, req.getCvmGinsType());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getIsHedge, req.getIsHedge());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getHasHedged, req.getHasHedged());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getIsCa, req.getIsCa());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getCapacityUnit, req.getCapacityUnit());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getBusinessType, req.getBusinessType());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getObsBusinessType, req.getObsBusinessType());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getSelfToCloudType, req.getSelfToCloudType());
        whereContent.andInIfValueNotEmpty(AdsSopReportMifDO::getCvmGinsKingdom, req.getCvmGinsKingdom());
    }

    private boolean contain(List reqList, Object v) {
        if (!CollectionUtils.isEmpty(reqList)) {
            return reqList.contains(v);
        } else {
            // 无筛选就是都包含
            return true;
        }
    }

    private List<AdsSopReportMifSumVO> memoryWhere(QueryReportCommonReq req, List<AdsSopReportMifSumVO> voList) {
        return voList.stream()
                .filter(v -> contain(req.getResType(), v.getResType())
                        && contain(req.getObsProjectType(), v.getObsProjectType())
                        && contain(req.getResPoolType(), v.getResPoolType())
                        && contain(req.getBgName(), v.getBgName())
                        && contain(req.getCustomBgName(), v.getCustomBgName())
                        && contain(req.getDeptName(), v.getDeptName())
                        && contain(req.getPlanProductName(), v.getPlanProductName())
                        && contain(req.getCustomhouseTitle(), v.getCustomhouseTitle())
                        && contain(req.getCountryName(), v.getCountryName())
                        && contain(req.getCityName(), v.getCityName())
                        && contain(req.getCmdbCampusName(), v.getCmdbCampusName())
                        && contain(req.getCmdbModuleName(), v.getCmdbModuleName())
                        && contain(req.getTxyZoneName(), v.getTxyZoneName())
                        && contain(req.getPhyDeviceFamily(), v.getPhyDeviceFamily())
                        && contain(req.getPhyDeviceType(), v.getPhyDeviceType())
                        && contain(req.getCvmGinsFamily(), v.getCvmGinsFamily())
                        && contain(req.getCvmGinsType(), v.getCvmGinsType())
                        && contain(req.getIsHedge(), v.getIsHedge())
                        && contain(req.getHasHedged(), v.getHasHedged())
                        && contain(req.getIsCa(), v.getIsCa())
                        && contain(req.getCapacityUnit(), v.getCapacityUnit()))
                .collect(Collectors.toList());
    }

    @Override
    public Supplier<List<AdsSopReportMifSumVO>> grabFunction(QueryReportCommonReq req) {
        return () -> {
            String baseSql = ORMUtils.getSql("/sql/sop/ads_start_inventory_base.sql");
            String sql = ORMUtils.getSql("/sql/sop/ads_start_inventory.sql");
            sql = sql.replace("${BASE_SQL}", baseSql);
            String groupBySql = "";
            if (!CollectionUtils.isEmpty(req.getDim())) {
                List<String> reqColumnValues = req.getDim().stream()
                        .map(col -> ORMUtils.getColumnByFieldName(AdsSopReportOriginMifDO.class, col))
                        .collect(Collectors.toList());
                groupBySql = String.join(", ", reqColumnValues);
            }
            if (StringUtils.isNotBlank(groupBySql)) {
                sql = sql + " group by " + groupBySql;
            }
            // 添加 where 并替换 sql
            WhereContent deviceWhere = deviceWhereSql(req);
            WhereContent cvmWhere = cvmWhereSql(req);
            sql = replaceWhereContentToSql(sql, deviceWhere, cvmWhere);
            Object[] params = combineParams(deviceWhere, cvmWhere);

            List<AdsSopReportMifSumVO> voList = ckcrpreadDBHelper.getRaw(AdsSopReportMifSumVO.class,
                    sql, params);

            // cvm和物理机过滤
            if (ListUtils.isNotEmpty(req.getResType()) && ListUtils.isNotEmpty(voList)){
                Set<String> set = new HashSet<>(req.getResType());
                voList = voList.stream().filter(item->set.contains(item.getResType())).collect(Collectors.toList());
            }

            // 给起始版本和结束版本复制一个
            List<AdsSopReportMifSumVO> result = new ArrayList<>();
            for (AdsSopReportMifSumVO v1 : voList) {
                AdsSopReportMifSumVO v2 = AdsSopReportMifSumVO.copy(v1);
                v1.setVersion(req.getStartVersion());
                v2.setVersion(req.getEndVersion());
                result.add(v1);
                result.add(v2);
            }
            return result;
        };
    }

    @Override
    public List<String> distinctData(QueryReportCommonReq req) {
        String paramType = req.getParamType();
        if (!StringUtils.isBlank(paramType)) {
            String dbName = ORMUtils.getColumnByFieldName(AdsSopReportMifDO.class, paramType);
            if (!StringUtils.isBlank(dbName)) {
                String baseSql = ORMUtils.getSql("/sql/sop/ads_start_inventory_base.sql");
                String sql = "select distinct $paramType from ( " + baseSql + " ) t";
                sql = sql.replace("$paramType", dbName);

                WhereContent deviceWhere = deviceWhereSql(req);
                WhereContent cvmWhere = cvmWhereSql(req);
                sql = replaceWhereContentToSql(sql, deviceWhere, cvmWhere);
                Object[] params = combineParams(deviceWhere, cvmWhere);

                return ckcrpreadDBHelper.getRaw(String.class, sql, params);
            }
        }
        return new ArrayList<>();
    }
}
