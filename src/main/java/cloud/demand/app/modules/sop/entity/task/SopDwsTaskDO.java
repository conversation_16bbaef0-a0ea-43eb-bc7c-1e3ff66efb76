package cloud.demand.app.modules.sop.entity.task;

import cloud.demand.app.modules.sop_device.sopTask.frame.task.SimpleSopTask;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
@Table("sop_dws_task")
public class SopDwsTaskDO extends SimpleSopTask implements SopAlertDO{

}
