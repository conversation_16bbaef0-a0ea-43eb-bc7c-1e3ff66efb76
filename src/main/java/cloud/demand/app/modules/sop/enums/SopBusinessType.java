package cloud.demand.app.modules.sop.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

// 业务类型
@Getter
@AllArgsConstructor
public enum SopBusinessType {
    SELF(1,"自研业务"),
    CLOUD(2,"云业务"),

    SELF_2_CLOUD(3,"自研上云"),

    ;
    private final Integer type;
    private final String desc;

    public static String getByType(Integer type){
        if (type == null){
            return null;
        }
        for (SopBusinessType value : values()) {
            if (Objects.equals(value.type, type)){
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 通过描述获取业务类型
     * @param desc 描述
     * @return
     */
    public static SopBusinessType getByDesc(String desc){
        if (StringUtils.isBlank(desc)){
            return null;
        }
        for (SopBusinessType value : values()) {
            if (desc.equals(value.getDesc())){
                return value;
            }
        }
        return null;
    }

    /**
     * 获取Obs业务类型名称
     * @param desc 业务类型描述
     * @return
     */
    public static String getObsName(String desc){
        if (StringUtils.isBlank(desc)){
            return desc;
        }else {
            for (SopBusinessType value : values()) {
                if (desc.equals(value.getDesc())){
                    if (value==SELF){
                        return "内部业务";
                    }else {
                        return desc;
                    }
                }
            }
        }
        return desc;
    }
}
