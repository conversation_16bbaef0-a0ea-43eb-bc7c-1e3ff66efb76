package cloud.demand.app.modules.sop_yunti.task.process;

import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.SimpleTaskEnum;
import cloud.demand.app.modules.mrpv2.task.process.SimpleAbstractCommonTaskProcess;
import cloud.demand.app.modules.sop.enums.TaskStatus;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_yunti.enums.YuntiTaskEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
public class DwsYuntiTaskProcess extends SimpleAbstractCommonTaskProcess<SimpleCommonTask> {
    @Override
    public SimpleCommonTask getReadyTask(ITaskEnum taskEnum) {
        List<String> dwsReadyStatus = Arrays.asList(TaskStatus.NEW.getName(), TaskStatus.ERROR.getName());

        List<String> adsNotReadStatus = Arrays.asList(TaskStatus.NEW.getName(), TaskStatus.RUNNING.getName(), TaskStatus.ERROR.getName());
        // dws就绪条件：
        // 1. 状态为NEW或者ERROR
        // 2. ads不存在同版本未完成
        // 3. dwd对应同期的版本都已完成
        List<String> dwdTask = ListUtils.newList(
                YuntiTaskEnum.DWD_SOP_YUNTI_CVM_RETURN.getName(), // 退回
                YuntiTaskEnum.DWD_SOP_YUNTI_CVM_DEMAND.getName() // 需求
        );
        return getDbHelper().getRawOne(SimpleCommonTask.class,
                "select dws.* from simple_common_task dws " +
                        "where dws.status in (?) and dws.name = ? " +
                        "and not exists (select 1 from simple_common_task ads where ads.batch_id != dws.batch_id and ads.name = ? and ads.version = dws.version and ads.status in (?)) " +
                        "and exists (select 1 from simple_common_task dwd where dwd.batch_id = dws.batch_id and dwd.version = dws.version and dwd.status = ? and dwd.name in (?) HAVING count(distinct dwd.name) = ?) " +
                        "order by dws.id asc",
                dwsReadyStatus,
                taskEnum.getName(),
                YuntiTaskEnum.ADS_SOP_YUNTI_CVM.getName(),
                adsNotReadStatus,
                TaskStatus.FINISH.getName(),
                dwdTask,
                dwdTask.size());
    }
}
