package cloud.demand.app.modules.sop_yunti.entity.ads;


import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("ads_sop_yunti_report_mif")
public class AdsSopYuntiReportMifDO {
    /** 分区键，代表数据版本<br/>Column: [version] */
    @Column(value = "version")
    private String version;

    /** 类似于 MYSQL 的自增 ID，靠内存写入，方便下游 dws 宽表分页拉取数据<br/>Column: [id] */
    @Column(value = "id")
    private Long id;

    /** 数据版本生成时间<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private Date statTime;

    /** 云梯数据版本生成时间<br/>Column: [yunti_stat_time] */
    @Column(value = "yunti_stat_time")
    private LocalDate yuntiStatTime;

    /** 指标全名，如需求的云业务的已执行量的英文代号<br/>Column: [index] */
    @Column(value = "index")
    private String index;

    /** 设备数<br/>Column: [num] */
    @Column(value = "num")
    private BigDecimal num;

    /** 核心数<br/>Column: [core_num] */
    @Column(value = "core_num")
    private BigDecimal coreNum;

    /** 容量<br/>Column: [capacity] */
    @Column(value = "capacity")
    private BigDecimal capacity;

    /** 业务类型，云业务; 自研业务<br/>Column: [business_type] */
    @Column(value = "business_type")
    private String businessType;

    /** 指标日期<br/>Column: [index_date] */
    @Column(value = "index_date")
    private LocalDate indexDate;

    /** 指标年份<br/>Column: [index_year] */
    @Column(value = "index_year")
    private Integer indexYear;

    /** 指标月份<br/>Column: [index_month] */
    @Column(value = "index_month")
    private Integer indexMonth;

    /** 指标年月<br/>Column: [index_year_month] */
    @Column(value = "index_year_month")
    private String indexYearMonth;

    /** 指标周数（全年第 n 周）<br/>Column: [index_week] */
    @Column(value = "index_week")
    private Integer indexWeek;

    /** 资源类型<br/>Column: [res_type] */
    @Column(value = "res_type")
    private String resType;

    /** 资源池类型<br/>Column: [res_pool_type] */
    @Column(value = "res_pool_type")
    private String resPoolType;

    /** 项目类型<br/>Column: [obs_project_type] */
    @Column(value = "obs_project_type")
    private String obsProjectType;

    /** BG名<br/>Column: [bg_name] */
    @Column(value = "bg_name")
    private String bgName;

    /** 自定义BG名<br/>Column: [custom_bg_name] */
    @Column(value = "custom_bg_name")
    private String customBgName;

    /** 部门名<br/>Column: [dept_name] */
    @Column(value = "dept_name")
    private String deptName;

    /** 规划产品名<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** 国内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 国家<br/>Column: [country_name] */
    @Column(value = "country_name")
    private String countryName;

    /** 城市：可同时表示物理机城市，也可以表示腾讯云 region<br/>Column: [city_name] */
    @Column(value = "city_name")
    private String cityName;

    /** cmdb campus<br/>Column: [cmdb_campus_name] */
    @Column(value = "cmdb_campus_name")
    private String cmdbCampusName;

    /** cmdb module<br/>Column: [cmdb_module_name] */
    @Column(value = "cmdb_module_name")
    private String cmdbModuleName;

    /** 腾讯云 zone<br/>Column: [txy_zone_name] */
    @Column(value = "txy_zone_name")
    private String txyZoneName;

    /** cvm 实例类型<br/>Column: [cvm_gins_family] */
    @Column(value = "cvm_gins_family")
    private String cvmGinsFamily;

    /** cvm 实例规格<br/>Column: [cvm_gins_type] */
    @Column(value = "cvm_gins_type")
    private String cvmGinsType;

    /** 物理机大类<br/>Column: [phy_device_big_family] */
    @Column(value = "phy_device_big_family")
    private String phyDeviceBigFamily;

    /** 物理机机型族<br/>Column: [phy_device_family] */
    @Column(value = "phy_device_family")
    private String phyDeviceFamily;

    /** 物理机设备类型<br/>Column: [phy_device_type] */
    @Column(value = "phy_device_type")
    private String phyDeviceType;

    /** 是否参与对冲，0 否 1 是<br/>Column: [is_hedge] */
    @Column(value = "is_hedge")
    private Integer isHedge;

    /** 是否有效对冲，0 否 1 是<br/>Column: [has_hedged] */
    @Column(value = "has_hedged")
    private Integer hasHedged;

    /** 是否CA<br/>Column: [is_ca] */
    @Column(value = "is_ca")
    private Integer isCa;

    /** 额外字段<br/>Column: [json_text] */
    @Column(value = "json_text")
    private String jsonText;

    /** 容量单位<br/>Column: [capacity_unit] */
    @Column(value = "capacity_unit")
    private String capacityUnit;

    /** obs业务<br/>Column: [obs_business_type] */
    @Column(value = "obs_business_type")
    private String obsBusinessType;

    /** 自研上云标识：自研上云；非自研上云<br/>Column: [self_to_cloud_type] */
    @Column(value = "self_to_cloud_type")
    private String selfToCloudType;

    /** 是否忽略，1：是，0 ：否<br/>Column: [is_ignore] */
    @Column(value = "is_ignore")
    private Integer isIgnore;

    /** cvm 机型族<br/>Column: [cvm_gins_kingdom] */
    @Column(value = "cvm_gins_kingdom")
    private String cvmGinsKingdom;

    /** 需求id<br/>Column: [item_id] */
    @Column(value = "item_id")
    private String itemId;

    /** 需求/退回执行流程单id<br/>Column: [order_id] */
    @Column(value = "order_id")
    private String orderId;

    /** 核心类型<br/>Column: [core_type] */
    @Column(value = "core_type")
    private String coreType;

    /** cpu平台<br/>Column: [cpu_platform] */
    @Column(value = "cpu_platform")
    private String cpuPlatform;

    /** cpu系列<br/>Column: [cpu_type] */
    @Column(value = "cpu_type")
    private String cpuType;

    /** cpu型号<br/>Column: [cpu_model] */
    @Column(value = "cpu_model")
    private String cpuModel;

    /** 网卡类型<br/>Column: [network_card_type] */
    @Column(value = "network_card_type")
    private String networkCardType;

    /** gpu卡型<br/>Column: [gpu_type] */
    @Column(value = "gpu_type")
    private String gpuType;

    /** gpu卡类型<br/>Column: [gpu_card_type] */
    @Column(value = "gpu_card_type")
    private String gpuCardType;

    /** gpu 型号<br/>Column: [gpu_model] */
    @Column(value = "gpu_model")
    private String gpuModel;

    /** num是否有效，0 否 1 是，已执行有效为指标日期小于等于切片日期，未执行有效为指标日期大于等于切片日期，物理机精确到月，cvm精确到天<br/>Column: [is_num_effective] */
    @Column(value = "is_num_effective")
    private Integer isNumEffective;

    /** 代码版本：1.0，2.0<br/>Column: [code_version] */
    @Column(value = "code_version")
    private String codeVersion;
}
