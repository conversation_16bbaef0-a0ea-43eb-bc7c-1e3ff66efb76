package cloud.demand.app.modules.soe.entitiy.fio.view;

import cloud.demand.app.modules.soe.entitiy.distribute.IForecastGroupKey;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

/** 预测内外 */
@Data
public class ReportDwdFioAnyDO implements IForecastGroupKey {
    @Column(value = "any_product_type",computed = "any(product_type)")
    private String productType;
    @Column(value = "any_product",computed = "any(product)")
    private String product;
    @Column(value = "any_year_month",computed = "any(year_month)")
    private String yearMonth;
    @Column(value = "any_industry_dept",computed = "any(industry_dept)")
    private String industryDept;
    @Column(value = "any_war_zone",computed = "any(war_zone)")
    private String warZone;
    @Column(value = "any_customer_short_name",computed = "any(customer_short_name)")
    private String customerShortName;
    @Column(value = "any_un_customer_short_name",computed = "any(un_customer_short_name)")
    private String unCustomerShortName;
    @Column(value = "any_customhouse_title",computed = "any(customhouse_title)")
    private String customhouseTitle;
    @Column(value = "any_country_name",computed = "any(country_name)")
    private String countryName;
    @Column(value = "any_region_name",computed = "any(region_name)")
    private String regionName;
    @Column(value = "any_un_instance_type",computed = "any(un_instance_type)")
    private String unInstanceType;
    @Column(value = "any_instance_type",computed = "any(instance_type)")
    private String instanceType;

    public void copy(ReportFioAnyDO ret){
        ret.setModType(this.getModType());
        ret.setProductType(this.getProductType());
        ret.setProduct(this.getProduct());
        ret.setYearMonth(this.getYearMonth());
        ret.setIndustryDept(this.getIndustryDept());
        ret.setWarZone(this.getWarZone());
        ret.setCustomerShortName(this.getCustomerShortName());
        ret.setUnCustomerShortName(this.getUnCustomerShortName());
        ret.setCustomhouseTitle(this.getCustomhouseTitle());
        ret.setCountryName(this.getCountryName());
        ret.setRegionName(this.getRegionName());
        ret.setUnInstanceType(this.getUnInstanceType());
        ret.setInstanceType(this.getInstanceType());
    }

    @Override
    public String getModType() {
        return null;
    }

    @Override
    public void setModType(String v) {
    }
}
