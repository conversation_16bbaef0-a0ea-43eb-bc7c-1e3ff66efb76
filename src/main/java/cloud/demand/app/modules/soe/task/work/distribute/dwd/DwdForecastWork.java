package cloud.demand.app.modules.soe.task.work.distribute.dwd;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.common.service.entity.ResPlanHolidayWeekDO;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.SimpleTaskEnum;
import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.operation_view.inventory_health.dto.HolidayWeekInfoDTO;
import cloud.demand.app.modules.operation_view.inventory_health.service.InventoryHealthDictService;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplItemCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplItemVersionCfDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.soe.entitiy.dict.SoeRegionNameCountryDO;
import cloud.demand.app.modules.soe.entitiy.distribute.dwd.DwdSoeForecastDatumItemDfDO;
import cloud.demand.app.modules.soe.enums.Constant;
import cloud.demand.app.modules.soe.enums.DistributeDimEnum;
import cloud.demand.app.modules.soe.enums.score.SoeWeekNEnum;
import cloud.demand.app.modules.soe.service.SoeCommonService;
import cloud.demand.app.modules.soe.task.work.distribute.DwdDistributeWork;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_util.utils.CommonUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 基准预测任务
 */
@Slf4j(topic = "分货看板-dwd层-预测基准数据：")
@Service
public class DwdForecastWork extends DwdDistributeWork {

    /**
     * 新ck的std_crp
     */
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    /**
     * 查节假周
     */
    @Resource
    private InventoryHealthDictService healthDictService;

    @Resource
    private DictService dictService;

    @Resource
    private SoeCommonService commonService;

    /**
     * mysql
     */
    @Resource
    private DBHelper demandDBHelper;

    @Override
    public ITaskEnum getEnum() {
        return SimpleTaskEnum.DWD_SOE_DISTRIBUTE_FORECAST;
    }


    @Override
    public void doWork(SimpleCommonTask simpleCommonTask) {
        String version = simpleCommonTask.getVersion(); // 版本即切片时间，格式：yyyy-MM-dd
        // 需要用到TaskLog，所以用bean取调用
        SpringUtil.getBean(DwdForecastWork.class).genForecastData(version);
    }

    @TaskLog(taskName = "soeDistribute@DWD@forecastDatum")
    public void genForecastData(String version) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start(String.format("切片时间【%s】,start", version));
        stopWatch.stop();
        // step1：查询预测基准
        LocalDate statTime = LocalDate.parse(version);
        // step2：切片时间月的首周
        YearMonth yearMonth = CommonUtils.toYearMonth(statTime);
        ResPlanHolidayWeekDO firstWeekByMonth = healthDictService.getFirstWeekByMonth(yearMonth);
        if (firstWeekByMonth == null) {
            throw new ITException("查询切片时间所在月首周异常：切片时间：【%s】", statTime);
        }
        // step2：未来3个月（按月遍历）
        List<YearMonth> yearMonths = SoeCommonUtils.rangeDate(yearMonth, 3);

        stopWatch.start("未来13周（遍历查询预约单对应的需求单）");
        List<DwdSoeForecastDatumItemDfDO> saveData = new ArrayList<>();
        for (YearMonth ym : yearMonths) {
            // 匹配预约和预测（按国家）
            saveData.addAll(matchOrderWithForecast(ym, DistributeDimEnum.Country));
            // 匹配预约和预测（按地域）
            saveData.addAll(matchOrderWithForecast(ym, DistributeDimEnum.Region));
        }


        // 填充切片时间，id和提前周补充信息
        AtomicLong id = new AtomicLong(0);
        for (DwdSoeForecastDatumItemDfDO saveDatum : saveData) {
            saveDatum.setStatTime(statTime);
            saveDatum.setId(id.getAndIncrement());
            Map<String, String> wInfoMap = saveDatum.getWInfoMap();
            if (wInfoMap != null) {
                saveDatum.setWInfo(JSON.toJson(wInfoMap));
            }
        }


        cleanService.simpleClean(saveData);
        stopWatch.stop();
        stopWatch.start(String.format("清洗预测基准底表数据：【%s】", version));
        // step4：清洗切片时间
        CkDBUtils.delete(ckcldStdCrpDBHelper, version, DwdSoeForecastDatumItemDfDO.class);
        // step5：写入ck
        log.info(String.format("写入ck量级：切片时间：【%s】，size：【%s】", version, saveData.size()));
        CkDBUtils.saveBatch(ckcldStdCrpDBHelper, saveData);
        log.info(stopWatch.prettyPrint());
    }

    /**
     * 匹配预约预测
     *
     * @param yearMonth 年月
     * @return 匹配完成的预约预测
     */
    private List<DwdSoeForecastDatumItemDfDO> matchOrderWithForecast(YearMonth yearMonth, DistributeDimEnum distributeDim) {
        // 是否按国家分货
        boolean distributeCountry = distributeDim == DistributeDimEnum.Country;
        List<DwdSoeForecastDatumItemDfDO> ret = new ArrayList<>();

        List<ResPlanHolidayWeekDO> weekInfoByYearMonth = dictService.getHolidayWeekInfoByYearMonth(yearMonth.getYear(), yearMonth.getMonthValue());
        weekInfoByYearMonth.sort((o1, o2) -> StringUtils.compare(o1.getStart(), o2.getStart()));
        ResPlanHolidayWeekDO firstWeek = weekInfoByYearMonth.get(0);

        String yearMonthFormat = SoeCommonUtils.getYearMonth(yearMonth.getYear(), yearMonth.getMonthValue());
        String yearWeek = SoeCommonUtils.getYearWeek(firstWeek.getYear(), firstWeek.getWeek()); // 月首周

        // 地域转国家
        Map<String, SoeRegionNameCountryDO> region2CountryMap = ListUtils.toMap(commonService.getRegion2Country(),
                SoeRegionNameCountryDO::getRegionName, item -> item);

        // step1：查询该年周范围内的预约单
        List<DwdCrpPplItemCfDO> appliedData = DBList.ckcldStdCrpDBHelper.getAll(DwdCrpPplItemCfDO.class,
                "where status = ? and `year` = ? and "
                        + "month = ? and source in (?) and product in (?) and demand_type in (?)",
                PplItemStatusEnum.APPLIED.getCode(),
                yearMonth.getYear(), yearMonth.getMonthValue(),
                Arrays.asList(PplOrderSourceTypeEnum.IMPORT.getCode(),
                        PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode())
                , Constant.distributeProductClass,
                Arrays.asList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode()));
        log.info(String.format("查询该年周范围内的预约单：年月：【%s】，数量级：【%s】", yearMonth, appliedData.size()));

        if (ListUtils.isEmpty(appliedData)) {
            log.error("无预约单信息");
            return ret;
        }

        // 预约单分组
        Map<String, List<DwdCrpPplItemCfDO>> appliedCollect = appliedData.stream()
                .collect(Collectors.groupingBy(v -> Strings.join("@",
                        v.getProduct(),
                        v.getIndustryDept(),
                        v.getCustomerShortName(),
                        v.getInstanceType(),
                        region2CountryMap.get(v.getRegionName()),
                        distributeCountry ? StdUtils.EMPTY_STR : v.getRegionName()))); // 产品 @ 行业部门 @ 客户简称 @ 实例类型 @ 国家 @地域

        Map<String, DwdSoeForecastDatumItemDfDO> dataMap = new HashMap<>();

        // 构建返回数据集
        appliedCollect.forEach((k, ls) -> {
            DwdSoeForecastDatumItemDfDO itemDfDO = new DwdSoeForecastDatumItemDfDO();
            DwdCrpPplItemCfDO v = ls.get(0);
            itemDfDO.setProductType(ProductTypeEnum.CVM.getCode()); // 只有CVM
            itemDfDO.setIndustryDept(v.getIndustryDept()); // 行业部门-维度
            itemDfDO.setCustomerShortName(v.getCustomerShortName()); // 客户简称-维度
            itemDfDO.setInstanceType(v.getInstanceType()); // 实例类型-维度
            itemDfDO.setDistributeType(distributeDim.getCode());//分货类型
            SoeRegionNameCountryDO soeRegionNameCountryDO = region2CountryMap.get(v.getRegionName());
            if (soeRegionNameCountryDO != null) {
                itemDfDO.setCountryName(soeRegionNameCountryDO.getCountryName()); // 国家-维度
                itemDfDO.setRegionName(distributeCountry ? StdUtils.EMPTY_STR : soeRegionNameCountryDO.getRegionName());
                itemDfDO.setCustomhouseTitle(soeRegionNameCountryDO.getCustomhouseTitle()); // 国内外
            }
            itemDfDO.setTotalCore(BigDecimal.valueOf(ls.stream().mapToInt(DwdCrpPplItemCfDO::getTotalCore).sum())); // 预约总量
            List<String> collect = ls.stream().map(DwdCrpPplItemCfDO::getYunxiaoOrderId).collect(Collectors.toList());
            itemDfDO.setOrderIds(StringUtils.join(collect, "@")); // 预约单id集合
            itemDfDO.setWInfoMap(new TreeMap<>());
            itemDfDO.setYearMonth(yearMonthFormat);
            itemDfDO.setYearWeek(yearWeek); // 存节假周所在月首周
            ret.add(itemDfDO);
            dataMap.put(k, itemDfDO); // 后面匹配WeekN时用
        });


        // step2：从起始时间开始看历史13周
        LocalDate firstWeekStartDate = LocalDate.parse(firstWeek.getStart()); // 月首周起始时间
        List<HolidayWeekInfoDTO> holidayWeekFromCurrent = healthDictService.getHolidayWeekInfoBase(firstWeekStartDate, -13); // 从w1开始，不看w0，w0用来存结余
        for (HolidayWeekInfoDTO holidayWeekInfoDTO : holidayWeekFromCurrent) {
            int weekN = Math.abs(holidayWeekInfoDTO.getWeekNFromNow()); // 从 -13 ~ 0，所以这里取绝对值
            SoeWeekNEnum weekNEnum = SoeWeekNEnum.getWeekN(weekN); // 提前周枚举
            String weekNEnd = holidayWeekInfoDTO.getEndDate(); // 周结束时间
            String weekNEnd_ = weekNEnd.replace("-", ""); // 格式：yyyyMMdd

            // step3：获取ppl版本（匹配条件：版本号小于周结束时间 & 有效版本）
            PplVersionDO versionDOS = demandDBHelper.getOne(PplVersionDO.class,
                    "where SUBSTR(version_code,3,8) <= ? " + // 版本号小于等于周结束时间（即改版本在周结束之前）
                            "and concat(demand_begin_year,'-',if(demand_begin_month < 10,concat('0',demand_begin_month),demand_begin_month)) <= ? " + // 有效版本
//                            "and concat(demand_end_year,'-',if(demand_end_month < 10,concat('0',demand_end_month),demand_end_month)) >= ? " + // 有效版本
                            "order by id desc limit 1", weekNEnd_, yearMonthFormat); // 在版本结束之前 & 提单时间按月维度最早

            if (versionDOS == null) {
                log.error(String.format("无对于版本号，结束时间：【%s】,预测年月：【%s】", weekNEnd, yearMonthFormat));
                continue;
            }

            String versionCode = versionDOS.getVersionCode();

            // step4：根据版本号获取week n的提前周预测数据
            List<DwdCrpPplItemVersionCfDO> forecastWeeNData = ckcldStdCrpDBHelper.getAll(
                    DwdCrpPplItemVersionCfDO.class, "where version_code = ? "
                            + " and is_comd != 1 and source != ? and product in (?) "
                            + " and demand_type in (?) "
                            + " and year = ?"
                            + " and month = ?",
                    versionCode, PplOrderSourceTypeEnum.FORECAST.getCode(),
                    Constant.distributeProductClass,
                    Arrays.asList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode()),
                    yearMonth.getYear(), yearMonth.getMonthValue());

            // step5：预测分组 （产品 @ 行业部门 @ 客户简称 @ 实例类型 @ 国家 @地域）

            // 预测分组
            Map<String, List<DwdCrpPplItemVersionCfDO>> forecastWeeKNCollect = forecastWeeNData.stream()
                    .collect(Collectors.groupingBy(v -> Strings.join("@",
                            v.getProduct(),
                            v.getIndustryDept(),
                            v.getCustomerShortName(),
                            v.getInstanceType(),
                            region2CountryMap.get(v.getRegionName()),
                            distributeCountry ? StdUtils.EMPTY_STR : v.getRegionName()))); // 产品 @ 行业部门 @ 客户简称 @ 实例类型 @ 国家 @地域

            // 预约单匹配预测，存对应Wn的提前预测量 和 ppl-id集合
            dataMap.forEach((k, v) -> {
                List<DwdCrpPplItemVersionCfDO> forecastLs = forecastWeeKNCollect.get(k);
                if (forecastLs == null) {
                    return;
                }
                List<String> pplIds = new ArrayList<>();
                int weekNNum = 0;
                for (DwdCrpPplItemVersionCfDO forecastL : forecastLs) {
                    weekNNum = weekNNum + forecastL.getTotalCore();
                    pplIds.add(forecastL.getPplId());
                }
                weekNEnum.addWn(v, BigDecimal.valueOf(weekNNum)); // 存到对应的Wn
                v.getWInfoMap().put(weekNEnum.name(), versionCode + "@" + pplIds); // 存一下对应的ppl-id
            });
        }

        return ret;
    }
}
