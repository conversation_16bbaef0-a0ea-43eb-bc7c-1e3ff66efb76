package cloud.demand.app.modules.soe.task.process;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.SimpleTaskEnum;
import cloud.demand.app.modules.mrpv2.task.process.SimpleAbstractCommonTaskProcess;
import cloud.demand.app.modules.mrpv2.task.process.SimpleCommonTaskProcess;
import cloud.demand.app.modules.sop.entity.task.SopDwdTaskDO;
import cloud.demand.app.modules.sop.enums.TaskStatus;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.SimpleAbstractSopProcess;
import com.pugwoo.dbhelper.DBHelper;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
public class DwdDistributeProcess extends SimpleAbstractCommonTaskProcess<SimpleCommonTask> {
    @Override
    public SimpleCommonTask getReadyTask(ITaskEnum taskEnum) {
        // dwd就绪条件：
        // 1.状态为NEW或者ERROR
        // 2.同期dwd没有运行中的
        // 3.同期dws不是NEW,ERROR或者RUNNING
        return getDbHelper().getRawOne(SimpleCommonTask.class,
                "select * from simple_common_task dwd " +
                        "where dwd.name = ? and dwd.status in (?) " +
                        "and not exists (select 1 from simple_common_task temp where temp.version = dwd.version and dwd.batch_id != temp.batch_id and dwd.name = temp.name and temp.status in (?)) " +
                        "and not exists (select 1 from simple_common_task temp where temp.version = dwd.version and dwd.batch_id < temp.batch_id and temp.name = ? and temp.status in (?)) " +
                        "order by id asc",
                taskEnum.getName(),
                Arrays.asList(TaskStatus.NEW.getName(),TaskStatus.ERROR.getName()),
                TaskStatus.RUNNING.getName(),
                SimpleTaskEnum.DWS_SOE_DISTRIBUTE.getName(),
                Arrays.asList(TaskStatus.NEW.getName(),TaskStatus.ERROR.getName(),TaskStatus.RUNNING.getName()));
    }

}
