package cloud.demand.app.modules.soe.web;


import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.mrpv2.domain.StatTimeRangeReq;
import cloud.demand.app.modules.mrpv2.domain.StatTimeRangeWithMonthEndDayReq;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.SimpleTaskEnum;
import cloud.demand.app.modules.mrpv2.task.process.SimpleCommonTaskProcess;
import cloud.demand.app.modules.mrpv2.utils.PageQueryUtils;
import cloud.demand.app.modules.mrpv2.utils.SecurityFiberSupplier;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.soe.task.process.AdsDistributeProcess;
import cloud.demand.app.modules.soe.task.work.DwdTxyScaleDfViewWork;
import cloud.demand.app.modules.soe.task.work.DwdTxyScaleLongTailDfViewWork;
import cloud.demand.app.modules.soe.task.work.distribute.ads.AdsDistributeItemWork;
import cloud.demand.app.modules.soe.task.work.distribute.dwd.DwdForecastWork;
import cloud.demand.app.modules.soe.task.work.distribute.dwd.DwdOrderWork;
import cloud.demand.app.modules.soe.task.work.distribute.dwd.DwdSupplyWork;
import cloud.demand.app.modules.soe.task.work.distribute.dws.DwsOrderWork;
import cloud.demand.app.modules.soe.task.work.foreacst_inside_out.dwd.DwdFIOForecastBaseWork;
import cloud.demand.app.modules.soe.task.work.foreacst_inside_out.dwd.DwdFIOForecastOrderWork;
import cloud.demand.app.modules.soe.task.work.foreacst_inside_out.dws.DwsFIOForecastWork;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.domain.ReturnT;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjuster;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestMapping;
import teglib.shaded.io.opencensus.trace.TraceId;
import yunti.boot.exception.ITException;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@Slf4j
@JsonrpcController("/soe/task")
public class SoeTaskController {

    // =============== 分货看板 ================
    // =============== dwd ================
    @Resource
    private DwdForecastWork dwdForecastWork;

    @Resource
    private DwdOrderWork dwdOrderWork;

    @Resource
    private DwdSupplyWork dwdSupplyWork;

    // =============== dws ================

    @Resource
    private DwsOrderWork dwsOrderWork;

    // =============== ads ================

    @Resource
    private AdsDistributeItemWork adsDistributeItemWork;

    // =============== 预测内外 ================
    @Resource
    private DwdFIOForecastBaseWork dwdFIOForecastBaseWork;

    @Resource
    private DwdFIOForecastOrderWork dwdFIOForecastOrderWork;

    @Resource
    private DwsFIOForecastWork dwsFIOForecastWork;

    /** 日规模任务（中长尾按照uin：0处理） */
    @Resource
    private DwdTxyScaleDfViewWork dwdTxyScaleDfViewWork;

    @Resource
    private SimpleCommonTaskProcess process;


    /** 日规模中长尾任务（只有中长尾数据） */
    @Resource
    private DwdTxyScaleLongTailDfViewWork dwdTxyScaleLongTailDfViewWork;

    @Resource
    private PplCommonService commonService;

//    @RequestMapping
//    public ReturnT<String> rebuild0520Data(){
//        commonService.rebuild0520Data();
//        return ReturnT.ok();
//    }

    /**
     * 9：00生成
     */
    @Scheduled(cron = "0 0 9 * * ?")
    @Synchronized(waitLockMillisecond = 0, throwExceptionIfNotGetLock = false)
    public void scheduledDistributeTask() {
        // 分货看板
//        doInitDistributeTask(null);
        // 预测内外
        doInitFIOTask(null);

        // 时停1s，避免执行过快导致重复执行
        try {
            TimeUnit.SECONDS.sleep(1);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 初始化分货任务
     */
    @RequestMapping
    public ReturnT<String> initDistributeTask(@JsonrpcParam @Valid StatTimeRangeReq req) {
        LocalDate startTime = req.getStartTime();
        LocalDate endTime = req.getEndTime();
        long between = ChronoUnit.DAYS.between(startTime, endTime);
        if (between < 0 || between > 31) {
            throw new ITException("初始化定时任务最多跑一个月");
        }
        while (!startTime.isAfter(endTime)) {
            doInitDistributeTask(startTime);
            startTime = startTime.plusDays(1);
        }
        return ReturnT.ok();
    }

    /**
     * 初始化分货任务
     */
    private void doInitDistributeTask(LocalDate statTime) {
        String batchId = TraceId.generateRandomId(new Random()).toLowerBase16(); // 32位
        // dwd
        dwdForecastWork.initTask(statTime, batchId);
        dwdOrderWork.initTask(statTime, batchId);
        dwdSupplyWork.initTask(statTime, batchId);
        // dws
        dwsOrderWork.initTask(statTime, batchId);
        // ads
        adsDistributeItemWork.initTask(statTime, batchId);
    }

    /**
     * 初始化分货任务
     */
    @RequestMapping
    public ReturnT<String> initFIOTask(@JsonrpcParam @Valid StatTimeRangeReq req) {
        LocalDate startTime = req.getStartTime();
        LocalDate endTime = req.getEndTime();
        long between = ChronoUnit.DAYS.between(startTime, endTime);
        if (between < 0 || between > 31) {
            throw new ITException("初始化定时任务最多跑一个月");
        }
        while (!startTime.isAfter(endTime)) {
            doInitFIOTask(startTime);
            startTime = startTime.plusDays(1);
        }
        return ReturnT.ok();
    }

    /**
     * 预测内外任务
     */
    private void doInitFIOTask(LocalDate statTime) {
        String batchId = SoeCommonUtils.getBatchId();
        // dwd
        dwdFIOForecastBaseWork.initTask(statTime, batchId);
        dwdFIOForecastOrderWork.initTask(statTime, batchId);
        // dws
        dwsFIOForecastWork.initTask(statTime, batchId);
    }

    /**
     * 重新初始化ads层
     */
    @RequestMapping
    public ReturnT<String> reInitAdsTask(@JsonrpcParam @Valid StatTimeRangeReq req) {
        AdsDistributeProcess process = (AdsDistributeProcess) adsDistributeItemWork.getTask();
        LocalDate startTime = req.getStartTime();
        LocalDate endTime = req.getEndTime();
        if (startTime == null || endTime == null) {
            throw new ITException("起始和结束时间不能为空");
        }
        long between = ChronoUnit.DAYS.between(startTime, endTime);
        if (between < 0 || between > 31) {
            throw new ITException("初始化定时任务最多跑一个月");
        }
        while (!startTime.isAfter(endTime)) {
            process.reInitTask(SimpleTaskEnum.ADS_SOE_ITEM_DISTRIBUTE, DateUtils.format(startTime));
            startTime = startTime.plusDays(1);
        }
        return ReturnT.ok();
    }

    /**
     * 重建日规模视图（这里中长尾视为同一客户）
     */
    @Deprecated
    @RequestMapping
    public ReturnT<String> rebuildDwdTxyScaleDfView() {
        PageQueryUtils.fiberTaskExecutor.submit(new SecurityFiberSupplier() {
            @Override
            public void consume() {
                dwdTxyScaleDfViewWork.rebuild();
            }
        });
        return ReturnT.ok();
    }

    /**
     * 重建日规模中长尾视图
     */
    @Deprecated
    @RequestMapping
    public ReturnT<String> rebuildDwdTxyScaleLongTailDfView() {
        PageQueryUtils.fiberTaskExecutor.submit(new SecurityFiberSupplier() {
            @Override
            public void consume() {
                dwdTxyScaleLongTailDfViewWork.rebuild();
            }
        });
        return ReturnT.ok();
    }

    @RequestMapping
    public ReturnT<String> initFlushDwdTxyScaleDfViewTask(@JsonrpcParam @Valid StatTimeRangeWithMonthEndDayReq req){
        LocalDate startTime = req.getStartTime();
        LocalDate endTime = req.getEndTime();
        while (!startTime.isAfter(endTime)) {
            // 月末直接设置为 startTime 的最后一天
            if (BooleanUtils.isTrue(req.getIsMonthEndDay())){
                startTime = startTime.with(TemporalAdjusters.lastDayOfMonth());
            }

            // 插入任务
            SimpleCommonTask task = new SimpleCommonTask();
            task.setVersion(DateUtils.format(startTime));
            process.initTask(task, SimpleTaskEnum.DWD_TXY_SCALE_DF_VIEW_FLUSH);

            startTime = startTime.plusDays(1);
        }
        return ReturnT.ok();
    }

    /**
     * 刷新日规模视图（这里中长尾视为同一客户）
     */
    @RequestMapping
    public ReturnT<String> flushDwdTxyScaleDfView(@JsonrpcParam @Valid StatTimeRangeReq req) {
        LocalDate startTime = req.getStartTime();
        LocalDate endTime = req.getEndTime();
        if (startTime == null || endTime == null) {
            throw new ITException("起始和结束时间不能为空");
        }
        long between = ChronoUnit.DAYS.between(startTime, endTime);
        if (between < 0 || between > 31) {
            throw new ITException("初始化定时任务最多跑一个月");
        }
        while (!startTime.isAfter(endTime)) {
            dwdTxyScaleDfViewWork.flushOneDay(startTime);
            startTime = startTime.plusDays(1);
        }
        return ReturnT.ok();
    }

    /**
     * 刷新日规模中长尾视图
     */
    @RequestMapping
    public ReturnT<String> flushDwdTxyScaleLongTailDfView(@JsonrpcParam @Valid StatTimeRangeReq req) {
        LocalDate startTime = req.getStartTime();
        LocalDate endTime = req.getEndTime();
        if (startTime == null || endTime == null) {
            throw new ITException("起始和结束时间不能为空");
        }
        long between = ChronoUnit.DAYS.between(startTime, endTime);
        if (between < 0 || between > 31) {
            throw new ITException("初始化定时任务最多跑一个月");
        }
        while (!startTime.isAfter(endTime)) {
            dwdTxyScaleLongTailDfViewWork.flushOneDay(startTime);
            startTime = startTime.plusDays(1);
        }
        return ReturnT.ok();
    }

    @RequestMapping
    public ReturnT<String> initFlushDwdTxyScaleLongTailDfView(@JsonrpcParam @Valid StatTimeRangeWithMonthEndDayReq req){
        LocalDate startTime = req.getStartTime();
        LocalDate endTime = req.getEndTime();
        while (!startTime.isAfter(endTime)) {
            // 月末直接设置为 startTime 的最后一天
            if (BooleanUtils.isTrue(req.getIsMonthEndDay())){
                startTime = startTime.with(TemporalAdjusters.lastDayOfMonth());
            }

            // 插入任务
            SimpleCommonTask task = new SimpleCommonTask();
            task.setVersion(DateUtils.format(startTime));
            process.initTask(task, SimpleTaskEnum.DWD_TXY_SCALE_LONG_TAIL_DF_VIEW_FLUSH);

            startTime = startTime.plusDays(1);
        }
        return ReturnT.ok();
    }

    /**
     * 获取任务状态
     */
    @RequestMapping
    public ReturnT<List<SoeTaskItem>> getTaskStatus(@JsonrpcParam @Valid StatTimeRangeReq req) {
        AdsDistributeProcess process = (AdsDistributeProcess) adsDistributeItemWork.getTask();
        List<SoeTaskItem> taskList = process.getTaskList(SimpleTaskEnum.ADS_SOE_ITEM_DISTRIBUTE, req);
        return ReturnT.ok(taskList);
    }

    @RequestMapping
    public ReturnT<String> doWork(@JsonrpcParam @Valid SoeTaskReq req) {
        String name = req.getName();
        String type = req.getType();
        String version = req.getVersion();
        switch (type) {
            case "dwd":
                switch (name) {
                    case "forecast":
                        dwdForecastWork.genForecastData(version);
                        break;
                    case "order":
                        dwdOrderWork.genOrderData(version);
                        break;
                    case "supply":
                        dwdSupplyWork.genSupplyData(version);
                        break;
                    default:
                        throw new ITException("dwd层任务名称错误");
                }
                break;
            case "dws":
                if (name.equals("order")) {
                    dwsOrderWork.genOrderForecastData(version);
                } else {
                    throw new ITException("dws层任务名称错误");
                }
                break;
            case "ads":
                switch (name) {
                    case "item":
                        adsDistributeItemWork.genDistributeItemData(version);
                        break;
                    default:
                        throw new ITException("ads层任务名称错误");
                }
                break;
            default:
                throw new ITException("数据层类型错误");
        }
        return ReturnT.ok();
    }

    @RequestMapping
    public ReturnT<String> doForecastInSideAndOutWork(@JsonrpcParam @Valid SoeTaskReq req) {
        String name = req.getName();
        String type = req.getType();
        String version = req.getVersion();
        switch (type) {
            case "dwd":
                switch (name) {
                    case "base":
                        dwdFIOForecastBaseWork.genForecastBase(version);
                        break;
                    case "order":
                        dwdFIOForecastOrderWork.genOrderData(version);
                        break;
                }
                break;
            case "dws":
                switch (name) {
                    case "fio":
                        dwsFIOForecastWork.genForecastInsideAndOut(version);
                        break;
                }
                break;
            default:
                throw new ITException("数据层类型错误");
        }
        return ReturnT.ok();
    }

    @Data
    public static class SoeTaskReq {

        @NotEmpty
        private String type; // dwd，dws，ads

        @NotEmpty
        private String name; // forecast，order，supply

        @NotEmpty
        private String version; // 2024-04-21
    }

    @Data
    public static class SoeTaskItem {

        private String version;
        private String status;
        private String statusDesc;
        private String errMsg;
        private Date startDate;
        private Date endDate;
    }
}
