package cloud.demand.app.modules.soe.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SoeHistoricalFulfillmentModel {
    @ExcelProperty(value = "履约率等级",index = 0)
    private String name;

    @ExcelProperty(value = "得分", index = 1)
    private Integer score;
}
