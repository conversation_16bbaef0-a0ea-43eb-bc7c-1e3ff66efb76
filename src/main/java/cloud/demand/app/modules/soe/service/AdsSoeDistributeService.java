package cloud.demand.app.modules.soe.service;

import cloud.demand.app.modules.soe.dto.distribute.*;
import cloud.demand.app.modules.soe.entitiy.distribute.ads.AdsSoeDistributeItemDfDO;
import cloud.demand.app.modules.soe.entitiy.distribute.ads.AdsSoeDistributeItemWithDwsDO;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;

import java.util.List;

/** soe分货 */
public interface AdsSoeDistributeService {

    /** 获取报表时间范围 */
    List<ReportStatTimeYearMonthItem> getReportTimeRange();

    /** 查询总览，暂不支持分页，有需求在做 */
    List<QueryDistributeReportTotal> getReportSummary(ReportSummaryReq req);

    /** 查询明细 */
    List<AdsSoeDistributeItemDfDO> getReportItem(ReportItemReq req);

    /** 查询一条详细信息 */
    AdsSoeDistributeItemWithDwsDO getReportItemDetail(ReportIdReq req);

    /** 导出excel（得分明细+预约单信息）  */
    ResponseEntity<InputStreamResource> exportExcel(ReportItemReq req);

    /** 获取预测内外数据 */
    ReportForecastInsideAndOutsideResp getForecastInsideAndOutside(ReportForecastReq req);
}
