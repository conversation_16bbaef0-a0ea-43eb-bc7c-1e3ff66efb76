package cloud.demand.app.modules.soe.dto.item;

import cloud.demand.app.modules.soe.dto.req.IDimFieldsReq;
import cloud.demand.app.modules.soe.dto.other.OrderBy;
import cloud.demand.app.modules.soe.dto.req.PageReq;
import cloud.demand.app.modules.soe.model.AbstractReportData;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_util.process.step.IQueryData;
import cloud.demand.app.modules.sop_util.utils.CommonUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;

/** 不带下钻的，无dimMap的指标 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportIndexItem implements IQueryData {

    /** 标签 */
    private String label;

    /** 维度标签 */
    private String dimLabel;

    /** 维度集合 */
    private Map<String,Object> dimMap;

    /** 指标信息 */
    private List<ReportItem> data;

    /** 总计 */
    private ReportItem total;

    /** 可以做最新key和最大key总计处理，而不是统计 */
    public void flushTotal(Boolean startOrEnd,String key){
        ReportItem newTotal = new ReportItem();
        BigDecimal total = BigDecimal.ZERO;
        if (ListUtils.isNotEmpty(data)){
            if (startOrEnd == null || StringUtils.isBlank(key)){
                total = data.stream().map(ReportItem::getNumber).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            }else {
                ReportItem reportItem = data.stream()
                        .min((o1, o2) -> {
                            int compare = StringUtils.compare(String.valueOf(o1.getFields().get(key)), String.valueOf(o2.getFields().get(key)));
                            return startOrEnd? compare: -compare;
                        })
                        .orElse(null);
                if (reportItem != null){
                    Object o = reportItem.getFields().get(key);
                    total = data.stream().filter(item->Objects.equals(item.getFields().get(key),o)).map((item)->ObjectUtils.defaultIfNull(item.getNumber(),BigDecimal.ZERO)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                }
            }
        }
        newTotal.setLabel(SoeCommonUtils.totalLabel);
        newTotal.setNumber(total);
        newTotal.setValue(CommonUtils.getValue(total));
        this.setTotal(newTotal);
    }

    /** 刷新总数 */
    public void flushTotal(){
        flushTotal(null,null);
    }

    public static ReportIndexItem copy(ReportIndexItem item) {
        ReportIndexItem ret = new ReportIndexItem();
        ret.setLabel(item.getLabel());
        ret.setDimLabel(item.getDimLabel());
        ret.setDimMap(item.getDimMap());
        ret.setData(item.getData());
        ret.setTotal(item.getTotal());
        return ret;
    }

    public String getDimLabel(){
        if (this.dimLabel == null){
            this.dimLabel = CommonUtils.getKey(dimMap);
        }
        return dimLabel;
    }

    public void flushDimLabel(){
        this.dimLabel = CommonUtils.getKey(dimMap);
    }

    public ReportIndexItem(String label,Map<String,Object> dimMap) {
        this.label = label;
        this.dimMap = dimMap;
    }

    /** 实体类集合转换为指标集合 */
    public static List<ReportIndexItem> transform(IDimFieldsReq req, List<AbstractReportData> reportData, String label){
        List<ReportIndexItem> ret = new ArrayList<>();

        if (ListUtils.isNotEmpty(reportData)){
            List<String> dims = req.getDims();
            List<String> fields = req.getFields();
            // step 1：按照dimMap维度分组
            Map<String, List<AbstractReportData>> map = ListUtils.groupBy(reportData,
                    report -> SoeCommonUtils.getKey(report,dims));
            for (Map.Entry<String, List<AbstractReportData>> lsEntry : map.entrySet()) {
                List<AbstractReportData> value = lsEntry.getValue();
                ReportIndexItem e = new ReportIndexItem(label,value.get(0).getFields_(dims));
                List<ReportItem> items = new ArrayList<>();
                e.setDimLabel(lsEntry.getKey());
                e.setData(items);
                ReportItem totalItem = new ReportItem();
                e.setTotal(totalItem);
                totalItem.setLabel(SoeCommonUtils.totalLabel);
                BigDecimal total = BigDecimal.ZERO;
                // step 2：按照字段维度进行分组
                Map<String, List<AbstractReportData>> ymMap = ListUtils.groupBy(value, data -> SoeCommonUtils.getKey(data,fields));
                for (Map.Entry<String, List<AbstractReportData>> entry : ymMap.entrySet()) {
                    BigDecimal number = entry.getValue().stream().map((item)->ObjectUtils.defaultIfNull(item.getValue_(),BigDecimal.ZERO))
                            .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    AbstractReportData iReportData = entry.getValue().get(0);
                    total = total.add(number);
                    items.add(new ReportItem(entry.getKey(),iReportData.getFields_(fields),number));
                }
                totalItem.setNumber(total);
                totalItem.setValue(CommonUtils.getValue(total));
                // 按照fields升序排序
                ReportItem.sort(items,fields);
                ret.add(e);
            }
        }
        return ret;
    }

    public static List<ReportIndexItem> sortAndLimit(List<ReportIndexItem> items, List<OrderBy> orderByDim, List<OrderBy> orderByField, PageReq pageReq){
        sort(items,orderByDim,orderByField);
        return SoeCommonUtils.page(items,pageReq);
    }

    /** 排序 */
    public static void sort(List<ReportIndexItem> items,List<OrderBy> orderByDim,List<OrderBy> orderByField){
        if (ListUtils.isEmpty(items) || ListUtils.isEmpty(orderByDim) || ListUtils.isEmpty(orderByField)){
            return;
        }

        List<OrderByFun> orderBy = new ArrayList<>();

        // 维度排序
        if (ListUtils.isNotEmpty(orderByDim)){
            for (OrderBy by : orderByDim) {
                orderBy.add(new OrderByFun(by.getLevel(), item -> item.getDimMap().get(by.getField()),by.getIsDesc()));
            }
        }

        // 字段值排序
        if (ListUtils.isNotEmpty(orderByField)){
            for (OrderBy by : orderByField) {
                orderBy.add(new OrderByFun(by.getLevel(), item -> {
                    List<ReportItem> data = item.getData();
                    if (ListUtils.isEmpty(data)) {
                        return null;
                    }
                    ReportItem reportItem = data.stream().filter(dataItem -> {
                        Object o = dataItem.getFields().get(by.getField());
                        return o!= null && Objects.equals(String.valueOf(o),by.getFieldValue());
                    }).findFirst().orElse(null);
                    return reportItem == null ? null: reportItem.getNumber();
                },by.getIsDesc()));
            }
        }

        // 按照level排序，level越小优先级越高
        orderBy.sort(Comparator.comparingInt(OrderByFun::getLevel));

        // 排序
        items.sort((item1, item2) -> {
            for (OrderByFun orderByFun : orderBy) {
                Object o1 = orderByFun.getFun().apply(item1);
                Object o2 = orderByFun.getFun().apply(item2);
                int compare = SoeCommonUtils.compare(o1, o2);
                // 只要不是相等就直接返回，否则继续比下去
                if (compare!=0){
                    return BooleanUtils.isTrue(orderByFun.isDesc()) ? -compare:compare;
                }
            }
            return 0;
        });
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class OrderByFun {
        private Integer level;

        private Function<ReportIndexItem,Object> fun;

        private boolean isDesc;
    }

}
