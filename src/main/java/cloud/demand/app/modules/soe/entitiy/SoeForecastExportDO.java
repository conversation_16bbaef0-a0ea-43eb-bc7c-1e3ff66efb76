package cloud.demand.app.modules.soe.entitiy;

import cloud.demand.app.modules.soe.model.item.SoeForecastItem;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/** 预测 */
@Data
public class SoeForecastExportDO extends SoeForecastDO{

    @Column("customer_short_name")
    private String customerShortName;

    @Column("industry_dept")
    private String industryDept;

    @Column("demand_source")
    private String demandSource;

    @Column("yunxiao_order_id")
    private String yunxiaoOrderId;
    @Column("begin_buy_date")
    private String beginBuyDate;
    @Column("end_buy_date")
    private String endBuyDate;

    @Column("demand_type_name")
    private String demandType;


    public static SoeForecastItem transform(SoeForecastExportDO entity) {
        SoeForecastItem ret = new SoeForecastItem();
        ret.setProductType(entity.getProductType());
        ret.setYearMonth(entity.getYearMonth());
        ret.setYearWeek(entity.getYearWeek());
        ret.setInstanceType(entity.getInstanceType());
        ret.setGpuType(entity.getGpuType());
        ret.setRegionName(entity.getRegionName());
        ret.setZoneName(entity.getZoneName());
        ret.setCoreNum(entity.getCoreNum());
        ret.setCustomerShortName(entity.getCustomerShortName());
        ret.setIndustryDept(entity.getIndustryDept());
        ret.setDemandSource(entity.getDemandSource());
        ret.setYunxiaoOrderId(entity.getYunxiaoOrderId());
        if (entity.getWinRate() != null && entity.getWinRate().compareTo(BigDecimal.ZERO) > 0){
            ret.setWinRate(entity.getWinRate());
        }
        ret.setBeginBuyDate(entity.getBeginBuyDate());
        ret.setEndBuyDate(entity.getEndBuyDate());
        ret.setDemandType(entity.getDemandType());
        return ret;
    }
}
