package cloud.demand.app.modules.soe.process.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.soe.entitiy.SoeInventoryStartDO;
import cloud.demand.app.modules.soe.entitiy.SoeInventoryStartExportDO;
import cloud.demand.app.modules.soe.model.item.SoeInventoryStartItem;
import cloud.demand.app.modules.soe.model.req.SoeInventoryStartReq;
import cloud.demand.app.modules.soe.model.sql.ObjectSqlBuilder;
import cloud.demand.app.modules.soe.model.sql.SoeSqlParams;
import cloud.demand.app.modules.soe.process.service.SoeInventoryStartService;
import cloud.demand.app.modules.soe.service.SoeCleanService;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

@Service
public class SoeInventoryStartServiceImpl implements SoeInventoryStartService {

    @Resource
    private SoeCleanService cleanService;

    @Resource
    private DBHelper ckcldDBHelper;

    /** GPU库存 */
    @Resource
    private DBHelper planReportDBHelper;

    @Override
    public List<SoeInventoryStartItem> getData(SoeInventoryStartReq req) {
        ObjectSqlBuilder sqlBuilder = req.getSqlBuilder();
        SoeSqlParams sqlParams = req.getSqlParams();
        boolean isExcel = BooleanUtils.isTrue(sqlParams.getIsExcel());
        List<SoeInventoryStartItem> transform;
        if (BooleanUtils.isTrue(sqlParams.getIsCvm())){
            String sql;
            // 导出字段固定一个sql，不在原有sql上动态配置
            if (isExcel){
                sql = ORMUtils.getSql("/sql/soe/inventory/cvm/soe_inventory_start_cvm_export.sql");
                sql = sqlBuilder.build(sql);
                List<SoeInventoryStartExportDO> raw = ckcldDBHelper.getRaw(SoeInventoryStartExportDO.class, sql);
                transform = ListUtils.transform(raw, SoeInventoryStartExportDO::transform);
            }else {
                sql = ORMUtils.getSql("/sql/soe/inventory/cvm/soe_inventory_start_cvm.sql");
                sql = sqlBuilder.build(sql);
                List<SoeInventoryStartDO> raw = ckcldDBHelper.getRaw(SoeInventoryStartDO.class, sql);
                transform = ListUtils.transform(raw, SoeInventoryStartDO::transform);
            }
        }else {
            String sql = ORMUtils.getSql("/sql/soe/inventory/gpu/soe_inventory_start_gpu.sql");
            sql = sqlBuilder.build(sql);
            List<SoeInventoryStartExportDO> raw = planReportDBHelper.getRaw(SoeInventoryStartExportDO.class, sql);
            transform = ListUtils.transform(raw, SoeInventoryStartExportDO::transform);
        }

        doCommon(sqlParams,transform);
        return transform;
    }

    private void doCommon(SoeSqlParams sqlParams,List<SoeInventoryStartItem> transform){
        // 填充年月、年周（由于是实际库存，这里的年月取查询的切片日期年月）
        if (BooleanUtils.isTrue(sqlParams.getIsTrend())){
            boolean isYearMonth = BooleanUtils.isTrue(sqlParams.getIsYearMonth());
            String statTime = sqlParams.getStatTime();
            String yearMonth = statTime.substring(0,7);
            String yearWeek = SoeCommonUtils.getYearWeek(LocalDate.parse(statTime));
            for (SoeInventoryStartItem item : transform) {
                if (isYearMonth){
                    item.setYearMonth(yearMonth);
                }else {
                    item.setYearWeek(yearWeek);
                }
            }
        }
        // 清洗（这里先不清洗展示机型，要先过滤原始机型在清洗）
        cleanService.clean(transform,BooleanUtils.isTrue(sqlParams.getUseShowStrategy()));
    }
}
