package cloud.demand.app.modules.soe.service;

import cloud.demand.app.modules.soe.dto.fio.ReportFioAdvanceWeekResp;
import cloud.demand.app.modules.soe.dto.fio.ReportFioReq;
import cloud.demand.app.modules.soe.dto.fio.ReportFioResp;
import cloud.demand.app.modules.soe.dto.fio.ReportFioV2Resp;
import cloud.demand.app.modules.soe.dto.req.ForecastBaseReq;
import cloud.demand.app.modules.soe.dto.req.YearMonthReq;
import cloud.demand.app.modules.soe.entitiy.fio.dwd.DwdFioForecastBaseItemDfDO;

import java.time.LocalDate;
import java.util.List;

/** 预测内外 */
public interface FioReportService {
    /** 获取最新切片 */
    public LocalDate getMaxStatTime();

    /** 提前周查询 */
    ReportFioResp queryReport(ReportFioReq req);

    List<DwdFioForecastBaseItemDfDO> getForecastBaseData(ForecastBaseReq req);

    /** 提前周查询 */
    ReportFioAdvanceWeekResp getAdvanceWeek(YearMonthReq req);


    /** 预测内外查询（w0~w13分开看） */
    ReportFioV2Resp queryReportV2(ReportFioReq req);

    ReportFioResp queryReportV3(ReportFioReq req);
}
