package cloud.demand.app.modules.soe.enums;

import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import cn.hutool.core.lang.func.Func;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.time.temporal.WeekFields;
import java.util.Objects;
import java.util.function.Function;

/**
 * 时间维度
 */
@Getter
@AllArgsConstructor
public enum DateTypeEnum {

    /**
     * 年月，起始月取月首，结束月取月末
     */
    YearMonth("月", ChronoUnit.MONTHS, SopDateUtils::getYearMonth,
            date -> date.withDayOfMonth(1), date -> date.plusMonths(3).plusDays(-1)),

    /**
     * 年周，起始周取周一，结束周取周末
     */
    YearWeek("周", ChronoUnit.WEEKS, SoeCommonUtils::getYearWeek,
            date -> date.with(WeekFields.ISO.getFirstDayOfWeek()), date -> date.plus(13, ChronoUnit.WEEKS).plusDays(-1)),
    ;
    /**
     * 时间类型名称
     */
    private final String name;

    /**
     * 时间单位
     */
    private final ChronoUnit unit;

    private final Function<LocalDate,String> labelFun;

    /**
     * 计算起始时间的方法
     */
    private final Function<LocalDate, LocalDate> startFun;

    /**
     * 计算结束时间的方法
     */
    private final Function<LocalDate, LocalDate> endFun;

    /**
     * 获取默认
     */
    public static String getDef() {
        return YearMonth.getName();
    }

    public static DateTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (DateTypeEnum value : values()) {
            if (Objects.equals(value.getName(), name)) {
                return value;
            }
        }
        return null;
    }
}
