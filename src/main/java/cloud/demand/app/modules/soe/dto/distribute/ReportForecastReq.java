package cloud.demand.app.modules.soe.dto.distribute;

import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDate;
import java.util.List;

/** 预测内外查询 */
@Data
public class ReportForecastReq {

    @NotNull(message = "切片时间不能为空")
    @DateTimeFormat("yyyy-MM-dd")
    @SopReportWhere(columnValue = "stat_time")
    private LocalDate statTime;

    /** 产品 */
    @NotNull(message = "产品不能为空")
    @Pattern(regexp = "CVM",message = "暂只支持CVM")
    @SopReportWhere(columnValue = "product_type")
    private String productType;

    /** 起始年月 */
    @SopReportWhere(sql = "year_month >= ?")
    private String startYearMonth;

    /** 结束年月 */
    @SopReportWhere(sql = "year_month <= ?")
    private String endYearMonth;

    /** 提前预测周（w0~w13），默认w13，即通过提前13周判断预测内外 {@link cloud.demand.app.modules.soe.enums.score.SoeWeekNEnum} */
    private String weekN;

    /** 匹配策略 {@link cloud.demand.app.modules.soe.enums.ForecastMatchStrategyEnum} */
    private String forecastMatchStrategy;

    /** 行业部门 */
    @SopReportWhere(columnValue = "industry_dept")
    private List<String> industryDept;

    /** 客户简称 */
    @SopReportWhere(columnValue = "customer_short_name")
    private List<String> customerShortName;

    /** 境内外 */
    @SopReportWhere(columnValue = "customhouse_title")
    private List<String> customhouseTitle;

    /** 国家 */
    @SopReportWhere(columnValue = "country_name")
    private List<String> countryName;

    /** 实例类型 */
    @SopReportWhere(columnValue = "instance_type")
    private List<String> instanceType;

    /** 统计维度 */
    private List<String> dims; // 目前可支持维度：行业部门 - industryDept，客户简称 - customerShortName，境内外 - customhouseTitle，国家 - countryName，实例类型 - instanceType

}

