package cloud.demand.app.modules.soe.dto.item;

import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.function.BiFunction;

@Getter
public class BigDecimalItem extends BigDecimal {

    public static BigDecimal ZERO = new BigDecimalItem(0);

    private BigDecimal[] items;

    public static BigDecimal add(BigDecimal v1, BigDecimal v2) {
        if (v1 instanceof BigDecimalItem) {
            return v1.add(v2);
        } else if (v2 instanceof BigDecimalItem) {
            return v2.add(v1);
        } else if (v1 == null) {
            return v2;
        } else if (v2 == null) {
            return v1;
        }
        return v1.add(v2);
    }


    private BigDecimal[] compute(BigDecimal[] mergeItems, BiFunction<BigDecimal, BigDecimal, BigDecimal> func) {
        BigDecimal[] newItems = null;
        if (mergeItems != null) {
            if (items == null) {
                newItems = new BigDecimal[mergeItems.length];
                for (int i = 0; i < mergeItems.length; i++) {
                    newItems[i] = func.apply(BigDecimal.ZERO, mergeItems[i]);
                }
            } else if (items.length == mergeItems.length) {
                newItems = new BigDecimal[mergeItems.length];
                for (int i = 0; i < items.length; i++) {
                    newItems[i] = func.apply(items[i], mergeItems[i]);
                }
            }
        }
        return newItems;
    }

    @NotNull
    @Override
    public BigDecimal add(BigDecimal augend) {
        BigDecimal ret = super.add(augend);
        if (augend instanceof BigDecimalItem) {
            BigDecimal[] newItems = compute(((BigDecimalItem) augend).getItems(), BigDecimal::add);
            ret = build(ret, newItems);
        }
        return ret;
    }


    @NotNull
    @Override
    public BigDecimal subtract(BigDecimal subtrahend) {
        BigDecimal ret = super.subtract(subtrahend);
        if (subtrahend instanceof BigDecimalItem) {
            BigDecimal[] newItems = compute(((BigDecimalItem) subtrahend).getItems(), BigDecimal::subtract);
            ret = build(ret, newItems);
        }
        return ret;
    }

    @NotNull
    @Override
    public BigDecimal multiply(BigDecimal multiplicand) {
        BigDecimal ret = super.multiply(multiplicand);
        if (multiplicand instanceof BigDecimalItem) {
            BigDecimal[] newItems = compute(((BigDecimalItem) multiplicand).getItems(), BigDecimal::multiply);
            ret = build(ret, newItems);
        }
        return ret;
    }

    @Override
    public BigDecimal divide(BigDecimal divisor, int scale, int roundingMode) {
        BigDecimal ret = super.divide(divisor, scale, roundingMode);
        if (divisor instanceof BigDecimalItem) {
            BigDecimal[] newItems = compute(((BigDecimalItem) divisor).getItems(), SoeCommonUtils::divide);
            ret = build(ret, newItems);
        }
        return ret;
    }

    @Override
    public BigDecimal divide(BigDecimal divisor, int roundingMode) {
        BigDecimal ret = super.divide(divisor, roundingMode);
        if (divisor instanceof BigDecimalItem) {
            BigDecimal[] newItems = compute(((BigDecimalItem) divisor).getItems(), (v1, v2) -> SoeCommonUtils.divide(v1, v2, roundingMode));
            ret = build(ret, newItems);
        }
        return ret;
    }

    @NotNull
    @Override
    public BigDecimal divide(BigDecimal divisor) {
        BigDecimal ret = super.divide(divisor);
        if (divisor instanceof BigDecimalItem) {
            BigDecimal[] newItems = compute(((BigDecimalItem) divisor).getItems(), SoeCommonUtils::divide);
            ret = build(ret, newItems);
        }
        return ret;
    }

    @NotNull
    @Override
    public BigDecimal divideToIntegralValue(BigDecimal divisor) {
        BigDecimal ret = super.divideToIntegralValue(divisor);
        if (divisor instanceof BigDecimalItem) {
            BigDecimal[] newItems = compute(((BigDecimalItem) divisor).getItems(), BigDecimal::divideToIntegralValue);
            ret = build(ret, newItems);
        }
        return ret;
    }

    @NotNull
    @Override
    public BigDecimal remainder(BigDecimal divisor) {
        BigDecimal ret = super.remainder(divisor);
        if (divisor instanceof BigDecimalItem) {
            BigDecimal[] newItems = compute(((BigDecimalItem) divisor).getItems(), BigDecimal::remainder);
            ret = build(ret, newItems);
        }
        return ret;
    }

    @NotNull
    @Override
    public BigDecimal divide(BigDecimal divisor, RoundingMode roundingMode) {
        BigDecimal ret = super.divide(divisor, roundingMode);
        if (divisor instanceof BigDecimalItem) {
            BigDecimal[] newItems = compute(((BigDecimalItem) divisor).getItems(), (v1, v2) -> SoeCommonUtils.divide(v1, v2, roundingMode));
            ret = build(ret, newItems);
        }
        return ret;
    }

    @NotNull
    @Override
    public BigDecimal divide(BigDecimal divisor, int scale, RoundingMode roundingMode) {
        BigDecimal ret = super.divide(divisor, scale, roundingMode);
        if (divisor instanceof BigDecimalItem) {
            BigDecimal[] newItems = compute(((BigDecimalItem) divisor).getItems(), (v1, v2) -> SoeCommonUtils.divide(v1, v2, scale, roundingMode));
            ret = build(ret, newItems);
        }
        return ret;
    }

    @Override
    public BigDecimal pow(int n) {
        BigDecimal ret = super.pow(n);
        if (items != null) {
            BigDecimal[] newItems = new BigDecimal[items.length];
            for (int i = 0; i < items.length; i++) {
                newItems[i] = items[i].pow(n);
            }
            ret = build(ret, newItems);
        }
        return ret;
    }

    @NotNull
    @Override
    public BigDecimal abs() {
        BigDecimal ret = super.abs();
        if (items != null) {
            BigDecimal[] newItems = new BigDecimal[items.length];
            for (int i = 0; i < items.length; i++) {
                newItems[i] = items[i].abs();
            }
            ret = build(ret, newItems);
        }
        return ret;
    }

    @NotNull
    @Override
    public BigDecimal setScale(int newScale, RoundingMode roundingMode) {
        BigDecimal ret = super.setScale(newScale, roundingMode);
        if (items != null) {
            BigDecimal[] newItems = new BigDecimal[items.length];
            for (int i = 0; i < items.length; i++) {
                BigDecimal item = items[i];
                newItems[i] = item == null ? null : item.setScale(newScale, roundingMode);
            }
            ret = build(ret, newItems);
        }
        return ret;
    }

    @Override
    public BigDecimal setScale(int newScale, int roundingMode) {
        BigDecimal ret = super.setScale(newScale, roundingMode);
        if (items != null) {
            BigDecimal[] newItems = new BigDecimal[items.length];
            for (int i = 0; i < items.length; i++) {
                BigDecimal item = items[i];
                newItems[i] = item == null ? null : item.setScale(newScale, roundingMode);
            }
            ret = build(ret, newItems);
        }
        return ret;
    }

    @NotNull
    @Override
    public BigDecimal setScale(int newScale) {
        BigDecimal ret = super.setScale(newScale);
        if (items != null) {
            BigDecimal[] newItems = new BigDecimal[items.length];
            for (int i = 0; i < items.length; i++) {
                BigDecimal item = items[i];
                newItems[i] = item == null ? null : item.setScale(newScale);
            }
            ret = build(ret, newItems);
        }
        return ret;
    }

    @NotNull
    @Override
    public BigDecimal negate() {
        BigDecimal ret = super.negate();
        if (items != null) {
            BigDecimal[] newItems = new BigDecimal[items.length];
            for (int i = 0; i < items.length; i++) {
                BigDecimal item = items[i];
                newItems[i] = item == null ? null : item.negate();
            }
            ret = build(ret, newItems);
        }
        return ret;
    }

    @NotNull
    @Override
    public BigDecimal plus() {
        BigDecimal ret = super.plus();
        if (items != null) {
            BigDecimal[] newItems = new BigDecimal[items.length];
            for (int i = 0; i < items.length; i++) {
                BigDecimal item = items[i];
                newItems[i] = item == null ? null : item.plus();
            }
            ret = build(ret, newItems);
        }
        return ret;
    }


    private BigDecimal build(@NotNull BigDecimal val, BigDecimal[] items) {
        if (val instanceof BigDecimalItem) {
            ((BigDecimalItem) val).items = items;
            return val;
        }
        return new BigDecimalItem(val, items);
    }

    public BigDecimalItem(BigDecimal val, BigDecimal[] items) {
        super(val.toString());
        build(this, items);
    }

    public BigDecimalItem(String val, String[] items) {
        super(val);
        if (items != null) {
            this.items = new BigDecimalItem[items.length];
            for (int i = 0; i < items.length; i++) {
                this.items[i] = new BigDecimalItem(items[i]);
            }
        }
    }

    public BigDecimalItem(int val, int[] items) {
        super(val);
        if (items != null) {
            this.items = new BigDecimalItem[items.length];
            for (int i = 0; i < items.length; i++) {
                this.items[i] = new BigDecimalItem(items[i]);
            }
        }
    }

    public BigDecimalItem(long val, long[] items) {
        super(val);
        if (items != null) {
            this.items = new BigDecimalItem[items.length];
            for (int i = 0; i < items.length; i++) {
                this.items[i] = new BigDecimalItem(items[i]);
            }
        }
    }

    public BigDecimalItem(double val, double[] items) {
        super(val);
        if (items != null) {
            this.items = new BigDecimalItem[items.length];
            for (int i = 0; i < items.length; i++) {
                this.items[i] = new BigDecimalItem(items[i]);
            }
        }
    }


    public BigDecimalItem(String val) {
        super(val);
    }


    public BigDecimalItem(double val) {
        super(val);
    }


    public BigDecimalItem(int val) {
        super(val);
    }


    public BigDecimalItem(long val) {
        super(val);
    }


}
