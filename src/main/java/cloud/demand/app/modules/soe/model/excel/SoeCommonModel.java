package cloud.demand.app.modules.soe.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/** 公共model类 */
@Data
public class SoeCommonModel{
    /** 产品类型 */
    @ExcelProperty(value = "产品类型",index = 0)
    private String productType;
    /** 年月 */
    @ExcelProperty(value = "年月",index = 1)
    private String yearMonth;

    /** 实例族 */
    @ExcelProperty(value = "机型族",index = 2)
    private String ginsFamily;

    /** 实例类型（GPU的为卡型） */
    @ExcelProperty(value = "机型",index = 3)
    private String instanceType;

    /** 卡型 */
    @ExcelProperty(value = "卡型",index = 4)
    private String gpuType;


    /** 国内外 */
    @ExcelProperty(value = "境内外",index = 5)
    private String customhouseTitle;

    /** 国家 */
    @ExcelProperty(value = "国家",index = 6)
    private String countryName;


    /** 区域 */
    @ExcelProperty(value = "区域",index = 7)
    private String areaName;

    /** 地域 */
    @ExcelProperty(value = "地域",index = 8)
    private String regionName;

    /** 可用区 */
    @ExcelProperty(value = "可用区",index = 9)
    private String zoneName;

    /** 核心数 */
    @ExcelProperty(value = "核数（卡数）",index = 10)
    private BigDecimal coreNum;
}
