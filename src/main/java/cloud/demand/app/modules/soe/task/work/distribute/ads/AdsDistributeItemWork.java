package cloud.demand.app.modules.soe.task.work.distribute.ads;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.SimpleTaskEnum;
import cloud.demand.app.modules.soe.entitiy.dict.SoeHistoricalFulfillmentDO;
import cloud.demand.app.modules.soe.entitiy.dict.SoeProfitAndLossDO;
import cloud.demand.app.modules.soe.entitiy.distribute.ICustomerInfo;
import cloud.demand.app.modules.soe.entitiy.distribute.IDistributeTotalGroupKey;
import cloud.demand.app.modules.soe.entitiy.distribute.ITotalCoreCompute;
import cloud.demand.app.modules.soe.entitiy.distribute.IWeekNData;
import cloud.demand.app.modules.soe.entitiy.distribute.ads.AdsSoeDistributeItemDfDO;
import cloud.demand.app.modules.soe.entitiy.distribute.dwd.DwdSoeSupplyItemDfDO;
import cloud.demand.app.modules.soe.entitiy.distribute.dws.DwsSoeYunxiaoApplyItemDfDO;
import cloud.demand.app.modules.soe.enums.DistributeDimEnum;
import cloud.demand.app.modules.soe.enums.DynamicProperties;
import cloud.demand.app.modules.soe.enums.score.*;
import cloud.demand.app.modules.soe.service.SoeCommonService;
import cloud.demand.app.modules.soe.service.SoeDictService;
import cloud.demand.app.modules.soe.task.work.distribute.AdsDistributeWork;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import com.alibaba.fastjson.JSON;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分货明细ads任务
 */
@Slf4j(topic = "分货看板-ads层-分货明细数据：")
@Service
public class AdsDistributeItemWork extends AdsDistributeWork {

    /**
     * 新ck的std_crp
     */
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    /**
     * 公共方法
     */
    @Resource
    private SoeCommonService commonService;

    /**
     * 字典
     */
    @Resource
    private SoeDictService dictService;

    @Override
    public ITaskEnum getEnum() {
        return SimpleTaskEnum.ADS_SOE_ITEM_DISTRIBUTE;
    }

    @Override
    public void doWork(SimpleCommonTask simpleCommonTask) {
        String version = simpleCommonTask.getVersion(); // 版本即切片时间，格式：yyyy-MM-dd
        // 需要用到TaskLog，所以用bean取调用
        SpringUtil.getBean(AdsDistributeItemWork.class).genDistributeItemData(version);
    }

    /**
     * 生成分货明细
     */
    @TaskLog(taskName = "soeDistribute@ADS@ItemData")
    public void genDistributeItemData(String version) {
        // 切片时间（起始时间）
        LocalDate statTime = LocalDate.parse(version);
        log.info(String.format("生成分货明细：start：切片时间：【%s】", version));
        // step1：获取dws预约单&提前周信息
        List<DwsSoeYunxiaoApplyItemDfDO> orderData = ckcldStdCrpDBHelper.getAll(DwsSoeYunxiaoApplyItemDfDO.class, "where stat_time = ?", version);
        log.info(String.format("获取dws预约单&提前周信息，切片时间：【%s】,size：【%s】", version, orderData.size()));
        // step2：获取dwd供应汇总表
        List<DwdSoeSupplyItemDfDO> supplyData = ckcldStdCrpDBHelper.getAll(DwdSoeSupplyItemDfDO.class, "where stat_time = ?", version);
        log.info(String.format("获取dwd供应汇总表，切片时间：【%s】,size：【%s】", version, orderData.size()));

        // step3：合并统计
        // 策略表：客户履约率 + 客户盈利率
        Function<ICustomerInfo, String> customerKeyFun = (item) -> StringUtils.joinWith("@", item.getCountryName(), item.getCustomerShortName());
        Map<String, SoeHistoricalFulfillmentDO> allHistoricalFulfillment = ListUtils.toMap(commonService.getAllHistoricalFulfillment(ProductTypeEnum.CVM.getCode()),
                customerKeyFun, item -> item); // 客户履约率
        Map<String, SoeProfitAndLossDO> allProfitAndLoss = ListUtils.toMap(commonService.getAllProfitAndLoss(ProductTypeEnum.CVM.getCode()),
                customerKeyFun, item -> item); // 客户盈利率
        // 未命中的 国家 @ 客户简称 维度计入自动录入集合
        Map<String, SoeHistoricalFulfillmentDO> autoInputHistoricalFulfillment = new HashMap<>(); // 客户履约率（自动录入）
        Map<String, SoeProfitAndLossDO> autoInputProfitAndLoss = new HashMap<>();    // 客户盈利率（自动录入）

        ScoreDict scoreDict = new ScoreDict(allHistoricalFulfillment, allProfitAndLoss, autoInputHistoricalFulfillment, autoInputProfitAndLoss); // 得分策略表

        List<AdsSoeDistributeItemDfDO> saveData = new ArrayList<>();
        orderData.stream().collect(Collectors.groupingBy(item -> item.getDistributeType()))
                .forEach((k, list) -> {
                    saveData.addAll(matchOrderAndSupply(list, supplyData, scoreDict, DistributeDimEnum.getByCode(k)));
                });
        // 填充id
        AtomicLong id = new AtomicLong(0);
        for (AdsSoeDistributeItemDfDO saveDatum : saveData) {
            saveDatum.setId(id.getAndIncrement());
        }

        // step7：自动录入履约率和盈利率策略表
        log.info(String.format("自动录入履约率和盈利率策略表：履约率：【%s】，盈利率：【%s】",
                JSON.toJSON(autoInputHistoricalFulfillment.values()), JSON.toJSON(autoInputProfitAndLoss.values())));
        dictService.autoInputHistoricalFulfillment(new ArrayList<>(autoInputHistoricalFulfillment.values()));
        dictService.autoInputProfitAndLoss(new ArrayList<>(autoInputProfitAndLoss.values()));


        // step8：清理当前切片
        CkDBUtils.delete(ckcldStdCrpDBHelper, version, AdsSoeDistributeItemDfDO.class);
        // step9：写入ck
        log.info(String.format("写入CK，size：【%s】，first：【%s】", saveData.size(), ListUtils.isEmpty(saveData) ? "-" : saveData.get(0)));
        CkDBUtils.saveBatch(ckcldStdCrpDBHelper, saveData);
    }

    private List<AdsSoeDistributeItemDfDO> matchOrderAndSupply(List<DwsSoeYunxiaoApplyItemDfDO> orderData, List<DwdSoeSupplyItemDfDO> supplyData, ScoreDict scoreDict, DistributeDimEnum distributeDim) {
        // 是否按国家分货
        boolean distributeCountry = distributeDim == DistributeDimEnum.Country;

        // step4：预约单&提前周 分组聚合，key：年月，先按年月分组，次月需要用到上个月剩余的库存
        Function<IDistributeTotalGroupKey, String> groupYearMonthKey = item ->
                StringUtils.joinWith("@", item.getYearMonth());

        // 预约单&提前周 分组聚合，key：国家 @ 地域 @ 实例类型
        Function<IDistributeTotalGroupKey, String> groupKey = item ->
                StringUtils.joinWith("@", item.getCountryName(), distributeCountry ? StdUtils.EMPTY_STR : item.getRegionName(), item.getInstanceType());

        // 用treeMap，key的年月需要有序处理
        Map<String, List<DwsSoeYunxiaoApplyItemDfDO>> orderYearMonthMap = new TreeMap<>(ListUtils.groupBy(orderData, groupYearMonthKey)); // 预约单&提前周 分组

        // step5：采购 分组聚合，key：年月
        Map<String, List<DwdSoeSupplyItemDfDO>> purchaseYearMonthMap = ListUtils.groupBy(supplyData.stream()
                        .filter(item -> Objects.equals(item.getSupplyType(), "采购")).collect(Collectors.toList())
                , groupYearMonthKey);


        List<AdsSoeDistributeItemDfDO> saveData = new ArrayList<>();


        // 库存分组，key：国家 @ 地域 @ 实例类型
        Map<String, List<DwdSoeSupplyItemDfDO>> inventoryGroupMap = ListUtils.groupBy(supplyData.stream()
                        .filter(item -> Objects.equals(item.getSupplyType(), "库存")).collect(Collectors.toList())
                , groupKey);

        // 客户+计费类型分组，key：客户简称 @ 计费类型
        Function<DwsSoeYunxiaoApplyItemDfDO, String> customerGroupKey = (item) ->
                StringUtils.joinWith("@", item.getCustomerShortName(), item.getBillType());

        orderYearMonthMap.forEach((yearMonth, ls) -> {

            List<DwdSoeSupplyItemDfDO> purchaseYearMonthData = purchaseYearMonthMap.getOrDefault(yearMonth, new ArrayList<>()); // 同月的采购数据

            Map<String, List<DwdSoeSupplyItemDfDO>> purchaseGroupMap = ListUtils.groupBy(purchaseYearMonthData, groupKey); // 同维度采购，key：国家 @ 地域 @ 实例类型

            Map<String, List<DwsSoeYunxiaoApplyItemDfDO>> orderGroupMap = ListUtils.groupBy(ls, groupKey); // 同维度预约单，key：国家 @ 地域 @ 实例类型

            orderGroupMap.forEach((k, v) -> {
                List<DwdSoeSupplyItemDfDO> inventoryData = inventoryGroupMap.remove(k); // 同维度库存（先remove掉，有剩余后续再插入）
                List<DwdSoeSupplyItemDfDO> purchaseData = purchaseGroupMap.remove(k); // 同维度采购（用remove是为了最后统计剩余的采购，如果采购没用完则放到库存集合中）
                BigDecimal inventoryTotalCore = getTotalCore(inventoryData); // 库存核数
                BigDecimal purchaseTotalCore = getTotalCore(purchaseData); // 采购核数
                BigDecimal supplyTotalCore = inventoryTotalCore.add(purchaseTotalCore); // 供应核数
                BigDecimal inventoryRate = supplyTotalCore.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE :
                        inventoryTotalCore.divide(supplyTotalCore, 6, RoundingMode.HALF_UP); // 库存占供应比
                BigDecimal demandTotalCore = getTotalCore(v); // 需求核数
                BigDecimal gapTotalCore = demandTotalCore.subtract(supplyTotalCore); // 缺口核数

                boolean hasSurplus = gapTotalCore.compareTo(BigDecimal.ZERO) <= 0; // 供应是否有结余
                if (hasSurplus) {
                    mergeToInventory(k, inventoryGroupMap, gapTotalCore);
                }

                Map<String, List<DwsSoeYunxiaoApplyItemDfDO>> customerGroupMap = ListUtils.groupBy(v, customerGroupKey); // 客户简称 @ 计费类型 分组

                List<AdsSoeDistributeItemDfDO> tempSaveData = new ArrayList<>();

                customerGroupMap.forEach((key, vs) -> {  // key：客户简称@计费类型，vs：同客户简称的数据集
                    AdsSoeDistributeItemDfDO tempData = transform(vs.get(0));
                    tempData.setDemandCore(getTotalCore(vs)); // 需求核心数
                    tempData.setDistributeType(distributeDim.getCode());
                    tempData.setRegionName(distributeCountry ? StdUtils.EMPTY_STR : vs.get(0).getRegionName());
                    tempData.setDwsId(StringUtils.join(vs.stream().map(DwsSoeYunxiaoApplyItemDfDO::getId).collect(Collectors.toSet()), ",")); // dws层id集合
                    computeScore(tempData, vs, scoreDict); // 计算得分
                    tempSaveData.add(tempData);
                });

                // 分货，分两种情况，1：需求 > 供应，按优先级依次分供应量；2：需求 <= 供应，由优先级最高的分到供应结余到供应(满足量还是 = 需求)，其他 需求 = 供应分配
                BigDecimal surplusInv = hasSurplus ? gapTotalCore.abs() : BigDecimal.ZERO; // 结余库存（由优先级最高的分到，然后归为0）
                BigDecimal maxRate = BigDecimal.valueOf(100);
                BigDecimal surplusSupply = supplyTotalCore; // 剩余供应（无库存结余时使用）
                // 分组，key：分货档位
                Map<String, List<AdsSoeDistributeItemDfDO>> distributeLevelGroupMap = ListUtils.groupBy(tempSaveData, AdsSoeDistributeItemDfDO::getDistributeLevel);
                for (String level : DistributeLevelEnum.getLevelNameByOrder()) {
                    List<AdsSoeDistributeItemDfDO> levelList = distributeLevelGroupMap.get(level);
                    if (ListUtils.isEmpty(levelList)) {
                        continue;
                    }
                    // 降序排列（总分越高，优先级越高）
                    levelList.sort((o1, o2) -> o2.getPriorityTotalScore().compareTo(o1.getPriorityTotalScore()));
                    for (int i = 0; i < levelList.size(); i++) {
                        AdsSoeDistributeItemDfDO item = levelList.get(i);
                        item.setRanking(i + 1);
                        // 有结余时，缺口 = 0，需求 = 分货量，满足率 = 100%
                        if (hasSurplus) {
                            item.setGapCore(BigDecimal.ZERO);
                            BigDecimal demandCore = item.getDemandCore();
                            BigDecimal supplyCore = demandCore;
                            if (surplusInv.compareTo(BigDecimal.ZERO) > 0) {
                                supplyCore = supplyCore.add(surplusInv);
                                surplusInv = BigDecimal.ZERO; // 分完结余归0
                            }
                            item.setDistributeCore(demandCore); // 分货不用看供应，实际等于 min(需求，供应)
                            item.setSatisfactionRate(maxRate); // 满足率，等于 分货量/需求
                            item.setInventoryCore(supplyCore.multiply(inventoryRate).setScale(0, RoundingMode.HALF_UP)); // 库存，分货前库存，取整
                            item.setPurchaseCore(supplyCore.subtract(item.getInventoryCore())); // 采购，分货前采购
                        } else {
                            // 无结余时需要按优先级依次分货
                            BigDecimal demandCore = item.getDemandCore(); // 需求量
                            BigDecimal distributeCore = SoeCommonUtils.min(surplusSupply, demandCore); // 需求 和 供应剩余 的最小值
                            BigDecimal gapCore = demandCore.subtract(distributeCore);
                            item.setGapCore(gapCore); // 缺口 = 需求 - 分货量
                            item.setDistributeCore(distributeCore); // 分货量
                            item.setSatisfactionRate(distributeCore.multiply(maxRate).divide(demandCore, 2, RoundingMode.HALF_UP)); // 满足率,保留两位小数
                            item.setInventoryCore(distributeCore.multiply(inventoryRate).setScale(0, RoundingMode.HALF_UP)); // 库存，分货前库存，取整
                            item.setPurchaseCore(distributeCore.subtract(item.getInventoryCore())); // 采购，分货前采购

                            surplusSupply = surplusSupply.subtract(distributeCore); // 供应剩余减去分货量
                        }
                    }
                }
                log.info(String.format("并入集合：年月：【%s】，key：【%s】，size：【%s】", yearMonth, k, tempSaveData.size()));
                saveData.addAll(tempSaveData); // 并入集合（tempSaveDate为某月的记录）
            });

        });
        return saveData;
    }

    /**
     * 计算得分
     *
     * @param tempData  ads层实体
     * @param vs        待统计得分dws预约单&提前周实体集合
     * @param scoreDict 得分策略表字典
     */
    private void computeScore(AdsSoeDistributeItemDfDO tempData, List<DwsSoeYunxiaoApplyItemDfDO> vs, ScoreDict scoreDict) {
        String countryName = tempData.getCountryName();
        String customerShortName = tempData.getCustomerShortName();

        // 累加计算提前预测周核数
        for (DwsSoeYunxiaoApplyItemDfDO v : vs) {
            for (SoeWeekNEnum value : SoeWeekNEnum.values()) {
                value.addWn(tempData, value.getGetter().apply(v)); // 累加 wn
            }
        }

        tempData.setHistoricalFulfillmentRate(scoreDict.getHistoricalFulfillmentByKey(countryName, customerShortName)); // 客户履约率
        tempData.setProfitAndLossLevel(scoreDict.getProfitAndLossByKey(countryName, customerShortName)); // 客户盈利率

        // 得分计算，使用时长，履约率，盈利率，提前周，总分
        String billType = tempData.getBillType();
        Integer score = UsageDurationScoreEnum.getScoreByName(billType);
        if (score == null) {
            throw new ITException(String.format("计算使用时长得分异常，不存在改计费类型枚举：【%s】", billType));
        }
        tempData.setUsageDurationScore(BigDecimal.valueOf(score)); // 使用时长得分

        HistoricalFulfillmentScoreEnum levelByRate = HistoricalFulfillmentScoreEnum.getLevelByRate(tempData.getHistoricalFulfillmentRate());
        if (levelByRate == null) {
            throw new ITException(String.format("计算客户履约率得分异常，不存在该履约率对应得发：【%s】",
                    tempData.getHistoricalFulfillmentRate() == null ? "-" : tempData.getHistoricalFulfillmentRate()));
        }
        tempData.setHistoricalFulfillmentScore(BigDecimal.valueOf(levelByRate.getScore())); // 履约率得分

        ProfitAndLossScoreEnum scoreByName = ProfitAndLossScoreEnum.getScoreByName(tempData.getProfitAndLossLevel());
        if (scoreByName == null) {
            throw new ITException(String.format("计算客户盈利率得分异常，不存在该盈利率等级对应得发：【%s】",
                    tempData.getProfitAndLossLevel() == null ? "-" : tempData.getProfitAndLossLevel()));

        }
        tempData.setProfitAndLossScore(BigDecimal.valueOf(scoreByName.getScore())); // 盈利率得分

        tempData.setPredictedLeadTimeScore(SoeWeekNEnum.getScoreByWeekData(tempData)); // 提前周得分

        BigDecimal totalScore = SoeCommonUtils.addAll(
                tempData.getUsageDurationScore(),   // 使用时长
                tempData.getHistoricalFulfillmentScore(), // 履约率
                tempData.getProfitAndLossScore(), // 盈利率
                tempData.getPredictedLeadTimeScore()); // 提前周

        tempData.setPriorityTotalScore(totalScore); // 优先级总分

        // 客户优先级计算（需要参数：提前周得分，w0~w2是否有需求，计费类型，盈利率）
        BigDecimal predictedLeadTimeScore = tempData.getPredictedLeadTimeScore(); // 提前周得分
        BigDecimal w0_2 = SoeCommonUtils.addAll(tempData.getW3(), tempData.getW4()); // w3~4周是否有需求
        boolean isHigh = BigDecimal.valueOf(SoeWeekNEnum.w13.getScore()).compareTo(predictedLeadTimeScore) == 0; // 提前周得分为满分，即提前13周
        boolean isLow = BigDecimal.valueOf(SoeWeekNEnum.w0.getScore()).compareTo(predictedLeadTimeScore) == 0
                && (w0_2.compareTo(BigDecimal.ZERO) == 0); // 提前周得分为0，且w3~4周没有需求，即需求都在w0~2
        boolean isPrepaid = Objects.equals(billType, UsageDurationScoreEnum.PREPAID.getName()); // 是否为包年包月
        boolean isNotNegative = !Objects.equals(tempData.getProfitAndLossLevel(), ProfitAndLossScoreEnum.NEGATIVE.getName()); // 不为负毛利

        DistributeLevelEnum levelEnum;

        // 1. 高优分货档位(同时满足提前期13周&包月&损益>=0 定义为高优分货档)
        // 2. 中优分货档位(剩余其他的归为中优分货档)
        // 3. 低优分货档位(提前期2周内的定义为低优分货档)
        if (isHigh && isPrepaid && isNotNegative) {
            levelEnum = DistributeLevelEnum.HIGH;
        } else if (isLow) {
            levelEnum = DistributeLevelEnum.LOW;
        } else {
            levelEnum = DistributeLevelEnum.MEDIUM;
        }

        tempData.setDistributeLevel(levelEnum.getName()); // 分货档位

    }

    private void mergeToInventory(String k, Map<String, List<DwdSoeSupplyItemDfDO>> inventoryGroupMap, BigDecimal gapTotalCore) {
        gapTotalCore = gapTotalCore.abs();
        DwdSoeSupplyItemDfDO e = new DwdSoeSupplyItemDfDO();
        e.setTotalCore(gapTotalCore); // 只设置库存核数，如果后续不再分组，这里就不需要补充额外的属性
        inventoryGroupMap.computeIfAbsent(k, ls -> new ArrayList<>()).add(e);
    }

    private AdsSoeDistributeItemDfDO transform(DwsSoeYunxiaoApplyItemDfDO item) {
        AdsSoeDistributeItemDfDO ret = new AdsSoeDistributeItemDfDO();
        ret.setStatTime(item.getStatTime());
        ret.setProductType(item.getProductType());
        ret.setYearMonth(item.getYearMonth());
        ret.setIndustryDept(item.getIndustryDept());
        ret.setCustomerShortName(item.getCustomerShortName());
        ret.setCustomhouseTitle(item.getCustomhouseTitle());
        ret.setCountryName(item.getCountryName());
        ret.setInstanceFamily(item.getInstanceFamily());
        ret.setInstanceType(item.getInstanceType());
        ret.setBillType(item.getBillType());
//        adsSoeDistributeItemDfDO.setDemandCore();
//        adsSoeDistributeItemDfDO.setDistributeLevel();
//        adsSoeDistributeItemDfDO.setDistributeCore();
//        adsSoeDistributeItemDfDO.setSatisfactionRate();
//        adsSoeDistributeItemDfDO.setGapCore();
//        adsSoeDistributeItemDfDO.setInventoryCore();
//        adsSoeDistributeItemDfDO.setPurchaseCore();
//        adsSoeDistributeItemDfDO.setPredictedLeadTimeScore();
//        adsSoeDistributeItemDfDO.setUsageDurationScore();
//        adsSoeDistributeItemDfDO.setProfitAndLossScore();
//        adsSoeDistributeItemDfDO.setHistoricalFulfillmentScore();
//        adsSoeDistributeItemDfDO.setPriorityTotalScore();
//        adsSoeDistributeItemDfDO.setDwsId();
        return ret;
    }

    /**
     * 获取供应集合的总核数
     *
     * @param supplyList 供应数据
     * @return 总核数
     */
    private BigDecimal getTotalCore(List<? extends ITotalCoreCompute> supplyList) {
        if (ListUtils.isEmpty(supplyList)) {
            return BigDecimal.ZERO;
        }
        return supplyList.stream()
                .map(ITotalCoreCompute::getTotalCore)
                .reduce(BigDecimal::add).
                orElse(BigDecimal.ZERO);
    }

    /**
     * 得分字典
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScoreDict {
        private Map<String, SoeHistoricalFulfillmentDO> allHistoricalFulfillment; // 客户履约率
        private Map<String, SoeProfitAndLossDO> allProfitAndLoss; // 客户盈利率
        // 未命中的 国家 @ 客户简称 维度计入自动录入集合
        private Map<String, SoeHistoricalFulfillmentDO> autoInputHistoricalFulfillment; // 客户履约率（自动录入）
        private Map<String, SoeProfitAndLossDO> autoInputProfitAndLoss;    // 客户盈利率（自动录入）

        /**
         * 获取履约率，key：国家 @ 客户简称
         */
        public BigDecimal getHistoricalFulfillmentByKey(String countryName, String customerShortName) {
            String key = StringUtils.joinWith("@", countryName, customerShortName);
            SoeHistoricalFulfillmentDO item = allHistoricalFulfillment.get(key);
            if (item == null) {
                // 未命中则初始化并加入自动录入集合
                item = autoInputHistoricalFulfillment.get(key);
                if (item == null) {
                    item = new SoeHistoricalFulfillmentDO();
                    item.setCountryName(countryName);
                    item.setCustomerShortName(customerShortName);
                    item.setHistoricalFulfillmentRate(DynamicProperties.getDefHistoricalFulfillmentRate()); // 默认 50%
                    autoInputHistoricalFulfillment.put(key, item);
                }
            }
            return item.getHistoricalFulfillmentRate();
        }

        /**
         * 获取盈利率等级，key：国家 @ 客户简称
         */
        public String getProfitAndLossByKey(String countryName, String customerShortName) {
            String key = StringUtils.joinWith("@", countryName, customerShortName);
            SoeProfitAndLossDO item = allProfitAndLoss.get(key);
            if (item == null) {
                // 未命中则初始化并加入自动录入集合
                item = autoInputProfitAndLoss.get(key);
                if (item == null) {
                    item = new SoeProfitAndLossDO();
                    item.setCountryName(countryName);
                    item.setCustomerShortName(customerShortName);
                    item.setProfitAndLossLevel(DynamicProperties.getDefProfitAndLoss());
                    autoInputProfitAndLoss.put(key, item);
                }
            }
            return item.getProfitAndLossLevel();
        }
    }
}
