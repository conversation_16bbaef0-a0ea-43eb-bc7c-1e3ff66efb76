package cloud.demand.app.modules.soe.enums.score;

import cloud.demand.app.modules.soe.entitiy.distribute.IScoreGetter;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.function.Function;

/** 历史履约得分 */

@Getter
@AllArgsConstructor
public enum HistoricalFulfillmentScoreEnum implements IScoreGetter {
    LEVEL_1("80%-100%",(rate)-> match(rate,80,100),8),
    LEVEL_2("60%-80%",(rate)-> match(rate,60,80),6),
    LEVEL_3("40%-60%",(rate)-> match(rate,40,60),4),
    LEVEL_4("20%-40%",(rate)-> match(rate,20,40),2),
    LEVEL_5("0%-20%",(rate)-> match(rate,0,20),0),

    ;

    private final String name;

    private final Function<BigDecimal,Boolean> matchFun;
    private final Integer score;

    public static String getDefRate(){
        return "50";
    }

    /** 根据履约率计算履约得分 */
    public static HistoricalFulfillmentScoreEnum getLevelByRate(BigDecimal rate){
        if (rate == null){
            return LEVEL_5;
        }
        if (rate.compareTo(BigDecimal.valueOf(100))>0){
            return LEVEL_1;
        }
        for (HistoricalFulfillmentScoreEnum value : values()) {
            if (value.matchFun.apply(rate)){
                return value;
            }
        }
        return LEVEL_5;
    }

    private static boolean match(BigDecimal v,Integer min,Integer max){
        if (v == null){
            v = BigDecimal.ZERO;
        }

        return v.compareTo(BigDecimal.valueOf(min)) >= 0 && v.compareTo(BigDecimal.valueOf(max)) <= 0;
    }
}
