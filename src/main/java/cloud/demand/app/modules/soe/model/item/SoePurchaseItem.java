package cloud.demand.app.modules.soe.model.item;

import cloud.demand.app.modules.soe.model.CommonItem;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class SoePurchaseItem extends CommonItem {

    private String campus;

    /** 客户简称 */
    private String customerName;

    /** 星云单号 */
    private String quotaId;

    /** 行业部门 */
    private String industryDept;

    /** 是否无货期 */
    private String isNoExpect;

    /** 预计交付日期 */
    private String slaDateExpect;

    /** 期望交付日期 */
    private String quotaUseTime;


    public static SoePurchaseItem transformMock(SoeMockItem mockItem) {
        SoePurchaseItem soePurchaseItem = new SoePurchaseItem();
        soePurchaseItem.setIndustryDept(mockItem.getIndustryDept());
        soePurchaseItem.setProductType(mockItem.getProductType());
        soePurchaseItem.setYearMonth(mockItem.getYearMonth());
        soePurchaseItem.setYearWeek(mockItem.getYearWeek());
        soePurchaseItem.setOriginInstanceType(mockItem.getOriginInstanceType());
        soePurchaseItem.setInstanceType(mockItem.getInstanceType());
        soePurchaseItem.setDeviceType(mockItem.getDeviceType());
        soePurchaseItem.setOriginGinFamily(mockItem.getOriginGinFamily());
        soePurchaseItem.setGinsFamily(mockItem.getGinsFamily());
        soePurchaseItem.setOriginGpuType(mockItem.getOriginGpuType());
        soePurchaseItem.setGpuType(mockItem.getGpuType());
        soePurchaseItem.setCustomhouseTitle(mockItem.getCustomhouseTitle());
        soePurchaseItem.setCountryName(mockItem.getCountryName());
        soePurchaseItem.setAreaName(mockItem.getAreaName());
        soePurchaseItem.setRegionName(mockItem.getRegionName());
        soePurchaseItem.setZoneName(mockItem.getZoneName());
        soePurchaseItem.setCoreNum(mockItem.getCoreNum());
        soePurchaseItem.setNum(mockItem.getNum());
        soePurchaseItem.setIndustryDept(mockItem.getIndustryDept());
        soePurchaseItem.setCustomerShortName(mockItem.getCustomerShortName());
        return soePurchaseItem;
    }
}
