package cloud.demand.app.modules.soe.utils;

import cloud.demand.app.modules.soe.dto.item.BigDecimalItem;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;

/** BigDecimalItem转换 --- BigDecimalItem实际是个数组，转换的时候也要转为数组 */
public class BigDecimalItemSerialize extends JsonSerializer<BigDecimal> {

    @Override
    public void serialize(BigDecimal bigDecimal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (bigDecimal instanceof BigDecimalItem) {
            BigDecimalItem item = (BigDecimalItem) bigDecimal;
            BigDecimal[] items = item.getItems();
            if (items == null){
                jsonGenerator.writeNumber(bigDecimal);
            }else {
                double[] itemV = new double[items.length + 1];
                itemV[0] = item.doubleValue();
                for (int i = 0; i < items.length; i++) {
                    BigDecimal itemI = items[i];
                    itemV[i + 1] = itemI == null ? 0 : itemI.doubleValue();
                }
                jsonGenerator.writeArray(itemV, 0, itemV.length);
            }
        } else {
            jsonGenerator.writeNumber(bigDecimal);
        }
    }
}
