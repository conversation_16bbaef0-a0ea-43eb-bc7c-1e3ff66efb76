package cloud.demand.app.modules.soe.enums.index;


import cloud.demand.app.modules.soe.enums.StoreNameEnum;
import cloud.demand.app.modules.soe.model.compute.IComputeMethod;
import cloud.demand.app.modules.sop_util_v2.enums.index.ICommonIndexEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 动态指标枚举
 */
@Getter
@AllArgsConstructor
public enum DyIndexEnum implements ICommonIndexEnum {
    demo("DY_INDEX_DEMO", StoreNameEnum.dy_index_demo , false, null, null, null,
            ListUtils.newList("orderNumber")),

    ;
    private final String name;

    private final StoreNameEnum storeName;

    private final boolean isHide; // 是否隐藏

    private final Supplier<Map<String, Object>> indexParam; // 指标参数

    private final SupplyAndDemandIndexEnum[] depIndex; // 依赖指标

    private final IComputeMethod compute; // 指标计算

    private final List<String> dyIndexField;
}
