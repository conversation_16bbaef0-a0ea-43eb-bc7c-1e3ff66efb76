package cloud.demand.app.modules.soe.dto.fio;

import cloud.demand.app.modules.soe.entitiy.distribute.IForecastGroupKey;
import cloud.demand.app.modules.soe.model.fields.ForecastFieldEnum;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;

/** 预测内外返回数据 */
@Data
public class ReportFioResp {
    private List<Item> data;

    public static LinkedHashMap<String,String> getDims(){
        LinkedHashMap<String,String> ret = new LinkedHashMap<>();
        for (ForecastFieldEnum value : ForecastFieldEnum.values()) {
            ret.put(value.name(),value.getName());
        }
        return ret;
    }

    @Data
    public static class Item implements IForecastGroupKey {
        private String productType;
        private String product;
        private String yearMonth;
        private String modType;
        private String industryDept;
        private String warZone;
        private String customerShortName;
        private String unCustomerShortName;
        private String customhouseTitle;
        private String countryName;
        private String regionName;
        private String unInstanceType;
        private String instanceType;

        private BigDecimal totalCore;
        private BigDecimal w13; // 提前 w13 订单量
        private BigDecimal w9; // 提前 w9 订单量
        private BigDecimal w5; // 提前 w5 订单量
        private BigDecimal w0; // 提前 w0 订单量（无提前期订单量）

        private BigDecimal baseW13; // w13 基准量
        private BigDecimal baseW9; // w9 基准量
        private BigDecimal baseW5; // w5 基准量

        private BigDecimal useBaseW13; // w13 基准量使用量
        private BigDecimal useBaseW9; // w9 基准量使用量
        private BigDecimal useBaseW5; // w5 基准量使用量

        private BigDecimal orderW13 = BigDecimal.ZERO; // 原订单 w13 提前量（可以不占用基准）
        private BigDecimal orderW9 = BigDecimal.ZERO; // 原订单 w9 提前量（可以不占用基准）
        private BigDecimal orderW5 = BigDecimal.ZERO; // 原订单 w5 提前量（可以不占用基准）

        private BigDecimal adjW13 = BigDecimal.ZERO; // 调整 w13 提前量
        private BigDecimal adjW9 = BigDecimal.ZERO; // 调整 w9 提前量
        private BigDecimal adjW5 = BigDecimal.ZERO; // 调整 w5 提前量

        public BigDecimal getW13Rate(){
            return SoeCommonUtils.rate(w13,totalCore);
        }

        public BigDecimal getW9Rate(){
            return SoeCommonUtils.rate(w9,totalCore);
        }

        public BigDecimal getW5Rate(){
            return SoeCommonUtils.rate(w5,totalCore);
        }

        public BigDecimal getW0Rate(){
            return SoeCommonUtils.rate(w0,totalCore);
        }
    }
}
