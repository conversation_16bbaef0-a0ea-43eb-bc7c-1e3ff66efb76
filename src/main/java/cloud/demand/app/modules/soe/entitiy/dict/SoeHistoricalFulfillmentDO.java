package cloud.demand.app.modules.soe.entitiy.dict;

import cloud.demand.app.modules.soe.entitiy.IVersionBasTableDO;
import cloud.demand.app.modules.soe.entitiy.distribute.ICustomerInfo;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户历史履约情况
 */
@Data
@Table("bas_soe_historical_fulfillment")
public class SoeHistoricalFulfillmentDO implements ICustomerInfo, IVersionBasTableDO {
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    @ExcelIgnore
    private Integer id;

    /**
     * 产品，CVM
     */
    @Column("product_type")
    @ExcelIgnore
    private String productType;

    /**
     * 国家
     */
    @Column("country_name")
    @ExcelProperty(value = "国家", index = 0)
    private String countryName;

    /**
     * 客户简称
     */
    @Column("customer_short_name")
    @ExcelProperty(value = "客户简称", index = 1)
    private String customerShortName;

    /**
     * 历史履约率，单位：%
     */
    @Column("historical_fulfillment_rate")
    @ExcelProperty(value = "历史履约率(%)", index = 2)
    private BigDecimal historicalFulfillmentRate;

    /**
     * 是否自动录入
     */
    @Column("is_auto_input")
    @ExcelProperty(value = "是否自动录入", index = 3)
    private String isAutoInput;

    /**
     * 版本号
     */
    @Column("version")
    @ExcelIgnore
    private Long version;

    /**
     * 是否为默认版本，1：是，0：否
     */
    @Column("default_flag")
    @ExcelIgnore
    private String defaultFlag;

    /**
     * 创建的时间<br/>Column: [create_time]
     */
    @Column(value = "create_time", setTimeWhenInsert = true)
    @ExcelIgnore
    private Date createTime;

    /**
     * 创建用户<br/>Column: [create_user]
     */
    @Column(value = "create_user", insertValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()")
    @ExcelIgnore
    private String createUser;

}
