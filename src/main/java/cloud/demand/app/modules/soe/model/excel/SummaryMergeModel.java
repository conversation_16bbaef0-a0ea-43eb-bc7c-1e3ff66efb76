package cloud.demand.app.modules.soe.model.excel;

import cloud.demand.app.modules.soe.anno.ExcelSheet;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.enums.FlagType;
import lombok.Data;

import java.util.List;

/** 汇总的excel（合并） */
@Data
public class SummaryMergeModel {
    @ExcelSheet(value = "供应-采购",modelClass = SoePurchaseModel.class)
    private List<SoePurchaseModel> soePurchase;

    @ExcelSheet(value = "供应-采购@待执行",modelClass = SoePurchaseNotExecutedModel.class)
    private List<SoePurchaseNotExecutedModel> soeNotExecuted;

    @ExcelSheet(value = "需求-预测净增",modelClass = SoeForecastModel.class)
    private List<SoeForecastModel> soeForecast;

    @ExcelSheet(value = "当月实际净增",modelClass = SoeScaleModel.class)
    private List<SoeScaleModel> soeScaleNet;

    @ExcelSheet(value = "库存-期初库存",modelClass = SoeInventoryStartModel.class)
    private List<SoeInventoryStartModel> soeInventoryStart;

    @ExcelSheet(value = "库存-期末库存",modelClass = SoeInventoryEndModel.class)
    private List<SoeInventoryEndModel> soeInventoryEnd;

    @ExcelSheet(value = "端到端利用率@售卖规模",modelClass = SoeEndToEndModel.class)
    private List<SoeEndToEndModel> soeEndToEndScale;

    @ExcelSheet(value = "端到端利用率@物理总规模",modelClass = SoeEndToEndModel.class)
    private List<SoeEndToEndModel> soeEndToEndTotal;

    /**
     * excel导出部分sheet需要合并，这里做合并转换
     * 2、供应-采购@采购到货、供应-采购@采购（无货期）合并，通过“是否无货期字段区分”
     * 3、需求-预测净增@新增、需求-预测净增@退回 合并，通过“需求类型”字段区分
     * 4、库存-期末库存@安全库存 和 库存-期末库存@周转库存 合并，通过“库存类型”字段区分
     * @param summaryModel 合并前model
     * @return 合并后model
     */
    public static SummaryMergeModel transform(SummaryModel summaryModel){
        if (summaryModel == null){
            return null;
        }
        SummaryMergeModel summaryMergeModel = new SummaryMergeModel();
        // step1：合并采购
        List<SoePurchaseModel> soePurchase = summaryModel.getSoePurchase();
        List<SoePurchaseModel> soeRelocation = summaryModel.getSoeRelocation();
        summaryMergeModel.setSoePurchase(SoeCommonUtils.unionAll(soePurchase, soeRelocation,
                soePurchaseModel -> soePurchaseModel.setIsNoExpect(FlagType.NO.getDesc()),  // 有货期
                soePurchaseModel -> soePurchaseModel.setIsNoExpect(FlagType.YES.getDesc()))); // 无货期
        // step2：合并需求
        List<SoeForecastModel> soeForecastAdd = summaryModel.getSoeForecastAdd();
        List<SoeForecastModel> soeForecastReturn = summaryModel.getSoeForecastReturn();
        summaryMergeModel.setSoeForecast(SoeCommonUtils.unionAll(soeForecastAdd, soeForecastReturn,null, soeForecastModel -> {
            if (soeForecastModel.getCoreNum() != null){
                soeForecastModel.setCoreNum(soeForecastModel.getCoreNum().negate()); // 退回取反
            }
        }));
        // step3：合并期末
        List<SoeInventoryEndModel> soeInventoryEndSecurity = summaryModel.getSoeInventoryEndSecurity();
        List<SoeInventoryEndModel> soeInventoryEndTurnover = summaryModel.getSoeInventoryEndTurnover();
        summaryMergeModel.setSoeInventoryEnd(SoeCommonUtils.unionAll(soeInventoryEndSecurity, soeInventoryEndTurnover,
                soeInventoryEndModel -> soeInventoryEndModel.setInvType("安全库存"),
                soeInventoryEndModel -> soeInventoryEndModel.setInvType("周转库存")));
        // step4：补全其他无需合并数据
        summaryMergeModel.setSoeScaleNet(summaryModel.getSoeScaleNet()); // 规模-实际净增
        summaryMergeModel.setSoeNotExecuted(summaryModel.getSoeNotExecuted()); // 待执行
        summaryMergeModel.setSoeInventoryStart(summaryModel.getSoeInventoryStart()); // 期初
        summaryMergeModel.setSoeEndToEndScale(summaryModel.getSoeEndToEndScale()); // 端到端@售卖规模
        summaryMergeModel.setSoeEndToEndTotal(summaryModel.getSoeEndToEndTotal()); // 端到端@物理总规模
        return summaryMergeModel;
    }

}
