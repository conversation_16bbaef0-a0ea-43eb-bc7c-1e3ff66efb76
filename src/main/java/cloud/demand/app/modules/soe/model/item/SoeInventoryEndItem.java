package cloud.demand.app.modules.soe.model.item;

import cloud.demand.app.modules.soe.entitiy.SoeInventoryEndDO;
import cloud.demand.app.modules.soe.model.CommonItem;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SoeInventoryEndItem extends CommonItem{

    /** 库存来源之一：db数据 */
    public static SoeInventoryEndItem transform(SoeInventoryEndDO endDO){
        SoeInventoryEndItem ret = new SoeInventoryEndItem();
        ret.setProductType(endDO.getProductType());
        ret.setYearMonth(endDO.getYearMonth());
        ret.setYearWeek(endDO.getYearWeek());
        ret.setInstanceType(endDO.getInstanceType());
        ret.setCustomhouseTitle(endDO.getCustomhouseTitle());
        ret.setAreaName(endDO.getAreaName());
        ret.setRegionName(endDO.getRegionName());
        ret.setZoneName(endDO.getZoneName());
        ret.setCoreNum(endDO.getCoreNum());
        return ret;

    }

    /** 库存来源之一：模拟数据 */
    public static SoeInventoryEndItem transformMock(SoeMockItem mockItem) {
        SoeInventoryEndItem soeInventoryEndItem = new SoeInventoryEndItem();
        soeInventoryEndItem.setProductType(mockItem.getProductType());
        soeInventoryEndItem.setYearMonth(mockItem.getYearMonth());
        soeInventoryEndItem.setYearWeek(mockItem.getYearWeek());
        soeInventoryEndItem.setOriginInstanceType(mockItem.getOriginInstanceType());
        soeInventoryEndItem.setInstanceType(mockItem.getInstanceType());
        soeInventoryEndItem.setDeviceType(mockItem.getDeviceType());
        soeInventoryEndItem.setOriginGinFamily(mockItem.getOriginGinFamily());
        soeInventoryEndItem.setGinsFamily(mockItem.getGinsFamily());
        soeInventoryEndItem.setOriginGpuType(mockItem.getOriginGpuType());
        soeInventoryEndItem.setGpuType(mockItem.getGpuType());
        soeInventoryEndItem.setCustomhouseTitle(mockItem.getCustomhouseTitle());
        soeInventoryEndItem.setCountryName(mockItem.getCountryName());
        soeInventoryEndItem.setAreaName(mockItem.getAreaName());
        soeInventoryEndItem.setRegionName(mockItem.getRegionName());
        soeInventoryEndItem.setZoneName(mockItem.getZoneName());
        soeInventoryEndItem.setCoreNum(mockItem.getCoreNum());
        soeInventoryEndItem.setNum(mockItem.getNum());
        soeInventoryEndItem.setIndustryDept(mockItem.getIndustryDept());
        soeInventoryEndItem.setCustomerShortName(mockItem.getCustomerShortName());
        return soeInventoryEndItem;
    }
}
