package cloud.demand.app.modules.soe.entitiy.fio.view;

import cloud.demand.app.modules.soe.dto.fio.ReportFioResp;
import cloud.demand.app.modules.soe.entitiy.distribute.IForecastGroupKey;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import lombok.NoArgsConstructor;

/** 预测内外 */
@Data
@Table("dws_fio_forecast_inside_and_out_item_df")
public class ReportFioAnyDO implements IForecastGroupKey {
    @Column(value = "any_product_type",computed = "any(product_type)")
    private String productType;
    @Column(value = "any_product",computed = "any(product)")
    private String product;

    @Column(value = "any_year_month",computed = "any(year_month)")
    private String yearMonth;

    @Column(value = "any_mod_type",computed = "any(if(is_mod = 0,'干预前','干预后'))")
    private String modType;

    @Column(value = "any_industry_dept",computed = "any(industry_dept)")
    private String industryDept;
    @Column(value = "any_war_zone",computed = "any(war_zone)")
    private String warZone;
    @Column(value = "any_customer_short_name",computed = "any(customer_short_name)")
    private String customerShortName;
    @Column(value = "any_un_customer_short_name",computed = "any(un_customer_short_name)")
    private String unCustomerShortName;
    @Column(value = "any_customhouse_title",computed = "any(customhouse_title)")
    private String customhouseTitle;
    @Column(value = "any_country_name",computed = "any(country_name)")
    private String countryName;
    @Column(value = "any_region_name",computed = "any(region_name)")
    private String regionName;

    @Column(value = "any_un_instance_type",computed = "any(un_instance_type)")
    private String unInstanceType;

    @Column(value = "any_instance_type",computed = "any(instance_type)")
    private String instanceType;

    @Column(value = "sum_total_core",computed = "sum(total_core)")
    private BigDecimal totalCore;

    @Column(value = "sum_w13",computed = "sum(w13)")
    private BigDecimal w13;

    @Column(value = "sum_w9",computed = "sum(w9)")
    private BigDecimal w9;

    @Column(value = "sum_w5",computed = "sum(w5)")
    private BigDecimal w5;

    @Column(value = "sum_w0",computed = "sum(w0)")
    private BigDecimal w0;

    private BigDecimal baseW13; // w13 基准量
    private BigDecimal baseW9; // w9 基准量
    private BigDecimal baseW5; // w5 基准量

    private BigDecimal useBaseW13; // w13 基准量使用量
    private BigDecimal useBaseW9; // w9 基准量使用量
    private BigDecimal useBaseW5; // w5 基准量使用量

    private BigDecimal orderW13; // 原订单 w13 提前量（可以不占用基准）
    private BigDecimal orderW9; // 原订单 w9 提前量（可以不占用基准）
    private BigDecimal orderW5; // 原订单 w5 提前量（可以不占用基准）

    private BigDecimal adjW13; // 调整 w13 提前量
    private BigDecimal adjW9; // 调整 w9 提前量
    private BigDecimal adjW5; // 调整 w5 提前量

    private List<OrderItem> orderItems; // 订单按 diffWeek 区分的列表

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class OrderItem {
        private Integer diffWeek;
        private BigDecimal totalCore;
    }

    public static ReportFioResp.Item transform(ReportFioAnyDO anyDO, boolean isUnCustomerShortName,boolean isUnInstanceType) {
        ReportFioResp.Item item = new ReportFioResp.Item();
        item.setProductType(anyDO.getProductType());
        item.setProduct(anyDO.getProduct());
        item.setYearMonth(anyDO.getYearMonth());
        item.setModType(anyDO.getModType());
        item.setIndustryDept(anyDO.getIndustryDept());
        item.setWarZone(anyDO.getWarZone());
        item.setCustomerShortName(anyDO.getCustomerShortName());
        item.setUnCustomerShortName(anyDO.getUnCustomerShortName());
        // 如果是通用客户简称，则覆盖客户简称字段
        if (isUnCustomerShortName){
            item.setCustomerShortName(anyDO.getUnCustomerShortName());
        }
        item.setCustomhouseTitle(anyDO.getCustomhouseTitle());
        item.setCountryName(anyDO.getCountryName());
        item.setRegionName(anyDO.getRegionName());
        item.setUnInstanceType(anyDO.getUnInstanceType());
        item.setInstanceType(anyDO.getInstanceType());
        if (isUnInstanceType){
            item.setInstanceType(anyDO.getUnInstanceType());
        }
        item.setTotalCore(anyDO.getTotalCore());
        // 用来计算订单基准
        item.setW13(anyDO.getW13());
        item.setW9(anyDO.getW9());
        item.setW5(anyDO.getW5());
        item.setW0(anyDO.getW0());
        // 记录原始预测基准
        item.setBaseW13(anyDO.getBaseW13());
        item.setBaseW9(anyDO.getBaseW9());
        item.setBaseW5(anyDO.getBaseW5());

        item.setUseBaseW13(anyDO.getUseBaseW13());
        item.setUseBaseW9(anyDO.getUseBaseW9());
        item.setUseBaseW5(anyDO.getUseBaseW5());
        // 原订单提前量
        item.setOrderW13(anyDO.getOrderW13());
        item.setOrderW9(anyDO.getOrderW9());
        item.setOrderW5(anyDO.getOrderW5());
        // 由于diffWeek 的调整量
        item.setAdjW13(anyDO.getAdjW13());
        item.setAdjW9(anyDO.getAdjW9());
        item.setAdjW5(anyDO.getAdjW5());

        return item;
    }

    public static ReportFioAnyDO copy(ReportFioAnyDO anyDO){
        ReportFioAnyDO ret = new ReportFioAnyDO();
        ret.setProductType(anyDO.getProductType());
        ret.setProduct(anyDO.getProduct());
        ret.setYearMonth(anyDO.getYearMonth());
        ret.setModType(anyDO.getModType());
        ret.setIndustryDept(anyDO.getIndustryDept());
        ret.setWarZone(anyDO.getWarZone());
        ret.setCustomerShortName(anyDO.getCustomerShortName());
        ret.setUnCustomerShortName(anyDO.getUnCustomerShortName());
        ret.setCustomhouseTitle(anyDO.getCustomhouseTitle());
        ret.setCountryName(anyDO.getCountryName());
        ret.setRegionName(anyDO.getRegionName());
        ret.setInstanceType(anyDO.getInstanceType());
        ret.setUnInstanceType(anyDO.getUnInstanceType());
        ret.setTotalCore(anyDO.getTotalCore());
        ret.setW13(anyDO.getW13());
        ret.setW9(anyDO.getW9());
        ret.setW5(anyDO.getW5());
        ret.setW0(anyDO.getW0());
        ret.setBaseW13(anyDO.getBaseW13());
        ret.setBaseW9(anyDO.getBaseW9());
        ret.setBaseW5(anyDO.getBaseW5());
        ret.setUseBaseW13(anyDO.getUseBaseW13());
        ret.setUseBaseW9(anyDO.getUseBaseW9());
        ret.setUseBaseW5(anyDO.getUseBaseW5());
        ret.setOrderW13(anyDO.getOrderW13());
        ret.setOrderW9(anyDO.getOrderW9());
        ret.setOrderW5(anyDO.getOrderW5());
        ret.setAdjW13(anyDO.getAdjW13());
        ret.setAdjW9(anyDO.getAdjW9());
        ret.setAdjW5(anyDO.getAdjW5());
        return ret;
    }
}
