package cloud.demand.app.modules.soe.model;

import cloud.demand.app.modules.soe.dto.item.IExtObject;
import cloud.demand.app.modules.sop_util.process.model.IIndexData;
import cloud.demand.app.modules.sop_util.process.store.IDBData;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/** 报表数据结构 */
public interface IReportData extends IDBData, IIndexData {

    /** 字段集合的key */
    public Object[] getFieldValues_(List<String> fields);

    /** 获取字段集合 */
    public Map<String,Object> getFields_(List<String> fields);

    /** 值 */
    public BigDecimal getValue_();

    default Map<String, IExtObject> getExt(){
        return null;
    }
}
