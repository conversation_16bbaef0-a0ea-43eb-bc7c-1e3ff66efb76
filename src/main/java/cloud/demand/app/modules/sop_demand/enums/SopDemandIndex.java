package cloud.demand.app.modules.sop_demand.enums;

import cloud.demand.app.modules.sop_return.frame.group.interfaces.ISopIndex;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum SopDemandIndex implements ISopIndex {

    DEMAND_EXECUTED("DEMAND_EXECUTED", "已执行量"),
    DEMAND_NOT_EXECUTED("DEMAND_NOT_EXECUTED", "未执行量"),
    DEMAND_NOT_HEDG("DEMAND_NOT_HEDG", "不参与对冲"),
    DEMAND_EXPIRED("DEMAND_EXPIRED", "已过期"),
    DEMAND_TOTAL("DEMAND_TOTAL", "需求总流水"),
    DEMAND_IS_HEDGE("DEMAND_IS_HEDGE", "云运管参与对冲"),
    DEMAND_HAS_HEDGE("DEMAND_HAS_HEDGE", "云运管有效对冲"),
    DEMAND_PROCUREMENT("DEMAND_PROCUREMENT", "ERP采购下单量"),
    ;


    /**
     * @param index 指标名称
     * @return 分组
     */
    public static int getIndexGroup(String index,boolean isYuntiData) {
        if (Objects.equals(DEMAND_TOTAL.name, index)) {
            return isYuntiData?1:0;
        }
        return -1;
    }

    /**
     * DEMAND_TOTAL为统计指标，不在数据库存储指标范围内，所有不纳入分组
     * @return 排除 DEMAND_TOTAL 后的指标集合
     */
    public static ISopIndex[] getISopIndex(){
        return SopDemandIndex.values();
    }

    private final String name;
    private final String desc;
}
