package cloud.demand.app.modules.sop_demand.model.req.resource;

import cloud.demand.app.modules.sop.domain.report.ISimpleHasYuntiVersion;
import cloud.demand.app.modules.sop.domain.report.ISimpleResourceReq;
import cloud.demand.app.modules.sop_demand.model.req.QueryDemandDimReq;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/** v2-维度数据分布请求体 */
@Data
public class QueryDemandDimResourceReq implements ISimpleResourceReq {

    /** cvm的起始时间 */
    @NotBlank(message = "CVM 版本不能为空")
    private String versionCvm;


    /** 物理机的起始时间 */
    @NotBlank(message = "物理机 版本不能为空")
    private String versionDevice;

    /** 通用查询类 */
    private QueryDemandDimReq commonReq;

}
