package cloud.demand.app.modules.sop_demand.service;

import cloud.demand.app.modules.sop_demand.entity.other.SopDemandVersionChangeResultDO;
import cloud.demand.app.modules.sop_demand.model.req.CreateDemandChangeResultReq;
import cloud.demand.app.modules.sop_demand.model.req.QueryDemandChangeResultReq;
import cloud.demand.app.modules.sop_demand.model.req.QueryDemandDimReq;
import cloud.demand.app.modules.sop_demand.model.req.QueryDemandReportReq;
import cloud.demand.app.modules.sop_demand.model.req.QueryDemandTrendReq;
import cloud.demand.app.modules.sop_demand.model.req.UpdateDemandChangeResultReq;
import cloud.demand.app.modules.sop_demand.model.req.resource.QueryDemandDimResourceReq;
import cloud.demand.app.modules.sop_demand.model.req.resource.QueryDemandParamsResourceReq;
import cloud.demand.app.modules.sop_demand.model.req.resource.QueryDemandReportResourceReq;
import cloud.demand.app.modules.sop_demand.model.req.resource.QueryDemandTrendResourceReq;
import cloud.demand.app.modules.sop_demand.model.resp.QueryDemandDimResp;
import cloud.demand.app.modules.sop_demand.model.resp.QueryDemandReportItemResp;
import cloud.demand.app.modules.sop_demand.model.resp.QueryDemandTrendResp;
import cloud.demand.app.modules.sop_demand.web.SopDemandJxcController;

import java.util.List;

public interface SopDemandJxcService {

    /**
     * 查询需求报表下拉框
     *
     * @param req 请求参数
     * @return 返回查询下拉框结果
     */
    List<String> queryParams(SopDemandJxcController.QueryDemandColumnReq req);

    /**
     * 查询报表信息，按dim维度分组，统计台数，核数和容量
     *
     * @param req 请求参数
     * @return 报表消息体
     */
    QueryDemandReportItemResp queryReportItem(QueryDemandReportReq req);

    /**
     * 查询需求变化趋势
     *
     * @param req 请求参数
     * @return
     */
    QueryDemandTrendResp queryTrend(QueryDemandTrendReq req);

    /**
     * 查询需求维度分组统计数据
     *
     * @param req 请求参数
     */
    QueryDemandDimResp queryDim(QueryDemandDimReq req);

    /**
     * 查询sop需求版本变化原因
     *
     * @param req 请求参数
     * @return 返回的原因组
     */
    List<SopDemandVersionChangeResultDO> queryChangeResult(QueryDemandChangeResultReq req);

    /**
     * 创建sop需求版本变化原因
     *
     * @param req 请求参数
     */
    void createSopDemandResult(CreateDemandChangeResultReq req);

    /**
     * 更新sop需求版本变化原因
     *
     * @param req 请求参数
     */
    void updateSopDemandResult(UpdateDemandChangeResultReq req);

    /**
     * 通过id主键组删除sop需求版本变化原因
     *
     * @param ids 待删除id组
     */
    void deleteSopDemandResult(List<Long> ids);

    // =============== v2版本（cvm和物理机分开查询） ================
    /** v2-查询趋势按cvm和物理机分开查 */
    QueryDemandTrendResp queryTrendByResource(QueryDemandTrendResourceReq req);

    /** v2-查询指标分布 */
    QueryDemandDimResp queryDimByResource(QueryDemandDimResourceReq req);

    /** v2-查询需求明细报表按cvm和物理机分开查 */
    QueryDemandReportItemResp queryReportItemByResource(QueryDemandReportResourceReq req);

    /** v2-查询参数 */
    List<String> queryParamsByResource(QueryDemandParamsResourceReq req);

}
