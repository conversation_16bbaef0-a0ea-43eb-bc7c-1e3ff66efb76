package cloud.demand.app.modules.sop_demand.entity.report;

import cloud.demand.app.modules.sop.entity.IVersionDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Table("ads_sop_yunti_report_mif")
public class AdsSopYuntiDemandTrendSumDO extends AdsSopDemandTrendSumDO {
    public static AdsSopDemandTrendSumDO transform(AdsSopYuntiDemandTrendSumDO sumDO){
        AdsSopDemandTrendSumDO ret = new AdsSopDemandTrendSumDO();
        ret.setVersion(sumDO.getVersion());
        ret.setIndexYearMonth(sumDO.getIndexYearMonth());
        ret.setSumNum(sumDO.getSumNum());
        ret.setSumCoreNum(sumDO.getSumCoreNum());
        ret.setSumCapacity(sumDO.getSumCapacity());
        return ret;
    }
}
