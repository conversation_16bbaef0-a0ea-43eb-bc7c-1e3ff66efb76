package cloud.demand.app.modules.sop_demand.entity.other;

import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportGroupBy;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("dws_sop_demand_report_mif")
public class SopDemandDwsClean {

    /**
     * 资源类型<br/>Column: [res_type]
     */
    @SopReportGroupBy
    @Column(value = "res_type")
    private String resType;

    /**
     * 规划产品名<br/>Column: [plan_product_name]
     */
    @SopReportGroupBy
    @Column(value = "plan_product_name")
    private String planProductName;

    /**
     * 物理机设备类型<br/>Column: [phy_device_type]
     */
    @SopReportGroupBy
    @Column(value = "phy_device_type")
    private String phyDeviceType;

    /**
     * cvm 实例规格<br/>Column: [cvm_gins_type]
     */
    @SopReportGroupBy
    @Column(value = "cvm_gins_type")
    private String cvmGinsType;

}
