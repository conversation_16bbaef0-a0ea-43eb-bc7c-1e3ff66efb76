package cloud.demand.app.modules.return_report.model;

import lombok.Data;

@Data
public class ReturnFilterValueReq {

    /**
     * 取值为：
     * 大类：demandCategory1
     * 产品大类：demandCategory2
     * 规划产品：planProductName
     * 云退回类型：cloudReturnReasonType
     * 地域类型：areaType
     * 设备类型：deviceType
     * 机型代次：generationType
     */
    private String type;

    /**当有选择大类时，传这个值，用于级联查询*/
    private String demandCategory1;

}
