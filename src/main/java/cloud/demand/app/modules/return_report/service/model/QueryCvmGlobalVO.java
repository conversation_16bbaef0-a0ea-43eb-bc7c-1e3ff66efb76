package cloud.demand.app.modules.return_report.service.model;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用于查询cvm退回汇总信息的vo
 */
@Data
@Table("ads_erp_cvm_return_report")
public class QueryCvmGlobalVO {

    @Column("indicator_code")
    private String indicatorCode;

    @Column(value = "sumCores", computed = "sum(logic_cpu_core)")
    private BigDecimal sumCores;

}
