package cloud.demand.app.modules.return_report.model;

import cloud.demand.app.entity.ck_cloud_demand.AdsErpCvmReturnReportDO;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 未退回计划明细
 */
@Data
public class CvmReturnPlanDetailResp {

    private List<Item> data;

    @Data
    public static class Item {
        /**预计退回日期*/
        private LocalDate planReturnDate;
        /**事业群*/
        private String bgName;
        /**部门*/
        private String deptName;
        /**规划产品*/
        private String planProductName;
        /**项目类型*/
        private String project;
        /**国家*/
        private String country;
        /**区域*/
        private String region;
        /**城市*/
        private String city;
        /**可用区*/
        private String zone;
        /**核心类型*/
        private String coreType;
        /**资源池类型*/
        private String pool;
        /**机型族*/
        private String instanceFamily;
        /**实例类型(中文)*/
        private String instanceTypeName;
        /**实例规格*/
        private String instanceModel;
        /**机型代次*/
        private String generationType;
        /**待退回核心数*/
        private Integer planToReturnCores;
    }

    public static CvmReturnPlanDetailResp from(List<AdsErpCvmReturnReportDO> all) {
        CvmReturnPlanDetailResp resp = new CvmReturnPlanDetailResp();
        resp.setData(ListUtils.transform(all, o -> from(o)));
        return resp;
    }

    private static Item from(AdsErpCvmReturnReportDO d) {
        Item item = new Item();
        item.setPlanReturnDate(d.getReturnDate());
        item.setBgName(d.getBgName());
        item.setDeptName(d.getDeptName());
        item.setPlanProductName(d.getPlanProductName());
        item.setProject(d.getProject());
        item.setCountry(d.getCountry());
        item.setRegion(d.getRegion());
        item.setCity(d.getCity());
        item.setZone(d.getZone());
        item.setCoreType(d.getCoreType());
        item.setPool(d.getPool());
        item.setInstanceFamily(d.getInstanceFamily());
        item.setInstanceTypeName(d.getInstanceTypeName());
        item.setInstanceModel(d.getInstanceModel());
        item.setGenerationType(d.getGenerationType());
        item.setPlanToReturnCores(d.getLogicCpuCore());
        return item;
    }

}
