package cloud.demand.app.modules.sop_util.dto.supply;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class InventoryRespData {

    @JsonProperty("device_type")
    private String name;
    @JsonProperty("device_family_class")
    private String klass;
    private String unit;
    @JsonProperty("m1_num")
    private BigDecimal num;

    public String key() {
        return String.join("@", klass, name);
    }
}
