package cloud.demand.app.modules.sop_util.process.service;

import cloud.demand.app.modules.sop_util.dto.supply.InventoryRespData;
import cloud.demand.app.modules.sop_util.model.other.InventorySupplyReq;
import cloud.demand.app.modules.sop_util.process.store.item.InventoryItem;

import java.util.List;
import java.util.Map;

/**
 * 库存底层数据
 */
public interface InventoryService {

    /**
     * 获取未分摊的 M1 库存数据（只有公有云 CVM 使用的那些机型库存数据）
     *
     * @param version
     * @return
     */
    Map<String, InventoryRespData> getNotSpiltInventoryM1Data(String version);


    List<InventoryItem> getData(InventorySupplyReq req);

}
