package cloud.demand.app.modules.sop_util.process.utils.step;

import cloud.demand.app.modules.sop.enums.Constant;
import cloud.demand.app.modules.sop.enums.ResourceType;
import cloud.demand.app.modules.sop.util.DataTransformUtil;
import cloud.demand.app.modules.sop_util.domain.item.ReportIndexItem;
import cloud.demand.app.modules.sop_util.domain.item.ReportItem;
import cloud.demand.app.modules.sop_util.domain.supply.ReportSupplyReq;
import cloud.demand.app.modules.sop_util.entity.ObsBudgetRollAdjustCvmAppendDeviceDO;
import cloud.demand.app.modules.sop_util.model.demand.SopDemandReturnReq;
import cloud.demand.app.modules.sop_util.model.supply.VersionInfo;
import cloud.demand.app.modules.sop_util.process.step.QueryStep;
import cloud.demand.app.modules.sop_util.process.store.item.CommonItem;
import cloud.demand.app.modules.sop_util.process.store.item.SopDemandReturnApportionItem;
import cloud.demand.app.modules.sop_util.process.store.item.SopDemandReturnItem;
import cloud.demand.app.modules.sop_util.process.store.item.SopDemandReturnSupplyItem;
import cloud.demand.app.modules.sop_util.process.utils.StepUtils;
import cloud.demand.app.modules.sop_util.utils.CommonUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Year;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cloud.demand.app.modules.sop_util.utils.CommonUtils.toChildren;
import static cloud.demand.app.modules.sop_util.utils.CommonUtils.toParent;

@Slf4j
public class SupplyUtils {

    public QueryStep buildQueryStep(String stepName, String storeName,String depStep) {
        QueryStep queryStep = new QueryStep(
                stepName,
                stepName,
                storeName,
                (map, stringListMap) -> toChildren(stringListMap.get(stepName)),
                (map,idbData) -> {
                    SopDemandReturnReq.TimePeriod time = StepUtils.getTime(map);
                    List<ReportIndexItem> ret = ReportIndexItem.transform(toChildren(idbData), stepName);
                    ret.forEach(item -> ReportItem.fillData(item.getData(),time));
                    return toParent(ret);
                },
                null);
        if (StringUtils.isNotBlank(depStep)){
            queryStep.setDependence(ListUtils.newList(depStep));
        }
        // 异步查询
        queryStep.setAsync(true);
        return queryStep;
    }


    public QueryStep buildRate(String stepName, String step1, String step2) {
        BiFunction<ReportItem, ReportItem, ReportItem> compareFunc = StepUtils.getRateFun();
        Function<ReportItem, ReportItem> rightNullFunc = item -> {
            ReportItem copy = ReportItem.copy(item);
            copy.setNumber(null);
            copy.setValue(CommonUtils.getValue(null));
            return copy;
        };
        Function<ReportItem, ReportItem> leftNullFunc = item -> {
            ReportItem copy = ReportItem.copy(item);
            copy.setNumber(null);
            copy.setValue(CommonUtils.getValue(null));
            return copy;
        };

        return StepUtils.buildCompare(stepName, step1, step2, compareFunc, rightNullFunc, leftNullFunc);
    }

    public QueryStep buildDemandStep(String stepName, String depName) {
        QueryStep queryStep = new QueryStep(
                stepName,
                null,
                (map, stringListMap) -> {
                    SopDemandReturnReq.TimePeriod timePeriod = StepUtils.getTime(map);

                    List<SopDemandReturnItem> items = StepUtils.demandTransform((ReportSupplyReq) map.get("req"),
                            (List<String>) map.get("dim"),
                            toChildren(stringListMap.get(depName)),
                            YearMonth.parse(timePeriod.getStart()),
                            12);
                    List<ReportIndexItem> ret = ReportIndexItem.transform(toParent(items), stepName);
                    ret.forEach(item -> ReportItem.fillData(item.getData(),timePeriod));
                    return toParent(ret);
                },
                idbData -> null,
                null);
        queryStep.setDependence(ListUtils.newList(depName));
        return queryStep;
    }



    /** 构建初始化步骤 */
    public QueryStep buildStep0(String stepName, String storeName,Map<String,Object> stepParams) {
        QueryStep queryStep = new QueryStep(
                stepName,
                storeName,
                (map, stringListMap) -> toChildren(stringListMap.get(stepName)),
                idbData -> toParent(toChildren(idbData)),
                stepParams);
        // 不展示数据，所以不用合并到返回值中
        queryStep.setMerge((iQueryData, iQueryData2) -> {});
        return queryStep;
    }

    /** 构建分摊步骤 */
    public QueryStep buildApportionStep(String stepName, String depStep) {
        QueryStep queryStep = new QueryStep(
                stepName,
                null,
                (map, stringListMap) -> {
                    // 只处理分摊系数，存到map，不做返回值
                    ReportSupplyReq req = (ReportSupplyReq)map.get("req");
                    boolean computeGap = BooleanUtils.isTrue(req.getComputeGap());
                    VersionInfo info = (VersionInfo) map.get("info");
                    dealApportion((Map<String, SopDemandReturnApportionItem>)map.get("apportion_map"),
                            ((Map<String, ObsBudgetRollAdjustCvmAppendDeviceDO>) map.get("cvm_2_device")),
                            (Map<String,Map<String,BigDecimal>>)map.get("lose_data_map"),
                            computeGap?(List<SopDemandReturnSupplyItem>)map.get("demand_data"):null,
                            info,
                            toChildren(stringListMap.get(depStep)));
                    return null;
                },
                idbData -> null,
                null);
        // 不展示数据，所以不用合并到返回值中
        queryStep.setMerge((iQueryData, iQueryData2) -> {});
        // 依赖step0的需求数据
        queryStep.setDependence(ListUtils.newList(depStep));
        return queryStep;
    }

    /**
     * @param apportionMap 分摊map
     * @param cvm2Device cvm转物理机mapping
     * @param items 需求数据
     */
    private void dealApportion(Map<String, SopDemandReturnApportionItem> apportionMap,
                               Map<String, ObsBudgetRollAdjustCvmAppendDeviceDO> cvm2Device,
                               Map<String,Map<String,BigDecimal>> loseDataMap,
                               List<SopDemandReturnSupplyItem> demandData,
                               VersionInfo info,
                               List<SopDemandReturnSupplyItem> items) {
        Map<String,BigDecimal> loseData = new HashMap<>();
        for (SopDemandReturnSupplyItem item : items) {
            if (Objects.equals(item.getResType(), ResourceType.CVM.getName())){
                ObsBudgetRollAdjustCvmAppendDeviceDO mapping = cvm2Device.get(item.getCvmGinsType());
                if (mapping!=null){
                    item.setPhyDeviceType(mapping.getToType());
                    // CVM分摊核数 = 核数 * 转换比率 / cvm逻辑核心 * 物理机逻辑核心
                    item.setApportionCoreNum(item.getCoreNum().multiply(mapping.getTransferRatio()));
                }else if (Objects.equals(item.getCvmGinsType(),Constant.EMPTY_VALUE_STR) && DataTransformUtil.isNotBlank(item.getPhyDeviceType())){
                    // 空值处理且有设备类型 按 1：1转换
                    item.setApportionCoreNum(item.getCoreNum());
                }else {
                    loseData.put(item.getCvmGinsType(),
                            loseData.getOrDefault(item.getCvmGinsType(),BigDecimal.ZERO).add(item.getCoreNum()));
                }
            }else if (item.getCoreNum().compareTo(BigDecimal.ZERO)>0){
                // 物理机的分摊比率默认为 1 （即取核数）
                item.setApportionCoreNum(item.getCoreNum());
            }
        }

        if (demandData!=null) {
            SopDemandReturnReq.TimePeriod timePeriod = SopDemandReturnReq.TimePeriod.rollingOneYear(info.getDeviceYearMonth());
            String end = timePeriod.getEnd();
            items.forEach((item)->{
                String yearMonth = item.getYearMonth_();
                if (yearMonth.compareTo(end)<=0){
                    demandData.add(item);
                }
            });
        }

        if (ListUtils.isNotEmpty(loseData)){
            if (loseDataMap!=null){
                loseDataMap.put("cvm折算物理机丢失",loseData);
            }else {
                log.error(String.format("数据丢失：cvm折算物理机丢失,数量：【%s】,明细：【%s】", loseData.size(), JSON.toJson(loseData)));
            }
        }

        // 分组处理，年月+设备类型
        Map<String, List<SopDemandReturnSupplyItem>> groupByMap = ListUtils.groupBy(items,
                SopDemandReturnSupplyItem::getPhyDeviceType);

        // 清空
        apportionMap.clear();

        // 转换为分摊集合
        for (Map.Entry<String, List<SopDemandReturnSupplyItem>> entry : groupByMap.entrySet()) {
            String k = entry.getKey();
            List<SopDemandReturnSupplyItem> v = entry.getValue();
            SopDemandReturnApportionItem value = new SopDemandReturnApportionItem();
            SopDemandReturnSupplyItem supplyItem = v.get(0);
            value.setDeviceType(supplyItem.getPhyDeviceType());
            Map<String,BigDecimal> apportionRateMap = new HashMap<>();
            AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
            v.forEach(item->{
                BigDecimal apportionCoreNum = item.getApportionCoreNum();
                String utilBusinessType = item.getUtilBusinessType();
                // 只处理大于0的需求数据
                if (apportionCoreNum != null && apportionCoreNum.compareTo(BigDecimal.ZERO) > 0){
                    BigDecimal add = ObjectUtils.defaultIfNull(apportionRateMap.get(utilBusinessType), BigDecimal.ZERO).
                            add(apportionCoreNum);
                    apportionRateMap.put(utilBusinessType, add);
                    total.set(total.get().add(apportionCoreNum));
                }
            });
            // 总计为0的不处理，后续匹配过程中归到丢失集合中
            BigDecimal totalCoreNum = total.get();
            if (totalCoreNum.compareTo(BigDecimal.ZERO) > 0){
                // 修改为占比
                Map<String,BigDecimal> newApportionRateMap = new HashMap<>();
                apportionRateMap.forEach((rateK,rateV)-> newApportionRateMap.put(rateK,rateV.divide(totalCoreNum,6, RoundingMode.HALF_UP)));
                value.setApportionMap(newApportionRateMap);
                value.setTotalCoreNum(totalCoreNum);
                apportionMap.put(k, value);
            }
        }
    }
}
