package cloud.demand.app.modules.sop_util.process.store.item;

import cloud.demand.app.modules.sop_util.entity.GapSupplyDO;
import com.pugwoo.wooutils.collect.MapUtils;
import lombok.Data;

import java.util.Map;

/** 缺口 */
@Data
public class GapItem extends CommonDimItem{

    public static GapItem transform(GapSupplyDO gapSupplyDO) {
        GapItem ret = new GapItem();
        ret.setUtilBusinessType(gapSupplyDO.getUtilBusinessType());
        ret.setSopBigFamily(gapSupplyDO.getSopBigFamily());
        ret.setYearMonth(gapSupplyDO.getYearMonth());
        ret.setCoreNum(gapSupplyDO.getCoreNum());
        ret.setDeviceType(ret.getDeviceType());
        return ret;
    }

    public static GapItem copy(GapItem datum) {
        GapItem gapItem = new GapItem();
        gapItem.setDeviceType(datum.getDeviceType());
        gapItem.setUtilBusinessType(datum.getUtilBusinessType());
        gapItem.setSopBigFamily(datum.getSopBigFamily());
        gapItem.setYearMonth(datum.getYearMonth());
        gapItem.setCoreNum(datum.getCoreNum());
        return gapItem;
    }
}
