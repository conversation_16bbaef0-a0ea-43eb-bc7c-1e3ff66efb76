package cloud.demand.app.modules.sop_util.web;


import cloud.demand.app.modules.sop_util.domain.baffle.req.GapBaffleReq;
import cloud.demand.app.modules.sop_util.domain.baffle.req.InventoryBaffleReq;
import cloud.demand.app.modules.sop_util.domain.baffle.req.SupplyBaffleReq;
import cloud.demand.app.modules.sop_util.domain.baffle.resp.GapRespItem;
import cloud.demand.app.modules.sop_util.domain.baffle.resp.InventoryRespItem;
import cloud.demand.app.modules.sop_util.domain.baffle.resp.SupplyRespItem;
import cloud.demand.app.modules.sop_util.service.SopUtilBaffleService;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/** sop工具挡板-提供库存那边供应，库存和缺口数据 */
@JsonrpcController("/sop/util/baffle")
public class SopUtilBaffleController {

    /** 挡板 */
    @Resource
    private SopUtilBaffleService baffleService;


    /** 供应数据 */
    @RequestMapping
    public List<SupplyRespItem> getSupplyData(@JsonrpcParam @Valid SupplyBaffleReq req){
        return baffleService.getSupplyData(req);
    }

    /** 库存数据 */
    @RequestMapping
    public List<InventoryRespItem> getInventoryData(@JsonrpcParam @Valid InventoryBaffleReq req){
        return baffleService.getInventoryData(req);
    }

    /** 缺口数据 */
    @RequestMapping
    public List<GapRespItem> getGapData(@JsonrpcParam @Valid GapBaffleReq req){
        return baffleService.getGapData(req);
    }
}
