package cloud.demand.app.modules.sop_util.process;

import cloud.demand.app.modules.sop_util.enums.QueryProcessEnum;
import cloud.demand.app.modules.sop_util.enums.StoreNameEnum;
import cloud.demand.app.modules.sop_util.enums.demnad.DemandDetailIndexEnum;
import cloud.demand.app.modules.sop_util.enums.demnad.DemandOverviewIndexEnum;
import cloud.demand.app.modules.sop_util.enums.supply.SupplyIndexEnum;
import cloud.demand.app.modules.sop_util.model.supply.VersionInfo;
import cloud.demand.app.modules.sop_util.process.step.QueryStep;
import cloud.demand.app.modules.sop_util.process.store.item.SupplyItem;
import cloud.demand.app.modules.sop_util.process.utils.StepUtils;
import cloud.demand.app.modules.sop_util.process.utils.step.*;
import cloud.demand.app.modules.sop_util.service.SopUtilCommonService;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/** 查询进程构建工厂 */
@Component("soeQueryProcessFactory")
public class QueryProcessFactory {

    private Map<String,QueryProcess> processMap;

    /** 需求复盘总览工具 */
    private final ScaleDemandUtils demandUtils = new ScaleDemandUtils();

    /** 需求复盘明细工具 */
    private final ScaleDemandDetailUtils demandDetailUtils = new ScaleDemandDetailUtils();

    /** 供应复盘总览工具类 */
    private final SupplyUtils supplyUtils = new SupplyUtils();

    /** 供应复盘明细工具类 */
    private final SupplyDetailUtils supplyDetailUtils = new SupplyDetailUtils();

    /** 供应复盘-库存周转工具类 */
    private final InventoryDetailUtils inventoryDetailUtils = new InventoryDetailUtils();

    /** 仓库 */
    @Resource
    private StoreFactory storeFactory;

    /** 公共 */
    @Resource
    private SopUtilCommonService commonService;

    @PostConstruct
    public void init(){
        processMap = new HashMap<>();
        register();
    }

    /** 通过注册的进程名获取查询进程 */
    public QueryProcess getProcess(String name){
        return processMap.get(name);
    }

    /** 注册查询流程 */
    private void register(){
        // 客户需求计划复盘总览
        processMap.put(QueryProcessEnum.Scale_Demand.getName(),registerScaleDemand());
        // 客户需求计划复盘明细
        processMap.put(QueryProcessEnum.Scale_Demand_Detail.getName(),registerScaleDemandDetail());
        // 客户需求计划复盘-未来12个月预估趋势
        processMap.put(QueryProcessEnum.Scale_Demand_Trend.getName(),registerScaleDemandTrend());
        // 客户需求计划复盘-维度分组
        processMap.put(QueryProcessEnum.Scale_Demand_Dim.getName(),registerScaleDemandDim());

        // 供应复盘总览
        processMap.put(QueryProcessEnum.Supply.getName(),registerSupply());
        // 供应复盘明细
        processMap.put(QueryProcessEnum.Supply_Detail.getName(),registerSupplyDetail());
        // 供应复盘-库存周转
        processMap.put(QueryProcessEnum.Supply_Inventory_Turnover.getName(), registerInventoryTurnover());
        // 供应复盘-库存健康
        processMap.put(QueryProcessEnum.Supply_Inventory_Health.getName(), registerInventoryHealth());

        // =============== 监控 ================

        // 分摊丢失的和CVM折算丢失的数据
        processMap.put(QueryProcessEnum.Monitor_Lose_Data.getName(),registerMonitorLoseData());

        // =============== 补充，不提供页面展示，方便查原因 ================

        // 分摊系数
        processMap.put(QueryProcessEnum.Apportion_Rate_Data.getName(), registerApportionRate());
    }



    /** 客户需求计划复盘-维度分组 */
    private QueryProcess registerScaleDemandDim() {
        QueryProcessBuilder builder = initBuilder();
        // 执行前提：demandType = 'SCALE'
        // step 1-1：Y24 大盘规模存量
        builder.addStep(demandUtils.buildTrendStep(DemandDetailIndexEnum.INDEX_1_1,StoreNameEnum.totalScale.getName(),true));
        // 执行前提：demandType in ('ADD','RET','ADD_RET')
        // step 2-1：Y24 净增
        builder.addStep(demandUtils.buildTrendStep(DemandDetailIndexEnum.INDEX_2_1,StoreNameEnum.demandReturn.getName(),false));
        return builder.build();
    }

    // =============== 客户需求计划复盘视图 ================

    /** 客户需求计划复盘-未来12个月预估趋势 */
    private QueryProcess registerScaleDemandTrend(){
        QueryProcessBuilder builder = initBuilder();
        // 执行前提：demandType = 'SCALE'
        // step 1-1：Y24 大盘规模存量
        builder.addStep(demandUtils.buildTrendStep(DemandDetailIndexEnum.INDEX_1_1,StoreNameEnum.totalScale.getName(),true));
        // step 1-2：Y24 大盘规模（上一轮）
        builder.addStep(demandUtils.buildTrendStep(DemandDetailIndexEnum.INDEX_1_2,StoreNameEnum.totalScale.getName(), MapUtils.of("isLast",true),true));
        // step 1-3：Y23 实际大盘规模
        builder.addStep(demandUtils.buildTrendStep(DemandDetailIndexEnum.INDEX_1_3,StoreNameEnum.totalScale.getName(), MapUtils.of("isLastYear",true),true));
        // step 1-4: Y24 预算
        builder.addStep(demandUtils.buildTrendStep(DemandDetailIndexEnum.INDEX_1_4,StoreNameEnum.budget.getName(),true));

        // 执行前提：demandType in ('ADD','RET','ADD_RET')
        // step 2-1：Y24 净增
        builder.addStep(demandUtils.buildTrendStep(DemandDetailIndexEnum.INDEX_2_1,StoreNameEnum.demandReturn.getName(),false));
        // step 2-2：Y24 净增 （上一轮）
        builder.addStep(demandUtils.buildTrendStep(DemandDetailIndexEnum.INDEX_2_2,StoreNameEnum.demandReturn.getName(), MapUtils.of("isLast",true),false));
        // step 2-3：Y23 实际净增
        builder.addStep(demandUtils.buildTrendStep(DemandDetailIndexEnum.INDEX_2_3,StoreNameEnum.demandReturn.getName(), MapUtils.of("isLastYear",true),false));
        // step 2-4: Y24 净增预算
        QueryStep step = demandUtils.buildTrendStep(DemandDetailIndexEnum.INDEX_2_4, StoreNameEnum.budgetAdd.getName(), false);
        // 跳过净增预算，不处理
        step.setSkipFunc(map -> true);
        builder.addStep(step);
       return builder.build();
    }

    /** 客户需求计划复盘明细 */
    private QueryProcess registerScaleDemandDetail() {
        QueryProcessBuilder builder = initBuilder();
        // 执行前提：demandType = 'SCALE'
        // step 1-1：Y24 大盘规模存量
        builder.addStep(demandDetailUtils.buildScaleStep(DemandDetailIndexEnum.INDEX_1_1,StoreNameEnum.totalScale.getName()));
        // step 1-2：Y24 大盘规模（上一轮）
        builder.addStep(demandDetailUtils.buildScaleStep(DemandDetailIndexEnum.INDEX_1_2,StoreNameEnum.totalScale.getName(), MapUtils.of("isLast",true)));
        // step 1-3：Y23 实际大盘规模
        builder.addStep(demandDetailUtils.buildScaleStep(DemandDetailIndexEnum.INDEX_1_3,StoreNameEnum.totalScale.getName(), MapUtils.of("isLastYear",true)));
        // step 1-4: Y24 预算
        builder.addStep(demandDetailUtils.buildScaleStep(DemandDetailIndexEnum.INDEX_1_4,StoreNameEnum.budget.getName()));
        // step 1-5：总规模Δ VS Y24 上一轮
        builder.addStep(demandDetailUtils.buildScaleSubtract(DemandDetailIndexEnum.INDEX_1_5,DemandDetailIndexEnum.INDEX_1_1,DemandDetailIndexEnum.INDEX_1_2));
        // step 1-6：总规模增长% Y24/Y23
        builder.addStep(demandDetailUtils.buildScaleDivide(DemandDetailIndexEnum.INDEX_1_6,DemandDetailIndexEnum.INDEX_1_1,DemandDetailIndexEnum.INDEX_1_3));
        // step 1-7：总规模Δ VS Y24 预算
        builder.addStep(demandDetailUtils.buildScaleSubtract(DemandDetailIndexEnum.INDEX_1_7,DemandDetailIndexEnum.INDEX_1_1,DemandDetailIndexEnum.INDEX_1_4));

        // 执行前提：demandType in ('ADD','RET','ADD_RET')
        // step 2-1：Y24 净增
        builder.addStep(demandDetailUtils.buildDemandStep(DemandDetailIndexEnum.INDEX_2_1,StoreNameEnum.demandReturn.getName()));
        // step 2-2：Y24 净增 （上一轮）
        builder.addStep(demandDetailUtils.buildDemandStep(DemandDetailIndexEnum.INDEX_2_2,StoreNameEnum.demandReturn.getName(), MapUtils.of("isLast",true)));
        // step 2-3：Y23 实际净增
        builder.addStep(demandDetailUtils.buildDemandStep(DemandDetailIndexEnum.INDEX_2_3,StoreNameEnum.demandReturn.getName(), MapUtils.of("isLastYear",true)));
        // step 2-4: Y24 净增预算
        builder.addStep(demandDetailUtils.buildDemandStep(DemandDetailIndexEnum.INDEX_2_4,StoreNameEnum.budgetAdd.getName()));
        // step 2-5：净增 VS Y24 上一轮
        builder.addStep(demandDetailUtils.buildDemandSubtract(DemandDetailIndexEnum.INDEX_2_5,DemandDetailIndexEnum.INDEX_2_1,DemandDetailIndexEnum.INDEX_2_2));
        // step 2-6：净增增长% Y24/Y23
        builder.addStep(demandDetailUtils.buildDemandDivide(DemandDetailIndexEnum.INDEX_2_6,DemandDetailIndexEnum.INDEX_2_1,DemandDetailIndexEnum.INDEX_2_3));
        // step 2-7：净增 VS Y24 预算增长
        builder.addStep(demandDetailUtils.buildDemandSubtract(DemandDetailIndexEnum.INDEX_2_7,DemandDetailIndexEnum.INDEX_2_1,DemandDetailIndexEnum.INDEX_2_4,true));
        return builder.build();
    }

    /** 客户需求计划复盘总览 */
    private QueryProcess registerScaleDemand(){
        QueryProcessBuilder builder = initBuilder();
        // step1：Y24 大盘规模存量
        builder.addStep(demandUtils.buildQueryStep(DemandOverviewIndexEnum.INDEX_01.getName(),StoreNameEnum.totalScale.getName()));
        // step2：Y24 净增
        builder.addStep(demandUtils.buildQueryStep(DemandOverviewIndexEnum.INDEX_02.getName(),StoreNameEnum.demandReturn.getName()));
        // step3：Y24 大盘规模（上一轮）
        builder.addStep(demandUtils.buildQueryStep(DemandOverviewIndexEnum.INDEX_03.getName(),StoreNameEnum.totalScale.getName(),MapUtils.of("isLast",true)));
        // step4：Y24 净增 （上一轮）
        builder.addStep(demandUtils.buildQueryStep(DemandOverviewIndexEnum.INDEX_04.getName(),StoreNameEnum.demandReturn.getName(),MapUtils.of("isLast",true)));
        // step5：Y24 预算
        builder.addStep(demandUtils.buildQueryStep(DemandOverviewIndexEnum.INDEX_05.getName(),StoreNameEnum.budget.getName()));
        // step6：Y24 净增预算
        builder.addStep(demandUtils.buildQueryStep(DemandOverviewIndexEnum.INDEX_06.getName(),StoreNameEnum.budgetAdd.getName()));
        // step7：Y23 实际大盘规模
        builder.addStep(demandUtils.buildQueryStep(DemandOverviewIndexEnum.INDEX_07.getName(),StoreNameEnum.totalScale.getName(),MapUtils.of("isLastYear",true)));
        // step8：Y23 实际净增
        builder.addStep(demandUtils.buildQueryStep(DemandOverviewIndexEnum.INDEX_08.getName(),StoreNameEnum.demandReturn.getName(),MapUtils.of("isLastYear",true)));
        // step9：净增Δ VS Y24 上一轮
        builder.addStep(demandUtils.buildSubtract(DemandOverviewIndexEnum.INDEX_09.getName(), DemandOverviewIndexEnum.INDEX_02.getName(), DemandOverviewIndexEnum.INDEX_04.getName()));
        // step10：总规模Δ VS Y24 预算
        builder.addStep(demandUtils.buildSubtract(DemandOverviewIndexEnum.INDEX_10.getName(), DemandOverviewIndexEnum.INDEX_01.getName(), DemandOverviewIndexEnum.INDEX_05.getName()));
        // step11：总规模Δ VS Y24 上一轮
        builder.addStep(demandUtils.buildSubtract(DemandOverviewIndexEnum.INDEX_11.getName(), DemandOverviewIndexEnum.INDEX_01.getName(), DemandOverviewIndexEnum.INDEX_03.getName()));
        // step12：总规模增长% Y24/Y23
        builder.addStep(demandUtils.buildDivide(DemandOverviewIndexEnum.INDEX_12.getName(), DemandOverviewIndexEnum.INDEX_01.getName(), DemandOverviewIndexEnum.INDEX_07.getName()));
        // step13：净增Δ VS Y23
        builder.addStep(demandUtils.buildSubtract(DemandOverviewIndexEnum.INDEX_13.getName(), DemandOverviewIndexEnum.INDEX_02.getName(), DemandOverviewIndexEnum.INDEX_08.getName()));

        return builder.build();
    }

    // =============== 供应复盘 ================

    /** 供应复盘-总览 */
    private QueryProcess registerSupply() {
        // 初始化前置步骤
        QueryProcessBuilder builder = initSupplyQueryProcess(12);
        // =============== 指标 ================
        // step1：Y24 需求
        builder.addStep(supplyUtils.buildDemandStep(SupplyIndexEnum.INDEX_01.getName(),"STEP-0-1"));
        // step2：Y24 供应
        builder.addStep(supplyUtils.buildQueryStep(SupplyIndexEnum.INDEX_02.getName(),StoreNameEnum.cloudSupply.getName(),"STEP-0-2"));
        // step3：Y24 库存
        builder.addStep(supplyUtils.buildQueryStep(SupplyIndexEnum.INDEX_03.getName(),StoreNameEnum.cloudInventory.getName(),"STEP-0-2"));
        // step4：Y24 缺口
        builder.addStep(supplyUtils.buildQueryStep(SupplyIndexEnum.INDEX_04.getName(),StoreNameEnum.cloudGap.getName(),"STEP-0-2"));
        // step5：满足率%
        builder.addStep(supplyUtils.buildRate(SupplyIndexEnum.INDEX_05.getName(),SupplyIndexEnum.INDEX_01.getName(),SupplyIndexEnum.INDEX_04.getName()));
        return builder.build();
    }

    /** 供应复盘-明细 */
    private QueryProcess registerSupplyDetail() {
        // 初始化前置步骤
        QueryProcessBuilder builder = initSupplyQueryProcess(12);
        // =============== 指标 ================
        // step1：Y24 需求
        builder.addStep(supplyDetailUtils.buildDemandStep(SupplyIndexEnum.INDEX_01.getName(),"STEP-0-1",false));
        // step2：Y24 供应
        builder.addStep(supplyDetailUtils.buildQueryStep(SupplyIndexEnum.INDEX_02.getName(),StoreNameEnum.cloudSupply.getName(),"STEP-0-2",false,true));
        // step3：Y24 库存
        builder.addStep(supplyDetailUtils.buildQueryStep(SupplyIndexEnum.INDEX_03.getName(),StoreNameEnum.cloudInventory.getName(),"STEP-0-2",false,true));
        // step4：Y24 缺口
        builder.addStep(supplyDetailUtils.buildQueryStep(SupplyIndexEnum.INDEX_04.getName(),StoreNameEnum.cloudGap.getName(),"STEP-0-2",false,true));
        // step5：满足率%
        builder.addStep(supplyDetailUtils.buildRate(SupplyIndexEnum.INDEX_05.getName(),SupplyIndexEnum.INDEX_01.getName(),SupplyIndexEnum.INDEX_04.getName()));
        return builder.build();
    }

    private QueryProcessBuilder initSupplyQueryProcess(int monthRange){
        QueryProcessBuilder builder = initBuilder();
        // 初始化过程参数：
        // 1. apportion_map：分摊系数（key：年月@设备类型）
        builder.addParam("apportion_map", HashMap::new);
        // 2. cvm转物理机函数
        builder.addParam("cvm_2_device", () -> commonService.getCvm2Device());
        // 3. 供应数据
        builder.addParam("supply_data", ArrayList::new);
        // 4. 需求数据
        builder.addParam("demand_data", ArrayList::new);
        // 5. 供应数据是否就绪的门栓
        builder.addParam("supply_data_latch", () -> new CountDownLatch(1));

        // =============== 前置处理 ================

        // step0-1：需求明细，提供Y24 需求数据和分摊数据
        builder.addStep(supplyUtils.buildStep0("STEP-0-1",StoreNameEnum.cloudDemand.getName(),MapUtils.of("monthRange",monthRange))); // 月滚动长度，包括当月
        // step0-2：分摊数据，为供应，库存提供分摊依据
        builder.addStep(supplyUtils.buildApportionStep("STEP-0-2","STEP-0-1"));

        return builder;
    }

    /** 供应复盘-库存周转 */
    public QueryProcess registerInventoryTurnover(){
        // 初始化前置步骤(这里查库存周转需要未来3个月的需求数据，所以查未来15个月的)
        QueryProcessBuilder builder = initSupplyQueryProcess(15);
        // step 1：Y24 需求（例子：库存24年1月，取24年2月-4月的需求总数）
        builder.addStep(supplyDetailUtils.buildDemandStep(SupplyIndexEnum.INDEX_01.getName(),"STEP-0-1",true));
        // step 2：Y24 库存
        builder.addStep(supplyDetailUtils.buildQueryStep(SupplyIndexEnum.INDEX_03.getName(),StoreNameEnum.cloudInventory.getName(),"STEP-0-2",false,true));
        // step 3：Y24 缺口
        builder.addStep(supplyDetailUtils.buildQueryStep(SupplyIndexEnum.INDEX_02.getName(),StoreNameEnum.cloudSupply.getName(),"STEP-0-2",true,true));
        builder.addStep(supplyDetailUtils.buildQueryStep(SupplyIndexEnum.INDEX_04.getName(),StoreNameEnum.cloudGap.getName(),"STEP-0-2",false,true));
        // step 4：满足率%
        builder.addStep(supplyDetailUtils.buildRate(SupplyIndexEnum.INDEX_05.getName(),SupplyIndexEnum.INDEX_01.getName(),SupplyIndexEnum.INDEX_04.getName()));
        // step 5: 未来三个月需求（例子：库存24年1月，取24年2月-4月的需求总数）
        builder.addStep(inventoryDetailUtils.buildDemand3Step(SupplyIndexEnum.INDEX_06.getName(),"STEP-0-1",null,true));
        // step 6：库存周转（通过需求和库存计算：库存/未来三个月均需求净增*30）
        builder.addStep(inventoryDetailUtils.buildInventoryStep(SupplyIndexEnum.INDEX_07.getName(),SupplyIndexEnum.INDEX_06.getName(),SupplyIndexEnum.INDEX_03.getName(),false));
        return builder.build();
    }

    /** 供应复盘-库存健康 */
    public QueryProcess registerInventoryHealth(){
        // 强制分组
        List<String> dim = ListUtils.newList("sopBigFamily", "utilBusinessType","deviceType");
        // 初始化前置步骤(这里查库存周转需要未来3个月的需求数据，所以查未来15个月的)
        QueryProcessBuilder builder = initSupplyQueryProcess(15);
        // step 1: 未来三个月需求（例子：库存24年1月，取24年2月-4月的需求总数），强制进行设备类型的分组，这里分组给健康库存使用
        builder.addStep(inventoryDetailUtils.buildDemand3Step(SupplyIndexEnum.INDEX_06.getName(),"STEP-0-1",MapUtils.of("dim", dim),false));
        // step 2：Y24 库存
        builder.addStep(supplyDetailUtils.buildQueryStep(SupplyIndexEnum.INDEX_03.getName(),StoreNameEnum.cloudInventory.getName(),"STEP-0-2", MapUtils.of("dim",dim),true,true,false));
        // step 3：缺货
        builder.addStep(supplyDetailUtils.buildQueryStep(SupplyIndexEnum.INDEX_02.getName(),StoreNameEnum.cloudSupply.getName(),"STEP-0-2",true,true));
        builder.addStep(supplyDetailUtils.buildQueryStep(SupplyIndexEnum.INDEX_09.getName(),StoreNameEnum.cloudGap.getName(),"STEP-0-2",null,false,true,false));
        // step 4：库存周转（通过需求和库存计算：库存/未来三个月均需求净增*30）
        builder.addStep(inventoryDetailUtils.buildInventoryStep(SupplyIndexEnum.INDEX_07.getName(),SupplyIndexEnum.INDEX_06.getName(),SupplyIndexEnum.INDEX_03.getName(),true));
        // step 5：库存健康
        builder.addStep(inventoryDetailUtils.buildInventoryHealthStep(SupplyIndexEnum.INDEX_08.getName(),SupplyIndexEnum.INDEX_03.getName(), SupplyIndexEnum.INDEX_07.getName(),false));

        return builder.build();
    }

    /** 分摊丢失+CVM折算丢失监控 */
    public QueryProcess registerMonitorLoseData(){
        QueryProcessBuilder builder = initSupplyQueryProcess(12);

        // step 0：丢失的集合map
        builder.addParam("lose_data_map", HashMap::new);

        // step 1：库存-分摊丢失的数据
        builder.addStep(supplyDetailUtils.buildQueryStep(SupplyIndexEnum.INDEX_02.getName(),StoreNameEnum.cloudSupply.getName(),"STEP-0-2",true,false));
        // step 2：供应-分摊丢失的数据
        builder.addStep(supplyDetailUtils.buildQueryStep(SupplyIndexEnum.INDEX_03.getName(),StoreNameEnum.cloudInventory.getName(),"STEP-0-2",true,false));
        // step 3：缺口-分摊丢失的数据
        builder.addStep(supplyDetailUtils.buildQueryStep(SupplyIndexEnum.INDEX_04.getName(),StoreNameEnum.cloudGap.getName(),"STEP-0-2",true,false));

        return builder.build();
    }

    /** 分摊系数 */
    private QueryProcess registerApportionRate() {
        QueryProcessBuilder builder = initSupplyQueryProcess(12);

        return builder.build();
    }

    private QueryProcessBuilder initBuilder(){
        QueryProcessBuilder builder = new QueryProcessBuilder(storeFactory);
        // 版本和年月信息
        builder.addParam("info", VersionInfo::new);

        // 填充版本年月的步骤
        builder.addStep(StepUtils.buildInit("STEP-INIT",StoreNameEnum.initVersion.getName()));
        return builder;
    }
}
