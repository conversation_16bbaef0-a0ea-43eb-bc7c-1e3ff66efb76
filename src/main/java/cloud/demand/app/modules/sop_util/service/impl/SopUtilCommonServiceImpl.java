package cloud.demand.app.modules.sop_util.service.impl;

import cloud.demand.app.modules.common.service.entity.BasObsCloudCvmTypeDO;
import cloud.demand.app.modules.sop.domain.http.YuntiCvmDemandVersionCodeRes;
import cloud.demand.app.modules.sop.domain.report.VersionDTO;
import cloud.demand.app.modules.sop.enums.DynamicProperties;
import cloud.demand.app.modules.sop.service.CommonDbHelper;
import cloud.demand.app.modules.sop.service.web.SopJxcReportService;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import cloud.demand.app.modules.sop_util.domain.demand.ReportDemandVersionResp;
import cloud.demand.app.modules.sop_util.entity.ObsBudgetRollAdjustCvmAppendDeviceDO;
import cloud.demand.app.modules.sop_util.model.version.SopUtilVersionItem;
import cloud.demand.app.modules.sop_util.model.version.VersionItem;
import cloud.demand.app.modules.sop_util.service.SopUtilCommonService;
import cloud.demand.app.modules.sop_util.utils.CommonUtils;
import cloud.demand.app.modules.sop_util.utils.SopUtilsVersionBuilder;
import cloud.demand.app.modules.sop_yunti.service.YuntiCommonService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.stream.Collectors;

/** 公共方法 */
@Service
public class SopUtilCommonServiceImpl implements SopUtilCommonService {

    /** sop报表 */
    @Resource
    private SopJxcReportService sopJxcReportService;

    /** obs */
    @Resource
    private DBHelper obsDBHelper;

    /** 库存 */
    @Resource
    private DBHelper resourcedbDBHelper;

    /** sop公共清洗 */
    @Resource
    private CommonDbHelper commonDbHelper;

    /** 云梯公共方法 */
    @Resource
    private YuntiCommonService yuntiCommonService;

    @HiSpeedCache(expireSecond = 600,continueFetchSecond = 180)
    @Override
    public List<String> getDeviceGpuFamily() {
        // 匹配带有GPU的物理机机型族
        return resourcedbDBHelper.getRaw(String.class,
                "select distinct DeviceFamilyName from bas_stratege_device_type where DeviceFamilyName like '%GPU%'");
    }

    @HiSpeedCache(expireSecond = 600,continueFetchSecond = 180)
    @Override
    public List<String> getCvmGpuFamily() {
        // 匹配带有GPU的CVM机型族
        return obsDBHelper.getRaw(String.class,
                "select distinct CvmInstanceType from bas_obs_cloud_cvm_type where CvmInstanceGroup like '%GPU%'");
    }

    //    @HiSpeedCache(expireSecond = 600,continueFetchSecond = 1800)
    @Override
    public Map<String, LocalDateTime> getVersionDate() {
        List<VersionDTO> versionDTOS = sopJxcReportService.listVersion();
        Map<String, LocalDateTime> ret = new HashMap<>();
        if (ListUtils.isNotEmpty(versionDTOS)){
            for (VersionDTO versionDTO : versionDTOS) {
                if (versionDTO.getTime()!=null){
                    ret.put(versionDTO.getVersion(), versionDTO.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                }
            }
        }
        return ret;
    }

    @Override
    public LocalDate getVersionDate(String version) {
        List<VersionDTO> versionDTOS = sopJxcReportService.listVersion();
        if (StringUtils.isBlank(version)){
            return null;
        }
        for (VersionDTO versionDTO : versionDTOS) {
            if (Objects.equals(versionDTO.getVersion(),version)){
                return SopDateUtils.getYearMonthDay(versionDTO.getTime());
            }
        }
        return null;
    }

    /** 这个方法最后可以录到db里面，作为初始化和flush版本信息，后续查只需要查db就行 */
//    @HiSpeedCache(expireSecond = 600,continueFetchSecond = 1800)
    @Override
    public ReportDemandVersionResp versionList() {
        ReportDemandVersionResp ret = new ReportDemandVersionResp();
        List<ReportDemandVersionResp.Item> transform = ListUtils.transform(getVersionList(), ReportDemandVersionResp::transform);
        ret.setData(transform);
        // 如果默认开启了云梯版本数据（这里会加上标识，告诉前端该版本是否有云梯数据）
        if (DynamicProperties.getSopUtilsIsYuntiVersion()){
            YuntiCvmDemandVersionCodeRes res = yuntiCommonService.queryVersionListV2();
            List<YuntiCvmDemandVersionCodeRes.Item> data = res.getData();
            Set<String> set = ListUtils.toSet(data, item-> DateUtils.format(item.getCvmSnapshot()));
            for (ReportDemandVersionResp.Item item : transform) {
                LocalDate localDate = item.getDeviceVersionDateTime().toLocalDate();
                String format = DateUtils.format(localDate);
                item.setIsForbidden(!set.contains(format)); // 是否包含云梯数据
            }
        }
        return ret;
    }


    @Override
    public SopUtilVersionItem getVersion(String version) {
        List<SopUtilVersionItem> list = this.getVersionList();
        if (ListUtils.isEmpty(list)){
            return null;
        }
        if (StringUtils.isBlank(version)){
            return list.get(0);
        }
        return list.stream().filter(item->item.getLabel().equals(version)).findFirst().orElse(null);
    }

    @Override
    public SopUtilVersionItem getLastVersion(String version) {
        List<SopUtilVersionItem> list = this.getVersionList();
        if (ListUtils.isEmpty(list) || list.size() == 1){
            return null;
        }
        // 为空取最新版本的上一个版本，即下标为1
        if (StringUtils.isBlank(version)){
            return list.get(1);
        }
        // 找到label取label下标+1的版本
        for (int i = 0; i < list.size(); i++) {
            SopUtilVersionItem item = list.get(i);
            if (Objects.equals(item.getLabel(),version)){
                return (i == list.size() -1)? null: list.get(i+1);
            }
        }
        return null;
    }

    @Override
    public SopUtilVersionItem getLastYearVersion(String version) {
        List<SopUtilVersionItem> list = this.getVersionList();
        if (ListUtils.isEmpty(list) || list.size() == 1){
            return null;
        }
        // 为空取最新版本
        if (StringUtils.isBlank(version)){
            version = list.get(0).getLabel();
        }
        // 找到去年同期的版本，找不到则从时间上后推
        String lastYearVersion = CommonUtils.getLastYearVersion(version);
        SopUtilVersionItem ret = null;

        // 首个小于去年同期版本的前一个版本为要找的版本
        for (SopUtilVersionItem item : list) {
            if (item.getLabel().compareTo(lastYearVersion)>=0){
                ret = item;
            }else {
                break;
            }
        }
        return ret;
    }

    @HiSpeedCache(expireSecond = 600,continueFetchSecond = 1800)
    @Override
    public Map<String,ObsBudgetRollAdjustCvmAppendDeviceDO> getCvm2Device() {
        List<ObsBudgetRollAdjustCvmAppendDeviceDO> all = obsDBHelper.
                getAll(ObsBudgetRollAdjustCvmAppendDeviceDO.class, "where EnableFlag=1");
        Map<String, Integer> cvmCpuCoreMap = commonDbHelper.getCvmCpuCoreMap();
        Map<String, Integer> deviceCpuCoreMap = commonDbHelper.getDeviceCpuCoreMap();
        Map<String,ObsBudgetRollAdjustCvmAppendDeviceDO> ret = new HashMap<>();
        for (ObsBudgetRollAdjustCvmAppendDeviceDO item : all) {
            BigDecimal transferRatio = item.getTransferRatio();
            Integer deviceCore = deviceCpuCoreMap.get(item.getToType());
            Integer cvmCore = cvmCpuCoreMap.get(item.getFromType());
            if (transferRatio != null && deviceCore != null && cvmCore != null){
                // 转换比率 = 原转换比率 * cpu（物理机） / cpu（cvm）
                item.setTransferRatio(transferRatio.
                        multiply(BigDecimal.valueOf(deviceCore)).
                        divide(BigDecimal.valueOf(cvmCore),6 ,RoundingMode.HALF_UP));
                ret.put(item.getFromType(),item);
            }
        }
        return ret;
    }

    @Override
    @HiSpeedCache(expireSecond = 600,continueFetchSecond = 1800)
    public Map<String, ObsBudgetRollAdjustCvmAppendDeviceDO> getCvm2DeviceOriginalRate() {
        List<ObsBudgetRollAdjustCvmAppendDeviceDO> all = obsDBHelper.
                getAll(ObsBudgetRollAdjustCvmAppendDeviceDO.class, "where EnableFlag=1");
        Map<String, ObsBudgetRollAdjustCvmAppendDeviceDO> map = all.stream()
                .collect(Collectors.toMap(ObsBudgetRollAdjustCvmAppendDeviceDO::getFromType, item -> item));
        return map;
    }

    @HiSpeedCache(expireSecond = 600,continueFetchSecond = 1800)
    @Override
    public Map<String, String> getInstanceType2DeviceType() {
        List<BasObsCloudCvmTypeDO> all = obsDBHelper.getAll(BasObsCloudCvmTypeDO.class);
        return ListUtils.toMap(all, BasObsCloudCvmTypeDO::getCvmInstanceType, BasObsCloudCvmTypeDO::getHostDeviceClass);
    }

    private List<SopUtilVersionItem> getVersionList(){
        // step1：查询版本结合
        List<VersionDTO> versionDTOS = sopJxcReportService.listVersion();
        // step2：转换为通用版本集合类
        List<VersionItem> transform = ListUtils.transform(versionDTOS, VersionItem::transform);
        transform = transform.stream().filter(Objects::nonNull).collect(Collectors.toList());
        transform.sort(Comparator.comparing(VersionItem::getVersion));

        // step3：构建器准备
        SopUtilsVersionBuilder builder = new SopUtilsVersionBuilder();

        // step3-1：标签集合（标签：年-月-周）
        builder.setLabelFun(item -> CommonUtils.getYearWeek(item.getVersionDateTime()));
        // step3-2：标签内分组（分组key：周几）
        builder.setItemGroupFun(item-> CommonUtils.getWeek(item.getVersionDateTime()));

        // step3-2：设置标签匹配策略，多个命中按order优先级选择,底层已经按order排好序了，取下标为0即可

        // 当前年周
        String curYearWeek = CommonUtils.getYearWeek(LocalDateTime.now());

        // 1. 物理机精准匹配
        // 匹配逻辑：强制匹配下发，周一~周五按顺序匹配，优先周一最后周五，如果是本周且没有下发，则取周二的数据
        builder.addDeviceStrategy((label, treeMap) -> {
            Map<String, List<VersionItem>> map = treeMap.get(label);
            if (ListUtils.isEmpty(map)){
                return null;
            }
//            // 周一~周五，顺序优先
//            String[] weeks = new String[]{"1","2","3","4","5"};
//            for (String week : weeks) {
//                List<VersionItem> items = map.get(week);
//                // 已下发的order=0，已经按order升序排列，取第一个即可
//                if (ListUtils.isNotEmpty(items) && items.get(0).getOrder() == 0){
//                    return items.get(0);
//                }
//            }
//
//            // 如果是当周，则取周二的数据
//            if (Objects.equals(curYearWeek,label)){
//                List<VersionItem> items = map.get("2");
//                if (ListUtils.isNotEmpty(items)){
//                    return items.get(0);
//                }
//            }

            // 物理机取周二的
            List<VersionItem> items = map.get("2");
            // 取第一个即可，这里不限制是否下发
            if (ListUtils.isNotEmpty(items)){
                return items.get(0);
            }

            // 匹配不到
            return null;
        });
        // 2. cvm匹配
        // 匹配逻辑：强制匹配周五，下发优先级更高
        builder.addCvmStrategy((label, treeMap) -> {
            // 不用取上周的，直接本周就行
//            Map.Entry<String, Map<String, List<VersionItem>>> entry = treeMap.lowerEntry(label);
//            if (entry == null){
//                return null;
//            }
//            Map<String, List<VersionItem>> map = entry.getValue();
            Map<String, List<VersionItem>> map = treeMap.get(label);
            if (ListUtils.isEmpty(map)){
                return null;
            }
            // 取周一的
            List<VersionItem> items = map.get("1");
            if (ListUtils.isEmpty(items)){
                return null;
            }
            return items.get(0);
        });
        // step4：特殊处理
        builder.setItemFun(versionItem -> {
            LocalDateTime datetTime = versionItem.getDeviceVersionDateTime();
            versionItem.setLabel(
                    String.format("%s（%s周）",
                            datetTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                            datetTime.get(WeekFields.ISO.weekOfWeekBasedYear())));
        });

        List<SopUtilVersionItem> ret = builder.transform(transform);
        // 降序排列
        ret.sort((o1, o2) -> o2.getLabel().compareTo(o1.getLabel()));
        return ret;
    }
}
