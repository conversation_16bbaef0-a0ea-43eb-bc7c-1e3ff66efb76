package cloud.demand.app.modules.sop_util.process.store.item;

import cloud.demand.app.modules.sop_util.model.IReportData;
import cloud.demand.app.modules.sop_util.process.store.IDBData;
import cloud.demand.app.modules.sop_util.utils.CommonUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Map;

@Data
public abstract class CommonItem implements IReportData {
    private String yearMonth;
    private BigDecimal coreNum;

    @Override
    public String getYearMonth_() {
        return yearMonth;
    }

    @Override
    public BigDecimal getValue_() {
        return coreNum;
    }
}
