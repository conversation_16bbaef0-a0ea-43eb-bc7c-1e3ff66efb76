package cloud.demand.app.modules.sop_util.domain.demand;

import cloud.demand.app.modules.sop_util.domain.ReportCommon;
import cloud.demand.app.modules.sop_util.domain.item.ReportIndexItem;
import lombok.Data;

import java.util.List;

@Data
public class ReportDemandResp extends ReportCommon {

    /** 当前版本数据 */
    private List<ReportIndexItem> data;

    /** 上一个版本数据 */
    private List<ReportIndexItem> lastData;
}
