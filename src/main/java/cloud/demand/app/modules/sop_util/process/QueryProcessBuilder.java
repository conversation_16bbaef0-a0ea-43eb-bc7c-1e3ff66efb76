package cloud.demand.app.modules.sop_util.process;

import cloud.demand.app.modules.sop.exception.SopException;
import cloud.demand.app.modules.sop_util.process.step.IQueryData;
import cloud.demand.app.modules.sop_util.process.step.QueryStep;
import cloud.demand.app.modules.sop_util.process.store.DataStore;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/** 查询进程构建器 */
public class QueryProcessBuilder {

    private final List<QueryStep> steps = new ArrayList<>();

    private final Map<String, Supplier<Object>> params = new ConcurrentHashMap<>();

    private final StoreFactory storeFactory;

    public QueryProcessBuilder(StoreFactory storeFactory){
        this.storeFactory = storeFactory;
    }

    /**
     * 是否包含某个步骤
     * @param stepName 步骤名称
     * @return
     */
    public boolean containStep(String stepName){
        if (ListUtils.isEmpty(steps) || StringUtils.isBlank(stepName)){
            return false;
        }
        for (QueryStep step : steps) {
            if (Objects.equals(step.getStepName(),stepName)){
                return true;
            }
        }
        return false;
    }

    /** 设置全局初始参数 */
    public QueryProcessBuilder addParam(String key, Supplier<Object> val){
        params.put(key,val);
        return this;
    }

    /** 添加步骤 */
    public QueryProcessBuilder addStep(QueryStep step){
        steps.add(step);
        return this;
    }

    /** 批量更新step */
    public void batchUpdateStep(Consumer<QueryStep> consumer){
        if (ListUtils.isNotEmpty(this.steps)){
            for (QueryStep step : this.steps) {
                consumer.accept(step);
            }
        }
    }

    /** 执行构建 */
    public QueryProcess build(){
        Set<String> stepName = new HashSet<>();
        Map<String, DataStore> storeMap = new HashMap<>();
        Map<String,BiConsumer<List<IQueryData>,List<IQueryData>>> mergeMap = new LinkedHashMap<>();
        for (QueryStep step : steps) {
            stepName.add(step.getStepName());
            String storeName = step.getIndexModel().getStoreName();
            DataStore dataStore;
            if (StringUtils.isBlank(storeName)){
                dataStore = new DataStore((map)-> new ArrayList<>());
            }else{
                dataStore = storeFactory.getDataStore(storeName);
                if (dataStore == null){
                    throw new SopException(String.format("该仓库没有注册：【%s】", storeName));
                }
            }

            storeMap.put(storeName, dataStore);
            List<String> dependence = step.getDependence();
            if (ListUtils.isNotEmpty(dependence)){
                for (String dep : dependence) {
                    if (!stepName.contains(dep)){
                        throw new SopException(String.format("依赖的步骤不存在，或不在该步骤之前执行: 【%s】->【%s】", dep,step.getStepName()));
                    }
                }
            }
            if (step.getMerge() == null){
                throw new SopException(String.format("合并逻辑不能为空，步骤：【%s】", step.getStepName()));
            }
            mergeMap.put(step.getStepName(),step.getMerge());
        }

        // 注册合并的方法
        Function<Map<String, List<IQueryData>>, List<IQueryData>> mergeFunc = stringListMap -> {
            List<IQueryData> ret = new ArrayList<>();
            for (Map.Entry<String, BiConsumer<List<IQueryData>, List<IQueryData>>> entry : mergeMap.entrySet()) {
                entry.getValue().accept(ret,stringListMap.get(entry.getKey()));
            }
            return ret;
        };

        return QueryProcess.builder()
                .params(params) // 全局参数
                .steps(steps)   // 步骤
                .mergeFunc(mergeFunc) // 查询返回值的合并函数
                .storeMap(storeMap) // 注册仓库集合
                .build();
    }
}
