package cloud.demand.app.modules.sop_util.entity.battle.item;

import cloud.demand.app.modules.sop_util.entity.battle.BaseDO;
import cloud.demand.app.modules.sop_util.entity.battle.IM12DO;
import lombok.Data;

/** 供应实体类 */
@Data
public class SupplyDO extends InventoryDO {
    @Override
    public <T extends IM12DO> T init(T im12DO) {
        SupplyDO ret = new SupplyDO();
        SupplyDO origin = (SupplyDO)im12DO;
        ret.setDeviceType(origin.getDeviceType());
        ret.setDeviceFamily(origin.getDeviceFamily());
        return (T)ret;
    }
}
