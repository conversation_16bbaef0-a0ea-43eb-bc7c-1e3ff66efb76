package cloud.demand.app.modules.sop_util.process.utils.step;

import cloud.demand.app.modules.sop.enums.Constant;
import cloud.demand.app.modules.sop_util.domain.item.ReportItem;
import cloud.demand.app.modules.sop_util.domain.item.ReportMapIndexItem;
import cloud.demand.app.modules.sop_util.domain.supply.ReportSupplyReq;
import cloud.demand.app.modules.sop_util.enums.supply.InventoryHealthEnum;
import cloud.demand.app.modules.sop_util.model.demand.SopDemandReturnReq;
import cloud.demand.app.modules.sop_util.model.supply.VersionInfo;
import cloud.demand.app.modules.sop_util.process.step.IQueryData;
import cloud.demand.app.modules.sop_util.process.step.QueryStep;
import cloud.demand.app.modules.sop_util.process.store.item.SopDemandReturnItem;
import cloud.demand.app.modules.sop_util.process.utils.StepUtils;
import cloud.demand.app.modules.sop_util.utils.CommonUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.YearMonth;
import java.util.*;
import java.util.function.*;
import java.util.stream.Collectors;

import static cloud.demand.app.modules.sop_util.utils.CommonUtils.toChildren;
import static cloud.demand.app.modules.sop_util.utils.CommonUtils.toParent;

/**
 * 库存周转工具类
 */
public class InventoryDetailUtils {

    /** 需求未来三个月，例子：1月份的coreNum为2-4月的sum(coreNum) */
    public QueryStep buildDemand3Step(String stepName, String depStep,Map<String,Object> stepParams,boolean needFill) {
        QueryStep queryStep = new QueryStep(
                stepName,
                null,
                (map, stringListMap) -> {
                    SopDemandReturnReq.TimePeriod timePeriod = StepUtils.getTime(map);
                    String start = timePeriod.getStart();
                    String end = timePeriod.getEnd();

                    List<SopDemandReturnItem> items = StepUtils.demandTransform((ReportSupplyReq) map.get("req"),
                            (List<String>) map.get("dim"),
                            toChildren(stringListMap.get(depStep)),YearMonth.parse(start),
                            15);

                    items = dataProcess(items, currMonth -> {
                        List<String> ret = new ArrayList<>();
                        YearMonth parse = YearMonth.parse(currMonth);
                        int range = -3;
                        while(range < 0){
                            String next = CommonUtils.yearMonth(parse.plusMonths(range));
                            if (next.compareTo(start) >= 0 && next.compareTo(end) <= 0){
                                ret.add(next);
                            }
                            range++;
                        }
                        return ret;
                    });
                    List<ReportMapIndexItem> ret = ReportMapIndexItem.transform(toParent(items), stepName);
                    if (needFill){
                        ret.forEach(item -> item.getData().values().forEach(data -> ReportItem.fillData(data, timePeriod)));
                    }else {
                        ret.forEach(item -> item.getData().values().forEach(ReportItem::sort));
                    }
                    return toParent(ret);
                },
                idbData -> null,
                stepParams);
        // 不加入到返回集合中
        queryStep.setAsync(true);
        queryStep.setMerge((iQueryData, iQueryData2) -> {});
        queryStep.setDependence(ListUtils.newList(depStep));
        return queryStep;
    }

    /** 健康库存步骤构建 */
    public QueryStep buildInventoryHealthStep(String stepName, String inventoryStep, String turnoverStep,boolean needHide) {
        QueryStep queryStep = new QueryStep(
                stepName,
                null,
                (map, stringListMap) -> {
                    // 库存数据
                    List<ReportMapIndexItem> inventoryData = toChildren(stringListMap.get(inventoryStep));
                    // 库存周转数据
                    List<ReportMapIndexItem> turnoverData = toChildren(stringListMap.get(turnoverStep));
                    // 请求
                    ReportSupplyReq req = (ReportSupplyReq)map.get("req");
                    // 起始年月
                    VersionInfo info = (VersionInfo)map.get("info");
                    YearMonth deviceYearMonth = info.getDeviceYearMonth();
                    // sop大类、sop业务类型过滤
                    Map<String, Set<String>> dimValue = new HashMap<>();
                    if (ListUtils.isNotEmpty(req.getSopBigFamily())){
                        dimValue.put("sopBigFamily",new HashSet<>(req.getSopBigFamily()));
                    }
                    if (ListUtils.isNotEmpty(req.getUtilBusinessType())){
                        dimValue.put("utilBusinessType",new HashSet<>(req.getUtilBusinessType()));
                    }

                    BiPredicate<ReportMapIndexItem, String> checkFun = (item, s) -> {
                        Map<String, Object> dimMap = item.getDimMap();
                        Object o = dimMap.get(s);
                        return dimValue.get(s).contains(o==null?null:o.toString());
                    };
                    inventoryData = StepUtils.filter(inventoryData, checkFun, dimValue);
                    turnoverData = StepUtils.filter(turnoverData, checkFun, dimValue);
                    // 合并计算（统计按设备类型维度，不同库存健康的库存聚合）
                    List<ReportMapIndexItem> ret = computeHealth(inventoryData,turnoverData,inventoryStep,turnoverStep);
                    // 聚合处理（按照查询的dim进行聚合）
                    if (ListUtils.isNotEmpty(ret)){
                        List<String> dim = req.getDim();
                        Map<String, List<ReportMapIndexItem>> groupByMap = ret.stream().collect(Collectors.groupingBy(item -> {
                            Map<String, Object> dimMap = item.getDimMap();
                            if (ListUtils.isNotEmpty(dim)) {
                                StringBuilder sbr = new StringBuilder();
                                for (String s : dim) {
                                    sbr.append("@").append(dimMap.get(s));
                                }
                                return sbr.toString();
                            }
                            return "@";
                        }));
                        // 统计groupBy的数据
                        List<ReportMapIndexItem> newRet = new ArrayList<>();
                        groupByMap.forEach((k, v) -> newRet.add(merge(dim,deviceYearMonth,v)));
                        ret = newRet;
                    }
                    return toParent(ret);
                },
                idbData -> null,
                null
        );
        queryStep.setDependence(ListUtils.newList(inventoryStep,turnoverStep));
        if (needHide){
            queryStep.setMerge((iQueryData, iQueryData2) -> {});
        }else{
            queryStep.setMerge(getMerge());
        }
        return queryStep;
    }

    /** 合并 */
    private ReportMapIndexItem merge(List<String> dim,YearMonth yearMonth,List<ReportMapIndexItem> items){
        ReportMapIndexItem ret = new ReportMapIndexItem();

        // 取首个dimMap，根据dim存sop业务类型和sop大类，有该维度的存实际数据，否则存空值
        ReportMapIndexItem item = items.get(0);
        Map<String, Object> itemDimMap = item.getDimMap();
        HashMap<String, Object> dimMap = new HashMap<>();
        dimMap.put("utilBusinessType",dim.contains("utilBusinessType")?itemDimMap.get("utilBusinessType"): Constant.EMPTY_VALUE_STR);
        dimMap.put("sopBigFamily",dim.contains("sopBigFamily")?itemDimMap.get("sopBigFamily"): Constant.EMPTY_VALUE_STR);

        // 现存map，用于merge
        Map<String, Map<String,ReportItem>> dataMap = new HashMap<>();
        for (ReportMapIndexItem indexItem : items) {
            Map<String, List<ReportItem>> tempData = indexItem.getData();
            tempData.forEach((k,v)->{
                Map<String, ReportItem> map = dataMap.computeIfAbsent(k, l -> new HashMap<>());
                for (ReportItem reportItem : v) {
                    String label = reportItem.getLabel();
                    if (map.containsKey(label)){
                        ReportItem mergeItem = map.get(label);
                        // 相加处理
                        mergeItem.setNumber(mergeItem.getNumber().add(ObjectUtils.defaultIfNull(reportItem.getNumber(),BigDecimal.ZERO)));
                        mergeItem.setValue(CommonUtils.getValue(mergeItem.getNumber()));
                    }else {
                        map.put(label,ReportItem.copy(reportItem));
                    }
                }
            });
        }

        SopDemandReturnReq.TimePeriod timePeriod = SopDemandReturnReq.TimePeriod.rollingOneYear(yearMonth);

        Map<String, List<ReportItem>> data = new HashMap<>();
        dataMap.forEach((k,v)->{
            List<ReportItem> value = new ArrayList<>(v.values());
            // 补充空缺的年月
            ReportItem.fillEmptyMonth(value,timePeriod);
            // 排序
            ReportItem.sort(value);
            data.put(k, value);
        });

        ret.setData(data);
        ret.setDimMap(dimMap);

        return ret;
    }

    /** 计算库存健康 */
    private List<ReportMapIndexItem> computeHealth(List<ReportMapIndexItem> inventoryData,
                                                   List<ReportMapIndexItem> turnoverData,
                                                   String inventoryStep,
                                                   String turnoverStep) {
        return StepUtils.compare(turnoverData, inventoryData, (item, item2) -> {
            // item = 库存周转，item2 = 库存
            if (item == null){
                return null;
            }
            return InventoryHealthEnum.getByTurnover(item.getNumber());
        }, turnoverStep, inventoryStep, (item, item2) -> {
            // 取库存的值
            ReportItem copy = ReportItem.copy(item);
            copy.setNumber(item2.getNumber());
            copy.setValue(CommonUtils.getValue(copy.getNumber()));
            return copy;
        }, item -> {
            // 库存为空默认设置ZERO
            ReportItem copy = ReportItem.copy(item);
            copy.setNumber(BigDecimal.ZERO);
            copy.setValue(CommonUtils.getValue(copy.getNumber()));
            return copy;
        }, reportItem -> null);
    }



    /**
     * 统计未来3月的需求数据
     */
    private List<SopDemandReturnItem> dataProcess(List<SopDemandReturnItem> data,
                                                  Function<String, List<String>> dateMapping) {
        List<SopDemandReturnItem> ret = new ArrayList<>();
        if (ListUtils.isEmpty(data)) {
            return ret;
        }
        Map<String, SopDemandReturnItem> map = new HashMap<>();
        for (SopDemandReturnItem datum : data) {
            // 数据分组的key
            String key = CommonUtils.getKey(datum.getDimMap_());
            String yearMonth = datum.getYearMonth();
            // 遍历数据需要入的年月，例子：4月 -> 依次加入 1月，2月，3月的集合
            for (String targetYearMonth : dateMapping.apply(yearMonth)) {
                String tempKey = key + "@" + targetYearMonth;
                SopDemandReturnItem item = map.get(tempKey);
                if (item == null) {
                    item = new SopDemandReturnItem();
                    item.setYearMonth(targetYearMonth);
                    item.setSopBigFamily(datum.getSopBigFamily());
                    item.setUtilBusinessType(datum.getUtilBusinessType());
                    item.setDeviceType(datum.getDeviceType());
                    map.put(tempKey, item);
                    ret.add(item);
                } else {
                    item.setCoreNum(CommonUtils.add(item.getCoreNum(), datum.getCoreNum()));
                }
            }
        }
        return ret;
    }


    public QueryStep buildInventoryStep(String stepName, String depStepName1, String depStepName2,boolean needHide) {
        BiFunction<ReportItem, ReportItem, ReportItem> compareFunc = (item, item2) -> {
            ReportItem copy = ReportItem.copy(item);
            // 需求为空或者小于等于0即为【-】
            if (item.getNumber() != null && item.getNumber().compareTo(BigDecimal.ZERO) > 0) {
                // 库存/未来三个月均需求净增*30，如需求净增<=0，周转天数∞
                BigDecimal turnover_days = BigDecimal.valueOf(30).
                        multiply(ObjectUtils.defaultIfNull(item2.getNumber(), BigDecimal.ZERO)).
                        divide(item.getNumber(), RoundingMode.HALF_UP).
                        abs();
                copy.setNumber(turnover_days);
                copy.setValue(CommonUtils.getValue(copy.getNumber()));
            } else {
                copy.setNumber(null);
                copy.setValue(CommonUtils.getValue(null));
            }
            return copy;
        };
        Function<ReportItem, ReportItem> rightNullFunc = item -> {
            ReportItem copy = ReportItem.copy(item);
            copy.setNumber(null);
            copy.setValue(CommonUtils.getValue(null));
            return copy;
        };
        Function<ReportItem, ReportItem> leftNullFunc = item -> {
            ReportItem copy = ReportItem.copy(item);
            copy.setNumber(null);
            copy.setValue(CommonUtils.getValue(null));
            return copy;
        };

        QueryStep queryStep = StepUtils.buildCompare(stepName,stepName,
                depStepName1,depStepName1,
                depStepName2,depStepName2,
                compareFunc, rightNullFunc, leftNullFunc);
        if (needHide){
            queryStep.setMerge((iQueryData, iQueryData2) -> {});
        }else{
            queryStep.setMerge(getMerge());
        }

        return queryStep;
    }

    /**
     * 合并
     */
    private BiConsumer<List<IQueryData>, List<IQueryData>> getMerge() {
        return (iQueryData, iQueryData2) -> {
            // 合并处理
            StepUtils.merge(toChildren(iQueryData), toChildren(iQueryData2));
        };
    }



}
