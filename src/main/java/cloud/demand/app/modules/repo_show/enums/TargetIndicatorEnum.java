package cloud.demand.app.modules.repo_show.enums;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Lang;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 *  目标指标的枚举对象
 */
@Getter
public enum TargetIndicatorEnum {

    INVENTORY_TARGET("invTarget", "库存目标"),
    INVENTORY_RATIO_TARGET("invRatioTarget", "库存占比目标"),
    SALE_TARGET("saleTarget", "售卖净增目标"),
    P2PRATE_TARGET("p2pTarget", "端到端利用率目标"),
    RETURN_REUSE_TARGET("returnReuseTarget", "退回盘活目标"),
    INVENTORY_PRICE_TARGET("priceTarget", "库存金额目标"),
    //  给网络产品另外修改了一个
    OFFLINE_INVENTORY_PRICE_TARGET("offlinePriceTarget", "线下库存金额目标"),
    NOT_FOR_SALE_TARGET("notForSaleTarget", "非可售目标")
    ;

    private String key;
    private String name;

    TargetIndicatorEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

    /**
     * 数据库没有存key，只有name
     * 这里通过本枚举对象中定义的映射关系，产生<key,name>的map
     * 如果传入的names为空，则返回所有枚举对象的<key,name>map
     */
    public static List<Map<String, String>> getTargetMapByName(List<String> names){
        //  如果什么都不传，就生成全量的map
        if (ListUtils.isEmpty(names)){
            names.addAll(ListUtils.transform(TargetIndicatorEnum.values(), o -> o.getName()));
        }
        List<Map<String, String>> result = Lang.list();
        for (String name : names) {
            Map<String, String> item = new HashMap<>();
            String key = getKeyByName(name);
            if (StringUtils.isNotBlank(key)){
                item.put("key", key);
                item.put("name", name);
            }
            result.add(item);
        }
        return result;
    }

    /**
     * 通过指标名获取到映射的指标k
     */
    public static String getKeyByName(String name){
        if (StringUtils.isBlank(name)){
            return "";
        }
        for (TargetIndicatorEnum e : TargetIndicatorEnum.values()) {
            if (Objects.equals(e.getName(), name)){
                return e.getKey();
            }
        }
        return "";
    }

    /**
     * 通过k获取到映射的指标名
     */
    public static String getNameByKey(String key){
        if (StringUtils.isBlank(key)){
            return "";
        }
        for (TargetIndicatorEnum e : TargetIndicatorEnum.values()) {
            if (Objects.equals(e.getKey(), key)){
                return e.getName();
            }
        }
        return "";
    }

    /**
     * 获取指定目标指标集合的信息map
     * @param list  指定目标集合
     * @param unit  对应产品类型的单位
     * @param configs   从数据库查到的指标值
     * @return
     */
    public static List<Map<String, Object>> genTargetMap(List<TargetIndicatorEnum> list, String unit,
                                                         Map<String, BigDecimal> configs){
        List<Map<String, Object>> result = Lang.list();
        for (TargetIndicatorEnum e : list) {
            Map<String, Object> map = new HashMap<>();
            for (TargetIndicatorEnum value : TargetIndicatorEnum.values()) {
                if (Objects.equals(value, e)){
                    map.put("key", value.getKey());
                    map.put("name", value.getName());
                    map.put("unit", genIndicatorUnit(e, unit));
                    map.put("value", configs.get(value.getName()));
                }
            }
            result.add(map);
        }
        return result;
    }

    /**
     * 获取指定指标的单位
     * @param e     指定指标
     * @param unit  不同产品，此类单位不同
     * @return
     */
    public static String genIndicatorUnit(TargetIndicatorEnum e, String unit){
        switch (e){
            case INVENTORY_TARGET:
                return unit;
            case INVENTORY_RATIO_TARGET:
                return "%";
            case SALE_TARGET:
                return unit;
            case P2PRATE_TARGET:
                return "%";
            case RETURN_REUSE_TARGET:
                return unit;
            case INVENTORY_PRICE_TARGET:
                return "千万";
            case OFFLINE_INVENTORY_PRICE_TARGET:
                return "千万";
            case NOT_FOR_SALE_TARGET:
                return unit;
            default:
                return "";
        }
    }

    public static TargetIndicatorEnum getByName(String name){
        if (StringUtils.isBlank(name)){
            return null;
        }
        for (TargetIndicatorEnum e : TargetIndicatorEnum.values()) {
            if (Objects.equals(e.getName(), name)){
                return e;
            }
        }
        return null;
    }


}



