package cloud.demand.app.modules.repo_show.service;


import cloud.demand.app.modules.repo_show.req.QueryObsCostReq;
import cloud.demand.app.modules.repo_show.rsp.QueryObsCostRsp;

/**
 * 库存晾晒调用到的外部接口Service
 */
public interface ThirdPartService {

    /**
     * 调用plan的接口，获取网络CLB和EIP的数据并保存
     */
    void getAndSaveNetworkInvData(String statTime);

    /**
     * 调用obs的接口，根据设备类型等相关信息查询成本
     */
    QueryObsCostRsp.ObsData getAndSaveInventoryCostData(QueryObsCostReq req);

}
