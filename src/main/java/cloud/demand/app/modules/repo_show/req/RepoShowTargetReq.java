package cloud.demand.app.modules.repo_show.req;

import cloud.demand.app.web.model.common.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 查询全部指标的请求体
 */
@Data
public class RepoShowTargetReq {

    /** 开始年月 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    private Date start;

    /** 结束年月 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    private Date end;

    /** 产品类型 */
    private List<String> productType;

    /** 指标 */
    private List<String> indicator;

    /** 分页参数 */
    private Page page;

}
