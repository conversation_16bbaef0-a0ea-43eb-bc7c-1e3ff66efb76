package cloud.demand.app.modules.repo_show.model;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.util.Date;

/**
 * 预约单相关信息DTO
 */
@Data
public class YuyueOrderInfoDTO {

    /** 需求单号 */
    @Column("DetailCode")
    private String DetailCode;

    /** 可交付时间 */
    @Column("ActualDate")
    private Date actualDate;

    /** 初次需求时间 */
    @Column("PromisTime")
    private String promisTime;

    /** 最新需求时间 */
    @Column("ExpectDate")
    private String expectDate;
}
