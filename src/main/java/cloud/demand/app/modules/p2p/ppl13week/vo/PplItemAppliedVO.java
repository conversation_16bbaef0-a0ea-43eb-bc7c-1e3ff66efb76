package cloud.demand.app.modules.p2p.ppl13week.vo;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemAppliedDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PplItemAppliedVO extends PplItemAppliedDO {

    @RelatedColumn(localColumn = "ppl_id", remoteColumn = "ppl_id")
    private PplItemDO pplItem1;

    @RelatedColumn(localColumn = "ppl_id2", remoteColumn = "ppl_id")
    private PplItemDO pplItem2;

    public BigDecimal getPplId1TotalGpu() {
         return pplItem1 == null ? BigDecimal.ZERO :
                 (pplItem1.getTotalGpuNum() == null ? BigDecimal.ZERO : pplItem1.getTotalGpuNum());
    }

    public BigDecimal getPplId2TotalGpu() {
        return pplItem2 == null ? BigDecimal.ZERO :
                (pplItem2.getTotalGpuNum() == null ? BigDecimal.ZERO : pplItem2.getTotalGpuNum());
    }

    public Integer getPplId1TotalInstanceNum() {
        return pplItem1 == null ? 0 : pplItem1.getInstanceNum();
    }

    public Integer getPplId2TotalInstanceNum() {
        return pplItem2 == null ? 0 : pplItem2.getInstanceNum();
    }

}
