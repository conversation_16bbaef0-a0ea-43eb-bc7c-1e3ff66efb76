package cloud.demand.app.modules.p2p.longterm.controller.dto;

import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupRecordDO;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;

/**
 * 驳回相关信息
 */
@Data
public class LongtermGroupRejectInfoDTO {

    private String rejectReason;

    private String rejectUser;

    private String rejectTime;

    public static LongtermGroupRejectInfoDTO from(LongtermVersionGroupRecordDO recordDO) {
        LongtermGroupRejectInfoDTO rejectInfo = new LongtermGroupRejectInfoDTO();
        rejectInfo.setRejectReason(recordDO.getApproveNote());
        rejectInfo.setRejectUser(recordDO.getOperateUser());
        rejectInfo.setRejectTime(DateUtils.format(recordDO.getApproveEndTime()));
        return rejectInfo;
    }

}
