package cloud.demand.app.modules.p2p.ppl13week.vo;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cn.hutool.core.collection.CollectionUtil;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class PplInnerProcessVersionVO extends PplInnerProcessVersionDO {

    /**
     * 具体适用产品
     */
    private String singleProduct;

    private Boolean isAudit = Boolean.FALSE;

    /**
     * 对 ppl_inner_process_version 表中的 product 的字段值进行分割，分割符为 {@code ;}
     *
     * @param processVersionDO 包含 ppl_inner_process_version 表中的 product 的字段值
     * @return 产品列表
     */
    public static List<String> splitProductFieldToProductList(PplInnerProcessVersionDO processVersionDO) {
        if (processVersionDO == null || StringUtils.isBlank(processVersionDO.getProduct())) {
            return new ArrayList<>();
        }
        String[] array = processVersionDO.getProduct().split(";");
        return CollectionUtil.newArrayList(array);
    }

}
