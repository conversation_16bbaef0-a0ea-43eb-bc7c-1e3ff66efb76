package cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao;

import lombok.Getter;

import java.util.Objects;

/**
 * 预扣单状态
 */
@Getter
public enum YunxiaoReservationStatusEnum {

    /**
     * 待审批
     */
    APPROVAL_PENDING("APPROVAL_PENDING", "待审批"),

    /**
     * 待处理
     */
    WAITING("WAITING", "待处理"),

    /**
     * 处理中
     */
    CREATE_PENDING("CREATE_PENDING", "处理中"),

    /**
     * 全部创建
     */
    CREATED("CREATED", "全部创建"),

    /**
     * 部分创建
     */
    PARTIAL_CREATED("PARTIAL_CREATED", "部分创建"),

    /**
     * 销毁中
     */
    DESTROY_PENDING("DESTROY_PENDING", "销毁中"),

    /**
     * 销毁
     */
    DESTROYED("DESTROYED", "销毁"),

    /**
     * 处理失败
     */
    FAILED("FAILED", "处理失败"),

    /**
     * 忽略
     */
    IGNORED("IGNORED", "忽略"),

    /**
     * 拒绝
     */
    REJECTED("REJECTED", "拒绝"),

    /**
     * 主动关闭
     */
    CLOSED("CLOSED", "主动关闭"),

    ;

    final private String code;
    final private String name;

    YunxiaoReservationStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static YunxiaoReservationStatusEnum getByCode(String code) {
        for (YunxiaoReservationStatusEnum e : YunxiaoReservationStatusEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        YunxiaoReservationStatusEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}