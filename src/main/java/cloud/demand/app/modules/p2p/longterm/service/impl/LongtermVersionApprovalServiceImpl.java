package cloud.demand.app.modules.p2p.longterm.service.impl;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermGroupDTO;
import cloud.demand.app.modules.p2p.longterm.controller.req.OperateLongtermAuditReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.QueryLongtermAuditDictReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.QueryLongtermAuditListReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.SubmitLongtermAuditReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.WithdrawLongtermSubmitReq;
import cloud.demand.app.modules.p2p.longterm.controller.resp.OperateLongtermAuditResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.QueryLongtermAuditDictResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.QueryLongtermAuditListResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.SubmitLongtermAuditResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.WithdrawLongtermSubmitResp;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupRecordDO;
import cloud.demand.app.modules.p2p.longterm.entity.vo.LongtermVersionGroupWithVersionVO;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupStatusEnum;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionStatusEnum;
import cloud.demand.app.modules.p2p.longterm.service.LongtermGroupItemService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermVersionApprovalService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermVersionGroupService;
import cloud.demand.app.modules.rrp_remake.enums.AuthRoleEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
public class LongtermVersionApprovalServiceImpl implements LongtermVersionApprovalService {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private LongtermVersionGroupService versionGroupService;
    @Resource
    private LongtermGroupItemService longtermGroupItemService;
    @Resource
    private PermissionService permissionService;

    @Resource
    private DictService dictService;

    static ExecutorService executor = Executors.newFixedThreadPool(8);

    @Override
    public QueryLongtermAuditListResp queryLongtermAuditList(QueryLongtermAuditListReq req) {

        // 获取列表数据权限
        WhereSQL dataAuth = getDataAuth(true);
        if (dataAuth == null){
            return new QueryLongtermAuditListResp();
        }

        // 2. 查询审批列表
        WhereSQL where = req.toWhereSQLForLongtermVersionGroupDO();
        where.and(dataAuth);
        where.addOrderBy("id desc");

        PageData<LongtermVersionGroupWithVersionVO> page = demandDBHelper.getPage(LongtermVersionGroupWithVersionVO.class,
                req.getPage().getPageIndex(), req.getPage().getSize(),
                where.getSQL(), where.getParams());

        QueryLongtermAuditListResp resp = new QueryLongtermAuditListResp();
        resp.setTotal(page.getTotal());
        resp.setData(ListUtils.transform(page.getData(), LongtermGroupDTO::trans));
        return resp;
    }

    /**
     * 获取当前用户的中长期数据权限
     * @return
     */
    @Override
    public WhereSQL getDataAuth(Boolean isAddIndustryDataFollower){
        WhereSQL whereSQL = new WhereSQL();
        String username = LoginUtils.getUserNameWithSystem();
        if (permissionService.checkIsAdmin(username)) {
            whereSQL.and("1 = 1");
        }else {
            List<String> auditRoleList = LongtermVersionGroupStatusEnum.getAuditRoleList();
            if (isAddIndustryDataFollower){
                auditRoleList.add(IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode());
            }
            List<IndustryDemandAuthDO> authList = demandDBHelper.getAll(IndustryDemandAuthDO.class,
                    "where role in (?) and user_name = ?", auditRoleList, username);

            for (IndustryDemandAuthDO industryDemandAuthDO : authList) {
                if (StringUtils.isBlank(industryDemandAuthDO.getIndustry())){
                    continue;
                }
                if (industryDemandAuthDO.getRole().equals(IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode())
                && !industryDemandAuthDO.getIsAllWarZone()){
                    continue;
                }
                WhereSQL roleSql = new WhereSQL();
                List<String> industryList = new ArrayList<>(Arrays.asList(industryDemandAuthDO.getIndustry().split(";")));
                // 如果只有内部业务部  还需要判断产品权限
                if (industryList.size() == 1 && industryList.get(0).equals("内部业务部")){
                    roleSql.and("industry_dept = ? and biz_group in (?)", industryList.get(0),
                            new ArrayList<>(Arrays.asList(industryDemandAuthDO.getProduct().split(";"))));
                }else {
                    // 如果有多个行业  只判断行业权限
                    roleSql.and("industry_dept in (?)", industryList);
                }
                whereSQL.or(roleSql);
            }
        }

        // 没有任何权限
        if (StringUtils.isBlank(whereSQL.getSQL().trim())) {
            whereSQL.and("1 = 2");
        }

        return whereSQL;
    }

    @Override
    public QueryLongtermAuditDictResp queryLongtermAuditDict(QueryLongtermAuditDictReq req) {
        QueryLongtermAuditDictResp resp = new QueryLongtermAuditDictResp();
        resp.setStatusDict(ListUtils.transform(LongtermVersionGroupStatusEnum.values(),
                o -> new QueryLongtermAuditDictResp.KeyValue(o.getCode(), o.getName())));

        List<LongtermVersionDO> versions = demandDBHelper.getAll(LongtermVersionDO.class,
                "where status != ? group by version_code", LongtermVersionStatusEnum.CREATED.getCode());
        List<LongtermVersionGroupDO> groups = demandDBHelper.getAll(LongtermVersionGroupDO.class,
                "group by industry_dept,biz_group");

        resp.setVersionCodeDict(ListUtils.transform(versions, LongtermVersionDO::getVersionCode).stream().distinct().collect(Collectors.toList()));
        ListUtils.sortDescNullLast(resp.getVersionCodeDict(), o -> o);

        resp.setIndustryDeptDict(ListUtils.transform(groups, LongtermVersionGroupDO::getIndustryDept).stream().distinct().collect(Collectors.toList()));
        ListUtils.sortAscNullLast(resp.getIndustryDeptDict(), o -> o);

        resp.setBizGroupDict(ListUtils.filter(ListUtils.transform(groups, LongtermVersionGroupDO::getBizGroup), StringTools::isNotBlank)
                .stream().distinct().collect(Collectors.toList()));
        ListUtils.sortAscNullLast(resp.getBizGroupDict(), o -> o);

        Set<Integer> years = ListUtils.toSet(versions, LongtermVersionDO::getDemandBeginYear);
        years.addAll(ListUtils.toSet(versions, LongtermVersionDO::getDemandEndYear));
        resp.setYearRangeDict(ListUtils.transform(years, o -> o));
        ListUtils.sortAscNullLast(resp.getYearRangeDict(), o -> o);

        return resp;
    }

    @Override
    @Synchronized(namespace = "operateGroup", keyScript = "args[0].groupId", customExceptionMessage = "其他用户正在操作当前分组，请稍后再试")
    @Transactional(value = "demandTransactionManager")
    public SubmitLongtermAuditResp submitLongtermAudit(SubmitLongtermAuditReq req) {
        // 1. 查询版本相关信息
        LongtermVersionGroupDO groupDO = demandDBHelper.getOne(LongtermVersionGroupDO.class, "where id=?", req.getGroupId());
        if (groupDO == null) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        LongtermVersionDO versionDO = demandDBHelper.getOne(LongtermVersionDO.class, "where version_code=?", groupDO.getVersionCode());
        if (versionDO == null) {
            throw new WrongWebParameterException("版本号:" + groupDO.getVersionCode() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        LongtermVersionGroupRecordDO latestRecordDO = demandDBHelper.getOne(LongtermVersionGroupRecordDO.class,
                "where version_group_id=? order by id desc", req.getGroupId());
        if (latestRecordDO == null) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "还没有初始化，请联系开发进行初始化");
        }
        if (!Objects.equals(latestRecordDO.getId(), req.getGroupRecordId())) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "的记录Id:" + req.getGroupRecordId() + "已不是最新的recordId，不允许修改，请刷新页面后重试");
        }

        // 2. 检查提交人有没有权限
        if (!isAllowApprove(groupDO,LoginUtils.getUserNameWithSystem())) {
            throw new BizException("当前用户没有提交权限,如需添加权限,请联系kaijiazhang");
        }

        // 3. 检查提交审批的状态
        String groupStatus = latestRecordDO.getVersionGroupNodeCode();
        LongtermVersionGroupStatusEnum statusEnum = LongtermVersionGroupStatusEnum.getByCode(groupStatus);
        if (statusEnum == null) {
            throw new BizException("未知的分组状态：" + groupStatus + "，请联系开发处理");
        }
        if (!LongtermVersionGroupStatusEnum.isInSubmitStatus(statusEnum)) {
            throw new BizException("当前分组状态为：" + statusEnum.getName() + "，不允许提交审批，请刷新页面后重试");
        }
        LongtermVersionGroupStatusEnum nextStatus = LongtermVersionGroupStatusEnum.getNextAuditStatus(groupDO,statusEnum, false);
        if (nextStatus == null) {
            throw new BizException("当前分组状态为：" + statusEnum.getName() + "的下一个审批节点未配置，请联系开发处理");
        }

        // 4. 处理提交
        latestRecordDO.setOperateUser(LoginUtils.getUserNameWithSystem());
        latestRecordDO.setApproveEndTime(new Date());
        latestRecordDO.setApproveResult("提交审批");
        latestRecordDO.setApproveNote("");
        demandDBHelper.update(latestRecordDO);

        LongtermVersionGroupRecordDO nextRecordDO = fromPreviousGroupRecord(latestRecordDO, nextStatus);
        demandDBHelper.insert(nextRecordDO);

        longtermGroupItemService.copyGroupRecordItem(latestRecordDO.getId(), nextRecordDO.getId());

        groupDO.setStatus(nextStatus.getCode());
        groupDO.setCurrentProcessor(getNodeCodeProcessor(groupDO.getStatus(),groupDO.getIndustryDept(),groupDO.getBizGroup()));
        demandDBHelper.update(groupDO);

        SubmitLongtermAuditResp resp = new SubmitLongtermAuditResp();
        resp.setSuccess(true);
        resp.setErrMsg("");

        if (resp.getSuccess()) {
            versionGroupService.sendTodoAndMoa(groupDO);
        }
        return resp;
    }

    @Override
    @Synchronized(namespace = "operateGroup", keyScript = "args[0].groupId", customExceptionMessage = "其他用户正在操作当前分组，请稍后再试")
    @Transactional(value = "demandTransactionManager")
    public WithdrawLongtermSubmitResp withdrawLongtermSubmit(WithdrawLongtermSubmitReq req) {
        // 1. 查询版本相关信息
        LongtermVersionGroupDO groupDO = demandDBHelper.getOne(LongtermVersionGroupDO.class, "where id=?", req.getGroupId());
        if (groupDO == null) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        LongtermVersionDO versionDO = demandDBHelper.getOne(LongtermVersionDO.class, "where version_code=?", groupDO.getVersionCode());
        if (versionDO == null) {
            throw new WrongWebParameterException("版本号:" + groupDO.getVersionCode() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        LongtermVersionGroupRecordDO latestRecordDO = demandDBHelper.getOne(LongtermVersionGroupRecordDO.class,
                "where version_group_id=? order by id desc", req.getGroupId());
        if (latestRecordDO == null) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "还没有初始化，请联系开发进行初始化");
        }

        // 2. 检查状态是否允许撤回
        LongtermVersionGroupStatusEnum statusEnum = LongtermVersionGroupStatusEnum.getByCode(latestRecordDO.getVersionGroupNodeCode());
        if (!LongtermVersionGroupStatusEnum.canWithdrawSubmit(statusEnum)) {
            throw new WrongWebParameterException("当前分组状态为："
                    + (statusEnum == null ? latestRecordDO.getVersionGroupNodeCode() : statusEnum.getName())
                    + "，不允许撤回，请刷新页面后重试");
        }

        // 3. 检查权限
        String username = LoginUtils.getUserNameWithSystem();
        String nodeCodeProcessor = getNodeCodeProcessor(LongtermVersionGroupStatusEnum.CREATE.getCode(), groupDO.getIndustryDept(), groupDO.getBizGroup());
        if (isAllowApprove(groupDO,LoginUtils.getUserNameWithSystem())) {
            throw new BizException("当前用户没有撤回权限,如需添加权限,请联系kaijiazhang");
        }

        // 4. 撤回
        latestRecordDO.setOperateUser(username);
        latestRecordDO.setApproveEndTime(new Date());
        latestRecordDO.setApproveResult("撤回提交");
        latestRecordDO.setApproveNote("");
        demandDBHelper.update(latestRecordDO);

        LongtermVersionGroupRecordDO nextRecordDO = fromPreviousGroupRecord(latestRecordDO, LongtermVersionGroupStatusEnum.IN_SUBMIT);
        demandDBHelper.insert(nextRecordDO);

        longtermGroupItemService.copyGroupRecordItem(latestRecordDO.getId(), nextRecordDO.getId());

        groupDO.setStatus(LongtermVersionGroupStatusEnum.IN_SUBMIT.getCode());
        groupDO.setCurrentProcessor(nodeCodeProcessor);
        demandDBHelper.update(groupDO);

        // 处理完统一回调代办中心 完成任务。
        executor.execute(() -> {
            versionGroupService.completeTodo(req.getGroupId(),statusEnum.getCode(),username);
        });

        WithdrawLongtermSubmitResp resp = new WithdrawLongtermSubmitResp();
        resp.setSuccess(true);
        resp.setErrMsg("");
        return resp;
    }

    @Override
    public String getNodeCodeProcessor(String nodeCode,String industryDept,String bizGroup) {
        LongtermVersionGroupStatusEnum node = LongtermVersionGroupStatusEnum.getByCode(nodeCode);
        if (node == null){
            throw new BizException("未知的分组状态：" + nodeCode + "，请联系开发处理");
        }
        if (StringUtils.isBlank(node.getAuditRole())){
            return "";
        }

        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("role = ?",node.getAuditRole());
        if (StringUtils.isNotBlank(industryDept)){
            whereSQL.and("industry like ?", "%" + industryDept + "%");
        }
        if (StringUtils.isNotBlank(bizGroup)){
            whereSQL.and("product like ? ","%" + bizGroup + "%");
        }
        List<IndustryDemandAuthDO> authList = demandDBHelper.getAll(IndustryDemandAuthDO.class, whereSQL.getSQL(),whereSQL.getParams());
        if (authList.isEmpty()){
            return "";
        }
        return authList.stream().map(IndustryDemandAuthDO::getUserName).distinct().collect(Collectors.joining(";"));
    }

    @Override
    @Synchronized(namespace = "operateGroup", keyScript = "args[0].groupId", customExceptionMessage = "其他用户正在操作当前分组，请稍后再试")
    @Transactional(value = "demandTransactionManager")
    public OperateLongtermAuditResp operateLongtermAudit(OperateLongtermAuditReq req) {

        // 如果moa回调的 则用moa的操作用户 否则拿鉴权信息的操作用户
        String operateUser = StringUtils.isNotBlank(req.getOperateUserByMoa()) ? req.getOperateUserByMoa() : LoginUtils.getUserNameWithSystem();
        // 1. 查询版本相关信息
        LongtermVersionGroupDO groupDO = demandDBHelper.getOne(LongtermVersionGroupDO.class, "where id=?", req.getGroupId());
        if (groupDO == null) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        LongtermVersionDO versionDO = demandDBHelper.getOne(LongtermVersionDO.class, "where version_code=?", groupDO.getVersionCode());
        if (versionDO == null) {
            throw new WrongWebParameterException("版本号:" + groupDO.getVersionCode() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        LongtermVersionGroupRecordDO latestRecordDO = demandDBHelper.getOne(LongtermVersionGroupRecordDO.class,
                "where version_group_id=? order by id desc", req.getGroupId());
        if (latestRecordDO == null) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "还没有初始化，请联系开发进行初始化");
        }
        if (!Objects.equals(latestRecordDO.getId(), req.getGroupRecordId())) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "的记录Id:" + req.getGroupRecordId() + "已不是最新的recordId，不允许修改，请刷新页面后重试");
        }

        // 2. 审批人权限校验
        if (!isAllowApprove(groupDO,operateUser)) {
            throw new BizException("当前用户没有审批权限,如需添加权限,请联系kaijiazhang");
        }

        // 3. 检查审批状态
        String groupStatus = latestRecordDO.getVersionGroupNodeCode();
        LongtermVersionGroupStatusEnum statusEnum = LongtermVersionGroupStatusEnum.getByCode(groupStatus);
        if (statusEnum == null) {
            throw new BizException("未知的分组状态：" + groupStatus + "，请联系开发处理");
        }
        if (!LongtermVersionGroupStatusEnum.isInAuditStatus(statusEnum)) {
            throw new BizException("当前分组状态为：" + statusEnum.getName() + "，不允许审批，请刷新页面后重试");
        }
        LongtermVersionGroupStatusEnum nextStatus = LongtermVersionGroupStatusEnum.getNextAuditStatus(groupDO,statusEnum, !req.getApproveResult());
        if (nextStatus == null) {
            throw new BizException("当前分组状态为：" + statusEnum.getName() + "的下一个审批节点未配置，请联系开发处理");
        }

        // 4. 处理审批，需要创建一个新的record的数据
        latestRecordDO.setOperateUser(operateUser);
        latestRecordDO.setApproveEndTime(new Date());
        latestRecordDO.setApproveResult(req.getApproveResult() ? "审批通过" : "审批驳回");
        latestRecordDO.setApproveNote(req.getApproveNote());
        demandDBHelper.update(latestRecordDO);

        LongtermVersionGroupRecordDO nextRecordDO = fromPreviousGroupRecord(latestRecordDO, nextStatus);
        demandDBHelper.insert(nextRecordDO);

        longtermGroupItemService.copyGroupRecordItem(latestRecordDO.getId(), nextRecordDO.getId());

        groupDO.setStatus(nextStatus.getCode());
        groupDO.setCurrentProcessor(getNodeCodeProcessor(groupDO.getStatus(),groupDO.getIndustryDept(),groupDO.getBizGroup()));
        demandDBHelper.update(groupDO);

        OperateLongtermAuditResp resp = new OperateLongtermAuditResp();
        resp.setSuccess(true);
        resp.setErrMsg("");

        if (resp.getSuccess()) {
            if (req.getApproveResult()) {
                // 审批通过 - 仅当分组是 行业总监审批/行业GM审批 时发送MOA
                if (LongtermVersionGroupStatusEnum.isMoaNodeList(groupDO.getStatus())) {
                    versionGroupService.sendTodoAndMoa(groupDO);
                }
            } else {
                // 审批驳回
                Map<String, Object> templateParams = new HashMap<>();
                templateParams.put("operateUser",operateUser);
                templateParams.put("rejectReason",req.getApproveNote());
                dictService.eventNotice(CrpEventEnum.long_term_reject_notice.getCode(),null,null,
                        templateParams,getNodeCodeProcessor(LongtermVersionGroupStatusEnum.CREATE.getCode(),groupDO.getIndustryDept(),groupDO.getBizGroup()));
            }
        }

        // 处理完统一回调代办中心 完成任务。
        executor.execute(() -> {
            versionGroupService.completeTodo(req.getGroupId(),groupStatus,operateUser);
        });
        return resp;
    }

    private LongtermVersionGroupRecordDO fromPreviousGroupRecord(LongtermVersionGroupRecordDO previousRecord,
                                                                 LongtermVersionGroupStatusEnum nextStatus) {
        LongtermVersionGroupRecordDO record = new LongtermVersionGroupRecordDO();
        record.setVersionCode(previousRecord.getVersionCode());
        record.setVersionGroupId(previousRecord.getVersionGroupId());
        record.setVersionGroupNodeCode(nextStatus.getCode());
        record.setVersionGroupName(nextStatus.getName());
        record.setApproveStartTime(new Date());
        return record;
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void allGroupToStatus(String versionCode, LongtermVersionGroupStatusEnum targetStatus, String reason) {
        LongtermVersionDO versionDO = demandDBHelper.getOne(LongtermVersionDO.class, "where version_code=?", versionCode);
        if (versionDO == null) {
            throw new WrongWebParameterException("版本号:" + versionCode + "不存在，可能已经被删除，请回退到中长期版本列表");
        }

        List<LongtermVersionGroupDO> groupDOList = demandDBHelper.getAll(LongtermVersionGroupDO.class,
                "where version_code=?", versionCode);

        for (LongtermVersionGroupDO groupDO : groupDOList) {
            LongtermVersionGroupStatusEnum curStatus = LongtermVersionGroupStatusEnum.getByCode(groupDO.getStatus());
            if (LongtermVersionGroupStatusEnum.canSetStatusTo(curStatus, targetStatus)) {
                LongtermVersionGroupRecordDO latestRecordDO = demandDBHelper.getOne(LongtermVersionGroupRecordDO.class,
                        "where version_group_id=? order by id desc", groupDO.getId());
                if (latestRecordDO == null) {
                    throw new WrongWebParameterException("分组Id:" + groupDO.getId() + "还没有初始化，请联系开发进行初始化");
                }

                latestRecordDO.setOperateUser(LoginUtils.getUserNameWithSystem());
                latestRecordDO.setApproveEndTime(new Date());
                latestRecordDO.setApproveResult("自动流转");
                latestRecordDO.setApproveNote(reason == null ? "" : reason);
                demandDBHelper.update(latestRecordDO);

                LongtermVersionGroupRecordDO nextRecordDO = fromPreviousGroupRecord(latestRecordDO, targetStatus);
                demandDBHelper.insert(nextRecordDO);

                longtermGroupItemService.copyGroupRecordItem(latestRecordDO.getId(), nextRecordDO.getId());

                groupDO.setStatus(targetStatus.getCode());
                groupDO.setCurrentProcessor(getNodeCodeProcessor(groupDO.getStatus(),groupDO.getIndustryDept(),groupDO.getBizGroup()));
                demandDBHelper.update(groupDO);

                // 审批通过 - 仅当分组是 行业总监审批/行业GM审批 时发送MOA
                if (LongtermVersionGroupStatusEnum.isMoaNodeList(groupDO.getStatus())) {
                    versionGroupService.sendTodoAndMoa(groupDO);
                }
            } else {
                 // 忽略
            }
        }
    }

    @Override
    public Boolean isAllowApprove(LongtermVersionGroupDO groupDO,String operateUser) {
        String currentProcessor = LongtermGroupDTO.mergeProcessor(groupDO.getCurrentProcessor(),
                getNodeCodeProcessor(groupDO.getStatus(), groupDO.getIndustryDept(),
                        groupDO.getBizGroup()));
        return currentProcessor.contains(operateUser);
    }
}
