package cloud.demand.app.modules.p2p.product_demand.entity;

// package a.b.c;

import cloud.demand.app.common.BaseUserDO;
import cloud.demand.app.modules.p2p.product_demand.Constant;
import cloud.demand.app.modules.p2p.product_demand.enums.ProductDemandGroupStatusEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("product_demand_version_group")
public class ProductDemandVersionGroupDO extends BaseUserDO {

    /**
     * 状态<br/>Column: [status]
     */
    @Column(value = "status")
    private String status;

    /**
     * 需求版本号<br/>Column: [demand_version]
     */
    @Column(value = "demand_version")
    private String demandVersion;

    /**
     * 规划产品<br/>Column: [plan_product]
     */
    @Column(value = "plan_product")
    private String planProduct;

    /**
     * 需求初次提交时间<br/>Column: [submit_time]
     */
    @Column(value = "submit_time")
    private Date submitTime;

    /**
     * 需求初次提交人<br/>Column: [submit_user]
     */
    @Column(value = "submit_user")
    private String submitUser;

    /**
     * 需求重新提交时间<br/>Column: [resubmit_time]
     */
    @Column(value = "resubmit_time")
    private Date resubmitTime;

    /**
     * 需求重新提交人<br/>Column: [resubmit_user]
     */
    @Column(value = "resubmit_user")
    private String resubmitUser;

    /**
     * 继承自哪个版本号<br/>Column: [inherit_version]
     */
    @Column(value = "inherit_version")
    private String inheritVersion;

    /**
     * 当前处理人<br/>Column: [current_handler]
     */
    @Column(value = "current_handler")
    private String currentHandler;

    /**
     * 进入审批流的次数<br/>Column: [exec_count]
     */
    @Column(value = "exec_count")
    private Integer execCount;

    public static ProductDemandVersionGroupDO inherit(ProductDemandVersionGroupDO source, String demandVersion) {
        ProductDemandVersionGroupDO dest = new ProductDemandVersionGroupDO();
        dest.setStatus(ProductDemandGroupStatusEnum.SYSTEM_INIT.getCode());
        dest.setDemandVersion(demandVersion);
        dest.setPlanProduct(source.getPlanProduct());
        dest.setInheritVersion(source.getDemandVersion());
        dest.setCreator(Constant.SYSTEM_INIT_CREATOR);
        dest.setUpdater(Constant.SYSTEM_INIT_CREATOR);
        return dest;

    }
}