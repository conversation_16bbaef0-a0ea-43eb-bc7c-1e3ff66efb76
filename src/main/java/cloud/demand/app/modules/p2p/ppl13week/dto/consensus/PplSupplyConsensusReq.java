package cloud.demand.app.modules.p2p.ppl13week.dto.consensus;

import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.orm.SingleWhere;
import cloud.demand.app.common.utils.orm.WhereType;
import cloud.demand.app.common.utils.orm.convert.YearMonthStringListRangeAccurateToDayConvert;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplSupplyConsensusDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordItemDO;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class PplSupplyConsensusReq {

//    /**
//     * 行业版本节点名称  需求沟通 / 其他节点名称  下发共识数据接口用
//     */
//    private String versionCurrentNode;
//
//    /**
//     * 行业版本id
//     */
//    private Long industryVersionId;

    @NotBlank
    @SingleWhere(type = WhereType.EQUALS, tableClass = PplSupplyConsensusDO.class, columnName = "industry_dept")
    private String industryDept;

    @NotBlank
    @SingleWhere(type = WhereType.EQUALS, tableClass = PplSupplyConsensusDO.class, columnName = "product")
    private String product;

    @NotBlank
    @SingleWhere(type = WhereType.EQUALS, tableClass = PplSupplyConsensusDO.class, columnName = "version_code")
    private String versionCode;

    /**
     * 需求年月 格式yyyy-MM
     */
    @SingleWhere(type = WhereType.INTERVAL_ALL_CLOSE, tableClass = PplVersionGroupRecordItemDO.class,
            columnName = "begin_buy_date", convert = YearMonthStringListRangeAccurateToDayConvert.class)
    private List<String> demandYearMonth;

    /**
     * 实例类型
     */
    @SingleWhere(type = WhereType.IN, tableClass = PplVersionGroupRecordItemDO.class, columnName = "instance_type")
    private List<String> instanceType;

    /**
     * gpu卡型
     */
    @SingleWhere(type = WhereType.IN, tableClass = PplVersionGroupRecordItemDO.class, columnName = "gpu_type")
    private List<String> gpuType;

    /**
     * 地域
     */
    @SingleWhere(type = WhereType.IN, tableClass = PplVersionGroupRecordItemDO.class, columnName = "region_name")
    private List<String> regionName;

    /**
     * 客户简称
     */
    @SingleWhere(type = WhereType.IN, tableClass = PplOrderDO.class, columnName = "customer_short_name")
    private List<String> customerShortName;

    /**
     * 满足方式
     */
    @SingleWhere(type = WhereType.IN, tableClass = PplSupplyConsensusDO.class, columnName = "match_type")
    private List<String> matchType;

    /**
     * 共识状态
     */
    @SingleWhere(type = WhereType.IN, tableClass = PplSupplyConsensusDO.class, columnName = "consensus_status")
    private List<String> consensusStatus;

    /**
     * 战区
     */
    @SingleWhere(type = WhereType.IN, tableClass = PplOrderDO.class, columnName = "war_zone")
    private List<String> warZone;

    @SingleWhere(type = WhereType.IN, tableClass = PplOrderDO.class, columnName = "customer_uin")
    private List<String> customerUin;

    /**
     * 项目名称
     */
    @SingleWhere(type = WhereType.IN, tableClass = PplVersionGroupRecordItemDO.class, columnName = "project_name")
    private List<String> projectName;

    /**
     * 可用区
     */
    @SingleWhere(type = WhereType.IN, tableClass = PplVersionGroupRecordItemDO.class, columnName = "zone_name")
    private List<String> zoneName;

    /**
     * 实例规格
     */
    @SingleWhere(type = WhereType.IN, tableClass = PplVersionGroupRecordItemDO.class, columnName = "instance_model")
    private List<String> instanceModel;

    /**
     * 需求类型
     */
    @SingleWhere(type = WhereType.IN, tableClass = PplVersionGroupRecordItemDO.class, columnName = "demand_type")
    private List<String> demandType;

    /**
     * 架构师
     */
    @SingleWhere(type = WhereType.IN, tableClass = PplOrderDO.class, columnName = "submit_user")
    private List<String> submitUser;

    /**
     * 实际处理人
     */
    @SingleWhere(type = WhereType.IN, tableClass = PplSupplyConsensusDO.class, columnName = "operate_user")
    private List<String> operateUser;

    @SingleWhere(type = WhereType.IN, tableClass = PplSupplyConsensusDO.class, columnName = "ppl_id")
    private List<String> pplId;

    /**
     * 境内外
     */
    private String customhouseTitle;

    private Boolean returnEmpty;

    private String user;

    private boolean needAuth;

    public WhereContent toWhereContent() {
        return new WhereContent()
                .addAndAllSingleWhereAnnotationField(this)
                .addAnd(" ppl_supply_consensus.deleted = 0 ")
                .addAnd("version_group_record_id IN (\n"
                        + "    SELECT MAX(id) FROM `ppl_version_group_record`\n"
                        + "    WHERE deleted=0\n"
                        + "  AND version_group_id IN ("
                        + "SELECT id FROM `ppl_version_group` WHERE deleted=0 and version_code = ?)\n"
                        + "    GROUP BY version_group_id\n"
                        + "    )", versionCode);
    }

}
