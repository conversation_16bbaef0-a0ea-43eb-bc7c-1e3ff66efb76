package cloud.demand.app.modules.p2p.ppl13week.dto.version;

import cloud.demand.app.common.excel.GroupConstant;
import cloud.demand.app.common.excel.core.ParseContext;
import cloud.demand.app.common.excel.core.annotation.DotExcelEntity;
import cloud.demand.app.common.excel.core.annotation.DotExcelField;
import cloud.demand.app.common.excel.core.checker.ExcelColumnValueChecker;
import cloud.demand.app.common.excel.core.checker.ExcelRowDataAfterConvertChecker;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.IGetter;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.p2p.ppl13week.dto.PplItemJoinOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.dict.InstanceModelInfoDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplDraftItemJoinDraftOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.QueryInfoByUinRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp.ErrorMessage;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDraftDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDiskTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDraftService;
import cloud.demand.app.modules.p2p.ppl13week.service.excel.AbstractGeneralPplExcelParseService.CheckYunxiaoReq;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.dbhelper.utils.DOInfoReader;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.tencent.rainbow.util.StringUtil;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.SneakyThrows;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import yunti.boot.exception.BizException;

/**
 * 各行业 * 各产品的 excel导入
 * 此方法中 ExcelProperty的index不做具体意义; 用该注解仅为了获取 value 即excel列名称
 */
@Data
@DotExcelEntity
public class PplImportExcelDTO {

    private static String null2empty(BigDecimal a) {
        return a == null ? "" : a.setScale(3, RoundingMode.HALF_UP).toString();
    }

    private static String null2empty(LocalTime a) {
        return a == null ? "" : DateUtils.format(a, "HH:mm");
    }

    private static String null2empty(LocalDate a) {
        return a == null ? "" : DateUtils.format(a, "yyyy-MM-dd");
    }

    private static String null2empty(Integer a) {
        return a == null ? "" : a.toString();
    }

    private <K> ExcelProperty getEP(IGetter<K> fn) {
        return ORMUtils.getAnnotationByGetter(ExcelProperty.class, fn);
    }

    private <K> String getColName(IGetter<K> fn) {
        return getEP(fn).value()[0];
    }

    private <K> int getColIndex(IGetter<K> fn) {
        return getEP(fn).index() + 1;
    }

    private <K> ErrorMessage makeError(int raw, IGetter<K> fn, String message) {
        return new ErrorMessage(raw, getColIndex(fn), getColName(fn), message);
    }

    public <K> ErrorMessage makeErrorIfTure(int raw, IGetter<K> fn, boolean condition, String msg) {
        return condition ? makeError(raw, fn, msg) : null;
    }

    /**
     * 只支持 String 的方法
     */
    public <K> ErrorMessage makeErrorIfBlank(int raw, IGetter<K> fn) {

        Field declaredField = ORMUtils.getFiledByGetter(fn);
        Object value = DOInfoReader.getValue(declaredField, this);
        String colName = getColName(fn);
        if (value == null) {
            return makeError(raw, fn, colName + "为空");
        }
        if (value instanceof String) {
            value = StringUtils.trimWhitespace((String) value);
            if (Strings.isBlank((String) value)) {
                return makeError(raw, fn, colName + "为空");
            }
        } else {
            throw Lang.makeThrow("不支持类型");
        }
        return null;
    }

    /**
     * 首先会判断空值, 如果有空值直接返回空的 error
     *
     * 值包不含在 all 中， 返回 error
     *
     * 包含返回 null
     */
    public <K> ErrorMessage makeErrorIfNotContain(int raw, IGetter<K> fn, List<String> all) {

        ErrorMessage errorMessage = makeErrorIfBlank(raw, fn);
        if (errorMessage != null) {
            return errorMessage;
        }

        Field declaredField = ORMUtils.getFiledByGetter(fn);
        Object value = DOInfoReader.getValue(declaredField, this);
        if (value instanceof String) {
            value = StringUtils.trimWhitespace((String) value);
            if (!all.contains((String) value)) {
                String colName = getColName(fn);
                String msg = colName + "出错:  [" + value + "] 不在下面范围 " + all + " 中";
                if (Lang.isEmpty(all)) {
                    msg = colName + "出错:  不可以填 [" + value + "] ";
                }
                return makeError(raw, fn, msg);
            }
        } else {
            throw Lang.makeThrow("不支持类型");
        }
        return null;
    }

    /**
     * 首先会判断空值, 如果有空值直接返回空的 error
     *
     * 值包不含在 all 中， 返回 error
     *
     * 包含返回 null
     */
    public <K> ErrorMessage makeErrorIfNotContain(int raw, IGetter<K> fn, List<String> all, String rangeMsg) {

        ErrorMessage errorMessage = makeErrorIfBlank(raw, fn);
        if (errorMessage != null) {
            return errorMessage;
        }

        Field declaredField = ORMUtils.getFiledByGetter(fn);
        Object value = DOInfoReader.getValue(declaredField, this);
        if (!all.contains(value)) {
            String colName = getColName(fn);
            String msg = colName + "出错:  [" + value + "], " + rangeMsg;
            if (Lang.isEmpty(all)) {
                msg = colName + "出错:  不可以填 [" + value + "] ";
            }
            return makeError(raw, PplImportExcelDTO::getDemandTypeName, msg);
        }
        return null;
    }

    @ExcelProperty(index = 0, value = "PPL-ID")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT},
            excelColumnName = "PPL-ID")
    private String pplId;

    @ExcelProperty(index = 1, value = "客户类型")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT,
            },
            excelColumnName = "客户类型")
    private String customerTypeName;
    @ExcelProperty(index = 2, value = "客户Uin")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT,
            },
            excelColumnName = "客户UIN")
    private String customerUin;
    @ExcelProperty(index = 3, value = "客户简称")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT,
            },
            excelColumnName = "客户简称")
    private String customerShortName;

    @ExcelProperty(index = 4, value = "战区")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT,},
            excelColumnName = "战区")
    private String warZone;

    /**
     * 需求类型
     */
    @ExcelProperty(index = 5, value = "需求类型")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT,
            },
            excelColumnName = "需求类型")
    private String demandTypeName;

    /**
     * 需求场景<br/>Column: [demand_scene]
     */
    @ExcelProperty(index = 6, value = "需求场景")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "需求场景")
    private String demandScene;

    /**
     * 项目名称<br/>Column: [project_name]
     */
    @ExcelProperty(index = 7, value = "项目名称")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT,
            },
            excelColumnName = "项目名称")
    private String projectName;

    /**
     * 计费模式<br/>Column: [bill_type]
     */
    @ExcelProperty(index = 8, value = "计费模式")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "计费模式")
    private String billType;

    /**
     * 是否重保需求，当前仅GPU产品需求这个字段，重保需求盈率固定为100%。备货策略：重保需求按100%备货，非重保需求按量级*赢率进行备货
     */
    private Boolean importantDemand;

    /**
     * 赢率，百分比<br/>Column: [win_rate]
     */
    @ExcelProperty(index = 9, value = "赢率，百分比")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT,
            },
            excelColumnName = "赢率")
    private String winRate;


    /**
     * 开始购买日期<br/>Column: [begin_buy_date]
     */
    @ExcelProperty(index = 10, value = "开始购买日期")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "开始购买/退回日期")
    private String beginBuyDate;

    /**
     * 结束购买日期<br/>Column: [end_buy_date]
     */
    @ExcelProperty(index = 11, value = "结束购买日期")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "结束购买/退回日期")
    private String endBuyDate;


    /**
     * 弹性开始日期<br/>Column: [begin_elastic_date]
     */
    @ExcelProperty(index = 12, value = "弹性开始日期")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "弹性开始时间")
    private String beginElasticDate;

    /**
     * 弹性结束日期<br/>Column: [end_elastic_date]
     */
    @ExcelProperty(index = 13, value = "弹性结束日期")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "弹性结束时间")
    private String endElasticDate;

    /**
     * 特殊备注说明<br/>Column: [note]
     */
    @ExcelProperty(index = 14, value = "特殊备注说明")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "特殊备注说明")
    private String note;

    /**
     * 地域，看看要不要存id<br/>Column: [region_name]
     */
    @ExcelProperty(index = 15, value = "地域")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT,
            },
            excelColumnName = "地域")
    private String regionName;

    /**
     * 可用区，看看要不要存id<br/>Column: [zone_name]
     */
    @ExcelProperty(index = 16, value = "可用区")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "可用区")
    private String zoneName;

    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT,GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "是否强指定可用区")
    @ExcelProperty(index = 16, value = "是否强指定可用区")
    private String isStrongDesignateZone;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @ExcelProperty(index = 17, value = "实例类型")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "实例类型")
    private String instanceType;


    /**
     * 实例规格<br/>Column: [instance_model]
     */
    @ExcelProperty(index = 18, value = "实例规格")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT}, excelColumnName = "实例规格-非必填")
    private String instanceModel;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    @ExcelProperty(index = 19, value = "实例规格核心数")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "单台CPU核心")
    private String instanceModelCpuCore;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    @ExcelProperty(index = 20, value = "实例规格内存数")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "单台内存(GB)")
    private String instanceModelRam;


    /**
     * 实例数量<br/>Column: [instance_num]
     */
    @ExcelProperty(index = 21, value = "实例数量")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT}, excelColumnName = "实例数量(台)")
    private String instanceNum;

    /**
     * 实例数量<br/>Column: [instance_num]
     */
    @ExcelProperty(index = 22, value = "总核心数")
    private String totalCoreNum;


    /**
     * isAcceptAlternative
     */
    @ExcelProperty(index = 23, value = "isAcceptAlternative")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "是否接受同配置不同实例机型")
    private String isAcceptAlternative;

    /**
     * 不用填
     */
    @ExcelProperty(index = 24, value = "接受机型")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "接受实例类型")
    private String alternativeInstanceType;

    /**
     * 亲和度类型<br/>Column: [affinity_type]
     */
    @ExcelProperty(index = 25, value = "亲和度类型")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "亲和度类型")
    private String affinityType;

    /**
     * 亲和度值<br/>Column: [affinity_value]
     */
    @ExcelProperty(index = 26, value = "亲和度值")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "亲和度值")
    private String affinityValue;

    /**
     * 磁盘类型<br/>Column: [system_disk_type]
     */
    @ExcelProperty(index = 27, value = "系统磁盘类型")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "系统盘类型")
    private String systemDiskType;

    /**
     * 磁盘容量，单位G<br/>Column: [system_disk_storage]
     */
    @ExcelProperty(index = 28, value = "系统磁盘容量")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "单台系统盘容量(GB)")
    private String systemDiskStorage;

    /**
     * 磁盘类型<br/>Column: [data_disk_type]
     */
    @ExcelProperty(index = 29, value = "数据磁盘类型")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "数据盘类型")
    private String dataDiskType;

    /**
     * 磁盘容量，单位G<br/>Column: [data_disk_storage]
     */
    @ExcelProperty(index = 30, value = "数据磁盘容量")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "单块数据盘容量(GB)")
    private String dataDiskStorage;

    /**
     * 磁盘块数<br/>Column: [data_disk_num]
     */
    @ExcelProperty(index = 31, value = "数据磁盘块数")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "数据盘块数")
    private String dataDiskNum;

    // -------gpu新增-------

    /**
     * 业务场景<br/>Column: [biz_scene]
     */
    @ExcelProperty(index = 32, value = "业务场景")
    private String bizScene;

    /**
     * 业务详情<br/>Column: [biz_detail]
     */
    @ExcelProperty(index = 33, value = "业务详情")
    private String bizDetail;

    /**
     * 使用时长<br/>Column: [service_time]
     */
    @ExcelProperty(index = 34, value = "使用时长")
    private String serviceTime;


    /**
     * 单台卡数<br/>Column: [gpu_gum]
     */
    @ExcelProperty(index = 35, value = "单台卡数")
    private BigDecimal gpuNum;

    /**
     * 是否接受其他卡型<br/>Column: [isAcceptAdjust]
     */
    @ExcelProperty(index = 36, value = "是否接受其他卡型")
    private String isAcceptAdjust;

    /**
     * 接受GPU卡型 用;分割<br/>Column: [acceptGpu]
     */
    @ExcelProperty(index = 37, value = "接受卡型")
    private String acceptGpu;

    /**
     * 产品形态<br/>Column: [gpuProductType]
     */
    @ExcelProperty(index = 38, value = "产品形态")
    private String gpuProductType;

    /**
     * 卡型<br/>Column: [gpuType]
     */
    @ExcelProperty(index = 39, value = "卡型")
    private String gpuType;

    /**
     * 总卡数<br/>Column: [instance_num]
     */
    @ExcelProperty(index = 40, value = "总卡数")
    private String totalGpuNum;

    /**
     * 业务标识ID<br/> Column: [biz_id]
     */
    @ExcelProperty(index = 41, value = "业务标识ID")
    @DotExcelField(group = {GroupConstant.COMD_CVM_PPL_IMPORT}, excelColumnName = "业务标识ID(战略客户部独有)")
    private String bizId;

    /**
     * 包销时长(年)（允许一位小数点，excel导入时最小填写单位为0.5）
     */
    @ExcelProperty(index = 42, value = "包销时长(年)")
    private BigDecimal saleDurationYear;

    /**
     * 申请折扣(折)（填写范围为0.1~10）
     */
    @ExcelProperty(index = 43, value = "申请折扣(折)")
    private BigDecimal applyDiscount;

    /**
     * 商务进展（枚举值-CpqTypeEnum）
     */
    @ExcelProperty(index = 44, value = "商务进展")
    private String businessCpqName;

    @ExcelProperty(index = 45, value = "预约状态")
    @DotExcelField(group = {GroupConstant.COMD_CVM_PPL_IMPORT}, excelColumnName = "预约状态")
    private String appliedStatus;

    @ExcelProperty(index = 47, value = "预约单号")
    @DotExcelField(group = {GroupConstant.COMD_CVM_PPL_IMPORT}, excelColumnName = "预约单号")
    private String yunxiaoOrderId;

    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT, GroupConstant.COMD_CVM_PPL_IMPORT},
            excelColumnName = "单实例IO(MB/s)")
    private String cbsIo;

    @DotExcelField(group = {GroupConstant.COMD_CVM_PPL_IMPORT}, excelColumnName = "项目类型")
    private String isSpikeName;

    @ExcelProperty(index = 51, value = "备选可用区")
    @DotExcelField(group = {GroupConstant.DEFAULT_CVM_PPL_IMPORT},
            excelColumnName = "备选可用区")
    private String alternativeZoneName;

    /**
     * 数据库：数据库名称
     */
    private String databaseName;

    /**
     * 数据库：是否多AZ
     */
    private Boolean moreThanOneAZ;

    /*
     * 数据库：存储类型
     */
    private String databaseStorageType;

    /**
     * 数据库：实例部署类型
     */
    private String deployType;

    /**
     * 数据库：实例类型
     */
    private String frameworkType;

    /**
     * 数据库：分片数量
     */
    private Integer sliceNum;

    /**
     * 数据库：副本数量
     */
    private Integer replicaNum;

    /**
     * 数据库：只读数量
     */
    private Integer readOnlyNum;

    /**
     * 数据库实例规格
     */
    private String databaseSpecs;

    /**
     * 数据库存储
     */
    private BigDecimal databaseStorage;

    /**
     * COS存储类型
     */
    private String cosStorageType;

    /**
     * COS：单AZ/3AZ
     */
    private String cosAZ;

    /**
     * COS：存储量(PB)
     */
    private BigDecimal cosStorage;

    /**
     * COS：带宽(10Gbit/s)
     */
    private Integer bandwidth;

    /**
     * COS：qps
     */
    private Integer qps;

    /**
     * 云运管干预时才有, 不用于excel导入导出
     */
    private String comdType;

    public static List<VersionGroupItemResp.GroupItemDTO> convertToGroupItemVo(List<PplImportExcelDTO> list,
            String product) {
        List<VersionGroupItemResp.GroupItemDTO> result = new ArrayList<>();
        for (PplImportExcelDTO pplImportExcelDTO : list) {
            VersionGroupItemResp.GroupItemDTO groupItemDTO = new VersionGroupItemResp.GroupItemDTO();
            LocalDate beginBuyDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(
                    pplImportExcelDTO.getBeginBuyDate());
            LocalDate endBuyDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(pplImportExcelDTO.getEndBuyDate());
            groupItemDTO.setBeginBuyDate(beginBuyDate);
            groupItemDTO.setEndBuyDate(endBuyDate);

            if (Strings.isNotBlank(pplImportExcelDTO.getBeginElasticDate())) {
                LocalTime beginElasticDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalTime(
                        pplImportExcelDTO.getBeginElasticDate());
                groupItemDTO.setBeginElasticDate(beginElasticDate);
            }
            if (Strings.isNotBlank(pplImportExcelDTO.getEndElasticDate())) {
                LocalTime endElasticDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalTime(
                        pplImportExcelDTO.getEndElasticDate());
                groupItemDTO.setEndElasticDate(endElasticDate);
            }
            groupItemDTO.setStatus(PplItemStatusEnum.VERSION_IMPORT.getCode());
            groupItemDTO.setStatusName(PplItemStatusEnum.VERSION_IMPORT.getName());
            groupItemDTO.setAffinityValue(NumberUtils.parseBigDecimal(pplImportExcelDTO.getAffinityValue()));
            if (Strings.equals(pplImportExcelDTO.getIsAcceptAlternative(), "是")) {
                String alterInstance = pplImportExcelDTO.getAlternativeInstanceType();
                if (Strings.isNotBlank(alterInstance)) {
                    List<String> alterInstanceList = Lang.list(alterInstance.split(";"));
                    groupItemDTO.setAlternativeInstanceType(alterInstanceList);
                }
            }
            groupItemDTO.setProduct(product);

            groupItemDTO.setImportType(PplItemStatusEnum.VERSION_IMPORT.getCode());
            result.add(groupItemDTO);
        }
        return result;
    }

    /**
     * 指定字段的值是否和数据库值相同，不相同则写入入参 errors 中 <br/>
     * 若当前字段值为空，则直接赋值为数据库的值
     */
    public <K> void valueCheckAndSet(IGetter<K> fn, Object dataBaseValue, List<ErrorMessage> errors, int row) {
        String colName = getColName(fn);
        Field declaredField = ORMUtils.getFiledByGetter(fn);
        Object value = DOInfoReader.getValue(declaredField, this);
        if (value == null || org.apache.commons.lang3.StringUtils.isBlank(value.toString())) {
            ReflectUtil.setFieldValue(this, declaredField, dataBaseValue);
            return;
        }
        if (!Objects.equals(value, dataBaseValue)) {
            String fmt = "此字段值不能修改，excel中值【{}】，当前值【{}】";
            String msg = StrUtil.format(fmt, value, dataBaseValue == null ? "" : dataBaseValue);
            errors.add(new ErrorMessage(row, null, colName, msg));
        }
    }

    /**
     * 若当前指定字段值为空，则直接赋值为数据库的值
     */
    public <K> void valueCheckNullAndSet(IGetter<K> fn, Object dataBaseValue) {
        String colName = getColName(fn);
        Field declaredField = ORMUtils.getFiledByGetter(fn);
        Object value = DOInfoReader.getValue(declaredField, this);
        if (value == null || org.apache.commons.lang3.StringUtils.isBlank(value.toString())) {
            ReflectUtil.setFieldValue(this, declaredField, dataBaseValue);
        }
    }

    /**
     * 需求场景、账单类型校验
     */
    public static class DemandAndBillTypeChecker implements ExcelRowDataAfterConvertChecker<PplImportExcelDTO> {

        @Override
        @SneakyThrows
        public <R extends PplImportExcelDTO> void checkRowDataAfterConvert(int rowIndex, R rowData,
                List<cloud.demand.app.common.excel.core.ErrorMessage> errors, ParseContext<R> context) {

            cloud.demand.app.common.excel.core.ErrorMessage message = cloud.demand.app.common.excel.core.ErrorMessage
                    .makeErrorIfNotContain(rowIndex, rowData::getDemandTypeName,
                            PplDemandTypeEnum.names(), rowData, context);
            addNoneNullErrorMessage(errors, message);

            CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getByName(rowData.getCustomerTypeName());
            PplDemandTypeEnum demandTypeEnum = PplDemandTypeEnum.getByName(rowData.getDemandTypeName());
            PplDictService pplDictService = SpringUtil.getBean(PplDictService.class);
            if (demandTypeEnum != null && customerTypeEnum != null) {
                List<String> demandScene = pplDictService.queryDemandScene(customerTypeEnum.getCode(),
                        demandTypeEnum.getCode());

                message = cloud.demand.app.common.excel.core.ErrorMessage
                        .makeErrorIfNotContain(rowIndex, rowData::getDemandScene, demandScene, rowData, context);
                addNoneNullErrorMessage(errors, message);
            }

            if (demandTypeEnum != null && demandTypeEnum != PplDemandTypeEnum.RETURN) {
                List<String> billTypes = pplDictService.queryBillType(demandTypeEnum);
                message = cloud.demand.app.common.excel.core.ErrorMessage
                        .makeErrorIfNotContain(rowIndex, rowData::getBillType, billTypes, rowData, context);
                addNoneNullErrorMessage(errors, message);
            }
//            if (demandTypeEnum == PplDemandTypeEnum.RETURN && customerTypeEnum != null) {
//                //非存量用户无法选择退回需求
//                if (customerTypeEnum != CustomerTypeEnum.EXISTING) {
//                    Integer colIndex = cloud.demand.app.common.excel.core.ErrorMessage
//                            .getExcelColumnIndex(rowData::getDemandTypeName, context);
//                    String colName = cloud.demand.app.common.excel.core.ErrorMessage
//                            .getExcelColumnName(rowData::getDemandTypeName, context);
//                    message = new cloud.demand.app.common.excel.core.ErrorMessage(rowIndex, colIndex, colName,
//                            "非存量用户无法选择退回需求");
//                    addNoneNullErrorMessage(errors, message);
//                }
//
//            }
        }


    }


    /**
     * 需求场景、账单类型校验
     */
    public static class StrongDesignateZoneChecker implements ExcelRowDataAfterConvertChecker<PplImportExcelDTO> {

        @Override
        @SneakyThrows
        public <R extends PplImportExcelDTO> void checkRowDataAfterConvert(int rowIndex, R rowData,
                List<cloud.demand.app.common.excel.core.ErrorMessage> errors, ParseContext<R> context) {

            if (org.springframework.util.StringUtils.isEmpty(rowData.getIsStrongDesignateZone()) &&
                    PplDemandTypeEnum.RETURN.getName().equals(rowData.getDemandTypeName()) ){
                rowData.setIsStrongDesignateZone("是");
            }

            cloud.demand.app.common.excel.core.ErrorMessage errorMessage2 = cloud.demand.app.common.excel.core.ErrorMessage
                    .makeErrorIfNotContain(rowIndex, rowData::getIsStrongDesignateZone, Arrays.asList("是","否"),rowData, context);
            addNoneNullErrorMessage(errors, errorMessage2);

            if ("是".equals(rowData.getIsStrongDesignateZone()) && "随机可用区".equals(rowData.getZoneName())){
                // 不能为空
                errors.add(new cloud.demand.app.common.excel.core.ErrorMessage(
                        rowIndex,context,rowData::getZoneName,"强指定可用区，不能填写随机可用区"));
            }

            if (PplDemandTypeEnum.RETURN.getName().equals(rowData.getDemandTypeName())
                    && "否".equals(rowData.getIsStrongDesignateZone())){
                errors.add(new cloud.demand.app.common.excel.core.ErrorMessage(
                        rowIndex,context,rowData::getZoneName,"退回需求【是否指定强可用区】 必须为是"));
            }

            if (PplDemandTypeEnum.RETURN.getName().equals(rowData.getDemandTypeName())
            && "随机可用区".equals(rowData.getZoneName())){
                errors.add(new cloud.demand.app.common.excel.core.ErrorMessage(
                        rowIndex,context,rowData::getZoneName,"退回需求必须指定强可用区，不能填写随机可用区"));
            }
        }
    }

    /**
     * 行业版本相关审批校验
     */
    public static class PplInnerVersionRelevantChecker implements ExcelRowDataAfterConvertChecker<PplImportExcelDTO> {

        private final PplInnerProcessVersionDO versionDO;

        private final List<String> overseasRegionNameList;

        public PplInnerVersionRelevantChecker(PplInnerProcessVersionDO pplInnerProcessVersionDO,List<String> overseasRegionNameList) {
            this.versionDO = pplInnerProcessVersionDO;
            this.overseasRegionNameList = overseasRegionNameList;
        }

        @Override
        @SneakyThrows
        public <R extends PplImportExcelDTO> void checkRowDataAfterConvert(int rowIndex, R oneData,
                List<cloud.demand.app.common.excel.core.ErrorMessage> errors, ParseContext<R> context) {
            // 当前版本海外需求跨月 且 ppl为海外地域
            // 且开始购买时间不符合范围
            if (overseasRegionNameList.contains(oneData.getRegionName()) &&
                    !oneData.getDemandTypeName().equals(PplDemandTypeEnum.RETURN.getName()) &&
                    !versionDO.isSatisfyOverseasYearMonth(LocalDate.parse(oneData.getBeginBuyDate()))) {

                addNoneNullErrorMessage(errors, new cloud.demand.app.common.excel.core
                        .ErrorMessage(rowIndex, context, oneData::getBeginBuyDate,
                        "当前版本海外需求开始购买日期必须大于等于" + versionDO.getOverseasDemandYearMonthFirstDay()));
            }
        }

    }

    /**
     * 云霄方CVM相关校验 地域/可用区/实例类型/实例配置/核心数/内存数
     */
    public static class YunXiaoRelevantChecker implements ExcelRowDataAfterConvertChecker<PplImportExcelDTO> {

        private final CheckYunxiaoReq req;

        private final String product;

        public YunXiaoRelevantChecker(CheckYunxiaoReq req, String product) {
            this.req = req;
            this.product = product;
        }

        @Override
        @SneakyThrows
        public <R extends PplImportExcelDTO> void checkRowDataAfterConvert(int rowIndex, R oneData,
                List<cloud.demand.app.common.excel.core.ErrorMessage> errors, ParseContext<R> context) {

            List<String> citys = req.getCitys();
            Map<String, List<String>> region2Zone = req.getRegion2Zone();
            List<String> types = req.getTypes();
            Map<String, List<InstanceModelInfoDTO>> type2InstanceModelInfoMap = req.getType2InstanceModelInfoMap();

//            citys.add("随机");
            cloud.demand.app.common.excel.core.ErrorMessage error = cloud.demand.app.common.excel.core.ErrorMessage
                    .makeErrorIfNotContain(rowIndex, oneData::getRegionName,
                            citys, oneData, context);
            addNoneNullErrorMessage(errors, error);

            if (error == null) {
                if (Strings.isNotBlank(oneData.getRegionName())) {
                    oneData.setRegionName(oneData.getRegionName().trim());
                }
                if (Strings.isNotBlank(oneData.getZoneName())) {
                    oneData.setZoneName(oneData.getZoneName().trim());
                }
//                if (PplDemandTypeEnum.RETURN.getName().equals(oneData.getDemandTypeName())
//                && "随机可用区".equals(oneData.getZoneName())) {
//                    error = new cloud.demand.app.common.excel.core.ErrorMessage(rowIndex,context,oneData::getZoneName,
//                            "退回需求不允许选择随机可用区");
//                }
                List<String> zones = ListUtils.isNotEmpty(region2Zone.get(oneData.getRegionName())) ? region2Zone.get(
                        oneData.getRegionName()) : new ArrayList<>();
                zones.add("随机");
                zones.add("随机可用区");
                error = cloud.demand.app.common.excel.core.ErrorMessage
                        .makeErrorIfNotContain(rowIndex, oneData::getZoneName,
                                zones, oneData, context);
                addNoneNullErrorMessage(errors, error);
            }

            Integer coreNum = NumberUtils.parseInt(oneData.getInstanceModelCpuCore());
            Integer ramNum = NumberUtils.parseInt(oneData.getInstanceModelRam());
            boolean isRandomZone = Boolean.TRUE;

            if (product.equals(Ppl13weekProductTypeEnum.BM.getName())) {
                types = ListUtils.filter(types, v -> v.startsWith("BM"));
            } else {
                types = ListUtils.filter(types, v -> !v.startsWith("BM"));
            }

            String instanceType = oneData.getInstanceType();
            if (Strings.isNotBlank(instanceType)) {
                instanceType = instanceType.toUpperCase();
            }
            oneData.setInstanceType(instanceType);

            // 空的报错
            error = cloud.demand.app.common.excel.core.ErrorMessage
                    .makeErrorIfBlank(rowIndex, oneData::getInstanceType, oneData, context);
            addNoneNullErrorMessage(errors, error);
            boolean findOne = false;
            for (String type : types) {
                if (Strings.equalsIgnoreCase(type, instanceType)) {
                    findOne = true;
                    oneData.setInstanceType(type);
                    break;
                }
            }
            if (!findOne) {
                Integer colIndex = cloud.demand.app.common.excel.core.ErrorMessage
                        .getExcelColumnIndex(oneData::getInstanceType, context);
                String colName = cloud.demand.app.common.excel.core.ErrorMessage
                        .getExcelColumnName(oneData::getInstanceType, context);
                if (Lang.isEmpty(types)) {
                    addNoneNullErrorMessage(errors,
                            new cloud.demand.app.common.excel.core.ErrorMessage(rowIndex, colIndex, colName,
                                    oneData.getZoneName() + " 可用区不存在可选实例类型"));
                } else {
                    addNoneNullErrorMessage(errors,
                            new cloud.demand.app.common.excel.core.ErrorMessage(rowIndex, colIndex, colName,
                                    "实例类型不在下列范围内(由可用区筛选)： " + types));
                }
            }

            if (error == null && findOne) {
                error = cloud.demand.app.common.excel.core.ErrorMessage
                        .makeErrorIfBlank(rowIndex, oneData::getInstanceModelCpuCore, oneData, context);
                addNoneNullErrorMessage(errors, error);

                error = cloud.demand.app.common.excel.core.ErrorMessage
                        .makeErrorIfBlank(rowIndex, oneData::getInstanceModelRam, oneData, context);
                addNoneNullErrorMessage(errors, error);

                if (coreNum == null) {
                    addNoneNullErrorMessage(errors, new cloud.demand.app.common.excel.core
                            .ErrorMessage(rowIndex, context, oneData::getInstanceModelCpuCore,
                            "解析cpu核心数出错"));
                }
                if (ramNum == null) {
                    addNoneNullErrorMessage(errors, new cloud.demand.app.common.excel.core
                            .ErrorMessage(rowIndex, context, oneData::getInstanceModelRam,
                            "解析内存数出错"));
                }

                if (coreNum != null && ramNum != null) {
                    List<InstanceModelInfoDTO> instanceModelInfos;

                    instanceModelInfos = type2InstanceModelInfoMap.getOrDefault(oneData.getInstanceType(),
                            Lang.list());

                    if (Lang.isNotEmpty(instanceModelInfos)) {
                        instanceModelInfos = instanceModelInfos.stream().distinct().collect(Collectors.toList());
                    }

                    List<InstanceModelInfoDTO> filter = ListUtils.filter(instanceModelInfos,
                            (o) -> Strings.isNotBlank(o.getInstanceModel())
                                    && Objects.equals(o.getCoreNum(), coreNum)
                                    && Objects.equals(o.getRamNum(), ramNum));
                    if (Lang.isEmpty(filter)) {

                        List<Tuple2<Integer, Integer>> collect = instanceModelInfos.stream()
                                .map((o) -> Tuple.of(o.getCoreNum(), o.getRamNum())).distinct()
                                .collect(Collectors.toList());
                        String info = oneData.getInstanceType() + " 没有找到对应核心和内存的实例规格,可选范围 [(CPU核心，内存GB)]："
                                + collect;
                        addNoneNullErrorMessage(errors, new cloud.demand.app.common.excel.core
                                .ErrorMessage(rowIndex, context, oneData::getInstanceModel, info));
                    } else {
                        if (Strings.isBlank(oneData.getInstanceModel())) {
                            // 实例规格为空时才赋值
                            oneData.setInstanceModel(filter.get(0).getInstanceModel());
                        }
                    }
                }
            }

            error = cloud.demand.app.common.excel.core.ErrorMessage
                    .makeErrorIfBlank(rowIndex, oneData::getInstanceNum, oneData, context);
            addNoneNullErrorMessage(errors, error);
            if (error == null) {
                Integer integer = NumberUtils.parseInt(oneData.getInstanceNum());
                if (integer == null) {
                    error = new cloud.demand.app.common.excel.core
                            .ErrorMessage(rowIndex, context, oneData::getInstanceNum,
                            "解析数字出错： " + oneData.getInstanceNum());
                    addNoneNullErrorMessage(errors, error);
                }
            }

            if (Strings.isNotBlank(oneData.getAffinityType())) {
                List<String> tmpAff = Lang.list("母机", "交换机", "机柜");
                error = cloud.demand.app.common.excel.core.ErrorMessage
                        .makeErrorIfNotContain(rowIndex, oneData::getAffinityType,
                                tmpAff, oneData, context);
                addNoneNullErrorMessage(errors, error);
            }

            if (Strings.isNotBlank(oneData.getAffinityValue())) {
                BigDecimal affinityValue = NumberUtils.parseBigDecimal(oneData.getAffinityValue());
                if (affinityValue == null) {
                    error = new cloud.demand.app.common.excel.core
                            .ErrorMessage(rowIndex, context, oneData::getAffinityValue,
                            "解析数字出错： " + oneData.getAffinityValue());
                    addNoneNullErrorMessage(errors, error);
                }
            }
        }
    }

    /**
     * CBS方相关校验  磁盘类型/数量
     */
    public static class CBSRelevantChecker implements ExcelRowDataAfterConvertChecker<PplImportExcelDTO> {

        private final String product;

        public CBSRelevantChecker(String product) {
            this.product = product;
        }

        @Override
        @SneakyThrows
        public <R extends PplImportExcelDTO> void checkRowDataAfterConvert(int rowIndex, R oneData,
                List<cloud.demand.app.common.excel.core.ErrorMessage> errors, ParseContext<R> context) {
            cloud.demand.app.common.excel.core.ErrorMessage error = null;
            List<String> systemDiskTypes = PplDiskTypeEnum.systemDiskNameList;
            List<String> dataDiskTypes = PplDiskTypeEnum.dataDiskNameList;
            if (Strings.isNotBlank(oneData.getDataDiskType())) {
                error = cloud.demand.app.common.excel.core.ErrorMessage
                        .makeErrorIfNotContain(rowIndex, oneData::getDataDiskType,
                                dataDiskTypes, oneData, context);
                addNoneNullErrorMessage(errors, error);

                // EMR、ES、云数仓才能使用 大数据云盘、高IO型云硬盘
                Tuple2<Boolean, String> res = PplDiskTypeEnum.checkDataDiskForPpl(oneData.getDataDiskType(), product);
                if (!res._1) {
                    error = new cloud.demand.app.common.excel.core.ErrorMessage(rowIndex, context,
                            oneData::getDataDiskType, res._2);
                    addNoneNullErrorMessage(errors, error);
                }

                if (Strings.isNotBlank(oneData.getDataDiskStorage())) {
                    error = cloud.demand.app.common.excel.core.ErrorMessage
                            .makeErrorIfBlank(rowIndex, oneData::getDataDiskStorage, oneData, context);
                    if (error == null) {
                        Integer integer = NumberUtils.parseInt(oneData.getDataDiskStorage());
                        if (integer == null) {
                            error = new cloud.demand.app.common.excel.core
                                    .ErrorMessage(rowIndex, context, oneData::getDataDiskStorage,
                                    "解析数字出错： " + oneData.getDataDiskStorage());
                            addNoneNullErrorMessage(errors, error);
                        } else {
                            if (integer > 32000 || integer < 0) {
                                // https://tapd.woa.com/69994695/prong/stories/view/1069994695116927242
                                // 单块数据盘容量：填写范围为0～32000
                                error = new cloud.demand.app.common.excel.core
                                        .ErrorMessage(rowIndex, context, oneData::getDataDiskStorage,
                                        "单块数据盘容量：填写范围为0～32000; 当前值： "
                                                + oneData.getDataDiskStorage());
                                addNoneNullErrorMessage(errors, error);
                            }
                        }
                    }
                }

                if (Strings.isNotBlank(oneData.getDataDiskNum())) {
                    error = cloud.demand.app.common.excel.core.ErrorMessage
                            .makeErrorIfBlank(rowIndex, oneData::getDataDiskNum, oneData, context);
                    if (error == null) {
                        Integer integer = NumberUtils.parseInt(oneData.getDataDiskNum());
                        if (integer == null) {
                            error = new cloud.demand.app.common.excel.core
                                    .ErrorMessage(rowIndex, context, oneData::getDataDiskNum,
                                    "解析数字出错： " + oneData.getDataDiskNum());
                        }
                    }
                    errors.add(error);
                }

            }
            if (Strings.isNotBlank(oneData.getSystemDiskType())) {
                error = cloud.demand.app.common.excel.core.ErrorMessage
                        .makeErrorIfNotContain(rowIndex, oneData::getSystemDiskType,
                                systemDiskTypes, oneData, context);
                addNoneNullErrorMessage(errors, error);

                if (Strings.isNotBlank(oneData.getSystemDiskStorage())) {
                    error = cloud.demand.app.common.excel.core.ErrorMessage
                            .makeErrorIfBlank(rowIndex, oneData::getSystemDiskStorage, oneData, context);
                    if (error == null) {
                        Integer integer = NumberUtils.parseInt(oneData.getSystemDiskStorage());
                        if (integer == null) {
                            error = new cloud.demand.app.common.excel.core
                                    .ErrorMessage(rowIndex, context, oneData::getSystemDiskStorage,
                                    "解析数字出错： " + oneData.getSystemDiskStorage());
                            addNoneNullErrorMessage(errors, error);
                        } else {
                            if (integer > 2048 || integer < 0) {
                                // https://tapd.woa.com/69994695/prong/stories/view/1069994695116927242
                                // 单台系统盘容量：填写范围为0～2048
                                error = new cloud.demand.app.common.excel.core
                                        .ErrorMessage(rowIndex, context, oneData::getSystemDiskStorage,
                                        "单台系统盘容量：填写范围为0～2048; 当前值： "
                                                + oneData.getSystemDiskStorage());
                                addNoneNullErrorMessage(errors, error);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取uin，根据uin异步查询出对应的完整uin信息放入上下文供 {@link #getUinInfoMapCheckTask} 方法的调用者使用
     */
    public static class QueryInfoByUinRspHandler implements ExcelColumnValueChecker {

        public static final String CONTEXT_KEY_UIN_INFO_MAP = "CONTEXT_KEY_UIN_INFO_MAP";

        public static final String CONTEXT_KEY_UIN_INFO_MAP_TASK = "CONTEXT_KEY_UIN_INFO_MAP_TASK";

        private static final ExecutorService executor = Executors.newFixedThreadPool(10);

        private final PplDictService pplDictService;

        private final Set<String> uinSet = new HashSet<>();

        public QueryInfoByUinRspHandler() {
            this.pplDictService = SpringUtil.getBean(PplDictService.class);
        }

        @Override
        public void checkValue(int rowIndex, int columnIndex, String columnName, Object customerUin,
                List<cloud.demand.app.common.excel.core.ErrorMessage> errors, ParseContext<?> context) {
            if (customerUin == null) {
                return;
            }
            String uinStr = customerUin.toString();
            if (customerUin instanceof Number) {
                long value = ((Number) customerUin).longValue();
                uinStr = Long.toString(value);
            } else {
                if (Strings.isBlank(uinStr)) {
                    return;
                }
                int i = uinStr.indexOf(".");
                if (i > -1) {
                    uinStr = uinStr.substring(0, i);
                }
            }
            String uin = uinStr;
            Map<String, QueryInfoByUinRsp> uinMap = getUinInfoMap(context);
            if (uinSet.contains(uin)) {
                return;
            }
            List<Future<Boolean>> futureList = getUinInfoTaskResult(context);
            // 查询uin信息，放入上下文uinMap
            Future<Boolean> future = executor.submit(() -> {
                uinMap.put(uin, pplDictService.queryInfoByUin(uin));
                return Boolean.TRUE;
            });
            futureList.add(future);
            uinSet.add(uin);
            context.getCustomContext().put(CONTEXT_KEY_UIN_INFO_MAP_TASK, futureList);
            context.getCustomContext().put(CONTEXT_KEY_UIN_INFO_MAP, uinMap);
        }

        private static List<Future<Boolean>> getUinInfoTaskResult(ParseContext<?> context) {
            if (context == null || context.getCustomContext() == null) {
                return new ArrayList<>();
            }
            Object obj = context.getCustomContext().get(CONTEXT_KEY_UIN_INFO_MAP_TASK);
            if (obj == null) {
                return new ArrayList<>();
            }
            if (obj instanceof List) {
                return ((List<Future<Boolean>>) obj);
            }
            return new ArrayList<>();
        }

        private static Map<String, QueryInfoByUinRsp> getUinInfoMap(ParseContext<?> context) {
            if (context == null || context.getCustomContext() == null) {
                return new ConcurrentHashMap<>();
            }
            Object obj = context.getCustomContext().get(CONTEXT_KEY_UIN_INFO_MAP);
            if (obj == null) {
                return new ConcurrentHashMap<>();
            }
            if (obj instanceof Map) {
                return ((Map<String, QueryInfoByUinRsp>) obj);
            }
            return new ConcurrentHashMap<>();
        }

        /**
         * 等待异步任务执行完成，从 context 中获取 完整的 uin 信息
         */
        @SneakyThrows
        public static Map<String, QueryInfoByUinRsp> getUinInfoMapCheckTask(ParseContext<?> context) {
            List<Future<Boolean>> futures = QueryInfoByUinRspHandler.getUinInfoTaskResult(context);
            if (ListUtils.isNotEmpty(futures)) {
                for (Future<Boolean> future : futures) {
                    future.get();
                }
            }
            return QueryInfoByUinRspHandler.getUinInfoMap(context);
        }

    }


    /**
     * 检查pplId是否在草稿单或者已生效的数据中
     */
    public static class PplIdInDraftOrValidChecker implements ExcelColumnValueChecker {

        private Map<String, PplItemDO> pplIdToItem;

        private Map<String, PplItemDraftDO> pplIdToDraftItem;

        public PplIdInDraftOrValidChecker(String industryDept, YearMonth startYearMonth,
                String product, String userName) {
            if (startYearMonth == null) {
                throw BizException.makeThrow("开始年月不能为空");
            }
            PplDraftService pplDraftService = SpringUtil.getBean(PplDraftService.class);
            // 获取已生效的pplId
            List<PplItemJoinOrderVO> currentVersionValidPplItem
                    = pplDraftService.getCurrentVersionValidPplItem(industryDept, product,
                    null);
            if (!CollectionUtils.isEmpty(currentVersionValidPplItem)) {
                List<PplItemDO> validItemList = currentVersionValidPplItem.stream().map(PplItemJoinOrderVO::getItemDO)
                        .collect(Collectors.toList());
                pplIdToItem = validItemList.stream().collect(Collectors.toMap(PplItemDO::getPplId, v -> v));
            }

            // 拿到 草稿箱中存在的 ppl-id
            List<PplDraftItemJoinDraftOrderVO> preSubmitItem = pplDraftService.getCurrentDraftPplItem(industryDept,
                    product,
                    startYearMonth.getFirstDay(), null, userName);
            if (!CollectionUtils.isEmpty(preSubmitItem)) {
                List<PplItemDraftDO> preSubmitItemList = preSubmitItem.stream()
                        .map(PplDraftItemJoinDraftOrderVO::getItemDraftDO)
                        .collect(Collectors.toList());
                pplIdToDraftItem = preSubmitItemList.stream()
                        .collect(Collectors.toMap(PplItemDraftDO::getPplId, v -> v));
            }
        }

        @Override
        public void checkValue(int rowIndex, int columnIndex, String columnName, Object pplId,
                List<cloud.demand.app.common.excel.core.ErrorMessage> errors, ParseContext<?> context) {
            if (pplId != null && Strings.isNotBlank(pplId.toString())) {
                String pplIdStr = pplId.toString();
                if (pplIdToItem.get(pplIdStr) == null && pplIdToDraftItem.get(pplIdStr) == null) {
                    addNoneNullErrorMessage(errors, new cloud.demand.app.common.excel.core
                            .ErrorMessage(rowIndex, columnIndex, columnName,
                            "【" + pplIdStr + "】PPL-ID不存在 或 PPL-ID不在用户权限范围内"));
                }
            }
        }
    }

    public static class StringColumnValueCollect implements ExcelColumnValueChecker {

        private Set<String> valueSet;

        public StringColumnValueCollect() {
            this.valueSet = new HashSet<>();
        }

        public StringColumnValueCollect(Set<String> set) {
            this.valueSet = set == null ? new HashSet<>() : set;
        }

        @Override
        public void checkValue(int rowIndex, int columnIndex, String columnName, Object value,
                List<cloud.demand.app.common.excel.core.ErrorMessage> errors, ParseContext<?> context) {
            if (value == null) {
                return;
            }
            valueSet.add(value.toString());
        }

        public Set<String> getValueSet() {
            return valueSet;
        }
    }

    /**
     * 云运管干预导入时的 需求场景、账单类型校验
     */
    public static class DemandAndBillTypeCheckerForComd implements ExcelRowDataAfterConvertChecker<PplImportExcelDTO> {

        private final DemandAndBillTypeChecker checker = new DemandAndBillTypeChecker();

        @Override
        public <R extends PplImportExcelDTO> void checkRowDataAfterConvert(int rowIndex, R rowData,
                List<cloud.demand.app.common.excel.core.ErrorMessage> errors, ParseContext<R> context) {
            if (Strings.isBlank(rowData.getPplId()) || !rowData.getPplId().trim().contains("PE")) {
                // 需求场景、账单类型校验
                checker.checkRowDataAfterConvert(rowIndex, rowData, errors, context);
            }
        }

    }

    /**
     *  地域、可用区的混合校验。
     *  主可用区、备选可用区为空时不会校验
     */
    public static class RowCheckerForRegionZone implements ExcelRowDataAfterConvertChecker<PplImportExcelDTO> {

        private final Map<String, List<String>> regionNameZoneNameMap;

        public RowCheckerForRegionZone(List<StaticZoneDO> zoneList) {
            this.regionNameZoneNameMap = ListUtils.toMapList(zoneList,
                    StaticZoneDO::getRegionName, StaticZoneDO::getZoneName);
        }

        @Override
        @SneakyThrows
        public <R extends PplImportExcelDTO> void checkRowDataAfterConvert(int rowIndex, R rowData,
                List<cloud.demand.app.common.excel.core.ErrorMessage> errors, ParseContext<R> context) {
            // 地域、主可用区、备选可用区
            String mainZoneName = rowData.getZoneName();
            String regionName = rowData.getRegionName();
            List<String> configZoneNames = regionNameZoneNameMap.get(regionName);
            configZoneNames.add("随机可用区");
            if (ListUtils.isEmpty(configZoneNames)) {
                // 地域下无可用区
                cloud.demand.app.common.excel.core.ErrorMessage message = new cloud.demand.app.common.excel.core.ErrorMessage(rowIndex, context, rowData::getRegionName,
                        StrUtil.format("地域【{}】下无可用区", regionName));
                addNoneNullErrorMessage(errors, message);
            } else if (Strings.isNotBlank(mainZoneName)) {
                // 主可用区 需要在 地域下的可用区内
                int columnIndex = cloud.demand.app.common.excel.core.ErrorMessage
                        .getExcelColumnIndex(rowData::getZoneName, context);
                String columnName = cloud.demand.app.common.excel.core.ErrorMessage
                        .getExcelColumnName(rowData::getZoneName, context);
                cloud.demand.app.common.excel.core.ErrorMessage message = cloud.demand.app.common.excel.core.ErrorMessage
                        .makeErrorIfNotContain(rowIndex, columnIndex, columnName, mainZoneName, configZoneNames);
                addNoneNullErrorMessage(errors, message);
            }
            String otherZoneName = rowData.getAlternativeZoneName();
            if (Strings.isNotBlank(otherZoneName)) {
                int columnIndex = cloud.demand.app.common.excel.core.ErrorMessage
                        .getExcelColumnIndex(rowData::getAlternativeZoneName, context);
                String columnName = cloud.demand.app.common.excel.core.ErrorMessage
                        .getExcelColumnName(rowData::getAlternativeZoneName, context);
                // 备选可用区用 英文分号分隔
                String[] otherZoneNames = Strings.split(otherZoneName, false, false, ';');
                for (String item : otherZoneNames) {
                    // 备选可用区 需要在 地域下的可用区内
                    cloud.demand.app.common.excel.core.ErrorMessage message = cloud.demand.app.common.excel.core.ErrorMessage
                            .makeErrorIfNotContain(rowIndex, columnIndex, columnName, item, configZoneNames);
                    addNoneNullErrorMessage(errors, message);
                }
            }
        }
    }

}
