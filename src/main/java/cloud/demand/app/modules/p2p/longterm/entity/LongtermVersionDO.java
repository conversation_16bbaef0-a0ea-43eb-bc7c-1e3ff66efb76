package cloud.demand.app.modules.p2p.longterm.entity;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.time.YearMonth;
import java.util.List;

@Data
@ToString
@Table("longterm_version")
public class LongtermVersionDO extends BaseDO {

    /**
     * 版本编码<br/>Column: [version_code]
     */
    @Column(value = "version_code")
    private String versionCode;

    /**
     * 版本年月<br/>Column: [version_year_month]
     */
    @Column(value = "version_year_month")
    private String versionYearMonth;

    /**
     * 需求开始年<br/>Column: [demand_begin_year]
     */
    @Column(value = "demand_begin_year")
    private Integer demandBeginYear;

    /**
     * 需求开始月<br/>Column: [demand_begin_month]
     */
    @Column(value = "demand_begin_month")
    private Integer demandBeginMonth;

    /**
     * 需求结束年<br/>Column: [demand_end_year]
     */
    @Column(value = "demand_end_year")
    private Integer demandEndYear;

    /**
     * 需求结束月<br/>Column: [demand_end_month]
     */
    @Column(value = "demand_end_month")
    private Integer demandEndMonth;

    /**
     * 额外的35年开始年<br/>Column: [extra_begin_year]
     */
    @Column(value = "extra_begin_year")
    private Integer extraBeginYear;

    /**
     * 额外的35年开始月<br/>Column: [extra_begin_month]
     */
    @Column(value = "extra_begin_month")
    private Integer extraBeginMonth;

    /**
     * 额外的35年结束年<br/>Column: [extra_end_year]
     */
    @Column(value = "extra_end_year")
    private Integer extraEndYear;

    /**
     * 额外的35年结束月<br/>Column: [extra_end_month]
     */
    @Column(value = "extra_end_month")
    private Integer extraEndMonth;

    /**
     * 额外的35年适用的行业<br/>Column: [extra_industry]
     */
    @Column(value = "extra_industry")
    private String extraIndustry;

    /**
     * 状态<br/>Column: [status]
     */
    @Column(value = "status")
    private String status;

    /**
     * 关联的13周版<br/>Column: [ppl_13week_version_code]
     */
    @Column(value = "ppl_13week_version_code")
    private String ppl13weekVersionCode;

    /**
     * 关联的13周版本的录入开始月<br/>Column: [ppl_13week_start_year_month]
     */
    @Column(value = "ppl_13week_start_year_month")
    private String ppl13weekStartYearMonth;

    /**
     * 关联的13周版本的录入结束月<br/>Column: [ppl_13week_end_year_month]
     */
    @Column(value = "ppl_13week_end_year_month")
    private String ppl13weekEndYearMonth;

    public YearMonth parseVersionStartYearMonth() {
        if (versionYearMonth == null) {
            return null;
        }
        return YearMonth.parse(versionYearMonth);
    }

    public YearMonth parsePpl13weekStartYearMonth() {
        if (ppl13weekStartYearMonth == null) {
            return null;
        }
        return YearMonth.parse(ppl13weekStartYearMonth);
    }

    public YearMonth parsePpl13weekEndYearMonth() {
        if (ppl13weekEndYearMonth == null) {
            return null;
        }
        return YearMonth.parse(ppl13weekEndYearMonth);
    }

    public List<Integer> getAllDemandYears() {
        return ListUtils.union(getNormalDemandYears(), get35DemandYears());
    }

    public List<Integer> getNormalDemandYears() {
        List<Integer> years = ListUtils.newList();
        for (int i = demandBeginYear; i <= demandEndYear; i++) {
            years.add(i);
        }
        return years;
    }

    public List<Integer> get35DemandYears() {
        List<Integer> years = ListUtils.newList();
        if (StringUtils.isNotBlank(extraIndustry) ) {
            for (int i = extraBeginYear; i <= extraEndYear; i++) {
                years.add(i);
            }
            return years;
        }
        return years;
    }

    public List<String> getExtraIndustries() {
        List<String> res = ListUtils.newList();
        if (StringUtils.isBlank(extraIndustry)) {
            return res;
        } else {
            return ListUtils.of(extraIndustry.split(";"));
        }
    }

    /**
     * 描述<br/>Column: [note]
     */
    @Column(value = "note")
    private String note;

    /**
     * 创建人<br/>Column: [creator]
     */
    @Column(value = "creator")
    private String creator;

}