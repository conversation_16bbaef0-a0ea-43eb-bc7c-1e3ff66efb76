package cloud.demand.app.modules.p2p.product_demand.dto;

import lombok.Data;

import java.util.List;

@Data
public class SummaryStatisticReq {

    private String version;
    private Long groupId;
    private String lastVersion;
    private List<String> industryName;
    private List<String> customerName;
    private List<String> zoneName;
    private List<String> deviceTypeName;
    private List<String> reasonType;
    private List<String> reason;
    private List<String> planProduct;
    private String field;
    private Boolean core;
}
