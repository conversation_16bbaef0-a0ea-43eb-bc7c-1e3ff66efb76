package cloud.demand.app.modules.p2p.ppl13week.service.excel;

import cloud.demand.app.common.excel.core.ErrorMessage;
import cloud.demand.app.common.excel.core.ParseContext;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.IGetter;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandRegionZoneInstanceTypeDictDO;
import cloud.demand.app.modules.p2p.ppl13week.dto.dict.InstanceModelInfoDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.QueryInfoByUinRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplCvmImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplGpuRegionZoneDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CpqTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDiskTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.Period;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 通用excel导入方法
 */
@Service
@Slf4j
public abstract class AbstractGeneralPplExcelParseService {

    @Resource
    private PplDictService pplDictService;
    @Resource
    private DBHelper demandDBHelper;

    static ExecutorService executor = Executors.newFixedThreadPool(10);

    @SneakyThrows
    // 查询uin
    protected HashMap<String, QueryInfoByUinRsp> queryInfoByUinRspHashMap(List<String> uins) {
        List<Future<Tuple2<String, QueryInfoByUinRsp>>> futures = new ArrayList<>();
        for (String uin : uins) {
            Future<Tuple2<String, QueryInfoByUinRsp>> future = executor.submit(
                    () -> Tuple.of(uin, pplDictService.queryInfoByUin(uin)));
            futures.add(future);
        }
        HashMap<String, QueryInfoByUinRsp> uinMapInfo = new HashMap<>();
        // 遍历 future 对象列表，检查每个任务是否执行成功
        long startTmp = System.currentTimeMillis();
        for (Future<Tuple2<String, QueryInfoByUinRsp>> future : futures) {
            Tuple2<String, QueryInfoByUinRsp> one = future.get();
            uinMapInfo.put(one._1, one._2);
        }
        return uinMapInfo;
    }

    // uin校验并返回
    protected QueryInfoByUinRsp checkUin(List<PplItemImportRsp.ErrorMessage> errors,
            PplImportExcelDTO oneData, Integer row, Map<String, QueryInfoByUinRsp> uinMapInfo) {
        CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getByName(oneData.getCustomerTypeName());
        QueryInfoByUinRsp queryInfoByUinRsp = new QueryInfoByUinRsp();
        queryInfoByUinRsp.setIsExist(Boolean.FALSE);
        PplItemImportRsp.ErrorMessage error = null;
        if (customerTypeEnum != null) {
            queryInfoByUinRsp = oneData.getCustomerUin() == null ? null
                    : MapUtils.getObject(uinMapInfo, oneData.getCustomerUin());
            if (queryInfoByUinRsp == null) {
                queryInfoByUinRsp = new QueryInfoByUinRsp();
                queryInfoByUinRsp.setIsExist(Boolean.FALSE);
            }
            if (customerTypeEnum == CustomerTypeEnum.EXISTING) {

                if (!queryInfoByUinRsp.getIsExist() && !oneData.getDemandTypeName()
                        .equals(PplDemandTypeEnum.RETURN.getName())) {
                    error = makeError(row, PplImportExcelDTO::getCustomerUin,
                            "没有找到uin: " + oneData.getCustomerUin());
                    errors.add(error);
                }
            } else {
                String customerShortName = oneData.getCustomerShortName();
                if (Strings.isNotBlank(oneData.getCustomerUin())) {
                    if (!queryInfoByUinRsp.getIsExist()) {
                        error = makeError(row, PplImportExcelDTO::getCustomerUin, "Uin 不合法");
                        errors.add(error);
                    }
                }

                if (!queryInfoByUinRsp.getIsExist() && Strings.isBlank(customerShortName)) {
                    error = makeError(row, PplImportExcelDTO::getCustomerUin, "Uin 不合法 或者 客户简称为空");
                    errors.add(error);
                }
            }
        }
        return queryInfoByUinRsp;

    }

    // uin校验并返回
    protected QueryInfoByUinRsp checkUin(List<ErrorMessage> errors, ParseContext<? extends PplImportExcelDTO> context,
            PplImportExcelDTO oneData, Integer row, Map<String, QueryInfoByUinRsp> uinMapInfo) {
        CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getByName(oneData.getCustomerTypeName());
        QueryInfoByUinRsp queryInfoByUinRsp = new QueryInfoByUinRsp();
        queryInfoByUinRsp.setIsExist(Boolean.FALSE);
        if (customerTypeEnum != null) {
            queryInfoByUinRsp = oneData.getCustomerUin() == null ? null
                    : MapUtils.getObject(uinMapInfo, oneData.getCustomerUin());
            if (queryInfoByUinRsp == null) {
                queryInfoByUinRsp = new QueryInfoByUinRsp();
                queryInfoByUinRsp.setIsExist(Boolean.FALSE);
            }
            if (customerTypeEnum == CustomerTypeEnum.EXISTING) {

                if (!queryInfoByUinRsp.getIsExist() && !oneData.getDemandTypeName()
                        .equals(PplDemandTypeEnum.RETURN.getName())) {
                    ErrorMessage error = new ErrorMessage(row, context, oneData::getCustomerUin,
                            "没有找到uin: " + oneData.getCustomerUin());
                    errors.add(error);
                }
            } else {
                String customerShortName = oneData.getCustomerShortName();
                if (Strings.isNotBlank(oneData.getCustomerUin())) {
                    if (!queryInfoByUinRsp.getIsExist()) {
                        ErrorMessage error = new ErrorMessage(row, context, oneData::getCustomerUin,
                                "Uin 不合法：" + oneData.getCustomerUin());
                        errors.add(error);
                    }
                }

                if (!queryInfoByUinRsp.getIsExist() && Strings.isBlank(customerShortName)) {
                    ErrorMessage error = new ErrorMessage(row, context, oneData::getCustomerUin,
                            "Uin 不合法 或者 客户简称为空");
                    errors.add(error);
                }
            }
        }
        return queryInfoByUinRsp;

    }

    // 检查预约明细的主key信息和资源量（针对非战略客户部）
    protected void checkAppliedItem(List<PplItemImportRsp.ErrorMessage> errors, PplImportExcelDTO oneData,
            PplOrderDO appliedPplOrder, PplItemDO appliedPplItem, Integer row) {
        if (appliedPplItem == null || appliedPplOrder == null || oneData == null) {
            return;
        }

        // 1、比较主key信息是否有变动
        String appliedPplId = appliedPplItem.getPplId();
        checkAppliedKey(errors, oneData, appliedPplOrder, appliedPplItem, appliedPplId, row);

        // 2、比较实例数是否有变动
        Integer num = NumberUtils.parseInt(oneData.getInstanceNum());
        num = num == null ? 0 : num;
        if (num.compareTo(appliedPplItem.getInstanceNum()) != 0) {
            errors.add(makeError(row, PplImportExcelDTO::getInstanceNum,
                    appliedPplId + "单已预约，请勿修改其实例数量"));
        }

        // 3、比较资源量是否有变动（主要比较PN单，PE单的资源量不一定按台数相乘可以得到）
        if (!PplOrderSourceTypeEnum.IMPORT.getCode().equals(appliedPplOrder.getSource())) {
            return;
        }

        // GPU产品比较卡数-totalGpuNum，非GPU产品比较核心数-totalNum
        // 非战略部 限制 导入的预测量 = 已预约量（ppl-id单条维度）
        boolean isGpu = Objects.equals(appliedPplItem.getProduct(), Ppl13weekProductTypeEnum.GPU.getName());
        boolean isStrategy = Objects.equals(appliedPplOrder.getIndustryDept(), IndustryDeptEnum.STRATEGY.getName());
        if (!isGpu) {
            Integer coreNum = NumberUtils.parseInt(oneData.getInstanceModelCpuCore());
            coreNum = coreNum == null ? 0 : coreNum;
            Integer totalCore = num * coreNum;

            if (!isStrategy && totalCore.compareTo(appliedPplItem.getTotalCore()) != 0) {
                errors.add(makeError(row, PplImportExcelDTO::getTotalCoreNum,
                        appliedPplId + "单已预约，请勿修改其核心数"));
            }
            return;
        }

        BigDecimal gpuNum = oneData.getGpuNum() == null ? BigDecimal.ZERO : oneData.getGpuNum();
        BigDecimal instanceNum = NumberUtils.parseBigDecimal(oneData.getInstanceNum());
        instanceNum = instanceNum == null ? BigDecimal.ZERO : instanceNum;
        BigDecimal totalGpuNum = gpuNum.multiply(instanceNum);
        if (!isStrategy && totalGpuNum.compareTo(appliedPplItem.getTotalGpuNum()) != 0) {
            errors.add(makeError(row, PplImportExcelDTO::getTotalGpuNum,
                    appliedPplId + "单已预约，请勿修改其卡数"));
        }
    }

    // 检查预约明细的主key信息和资源量（针对战略客户部）
    protected void checkAppliedItemForStrategy(List<PplItemImportRsp.ErrorMessage> errors, PplImportExcelDTO oneData,
            PplOrderDO appliedPplOrder, List<PplItemDO> appliedPplItems, Integer row) {
        if (ListUtils.isEmpty(appliedPplItems) || appliedPplOrder == null || oneData == null) {
            return;
        }

        // 1、比较主key信息是否有变动
        PplItemDO appliedPplItem = appliedPplItems.get(0);
        String appliedBizId = appliedPplItem.getBizId();
        checkAppliedKey(errors, oneData, appliedPplOrder, appliedPplItem, appliedBizId, row);

        // 2、比较实例数是否有变动
        int numSum = appliedPplItems.stream().mapToInt(o -> o.getInstanceNum() != null ? o.getInstanceNum() : 0)
                .sum();
        int totalCoreSum = appliedPplItems.stream().mapToInt(o -> o.getTotalCore() != null ? o.getTotalCore() : 0)
                .sum();
        BigDecimal totalGpuNumSum = appliedPplItems.stream().map(PplItemDO::getTotalGpuNum).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        Integer num = NumberUtils.parseInt(oneData.getInstanceNum());
        num = num == null ? 0 : num;
//        if (num < numSum) {
//            errors.add(makeError(row, PplImportExcelDTO::getInstanceNum,
//                    appliedBizId + "单已预约，请勿调小其实例数量"));
//        }

        // 3、比较资源量是否有变动
        // GPU产品比较卡数-totalGpuNum，非GPU产品比较核心数-totalNum
        // 战略客户部 允许导入的预测量 >= 已预约量（bizId维度汇总）
        boolean isGpu = Objects.equals(appliedPplItem.getProduct(), Ppl13weekProductTypeEnum.GPU.getName());
        boolean isStrategy = Objects.equals(appliedPplOrder.getIndustryDept(), IndustryDeptEnum.STRATEGY.getName());
        if (!isGpu) {
            Integer coreNum = NumberUtils.parseInt(oneData.getInstanceModelCpuCore());
            coreNum = coreNum == null ? 0 : coreNum;
            int totalCore = num * coreNum;

            if (isStrategy && totalCore < totalCoreSum) {
                errors.add(makeError(row, PplImportExcelDTO::getTotalCoreNum,
                        appliedBizId + "单已预约，请勿调小其核心数"));
            }
            return;
        }

        BigDecimal gpuNum = oneData.getGpuNum() == null ? BigDecimal.ZERO : oneData.getGpuNum();
        BigDecimal instanceNum = NumberUtils.parseBigDecimal(oneData.getInstanceNum());
        instanceNum = instanceNum == null ? BigDecimal.ZERO : instanceNum;
        BigDecimal totalGpuNum = gpuNum.multiply(instanceNum);

        if (isStrategy && totalGpuNum.compareTo(totalGpuNumSum) < 0) {
            errors.add(makeError(row, PplImportExcelDTO::getTotalGpuNum,
                    appliedBizId + "单已预约，请勿调小其卡数"));
        }

    }

    private void checkAppliedKey(List<PplItemImportRsp.ErrorMessage> errors, PplImportExcelDTO oneData,
            PplOrderDO appliedPplOrder, PplItemDO appliedPplItem, String id, Integer row) {
        // 比较主key是否有变动
        if (!Objects.equals(appliedPplItem.getDemandType(),
                PplDemandTypeEnum.getCodeByName(oneData.getDemandTypeName()))) {
            errors.add(makeError(row, PplImportExcelDTO::getDemandTypeName,
                    id + "单已预约，请勿修改其需求类型"));
        }
        if (!Objects.equals(appliedPplOrder.getCustomerShortName(), oneData.getCustomerShortName())) {
            errors.add(makeError(row, PplImportExcelDTO::getCustomerShortName,
                    id + "单已预约，请勿修改其客户简称"));
        }
        if (!Objects.equals(appliedPplItem.getInstanceType(), oneData.getInstanceType())) {
            errors.add(makeError(row, PplImportExcelDTO::getInstanceType,
                    id + "单已预约，请勿修改其实例类型"));
        }
        if (!Objects.equals(appliedPplItem.getInstanceNum(), oneData.getInstanceNum())) {
            errors.add(makeError(row, PplImportExcelDTO::getInstanceNum,
                    id + "单已预约，请勿修改其数量"));
        }
        if (!Objects.equals(appliedPplItem.getInstanceModel(), oneData.getInstanceModel())) {
            errors.add(makeError(row, PplImportExcelDTO::getInstanceModel,
                    id + "单已预约，请勿修改其实例规格"));
        }
        if (!Objects.equals(appliedPplItem.getRegionName(), oneData.getRegionName())) {
            errors.add(makeError(row, PplImportExcelDTO::getRegionName,
                    id + "单已预约，请勿修改其地域"));
        }

//        LocalDate beginBuyDate = DateUtils.parseLocalDate(oneData.getBeginBuyDate());
//        if (beginBuyDate == null) {
//            errors.add(makeError(row, PplImportExcelDTO::getBeginBuyDate,
//                    "开始日期解析出错"));
//        } else if (!appliedPplItem.getBeginBuyDate().isEqual(beginBuyDate)) {
//            errors.add(makeError(row, PplImportExcelDTO::getBeginBuyDate,
//                    id + "单已预约，请勿修改其开始需求年月"));
//        }
//
//        LocalDate endBuyDate = DateUtils.parseLocalDate(oneData.getEndBuyDate());
//        if (endBuyDate == null) {
//            errors.add(makeError(row, PplImportExcelDTO::getEndBuyDate,
//                    "结束日期解析出错"));
//        } else if (!appliedPplItem.getEndBuyDate().isEqual(endBuyDate)) {
//            errors.add(makeError(row, PplImportExcelDTO::getEndBuyDate,
//                    id + "单已预约，请勿修改其结束需求年月"));
//        }
    }

    /**
     * <a href="https://iwiki.woa.com/pages/viewpage.action?pageId=4008320459">
     * 校验GPU新增的三个参数 SaleDuration(包销时长)、ApplyDiscount(申请折扣)、Cpq(是否CPQ) </a>
     */
    protected void gpuCheck(List<PplItemImportRsp.ErrorMessage> errors,
            PplImportExcelDTO oneData, Integer row) {
        if (oneData == null) {
            return;
        }

        if (oneData.getSaleDurationYear() != null
                && oneData.getSaleDurationYear().remainder(new BigDecimal("0.5")).compareTo(BigDecimal.ZERO) != 0) {
            String msg = "包销时长(年)最小填写单位为0.5";
            errors.add(oneData.makeErrorIfTure(row, PplImportExcelDTO::getSaleDurationYear, true, msg));
        }
        if (oneData.getApplyDiscount() != null && (new BigDecimal("10").compareTo(oneData.getApplyDiscount()) < 0
                || new BigDecimal("0.1").compareTo(oneData.getApplyDiscount()) > 0)) {
            String msg = "申请折扣(折)范围值[0.1,10]";
            errors.add(oneData.makeErrorIfTure(row, PplImportExcelDTO::getApplyDiscount, true, msg));
        }
        if (StringUtils.isNotBlank(oneData.getBusinessCpqName())
                && CpqTypeEnum.getByName(oneData.getBusinessCpqName()) == null) {
            String msg = oneData.getBusinessCpqName() + "，不在商务进展填写范围中";
            errors.add(oneData.makeErrorIfTure(row, PplImportExcelDTO::getBusinessCpqName, true, msg));
        }

    }


    // 需求场景和账单类型校验
    protected void checkDemandAndBillType(List<PplItemImportRsp.ErrorMessage> errors,
            PplImportExcelDTO oneData, Integer row) {
        PplItemImportRsp.ErrorMessage error = null;
        CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getByName(oneData.getCustomerTypeName());
        PplDemandTypeEnum demandTypeEnum = PplDemandTypeEnum.getByName(oneData.getDemandTypeName());

        error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getDemandTypeName,
                PplDemandTypeEnum.names());
        errors.add(error);

        if (demandTypeEnum != null && customerTypeEnum != null) {
            List<String> demandScenes = pplDictService.queryDemandScene(customerTypeEnum.getCode(),
                    demandTypeEnum.getCode());
            error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getDemandScene, demandScenes);
            errors.add(error);
        }

        if (demandTypeEnum != null && demandTypeEnum != PplDemandTypeEnum.RETURN) {
            List<String> billTypes = pplDictService.queryBillType(demandTypeEnum);
            error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getBillType, billTypes);
            errors.add(error);
        }
//        if (demandTypeEnum != null && demandTypeEnum == PplDemandTypeEnum.RETURN && customerTypeEnum != null) {
//            //非存量用户无法选择退回需求
//            if (customerTypeEnum != CustomerTypeEnum.EXISTING) {
//                error = makeError(row, PplImportExcelDTO::getDemandTypeName, "非存量用户无法选择退回需求");
//                errors.add(error);
//            }
//
//        }

    }

    // 项目名校验
    protected void checkProjectName(List<PplItemImportRsp.ErrorMessage> errors, PplImportExcelDTO oneData,
            Integer row) {
        PplItemImportRsp.ErrorMessage error = null;
        if (Strings.isBlank(oneData.getProjectName())) {
            error = makeError(row, PplImportExcelDTO::getProjectName, "项目名为空");
            errors.add(error);
        }
    }

    // 赢率校验
    protected BigDecimal checkWinRate(List<PplItemImportRsp.ErrorMessage> errors,
            PplImportExcelDTO oneData, Integer row) {
        PplItemImportRsp.ErrorMessage error = null;
        BigDecimal winRate = null;
        if (Strings.isNotBlank(oneData.getWinRate())) {
            // 包含在 0 - 100 中
            List<String> winRateRange = Lang.list();
            for (int cnt = 0; cnt <= 100; cnt++) {
                winRateRange.add(cnt + "%");
            }
            error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getWinRate, winRateRange);
            errors.add(error);
            String replace = oneData.getWinRate().replace('%', ' ').trim();
            winRate = NumberUtils.parseBigDecimal(replace);
        }
        return winRate;
    }

    // 各时间参数校验
    protected DateDTO checkTime(List<PplItemImportRsp.ErrorMessage> errors,
            PplImportExcelDTO oneData, Integer row, YearMonth startYearMonth, YearMonth endYearMonth) {
        boolean isGpu = this instanceof PplGpuExcelParseService; //判断是否为GPU
        boolean beginSuccess = false;
        PplItemImportRsp.ErrorMessage error = null;
        int year = 0, month = 0;
        String yearMonth = null;
        LocalDate beginBuyDateRet = null;
        LocalDate endBuyDateRet = null;
        LocalTime beginElasticDateRet = null;
        LocalTime endElasticDateRet = null;
        PplDemandTypeEnum demandTypeEnum = PplDemandTypeEnum.getByName(oneData.getDemandTypeName());
        DateDTO dateDTO = new DateDTO();
        String beginBuyDate = oneData.getBeginBuyDate();
        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getBeginBuyDate);
        errors.add(error);
        if (error == null) {
            LocalDate localDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(beginBuyDate);
            if (localDate == null) {
                error = makeError(row, PplImportExcelDTO::getBeginBuyDate, "开始日期解析出错");
                errors.add(error);
            } else {
                beginBuyDateRet = localDate;
                year = localDate.getYear();
                month = localDate.getMonth().getValue();
                yearMonth = com.pugwoo.wooutils.lang.DateUtils.format(localDate, "yyyy-MM");
                // 23/3/6 GPU录入和CVM录入的冲突点， 临时处理，逻辑确定后需马上修正。 isGpu ? month + 1 : month
                // 23/5/5 去除 ⬆️ 允许录入前一个月的逻辑
                if (startYearMonth.getYear() > year ||
                        (startYearMonth.getYear() == year && startYearMonth.getMonth() > month)) {
                    String info = "开始日期:" + yearMonth + "  <  版本开始日期:" + startYearMonth.toDateStr();
                    error = makeError(row, PplImportExcelDTO::getBeginBuyDate, info);
                    errors.add(error);
                }
                beginSuccess = true;
            }
        }

        String endBuyDate = oneData.getEndBuyDate();
        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getEndBuyDate);
        errors.add(error);
        if (error == null) {
            LocalDate localDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(endBuyDate);
            endBuyDateRet = localDate;
            if (localDate == null) {
                error = makeError(row, PplImportExcelDTO::getEndBuyDate, "结束日期解析出错");
                errors.add(error);
            } else {
                if (beginSuccess) {
                    LocalDate beginLocalDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(beginBuyDate);
                    if (beginLocalDate.compareTo(localDate) > 0) {
                        error = makeError(row, PplImportExcelDTO::getEndBuyDate, "结束日期比开始日期小");
                        errors.add(error);
                    }

                    if (Strings.equals(oneData.getDemandTypeName(), PplDemandTypeEnum.NEW.getName())
                            || Strings.equals(oneData.getDemandTypeName(), PplDemandTypeEnum.RETURN.getName())) {
//                            https://zhiyan.woa.com/requirement/6756/story/#/cloudrm-1186?tab=info&story_tab=info

                        Period between = Period.between(localDate.withDayOfMonth(1),
                                beginLocalDate.withDayOfMonth(1));
                        if (Math.abs(between.getMonths()) > 1) {
                            error = makeError(row, PplImportExcelDTO::getEndBuyDate,
                                    "新增需求/退回需求的月份差值大于1： 结束购买日期年月 - 开始购买日期年月 > 1");
                            errors.add(error);
                        }
                    }
                }
            }
        }

        if (demandTypeEnum != null) {
            if (demandTypeEnum == PplDemandTypeEnum.ELASTIC) {
                error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getBeginElasticDate);
                errors.add(error);
                error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getEndElasticDate);
                errors.add(error);
                String beginElasticDate = oneData.getBeginElasticDate();
                LocalTime localTime = com.pugwoo.wooutils.lang.DateUtils.parseLocalTime(beginElasticDate);
                beginElasticDateRet = localTime;
                if (localTime == null) {
                    error = makeError(row, PplImportExcelDTO::getBeginElasticDate, "弹性开始日期解析出错");
                    errors.add(error);
                }
                String endElasticDate = oneData.getEndElasticDate();
                localTime = com.pugwoo.wooutils.lang.DateUtils.parseLocalTime(endElasticDate);
                endElasticDateRet = localTime;
                if (localTime == null) {
                    error = makeError(row, PplImportExcelDTO::getEndElasticDate, "弹性结束日期解析出错");
                    errors.add(error);
                }
            }
        }

        dateDTO.setYear(year);
        dateDTO.setMonth(month);
        dateDTO.setBeginBuyDateRet(beginBuyDateRet);
        dateDTO.setEndBuyDateRet(endBuyDateRet);
        dateDTO.setBeginElasticDateRet(beginElasticDateRet);
        dateDTO.setEndElasticDateRet(endElasticDateRet);
        dateDTO.setYearMonth(yearMonth);
        return dateDTO;
    }

    // 海外跨月需求校验
    protected void checkOverseasCrossMonth(List<PplItemImportRsp.ErrorMessage> errors,
            PplImportExcelDTO oneData, Integer row, PplInnerProcessVersionDO versionDO, List<String> overseasRegionNameList) {
        PplItemImportRsp.ErrorMessage error = null;
        // 当前版本海外需求跨月 且 ppl为海外地域
        // 且开始购买时间不符合范围
        if (overseasRegionNameList.contains(oneData.getRegionName()) &&
                !oneData.getDemandTypeName().equals(PplDemandTypeEnum.RETURN.getName()) &&
                !versionDO.isSatisfyOverseasYearMonth(LocalDate.parse(oneData.getBeginBuyDate()))) {

            error = makeError(row, PplImportExcelDTO::getBeginBuyDate,
                    "当前版本海外需求开始购买日期必须大于等于" + versionDO.getOverseasDemandYearMonthFirstDay());
            errors.add(error);
        }
    }

    // 海外跨月需求校验
    protected void checkOverseasCrossMonth(List<PplItemImportRsp.ErrorMessage> errors,
            PplImportExcelDTO oneData, Integer row, PplVersionDO versionDO, List<String> overseasRegionNameList) {
        PplItemImportRsp.ErrorMessage error = null;
        // 当前版本海外需求跨月 且 ppl为海外地域
        // 且开始购买时间不符合范围
        if (overseasRegionNameList.contains(oneData.getRegionName()) &&
                !oneData.getDemandTypeName().equals(PplDemandTypeEnum.RETURN.getName()) &&
                !versionDO.isSatisfyOverseasYearMonth(LocalDate.parse(oneData.getBeginBuyDate()))) {

            error = makeError(row, PplImportExcelDTO::getBeginBuyDate,
                    "当前版本海外需求开始购买日期必须大于等于" + versionDO.getOverseasDemandYearMonthFirstDay());
            errors.add(error);
        }
    }

    protected void strongDesignateZoneCheck(List<PplItemImportRsp.ErrorMessage> errors,
            PplImportExcelDTO rowData, Integer rowIndex){
        PplItemImportRsp.ErrorMessage error = null;
        if (org.springframework.util.StringUtils.isEmpty(rowData.getIsStrongDesignateZone()) &&
                PplDemandTypeEnum.RETURN.getName().equals(rowData.getDemandTypeName()) ){
            rowData.setIsStrongDesignateZone("是");
        }

        if (!Arrays.asList("是","否").contains(rowData.getIsStrongDesignateZone())){
            error = makeError(rowIndex, PplImportExcelDTO::getZoneName,
                    "强指定可用区只能填 是 或 否");
            errors.add(error);
        }
        if ("是".equals(rowData.getIsStrongDesignateZone()) && "随机可用区".equals(rowData.getZoneName())){
            error = makeError(rowIndex, PplImportExcelDTO::getZoneName,
                    "强指定可用区，可用区不能为 随机可用区");
            errors.add(error);
        }

        if (PplDemandTypeEnum.RETURN.getName().equals(rowData.getDemandTypeName())
                && "否".equals(rowData.getIsStrongDesignateZone())){
            error = makeError(rowIndex, PplImportExcelDTO::getIsStrongDesignateZone,
                    "退回需求【是否指定强可用区】 必须为是");
            errors.add(error);
        }

        if (PplDemandTypeEnum.RETURN.getName().equals(rowData.getDemandTypeName())
                && "随机可用区".equals(rowData.getZoneName())){
            error = makeError(rowIndex, PplImportExcelDTO::getZoneName,
                    "退回需求必须指定具体可用区，不能填写随机可用区");
            errors.add(error);
        }
    }


    public CheckYunxiaoReq buildCheckYunxiaoReq() {
        CheckYunxiaoReq checkYunxiaoReq = new CheckYunxiaoReq();
        List<String> citys = pplDictService.queryAllCityName();
        List<String> types = pplDictService.queryInstanceType("");
        Map<String, List<String>> region2Zone = pplDictService.queryRegion2Zone();
        Map<String, List<InstanceModelInfoDTO>> type2InstanceModelInfoMap =
                pplDictService.queryType2InstanceModelInfoMap();
        checkYunxiaoReq.setCitys(citys);
        checkYunxiaoReq.setRegion2Zone(region2Zone);
        checkYunxiaoReq.setTypes(types);
        checkYunxiaoReq.setType2InstanceModelInfoMap(type2InstanceModelInfoMap);
        return checkYunxiaoReq;

    }

    @Data
    public static class CheckYunxiaoReq {

        private List<String> citys;

        private Map<String, List<String>> region2Zone;

        private List<String> types;

        private Map<String, List<InstanceModelInfoDTO>> type2InstanceModelInfoMap;
    }


    // 云霄cvm相关数据校验
    protected void checkYunXiaoRelevant(List<PplItemImportRsp.ErrorMessage> errors,
            PplImportExcelDTO oneData, Integer row,
            List<String> alternativeInstances, String product, Boolean isComd, CheckYunxiaoReq req) {
        String demandType = PplDemandTypeEnum.getCodeByName(oneData.getDemandTypeName());

        PplItemImportRsp.ErrorMessage error = null;
        List<String> citys = req.getCitys();
        Map<String, List<String>> region2Zone = req.getRegion2Zone();
        List<String> types = req.getTypes();
        Map<String, List<InstanceModelInfoDTO>> type2InstanceModelInfoMap = req.getType2InstanceModelInfoMap();

        citys.add("随机");
        error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getRegionName, citys);
        errors.add(error);

        if ("否".equals(oneData.getIsStrongDesignateZone())){
            oneData.setZoneName("随机可用区");
        }

        if (error == null) {
            if (Strings.isNotBlank(oneData.getRegionName())) {
                oneData.setRegionName(oneData.getRegionName().trim());
            }
            if (Strings.isNotBlank(oneData.getZoneName())) {
                oneData.setZoneName(oneData.getZoneName().trim());
            }else {
                if ("否".equals(oneData.getIsStrongDesignateZone())){
                     oneData.setZoneName("随机可用区");
                }
            }
            List<String> zones = ListUtils.isNotEmpty(region2Zone.get(oneData.getRegionName())) ? region2Zone.get(
                    oneData.getRegionName()) : new ArrayList<>();
            zones.add("随机");
            zones.add("随机可用区");

            error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getZoneName, zones);
            errors.add(error);
        }

        Integer coreNum = NumberUtils.parseInt(oneData.getInstanceModelCpuCore());
        Integer ramNum = NumberUtils.parseInt(oneData.getInstanceModelRam());
        // 23-4-17日 修改 由于放开所有地域可用区实例限制，因此所有都按照随机可用区来处理。
//        boolean isRandomZone =
//                Strings.equals(oneData.getZoneName(), "随机")
//                        || Strings.equals(oneData.getZoneName(), "随机可用区");
        boolean isRandomZone = Boolean.TRUE;

        if (product.equals(Ppl13weekProductTypeEnum.BM.getName())) {
            types = ListUtils.filter(types, v -> v.startsWith("BM"));
        } else {
            types = ListUtils.filter(types, v -> !v.startsWith("BM"));
        }

        String instanceType = oneData.getInstanceType();
        if (Strings.isNotBlank(instanceType)) {
            instanceType = instanceType.toUpperCase();

            List<String> notIAllowInstanceType = Arrays.asList("SA3", "MA3", "ITA4");
//            if (!demandType.equals(PplDemandTypeEnum.RETURN.getCode())
//                    && oneData.getPplId() == null && !isComd && notIAllowInstanceType.contains(instanceType)) {
//                errors.add(makeError(row, PplImportExcelDTO::getInstanceType,
//                        oneData.getInstanceType() + " 该实例类型已下架，不允许 新增/调整 该实例类型"));
//            }

        }
        oneData.setInstanceType(instanceType);

        // 空的报错
        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getInstanceType);
        errors.add(error);
        boolean findOne = false;
        for (String type : types) {
            if (Strings.equalsIgnoreCase(type, instanceType)) {
                findOne = true;
                oneData.setInstanceType(type);
                break;
            }
        }
        if (!findOne) {
            if (Lang.isEmpty(types)) {
                errors.add(makeError(row, PplImportExcelDTO::getInstanceType,
                        oneData.getZoneName() + " 可用区不存在可选实例类型"));
            } else {
                errors.add(makeError(row, PplImportExcelDTO::getInstanceType,
                        "实例类型不在下列范围内(由可用区筛选)： " + types));
            }
        }

        if (error == null && findOne) {

            error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getInstanceModelCpuCore);
            errors.add(error);
            error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getInstanceModelRam);
            errors.add(error);

            if (coreNum == null) {
                errors.add(makeError(row, PplImportExcelDTO::getInstanceModelCpuCore, "解析cpu核心数出错"));
            }
            if (ramNum == null) {
                errors.add(makeError(row, PplImportExcelDTO::getInstanceModelRam, "解析内存数出错"));
            }

            if (coreNum != null && ramNum != null) {
//                    List<InstanceModelInfoDTO> instanceModelInfos = pplDictService.queryInstanceModelInfo(
//                            isRandomZone ? "" : oneData.getZoneName(),
//                            oneData.getInstanceType());
                List<InstanceModelInfoDTO> instanceModelInfos;

                instanceModelInfos = type2InstanceModelInfoMap.getOrDefault(oneData.getInstanceType(),
                        Lang.list());

                if (Lang.isNotEmpty(instanceModelInfos)) {
                    instanceModelInfos = instanceModelInfos.stream().distinct().collect(Collectors.toList());
                }

                List<InstanceModelInfoDTO> filter = ListUtils.filter(instanceModelInfos,
                        (o) -> Strings.isNotBlank(o.getInstanceModel())
                                && Objects.equals(o.getCoreNum(), coreNum)
                                && Objects.equals(o.getRamNum(), ramNum));
                if (Lang.isEmpty(filter)) {

                    List<Tuple2<Integer, Integer>> collect = instanceModelInfos.stream()
                            .map((o) -> Tuple.of(o.getCoreNum(), o.getRamNum())).distinct()
                            .collect(Collectors.toList());
                    String info = oneData.getInstanceType() + " 没有找到对应核心和内存的实例规格,可选范围 [(CPU核心，内存GB)]："
                            + collect;
                    errors.add(makeError(row, PplImportExcelDTO::getInstanceModel, info));
                } else {
                    oneData.setInstanceModel(filter.get(0).getInstanceModel());
                }
            }
        }

        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getInstanceNum);
        if (error == null) {
            Integer integer = NumberUtils.parseInt(oneData.getInstanceNum());
            if (integer == null) {
                error = makeError(row, PplImportExcelDTO::getInstanceNum,
                        "解析数字出错： " + oneData.getInstanceNum());
            }
        }
        errors.add(error);

        if (Strings.isNotBlank(oneData.getAffinityType())) {
            List<String> tmpAff = Lang.list("母机", "交换机", "机柜");
            error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getAffinityType, tmpAff);
            errors.add(error);
        }

        if (Strings.isNotBlank(oneData.getAffinityValue())) {
            BigDecimal affinityValue = NumberUtils.parseBigDecimal(oneData.getAffinityValue());
            if (affinityValue == null) {
                error = makeError(row, PplImportExcelDTO::getAffinityValue,
                        "解析数字出错： " + oneData.getAffinityValue());
                errors.add(error);
            }
        }

        if (Strings.equals(oneData.getIsAcceptAlternative(), "是")) {
            String alterInstance = oneData.getAlternativeInstanceType();
            if (Strings.isNotBlank(alterInstance)) {
                List<String> list = Lang.list(alterInstance.split(";"));
                alternativeInstances.addAll(list);
            }
        }


    }

    //磁盘相关数据校验
    protected void checkCBSRelevant(List<PplItemImportRsp.ErrorMessage> errors,
            PplImportExcelDTO oneData, Integer row, String product) {
        PplItemImportRsp.ErrorMessage error = null;
        List<String> systemDiskTypes = PplDiskTypeEnum.systemDiskNameList;
        List<String> dataDiskTypes = PplDiskTypeEnum.dataDiskNameList;
        if (Strings.isNotBlank(oneData.getDataDiskType())) {

            error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getDataDiskType, dataDiskTypes);
            errors.add(error);

            // EMR、ES、云数仓才能使用 大数据云盘、高IO型云硬盘
            Tuple2<Boolean, String> res = PplDiskTypeEnum.checkDataDiskForPpl(oneData.getDataDiskType(), product);
            if (!res._1) {
                error = makeError(row, PplImportExcelDTO::getDataDiskType, res._2);
                errors.add(error);
            }

            if (Strings.isNotBlank(oneData.getDataDiskStorage())) {
                error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getDataDiskStorage);
                if (error == null) {
                    Integer integer = NumberUtils.parseInt(oneData.getDataDiskStorage());
                    if (integer == null) {
                        error = makeError(row, PplImportExcelDTO::getDataDiskStorage,
                                "解析数字出错： " + oneData.getDataDiskStorage());
                        errors.add(error);
                    } else {
                        if (integer > 32000 || integer < 0) {
                            // https://tapd.woa.com/69994695/prong/stories/view/1069994695116927242
                            // 单块数据盘容量：填写范围为0～32000
                            error = makeError(row, PplImportExcelDTO::getDataDiskStorage,
                                    "单块数据盘容量：填写范围为0～32000; 当前值："
                                            + oneData.getDataDiskStorage());
                            errors.add(error);
                        }
                    }
                }
            }

            if (Strings.isNotBlank(oneData.getDataDiskNum())) {
                error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getDataDiskNum);
                if (error == null) {
                    Integer integer = NumberUtils.parseInt(oneData.getDataDiskNum());
                    if (integer == null) {
                        error = makeError(row, PplImportExcelDTO::getDataDiskNum,
                                "解析数字出错： " + oneData.getDataDiskNum());
                    }
                }
                errors.add(error);
            }

        }
        if (Strings.isNotBlank(oneData.getSystemDiskType())) {
            error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getSystemDiskType, systemDiskTypes);
            errors.add(error);

            if (Strings.isNotBlank(oneData.getSystemDiskStorage())) {
                error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getSystemDiskStorage);
                if (error == null) {
                    Integer integer = NumberUtils.parseInt(oneData.getSystemDiskStorage());
                    if (integer == null) {
                        error = makeError(row, PplImportExcelDTO::getSystemDiskStorage,
                                "解析数字出错： " + oneData.getSystemDiskStorage());
                        errors.add(error);
                    } else {
                        if (integer > 2048 || integer < 0) {
                            // https://tapd.woa.com/69994695/prong/stories/view/1069994695116927242
                            // 单台系统盘容量：填写范围为0～2048
                            error = makeError(row, PplImportExcelDTO::getSystemDiskStorage,
                                    "单台系统盘容量：填写范围为0～2048; 当前值： "
                                            + oneData.getSystemDiskStorage());
                            errors.add(error);
                        }
                    }
                }
            }
        }

    }

    //  ------------  Gpu相关校验

    // 业务场景和业务类型校验
    protected void checkBizSceneAndDetail(List<PplItemImportRsp.ErrorMessage> errors,
            PplImportExcelDTO oneData, Integer row) {
        PplItemImportRsp.ErrorMessage error = null;
        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getBizScene);
        errors.add(error);
        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getBizDetail);
        errors.add(error);

    }

    // gpu实例相关校验
    protected GpuDTO checkGpuRelevant(List<PplItemImportRsp.ErrorMessage> errors,
            PplImportExcelDTO oneData, Integer row,
            Map<String, String> regionNameMap, Map<String, String> zoneNameMap,
            List<PplGpuRegionZoneDO> pplGpuRegionZoneList) {
        PplItemImportRsp.ErrorMessage error = null;
        Map<String, List<PplGpuRegionZoneDO>> regionToInstanceMap = pplGpuRegionZoneList.stream()
                .collect(Collectors.groupingBy(PplGpuRegionZoneDO::getRegion));
        // 根据 实例类型-卡数-cpu数-内存数 获取信息
        Map<String, PplGpuRegionZoneDO> groupGpuMap = pplGpuRegionZoneList.stream()
                .collect(Collectors.toMap(v -> Strings.join("-",
                                Arrays.asList(v.getInstanceType(), v.getGpuNum(), v.getCpuNum(), v.getMemory())), v -> v,
                        (v1, v2) -> v1));
        Map<String, List<PplGpuRegionZoneDO>> instanceTypeMap = pplGpuRegionZoneList.stream()
                .collect(Collectors.groupingBy(v -> v.getInstanceType()));
        List<PplGpuRegionZoneDO> whiteList = pplGpuRegionZoneList.stream().filter(v -> v.getIsWhite() == true)
                .collect(Collectors.toList());
        List<String> whiteNameList = pplGpuRegionZoneList.stream().filter(v -> v.getIsWhite() == true)
                .map(PplGpuRegionZoneDO::getInstanceType).collect(Collectors.toList());
        GpuDTO gpuDTO = new GpuDTO();
        Integer instanceModelRam = null;
        Integer instanceModelCpuCore = null;
        BigDecimal gpuNum = null;
        BigDecimal instanceNum = null;

        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getInstanceType);
        errors.add(error);
        if (error == null) {
            //gpu实例类型校验
            if (instanceTypeMap.get(oneData.getInstanceType()) == null) {
                error = makeError(row, PplImportExcelDTO::getInstanceType,
                        "实例类型: " + oneData.getInstanceType() + " 不是GPU实例");
                errors.add(error);
            } else {
                PplGpuRegionZoneDO pplGpuRegionZoneDO = instanceTypeMap.get(oneData.getInstanceType()).get(0);
                gpuDTO.setGpuType(pplGpuRegionZoneDO.getGpuType());
                gpuDTO.setGpuProductType(pplGpuRegionZoneDO.getGpuProductType());
            }
        }

        if (oneData.getGpuNum() == null) {
            error = makeError(row, PplImportExcelDTO::getGpuNum,
                    "卡数不能为空");
        }
//        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getGpuNum);
        errors.add(error);
        if (error == null) {
            gpuNum = oneData.getGpuNum();
            if (gpuNum.floatValue() <= 0) {
                error = makeError(row, PplImportExcelDTO::getGpuNum,
                        "单台卡数需为数字且>0");
                errors.add(error);
            }
        }

        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getInstanceModelCpuCore);
        errors.add(error);
        if (error == null) {
            instanceModelCpuCore = Integer.parseInt(oneData.getInstanceModelCpuCore());
            if (instanceModelCpuCore <= 0) {
                error = makeError(row, PplImportExcelDTO::getInstanceModelCpuCore,
                        "单台CPU核心需为数字且>0");
                errors.add(error);
            }
        }

        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getInstanceModelRam);
        errors.add(error);
        if (error == null) {
            instanceModelRam = Integer.parseInt(oneData.getInstanceModelRam());
            if (instanceModelRam <= 0) {
                error = makeError(row, PplImportExcelDTO::getInstanceModelRam,
                        "单台内存需为数字且>0");
                errors.add(error);
            }
        }

        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getInstanceNum);
        errors.add(error);
        if (error == null) {
            instanceNum = NumberUtils.parseBigDecimal(oneData.getInstanceNum());
            if (instanceNum.intValue() <= 0) {
                error = makeError(row, PplImportExcelDTO::getInstanceNum,
                        "实例数量需为数字且>0");
                errors.add(error);
            }
        }
        //region
        String region = null;
        String zone = null;
        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getRegionName);
        errors.add(error);
        if (error == null) {
            region = regionNameMap.get(oneData.getRegionName());
            if (region == null) {
                error = makeError(row, PplImportExcelDTO::getRegionName,
                        "无法找到地域 " + oneData.getRegionName());
                errors.add(error);
            }
        }

        if ("否".equals(oneData.getIsStrongDesignateZone())){
            oneData.setZoneName("随机可用区");
        }

        //可用区
        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getZoneName);
        errors.add(error);

        boolean isRandomZone =
                Strings.equals(oneData.getZoneName(), "随机")
                        || Strings.equals(oneData.getZoneName(), "随机可用区");
        if (error == null) {
            if (!isRandomZone) {
                zone = zoneNameMap.get(oneData.getZoneName());
                if (zone == null) {
                    error = makeError(row, PplImportExcelDTO::getZoneName,
                            "无法找到可用区 " + oneData.getZoneName());
                    errors.add(error);
                }
            }
        }

        Boolean numCheckTrue =
                (instanceModelRam != null && instanceModelCpuCore != null && gpuNum != null
                        && instanceNum != null); //数量参数校验正常
        if (numCheckTrue) {

            PplGpuRegionZoneDO pplGpuRegionZoneDO = groupGpuMap.get(Strings.join("-",
                    Arrays.asList(oneData.getInstanceType(), gpuNum.intValue(), instanceModelCpuCore,
                            instanceModelRam)));
            if (pplGpuRegionZoneDO != null) {
                gpuDTO.setInstanceModel(pplGpuRegionZoneDO.getInstanceModel());
                gpuDTO.setTotalCore(instanceModelCpuCore * instanceNum.intValue());
                gpuDTO.setTotalDisk(instanceModelRam * instanceNum.intValue());
                gpuDTO.setTotalGpu(gpuNum.multiply(instanceNum));
            } else {
                List<PplGpuRegionZoneDO> satisfyList = instanceTypeMap.get(oneData.getInstanceType());
                if (CollectionUtils.isEmpty(satisfyList)) {
                    error = makeError(row, PplImportExcelDTO::getInstanceType,
                            "无法找到该机型 " + oneData.getInstanceType());
                    errors.add(error);
                } else {
                    Set<String> satisfySet = satisfyList.stream().map(v -> Strings.join("-",
                                    Arrays.asList(v.getInstanceType(), v.getGpuNum(), v.getCpuNum(), v.getMemory())))
                            .collect(Collectors.toSet());
                    String recommendStr = "";
                    for (String str : satisfySet) {
                        List<String> split = new ArrayList<>(Arrays.asList(str.split("-")));
                        split.remove(0);
                        String recommendOne = "(" + Strings.join("/", split) + ")";
                        recommendStr = recommendStr + recommendOne;
                    }

                    error = makeError(row, PplImportExcelDTO::getInstanceType,
                            "所选规格不支持，支持配置为(卡数/核心数/内存数)：" + recommendStr);
                    errors.add(error);

                }
            }

        }

        return gpuDTO;

    }


    protected GroupItemDTO convert(PplImportExcelDTO oneData, String industryDept, String product,
            DateDTO dateDTO, QueryInfoByUinRsp queryInfoByUinRsp) {
        GroupItemDTO tmp = GroupItemDTO.trans(oneData);
        CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getByName(oneData.getCustomerTypeName());
        if (customerTypeEnum != null) {
            tmp.setCustomerType(customerTypeEnum.getCode());
        }
        tmp.setYear(dateDTO.getYear());
        tmp.setMonth(dateDTO.getMonth());
        tmp.setYearMonth(dateDTO.getYearMonth());
        tmp.setCustomerShortName(oneData.getCustomerShortName());
        if (queryInfoByUinRsp != null && queryInfoByUinRsp.getIsExist().equals(Boolean.TRUE)) {
            if (Strings.isNotBlank(queryInfoByUinRsp.getCustomerShortName())) {
                tmp.setCustomerShortName(queryInfoByUinRsp.getCustomerShortName());
            }
            if (Strings.isBlank(tmp.getCustomerShortName())) {
                tmp.setCustomerTypeName(tmp.getCustomerName());
            }
            tmp.setIndustry(queryInfoByUinRsp.getIndustry());
            tmp.setCustomerSource(queryInfoByUinRsp.getCustomerSource());
            tmp.setCustomerName(queryInfoByUinRsp.getCustomerName());
        }

        BigDecimal winRate = null;
        if (Strings.isNotBlank(oneData.getWinRate())) {
            String replace = oneData.getWinRate().replace('%', ' ').trim();
            winRate = NumberUtils.parseBigDecimal(replace);
        }
        tmp.setWinRate(winRate);
        tmp.setWinRateStr(oneData.getWinRate());

        tmp.setBeginBuyDate(dateDTO.getBeginBuyDateRet());
        tmp.setEndBuyDate(dateDTO.getEndBuyDateRet());
        tmp.setBeginElasticDate(dateDTO.getBeginElasticDateRet());
        tmp.setEndElasticDate(dateDTO.getEndElasticDateRet());

        Integer systemStorage = NumberUtils.parseInt(oneData.getSystemDiskStorage());
        Integer diskNum = NumberUtils.parseInt(oneData.getDataDiskNum());
        Integer diskStorage = NumberUtils.parseInt(oneData.getDataDiskStorage());
        int oneDiskSize = 0;
        if (systemStorage != null) {
            oneDiskSize += systemStorage;
        }
        if (diskNum != null && diskStorage != null) {
            oneDiskSize += diskNum * diskStorage;
        }
        Integer num = NumberUtils.parseInt(oneData.getInstanceNum());
        // 默认取传入的值
        Integer coreNum = NumberUtils.parseInt(oneData.getInstanceModelCpuCore());
        if (coreNum == null) {
            // 这里是前面解析实例规格的结果
            coreNum = tmp.getInstanceModelCoreNum();
        }
        coreNum = coreNum == null ? 0 : coreNum;
        tmp.setTotalCoreNum(coreNum * (num == null ? 0 : num));
        tmp.setTotalDiskNum(oneDiskSize * (num == null ? 0 : num));
        tmp.setInstanceModelCoreNum(coreNum);
        Integer ramNum = NumberUtils.parseInt(oneData.getInstanceModelRam());
        if (ramNum != null) {
            // 默认取传入的值
            tmp.setInstanceModelRamNum(ramNum);
        }

        tmp.setAffinityValue(NumberUtils.parseBigDecimal(oneData.getAffinityValue()));

        List<String> alternativeInstances = new ArrayList<>();
        if (Strings.equals(oneData.getIsAcceptAlternative(), "是")) {
            String alterInstance = oneData.getAlternativeInstanceType();
            if (Strings.isNotBlank(alterInstance)) {
                List<String> list = Lang.list(alterInstance.split(";"));
                alternativeInstances.addAll(list);
            }
        }
        tmp.setAlternativeInstanceType(alternativeInstances);
        if (Strings.isNotBlank(oneData.getAlternativeZoneName())) {
            List<String> list = Lang.list(oneData.getAlternativeZoneName().split(";"));
            tmp.setAlternativeZoneName(list);
            tmp.setAlternativeZoneNamesJoin(oneData.getAlternativeZoneName());
        }
        tmp.setProduct(product);

        // 2022-12-13 增加推荐机型
        List<String> mainInstanceTypes = pplDictService.queryMainInstanceType(tmp.getZoneName());

        boolean isMainInstanceType = mainInstanceTypes.contains(tmp.getInstanceType());
        tmp.setIsRecommendedInstanceType(isMainInstanceType);
        tmp.setBizId(oneData.getBizId());

        // 总内存
        if (tmp.getInstanceNum() != null && tmp.getInstanceModelRamNum() != null) {
            tmp.setTotalMemory(tmp.getInstanceNum() * tmp.getInstanceModelRamNum());
        }
        return tmp;
    }


    ExecutorService executorService = Executors.newFixedThreadPool(5);

    @SneakyThrows
    public List<PplCvmImportExcelDTO> getGeneralCvmDictData(HashMap<String, Integer> config) {

        String demandSceneSql = "select distinct ${column} "
                + "from ppl_config_demand_scene order by ${column};";

        Future<List<String>> billTypeFuture = executorService.submit(() -> {
            return pplDictService.queryBillType();
        });
        Future<List<String>> citysFuture = executorService.submit(() -> {
            return pplDictService.queryAllCityName(null);
        });
        Future<List<IndustryDemandRegionZoneInstanceTypeDictDO>> zonesFuture = executorService.submit(() -> {
            return pplDictService.queryAllZoneName();
        });
        Future<List<String>> instanceTypeFuture = executorService.submit(() -> {
            return pplDictService.queryInstanceType("");
        });
        Future<List<String>> instanceModelFuture = executorService.submit(() -> {
            return pplDictService.queryInstanceModel(null, null);
        });
        Future<List<String>> demandSceneFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class,
                    demandSceneSql.replace("${column}", "demand_scene"));
        });
        Future<List<String>> demandTypeFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class,
                    demandSceneSql.replace("${column}", "demand_type"));
        });

        String coreSpl = "select distinct parse_ram\n"
                + "from industry_demand_region_zone_instance_type_dict where deleted=0 and parse_ram > 0 order by parse_ram";
        Future<List<String>> parseCpuFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class, coreSpl.replace("parse_ram", "parse_core"));
        });
        Future<List<String>> parseRamFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class, coreSpl);
        });

        List<IndustryDemandRegionZoneInstanceTypeDictDO> zones = zonesFuture.get();
        List<String> demandScene = demandSceneFuture.get();
        List<String> billType = billTypeFuture.get();
        List<String> demandType = demandTypeFuture.get();
        List<String> cityNames = citysFuture.get();
        List<String> instanceType = instanceTypeFuture.get();
        List<String> instanceModel = instanceModelFuture.get();
        List<String> parseCpu = parseCpuFuture.get();
        List<String> parseRam = parseRamFuture.get();

        List<String> AffType = Lang.list("母机", "交换机", "机柜");
        List<String> diskTypes = Lang.list("SSD", "高性能");

        List<String> winRateRange = Lang.list();

        config.put("D", demandType.size());
        config.put("E", demandScene.size());
        config.put("G", billType.size());
        config.put("N", cityNames.size());
        config.put("O", zones.size());
        config.put("P", instanceType.size());
        config.put("Q", instanceModel.size());
        config.put("V", 2);
        config.put("X", AffType.size());
        config.put("Z", diskTypes.size());
        config.put("AB", diskTypes.size());
        config.put("R", parseCpu.size());
        config.put("S", parseRam.size());

        for (int cnt = 0; cnt <= 100; cnt++) {
            winRateRange.add(cnt + "%");
        }
        config.put("H", winRateRange.size());

        List<PplCvmImportExcelDTO> ret = Lang.list();
        for (int i = 0; i < 1000; i++) {
            PplCvmImportExcelDTO one = new PplCvmImportExcelDTO();

            if (i < parseCpu.size()) {
                one.setInstanceModelCpuCore(parseCpu.get(i));
            }
            if (i < parseRam.size()) {
                one.setInstanceModelRam(parseRam.get(i));
            }

            if (i < demandScene.size()) {
                one.setDemandScene(demandScene.get(i));
            }
            if (i < demandType.size()) {
                one.setDemandTypeName(demandType.get(i));
            }
            if (i == 0) {
                one.setBeginElasticDate("00:00");
                one.setEndElasticDate("23:59");
                one.setNote("备注");
                one.setIsAcceptAlternative("是");
                one.setZoneName("随机可用区");
            }
            if (i == 1) {
                one.setIsAcceptAlternative("否");
            }

            if (i < billType.size()) {
                one.setBillType(billType.get(i));
            }
            if (i < cityNames.size()) {
                one.setRegionName(cityNames.get(i));
            }
            if (i < instanceType.size()) {
                one.setInstanceType(instanceType.get(i));
            }
            if (i < instanceModel.size()) {
                one.setInstanceModel(instanceModel.get(i));
            }
            if (i > 0 && i < zones.size()) {
                one.setZoneName(zones.get(i).getZoneName());
            }
            if (i < AffType.size()) {
                one.setAffinityType(AffType.get(i));
            }
            if (i < winRateRange.size()) {
                one.setWinRate(winRateRange.get(i));
            }
            if (i < diskTypes.size()) {
                one.setDataDiskType(diskTypes.get(i));
                one.setSystemDiskType(diskTypes.get(i));
            }
            ret.add(one);
        }
        return ret;
    }

    protected <K> PplItemImportRsp.ErrorMessage makeError(int raw, IGetter<K> fn, String message) {
        return new PplItemImportRsp.ErrorMessage(raw, getColIndex(fn), getColName(fn), message);
    }

    private <K> ExcelProperty getEP(IGetter<K> fn) {
        return ORMUtils.getAnnotationByGetter(ExcelProperty.class, fn);
    }

    private <K> String getColName(IGetter<K> fn) {
        return getEP(fn).value()[0];
    }

    private <K> int getColIndex(IGetter<K> fn) {
        return getEP(fn).index() + 1;
    }

    @Data
    public class DateDTO {

        int year;
        int month;
        String yearMonth;
        LocalDate beginBuyDateRet;
        LocalDate endBuyDateRet;
        LocalTime beginElasticDateRet;
        LocalTime endElasticDateRet;
    }

    @Data
    public class GpuDTO {

        String gpuProductType;

        String gpuType;

        String instanceModel;

        Integer totalCore = 0;

        Integer totalDisk = 0;

        BigDecimal totalGpu = BigDecimal.valueOf(0);
    }
}
