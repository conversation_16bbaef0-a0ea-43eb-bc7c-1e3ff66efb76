package cloud.demand.app.modules.p2p.ppl13week.controller;

import cloud.demand.app.modules.common.model.rsp.ListResp;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.applied_supply.QueryAppliedSupplyListReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.physcical.QueryPhysicalReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.physcical.QueryPhysicalResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ApproveVersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ComdInterveneByForecastModelReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ComdPplExportReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ExportPplVersionItemReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ImportVersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.InitNewVersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionGroupResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionGroupView;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionInterveneByForecastReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionInterveneByForecastResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.SaveVersionGroupItemReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.SyncYunTiPplReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupApproveResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupForecastResultViewResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupIdReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupInfoResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupSpikeViewResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupStatReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupStatResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupSummaryResp;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionGroupStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplAppliedSupplyService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplComdInterveneService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplConvertPhysicalServerService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionGroupService;
import cloud.demand.app.modules.p2p.ppl13week.service.excel.PplExcelParseServiceAdapter;
import cloud.demand.app.modules.p2p.ppl13week.service.excel.comd_intervene.PplComdExcelParseServiceAdapter;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplAppliedSupplyVO;
import cloud.demand.app.web.model.common.DownloadBean;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Strings;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;
import yunti.boot.util.YuntiUtils;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@JsonrpcController("/ppl13week")
@Slf4j
public class PplVersionGroupController {

    @Resource
    private PplVersionGroupService pplVersionGroupService;
    @Resource
    private PplConvertPhysicalServerService pplConvertPhysicalServerService;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private PplExcelParseServiceAdapter pplExcelParseServiceAdapter;
    @Resource
    private PplComdExcelParseServiceAdapter comdExcelParseServiceAdapter;
    @Resource
    private PplAppliedSupplyService pplAppliedSupplyService;
    @Resource
    private PplComdInterveneService pplComdInterveneService;

    @RequestMapping
    public List<String> queryVersionCode() {
        return pplVersionGroupService.queryVersionCode();
    }

    @RequestMapping
    public List<Map<String, Object>> queryVersionGroupStatus() {
        return ListUtils.transform(PplVersionGroupStatusEnum.values(),
                o -> MapUtils.of("code", o.getCode(), "name", o.getName()));
    }


    /**
     * updateYunXiaoApplyData
     *
     * @return info
     */
    @RequestMapping
    public ImmutableMap<String, String> updateYunXiaoApplyData(@JsonrpcParam VersionGroupIdReq req) {
        pplVersionGroupService.updateYunXiaoApplyData(req.getGroupId());
        return ImmutableMap.of("data", "success");
    }

    /**
     * 分组列表
     */
    @RequestMapping
    public QueryVersionGroupResp queryVersionGroupList(@JsonrpcParam QueryVersionGroupReq req) {
        return pplVersionGroupService.queryVersionGroup(req);
    }

    /**
     * 查询分组信息(概要)
     */
    @RequestMapping
    public VersionGroupInfoResp queryVersionGroupInfo(@JsonrpcParam @Valid VersionGroupIdReq req) {
        return pplVersionGroupService.queryVersionGroupInfo(req.getGroupId());
    }

    /**
     * 查询审批记录
     */
    @RequestMapping
    public VersionGroupApproveResp queryVersionGroupApproveLog(@JsonrpcParam @Valid VersionGroupIdReq req) {
        return pplVersionGroupService.queryVersionGroupApproveLog(req.getGroupId());
    }

    /**
     * 统计信息
     */
    @RequestMapping
    public VersionGroupStatResp queryVersionGroupStat(@JsonrpcParam @Valid VersionGroupStatReq req) {
        return pplVersionGroupService.queryVersionGroupStat(req);
    }

    /**
     * 统计ppl 的毛刺和非毛刺
     */
    @RequestMapping
    public VersionGroupSpikeViewResp queryVersionGroupSpikeView(@JsonrpcParam @Valid VersionGroupStatReq req) {
        return pplVersionGroupService.queryVersionGroupSpikeView(req);
    }

    /**
     * 模型预测结果的图表数据
     */
    @RequestMapping
    public VersionGroupForecastResultViewResp queryVersionGroupForecastResultView(
            @JsonrpcParam @Valid VersionGroupStatReq req) {
        return pplVersionGroupService.queryVersionGroupForecastResultView(req);
    }

    /**
     * 统计信息
     */
    @RequestMapping
    public VersionGroupSummaryResp queryVersionGroupSummary(@JsonrpcParam @Valid VersionGroupStatReq req) {
        return pplVersionGroupService.queryVersionGroupSummary(req);
    }

    /**
     * 查询分组明细item
     */
    @RequestMapping
    public VersionGroupItemResp queryVersionGroupItem(@JsonrpcParam @Valid VersionGroupIdReq req) {
        return pplVersionGroupService.queryVersionGroupItem(req.getGroupId());
    }

    @RequestMapping
    public Object checkVersionDeadline() {
        pplVersionGroupService.checkVersionDeadline();
        return ImmutableMap.of("data", "success");
    }

    @RequestMapping
    public Map<String, Object> saveVersionGroupItem(@JsonrpcParam @Valid SaveVersionGroupItemReq req) {
        pplVersionGroupService.saveVersionGroupItem(req);
        return MapUtils.of("result", "success");
    }

    @RequestMapping
    public Map<String, Object> initNewVersionGroup(@JsonrpcParam @Valid InitNewVersionGroupReq req) {
        pplVersionGroupService.initVersionGroupItem(req);
        return MapUtils.of("result", "success");
    }

    /**
     * 查新可视化视图
     */
    @RequestMapping
    public Map<String, Integer> queryVersionGroupView(@JsonrpcParam @Valid QueryVersionGroupView req) {
        return pplVersionGroupService.queryVersionGroupView(req);
    }

    @RequestMapping
    public Map<String, Object> approveVersionGroup(@JsonrpcParam @Valid ApproveVersionGroupReq req) {
        pplVersionGroupService.approveVersionGroup(req);
        return MapUtils.of("result", "success");
    }


    @Data
    public static class YearMonthDTO {

        String startYearMonth;
        String endYearMonth;
        String product;
        String industryDept;
    }

    @RequestMapping
    public PplItemImportRsp uploadPplItemDetailExcel(@RequestParam("file") MultipartFile file,
            @RequestParam Map<String, String> params) {
        try {
            return pplExcelParseServiceAdapter.execute(file, params);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("EXCEL导入失败,请联系 oliverychen 或 fireflychen 查看");
        }
    }


    @RequestMapping
    public Map<String, Object> importPplVersionItem(@JsonrpcParam @Valid ImportVersionGroupReq req) {
        pplVersionGroupService.importPplVersionItem(req);
        return MapUtils.of("result", "success");
    }


    @RequestMapping
    public DownloadBean exportPplVersionItemExcel(@JsonrpcParam ExportPplVersionItemReq req) {
        if (Strings.isBlank(req.getIndustryDept())) {
            throw new BizException("industryDept 为空");
        }
        req.setFileName(req.getIndustryDept() + "-13周PPL数据导出");
        if (req.getProduct().equals(Ppl13weekProductTypeEnum.GPU.getName())) {
            req.setExcelTemplateName("ppl13week_gpu_import.xlsx");
        } else {
            req.setExcelTemplateName("ppl13week_version_import.xlsx");
        }
        FileNameAndBytesDTO fileNameAndBytesDTO = pplVersionGroupService.exportPplVersionItemExcel(req);
        return new DownloadBean(fileNameAndBytesDTO.getFileName(), fileNameAndBytesDTO.getBytes());

    }


    // CVM
    @RequestMapping
    public DownloadBean exportPplVersionItemExcelWithStockSupply(@JsonrpcParam ExportPplVersionItemReq req) {
        req.setExcelTemplateName("ppl13week_stock_supply_export.xlsx");
        req.setFileName(req.getIndustryDept() + "-13周供应方案数据导出");
        FileNameAndBytesDTO fileNameAndBytesDTO = pplVersionGroupService.exportPplVersionItemExcelWithStockSupply(req);
        return new DownloadBean(fileNameAndBytesDTO.getFileName(), fileNameAndBytesDTO.getBytes());

    }


    @RequestMapping
    public String getNoSuccessUin() {
        pplVersionGroupService.getNoSuccessUin();
        return "success";
    }

    @RequestMapping
    public QueryPhysicalResp queryPhysicalServerList(@JsonrpcParam QueryPhysicalReq req) {
        return pplConvertPhysicalServerService.queryPhysicalServerList(req);
    }

    @RequestMapping
    public Map<String, Object> withDrawVersionGroup(@JsonrpcParam ApproveVersionGroupReq req) {
        PplVersionGroupDO groupDO = demandDBHelper.getByKey(PplVersionGroupDO.class, req.getGroupId());
        groupDO.setStatus(req.getCurrentStatus());
        PplVersionDO versionDO = demandDBHelper.getOne(PplVersionDO.class, "where version_code = ?",
                groupDO.getVersionCode());
        if (versionDO.getDeadline().before(new Date())) {
            throw new BizException("已到截止录入时间,无法撤回");
        }
        pplVersionGroupService.approveVersionGroupByAdmin(PplVersionGroupStatusEnum.IN_SUBMIT.getCode(), groupDO);
        return MapUtils.of("result", "success");
    }

    @RequestMapping
    public Map<String, Object> syncYunTiPpl(@JsonrpcParam SyncYunTiPplReq req) {
        pplVersionGroupService.syncYunTiPpl(req.getVersionCode());
        return MapUtils.of("result", "success");
    }


    /**
     * 云运管导入干预
     *
     * @param file
     * @param params
     * @return
     */
    @RequestMapping
    public PplItemImportRsp comdImportIntervene(@RequestParam("file") MultipartFile file,
            @RequestParam Map<String, String> params) {
        try {
            return comdExcelParseServiceAdapter.execute(file, params);
        } catch (Exception e) {
            if (e instanceof BizException) {
                throw e;
            } else {
                throw new BizException("EXCEL干预导入失败,请联系 kaijiazhang 查看", e);
            }
        }
    }

    /**
     * 云运管使用模型预测结果进行干预
     */
    @Synchronized(namespace = "comdInterveneByForecastModel", customExceptionMessage = "当前有其他同事正在操作一键干预，请稍等后重试")
    @RequestMapping
    public PplItemImportRsp comdInterveneByForecastModel(@JsonrpcParam ComdInterveneByForecastModelReq req,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        return pplComdInterveneService.comdInterveneByForecastModel(req);
    }

    /**
     * 一键撤销该分组下的所有干预
     */
    @Synchronized(namespace = "comdInterveneByForecastModel", customExceptionMessage = "当前有其他同事正在操作一键干预，请稍等后重试")
    @RequestMapping
    public PplItemImportRsp resetAllComdIntervene(@JsonrpcParam ComdInterveneByForecastModelReq req,
            BindingResult bindingResult) {
        return pplComdInterveneService.resetAllComdIntervene(req.getVersionGroupId());
    }

    /**
     * 查询模型一键干预的记录
     */
    @RequestMapping
    public QueryVersionInterveneByForecastResp queryInterveneByForecastLog(
            @JsonrpcParam QueryVersionInterveneByForecastReq req,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        return pplComdInterveneService.queryInterveneByForecastLog(req);
    }

    /**
     * 云运管干预PPL导出
     *
     * @param req
     * @return
     */
    @RequestMapping
    public DownloadBean comdPplExport(@JsonrpcParam ComdPplExportReq req) {
        try {
            return comdExcelParseServiceAdapter.exportExcel(req);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("EXCEL导出失败,请联系 oliverychen 或 fireflychen 查看");
        }
    }

    /**
     * 13周预测列表-预约明细-查询
     */
    @RequestMapping
    public ListResp<PplAppliedSupplyVO> queryAppliedSupplyList(@JsonrpcParam @Valid QueryAppliedSupplyListReq req) {
        return pplAppliedSupplyService.queryAppliedSupplyList(req);
    }

    /**
     * 13周预测列表-预约明细-查询-导出
     */
    @RequestMapping
    public DownloadBean exportAppliedSupplyList(@JsonrpcParam @Valid QueryAppliedSupplyListReq req) {
        return pplAppliedSupplyService.exportAppliedSupplyList(req);
    }

}
