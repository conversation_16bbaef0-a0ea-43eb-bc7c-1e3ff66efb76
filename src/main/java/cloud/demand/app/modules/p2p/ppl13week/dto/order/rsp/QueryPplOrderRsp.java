package cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderStatusEnum;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;

@Data
public class QueryPplOrderRsp {


    private String createTime;

    /**
     * PPL单号(考虑单号可能有英文，目前可以与id保持一致)<br/>Column: [ppl_order]
     */
    private String pplOrder;

    /**
     * 来源,行业录入,自动创建<br/>Column: [source]
     */
    private String source;
    private String sourceName;

    /**
     * 需求年份<br/>Column: [year]
     */
    private Integer year;

    /**
     * 需求月份<br/>Column: [month]
     */
    private Integer month;

    private String yearMonth;

    /**
     * 行业<br/>Column: [industry]
     */
    private String industry;
    private String industryDept;

    /**
     * 客户类型,存量客户,winback客户<br/>Column: [customer_type]
     */
    private String customerType;

    /**
     * 客户名称<br/>Column: [customer_name]
     */
    private String customerName;

    /**
     * 客户uin<br/>Column: [customer_uin]
     */
    private String customerUin;

    /**
     * 客户简称<br/>Column: [customer_name]
     */
    private String customerShortName;

    private String customerSource;

    /**
     * 战区<br/>Column: [customer_name]
     */
    private String warZone;

    /**
     * ppl单状态<br/>Column: [status]
     *
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderStatusEnum
     */
    private String status;
    private String statusName;

    /**
     * 提单人<br/>Column: [submit_user]
     */
    private String submitUser;

    /**
     * 当前处理人，多个分号隔开<br/>Column: [current_processor]
     */
    private String currentProcessor;


    private String meddleUserName;
    private String meddleTime;

    private Integer allCoreNum;
    private Integer allDiskNum;

    private Boolean editAuth;

    public static QueryPplOrderRsp transFrom(PplOrderDO source) {
        QueryPplOrderRsp queryPplOrderRsp = new QueryPplOrderRsp();
        queryPplOrderRsp.setCreateTime(DateUtils.format(source.getCreateTime()));
        queryPplOrderRsp.setPplOrder(source.getPplOrder());
        queryPplOrderRsp.setSource(source.getSource());
        queryPplOrderRsp.setSourceName(PplOrderSourceTypeEnum.getNameByCode(source.getSource()));
        queryPplOrderRsp.setIndustry(source.getIndustry());
        queryPplOrderRsp.setIndustryDept(source.getIndustryDept());
        queryPplOrderRsp.setCustomerType(source.getCustomerType());
        queryPplOrderRsp.setCustomerName(source.getCustomerName());
        queryPplOrderRsp.setCustomerUin(source.getCustomerUin());
        queryPplOrderRsp.setCustomerShortName(source.getCustomerShortName());
        queryPplOrderRsp.setCustomerSource(source.getCustomerSource());
        queryPplOrderRsp.setWarZone(source.getWarZone());
        queryPplOrderRsp.setStatus(source.getStatus());
        queryPplOrderRsp.setStatusName(PplOrderStatusEnum.getNameByCode(source.getStatus()));
        queryPplOrderRsp.setSubmitUser(source.getSubmitUser());
        queryPplOrderRsp.setCurrentProcessor(source.getCurrentProcessor());
//        queryPplOrderRsp.setMeddleUserName();
//        queryPplOrderRsp.setMeddleTime();
        queryPplOrderRsp.setAllCoreNum(source.getAllCore());
        queryPplOrderRsp.setAllDiskNum(source.getAllDisk());
        return queryPplOrderRsp;
    }

}
