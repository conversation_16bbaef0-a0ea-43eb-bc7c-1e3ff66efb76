package cloud.demand.app.modules.p2p.product_demand.entity;


import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.Date;

@Data
@ToString
@Table("obs_budget_device_roll_data")
public class ObsBudgetDeviceRollDataDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    @Column(value = "data_date")
    private LocalDate dataDate;

    @Column(value = "db_time")
    private Date dbTime;

    @Column(value = "bg_name")
    private String bgName;

    @Column(value = "bg_type_name")
    private String bgTypeName;

    @Column(value = "budget_product_id")
    private Integer budgetProductId;

    @Column(value = "budget_product_name")
    private String budgetProductName;

    @Column(value = "budget_type")
    private String budgetType;

    @Column(value = "city_name")
    private String cityName;

    @Column(value = "cpu_category")
    private String cpuCategory;

    @Column(value = "cpu_core_amount")
    private Double cpuCoreAmount;

    @Column(value = "create_time")
    private String createTime;

    @Column(value = "data_source_name")
    private String dataSourceName;

    @Column(value = "data_type_name")
    private String dataTypeName;

    @Column(value = "dept_name")
    private String deptName;

    @Column(value = "device_amount")
    private Double deviceAmount;

    @Column(value = "device_main_category")
    private String deviceMainCategory;

    @Column(value = "device_type")
    private String deviceType;

    @Column(value = "disk_amount")
    private Double diskAmount;

    @Column(value = "end_time")
    private String endTime;

    @Column(value = "form_code")
    private String formCode;

    @Column(value = "gpu_app_category")
    private String gpuAppCategory;

    @Column(value = "gpu_category")
    private String gpuCategory;

    @Column(value = "gpu_card_category")
    private String gpuCardCategory;

    @Column(value = "gpu_number")
    private Double gpuNumber;

    @Column(value = "memo")
    private String memo;

    @Column(value = "memory_amount")
    private Double memoryAmount;

    @Column(value = "month")
    private Integer month;

    @Column(value = "platform_id")
    private Integer platformId;

    @Column(value = "platform_name")
    private String platformName;

    @Column(value = "project_name")
    private String projectName;

    @Column(value = "total_cost_cny")
    private Double totalCostCny;

    @Column(value = "total_cost_usd")
    private Double totalCostUsd;

    @Column(value = "year")
    private Integer year;

    @Column(value = "dept_id")
    private Integer deptId;

    /** 自定义事业群<br/>Column: [custom_bg_name] */
    @Column(value = "custom_bg_name")
    private String customBgName;

    /** 规划产品id<br/>Column: [plan_product_id] */
    @Column(value = "plan_product_id")
    private Integer planProductId;

    /** 规划产品<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

}
