package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

@Data
public class QueryForecastSchemaInfoRsp {

    private Long schemaId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date generateTime;
    private String algorithmName;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transferToCRPTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    private LocalDate forecastRangeFromDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    private LocalDate forecastRangeToDate;
    private String serialIntervalName;
    private String inputDimsName;
    private String billType;
    private String billTypeName;


    private String resourcePoolName;
    private String customerScopeName;


    private Long taskId;

    private String productCode;
    private String productName;

    /**
     * 预测的单位名称
     */
    private String unitName;

}
