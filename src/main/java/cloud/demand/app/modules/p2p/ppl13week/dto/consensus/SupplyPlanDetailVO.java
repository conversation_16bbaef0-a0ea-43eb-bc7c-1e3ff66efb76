package cloud.demand.app.modules.p2p.ppl13week.dto.consensus;

import cloud.demand.app.modules.p2p.common.P2PInstanceModelParse;
import cloud.demand.app.modules.p2p.ppl13week.dto.consensus.DemandWithSupplyPlanVO.SupplyPlanVO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplConsensusStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyMatchTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.ZoneInfoFiller;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import lombok.Data;
import org.nutz.lang.Strings;

/**
 * 供应方案明细数据
 */
@Data
public class SupplyPlanDetailVO extends SupplyPlanGroupField implements ZoneInfoFiller {

    /**
     * 供应方案id （ppl_supply_consensus）
     */
    @Column(value = "id")
    private Long id;

    /* 需求字段 */
    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    @Column(value = "end_buy_date")
    private LocalDate endBuyDate;

    /**
     * 原需求核心数
     */
    @Column(value = "total_core")
    private Integer totalCore;

    /**
     * 原需求卡数
     */
    @Column(value = "total_gpu_num")
    private Integer totalGpuNum;

    // 预约状态
    @Column(value = "ppl_status")
    private String pplStatus;

    private String demandTypeName;
    /* 需求字段结束 */


    /* 供应方案字段 */
    @Column(value = "match_type")
    private String matchType;

    /**
     * 供应方案-实例规格
     */
    @Column(value = "match_instance_type")
    private String matchInstanceType;

    @Column(value = "plan_zone_name")
    private String planZoneName;

    @Column(value = "instance_total_core")
    private Integer instanceTotalCore;

    @Column(value = "instance_total_gpu")
    private Integer instanceTotalGpu;

    @Column(value = "operate_note")
    private String operateNote;

    @Column(value = "consensus_status")
    private String consensusStatus;

    @Column(value = "operate_user")
    private String operateUser;

    // 对冲备注
    @Column(value = "remark")
    private String remark;

    // 供应方案-地域
    @Column(value = "plan_region_name")
    private String planRegionName;

    /**
     *  对冲需求满足的日期
     */
    @Column(value = "match_demand_date")
    private LocalDate matchDemandDate;

    /**
     *  行业共识的需求满足日期
     */
    @Column(value = "consensus_demand_date")
    private LocalDate consensusDemandDate;

    /**
     *  行业共识的实例类型（例S5）
     */
    @Column(value = "consensus_instance_type")
    private String consensusInstanceType;

    /**
     *  行业共识的可用区
     */
    @Column(value = "consensus_zone_name")
    private String consensusZoneName;

    private String consensusStatusName;

    private PplConsensusStatusEnum consensusStatusEnum;

    // 可处理人
    private Set<String> allowOperateUser;

    private String matchTypeName;

    /**
     * 实例类型，根据{@link #matchInstanceType} 按照规则生成,{@link #handleMatchInstance(SupplyPlanDetailVO)}
     */
    private String matchInstance;

    /* 供应方案字段 结束 */


    /**
     * 数据结构转换，将供应方案明细数据转换成 pplId信息和它对应的供应方案列表
     *
     * @param detailList 供应方案明细数据
     */
    public static List<DemandWithSupplyPlanVO> convert(List<SupplyPlanDetailVO> detailList) {
        List<DemandWithSupplyPlanVO> result = new ArrayList<>();
        if (ListUtils.isEmpty(detailList)) {
            return result;
        }
        Collection<List<SupplyPlanDetailVO>> groupData = ListUtils.groupBy(detailList, SupplyPlanDetailVO::getPplId)
                .values();
        for (List<SupplyPlanDetailVO> dataList : groupData) {
            SupplyPlanDetailVO first = dataList.get(0);
            DemandWithSupplyPlanVO item = new DemandWithSupplyPlanVO();
            if (first.getDemandYearMonth() == null) {
                handleDemandYearMonth(first);
            }
            item.copy(first);
            item.setPplId(first.getPplId());
            item.setTotalCore(first.getTotalCore());
            item.setTotalGpuNum(first.getTotalGpuNum());
            item.setBeginBuyDate(first.getBeginBuyDate());
            item.setEndBuyDate(first.getEndBuyDate());
            item.setPplStatus(first.getPplStatus());
            List<SupplyPlanVO> supplyPlanList = new ArrayList<>(dataList.size());
            dataList.forEach(data -> supplyPlanList.add(toSupplyPlanVO(data)));
            item.setSupplyPlanList(supplyPlanList);
            result.add(item);
        }
        return result;
    }

    public static SupplyPlanVO toSupplyPlanVO(SupplyPlanDetailVO detail) {
        if (detail == null) {
            return null;
        }
        SupplyPlanVO result = new SupplyPlanVO();
        result.setMatchType(detail.getMatchType());
        result.setMatchInstanceType(detail.getMatchInstanceType());
        result.setPlanZoneName(detail.getPlanZoneName());
        result.setInstanceTotalCore(detail.getInstanceTotalCore());
        result.setOperateNote(detail.getOperateNote());
        result.setConsensusStatus(detail.getConsensusStatus());
        result.setOperateUser(detail.getOperateUser());
        return result;
    }

    public static void handleDemandYearMonth(SupplyPlanDetailVO vo) {
        if (vo != null && vo.getBeginBuyDate() != null) {
            vo.setDemandYearMonth(LocalDateTimeUtil.format(vo.getBeginBuyDate(), "yyyy-MM"));
        }
    }

    public static void handleMatchInstance(SupplyPlanDetailVO vo) {
        if (vo != null && vo.getMatchInstanceType() != null) {
            vo.setMatchInstance(P2PInstanceModelParse.parse2InstanceType(vo.getMatchInstanceType()));
        }
    }

    public void fillBySelf() {
        // 其他字段赋值
        PplConsensusStatusEnum statusEnum = PplConsensusStatusEnum.getByCode(this.getConsensusStatus());
        this.setConsensusStatusEnum(statusEnum);
        this.setConsensusStatusName(statusEnum == null ? null : statusEnum.getName());

        PplStockSupplyMatchTypeEnum matchType = PplStockSupplyMatchTypeEnum.getByCode(this.getMatchType());
        this.setMatchTypeName(matchType == null ? null : matchType.getName());

        PplDemandTypeEnum demandType = PplDemandTypeEnum.getByCode(this.getDemandType());
        this.setDemandTypeName(demandType == null ? null : demandType.getName());

        SupplyPlanDetailVO.handleDemandYearMonth(this);
        SupplyPlanDetailVO.handleMatchInstance(this);
    }

    public String consensusSupplyPlan() {
        String matchTypeName = PplStockSupplyMatchTypeEnum.getNameByCode(this.matchType);
        if (Strings.isBlank(matchTypeName)) {
            return "";
        }
        String core = (this.instanceTotalCore == null ? "0" : this.instanceTotalCore.toString());
        String unit = "核";
        return matchTypeName + core + unit;
    }

    @Override
    public String provideZoneName() {
        return this.getZoneName();
    }

    @Override
    public void fillZone(String zone) {
        // 暂时不需要此字段
    }

    @Override
    public void fillAreaName(String areaName) {
        // 暂时不需要此字段
    }

    @Override
    public void fillCustomhouseTitle(String customhouseTitle) {
        this.setCustomhouseTitle(customhouseTitle);
    }
}
