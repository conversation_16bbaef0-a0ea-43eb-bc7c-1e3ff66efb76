package cloud.demand.app.modules.p2p.industry_demand.dto.product;

import cloud.demand.app.common.utils.DateUtils.YearMonth;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.springframework.util.StringUtils;

@Data
public class GpuDTO extends BaseProductDTO {

    private String prePaidType;
    private String instanceType;
    private String gpuType;

    private String productPattern;

    public static GpuDTO excel2DTO(Map<Integer, String> map, List<YearMonth> yearMonthArray) {
        GpuDTO gpuDTO = new GpuDTO();
        gpuDTO.excelBaseSetting(map, 4, yearMonthArray);
        gpuDTO.setPrePaidType(get(map, 0));
        gpuDTO.setInstanceType(get(map, 3));
        gpuDTO.setGpuType(get(map, 2));
        gpuDTO.setProductPattern(get(map, 1));
        return gpuDTO;
    }

    public static GpuDTO toDTO(Map<String, Object> data, List<YearMonth> yearMonthList) {
        GpuDTO dto = new GpuDTO();
        BaseProductDTO.baseMap2DTO(data, dto, yearMonthList);
        dto.setPrePaidType((String) data.get("prePaidType"));
        dto.setInstanceType((String) data.get("instanceType"));
        dto.setGpuType((String) data.get("gpuType"));
        dto.setProductPattern((String) data.get("productPattern"));
        return dto;
    }

    @Override
    public void trim() {
        super.trim();
        this.prePaidType = StringUtils.trimWhitespace(prePaidType);
        this.instanceType = StringUtils.trimWhitespace(instanceType);
        this.gpuType = StringUtils.trimWhitespace(gpuType);
        this.productPattern = StringUtils.trimWhitespace(productPattern);
    }

    @Override
    public String[] getHeaders() {
        String[] common = super.getHeaders();
        String[] my = new String[]{"prePaidType", "productPattern", "gpuType", "instanceType"};
        String[] yearMonth = getYearMonthHeader(false);
        String[] result = new String[common.length + my.length + yearMonth.length];
        System.arraycopy(common, 0, result, 0, common.length);
        System.arraycopy(my, 0, result, common.length, my.length);
        System.arraycopy(yearMonth, 0, result, common.length + my.length, yearMonth.length);
        return result;
    }

    @Override
    public String[] getChineseHeaders() {
        String[] common = super.getChineseHeaders();
        String[] my = new String[]{"付费类型", "产品形态", "卡型", "实例类型"};
        String[] yearMonth = getYearMonthHeader(true);
        String[] result = new String[common.length + my.length + yearMonth.length];
        System.arraycopy(common, 0, result, 0, common.length);
        System.arraycopy(my, 0, result, common.length, my.length);
        System.arraycopy(yearMonth, 0, result, common.length + my.length, yearMonth.length);
        return result;
    }

    @Override
    public Map<String, Object> toMap() {
        Map<String, Object> result = super.toMap();
        result.put("prePaidType", this.getPrePaidType());
        result.put("instanceType", this.getInstanceType());
        result.put("gpuType", this.getGpuType());
        result.put("productPattern", this.getProductPattern());
        return result;
    }

    @Data
    public static class Json {

        private String prePaidType;
        private String instanceType;
        private String gpuType;

        private String productPattern;

        public Json(BaseProductDTO rawDto) {
            GpuDTO dto = (GpuDTO) rawDto;
            this.prePaidType = dto.prePaidType;
            this.instanceType = dto.instanceType;
            this.gpuType = dto.gpuType;
            this.productPattern = dto.productPattern;
        }
    }
}
