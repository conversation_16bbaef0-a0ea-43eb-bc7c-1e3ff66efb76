package cloud.demand.app.modules.p2p.ppl13week.dto.item;

import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplRecordChangeEventEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplRecordChangeTypeEnum;
import com.pugwoo.dbhelper.annotation.Column;
import java.util.List;
import lombok.Data;

@Data
public class PplItemChangeRecordNewDTO {

    /**
     * 变更类型
     * @see PplRecordChangeTypeEnum
     */
    private String changeType;

    /**
     * 变更事件
     * @see PplRecordChangeEventEnum
     */
    private String changeEvent;

    /** 操作人 */
    private String operateUser;

    /** 操作备注 */
    private String operateNote;

    private String pplOrder;

    private String pplId;

    private String parentPplId;

    private String yunxiaoOrderId;

    private Long yunxiaoDetailId;

    private String industryDept;


    private String product;

    /** 需求类型 */
    private String demandType;

    /** 需求年*/
    private Integer demandYear;

    /** 需求月*/
    private Integer demandMonth;

    private String projectName;

    private String customerName;

    /** 客户简称 */
    private String customerShortName;

    /**
     * 客户类型
     *  @see CustomerTypeEnum
     *  */
    private String customerType;


    private String customerUin;


    private String customerSource;


    private String warZone;

    /** 地域 */
    private String regionName;

    /** 可用区 */
    private String zoneName;

    /** 实例类型 */
    private String instanceType;

    /** 实例规格 */
    private String instanceModel;

    /** 变化内容概览 */
    private List<String> changeOverviewList;

    /** 变化前item */
    private PplItemChangeAllFieldDTO beforeItem;

    /** 变化后item */
    private PplItemChangeAllFieldDTO afterItem;

}
