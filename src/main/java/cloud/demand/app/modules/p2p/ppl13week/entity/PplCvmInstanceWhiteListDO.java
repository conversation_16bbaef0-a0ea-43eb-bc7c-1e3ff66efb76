package cloud.demand.app.modules.p2p.ppl13week.entity;

// package a.b.c;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_cvm_instance_white_list")
public class PplCvmInstanceWhiteListDO {

    /**
     * id<br/>Column: [id]
     */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * 删除标记<br/>Column: [deleted]
     */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    /**
     * 创建时间<br/>Column: [create_time]
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * 更新时间<br/>Column: [update_time]
     */
    @Column(value = "update_time")
    private Date updateTime;

    @Column(value = "region")
    private String region;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone")
    private String zone;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "instance_model")
    private String instanceModel;

    @Column(value = "cpu_num")
    private Integer cpuNum;
    
    @Column(value = "memory")
    private Integer memory;

    @Column(value = "is_white")
    private Boolean isWhite;

}
