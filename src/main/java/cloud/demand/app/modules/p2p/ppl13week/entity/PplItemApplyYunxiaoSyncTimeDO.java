package cloud.demand.app.modules.p2p.ppl13week.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("ppl_item_apply_yunxiao_sync_time")
public class PplItemApplyYunxiaoSyncTimeDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 同步时间<br/>Column: [sync_time] */
    @Column(value = "sync_time")
    private Date syncTime;

}