package cloud.demand.app.modules.p2p.product_demand.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class ListProductDemandGroupReq {

    private List<String> version;
    private List<String> versionName;
    private List<String> planProduct;
    private List<String> status;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    private Date forecastFrom;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    private Date forecastTo;
}
