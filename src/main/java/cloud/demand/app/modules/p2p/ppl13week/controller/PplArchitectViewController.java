package cloud.demand.app.modules.p2p.ppl13week.controller;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.p2p.ppl13week.dto.architect_view.QueryIndustryDemandOverviewTreeReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.architect_view.QueryIndustryDemandOverviewTreeResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.architect_view.QueryIndustryDemandOverviewTrendResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.architect_view.QueryLastIndustryDemandOverviewUpdateTimeResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.auth.QueryArchitectAuthResp;
import cloud.demand.app.modules.p2p.ppl13week.service.PplArchitectViewService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * ppl架构师视图，用于预测预约融合二期
 */
@Slf4j
@JsonrpcController("/ppl13week")
public class PplArchitectViewController {

    @Resource
    private PplArchitectViewService pplArchitectViewService;
    @Resource
    private PplCommonService pplCommonService;

    @RequestMapping
    public QueryIndustryDemandOverviewTreeResp queryIndustryDemandOverviewTree(
            @JsonrpcParam @Valid QueryIndustryDemandOverviewTreeReq req) {
        if (req == null) {
            throw new WrongWebParameterException("缺少请求参数");
        }

        return pplArchitectViewService.queryIndustryDemandOverviewTree(req);
    }

    @RequestMapping
    public QueryIndustryDemandOverviewTrendResp queryIndustryDemandOverviewTrend(
            @JsonrpcParam @Valid QueryIndustryDemandOverviewTreeReq req) {
        if (req == null) {
            throw new WrongWebParameterException("缺少请求参数");
        }

        return pplArchitectViewService.queryIndustryDemandOverviewTrend(req);
    }

    @RequestMapping
    public QueryLastIndustryDemandOverviewUpdateTimeResp queryLastIndustryDemandOverviewUpdateTime() {
        return pplArchitectViewService.queryLastIndustryDemandOverviewUpdateTime();
    }

    /**
     * 查询架构师权限
     */
    @RequestMapping
    public QueryArchitectAuthResp queryArchitectAuth() {
        return pplCommonService.queryArchitectAuth(LoginUtils.getUserName());
    }

}
