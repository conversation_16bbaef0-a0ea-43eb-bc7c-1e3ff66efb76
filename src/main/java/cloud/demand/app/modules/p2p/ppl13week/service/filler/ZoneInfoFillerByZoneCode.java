package cloud.demand.app.modules.p2p.ppl13week.service.filler;

import cloud.demand.app.modules.p2p.ppl13week.service.filler.handler.ZoneInfoFillerHandler;

/**
 * ppl的区域信息字段的接口定义，用于统一的参数填充<br/>
   根据可用区编码来获取可用区信息并填充
 * 填充实现：{@link ZoneInfoFillerHandler}
 */
public interface ZoneInfoFillerByZoneCode {

    /** 可用区编码 */
    String provideZoneCode();

    void fillRegionName(String regionName);

    /** 填充可用区名称 */
    void fillZoneName(String zoneName);

    /** 填充区域名称 */
    void fillAreaName(String areaName);

    /** 境内外 */
    void fillCustomhouseTitle(String customhouseTitle);

}
