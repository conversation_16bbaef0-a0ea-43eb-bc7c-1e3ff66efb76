package cloud.demand.app.modules.p2p.ppl13week.service;

import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import org.springframework.web.multipart.MultipartFile;

/**
 * 行业内excel导入Service
 */
public interface PplInnerProcessExcelParseService {

    /**
     * 支持的资源类型
     *
     * @param productType
     * @param industryDpet
     * @return
     */
    boolean support(String productType, String industryDpet);

    /**
     * excel处理
     *
     * @param file
     * @param startYearMonth
     * @param endYearMonth
     * @param product
     */
    PplItemImportRsp execute(MultipartFile file,
            YearMonth startYearMonth, YearMonth endYearMonth, String product, String industryDept);

}
