package cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl;


import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictResultForZiyanSplitDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastTaskInputDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastCoreTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryPpl13weekAdjustReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QueryPpl13weekAdjustDetailRsp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QueryPpl13weekAdjustDetailRsp.Item;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QueryPpl13weekAdjustParamsRsp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.vo.PplForecastAdjustDictVO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.vo.PplForecastPredictResultSplitWithAdjustVO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastResourcePoolEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekAdjustService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekCommonDataAccess;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class Ppl13WeekAdjustServiceImpl implements Ppl13weekAdjustService {

    @Resource
    Ppl13weekCommonDataAccess ppl13weekCommonDataAccess;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Override
    public QueryPpl13weekAdjustParamsRsp queryAdjustParams(QueryPpl13weekAdjustReq req) {

        checkReq(req);

        PplForecastTaskInputDO task = demandDBHelper.getRawOne(PplForecastTaskInputDO.class,
                        "select * from ppl_forecast_task_input where task_id in (?)",
                        req.getTaskIds());

        if (Objects.isNull(task)) {
            throw Lang.makeThrow("未找到预测方案");
        }

        String dictSql;
        PplForecastAdjustDictVO dict;
        // 2023-12-28 这里改为宽表读取
        dictSql = ORMUtils.getSql("/sql/ppl13week_forecast/ppl_forecast_adjust_dict.sql");
        dict = ckcldStdCrpDBHelper.getRawOne(PplForecastAdjustDictVO.class, dictSql, req.getTaskIds());

        List<String> ginsFamilyDict = dict == null ? new ArrayList<>() : dict.getGinsFamilyDict();
        Map<String, List<String>> customhouseTitleToRegionName =
                dict == null ? new HashMap<>() : dict.getCustomhouseTitleToRegionNameDict();

        List<String> allBlackInstanceType = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();
        ginsFamilyDict = ListUtils.filter(ginsFamilyDict, (o) -> !allBlackInstanceType.contains(o));

        QueryPpl13weekAdjustParamsRsp ret = new QueryPpl13weekAdjustParamsRsp();
        ret.setType(dict == null ? new ArrayList<>() : dict.getTypeDict());
        ret.setRegionName(dict == null ? new ArrayList<>() : dict.getRegionNameDict());
        ret.setZoneName(dict == null ? new ArrayList<>() : dict.getZoneNameDict());
        ret.setGinsFamily(ginsFamilyDict);
        ret.setCustomhouseTitleToRegionName(customhouseTitleToRegionName);
        return ret;
    }

    @Override
    public QueryPpl13weekAdjustDetailRsp queryAdjustDetail(QueryPpl13weekAdjustReq req) {

        checkReq(req);

        PplForecastTaskInputDO taskInputDO = DBList.demandDBHelper.getOne(PplForecastTaskInputDO.class,
                "where task_id in (?)", req.getTaskIds());

        if (taskInputDO == null) {
            throw Lang.makeThrow("未找到方案id，请确认: %s", req.getTaskIds());
        }

        if (Ppl13weekForecastResourcePoolEnum.ZIYAN.getCode().equals(taskInputDO.getResourcePool())) {

            WhereContent whereContent = new WhereContent();
            whereContent.andIn(PplForecastPredictResultForZiyanSplitDO::getTaskId, req.getTaskIds());
            whereContent.andInIfValueNotEmpty(PplForecastPredictResultForZiyanSplitDO::getOutputVersionId,
                    req.getOutputVersionIds());

            if (Strings.isNotBlank(req.getStartYearMonth())) {
                whereContent.addAnd("DATE_FORMAT(stat_time,'%Y-%m') >=?", req.getStartYearMonth());
            }
            if (Strings.isNotBlank(req.getEndYearMonth())) {
                whereContent.addAnd("DATE_FORMAT(stat_time,'%Y-%m') <=? ", req.getEndYearMonth());
            }
            whereContent.andInIfValueNotEmpty(PplForecastPredictResultForZiyanSplitDO::getRegionName,
                    req.getRegionName());
            whereContent.andInIfValueNotEmpty(PplForecastPredictResultForZiyanSplitDO::getGinsFamily,
                    req.getGinsFamily());
            whereContent.andInIfValueNotEmpty(PplForecastPredictResultForZiyanSplitDO::getForecastSeqType, req.getType());

            List<PplForecastPredictResultForZiyanSplitDO> allZiyanDetail = ORMUtils.db(DBList.demandDBHelper)
                    .getAll(PplForecastPredictResultForZiyanSplitDO.class, whereContent);

            List<Item> transform = ListUtils.transform(allZiyanDetail, (o) -> trans(o, taskInputDO));

            QueryPpl13weekAdjustDetailRsp ret = new QueryPpl13weekAdjustDetailRsp();
            ret.setPredictAlgorithmName(
                    Strings.join("@", taskInputDO.getPredictAlgorithm(), taskInputDO.getPredictAlgorithmArgs()));
            ret.setCustomerType("中长尾");
            ret.setItems(transform);
            return ret;
        }

        List<PplForecastPredictResultSplitWithAdjustVO> detail = getPplForecastPredictResultSplitVO(req);
        PplForecastPredictTaskDO task = getOneTask(detail);

        List<String> allBlackInstanceType = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();
        detail = ListUtils.filter(detail, (o) -> !allBlackInstanceType.contains(o.getGinsFamily()));

        List<Item> transform = ListUtils.transform(detail, this::trans);

        boolean containError = ListUtils.contains(transform, (o) -> Strings.isNotBlank(o.getErrorMsg()));
        QueryPpl13weekAdjustDetailRsp ret = new QueryPpl13weekAdjustDetailRsp();
        if (task != null) {
            ret.setPredictAlgorithmName(Strings.join("@", task.getPredictAlgorithm(), task.getPredictAlgorithmArgs()));
        } else {
            ret.setPredictAlgorithmName("未知算法");
        }
        ret.setCustomerType("中长尾");
        ret.setItems(transform);
        ret.setHasError(containError);
        return ret;
    }

    private PplForecastPredictTaskDO getOneTask(List<PplForecastPredictResultSplitWithAdjustVO> detail) {
        if (ListUtils.isEmpty(detail)) {
            return null;
        }
        for (PplForecastPredictResultSplitWithAdjustVO d : detail) {
            if (d.getTaskDO() != null) {
                return d.getTaskDO();
            }
        }
        return null;
    }

    private List<PplForecastPredictResultSplitWithAdjustVO> getPplForecastPredictResultSplitVO(
            QueryPpl13weekAdjustReq req) {

        WhereContent whereContent = new WhereContent();
        if (req.getTaskId() != null) {
            whereContent.andEqual(PplForecastPredictResultSplitWithAdjustVO::getTaskId, req.getTaskId());
        } else if (Lang.isNotEmpty(req.getTaskIds())) {
            whereContent.andIn(PplForecastPredictResultSplitWithAdjustVO::getTaskId, req.getTaskIds());
        }

        if (Lang.isNotEmpty(req.getOutputVersionIds())) {
            whereContent.andIn(PplForecastPredictResultSplitWithAdjustVO::getOutputVersionId,
                    req.getOutputVersionIds());
        }

        if (Strings.isNotBlank(req.getStartYearMonth())) {
            whereContent.addAnd("DATE_FORMAT(stat_time,'%Y-%m') >=?", req.getStartYearMonth());
        }
        if (Strings.isNotBlank(req.getEndYearMonth())) {
            whereContent.addAnd("DATE_FORMAT(stat_time,'%Y-%m') <=? ", req.getEndYearMonth());
        }
        whereContent.andInIfValueNotEmpty(PplForecastPredictResultSplitWithAdjustVO::getRegionName,
                req.getRegionName());
        whereContent.andInIfValueNotEmpty(PplForecastPredictResultSplitWithAdjustVO::getGinsFamily,
                req.getGinsFamily());
        whereContent.andInIfValueNotEmpty(PplForecastPredictResultSplitWithAdjustVO::getForecastSeqType, req.getType());

        if (ListUtils.isNotEmpty(req.getSourceType())) {
            whereContent.addAnd(
                    "task_id in (select id from ppl_forecast_predict_task where source_type in (?))",
                    req.getSourceType());
        }

        return ORMUtils.db(DBList.demandDBHelper).getAll(PplForecastPredictResultSplitWithAdjustVO.class, whereContent);
    }

    private void checkReq(QueryPpl13weekAdjustReq req) {
        if ((req.getTaskId() == null || req.getTaskId() < 0) && (Lang.isEmpty(req.getTaskIds()))) {
            throw BizException.makeThrow("taskId 或 taskIds为空");
        }
        List<Long> taskIds = req.getTaskIds();
        if (Lang.isEmpty(taskIds)) {
            taskIds = Lang.list();
        }
        if (req.getTaskId() != null) {
            taskIds.add(req.getTaskId());
        }
        req.setTaskIds(taskIds);

        if (Strings.isNotBlank(req.getStartYearMonth())) {
            if (req.getStartYearMonth().length() != 7 || req.getStartYearMonth().charAt(4) != '-') {
                throw BizException.makeThrow("startYearMonth 格式出错: %s", req.getStartYearMonth());
            }
        }

        if (Strings.isNotBlank(req.getEndYearMonth())) {
            if (req.getEndYearMonth().length() != 7 || req.getEndYearMonth().charAt(4) != '-') {
                throw BizException.makeThrow("startYearMonth 格式出错: %s", req.getEndYearMonth());
            }
        }
    }

    private Item trans(PplForecastPredictResultForZiyanSplitDO source, PplForecastTaskInputDO taskInputDO) {
        Item item = new Item();
        item.setCreateTime(source.getCreateTime());
        item.setResultId(source.getResultId());
        item.setStatTime(source.getStatTime());
        item.setPredictIndex(source.getPredictIndex());
        item.setYear(source.getYear());
        item.setMonth(source.getMonth());
        item.setGinsFamily(source.getGinsFamily());
        item.setInstanceModel("");
        item.setRegionName(source.getRegionName());
        item.setZoneName("");
        item.setCoreType("");
        item.setCoreTypeName("");

        item.setSourceTypeName(Ppl13weekForecastSourceTypeEnum.getNameByCode(taskInputDO.getSourceType()));
        item.setSourceType(item.getSourceType());

        item.setType(source.getForecastSeqType());
        item.setTypeName(PplForecastTypeEnum.getNameByType(source.getForecastSeqType()));

        item.setOriginNum(source.getCoreNum());

        item.setDesc(source.getNote());
        return item;
    }

    private Item trans(PplForecastPredictResultSplitWithAdjustVO source) {

        Item item = new Item();
        item.setCreateTime(source.getCreateTime());
        item.setResultId(source.getId());
        item.setStatTime(source.getStatTime());
        item.setPredictIndex(source.getPredictIndex());
        item.setYear(source.getYear());
        item.setMonth(source.getMonth());

        item.setGinsFamily(source.getGinsFamily());
        item.setInstanceModel(source.getInstanceModel());

        item.setSourceType(source.getTaskDO().getSourceType());
        item.setSourceTypeName(Ppl13weekForecastSourceTypeEnum.getNameByCode(item.getSourceType()));

        item.setZoneName(source.getZoneName());
        item.setCoreType(source.getCoreType());
        item.setCoreTypeName(PplForecastCoreTypeEnum.getNameByType(source.getCoreType()));

        item.setRegionName(source.getRegionName());
        item.setType(source.getForecastSeqType());
        item.setTypeName(PplForecastTypeEnum.getNameByType(source.getForecastSeqType()));

        item.setOriginNum(source.getCoreNum());
        item.setOriginCvmNum(source.getCvmNum());

        item.setErrorMsg(source.getErrorMsg());
        return item;
    }


}