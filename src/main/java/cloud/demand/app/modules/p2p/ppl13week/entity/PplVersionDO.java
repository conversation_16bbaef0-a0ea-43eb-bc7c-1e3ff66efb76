package cloud.demand.app.modules.p2p.ppl13week.entity;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.common.utils.DateUtils;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import yunti.boot.exception.BizException;

import java.util.Date;

@Data
@ToString
@Table("ppl_version")
public class PplVersionDO extends BaseDO {

    /** 版本编码<br/>Column: [version_code] */
    @Column(value = "version_code")
    private String versionCode;

    /** 版本名称<br/>Column: [version_name] */
    @Column(value = "version_name")
    private String versionName;

    /** 需求开始年<br/>Column: [demand_begin_year] */
    @Column(value = "demand_begin_year")
    private Integer demandBeginYear;

    /** 需求开始月<br/>Column: [demand_begin_month] */
    @Column(value = "demand_begin_month")
    private Integer demandBeginMonth;

    /** 需求结束年<br/>Column: [demand_end_year] */
    @Column(value = "demand_end_year")
    private Integer demandEndYear;

    /** 需求结束月<br/>Column: [demand_end_month] */
    @Column(value = "demand_end_month")
    private Integer demandEndMonth;

    @Column(value = "deadline")
    private Date deadline;

    /** 版本类型，周版本月版本<br/>Column: [version_type]
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionTypeEnum
     * */
    @Column(value = "version_type")
    private String versionType;

    /** 状态<br/>Column: [status]
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionStatusEnum
     * */
    @Column(value = "status")
    private String status;

    /** 描述<br/>Column: [note] */
    @Column(value = "note")
    private String note;

    /** 创建人<br/>Column: [creator] */
    @Column(value = "creator")
    private String creator;

    /** 启动审批的时间<br/>Column: [start_audit_time] */
    @Column(value = "start_audit_time")
    private Date startAuditTime;

    /** 结束审批的时间<br/>Column: [end_audit_time] */
    @Column(value = "end_audit_time")
    private Date endAuditTime;

    /**
     * 海外需求开始年<br/>Column: [overseas_demand_begin_year]
     */
    @Column(value = "overseas_demand_begin_year")
    private Integer overseasDemandBeginYear;

    /**
     * 海外需求开始月<br/>Column: [overseas_demand_begin_month]
     */
    @Column(value = "overseas_demand_begin_month")
    private Integer overseasDemandBeginMonth;


    public boolean includeDemandYearMonth(Integer year, Integer month) {
        // 检查参数是否有效
        if (year == null || month == null) {
            throw new BizException("年月不能为空");
        }
        if (month <= 0 || month > 12) {
            throw new BizException("月份非法");
        }

        // 将年和月转换为“年月”的形式，以便进行比较
        int demandBegin = this.demandBeginYear * 100 + this.demandBeginMonth;
        int demandEnd = this.demandEndYear * 100 + this.demandEndMonth;
        int input = year * 100 + month;

        // 检查输入的年月是否在需求开始和结束的范围内
        return demandBegin <= input && input <= demandEnd;
    }

    public String getInCountryDemandYearMonth() {
        return DateUtils.getYearMonthStr(demandBeginYear,demandBeginMonth);
    }

    public String getOverseasDemandYearMonth() {
        return DateUtils.getYearMonthStr(overseasDemandBeginYear,overseasDemandBeginMonth);
    }

    public LocalDate getInCountryDemandYearMonthFirstDay() {
        return DateUtils.getFirstDayOfMonthLocalDate(getInCountryDemandYearMonth());
    }
    public LocalDate getOverseasDemandYearMonthFirstDay() {
        return DateUtils.getFirstDayOfMonthLocalDate(getOverseasDemandYearMonth());
    }

    /**
     * 判断某个日期是否满足当前版本海外需求年月
     * 调用此方法前，需判断非退回需求、为海外需求 ！！！重要 ！！！
     * @param localDate
     * @return
     */
    public Boolean isSatisfyOverseasYearMonth(LocalDate localDate) {
        LocalDate firstDayOfMonthLocalDate = DateUtils.getFirstDayOfMonthLocalDate(getOverseasDemandYearMonth());
        if (localDate.isBefore(firstDayOfMonthLocalDate)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}