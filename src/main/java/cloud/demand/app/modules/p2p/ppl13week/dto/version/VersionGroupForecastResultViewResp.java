package cloud.demand.app.modules.p2p.ppl13week.dto.version;

import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastInputDetailDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastInputExcludedSpikeDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictResultSplitMiddleDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO.DailyZoneAppidGinstypePaymodeApproleDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang.StringUtils;
import org.nutz.lang.Strings;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@ToString
public class VersionGroupForecastResultViewResp {

    /**
     * 新增序列的数据
     */
    private List<SummaryDTO> newDataList = new ArrayList<>();

    /**
     * 退回序列的数据
     */
    private List<SummaryDTO> retDataList = new ArrayList<>();


    /**
     * 增加4个字典表
     */
    List<String> regionName = new ArrayList<>();
    List<String> instanceType= new ArrayList<>();
    List<String> customerUin= new ArrayList<>();
    List<String> customerShortName= new ArrayList<>();


    public void distinctDict() {
        regionName = regionName.stream().distinct().collect(Collectors.toList());
        instanceType = instanceType.stream().distinct().collect(Collectors.toList());
        customerUin = customerUin.stream().distinct().collect(Collectors.toList());
        customerShortName = customerShortName.stream().distinct().collect(Collectors.toList());
    }



    public <T> void setDict(List<T> source,
            Function<T, String> getRegionName,
            Function<T, String> getInstanceType) {
        setDict(source, getRegionName, getInstanceType, null, null);
    }

    public <T> void setDict(List<T> source,
            Function<T, String> getRegionName,
            Function<T, String> getInstanceType,
            Function<T, String> getCustomerUin,
            Function<T, String> getCustomerShortName) {
        setList(source, getRegionName, regionName);
        setList(source, getInstanceType, instanceType);
        setList(source, getCustomerUin, customerUin);
        setList(source, getCustomerShortName, customerShortName);
    }

    private <T> void setList(List<T> source, Function<T, String> function, List<String> target) {
        if (function != null) {
            target.addAll(source.stream()
                    .map(function)
                    .filter(Objects::nonNull)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList()));
        }
    }


    public void fillSpikeMiddleExecute(List<PplForecastInputExcludedSpikeDO> list,
            Map<String, String> instanceTypeToMergedType) {
        if (list == null || list.isEmpty()) {
            return;
        }
        Map<String, List<PplForecastInputExcludedSpikeDO>> map =
                ListUtils.groupBy(list, o -> StringTools.join("@", o.getYear(), o.getMonth()));
        for (Map.Entry<String, List<PplForecastInputExcludedSpikeDO>> entry : map.entrySet()) {
            List<PplForecastInputExcludedSpikeDO> value = entry.getValue();
            String yearMonth =
                    value.get(0).getYear() + "-" + (value.get(0).getMonth() < 10 ? "0" : "") + value.get(0).getMonth();
            Integer newCore = NumberUtils.sum(value, PplForecastInputExcludedSpikeDO::getNewCore)
                    .setScale(0, RoundingMode.HALF_UP).intValue();
            Integer retCore = NumberUtils.sum(value, PplForecastInputExcludedSpikeDO::getRetCore)
                    .setScale(0, RoundingMode.HALF_UP).intValue();

            Map<String, List<PplForecastInputExcludedSpikeDO>> customerGroup = ListUtils.groupBy(value,
                    (o)-> Strings.join("@", o.getInstanceType(), o.getRegionName(),
                            o.getCustomerUin(),o.getCustomerShortName()));

            putNew(yearMonth, o -> {
                o.setSpikeExecuteNum(newCore);
                o.setSpikeExecuteDetail(ListUtils.transform(new ArrayList<>(customerGroup.keySet()), (i) -> {
                    List<PplForecastInputExcludedSpikeDO> tmp = customerGroup.get(i);
                    BigDecimal newSum = NumberUtils.sum(tmp, PplForecastInputExcludedSpikeDO::getNewCore);
                    return DetailDTO.from(tmp.get(0), (k) -> {
                        k.setNum(newSum);
                        k.setTranserInstanceType(instanceTypeToMergedType.getOrDefault(k.getInstanceType(),""));
                    });
                }));
                // 值为0的不要
                o.setSpikeExecuteDetail(ListUtils.filter(o.getSpikeExecuteDetail(),i -> i.getNum().compareTo(BigDecimal.ZERO) > 0));

                List<DetailDTO> sortedList = o.getSpikeExecuteDetail().stream()
                        .sorted(Comparator.comparing((DetailDTO i) -> "31个大客户名单".equals(i.getCustomer()))
                                .thenComparing(DetailDTO::getNum, Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList());
                o.setSpikeExecuteDetail(sortedList);

            });
            putRet(yearMonth, o -> {
                o.setSpikeExecuteNum(retCore);
                o.setSpikeExecuteDetail(ListUtils.transform(new ArrayList<>(customerGroup.keySet()),
                        (i) -> {
                    List<PplForecastInputExcludedSpikeDO> tmp = customerGroup.get(i);
                    BigDecimal retSum = NumberUtils.sum(tmp, PplForecastInputExcludedSpikeDO::getRetCore);
                    return DetailDTO.from(tmp.get(0), (k) -> {
                        k.setNum(retSum);
                        k.setTranserInstanceType(instanceTypeToMergedType.getOrDefault(k.getInstanceType(),""));
                    });
                }));
                // 值为0的不要
                o.setSpikeExecuteDetail(ListUtils.filter(o.getSpikeExecuteDetail(),i -> i.getNum().compareTo(BigDecimal.ZERO) > 0));
                List<DetailDTO> sortedList = o.getSpikeExecuteDetail().stream()
                        .sorted(Comparator.comparing((DetailDTO i) -> "31个大客户名单".equals(i.getCustomer()))
                                .thenComparing(DetailDTO::getNum, Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList());
                o.setSpikeExecuteDetail(sortedList);
            });
        }
    }

    @Data
    public static class SummaryDTO {

        /**
         * 年月，yyyy-MM
         */
        private String yearMonth;

        /**
         * 非毛刺的预测量，注意，模型预测只预测非毛刺的，因此这里只有非毛刺的预测量
         */
        private Integer notSpikeForecastNum;

        /**
         * 非毛刺预测值明细
         */
        private List<DetailDTO> notSpikeForecastDetail;

        /**
         * 非毛刺的执行量
         */
        private Integer notSpikeExecuteNum;

        /**
         * 毛刺执行量明细
         */
        private List<DetailDTO> notSpikeExecutedDetail;

        /**
         * 非毛刺的minMax 准确率
         */
        private BigDecimal notSpikeMinMaxRate;

        /**
         * 头部+毛刺的预测量
         */
        private Integer spikeForecastNum;

        /**
         * 头部+毛刺预测值明细
         */
        private List<DetailDTO> spikeForecastDetail;

        /**
         * 毛刺的执行量
         */
        private Integer spikeExecuteNum;

        /**
         * 毛刺预测值明细
         */
        private List<DetailDTO> spikeExecuteDetail;

        /**
         * 毛刺的minMax 准确率
         */
        private BigDecimal spikeMinMaxRate;

        /**
         * 提示信息没有数据
         */
        private String errorMsg;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DimKey {

        private String instanceType;

        private String regionName;
    }

    @Data
    public static class DetailDTO {

        private String regionName;
        private String instanceType;
        /**
         * 收揽机型机型大类<br/>Column: [instance_type]
         */
        private String transerInstanceType;
        /**
         * 客户，可以为空
         */
        private String customer;
        private String customerUin;

        private BigDecimal num;


        public static DetailDTO from(PplForecastInputDetailDO source,Consumer<DetailDTO> setter) {
            DetailDTO detailDTO = new DetailDTO(); detailDTO.setRegionName(source.getRegionName());
            detailDTO.setInstanceType(source.getGinsFamily()); detailDTO.setCustomer("");
            setter.accept(detailDTO);
            return detailDTO;
        }

        public static DetailDTO from(PplForecastPredictResultSplitMiddleDO source) {
            DetailDTO detailDTO = new DetailDTO();
            detailDTO.setRegionName(source.getRegionName());
            detailDTO.setInstanceType(source.getGinsFamily());
            detailDTO.setCustomer("");
            detailDTO.setNum(source.getCoreNum());
            return detailDTO;
        }
        public static DetailDTO from(PplForecastInputExcludedSpikeDO source,Consumer<DetailDTO> setter) {
            DetailDTO detailDTO = new DetailDTO();
            detailDTO.setRegionName(source.getRegionName());
            detailDTO.setInstanceType(source.getInstanceType());
            detailDTO.setCustomer(source.getCustomerShortName());
            detailDTO.setCustomerUin(source.getCustomerUin());
            setter.accept(detailDTO);
            return detailDTO;
        }

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DimValue {

        private BigDecimal notSpikeExecuteNum;

        private BigDecimal notSpikeForecastNum;
    }


    private void putNew(String yearMonth, Consumer<SummaryDTO> consumer) {
        put(newDataList, yearMonth, consumer);
    }

    private void putRet(String yearMonth, Consumer<SummaryDTO> consumer) {
        put(retDataList, yearMonth, consumer);
    }

    public void filNotSpikeMiddleExecute(List<DailyZoneAppidGinstypePaymodeApproleDTO> list,
                    List<PplForecastInputDetailDO> newTransData,
                    List<PplForecastInputDetailDO> retTransData
            ) {
        if (list == null || list.isEmpty()) {
            return;
        }
        Map<String, List<DailyZoneAppidGinstypePaymodeApproleDTO>> map =
                ListUtils.groupBy(list, o -> StringTools.join("@", o.getYear(), o.getMonth()));

        Map<String, List<PplForecastInputDetailDO>> newTransInstanceTypeMap =
                ListUtils.groupBy(newTransData, o -> StringTools.join("@", o.getYear(), o.getMonth()));
        Map<String, List<PplForecastInputDetailDO>> retTransInstanceTypeMap =
                ListUtils.groupBy(retTransData, o -> StringTools.join("@", o.getYear(), o.getMonth()));

        for (Map.Entry<String, List<DailyZoneAppidGinstypePaymodeApproleDTO>> entry : map.entrySet()) {
            List<DailyZoneAppidGinstypePaymodeApproleDTO> value = entry.getValue();
            String yearMonth =
                    value.get(0).getYear() + "-" + (value.get(0).getMonth() < 10 ? "0" : "") + value.get(0).getMonth();
            Integer newCore = NumberUtils.sum(value, DailyZoneAppidGinstypePaymodeApproleDTO::getNewDiff)
                    .setScale(0, RoundingMode.HALF_UP).intValue();
            Integer retCore = NumberUtils.sum(value, DailyZoneAppidGinstypePaymodeApproleDTO::getRetDiff)
                    .setScale(0, RoundingMode.HALF_UP).intValue();

            List<PplForecastInputDetailDO> newTransDetail = newTransInstanceTypeMap.get(entry.getKey());
            List<PplForecastInputDetailDO> retTransDetail = retTransInstanceTypeMap.get(entry.getKey());

            List<DetailDTO> newDetail = ListUtils.transform(newTransDetail,
                    (o)-> DetailDTO.from(o,(i)->i.setNum(o.getDiffCoreNum())));
            List<DetailDTO> retDetail = ListUtils.transform(retTransDetail,
                    (o)-> DetailDTO.from(o,(i)->i.setNum(o.getDiffCoreNum())));

            putNew(yearMonth, o -> {
                o.setNotSpikeExecuteNum(newCore);
                o.setNotSpikeExecutedDetail(newDetail);
            });
            putRet(yearMonth, o -> {
                o.setNotSpikeExecuteNum(retCore);
                o.setNotSpikeExecutedDetail(retDetail);
            });
        }
    }

    public void fillSplitMiddleResult(List<PplForecastPredictResultSplitMiddleDO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        Map<String, List<PplForecastPredictResultSplitMiddleDO>> map =
                ListUtils.groupBy(list, o -> StringTools.join("@", o.getYear(), o.getMonth(), o.getType()));
        for (Map.Entry<String, List<PplForecastPredictResultSplitMiddleDO>> entry : map.entrySet()) {
            List<PplForecastPredictResultSplitMiddleDO> value = entry.getValue();
            String yearMonth =
                    value.get(0).getYear() + "-" + (value.get(0).getMonth() < 10 ? "0" : "") + value.get(0).getMonth();
            Integer sumCore = NumberUtils.sum(value, PplForecastPredictResultSplitMiddleDO::getCoreNum)
                    .setScale(0, RoundingMode.HALF_UP).intValue();

            // 这里设置明细数据
            List<DetailDTO> transform = ListUtils.transform(value, DetailDTO::from);
            Consumer<SummaryDTO> consumer = o -> {
                o.setSpikeForecastNum(sumCore);
                o.setSpikeForecastDetail(transform);
            };
            if (value.get(0).getType().equals("NEW")) {
                putNew(yearMonth, consumer);
            } else {
                putRet(yearMonth, consumer);
            }
        }
    }

    private synchronized void put(List<SummaryDTO> list, String yearMonth, Consumer<SummaryDTO> consumer) {
        for (SummaryDTO summaryDTO : list) {
            if (summaryDTO.getYearMonth().equals(yearMonth)) {
                consumer.accept(summaryDTO);
                return;
            }
        }
        SummaryDTO summaryDTO = new SummaryDTO();
        summaryDTO.setYearMonth(yearMonth);
        consumer.accept(summaryDTO);
        list.add(summaryDTO);
    }

}
