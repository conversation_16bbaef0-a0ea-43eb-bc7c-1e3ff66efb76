package cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply;

import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryStockSupplyGroupViewRsp {


    List<String> regionName;
    List<String> warZone;
    List<String> instanceType;
    List<String> customerUin;
    List<String> customerShortName;
    List<String> instanceTypeNamePrefix;
    List<String> demandScene;
    String resourceType;
    /**
     * CVM, CBS, CORE
     */


    String statusInfo;

    /**
     * 下面这个是 物理机的
     */
    List<Item> items;

    BigDecimal newDemandTotal;
    BigDecimal elasticDemandTotal;
    BigDecimal returnDemandTotal;
    BigDecimal totalDemand;


    public QueryStockSupplyGroupViewRsp(List<Item> list) {
        this.items = list;
    }


    @Data
    public static class Item {

        /**
         * demandType
         */
        String demandType;

        /**
         * demandType showName
         */
        String demandTypeShowName;

        /**
         * 地域类型
         */
        String demandCountryName;
        /**
         * 地域
         */
        String demandRegionName;
        /**
         * 实例类别
         */
        String demandInstanceClassName;

        /**
         * 实例类型
         */
        String demandInstanceType;
        boolean demandIsRecommendedInstanceType;

        Integer demandTotal;
        List<YearMonthData> demandDetails;

        Integer applyTotal;
        List<YearMonthData> applyDetails;

        Integer stockTotal;
        List<YearMonthData> stockDetails;


        // 满足方式
        List<StockSupplyData> stockSupplyData;


        @lombok.Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class YearMonthData {

            String name;  // 202209
            BigDecimal value; //
        }

        @lombok.Data
        public static class StockSupplyData {

            String code;        //满足code
            String name;        //满足方式 名字
            String instanceTypeColumnName;

            List<Detail> details;

        }

        @Data
        public static class Detail {

            String instanceType;
            boolean isRecommendedInstanceType;
            BigDecimal total;
            List<YearMonthData> yearMonthDetails;
        }


    }


}
