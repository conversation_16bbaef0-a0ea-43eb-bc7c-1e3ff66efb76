package cloud.demand.app.modules.p2p.industry_demand.config;

import cloud.demand.app.common.utils.AlarmRobotUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;
import yunti.boot.security.TofUser;

public class DynamicProperties {

    private static final ObjectMapper object_mapper = new ObjectMapper();

    private static final Supplier<Set<String>> demand_type_enum_set =
            DynamicProperty.create("industry-annual-demand.demand-type-enum-set",
                    "[\"新增\", \"退回\", \"弹性\"]",
                    e -> object_mapper.readValue(e, new TypeReference<Set<String>>() {
                    }));

    private static final Supplier<Set<String>> prepaid_type_enum_set =
            DynamicProperty.create("industry-annual-demand.prepaid-type-enum-set",
                    "[\"按量计费\", \"包年包月\", \"竞价付费\", \"CDH\"]",
                    e -> object_mapper.readValue(e, new TypeReference<Set<String>>() {
                    }));

    private static final Supplier<Set<String>> cbs_prepaid_type_enum_set =
            DynamicProperty.create("industry-annual-demand.cbs-prepaid-type-enum-set",
                    "[\"按量计费\", \"包年包月\"]",
                    e -> object_mapper.readValue(e, new TypeReference<Set<String>>() {
                    }));

    private static final Supplier<Set<String>> metal_prepaid_type_enum_set =
            DynamicProperty.create("industry-annual-demand.metal-prepaid-type-enum-set",
                    "[\"按量计费\", \"包年包月\", \"竞价付费\", \"CDH\", \"包销4年\"]",
                    e -> object_mapper.readValue(e, new TypeReference<Set<String>>() {
                    }));


    private static final Supplier<Set<String>> cbs_disk_type_enum_set =
            DynamicProperty.create("industry-annual-demand.cbs-disk-type-enum-set",
                    "[\"增强型SSD云硬盘\", \"高性能云硬盘\", \"SSD云硬盘\", \"通用型SSD云硬盘\", \"极速型SSD云硬盘\"]",
                    e -> object_mapper.readValue(e, new TypeReference<Set<String>>() {
                    }));

    private static final Supplier<Set<String>> gpu_type_enum_set =
            DynamicProperty.create("industry-annual-demand.gpu-type-enum-set",
                    "[\"T4\", \"A10\", \"V100\", \"A100（H800）\", \"2080ti\", \"3070\", \"3080\", \"3090\", \"天玑\"]",
                    e -> object_mapper.readValue(e, new TypeReference<Set<String>>() {
                    }));

    private static final Supplier<Set<String>> gpu_product_pattern_enum_set =
            DynamicProperty.create("industry-annual-demand.gpu-product-pattern-enum-set",
                    "[\"虚拟机\", \"裸金属\"]",
                    e -> object_mapper.readValue(e, new TypeReference<Set<String>>() {
                    }));

    private static final Supplier<Set<String>> big_data_platform_arch_enum_set =
            DynamicProperty.create("industry-annual-demand.big-data-platform-arch-enum-set",
                    "[\"INTEL\", \"AMD\"]",
                    e -> object_mapper.readValue(e, new TypeReference<Set<String>>() {
                    }));

    private static final Supplier<Set<String>> big_data_sub_product_enum_set =
            DynamicProperty.create("industry-annual-demand.big-sub-product-enum-set",
                    "[\"EMR\", \"ES\", \"CDWCH\"]",
                    e -> object_mapper.readValue(e, new TypeReference<Set<String>>() {
                    }));

    private static final Supplier<String> yuntiUrl =
            DynamicProperty.create("yuntiUrl",
                    "http://exp.yunti.oa.com");


    private static final Supplier<String> sendPplMeddleMail =
            DynamicProperty.create("sendPplMeddleMail",
                    "false");

    public static String getYuntiUrl(){
        return yuntiUrl.get();
    }

    private static final Supplier<String> gpuPrefix =
            DynamicProperty.create("gpuPrefix",
                    "G;PN;BMG;H");

    private static final Supplier<String> crpUrl =
            DynamicProperty.create("crpUrl",
                    "");

    private static final Supplier<Set<String>> land_enum_set =
            DynamicProperty.create("industry-annual-demand.land-enum-set",
                    "[\"国内\", \"海外\"]",
                    e -> object_mapper.readValue(e, new TypeReference<Set<String>>() {
                    }));

    private static final Supplier<Set<String>> metal_instance_class_enum_set =
            DynamicProperty.create("industry-annual-demand.metal-instance-class-enum-set",
                    "[\"高IO型\", \"标准型\", \"内存型\", \"大数据型\", \"美团定制机型1\"]",
                    e -> object_mapper.readValue(e, new TypeReference<Set<String>>() {
                    }));

    /**要支持动态录入分组的部门*/
    private static final Supplier<Set<String>> input_group_industry =
            DynamicProperty.create("industry-annual-demand.input-group-industry",
                    "[\"战略客户部\"]",
                    e -> object_mapper.readValue(e, new TypeReference<Set<String>>() {
                    }));

    public static Set<String> landEnumSet() {
        return land_enum_set.get();
    }

    public static Set<String> demandTypeEnumSet() {
        return demand_type_enum_set.get();
    }

    public static Set<String> prepaidTypeEnumSet() {
        return prepaid_type_enum_set.get();
    }

    public static Set<String> cbsPrepaidTypeEnumSet() {
        return cbs_prepaid_type_enum_set.get();
    }

    public static Set<String> metalPrepaidTypeEnumSet() {
        return metal_prepaid_type_enum_set.get();
    }

    public static Set<String> cbsDiskTypeEnumSet() {
        return cbs_disk_type_enum_set.get();
    }

    public static Set<String> gpuTypeEnumSet() {
        return gpu_type_enum_set.get();
    }

    public static Set<String> bigDataPlatformArchEnumSet() {
        return big_data_platform_arch_enum_set.get();
    }

    public static Set<String> bigDataSubProductEnumSet() {
        return big_data_sub_product_enum_set.get();
    }

    public static String getGpuPrefix() {
        return gpuPrefix.get();
    }

    public static Set<String> metalInstanceClassEnumSet() {
        return metal_instance_class_enum_set.get();
    }

    public static Set<String> gpuProductPatternEnumSet() {
        return gpu_product_pattern_enum_set.get();
    }


    public static Set<String> inputGroupIndustry() {
        return input_group_industry.get();
    }

    public static String getCrpUrl() {
        return crpUrl.get();
    }

    public static String sendPplMeddleMail() {
        return sendPplMeddleMail.get();
    }


    private static final Supplier<String> pplIndustryDept =
            DynamicProperty.create("pplIndustryDept",
                    "智慧行业一部;战略客户部");
    public static List<String> getIndustryDept() {
        String industryDept = pplIndustryDept.get();
        if (Strings.isBlank(industryDept)) {
            return Lang.list();
        }
        return Lang.list(industryDept.trim().split(";"));
    }

    /**
     *   api_key 可查询的行业部门。<br/>
     *   格式： {"deptno_1183":["智慧行业一部","智慧交通事业部"], "deptno_123":["ALL"]}。 <br/>
     *   表示 api_key ： deptno_1183 可以查询 "智慧行业一部","智慧交通事业部" 的数据 <br/>
     *   api_key ： deptno_123 可以查询 所有行业的数据 ， ALL 表示对所有行业部门有权限<br/>
     */
    private static final Supplier<String> pplApiAuthIndustry = DynamicProperty.create("ppl.api.auth.industry", "");

    /**
     * 根据用户的api_key 查询对应的权限行业部门
     * @return left： 是否有所有行业部门的权限；right： 只拥有指定行业部门的权限
     * @see #pplApiAuthIndustry
     */
    public static Tuple2<Boolean, List<String>> authIndustryDept(TofUser user) {
        String authIndustry = null;
        String apiKey = null;
        try {
            authIndustry = pplApiAuthIndustry.get();
            apiKey = user.getUsername();
            if (Strings.isBlank(authIndustry)) {
                return Tuple.of(false, new ArrayList<>());
            }
            Map<String, Object> authMap = JSON.parseToMap(authIndustry);
            // username = api_key
            Object deptList = authMap.get(apiKey);
            if (deptList == null) {
                return Tuple.of(false, new ArrayList<>());
            }
            List<String> res = (List<String>) deptList;
            if (ListUtils.isNotEmpty(res) &&res.contains("ALL")) {
                // 有所有部门的权限
                return Tuple.of(true, res);
            }
            return Tuple.of(false, res);
        } catch (Exception e) {
            String msg = StrUtil.format("获取对外接口的行业部门权限出错，请检查七彩石配置【ppl.api.auth.industry】，"
                            + "当前值【{}】，当前api_key【{}】，异常信息【{}:{}】",
                    authIndustry, apiKey, e.getClass().getName(), e.getMessage());
            AlarmRobotUtil.doAlarm("DynamicProperties.authIndustryDept", msg, null, false);
            throw BizException.makeThrow("获取行业部门权限出错，请联系接口负责人");
        }
    }

    /**
     *   api_key 可查询的订单分类。<br/>
     *   格式： {"deptno_1183":["CVM","裸金属"], "deptno_123":["ALL"]}。 <br/>
     *   表示 api_key ： deptno_1183 可以查询 "CVM","裸金属" 的数据 <br/>
     *   api_key ： deptno_123 可以查询 所有订单分类的数据 ， ALL 表示对所有订单分类有权限<br/>
     */
    private static final Supplier<String> pplApiAuthOrderCategory = DynamicProperty.create("ppl.api.auth.order.category", "");

    /**
     * 根据用户的api_key 查询对应的权限订单分类
     * @return left： 是否有所有订单分类的权限；right： 只拥有指定订单分类的权限
     * @see #pplApiAuthOrderCategory
     */
    public static Tuple2<Boolean, List<String>> authOrderCategory(TofUser user) {
        String authConfig = null;
        String apiKey = null;
        try {
            authConfig = pplApiAuthOrderCategory.get();
            apiKey = user.getUsername();
            if (Strings.isBlank(authConfig)) {
                return Tuple.of(false, new ArrayList<>());
            }
            Map<String, Object> authMap = JSON.parseToMap(authConfig);
            // username = api_key
            Object deptList = authMap.get(apiKey);
            if (deptList == null) {
                return Tuple.of(false, new ArrayList<>());
            }
            List<String> res = (List<String>) deptList;
            if (ListUtils.isNotEmpty(res) &&res.contains("ALL")) {
                // 有所有订单分类的权限
                return Tuple.of(true, res);
            }
            return Tuple.of(false, res);
        } catch (Exception e) {
            String msg = StrUtil.format("获取对外接口的行业部门权限出错，请检查七彩石配置【ppl.api.auth.order.category】，"
                            + "当前值【{}】，当前api_key【{}】，异常信息【{}:{}】",
                    authConfig, apiKey, e.getClass().getName(), e.getMessage());
            AlarmRobotUtil.doAlarm("DynamicProperties.authOrderCategory", msg, null, false);
            throw BizException.makeThrow("获取订单分类权限出错，请联系接口负责人");
        }
    }
}
