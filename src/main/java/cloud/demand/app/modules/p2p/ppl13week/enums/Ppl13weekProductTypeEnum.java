package cloud.demand.app.modules.p2p.ppl13week.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Getter;

/**
 * 特别说明：目前数据库存储的是name，而不是code，所以这里的code实际上是废弃了的
 *
 * 数据库里关于产品的name是放在ppl_config_product_enum里的，这里只是代码引用
 */
@Getter
public enum Ppl13weekProductTypeEnum {

    CVM("CVM", "CVM&CBS"),

    EMR("EMR", "弹性MapReduce"),

    ES("ES", "Elasticsearch Service"),

    CDW("CDW", "云数据仓库"),

    EKS("EKS", "EKS官网"),

    GPU("GPU", "GPU(裸金属&CVM)"),

    BM("BM", "裸金属"),
    DLC("DLC", "数据湖DLC"),
    CS("CS", "CSIG容器平台"),

    LH("LH", "LightHouse"),

    // PAAS产品， paas类产品统称，数据层面不会这些值为PAAS产品的数据 仅做业务代码引用判断
    PAAS("PAAS", "PAAS产品"),

    DATABASE("DATABASE", "数据库", PplDatabaseEnum.allNames()),

    COS("COS", "COS"),

    CDC("CDC", "CDC"),

    CDZ("CDZ", "CDZ"),

    TEZ("TEZ", "TEZ"),


    ;

    static List<String> cvmCategoryProductNameList = new ArrayList<>(
            Arrays.asList(CVM.getName(),EMR.getName(), ES.getName(), CDW.getName()
                    , EKS.getName(), DLC.getName(), CS.getName()));

    static List<String> paasProductNameList = new ArrayList<>(Arrays.asList(EMR.getName(), ES.getName(), CDW.getName()
            , EKS.getName(), DLC.getName(), CS.getName()));

    static List<String> allProductNameList = new ArrayList<>(Arrays.asList(CVM.getName(), GPU.getName(), BM.getName(),
            EMR.getName(), ES.getName(), CDW.getName(), EKS.getName(), DLC.getName(), CS.getName()));

    static List<String> notSupport13WeekGroupList = new ArrayList<>(Arrays.asList(DATABASE.getName(), COS.getName()));


    final private String code;
    final private String name;

    final private List<String> subProducts;

    Ppl13weekProductTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
        this.subProducts = new ArrayList<>();
    }

    Ppl13weekProductTypeEnum(String code, String name, List<String> subProducts) {
        this.code = code;
        this.name = name;
        this.subProducts = subProducts;
    }


    public static List<String> getPaasProductNameList() {
        return paasProductNameList;
    }

    public static List<String> getAllProductNameList() {
        return allProductNameList;
    }

    public static List<String> getCvmCategoryProductNameList() {
        return cvmCategoryProductNameList;
    }

    public static List<String> getNotSupport13WeekGroupList() {
        return notSupport13WeekGroupList;
    }

    public static List<String> getProductCodeList(List<String> productNameList) {
        List<String> list = new ArrayList<>();
        for (String s : productNameList) {
            String codeByName  = getCodeByName(s);
            if (codeByName != null){
                list.add(codeByName);
            }
        }
        return list;
    }

        public static List<String> names() {
        return Arrays.stream(Ppl13weekProductTypeEnum.values())
                .map(Ppl13weekProductTypeEnum::getName)
                .collect(Collectors.toList());
    }

    public static Ppl13weekProductTypeEnum getByCode(String code) {
        for (Ppl13weekProductTypeEnum e : Ppl13weekProductTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static Ppl13weekProductTypeEnum getByName(String name) {
        for (Ppl13weekProductTypeEnum e : Ppl13weekProductTypeEnum.values()) {
            if (Objects.equals(name, e.getName())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        Ppl13weekProductTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }


    public static String getCodeByName(String name) {
        Ppl13weekProductTypeEnum e = getByName(name);
        return e == null ? "" : e.getCode();
    }

    public static String getUnitByProductName(String product) {
        if (product == null) {
            return "核";
        }
        switch (product) {
            case "CVM&CBS":
                return "核";
            case "GPU(裸金属&CVM)":
                return "卡";
            case "裸金属":
                return "台";
            default:
                // PAAS 产品均为核
                return "核";
        }
    }

}
