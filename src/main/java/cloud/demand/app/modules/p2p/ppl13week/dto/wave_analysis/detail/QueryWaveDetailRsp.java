package cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.detail;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 波动分析-明细响应体
 */
@Data
public class QueryWaveDetailRsp {

    @Data
    public static class ColumnName {
        private String title;
        private String dataIndex;

        public ColumnName(String title, String dataIndex) {
            this.title = title;
            this.dataIndex = dataIndex;
        }
    }

    private List<ColumnName> tableTitle;

    private List<Map<String, Object>> data;

}
