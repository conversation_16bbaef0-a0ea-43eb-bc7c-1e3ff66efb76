package cloud.demand.app.modules.p2p.ppl13week.service.filler.handler;

import cloud.demand.app.modules.operation_view.inventory_health.enums.InventoryHealthZoneType;
import cloud.demand.app.modules.operation_view.inventory_health.service.InventoryHealthConfigService;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.MainZoneFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerHandler;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class MainZoneFillerHandler implements FillerHandler<MainZoneFiller> {

    @Resource
    private InventoryHealthConfigService inventoryHealthConfigService;

    @Override
    public void fill(List<MainZoneFiller> obj) {
        String date = LocalDate.now().plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE);
        Map<String, List<String>> map = inventoryHealthConfigService.getZoneConfigMap(date);
        Set<String> mainZoneNames = new HashSet<>(
                map.getOrDefault(InventoryHealthZoneType.PRINCIPAL.getCode(), ListUtils.newList()));
        for (MainZoneFiller filler : obj) {
            filler.fillIsMainZone(mainZoneNames.contains(filler.provideZoneName()));
        }
    }
}
