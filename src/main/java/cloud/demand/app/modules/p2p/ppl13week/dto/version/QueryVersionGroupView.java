package cloud.demand.app.modules.p2p.ppl13week.dto.version;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class QueryVersionGroupView {

    /**分组id，必填*/
    @NotNull(message = "分组id必须提供")
    private Long groupId;

    /**审批结果，PASS或REJECT*/
    @NotBlank(message = "类型必须提供，WAR_ZONE CUSTOMER_NAME REGION INSTANCE_TYPE")
    private String type;


    private String startDate;


    private String  endDate;


    private String demandType;


    // 默认为核心数， GPU 产品加上后，gpu 产品展示卡数
    private String product;

}
