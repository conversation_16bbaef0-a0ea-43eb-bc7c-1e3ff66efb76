package cloud.demand.app.modules.p2p.ppl13week.config;

import java.util.List;
import java.util.function.Supplier;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import yunti.boot.config.DynamicProperty;

public class DynamicProperties {

    /**
     * 开启ppl单自动补需求的部门
     */
    private static final Supplier<String> pplIndustryDept =
            DynamicProperty.create("app.apply.pplIndustryDept", "智慧行业一部");

    /**
     * 直接判定为 长尾的 客户（按客户简称）
     */
    private static final Supplier<String> isLongtailCustomerShortName = DynamicProperty.create(
            "app.apply.isLongtailCustomerShortName",
            "深圳市腾讯计算机系统有限公司");

    /**
     * 直接判定为 长尾的 客户（按客户uin）
     */
    private static final Supplier<String> isLongtailCustomerUin = DynamicProperty.create(
            "app.apply.isLongtailCustomerUin",
            "3205597606");

    public static List<String> getApplyEnabledIndustryDept() {
        String industryDept = pplIndustryDept.get();
        if (Strings.isBlank(industryDept)) {
            return Lang.list();
        }
        return Lang.list(industryDept.trim().split(";"));
    }

    public static List<String> getLongtailCustomerShortName() {
        String customerShortName = isLongtailCustomerShortName.get();
        if (Strings.isBlank(customerShortName)) {
            return Lang.list();
        }
        return Lang.list(customerShortName.trim().split(";"));
    }

    public static List<String> getLongtailCustomerUin() {
        String customerUin = isLongtailCustomerUin.get();
        if (Strings.isBlank(customerUin)) {
            return Lang.list();
        }
        return Lang.list(customerUin.trim().split(";"));
    }

}
