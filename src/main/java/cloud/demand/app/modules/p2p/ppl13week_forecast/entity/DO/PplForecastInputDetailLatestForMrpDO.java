package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * 中长尾预测的数据
 */
@Data
@ToString
@Table("ppl_forecast_input_detail_latest_for_mrp")
public class PplForecastInputDetailLatestForMrpDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 年<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    @Column(value = "month")
    private Integer month;

    /** 海关<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title", insertValueScript = "''")
    private String customhouseTitle;

    /** 机型<br/>Column: [gins_family] */
    @Column(value = "gins_family")
    private String ginsFamily;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "core_num")
    private BigDecimal coreNum;

    /** new/ret/peak<br/>Column: [type] */
    @Column(value = "type")
    private String type;

    @Column(value = "diff_core_num")
    private BigDecimal diffCoreNum;

}