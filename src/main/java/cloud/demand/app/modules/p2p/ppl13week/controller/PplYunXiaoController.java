package cloud.demand.app.modules.p2p.ppl13week.controller;


import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.PlatformProductConfigListDTO;
import cloud.demand.app.modules.p2p.ppl13week.service.YunxiaoAPIService;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

/**
 * 基础的字典服务
 *
 * <AUTHOR>
 */
@Slf4j
@JsonrpcController("/ppl13week")
public class PplYunXiaoController {

    @Resource
    YunxiaoAPIService yunxiaoAPIService;


    @RequestMapping
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 1800) // 特别说明：已经和前端确认了，参数不会变化，所以这里没用keyScript
    String platformProductConfigList(@JsonrpcParam PlatformProductConfigListDTO req) {
        return yunxiaoAPIService.platformProductConfigList(req);
    }

}
