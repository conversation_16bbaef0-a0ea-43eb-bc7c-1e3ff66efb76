package cloud.demand.app.modules.p2p.industry_demand.dto.product;

import cloud.demand.app.common.utils.DateUtils.YearMonth;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.springframework.util.StringUtils;

@Data
public class MetalDTO extends BaseProductDTO {

    private String prePaidType;
    private String instanceType;
    private String instanceClass;

    public static MetalDTO excel2DTO(Map<Integer, String> map, List<YearMonth> yearMonthArray) {
        MetalDTO metalDTO = new MetalDTO();
        metalDTO.excelBaseSetting(map, 3, yearMonthArray);
        metalDTO.setPrePaidType(get(map, 0));
        metalDTO.setInstanceClass(get(map, 1));
        metalDTO.setInstanceType(get(map, 2));
        return metalDTO;
    }

    public static MetalDTO toDTO(Map<String, Object> data, List<YearMonth> yearMonthList) {
        MetalDTO dto = new MetalDTO();
        BaseProductDTO.baseMap2DTO(data, dto, yearMonthList);
        dto.setPrePaidType((String) data.get("prePaidType"));
        dto.setInstanceType((String) data.get("instanceType"));
        dto.setInstanceClass((String) data.get("instanceClass"));
        return dto;
    }

    @Override
    public void trim() {
        super.trim();
        this.prePaidType = StringUtils.trimWhitespace(prePaidType);
        this.instanceType = StringUtils.trimWhitespace(instanceType);
        this.instanceClass = StringUtils.trimWhitespace(instanceClass);
    }

    @Override
    public String[] getHeaders() {
        String[] common = super.getHeaders();
        String[] my = new String[]{"prePaidType", "instanceClass", "instanceType"};
        String[] yearMonth = getYearMonthHeader(false);
        String[] result = new String[common.length + my.length + yearMonth.length];
        System.arraycopy(common, 0, result, 0, common.length);
        System.arraycopy(my, 0, result, common.length, my.length);
        System.arraycopy(yearMonth, 0, result, common.length + my.length, yearMonth.length);
        return result;
    }

    @Override
    public String[] getChineseHeaders() {
        String[] common = super.getChineseHeaders();
        String[] my = new String[]{"付费类型", "实例类别", "实例类型"};
        String[] yearMonth = getYearMonthHeader(true);
        String[] result = new String[common.length + my.length + yearMonth.length];
        System.arraycopy(common, 0, result, 0, common.length);
        System.arraycopy(my, 0, result, common.length, my.length);
        System.arraycopy(yearMonth, 0, result, common.length + my.length, yearMonth.length);
        return result;
    }

    @Override
    public Map<String, Object> toMap() {
        Map<String, Object> result = super.toMap();
        result.put("prePaidType", this.getPrePaidType());
        result.put("instanceType", this.getInstanceType());
        result.put("instanceClass", this.getInstanceClass());
        return result;
    }

    @Override
    public String getWebName() {
        return "裸金属";
    }

    @Data
    public static class Json {

        private String prePaidType;
        private String instanceType;

        private String instanceClass;

        public Json(BaseProductDTO rawDto) {
            MetalDTO dto = (MetalDTO) rawDto;
            this.prePaidType = dto.prePaidType;
            this.instanceType = dto.instanceType;
            this.instanceClass = dto.instanceClass;
        }
    }
}
