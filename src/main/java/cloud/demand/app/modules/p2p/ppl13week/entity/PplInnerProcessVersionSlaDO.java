package cloud.demand.app.modules.p2p.ppl13week.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("ppl_inner_process_version_sla")
public class PplInnerProcessVersionSlaDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "version_id")
    private Long versionId;

    @Column(value = "node_code")
    private String nodeCode;

    @Column(value = "deadline_time")
    private Date deadlineTime;

    @Column(value = "deadline_type")
    private String deadlineType;

    /** 审批节点名 */
    @Column(value = "node_name")
    private String nodeName;

    /** 是否起始节点 */
    @Column(value = "is_begin_node")
    private Boolean isBeginNode;

    /** 下一个快照节点id（ppl_inner_process_version_sla表主键） */
    @Column(value = "next_sla_id")
    private Long nextSlaId;

    /** 审批角色Code */
    @Column(value = "approve_role")
    private String approveRole;

    /** 审批分组Code（CUSTOMER-客户, WAR_ZONE-战区, DEPT-部门） */
    @Column(value = "role_attribute")
    private String roleAttribute;

    /** 到期自动过单： CLOSE-关闭，UNCHANGED_PASS-无变化时自动过单，DIRECT_PASS-全部自动过单 */
    @Column(value = "deadline_auto_pass")
    private String deadlineAutoPass;

    /** 是否高亮展示审批意见 */
    @Column(value = "is_highlight")
    private Boolean isHighlight;

}