package cloud.demand.app.modules.p2p.industry_demand.service;

import cloud.demand.app.modules.p2p.industry_demand.dto.DataPermissionCondition;
import cloud.demand.app.modules.p2p.industry_demand.dto.KeyWordParam;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionDto;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionFilterReq;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionReq;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandRoleDictDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import com.pugwoo.dbhelper.model.PageData;
import java.util.List;

/**
 * 用户权限
 *
 * @see IndustryDemandAuthDO#getRole()
 */
public interface PermissionService {

    /**
     * 获取对应角色详情
     */
    IndustryDemandAuthDO getUserByRole(String roleCode, String username);

    /**
     * 获取角色-关键词-对应的用户
     */
    String getUserByRole(String roleCode, String industry, String product);

    /**
     * 获取角色-关键词-对应的用户
     */
    String getUserByRole(IndustryDemandAuthRoleEnum role, String industry, String product);

    /**
     * 获取角色对应的审批人
     *
     * @param roleCode 角色代码
     * @param industry 行业，必填
     * @param product 产品，必填
     * @param warZone 战区，必填
     */
    String getUserByRole(String roleCode, String industry, String product, String warZone);

    /**
     * 获取角色对应的审批人
     *
     * @param role 角色
     * @param industry 行业，必填
     * @param product 产品，必填
     * @param warZone 战区，必填
     */
    String getUserByRole(IndustryDemandAuthRoleEnum role, String industry, String product, String warZone);

    /**
     * 获得指定用户可以访问的战区(也即只返回有战区的)。管理员可以查看所有的战区
     *
     * @param username
     * @param industry 行业
     * @return
     */
    List<IndustryDemandAuthDO> getAuthForWarZone(String username, String industry);

    default String getUserByRole(IndustryDemandAuthRoleEnum role) {
        return getUserByRole(role, null, null);
    }

    /**
     * 获取角色-关键词-对应的用户
     */
    String getUserByRole(String role, KeyWordParam param);


    /**
     * 权限列表
     *
     * @param req
     * @return
     */
    PageData<UserPermissionDto> queryPermissionList(UserPermissionReq req);

    UserPermissionDto getPermissionByUserAndRole(String roleCode, String user);

    List<UserPermissionDto> getPermissionByUserAndRoles(List<String> roleCodes, String user);

    /**
     * 修改权限
     *
     * @param dto
     */
    void updatePermission(UserPermissionDto dto);

    /**
     * 增加权限
     *
     * @param dto
     */
    void addPermission(UserPermissionDto dto);

    /**
     * 删除权限
     *
     * @param dto
     */
    void delPermission(UserPermissionDto dto);

    /**
     * 聚合权限
     *
     * @param req
     * @return
     */
    UserPermissionDto getMergePermission(UserPermissionReq req);

    /**
     * 新增/更新角色定义
     *
     * @param role
     */
    void insertOrUpdate(IndustryDemandRoleDictDO role);

    /**
     * 获取某个行业下该用户的所有角色
     *
     * @param userName
     * @return
     */
    List<IndustryDemandAuthDO> getAllRoleByUsername(String userName, String industryDept);

    /**
     * 校验是否是管理员
     *
     * @param userName
     * @return
     */
    Boolean checkIsAdmin(String userName);

    /**
     *  检查是否为管理员，system、no 等非用户名不算管理员
     * @param userName 用户名
     */
    boolean checkIsActualAdmin(String userName);

    /**
     * 获取某个用户授权了的所有行业；取各角色的并集
     *
     * @return
     */
    List<String> queryUserIndustryDeptPermission(String username);

    /**
     * 查看是否为行业接口人
     */
    boolean checkIsIndustryOperate(String userName, String industryDept);

    /**
     * 查看传入的行业是否包含在此行业接口人登记的权限里
     */
    boolean checkIsContainSpecificIndustry(String userName, List<String> industryDept);

    /**
     *  为查询条件增加数据权限，保证查询条件在登陆用户对应的数据权限范围，同时会校验行业、产品、战区、客户的数据权限，无权限会报错出去<br/><br/>
     *
     *  ⚠️⚠️⚠️建议只使用一种角色进行权限数据添加，多种角色时权限可能不准确，<br/><br/>
     *
     *  ⚠️⚠️⚠️例如：a角色有 dept_a行业全部客户权限，b角色有dept_b行业部分客户权限（一般也不会这么配置），<br/>
     *  ⚠️⚠️⚠️输入a、b两个角色进行权限查询时，会返回dept_a、dept_b两个部门的全部客户权限。<br/>
     *  ⚠️⚠️⚠️无法实现 (dept = "dept_a") or (dept = "dept_b"  and customer in ("xxx","yyy"))，<br/>
     *  ⚠️⚠️⚠️实际结果却是 dept in ("dept_a","dept_b")。 <br/><br/>
     *
     *  ⚠️⚠️⚠️另一种情况：a角色有dept_a行业的 product_a产品权限，b角色有dept_b行业的 product_b产品权限（一般也不会这么配置），<br/>
     *  ⚠️⚠️⚠️输入a、b两个角色进行权限查询时，会返回dept_a、dept_b两个部门，以及 product_a、product_b两个产品，<br/>
     *  ⚠️⚠️⚠️无法实现 (dept = "dept_a" and product = "product_a") or (dept = "dept_b" and product = "product_b")，<br/>
     *  ⚠️⚠️⚠️实际结果却是 dept in ("dept_a","dept_b") and product in ("product_a","product_b")。<br/>
     * @param condition 查询条件（数据接口的查询条件），此方法会操作此对象内的查询条件<br/>
     * @param filter 数据权限查询的过滤条件，注意参数内的各种条件，以免影响数据权限结果。 <br/>
     */
    void addDataPermissionAndCheck(DataPermissionCondition condition, UserPermissionFilterReq filter);

    /**
     * 是否可查看全行业数据判断
     * @param username
     * @return
     */
    boolean isAllIndustryAuth(String username);

    /**
     *  根据权限角色和权限行业部门查询 用户名
     * @param role 为 null 时返回空列表
     * @param industryDept 为空时 返回空列表
     */
    List<String> queryUserNameByRoleAndIndustry(IndustryDemandAuthRoleEnum role, String industryDept);

    /**
     *  根据权限角色和权限行业部门查询 用户名 <br/>
     *  返回的用户名列表顺序：
     *  <ol>
     *      <li>精准匹配到行业部门的优先</li>
     *      <li>其次是包含了行业部门的</li>
     *      <li>最后是有所有行业部门权限的</li>
     *  </ol>
     * @param role 为 null 时返回空列表
     * @param industryDept 为空时 返回空列表
     */
    List<String> queryUserNameByRoleAndIndustrySort(IndustryDemandAuthRoleEnum role, String industryDept);

    boolean isRoleAndAllWarZone(String roleCode, String username,String industryDept);

    boolean hasRole(String userName, IndustryDemandAuthRoleEnum role);
}
