package cloud.demand.app.modules.p2p.product_demand.entity;

// package a.b.c;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("rrp_permission")
public class RrpPermissionDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /**
     * 用户名<br/>Column: [user]
     */
    @Column(value = "user")
    private String user;

    /**
     * 角色<br/>Column: [role]
     */
    @Column(value = "role")
    private String role;

    @Column(value = "product")
    private String product;

    @Column(value = "customer_name")
    private String customerName;

    @Column(value = "industry_dept")
    private String industryDept;

}