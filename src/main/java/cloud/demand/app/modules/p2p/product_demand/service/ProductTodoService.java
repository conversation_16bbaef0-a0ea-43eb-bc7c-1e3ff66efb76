package cloud.demand.app.modules.p2p.product_demand.service;

import cloud.demand.app.modules.p2p.industry_demand.service.TodoService;
import cloud.demand.app.modules.p2p.product_demand.dto.ApproveFlowDto;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandApproveRecordDO;
import cloud.demand.app.modules.p2p.product_demand.enums.ProductDemandGroupStatusEnum;
import com.fasterxml.jackson.annotation.JsonAlias;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 审批流程相关
 */
public interface ProductTodoService {


    /**
     * 获取审批流程
     *
     * @param groupId
     * @return
     */
    ApproveFlowDto productDemandApproveFlow(Long groupId);

    /**
     * 返回分组的审批日志
     *
     * @param groupId
     * @return
     */
    default List<ProductDemandApproveRecordDO> listApproveLogs(Long groupId) {
        return listApproveLogs(groupId, false);
    }

    /**
     * 返回分组的审批日志
     *
     * @param groupId
     * @param withALl
     * @return
     */
    List<ProductDemandApproveRecordDO> listApproveLogs(Long groupId, boolean withALl);

    /**
     * 判断某人是否能审批某个步骤
     *
     * @param orderTaskId 组成方式 version_groupId_status
     * @param operator
     * @return
     */
    boolean checkOrderStepCanApprove(String orderTaskId, String operator);

    /**
     * 开始审流程
     */
    void startProductDemandApproveFlow(long groupId, String userName);

    /**
     * 审批
     *
     * @param msg
     */
    void doProductDemandApprove(ApprovalMessageBody msg);

    /**
     * 获取节点审批人,多人使用";"隔开
     *
     * @return
     */
    String getApproveByStatus(ProductDemandGroupStatusEnum status, String planProduct, String creator);

    @Data
    class ApprovalData {

        String orderId;
        String taskId;
        String system;
        String sourceApp;
        String sourceEvent;
        String sourceAppCnName;
        String activity;
        String handler;
        String formUrl;
        List<ListView> listView;
        Integer isCallBackApi;
        String callBackUrl;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ListView {

        String key;
        String value;
    }

    @Data
    class ApprovalMessage {

        ApprovalMessageHeader headers;
        ApprovalMessageBody body;
    }


    @Data
    class ApprovalMessageHeader {

        String eventType;
        long occurrenceTime;
        String eventSource;
    }

    @Data
    class ApprovalMessageBody {

        @JsonAlias("approver-order")
        String approverOrder;
        @JsonAlias("approver-app-order")
        String approverAppOrder;
        String approver;
        @JsonAlias("approve-result")
        Integer approveResult;
        @JsonAlias("approve-memo")
        String approveMemo;
        @JsonAlias("approve-time")
        String approveTime;

        public static ApprovalMessageBody copy(TodoService.ApprovalMessageBody other) {
            ApprovalMessageBody my = new ApprovalMessageBody();
            my.setApproverOrder(other.getApproverOrder());
            my.setApproverAppOrder(other.getApproverAppOrder());
            my.setApprover(other.getApprover());
            my.setApproveResult(other.getApproveResult());
            my.setApproveMemo(other.getApproveMemo());
            my.setApproveTime(other.getApproveTime());
            return my;
        }
    }
}

