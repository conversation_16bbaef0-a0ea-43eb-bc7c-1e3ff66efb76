package cloud.demand.app.modules.p2p.ppl13week.dto.consensus;

import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class ConsensusVersionResp {

    private String versionCode;

    private Boolean isAllowConsensus;

    private String status;

    private Date issueTime;

    private Date pplFillTime;

    private Date applyDataFlushTime;

    private Date consensusDeadline;

    /**
     *  有供应方案的产品
     */
    private List<String> products;

    /**
     *  没有供应方案的产品
     */
    private List<String> nonDataProducts;
}
