package cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data;

import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq.DraftItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderAuditRecordItemVO;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.YearMonth;
import java.util.List;
import java.util.Objects;
import lombok.Data;

@Data
public class QueryBaseDataReq {

    private Boolean isPreSubmit = Boolean.FALSE;

    private YearMonth beginYearMonth;

    private YearMonth endYearMonth;

    private List<String> demandType;

    private List<String> customhouseTitle;

    private List<String> commonInstanceType;

    private List<String> region;

    private String product;

    private Long versionId;

    // 仅查询包基准数据
    private boolean onlyBaseData = false;

    // 查询空数据
    private boolean emptyData = false;

    private List<Long> baseDataIds;

    public WhereSQL toWhere() {
        WhereSQL where = new WhereSQL();
        where.and("deleted = 0 and latest = 1");
        if (beginYearMonth != null) {
            where.and("demand_year_month >= ?", beginYearMonth.toString());
        }
        if (endYearMonth != null) {
            where.and("demand_year_month <= ?", endYearMonth.toString());
        }
        if (ListUtils.isNotEmpty(demandType)) {
            where.and("demand_type in (?)", demandType);
        }
        if (ListUtils.isNotEmpty(customhouseTitle)) {
            where.and("customhouse_title in (?)", customhouseTitle);
        }
        if (ListUtils.isNotEmpty(commonInstanceType)) {
            where.and("common_instance_type in (?)", commonInstanceType);
        }
        if (ListUtils.isNotEmpty(region)) {
            where.and("region in (?)", region);
        }
        if (ListUtils.isNotEmpty(baseDataIds)) {
            where.and("id in (?)", baseDataIds);
        }
        where.and("product = ?", product);
        where.and("inner_version_id = ?", versionId);
        return where;
    }

    public boolean matchCondition(PplOrderAuditRecordItemVO ppl) {
        if (ppl == null) {
            return false;
        }
        if (ListUtils.isNotEmpty(demandType)) {
            PplDemandTypeEnum demandTypeEnum = BaseDataWithPplVO.findDemandType(ppl);
            String pplDemandTypeCode = demandTypeEnum == null ? null : demandTypeEnum.getCode();
            if (!demandType.contains(pplDemandTypeCode)) {
                return false;
            }
        }
        if (ListUtils.isNotEmpty(customhouseTitle) && !customhouseTitle.contains(ppl.getCustomhouseTitle())) {
            return false;
        }
        if (ListUtils.isNotEmpty(commonInstanceType) && !commonInstanceType.contains(ppl.getCommonInstanceType())) {
            return false;
        }
        if (ListUtils.isNotEmpty(region) && !region.contains(ppl.getRegion())) {
            return false;
        }
        if (!Objects.equals(product, ppl.getProduct())) {
            return false;
        }
        if (ppl.getDemandYearMonth() == null) {
            return false;
        }
        if (ppl.getDemandYearMonth().isBefore(beginYearMonth) || ppl.getDemandYearMonth().isAfter(endYearMonth)) {
            return false;
        }
        return true;
    }

    public boolean matchCondition(DraftItemDTO ppl) {
        if (ppl == null) {
            return false;
        }
        if (ListUtils.isNotEmpty(demandType)) {
            PplDemandTypeEnum demandTypeEnum = BaseDataWithPplVO.findDemandType(ppl);
            String pplDemandTypeCode = demandTypeEnum == null ? null : demandTypeEnum.getCode();
            if (!demandType.contains(pplDemandTypeCode)) {
                return false;
            }
        }
        if (ListUtils.isNotEmpty(customhouseTitle) && !customhouseTitle.contains(ppl.getCustomhouseTitle())) {
            return false;
        }
        if (ListUtils.isNotEmpty(commonInstanceType) && !commonInstanceType.contains(ppl.getPackageGroupKey())) {
            return false;
        }
        if (ListUtils.isNotEmpty(region) && !region.contains(ppl.getRegion())) {
            return false;
        }
        if (!Objects.equals(product, ppl.getProduct())
             && !Objects.equals(product, ppl.getDatabaseName())) {
            return false;
        }
        if (ppl.getDemandYearMonth() == null) {
            return false;
        }
        if (ppl.getDemandYearMonth().isBefore(beginYearMonth) || ppl.getDemandYearMonth().isAfter(endYearMonth)) {
            return false;
        }
        return true;
    }

}
