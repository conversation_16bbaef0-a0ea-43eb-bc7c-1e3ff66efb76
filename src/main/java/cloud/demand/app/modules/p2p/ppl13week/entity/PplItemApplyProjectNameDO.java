package cloud.demand.app.modules.p2p.ppl13week.entity;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 因云霄没有保存项目名称，所以由CRP记录
 */
@Data
@ToString
@Table("ppl_item_apply_project_name")
public class PplItemApplyProjectNameDO extends BaseDO {

    /** 云霄预约单id<br/>Column: [yunxiao_order_id] */
    @Column(value = "yunxiao_order_id")
    private String yunxiaoOrderId;

    /** 项目名称<br/>Column: [project_name] */
    @Column(value = "project_name")
    private String projectName;

}