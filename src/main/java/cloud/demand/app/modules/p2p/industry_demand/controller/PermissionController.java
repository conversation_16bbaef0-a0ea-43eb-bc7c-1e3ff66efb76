package cloud.demand.app.modules.p2p.industry_demand.controller;

import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionDto;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionReq;
import cloud.demand.app.modules.p2p.industry_demand.dto.dict.QueryCenterWarZoneReq;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandFlowDictDO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandRoleDictDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.dbhelper.DBHelper;
import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.exception.BizException;
import yunti.boot.security.CurrentUser;
import yunti.boot.security.TofUser;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@JsonrpcController("/industry/annual/api")
@Slf4j
public class PermissionController {

    @Resource
    PermissionService permissionService;

    @Resource
    DBHelper demandDBHelper;

    @Resource
    PplDictService pplDictService;

    @RequestMapping
    public Object queryIndustryUserPermission(
            @JsonrpcParam UserPermissionReq permissionReq) {
        return permissionService.queryPermissionList(permissionReq);
    }


    @RequestMapping
    public Object updateIndustryUserPermission(@CurrentUser TofUser user,
            @JsonrpcParam UserPermissionDto permissionReq) {
        checkIsAdmin(user.getUsername());
        permissionService.updatePermission(permissionReq);
        return ImmutableMap.of();
    }

    @RequestMapping
    public Object addIndustryUserPermission(@CurrentUser TofUser user, @JsonrpcParam UserPermissionDto permissionReq) {
        checkIsAdmin(user.getUsername());
        permissionService.addPermission(permissionReq);
        return ImmutableMap.of();
    }

    @RequestMapping
    public Object delIndustryUserPermission(@CurrentUser TofUser user, @JsonrpcParam UserPermissionDto permissionReq) {
        checkIsAdmin(user.getUsername());
        permissionService.delPermission(permissionReq);
        return ImmutableMap.of();
    }

    @RequestMapping
    public Object flowDefine(@CurrentUser TofUser user) {
        checkIsAdmin(user.getUsername());
        val flows = demandDBHelper.getAll(IndustryDemandFlowDictDO.class);
        return flows;
    }

    @RequestMapping
    public Object updateFlow(@CurrentUser TofUser user, UpdateFlowRoleQeq req) {
        checkIsAdmin(user.getUsername());
        IndustryDemandFlowDictDO flow = new IndustryDemandFlowDictDO();
        flow.setId(req.getId());
        demandDBHelper.updateCustom(flow, "auth_role=?", req.role);
        return ImmutableMap.of();
    }

    @RequestMapping
    public Object updateRole(@CurrentUser TofUser user, @JsonrpcParam UpdateRoleQeq req) {
        checkIsAdmin(user.getUsername());
        IndustryDemandRoleDictDO roleDictDO = new IndustryDemandRoleDictDO();
        roleDictDO.setId(req.id);
        roleDictDO.setRole(req.role);
        roleDictDO.setRoleName(req.roleName);
        permissionService.insertOrUpdate(roleDictDO);
        return ImmutableMap.of();
    }

    @RequestMapping
    public Object addRole(@CurrentUser TofUser user, @JsonrpcParam UpdateRoleQeq req) {
        checkIsAdmin(user.getUsername());
        IndustryDemandRoleDictDO roleDictDO = new IndustryDemandRoleDictDO();
        roleDictDO.setRole(req.role);
        roleDictDO.setRoleName(req.roleName);
        permissionService.insertOrUpdate(roleDictDO);
        return ImmutableMap.of();
    }

    @RequestMapping
    public Object queryCenterList(@JsonrpcParam QueryCenterWarZoneReq req) {
        return pplDictService.queryCenterList(req.getIndustryDept());
    }

    @RequestMapping
    public Object queryWarZoneList(@JsonrpcParam QueryCenterWarZoneReq req) {
        return pplDictService.queryWarZoneList(req.getIndustryDept());
    }

    @RequestMapping
    public Object queryUserIndustryDeptPermission() {
        return permissionService.queryUserIndustryDeptPermission(LoginUtils.getUserName());
    }

    void checkIsAdmin(String userName) {
        String admins = permissionService.getUserByRole(IndustryDemandAuthRoleEnum.ADMIN);
        if (Splitter.on(";").splitToList(admins).contains(userName)) {
            return;
        }
        throw new BizException("没有权限");
    }

    @Data
    public class UpdateRoleQeq {

        Long id;
        @NotEmpty
        String role;
        @NotEmpty
        String roleName;
        String desc;

    }

    @Data
    public class UpdateFlowRoleQeq {

        @NotEmpty
        Long id;
        @NotEmpty
        String role;
    }
}
