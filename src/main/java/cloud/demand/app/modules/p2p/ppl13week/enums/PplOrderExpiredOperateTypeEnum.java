package cloud.demand.app.modules.p2p.ppl13week.enums;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderExpiredRecordDO;
import java.util.Objects;
import lombok.Getter;

/**
 * ppl单过期操作方式枚举（此状态是相对于操作时所在的审批版本而言）
 *
 * @see PplOrderExpiredRecordDO#getExpiredOperateType()
 */
@Getter
public enum PplOrderExpiredOperateTypeEnum {

    TO_DEAL("TO_DEAL", "过期待处理"),

    DEAL_DELAY("DEAL_DELAY", "需求延期"),

    DEAL_IGNORE("DEAL_IGNORE", "忽略");

    final private String code;
    final private String name;

    PplOrderExpiredOperateTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PplOrderExpiredOperateTypeEnum getByCode(String code) {
        for (PplOrderExpiredOperateTypeEnum e : PplOrderExpiredOperateTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        PplOrderExpiredOperateTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}
