package cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply;

import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class QueryPplSupplyReq {

    @NotNull
    private String versionCode;

    private String type = "CVM";

    private List<String> industryDept;


    public List<String> getProduct() {
        List<String> product = new ArrayList<>();
        if ("CVM".equals(type)) {
            product = new ArrayList<>();
            for (Ppl13weekProductTypeEnum t : Ppl13weekProductTypeEnum.values()) {
                if (t != Ppl13weekProductTypeEnum.GPU && t != Ppl13weekProductTypeEnum.BM) {
                    product.add(t.getName());
                }
            }
        } else if ("GPU".equals(type)) {
            product.add(Ppl13weekProductTypeEnum.GPU.getName());
            product.add(Ppl13weekProductTypeEnum.BM.getName());
        } else {
            product.add("未知");
        }
        return product;
    }
}
