package cloud.demand.app.modules.p2p.industry_demand.entity;


import cloud.demand.app.common.BaseUserDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.Date;

@Data
@ToString
@Table("industry_demand_version")
public class IndustryDemandVersionDO extends BaseUserDO {

    /**
     * 状态，DISABLED-关闭 ENABLED-生效<br/>Column: [status]
     * @see cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandVersionStatusEnum
     */
    @Column(value = "status")
    private String status;

    /**
     * 版本号，代码层面控制唯一性<br/>Column: [demand_version]
     */
    @Column(value = "demand_version")
    private String demandVersion;

    /**
     * 版本名称<br/>Column: [name]
     */
    @Column(value = "name")
    private String name;

    /**
     * 描述<br/>Column: [desc]
     */
    @Column(value = "desc")
    private String desc;

    /**
     * 需求录入开放时间<br/>Column: [demand_import_open_date]
     */
    @Column(value = "demand_import_open_date")
    private LocalDate demandImportOpenDate;

    /**
     * 需求录入关闭时间<br/>Column: [demand_import_close_date]
     */
    @Column(value = "demand_import_close_date")
    private LocalDate demandImportCloseDate;

    /**
     * 预测开始年份<br/>Column: [forecast_from_year]
     */
    @Column(value = "forecast_from_year")
    private Integer forecastFromYear;

    /**
     * 预测开始月份<br/>Column: [forecast_from_month]
     */
    @Column(value = "forecast_from_month")
    private Integer forecastFromMonth;

    /**
     * 预测结束年份<br/>Column: [forecast_to_year]
     */
    @Column(value = "forecast_to_year")
    private Integer forecastToYear;

    /**
     * 预测结束月份<br/>Column: [forecast_to_month]
     */
    @Column(value = "forecast_to_month")
    private Integer forecastToMonth;

    /**
     * 版本完成时间<br/>Column: [finish_time]
     */
    @Column(value = "finish_time")
    private Date finishTime;

}
