package cloud.demand.app.modules.p2p.ppl13week.listener;

import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplItemChangeAllFieldDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplItemChangeFieldDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplItemChangeRecordNewDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplRecordChangeEventEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplRecordChangeStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.listener.event.PplChangeEvent;
import cloud.demand.app.modules.p2p.ppl13week.listener.tool.FieldComparator;
import cloud.demand.app.modules.p2p.ppl13week.service.PplChangeRecordService;
import com.pugwoo.dbhelper.DBHelper;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;

@EnableAsync
@Slf4j
@Component
public class PplChangeEventListener {

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    PplChangeRecordService pplChangeRecordService;


    @Async
    @TransactionalEventListener(condition = "#event.getSyncCode().equals('CHANGE_RECORD')")
    public void recordPplChangeEvent(PplChangeEvent event) {
        List<String> pplOrders = new ArrayList<>();
        List<PplItemChangeRecordNewDTO> recordItems = event.getRecordItems();

        // 1、收集 未设置pplOrder信息的 日志
        recordItems.forEach(recordItem -> {
            PplItemChangeAllFieldDTO currentItem = recordItem.getAfterItem();
            if (currentItem == null) {
                return;
            }

            if (StringUtils.isBlank(currentItem.getIndustryDept())) {
                pplOrders.add(currentItem.getPplOrder());
            }
        });

        // 2、获取pplOrder信息
        Map<String, PplOrderDO> pplOrderDOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(pplOrders)) {
            pplOrderDOMap = demandDBHelper.getAll(PplOrderDO.class, "where ppl_order in(?)", pplOrders).stream()
                    .collect(Collectors.toMap(PplOrderDO::getPplOrder, Function.identity(), (o1, o2) -> o2));
        }
        Map<String, PplOrderDO> finalPplOrderDOMap = pplOrderDOMap;

        // 3、补全日志信息 &  转换成可记录的格式
        List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
        recordItems.forEach(recordItem -> {
            PplItemChangeAllFieldDTO beforeItem = recordItem.getBeforeItem();
            PplItemChangeAllFieldDTO currentItem = recordItem.getAfterItem();

            if (currentItem == null) {
                return;
            }

            // 3.1、如果推送事件时未设置pplOrder信息，则进行补充
            if (StringUtils.isBlank(currentItem.getIndustryDept())) {
                PplOrderDO pplOrderDO = finalPplOrderDOMap.get(currentItem.getPplOrder());
                if (pplOrderDO != null) {
                    completeAllField(pplOrderDO, currentItem);
                    if (beforeItem != null) {
                        completeAllField(pplOrderDO, beforeItem);
                    }
                }
            }

            // 3.2、开始将事件信息 转换成 可记录的日志格式
            PplItemChangeRecordNewDTO newDTO = new PplItemChangeRecordNewDTO();
            BeanUtils.copyProperties(currentItem, newDTO);

            // 设置变更事件
            newDTO.setChangeType(event.getChangeType());
            newDTO.setChangeEvent(event.getChangeEvent());
            newDTO.setOperateUser(event.getOperateUser());

            newDTO.setDemandYear(currentItem.getBeginBuyDate().getYear());
            newDTO.setDemandMonth(currentItem.getBeginBuyDate().getMonthValue());

            // 设置变化内容概览
            newDTO.setChangeOverviewList(setChangeOverview(event.getChangeEvent(), beforeItem, currentItem));

            // 设置操作备注
            if (StringUtils.isNotBlank(newDTO.getYunxiaoOrderId())) {
                newDTO.setOperateNote("预约单：" + newDTO.getYunxiaoOrderId());
            } else if ((beforeItem != null && StringUtils.isNotBlank(beforeItem.getYunxiaoOrderId()))) {
                newDTO.setYunxiaoOrderId(beforeItem.getYunxiaoOrderId());
                newDTO.setOperateNote("预约单：" + beforeItem.getYunxiaoOrderId());
            }

            if (StringUtils.isNotBlank(recordItem.getOperateNote())) {
                if (newDTO.getOperateNote() == null) {
                    newDTO.setOperateNote(recordItem.getOperateNote());
                } else {
                    newDTO.setOperateNote(recordItem.getOperateNote() + " ; " + newDTO.getOperateNote());
                }
            }

            // 比较差异字段（先将有枚举类型的字段 从code转为name，后续比较差异字段时可直接将name值存入PplItemChangeFieldDTO的fieldValue）
            if (beforeItem != null) {
                beforeItem.setStatus(StringUtils.isBlank(beforeItem.getStatus()) ? PplItemStatusEnum.VALID.getName()
                        : PplItemStatusEnum.getNameByCode(beforeItem.getStatus()));
                beforeItem.setDemandType(PplDemandTypeEnum.getNameByCode(beforeItem.getDemandType()));
            }
            currentItem.setStatus(StringUtils.isBlank(currentItem.getStatus()) ? PplItemStatusEnum.VALID.getName()
                    : PplItemStatusEnum.getNameByCode(currentItem.getStatus()));
            currentItem.setDemandType(PplDemandTypeEnum.getNameByCode(currentItem.getDemandType()));

            List<Pair<PplItemChangeFieldDTO, PplItemChangeFieldDTO>> differentFields = new ArrayList<>();
            List<PplItemChangeFieldDTO> beforeChangeFields = new ArrayList<>();
            List<PplItemChangeFieldDTO> afterChangeFields = new ArrayList<>();
            try {
                differentFields = FieldComparator.getDifferentFields(beforeItem, currentItem);
            } catch (IllegalAccessException e) {
                log.error("getDifferentFields, 比较差异字段出错", e);
            }

            differentFields.forEach(e -> {
                beforeChangeFields.add(e.getLeft());
                afterChangeFields.add(e.getRight());
            });

            // 设置变化前后item、差异字段
            if (beforeItem != null) {
                beforeItem.setChangeStatus(PplRecordChangeStatusEnum.CHANGE_BEFORE.getCode());
                beforeItem.setChangeFieldDTOList(beforeChangeFields);
                newDTO.setBeforeItem(beforeItem);
            }

            currentItem.setChangeStatus(PplRecordChangeStatusEnum.CHANGE_AFTER.getCode());
            currentItem.setChangeFieldDTOList(afterChangeFields);
            newDTO.setAfterItem(currentItem);

            newDTOs.add(newDTO);
        });

        // 4、记录日志
        pplChangeRecordService.recordPplChange(newDTOs);
    }

    // 补全 日志记录字段（PplItemChangeAllFieldDTO）
    private void completeAllField(PplOrderDO pplOrderDO, PplItemChangeAllFieldDTO allFieldDTO) {
        allFieldDTO.setSource(pplOrderDO.getSource());
        allFieldDTO.setIndustryDept(pplOrderDO.getIndustryDept());

        allFieldDTO.setCustomerType(pplOrderDO.getCustomerType());
        allFieldDTO.setCustomerName(pplOrderDO.getCustomerName());
        allFieldDTO.setCustomerShortName(pplOrderDO.getCustomerShortName());
        allFieldDTO.setCustomerUin(pplOrderDO.getCustomerUin());
        allFieldDTO.setCustomerSource(pplOrderDO.getCustomerSource());

        allFieldDTO.setCenter(pplOrderDO.getCenter());
        allFieldDTO.setWarZone(pplOrderDO.getWarZone());
        allFieldDTO.setSubmitUser(pplOrderDO.getSubmitUser());

    }

    // 设置 变化内容概览
    private List<String> setChangeOverview(String changeEvent, PplItemChangeAllFieldDTO beforeItem,
            PplItemChangeAllFieldDTO currentItem) {
        List<String> changeOverviewList = new ArrayList<>();
        List<String> changeEventList = Arrays.asList(PplRecordChangeEventEnum.PPL_IMPORT_ADD.getCode(),
                PplRecordChangeEventEnum.PPL_AUDIT_ADD.getCode(), PplRecordChangeEventEnum.YUN_SUBMITTED_ADD.getCode());

        boolean isAllowBeforeItemNull = beforeItem == null && changeEventList.contains(changeEvent);
        if (currentItem.getInstanceNum() != null && (isAllowBeforeItemNull || (beforeItem != null && !Objects.equals(
                beforeItem.getInstanceNum(),
                currentItem.getInstanceNum())))) {
            changeOverviewList.add("实例台数：" + (beforeItem == null ? 0 : beforeItem.getInstanceNum()) + "->"
                    + currentItem.getInstanceNum());
        }
        if (currentItem.getTotalCore() != null && (isAllowBeforeItemNull || (beforeItem != null && !Objects.equals(
                beforeItem.getTotalCore(),
                currentItem.getTotalCore())))) {
            changeOverviewList.add("需求核心数：" + (beforeItem == null ? 0 : beforeItem.getTotalCore()) + "->"
                    + currentItem.getTotalCore());
        }
        if (Ppl13weekProductTypeEnum.GPU.getName().equals(currentItem.getProduct())) {
            if (currentItem.getTotalGpuNum() != null && (isAllowBeforeItemNull || (beforeItem != null
                    && !Objects.equals(
                    beforeItem.getTotalGpuNum(), currentItem.getTotalGpuNum())))) {
                changeOverviewList.add("需求卡数：" + (beforeItem == null ? 0 : beforeItem.getTotalGpuNum()) + "->"
                        + currentItem.getTotalGpuNum());
            }
        }
        if (Ppl13weekProductTypeEnum.COS.getName().equals(currentItem.getProduct())) {
            if (currentItem.getTotalCosStorage() != null && (isAllowBeforeItemNull || (beforeItem != null
                    && !Objects.equals(
                    beforeItem.getTotalCosStorage(), currentItem.getTotalCosStorage())))) {
                changeOverviewList.add("总COS存储量：" + (beforeItem == null ? 0 : beforeItem.getTotalCosStorage()) + "->"
                        + currentItem.getTotalCosStorage());
            }
        }
        if (Ppl13weekProductTypeEnum.DATABASE.getName().equals(currentItem.getProduct())) {
            if (currentItem.getTotalDatabaseStorage() != null && (isAllowBeforeItemNull || (beforeItem != null
                    && !Objects.equals(
                    beforeItem.getTotalDatabaseStorage(), currentItem.getTotalDatabaseStorage())))) {
                changeOverviewList.add("总数据库存储量：" + (beforeItem == null ? 0 : beforeItem.getTotalDatabaseStorage()) + "->"
                        + currentItem.getTotalDatabaseStorage());
            }
            if (currentItem.getTotalMemory() != null && (isAllowBeforeItemNull || (beforeItem != null
                    && !Objects.equals(
                    beforeItem.getTotalMemory(), currentItem.getTotalMemory())))) {
                changeOverviewList.add("总内存数：" + (beforeItem == null ? 0 : beforeItem.getTotalMemory()) + "->"
                        + currentItem.getTotalMemory());
            }
        }

        if ((StringUtils.isNotBlank(currentItem.getYunxiaoOrderId()) &&
                StringUtils.isBlank(beforeItem == null ? null : beforeItem.getYunxiaoOrderId()))
                ||
                (StringUtils.isNotBlank(beforeItem == null ? null : beforeItem.getYunxiaoOrderId())
                        && StringUtils.isBlank(currentItem.getYunxiaoOrderId()))) {
            if (Ppl13weekProductTypeEnum.GPU.getName().equals(currentItem.getProduct())) {
                currentItem.setTotalGpuNum(
                        currentItem.getTotalGpuNum() == null ? BigDecimal.ZERO : currentItem.getTotalGpuNum());
                currentItem.setTotalGpuNumApplyBefore(currentItem.getTotalGpuNumApplyBefore() == null ? BigDecimal.ZERO
                        : currentItem.getTotalGpuNumApplyBefore());
                currentItem.setTotalGpuNumApplyAfter(currentItem.getTotalGpuNumApplyAfter() == null ? BigDecimal.ZERO
                        : currentItem.getTotalGpuNumApplyAfter());
                BigDecimal beforeTotalGpuNumApplyAfter = beforeItem == null ? BigDecimal.ZERO : beforeItem.getTotalGpuNumApplyAfter();
                changeOverviewList.add("已预约卡数：" + beforeTotalGpuNumApplyAfter + " -> "
                        + currentItem.getTotalGpuNumApplyAfter());
                changeOverviewList.add(
                        "未预约卡数：" + currentItem.getTotalGpuNum().subtract(beforeTotalGpuNumApplyAfter)
                                + " -> " + currentItem.getTotalGpuNum()
                                .subtract(currentItem.getTotalGpuNumApplyAfter()));
            } else {
                currentItem.setTotalCore(currentItem.getTotalCore() == null ? 0 : currentItem.getTotalCore());
                currentItem.setTotalCoreApplyBefore(
                        currentItem.getTotalCoreApplyBefore() == null ? 0 : currentItem.getTotalCoreApplyBefore());
                currentItem.setTotalCoreApplyAfter(
                        currentItem.getTotalCoreApplyAfter() == null ? 0 : currentItem.getTotalCoreApplyAfter());
                Integer beforeTotalCoreApplyAfter = beforeItem == null ? 0 : beforeItem.getTotalCoreApplyAfter();
                changeOverviewList.add("已预约核心数：" + beforeTotalCoreApplyAfter + " -> "
                        + currentItem.getTotalCoreApplyAfter());
                changeOverviewList.add(
                        "未预约核心数：" + (currentItem.getTotalCore() - beforeItem.getTotalCoreApplyAfter()) + " -> "
                                + (currentItem.getTotalCore() - currentItem.getTotalCoreApplyAfter()));
            }
        }

        List<String> consensusEventList = Arrays.asList(PplRecordChangeEventEnum.CONSENSUS_ACCEPT.getCode(),
                PplRecordChangeEventEnum.CONSENSUS_REFUSE.getCode());
        if (consensusEventList.contains(changeEvent)) {
            StringJoiner consensusInstanceType = new StringJoiner("->");
            if (beforeItem != null && beforeItem.getConsensusInstanceType() != null) {
                consensusInstanceType.add("需求机型：" + beforeItem.getConsensusInstanceType());
            }
            if (currentItem.getConsensusInstanceType() != null) {
                consensusInstanceType.add("共识机型：" + currentItem.getConsensusInstanceType());
            }
            changeOverviewList.add(consensusInstanceType.toString());

            StringJoiner consensusZoneName = new StringJoiner("->");
            if (beforeItem != null && beforeItem.getConsensusZoneName() != null) {
                consensusZoneName.add("需求可用区：" + beforeItem.getConsensusZoneName());
            }
            if (currentItem.getConsensusZoneName() != null) {
                consensusZoneName.add("共识可用区：" + currentItem.getConsensusZoneName());
            }
            changeOverviewList.add(consensusZoneName.toString());

            StringJoiner consensusDemandDate = new StringJoiner("->");
            if (beforeItem != null && beforeItem.getConsensusDemandDate() != null) {
                consensusDemandDate.add("需求日期：" + beforeItem.getConsensusDemandDate());
            }
            if (currentItem.getConsensusDemandDate() != null) {
                consensusDemandDate.add("共识需求日期：" + currentItem.getConsensusDemandDate());
            }
            changeOverviewList.add(consensusDemandDate.toString());

            if (currentItem.getConsensusTotalCore() != null) {
                changeOverviewList.add("本条共识核心数：" + currentItem.getConsensusTotalCore());
            }
        }
        return changeOverviewList;
    }

}
