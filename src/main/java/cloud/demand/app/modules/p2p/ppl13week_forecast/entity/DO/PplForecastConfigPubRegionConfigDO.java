package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 *
 * 用于定义哪些园区为公有云可售卖园区，以及部分园区的替代关系
 */
@Data
@ToString
@Table("ppl_forecast_config_pub_region_config")
public class PplForecastConfigPubRegionConfigDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    @Column(value = "create_time")
    private Timestamp createTime;

    @Column(value = "update_time")
    private Timestamp updateTime;

    /** 国内国外<br/>Column: [region_type] */
    @Column(value = "region_type")
    private String regionType;

    @Column(value = "pm_city_name")
    private String pmCityName;

    @Column(value = "pm_campus")
    private String pmCampus;

    @Column(value = "city_name")
    private String cityName;

    @Column(value = "zone_name")
    private String zoneName;

    /** 是否有效<br/>Column: [is_value] */
    @Column(value = "is_value")
    private Boolean isValue;

    @Column(value = "replace_zone_name")
    private String replaceZoneName;

    @Column(value = "replace_exclude_instance_type")
    private String replaceExcludeInstanceType;

}