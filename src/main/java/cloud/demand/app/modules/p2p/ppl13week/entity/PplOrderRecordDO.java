package cloud.demand.app.modules.p2p.ppl13week.entity;


import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_order_record")
public class PplOrderRecordDO extends BaseDO {

    /**
     * ppl单号<br/>Column: [ppl_order]
     */
    @Column(value = "ppl_order")
    private String pplOrder;

    /**
     * ppl单版本，和明细保持一致<br/>Column: [record_version]
     */
    @Column(value = "record_version")
    private Integer recordVersion;

    /**
     * 当前节点状态<br/>Column: [status]
     *
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderStatusEnum
     */
    @Column(value = "status")
    private String status;
//
//    /** 批次号，同一批审批相同值<br/>Column: [batch_num] */
//    @Column(value = "batch_num")
//    private Integer batchNum;

    /**
     * 操作人<br/>Column: [operate_user]
     */
    @Column(value = "operate_user")
    private String operateUser;

    /**
     * 审批时间<br/>Column: [approve_time]
     */
    @Column(value = "approve_time")
    private Date approveTime;

    /**
     * 审批结果<br/>Column: [approve_result]
     */
    @Column(value = "approve_result")
    private String approveResult;

    /**
     * 审批备注<br/>Column: [approve_note]
     */
    @Column(value = "approve_note", insertValueScript = "''")
    private String approveNote;

}