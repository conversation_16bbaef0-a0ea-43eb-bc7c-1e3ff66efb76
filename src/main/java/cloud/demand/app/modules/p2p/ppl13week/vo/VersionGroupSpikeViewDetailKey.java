package cloud.demand.app.modules.p2p.ppl13week.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class VersionGroupSpikeViewDetailKey {

    @Column(value = "demand_type1")
    private String demandType;

    @Column(value = "customer")
    private String customer;

    @Column(value = "ym")
    private String yearMonth;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "instance_type")
    private String instanceType;

}