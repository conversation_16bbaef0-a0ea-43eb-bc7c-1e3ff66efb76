package cloud.demand.app.modules.p2p.ppl13week.dto.item;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplProjectTypeEnum;
import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.utils.DOInfoReader;
import com.pugwoo.wooutils.lang.DateUtils;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.util.StringUtils;

@Data
@Slf4j
public class PplComdInterveneCvmDTO {

    @ExcelProperty(index = 0, value = "ppl-id")
    private String pplId;

    @ExcelProperty(index = 1, value = "客户类型")
    private String customerTypeName;
    @ExcelProperty(index = 2, value = "客户Uin")
    private String customerUin;
    @ExcelProperty(index = 3, value = "客户简称")
    private String customerShortName;

    /**
     * 需求类型
     */
    @ExcelProperty(index = 4, value = "需求类型")
    private String demandTypeName;

    /**
     * 需求场景<br/>Column: [demand_scene]
     */
    @ExcelProperty(index = 5, value = "需求场景")
    private String demandScene;

    /**
     * 项目名称<br/>Column: [project_name]
     */
    @ExcelProperty(index = 6, value = "项目名称")
    private String projectName;

    /**
     * 计费模式<br/>Column: [bill_type]
     */
    @ExcelProperty(index = 7, value = "计费模式")
    private String billType;

    /**
     * 赢率，百分比<br/>Column: [win_rate]
     */
    @ExcelProperty(index = 8, value = "赢率，百分比")
    private String winRate;


    /**
     * 开始购买日期<br/>Column: [begin_buy_date]
     */
    @ExcelProperty(index = 9, value = "开始购买日期")
    private String beginBuyDate;

    /**
     * 结束购买日期<br/>Column: [end_buy_date]
     */
    @ExcelProperty(index = 10, value = "结束购买日期")
    private String endBuyDate;


    /**
     * 弹性开始日期<br/>Column: [begin_elastic_date]
     */
    @ExcelProperty(index = 11, value = "弹性开始日期")
    private String beginElasticDate;

    /**
     * 弹性结束日期<br/>Column: [end_elastic_date]
     */
    @ExcelProperty(index = 12, value = "弹性结束日期")
    private String endElasticDate;

    /**
     * 特殊备注说明<br/>Column: [note]
     */
    @ExcelProperty(index = 13, value = "特殊备注说明")
    private String note;

    /**
     * 地域，看看要不要存id<br/>Column: [region_name]
     */
    @ExcelProperty(index = 14, value = "地域")
    private String regionName;

    /**
     * 可用区，看看要不要存id<br/>Column: [zone_name]
     */
    @ExcelProperty(index = 15, value = "可用区")
    private String zoneName;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @ExcelProperty(index = 16, value = "实例类型")
    private String instanceType;


    /**
     * 实例规格<br/>Column: [instance_model]
     */
    @ExcelProperty(index = 17, value = "实例规格")
    private String instanceModel;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    @ExcelProperty(index = 18, value = "实例规格核心数")
    private String instanceModelCpuCore;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    @ExcelProperty(index = 19, value = "实例规格内存数")
    private String instanceModelRam;


    /**
     * 实例数量<br/>Column: [instance_num]
     */
    @ExcelProperty(index = 20, value = "实例数量")
    private Integer instanceNum;


    /**
     * 预约状态<br/>Column: [appliedStatus]
     */
    @ExcelProperty(index = 21, value = "预约状态")
    private String appliedStatus;

    /**
     * 预约单号<br/>Column: [applyOrder]
     */
    @ExcelProperty(index = 22, value = "预约单号")
    private String yunxiaoOrderId;


    /**
     * totalCoreNum<br/>Column: [totalCoreNum]
     */
    @ExcelProperty(index = 23, value = "总核心数")
    private Integer totalCoreNum;

    @ExcelProperty(index = 24, value = "预约总核心数")
    private Integer applyTotalCore;

    @ExcelProperty(index = 25, value = "预约单锁定总核心数")
    private Integer applyLockCore;

    @ExcelProperty(index = 26, value = "实际对冲总核心")
    private Integer stockSupplyCore;

    @ExcelProperty(index = 27, value = "共识状态名称")
    private String consensusStatusName;

    @ExcelProperty(index = 28, value = "共识机型")
    private String consensusInstanceType;

    @ExcelProperty(index = 29, value = "共识可用区")
    private String consensusZoneName;

    @ExcelProperty(index = 30, value = "共识需求时间")
    private String consensusDemandDate;

    @ExcelProperty(index = 31, value = "预约数量")
    private Integer applyInstanceNum;
    @ExcelProperty(index = 32, value = "预约总核心数")
    private Integer applyTotalCore2;

    @ExcelProperty(index = 33, value = "预测来源")
    private String sourceTypeName;

    @ExcelProperty(index = 34, value = "已占用核心数")
    private Integer occupyOthersNum;

    @ExcelProperty(index = 35, value = "占用ID")
    private String occupyOthersPplIds;

    @ExcelProperty(index = 36, value = "被占用核心数")
    private Integer occupiedNum;

    /**
     * isAcceptAlternative
     */
    @ExcelProperty(index = 37, value = "isAcceptAlternative")
    private String isAcceptAlternative;

    /**
     * 不用填
     */
    @ExcelProperty(index = 38, value = "接受机型")
    private String alternativeInstanceType;

    /**
     * 亲和度类型<br/>Column: [affinity_type]
     */
    @ExcelProperty(index = 39, value = "亲和度类型")
    private String affinityType;

    /**
     * 亲和度值<br/>Column: [affinity_value]
     */
    @ExcelProperty(index = 40, value = "亲和度值")
    private String affinityValue;


    //todo 这里少了一个单实例 2024-05-07

    /**
     * 磁盘类型<br/>Column: [system_disk_type]
     */
    @ExcelProperty(index = 41, value = "系统磁盘类型")
    private String systemDiskType;

    /**
     * 磁盘容量，单位G<br/>Column: [system_disk_storage]
     */
    @ExcelProperty(index = 42, value = "系统磁盘容量")
    private String systemDiskStorage;

    /**
     * 磁盘类型<br/>Column: [data_disk_type]
     */
    @ExcelProperty(index = 43, value = "数据磁盘类型")
    private String dataDiskType;

    /**
     * 磁盘容量，单位G<br/>Column: [data_disk_storage]
     */
    @ExcelProperty(index = 44, value = "数据磁盘容量")
    private String dataDiskStorage;

    /**
     * 磁盘块数<br/>Column: [data_disk_num]
     */
    @ExcelProperty(index = 45, value = "数据磁盘块数")
    private String dataDiskNum;

    /**
     * 业务唯一标识ID<br/>Column: [biz_id]
     */
    @ExcelProperty(index = 46, value = "业务唯一标识ID")
    private String bizId;

    /**
     * 实际是取ppl_inner_process_version表的begin_date 作为行业审批版本号
     */
    @ExcelProperty(index = 47, value = "行业预测版本号")
    private String innerVersionCode;

    /**
     * 新版本同步 / 继承上版
     */
    @ExcelProperty(index = 48, value = "数据来源")
    private String syncSource;

    /**
     * 需求年月
     */
    @ExcelProperty(index = 49, value = "需求年月")
    private String yearMonth;

    /**
     * 是否毛刺
     */
    @ExcelProperty(index = 51, value = "是否毛刺")
    private String isSpikeName;

    private String cbsIo; // CBS单实例IO(MB/s)

    private String isStrongDesignateZone;



    public static PplImportExcelDTO copyToNewDTO(PplComdInterveneCvmDTO source) {
        PplImportExcelDTO pplCvmImportExcelDTO = new PplImportExcelDTO();
        pplCvmImportExcelDTO.setCustomerTypeName(source.getCustomerTypeName());
        pplCvmImportExcelDTO.setCustomerUin(source.getCustomerUin());
        pplCvmImportExcelDTO.setCustomerShortName(source.getCustomerShortName());
        pplCvmImportExcelDTO.setDemandTypeName(source.getDemandTypeName());
        pplCvmImportExcelDTO.setDemandScene(source.getDemandScene());
        pplCvmImportExcelDTO.setProjectName(source.getProjectName());
        pplCvmImportExcelDTO.setBillType(source.getBillType());
        pplCvmImportExcelDTO.setWinRate(source.getWinRate());
        // 兼容2025/1/6这种格式
        if (source.getBeginBuyDate().contains("/")) {
            pplCvmImportExcelDTO.setBeginBuyDate(source.getBeginBuyDate()
                    .replaceAll("(\\d+)/(\\d{1,2})/(\\d{1,2})", "$1-$2-$3")
                    .replaceAll("-(\\d)(?!\\d)", "-0$1"));
        } else {
            pplCvmImportExcelDTO.setBeginBuyDate(source.getBeginBuyDate());
        }
        if (source.getEndBuyDate().contains("/")) {
            pplCvmImportExcelDTO.setEndBuyDate(source.getEndBuyDate()
                    .replaceAll("(\\d+)/(\\d{1,2})/(\\d{1,2})", "$1-$2-$3")
                    .replaceAll("-(\\d)(?!\\d)", "-0$1"));
        } else {
            pplCvmImportExcelDTO.setEndBuyDate(source.getEndBuyDate());
        }
        pplCvmImportExcelDTO.setBeginElasticDate(source.getBeginElasticDate());
        pplCvmImportExcelDTO.setEndElasticDate(source.getEndElasticDate());
        pplCvmImportExcelDTO.setNote(source.getNote());
        pplCvmImportExcelDTO.setRegionName(source.getRegionName());
        pplCvmImportExcelDTO.setZoneName(source.getZoneName());
        pplCvmImportExcelDTO.setInstanceType(source.getInstanceType());
        pplCvmImportExcelDTO.setInstanceModel(source.getInstanceModel());
        pplCvmImportExcelDTO.setInstanceModelCpuCore(source.getInstanceModelCpuCore());
        pplCvmImportExcelDTO.setInstanceModelRam(source.getInstanceModelRam());
        pplCvmImportExcelDTO.setInstanceNum(source.getInstanceNum() == null ? "0" : source.getInstanceNum().toString());
        pplCvmImportExcelDTO.setTotalCoreNum(
                source.getTotalCoreNum() == null ? "0" : source.getTotalCoreNum().toString());
        pplCvmImportExcelDTO.setIsAcceptAlternative(source.getIsAcceptAlternative());
        pplCvmImportExcelDTO.setAlternativeInstanceType(source.getAlternativeInstanceType());
        pplCvmImportExcelDTO.setAffinityType(source.getAffinityType());
        pplCvmImportExcelDTO.setAffinityValue(source.getAffinityValue());
        pplCvmImportExcelDTO.setSystemDiskType(source.getSystemDiskType());
        pplCvmImportExcelDTO.setSystemDiskStorage(source.getSystemDiskStorage());
        pplCvmImportExcelDTO.setDataDiskType(source.getDataDiskType());
        pplCvmImportExcelDTO.setDataDiskStorage(source.getDataDiskStorage());
        pplCvmImportExcelDTO.setDataDiskNum(source.getDataDiskNum());

        pplCvmImportExcelDTO.setPplId(source.getPplId());
        pplCvmImportExcelDTO.setProjectName(source.getProjectName());
        pplCvmImportExcelDTO.setAppliedStatus(source.getAppliedStatus());
        pplCvmImportExcelDTO.setYunxiaoOrderId(source.getYunxiaoOrderId());
        return pplCvmImportExcelDTO;
    }


    public static PplComdInterveneCvmDTO copy(PplComdInterveneCvmDTO source) {
        PplComdInterveneCvmDTO pplCvmImportExcelDTO = new PplComdInterveneCvmDTO();
        pplCvmImportExcelDTO.setCustomerTypeName(source.getCustomerTypeName());
        pplCvmImportExcelDTO.setCustomerUin(source.getCustomerUin());
        pplCvmImportExcelDTO.setCustomerShortName(source.getCustomerShortName());
        pplCvmImportExcelDTO.setDemandTypeName(source.getDemandTypeName());
        pplCvmImportExcelDTO.setDemandScene(source.getDemandScene());
        pplCvmImportExcelDTO.setProjectName(source.getProjectName());
        pplCvmImportExcelDTO.setBillType(source.getBillType());
        pplCvmImportExcelDTO.setWinRate(source.getWinRate());
        pplCvmImportExcelDTO.setBeginBuyDate(source.getBeginBuyDate());
        pplCvmImportExcelDTO.setEndBuyDate(source.getEndBuyDate());
        pplCvmImportExcelDTO.setBeginElasticDate(source.getBeginElasticDate());
        pplCvmImportExcelDTO.setEndElasticDate(source.getEndElasticDate());
        pplCvmImportExcelDTO.setNote(source.getNote());
        pplCvmImportExcelDTO.setRegionName(source.getRegionName());
        pplCvmImportExcelDTO.setZoneName(source.getZoneName());
        pplCvmImportExcelDTO.setInstanceType(source.getInstanceType());
        pplCvmImportExcelDTO.setInstanceModel(source.getInstanceModel());
        pplCvmImportExcelDTO.setInstanceModelCpuCore(source.getInstanceModelCpuCore());
        pplCvmImportExcelDTO.setInstanceModelRam(source.getInstanceModelRam());
        pplCvmImportExcelDTO.setInstanceNum(source.getInstanceNum());
        pplCvmImportExcelDTO.setTotalCoreNum(source.getTotalCoreNum());
        pplCvmImportExcelDTO.setIsAcceptAlternative(source.getIsAcceptAlternative());
        pplCvmImportExcelDTO.setAlternativeInstanceType(source.getAlternativeInstanceType());
        pplCvmImportExcelDTO.setAffinityType(source.getAffinityType());
        pplCvmImportExcelDTO.setAffinityValue(source.getAffinityValue());
        pplCvmImportExcelDTO.setSystemDiskType(source.getSystemDiskType());
        pplCvmImportExcelDTO.setSystemDiskStorage(source.getSystemDiskStorage());
        pplCvmImportExcelDTO.setDataDiskType(source.getDataDiskType());
        pplCvmImportExcelDTO.setDataDiskStorage(source.getDataDiskStorage());
        pplCvmImportExcelDTO.setDataDiskNum(source.getDataDiskNum());
        pplCvmImportExcelDTO.setAppliedStatus(source.getAppliedStatus());
        return pplCvmImportExcelDTO;
    }

    public static List<PplComdInterveneCvmDTO> transFrom(List<VersionGroupItemResp.GroupItemDTO> data,
            Map<Long, PplInnerProcessVersionDO> innerProcessVersionDOMap) {
        List<PplComdInterveneCvmDTO> pplCvmImportExcelDTOlist = Lists.newArrayList();
        for (VersionGroupItemResp.GroupItemDTO groupItemDTO : data) {
            PplComdInterveneCvmDTO pplCvmImportExcelDTO = convertFromGroupItemDTO(groupItemDTO,
                    innerProcessVersionDOMap);
            if (Strings.isBlank(groupItemDTO.getSourcePplId())
                    && (Strings.isBlank(pplCvmImportExcelDTO.getInstanceNum().toString())
                    || Objects.equals(pplCvmImportExcelDTO.getInstanceNum().toString().trim(), "0"))
                    && (pplCvmImportExcelDTO.getApplyInstanceNum() == null
                    || pplCvmImportExcelDTO.getApplyInstanceNum() < 0)) {
                continue;
            }
            pplCvmImportExcelDTOlist.add(pplCvmImportExcelDTO);
        }
        return pplCvmImportExcelDTOlist;
    }

    private static String null2empty(BigDecimal a) {
        return a == null ? "" : a.setScale(3, RoundingMode.HALF_UP).toString();
    }

    private static String null2empty(LocalTime a) {
        return a == null ? "" : DateUtils.format(a, "HH:mm");
    }

    private static String null2empty(LocalDate a) {
        return a == null ? "" : DateUtils.format(a, "yyyy-MM-dd");
    }

    private static String null2empty(Integer a) {
        return a == null ? "" : a.toString();
    }

    private static PplComdInterveneCvmDTO convertFromGroupItemDTO(VersionGroupItemResp.GroupItemDTO groupItemDTO,
            Map<Long, PplInnerProcessVersionDO> innerProcessVersionDOMap) {
        PplComdInterveneCvmDTO pplCvmImportExcelDTO = new PplComdInterveneCvmDTO();
        pplCvmImportExcelDTO.setCustomerTypeName(groupItemDTO.getCustomerTypeName());
        pplCvmImportExcelDTO.setCustomerUin(groupItemDTO.getCustomerUin());
        pplCvmImportExcelDTO.setCustomerShortName(groupItemDTO.getCustomerShortName());
        pplCvmImportExcelDTO.setDemandTypeName(groupItemDTO.getDemandTypeName());
        pplCvmImportExcelDTO.setDemandScene(groupItemDTO.getDemandScene());
        pplCvmImportExcelDTO.setProjectName(groupItemDTO.getProjectName());
        pplCvmImportExcelDTO.setBillType(groupItemDTO.getBillType());

        BigDecimal winRate = groupItemDTO.getWinRate();
        String winRateStr = winRate == null ? "" : "" + winRate.setScale(0, RoundingMode.HALF_UP).intValue() + "%";
        pplCvmImportExcelDTO.setWinRate(winRateStr);

        pplCvmImportExcelDTO.setBeginBuyDate(null2empty(groupItemDTO.getBeginBuyDate()));
        pplCvmImportExcelDTO.setEndBuyDate(null2empty(groupItemDTO.getEndBuyDate()));
        pplCvmImportExcelDTO.setBeginElasticDate(null2empty(groupItemDTO.getBeginElasticDate()));
        pplCvmImportExcelDTO.setEndElasticDate(null2empty(groupItemDTO.getEndElasticDate()));
        pplCvmImportExcelDTO.setNote(groupItemDTO.getNote());
        pplCvmImportExcelDTO.setRegionName(groupItemDTO.getRegionName());
        pplCvmImportExcelDTO.setIsStrongDesignateZone(groupItemDTO.getIsStrongDesignateZone() ? "是" : "否");
        pplCvmImportExcelDTO.setZoneName(groupItemDTO.getZoneName());
        pplCvmImportExcelDTO.setInstanceType(groupItemDTO.getInstanceType());
        pplCvmImportExcelDTO.setInstanceModel(groupItemDTO.getInstanceModel());
        pplCvmImportExcelDTO.setInstanceModelCpuCore(null2empty(groupItemDTO.getInstanceModelCoreNum()));
        pplCvmImportExcelDTO.setInstanceModelRam(null2empty(groupItemDTO.getInstanceModelRamNum()));
        pplCvmImportExcelDTO.setInstanceNum(groupItemDTO.getInstanceNum());
        pplCvmImportExcelDTO.setTotalCoreNum(
                groupItemDTO.getTotalCoreNum() == null ? 0 : groupItemDTO.getTotalCoreNum());
        boolean isAccept = Lang.isNotEmpty(groupItemDTO.getAlternativeInstanceType());
        pplCvmImportExcelDTO.setIsAcceptAlternative(isAccept ? "是" : "否");
        if (isAccept) {
            pplCvmImportExcelDTO.setAlternativeInstanceType(
                    Strings.join(";", groupItemDTO.getAlternativeInstanceType()));
        }
        pplCvmImportExcelDTO.setAffinityType(groupItemDTO.getAffinityType());
        Integer tmp = groupItemDTO.getAffinityValue() == null ? null : groupItemDTO.getAffinityValue().intValue();
        pplCvmImportExcelDTO.setAffinityValue(null2empty(tmp));
        pplCvmImportExcelDTO.setSystemDiskType(groupItemDTO.getSystemDiskType());
        pplCvmImportExcelDTO.setSystemDiskStorage(null2empty(groupItemDTO.getSystemDiskStorage()));
        pplCvmImportExcelDTO.setDataDiskType(groupItemDTO.getDataDiskType());
        pplCvmImportExcelDTO.setDataDiskStorage(null2empty(groupItemDTO.getDataDiskStorage()));
        pplCvmImportExcelDTO.setDataDiskNum(null2empty(groupItemDTO.getDataDiskNum()));
        pplCvmImportExcelDTO.setCbsIo(null2empty(groupItemDTO.getCbsIo()));

        pplCvmImportExcelDTO.setProjectName(groupItemDTO.getProjectName());
        pplCvmImportExcelDTO.setPplId(groupItemDTO.getPplId());
        pplCvmImportExcelDTO.setAppliedStatus(
                groupItemDTO.getStatus().equals(PplItemStatusEnum.APPLIED.getCode()) ? "已预约" : "未预约");
        if (groupItemDTO.getIsLock()) {
            pplCvmImportExcelDTO.setAppliedStatus("已锁定");
        }
        pplCvmImportExcelDTO.setApplyTotalCore(groupItemDTO.getApplyTotalCore());
        pplCvmImportExcelDTO.setApplyTotalCore2(groupItemDTO.getApplyTotalCore());
        pplCvmImportExcelDTO.setApplyInstanceNum(groupItemDTO.getApplyInstanceNum());
        pplCvmImportExcelDTO.setSourceTypeName(groupItemDTO.getSourceTypeName());
        pplCvmImportExcelDTO.setOccupyOthersPplIds(groupItemDTO.getOccupyOthersPplIds());
        pplCvmImportExcelDTO.setOccupiedNum(groupItemDTO.getOccupiedNum());
        pplCvmImportExcelDTO.setOccupyOthersNum(groupItemDTO.getOccupyOthersNum());
        pplCvmImportExcelDTO.setConsensusStatusName(groupItemDTO.getConsensusStatusName());
        pplCvmImportExcelDTO.setConsensusZoneName(groupItemDTO.getConsensusZoneName());
        pplCvmImportExcelDTO.setConsensusDemandDate(groupItemDTO.getConsensusDemandDate());
        pplCvmImportExcelDTO.setConsensusInstanceType(groupItemDTO.getConsensusInstanceType());

        pplCvmImportExcelDTO.setYunxiaoOrderId(groupItemDTO.getYunxiaoOrderId());
        pplCvmImportExcelDTO.setApplyLockCore(groupItemDTO.getApplyLockCore());
        pplCvmImportExcelDTO.setStockSupplyCore(groupItemDTO.getStockSupplyCore());

        // 设置导出的 关联审批版本信息
        PplInnerProcessVersionDO innerVersionDO = null;
        if (groupItemDTO.getInnerVersionId() != null && groupItemDTO.getInnerVersionId() != 0) {
            innerVersionDO = innerProcessVersionDOMap.get(groupItemDTO.getInnerVersionId());
        }
        pplCvmImportExcelDTO.setInnerVersionCode(
                innerVersionDO == null ? "" : innerVersionDO.getBeginDate().toString());
        if (!StringUtils.isEmpty(pplCvmImportExcelDTO.getInnerVersionCode())) {
            pplCvmImportExcelDTO.setSyncSource(
                    groupItemDTO.getIsExtendLastVersion() != null && groupItemDTO.getIsExtendLastVersion() ? "新版本同步"
                            : "继承上版");
        }
        if (groupItemDTO.getBeginBuyDate() != null) {
            pplCvmImportExcelDTO.setYearMonth(groupItemDTO.getBeginBuyDate().getYear() + "-" + (
                    groupItemDTO.getBeginBuyDate().getMonthValue() >= 10 ?
                            groupItemDTO.getBeginBuyDate().getMonthValue()
                            : "0" + groupItemDTO.getBeginBuyDate().getMonthValue()));
        }

//        String spikeName = GroupItemDTO.getSpikeName(groupItemDTO.getIsSpikeAfterComd());
        String spikeName = PplProjectTypeEnum.getNameByCode(groupItemDTO.getIsSpikeAfterComd());
        pplCvmImportExcelDTO.setIsSpikeName(spikeName);
        return pplCvmImportExcelDTO;
    }


    private <K> ExcelProperty getEP(ORMUtils.IGetter<K> fn) {
        return ORMUtils.getAnnotationByGetter(ExcelProperty.class, fn);
    }

    private <K> String getColName(ORMUtils.IGetter<K> fn) {
        return getEP(fn).value()[0];
    }

    private <K> int getColIndex(ORMUtils.IGetter<K> fn) {
        return getEP(fn).index() + 1;
    }

    private <K> PplItemImportRsp.ErrorMessage makeError(int raw, ORMUtils.IGetter<K> fn, String message) {
        return new PplItemImportRsp.ErrorMessage(raw, getColIndex(fn), getColName(fn), message);
    }

    /**
     * 只支持 String 的方法
     */
    public <K> PplItemImportRsp.ErrorMessage makeErrorIfBlank(int raw, ORMUtils.IGetter<K> fn) {

        Field declaredField = ORMUtils.getFiledByGetter(fn);
        Object value = DOInfoReader.getValue(declaredField, this);
        String colName = getColName(fn);
        if (value == null) {
            return makeError(raw, fn, colName + "为空");
        }
        if (value instanceof String) {
            value = StringUtils.trimWhitespace((String) value);
            if (Strings.isBlank((String) value)) {
                return makeError(raw, fn, colName + "为空");
            }
        } else {
            throw Lang.makeThrow("不支持类型");
        }
        return null;
    }

    /**
     * 首先会判断空值, 如果有空值直接返回空的 error
     *
     * 值包不含在 all 中， 返回 error
     *
     * 包含返回 null
     */
    public <K> PplItemImportRsp.ErrorMessage makeErrorIfNotContain(int raw, ORMUtils.IGetter<K> fn, List<String> all) {

        PplItemImportRsp.ErrorMessage errorMessage = makeErrorIfBlank(raw, fn);
        if (errorMessage != null) {
            return errorMessage;
        }

        Field declaredField = ORMUtils.getFiledByGetter(fn);
        Object value = DOInfoReader.getValue(declaredField, this);
        if (value instanceof String) {
            value = StringUtils.trimWhitespace((String) value);
            if (!all.contains((String) value)) {
                String colName = getColName(fn);
                String msg = colName + "出错:  [" + value + "] 不在下面范围 " + all + " 中";
                if (Lang.isEmpty(all)) {
                    msg = colName + "出错:  不可以填 [" + value + "] ";
                }
                return makeError(raw, fn, msg);
            }
        } else {
            throw Lang.makeThrow("不支持类型");
        }
        return null;
    }

    /**
     * 首先会判断空值, 如果有空值直接返回空的 error
     *
     * 值包不含在 all 中， 返回 error
     *
     * 包含返回 null
     */
    public <K> PplItemImportRsp.ErrorMessage makeErrorIfNotContain(int raw, ORMUtils.IGetter<K> fn, List<String> all,
            String rangeMsg) {

        PplItemImportRsp.ErrorMessage errorMessage = makeErrorIfBlank(raw, fn);
        if (errorMessage != null) {
            return errorMessage;
        }

        Field declaredField = ORMUtils.getFiledByGetter(fn);
        Object value = DOInfoReader.getValue(declaredField, this);
        if (!all.contains(value)) {
            String colName = getColName(fn);
            String msg = colName + "出错:  [" + value + "], " + rangeMsg;
            if (Lang.isEmpty(all)) {
                msg = colName + "出错:  不可以填 [" + value + "] ";
            }
            return makeError(raw, PplComdInterveneCvmDTO::getDemandTypeName, msg);
        }
        return null;
    }
}
