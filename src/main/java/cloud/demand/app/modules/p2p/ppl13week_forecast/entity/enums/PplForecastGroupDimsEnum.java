package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums;

import lombok.Getter;

import java.util.Objects;
import org.nutz.lang.Lang;

/**
 * 用于标识不同的执行量处理方式
 */
@Getter
public enum PplForecastGroupDimsEnum {

    /**
     * 保留客户维度，机型规格+可用区，这是最细粒度的，长尾的就是用该最细粒度
     */
    UIN_INSTANCE_MODEL_ZONE_NAME("UIN_INSTANCE_MODEL_ZONE_NAME", "保留客户维度(机型规格+可用区)"),

    /**
     * 保留客户维度，机型大类+可用区，这个就等于是行业数据看板的不去重方式
     */
    UIN_INSTANCE_TYPE_ZONE_NAME("UIN_INSTANCE_TYPE_ZONE_NAME", "保留客户维度(机型大类+可用区)"),

    /**
     * 不保留客户维度，机型规格+可用区，这个就等于是行业数据看板的去重方式
     */
    NO_UIN_INSTANCE_TYPE_ZONE_NAME("NO_UIN_INSTANCE_TYPE_ZONE_NAME", "不保留客户维度(机型大类+可用区)"),

    ;

    final private String code;
    final private String name;

    PplForecastGroupDimsEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getSqlColumnJoinByComma(PplForecastGroupDimsEnum groupDimsEnum) {
        if (groupDimsEnum == PplForecastGroupDimsEnum.UIN_INSTANCE_MODEL_ZONE_NAME) {
            return "uin,zone_name,instance_model";
        } else if (groupDimsEnum == PplForecastGroupDimsEnum.UIN_INSTANCE_TYPE_ZONE_NAME) {
            return "uin,zone_name,instance_type";
        } else if (groupDimsEnum == PplForecastGroupDimsEnum.NO_UIN_INSTANCE_TYPE_ZONE_NAME) {
            return "zone_name,instance_type";
        }
        throw Lang.makeThrow("未找到： ", groupDimsEnum);
    }



    public static PplForecastGroupDimsEnum getByCode(String code) {
        for (PplForecastGroupDimsEnum e : PplForecastGroupDimsEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        PplForecastGroupDimsEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}