package cloud.demand.app.modules.p2p.longterm.key_accessor.v1_key_getter;

import com.pugwoo.wooutils.string.StringTools;

public interface LongtermItemTimeDimGK extends GroupKey {

    @Override
    default String groupKey() {
        return StringTools.join("@", getTimeUnit(), getDemandYear(), getDemandQuarter(), getDemandMonth());
    }

    /**
     * 时间单位
     */
    String getTimeUnit();

    /**
     * 需求年
     */
    Integer getDemandYear();

    /**
     * 需求季度
     */
    Integer getDemandQuarter();

    /**
     * 需求月
     */
    Integer getDemandMonth();

}
