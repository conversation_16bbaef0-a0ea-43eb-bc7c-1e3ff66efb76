package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums;

import lombok.Getter;
import org.nutz.lang.Strings;

/**
 * <AUTHOR>
 */

@Getter
public enum PplForecastCoreTypeEnum {


    /**
     * new
     */
    BIG("<PERSON><PERSON>", "大核心"),
    SMALL("<PERSON><PERSON><PERSON>", "小核心");

    /**
     *  code
     */
    String type;
    /**
     *  名称
     */
    String typeName;

    PplForecastCoreTypeEnum(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public static PplForecastCoreTypeEnum getByName(String typeName) {
        for (PplForecastCoreTypeEnum value : PplForecastCoreTypeEnum.values()) {

            if (Strings.equals(value.typeName, typeName)) {
                return value;
            }
        }
        return null;
    }

    public static String getNameByType(String type) {
        for (PplForecastCoreTypeEnum value : PplForecastCoreTypeEnum.values()) {

            if (Strings.equals(value.type, type)) {
                return value.getTypeName();
            }
        }
        return "";
    }
}
