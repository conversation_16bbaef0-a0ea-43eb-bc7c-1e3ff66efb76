package cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * 预约单的资源准备进度
 */
@Data
@Table("dwd_yunxiao_apply_order_detail_cf")
public class StdYunXiaoMatchSummaryDTO {

    /**
     * 预约单ID, 接口返回的预约单ID, 例：order-65387bdcd8<br/>Column: [order_id]
     */
    @Column(value = "order_id")
    private String orderId;

    /**
     * 状态 ，接口返回的状态 , 例：待CBS审批<br/>Column: [status]
     */
    @Column(value = "status")
    private String status;

    /**
     * 资源已准备数, 云霄预约单维度
     */
    @Column(value = "order_completed_cpu_count")
    private Integer orderCompletedCpuCount;

    /**
     * 完成进度, 云霄预约单维度
     */
    @Column(value = "order_completed_rate")
    private Double orderCompletedRate;

    /**
     * 总需求, 云霄预约单维度
     */
    @Column(value = "order_total_cpu_count")
    private Integer orderTotalCpuCount;

}
