package cloud.demand.app.modules.p2p.industry_demand.enums;

import java.util.Objects;
import lombok.Getter;

@Getter
public enum IndustryDemandBigDataInstanceFamilyEnum {

    UNKNOWN("UNKNOWN", "未知"),

    STANDARD("STANDARD", "标准型"),

    MEMORY("MEMORY", "内存型"),

    BIG_DATA("BIG_DATA", "大数据型"),

    HEI_SHI("HEI_SHI", "黑石"),

    GPU("GPU", "GPU"),

    IO("IO", "高IO型");

    final private String code;
    final private String name;

    IndustryDemandBigDataInstanceFamilyEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static IndustryDemandBigDataInstanceFamilyEnum getByCode(String code) {
        for (IndustryDemandBigDataInstanceFamilyEnum e : IndustryDemandBigDataInstanceFamilyEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }

    public static IndustryDemandBigDataInstanceFamilyEnum getByName(String name) {
        for (IndustryDemandBigDataInstanceFamilyEnum e : IndustryDemandBigDataInstanceFamilyEnum.values()) {
            if (Objects.equals(name, e.getName())) {
                return e;
            }
        }
        return UNKNOWN;
    }

    public static String getNameByCode(String code) {
        IndustryDemandBigDataInstanceFamilyEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}