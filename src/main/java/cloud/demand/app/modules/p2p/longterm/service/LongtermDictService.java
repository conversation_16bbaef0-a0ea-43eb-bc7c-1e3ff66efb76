package cloud.demand.app.modules.p2p.longterm.service;

import cloud.demand.app.modules.soe.entitiy.dict.SoeRegionNameCountryDO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 中长期的字典接口服务, 考虑到未来的机型和城市不一定在现在的配置表中, 通过七彩石配置补充
 */
public interface LongtermDictService {

    /**
     * 找国家信息
     * @return map
     */
    Map<String, SoeRegionNameCountryDO> getRegionNameInfoMap();

    /**
     * 获取境内 境外的城市有哪些
     * @param regionName
     * @return
     */
    Map<String, List<String>> getCustomhouseTitleToRegionName(Set<String> regionName);

    /**
     * 获取地域和可用区的对应关系
     * @return ret
     */
    Map<String, List<String>> queryRegionNameToZoneName();

    /**
     * product 到 机型住的映射
     * @return
     */
    Map<String, List<String>> queryProductToInstanceFamily();

    /**
     * 获取实例族 到 实例大类的映射关系
     * @return ret
     */
    Map<String,List<String>> queryInstanceFamilyToInstanceType();


    /**
     * 查询GPU卡的字典接口信息
     * @return list
     */
    List<String> queryGpuType();

    /**
     * 查询GPU卡的字典接口信息
     * @return list
     */
    Map<String,List<String>> queryHistoryControl();
}
