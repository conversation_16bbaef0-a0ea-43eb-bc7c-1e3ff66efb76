package cloud.demand.app.modules.p2p.longterm.entity;

import com.sun.jna.platform.win32.WinDef.LONG;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("long_term_todo_record")
public class LongTermTodoRecordDO extends BaseDO {

    /** 行业版本id<br/>Column: [version_id] */
    @Column(value = "version_id")
    private Long versionId;

    /** 分组id<br/>Column: [version_group_id] */
    @Column(value = "version_group_id")
    private Long versionGroupId;

    /** nodecode节点<br/>Column: [node_code] */
    @Column(value = "node_code")
    private String nodeCode;

    /** 行业<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 业务分组<br/>Column: [biz_group] */
    @Column(value = "biz_group")
    private String bizGroup;

    /** todo的taskOrderId<br/>Column: [biz_key] */
    @Column(value = "biz_key")
    private String bizKey;

    /** 接收人<br/>Column: [accept_user] */
    @Column(value = "accept_user")
    private String acceptUser;

    /** 具体推送内容<br/>Column: [detail] */
    @Column(value = "detail")
    private String detail;

    /**
     *
     *     0-待推送 1-待审批 2-已完成 3-回调异常
     *
     * 推送状态<br/>Column: [push_status]
     * */
    @Column(value = "push_status")
    private Integer pushStatus;

    /** 错误日志<br/>Column: [error_message] */
    @Column(value = "error_message")
    private String errorMessage;

}