package cloud.demand.app.modules.p2p.industry_demand.controller;

import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.modules.p2p.industry_demand.Constant;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.IndustryDemandApproveDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.IndustryProductMapStatusDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.KeyWordParam;
import cloud.demand.app.modules.p2p.industry_demand.dto.ListIndustryDemandGroupReq;
import cloud.demand.app.modules.p2p.industry_demand.dto.QueryDemandInfoReq;
import cloud.demand.app.modules.p2p.industry_demand.dto.QueryDemandSummaryInfoReq;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionDto;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionReq;
import cloud.demand.app.modules.p2p.industry_demand.dto.ValidateResultDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.product.BaseProductDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.product.BigDataDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.product.CbsDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.product.CvmDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.product.GpuDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.product.MetalDTO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandFlowDictDO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandVersionDO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandVersionGroupDO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandVersionGroupWithVersionVO;
import cloud.demand.app.modules.p2p.industry_demand.entity.SatisfactionDTO;
import cloud.demand.app.modules.p2p.industry_demand.enums.GroupTypeEnum;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandDictService;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandService;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandVersionService;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.industry_demand.service.TodoService;
import cloud.demand.app.modules.p2p.industry_demand.utils.ExcelUtil;
import cloud.demand.app.modules.p2p.industry_demand.utils.UnitUtils;
import cloud.demand.app.web.model.common.DownloadBean;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.DBHelper;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;
import yunti.boot.security.CurrentUser;
import yunti.boot.security.TofUser;
import yunti.boot.util.YuntiUtils;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@JsonrpcController("/industry/annual/api")
@Slf4j
public class IndustryDemandController {

    @Resource
    IndustryDemandVersionService industryDemandVersionService;
    @Resource
    IndustryDemandService industryDemandService;

    @Resource
    PermissionService permissionService;

    @Resource
    TodoService todoService;

    @Resource
    IndustryDemandDictService industryDemandDictService;

    @Resource
    DBHelper demandDBHelper;

    @RequestMapping
    public Object decodeExcel(@RequestParam("file") MultipartFile file,
            @RequestParam(name = "product") String product,
            @RequestParam(name = "version") String version) {
        IndustryDemandVersionDO versionDO = industryDemandVersionService.getVersion(version);
        if (versionDO == null) {
            throw new BizException("找不到对应的需求版本");
        }
        List<YearMonth> yearMonthList = DateUtils.listYearMonth(versionDO.getForecastFromYear(),
                versionDO.getForecastFromMonth(),
                versionDO.getForecastToYear(), versionDO.getForecastToMonth());
        Class<? extends BaseProductDTO> tClass;
        switch (product) {
            case "CVM":
                tClass = CvmDTO.class;
                break;
            case "CBS":
                tClass = CbsDTO.class;
                break;
            case "GPU":
                tClass = GpuDTO.class;
                break;
            case "裸金属":
                tClass = MetalDTO.class;
                break;
            case "大数据":
                tClass = BigDataDTO.class;
                break;
            default:
                throw new BizException("未支持该产品的EXCEL解析:" + product);
        }
        Map<String, Object> context = new HashMap<>();
        context.put("regionLandMap", industryDemandDictService.regionName2RegionDTOMap());
        return ExcelUtil.decodeExcel(file, tClass, yearMonthList, context);
    }

    @RequestMapping
    public Object save(@CurrentUser TofUser user, @JsonrpcParam @Valid SaveDTO saveDTO,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        IndustryDemandVersionDO versionDO = industryDemandVersionService.getVersion(saveDTO.version);
        if (versionDO == null) {
            throw new BizException("需求版本不可用");
        }
        List<YearMonth> yearMonthList = DateUtils.listYearMonth(versionDO.getForecastFromYear(),
                versionDO.getForecastFromMonth(),
                versionDO.getForecastToYear(), versionDO.getForecastToMonth());
        Class<? extends BaseProductDTO> tClass;
        switch (saveDTO.product) {
            case "CVM":
                tClass = CvmDTO.class;
                break;
            case "CBS":
                tClass = CbsDTO.class;
                break;
            case "GPU":
                tClass = GpuDTO.class;
                break;
            case "裸金属":
                tClass = MetalDTO.class;
                break;
            case "大数据":
                tClass = BigDataDTO.class;
                break;
            default:
                throw new BizException("未支持该产品的录入:" + saveDTO.product);
        }

        //"INDUSTRY_REVENUE_INPUT") || item.equals("INDUSTRY_RESOURCE_INPUT"
        String role1 = todoService.getApproveFlowStep(GroupTypeEnum.getBySp(saveDTO.industry),
                industryDemandService.HY_BSC_INPUT_STEP).getAuthRole();
        String role2 = todoService.getApproveFlowStep(GroupTypeEnum.getBySp(saveDTO.industry),
                industryDemandService.HY_RESOURCE_INPUT_STEP).getAuthRole();

        UserPermissionReq permReq = new UserPermissionReq();
        permReq.setUsers(Lists.newArrayList(user.getUsername()));
        permReq.setRoles(Lists.newArrayList(role1, role2));
        UserPermissionDto perm = permissionService.getMergePermission(permReq);
        if (!IndustryDemandAuthRoleEnum.ADMIN.getCode().equals(perm.getRole())) {
            if (!perm.getProduct().contains(saveDTO.getProduct())) {
                throw new BizException("没有该产品权限");
            }
            if (!perm.getIndustry().contains(saveDTO.getIndustry())) {
                throw new BizException("没有该行业权限");
            }
        }
        ValidateResultDTO result = industryDemandService.overrideSave(versionDO, saveDTO.industry, saveDTO.product,
                saveDTO.annualIncomeTarget, saveDTO.lastAnnualIncomeTarget, saveDTO.annualIncomeIncreaseRate,
                saveDTO.annualIncomeTargetRate,
                ExcelUtil.map2DTO(saveDTO.data, yearMonthList, tClass),
                tClass);
        if (!CollectionUtils.isEmpty(result.getItems())) {
            // 2022-10-28 +2
            result.getItems().forEach(i -> {
                i.setIndex(i.getIndex() + 2);
            });
        }
        return result;
    }

    @RequestMapping
    public Object saveAndSubmit(@CurrentUser TofUser user, @JsonrpcParam @Valid SaveDTO saveDTO,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);

        ValidateResultDTO rs = (ValidateResultDTO) save(user, saveDTO, bindingResult);
        //todo流程 流转
        if (!rs.isHasError()) {
            IndustryDemandVersionGroupDO demandGroup = demandDBHelper.getOne(IndustryDemandVersionGroupDO.class,
                    "where id = ? ", rs.getGroupId());

            val flow = todoService.getApproveFlowStep(GroupTypeEnum.getBySp(demandGroup.getIndustry()),
                    demandGroup.getStatus());

            IndustryDemandApproveDTO approveDTO = new IndustryDemandApproveDTO();
            approveDTO.setDemandGroup(demandGroup);
            approveDTO.setStep(flow);
            approveDTO.setApprove(user.getUsername());
            approveDTO.setPass(true);
            approveDTO.setMemo(saveDTO.memo);
            todoService.doIndustryDemandApprove(approveDTO);

        }
        return rs;
    }

    /**
     * 行业录入页面- 行业-产品录入状态列表
     *
     * @param user
     * @return
     */
    @RequestMapping
    public Object queryCurrentVersionIndustryProductMapStatus(@CurrentUser TofUser user) {

        List<IndustryProductMapStatusDTO> result = industryDemandService.queryCurrentVersionIndustryProductMapStatus();

        List<String> roles = Lists.newArrayList(IndustryDemandAuthRoleEnum.ADMIN.getCode());

        //权限控制 硬编码 流程节点,INDUSTRY_REVENUE_INPUT，INDUSTRY_RESOURCE_INPUT
        result.forEach(item -> {
            if (item.getStep().equals(industryDemandService.HY_BSC_INPUT_STEP) || item.getStep().equals(
                    industryDemandService.HY_RESOURCE_INPUT_STEP)) {
                //处于编辑状态-需要设置是否可以编辑的属性

                IndustryDemandFlowDictDO step = todoService.getApproveFlowStep(GroupTypeEnum.HY, item.getStep());

                KeyWordParam param = new KeyWordParam(item.getIndustry(), item.getProduct(),null);
                List<String> auths = Splitter.on(";")
                        .splitToList(permissionService.getUserByRole(step.getAuthRole(), param));
                roles.add(step.getAuthRole());
                if (auths.contains(user.getUsername())) {
                    item.setHasAuth(true);
                }
            }
        });

        UserPermissionReq permReq = new UserPermissionReq();
        permReq.setUsers(Lists.newArrayList(user.getUsername()));
        permReq.setRoles(roles.stream().distinct().collect(Collectors.toList()));
        UserPermissionDto userPermissionDto = permissionService.getMergePermission(permReq);

        if (userPermissionDto == null || CollectionUtils.isEmpty(userPermissionDto.getIndustry())) {
            throw new BizException("没有权限");
        }
        Set<String> industry = new HashSet<>(userPermissionDto.getIndustry());
        result = result.stream()
                .filter(e -> industry.contains(e.getIndustry()))
                .collect(Collectors.toList());
        return result;
    }

    @RequestMapping
    public Object listGroup(@CurrentUser TofUser user, @JsonrpcParam @Valid ListIndustryDemandGroupReq req,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        //权限
        UserPermissionReq permReq = new UserPermissionReq();
        permReq.setUsers(Lists.newArrayList(user.getUsername()));
        UserPermissionDto perm = permissionService.getMergePermission(permReq);
        if (!IndustryDemandAuthRoleEnum.ADMIN.getCode().equals(perm.getRole())) {
            if (CollectionUtils.isEmpty(req.getProduct())) {
                req.setProduct(perm.getProduct());
            } else {
                req.getProduct().removeIf(s -> !perm.getProduct().contains(s));
            }
            if (CollectionUtils.isEmpty(req.getIndustry())) {
                req.setIndustry(perm.getIndustry());
            } else {
                req.getIndustry().removeIf(s -> !perm.getIndustry().contains(s));
            }
        }
        val ls = industryDemandService.listGroup(req);
        //fix status
        fixStatusDisplay(ls);
        return ls.stream()
                .map(ListGroupItem::toItem)
                .collect(Collectors.toList());
    }

    @RequestMapping
    public Object getUnit(@JsonrpcParam @Valid GetUnitDTO dto, BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        return ImmutableMap.of("unit", UnitUtils.getUnit(dto.product, dto.instanceFamily));
    }

    @RequestMapping
    public Object queryDemandInfoReq(@CurrentUser TofUser user, @JsonrpcParam QueryDemandInfoReq req) {
        return industryDemandService.queryDemandInfoReq(req, user.getUsername());
    }

    @RequestMapping
    public DownloadBean exportDemandInfoExcel(@CurrentUser TofUser user, @JsonrpcParam QueryDemandInfoReq req) {
        //权限
        UserPermissionReq permReq = new UserPermissionReq();
        permReq.setUsers(Lists.newArrayList(user.getUsername()));
        UserPermissionDto perm = permissionService.getMergePermission(permReq);
        if (!IndustryDemandAuthRoleEnum.ADMIN.getCode().equals(perm.getRole())) {
            if (!perm.getProduct().contains(req.getProduct())) {
                throw new BizException("没有该产品权限");
            }
            if (!perm.getIndustry().contains(req.getIndustry())) {
                throw new BizException("没有该行业权限");
            }
        }

        FileNameAndBytesDTO fileNameAndBytesDTO = industryDemandService.exportDemandDetail(req);
        return new DownloadBean(fileNameAndBytesDTO.getFileName(), fileNameAndBytesDTO.getBytes());
    }

    @RequestMapping
    public DownloadBean exportTemplate(@JsonrpcParam @Valid GetUnitDTO req, BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        FileNameAndBytesDTO fileNameAndBytesDTO = industryDemandService.exportDemandDetail(req.product);
        return new DownloadBean(fileNameAndBytesDTO.getFileName(), fileNameAndBytesDTO.getBytes());
    }

    @RequestMapping
    public Object queryDemandSummaryInfoReq(@CurrentUser TofUser user, @JsonrpcParam QueryDemandSummaryInfoReq req) {
        return industryDemandService.queryDemandSummaryInfoReq(req, user.getUsername());
    }

    @RequestMapping
    public Object inputSatisfaction(@CurrentUser TofUser user, @JsonrpcParam InputSatisfactionWarp dto,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        industryDemandService.inputSatisfaction(dto.getData(), dto.search, user.getUsername());
        return true;
    }

    @RequestMapping
    public Object listThisGroupCustomerNames(@JsonrpcParam QueryDemandInfoReq req) {
        return industryDemandService.listThisGroupCustomerNames(req);
    }

    public void fixStatusDisplay(List<IndustryDemandVersionGroupWithVersionVO> ls) {
        ls.forEach(item -> {
            val step = demandDBHelper.getOne(IndustryDemandFlowDictDO.class, "where flow_step=?", item.getStatus());
            item.setStatus(step != null ? step.getFlowStepName() : item.getStatus());
        });

    }

    @Data
    static class InputSatisfactionWarp {

        private QueryDemandInfoReq search;

        private List<SatisfactionDTO> data;
    }

    @Data
    static class GetUnitDTO {

        @NotEmpty(message = "产品不能为空")
        private String product;
        private String instanceFamily;
    }

    @Data
    static class ListGroupItem {

        private Long groupId;
        private String demandVersion;
        private String versionName;
        private String industry;
        private String product;
        private String submitUser;
        private String status;
        private Integer forecastFromYear;
        private Integer forecastFromMonth;
        private Integer forecastToYear;
        private Integer forecastToMonth;

        public static ListGroupItem toItem(IndustryDemandVersionGroupWithVersionVO vo) {
            ListGroupItem listGroupItem = new ListGroupItem();
            listGroupItem.setGroupId(vo.getId());
            listGroupItem.setDemandVersion(vo.getDemandVersion());
            listGroupItem.setVersionName(vo.getVersionDO().getName());
            listGroupItem.setIndustry(vo.getIndustry());
            listGroupItem.setProduct(vo.getProduct());
            listGroupItem.setSubmitUser(StringUtils.isEmpty(vo.getSubmitUser())
                    ? Constant.SYSTEM_INIT_CREATOR : vo.getSubmitUser());
            listGroupItem.setStatus(vo.getStatus());
            if (vo.getVersionDO() != null) {
                IndustryDemandVersionDO version = vo.getVersionDO();
                listGroupItem.setForecastFromMonth(version.getForecastFromMonth());
                listGroupItem.setForecastFromYear(version.getForecastFromYear());
                listGroupItem.setForecastToMonth(version.getForecastToMonth());
                listGroupItem.setForecastToYear(version.getForecastToYear());
            }
            return listGroupItem;
        }
    }

    @Data
    static class SaveDTO {

        @NotEmpty(message = "版本不能为空")
        private String version;
        @NotEmpty(message = "行业不能为空")
        private String industry;
        @NotEmpty(message = "产品不能为空")
        private String product;
        // 数据允许为空
        private List<Map<String, Object>> data;
        @NotNull(message = "2023年BSC收入目标不能为空")
        private Long annualIncomeTarget;
        private Long lastAnnualIncomeTarget;
        @NotEmpty(message = "BSC目标完成率不能为空")
        private String annualIncomeTargetRate;
        @NotEmpty(message = "2023年相比2022年增速不能为空")
        private String annualIncomeIncreaseRate;

        private String memo;
    }
}
