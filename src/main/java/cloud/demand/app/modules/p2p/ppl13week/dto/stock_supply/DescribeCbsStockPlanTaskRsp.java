package cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 */


@Data
@EqualsAndHashCode(callSuper = true)
public class DescribeCbsStockPlanTaskRsp extends CbsStockPlanCommenReq {

    @JsonAlias("Response")
    Response response;

    @Data
    public static class Response {

        @JsonAlias("Status")
        String status;
        @JsonAlias("Result")
        List<Result> result;
        @JsonAlias("RequestId")
        String requestId;
        @JsonAlias("Error")
        Error error;
    }


    @Data
    public static class Error {

        @JsonAlias("Code")
        String code;
        @JsonAlias("Message")
        String message;

    }


    @Data
    public static class Result {

        Long id;
        @JsonAlias("TaskId")
        String taskId;
        @JsonAlias("MatchType")
        String matchType;
        @JsonAlias("DiskTotalSize")
        Integer diskTotalSize;
        @JsonAlias("DiskType")
        String diskType;
        @JsonAlias("MatchDiskType")
        String matchDiskType;
        @JsonAlias("Label")
        String label;
        @JsonAlias("HostType")
        String hostType;
        @JsonAlias("HostCount")
        BigDecimal hostCount;
        @JsonAlias("Region")
        String region;
        @JsonAlias("Zone")
        String zone;
        @JsonAlias("Remark")
        String remark;
    }
}
