package cloud.demand.app.modules.p2p.longterm.key_accessor;

import cloud.demand.app.modules.p2p.longterm.key_accessor.v1_key_getter.LongtermItemTimeDimGK;

public interface LongtermItemTimeDimAccessor extends LongtermItemTimeDimGK {

    /**
     * 时间单位
     */
    void setTimeUnit(String timeUnit);

    /**
     * 需求年
     */
    void setDemandYear(Integer demandYear);

    /**
     * 需求季度
     */
    void setDemandQuarter(Integer demandQuarter);

    /**
     * 需求月
     */
    void setDemandMonth(Integer demandMonth);


    static void copy(LongtermItemTimeDimAccessor source, LongtermItemTimeDimAccessor target) {
        target.setTimeUnit(source.getTimeUnit());
        target.setDemandYear(source.getDemandYear());
        target.setDemandQuarter(source.getDemandQuarter());
        target.setDemandMonth(source.getDemandMonth());
    }
}
