package cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QueryPreDeductOrderListReq {

    private String orderId;
    private Boolean orderCreated;
    private String creator;
    private String appId;
    private String uin;
    private String region;
    private List<String> zone;
    private String appName;
    private List<String> appRole;
    private List<String> instanceFamily;
    private List<String> deviceClass;
    private List<String> organizationName;
    private List<String> instanceType;
    private List<String> disasterRecoverGroupIdList;
    private List<Integer> reservationFormId;
    private List<String> status;
    private List<String> instanceCategory;
    private Date startTime;
    private Date endTime;
    private Date destroyStartTime;
    private Date destroyEndTime;
    private Boolean keepReserved;
    private Boolean migrateReserved;
    private List<String> orderIds;
    private Long appIds;
    private List<String> uins;
    private Boolean mainZone;
    private Boolean mainInstanceFamily;
    private Boolean critical;
    private Integer createdCpuGreaterThan;
    private List<Sort> sort;
    private Integer pageNumber = 1;
    private Integer pageSize = 20;


    @Data
    @ToString
    class Sort {

        private String property;
        private String direction;

    }

}
