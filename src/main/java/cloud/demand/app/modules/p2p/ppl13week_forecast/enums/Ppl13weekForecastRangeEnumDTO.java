package cloud.demand.app.modules.p2p.ppl13week_forecast.enums;

import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskDO;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class Ppl13weekForecastRangeEnumDTO {

    private Ppl13weekForecastBillTypeEnum billTypeEnum;
    private Ppl13weekForecastResourcePoolEnum resourcePool;
    private Ppl13weekForecastCustomerScopeEnum customerScope;
    private Ppl13weekForecastSourceTypeEnum sourceTypeEnum;
    private Ppl13weekForecastSourceFromEnum sourceFromEnum;

    public static Ppl13weekForecastRangeEnumDTO newFrom(PplForecastPredictTaskDO task) {
        Ppl13weekForecastRangeEnumDTO ret = new Ppl13weekForecastRangeEnumDTO();
        ret.setBillTypeEnum(Ppl13weekForecastBillTypeEnum.getByCode(task.getBillType()));
        ret.setResourcePool(Ppl13weekForecastResourcePoolEnum.getByCode(task.getResourcePool()));
        ret.setCustomerScope(Ppl13weekForecastCustomerScopeEnum.getByCode(task.getCustomerScope()));
        ret.setSourceTypeEnum(Ppl13weekForecastSourceTypeEnum.getByCode(task.getSourceType()));
        ret.setSourceFromEnum(Ppl13weekForecastSourceFromEnum.getByCode(task.getSourceFrom()));
        return ret;
    }


    public void fillToTaskDO(@NotNull PplForecastPredictTaskDO taskDO) {
        taskDO.setBillType(this.getBillTypeEnum().getCode());
        taskDO.setResourcePool(this.getResourcePool().getCode());
        taskDO.setCustomerScope(this.getCustomerScope().getCode());
        taskDO.setSourceType(this.getSourceTypeEnum().getCode());
        taskDO.setSourceFrom(this.getSourceFromEnum().getCode());
    }

    public Ppl13weekForecastResourcePoolEnum getResourcePool() {
        return resourcePool == null ? Ppl13weekForecastResourcePoolEnum.PUBLIC : resourcePool;
    }

    public Ppl13weekForecastSourceTypeEnum getSourceTypeEnum() {
        return sourceTypeEnum == null ? Ppl13weekForecastSourceTypeEnum.INDUSTRY_INNER : sourceTypeEnum;
    }

    public Ppl13weekForecastSourceFromEnum getSourceFromEnum() {
        return sourceFromEnum == null ? Ppl13weekForecastSourceFromEnum.MONTH : sourceFromEnum;
    }

    public Ppl13weekForecastCustomerScopeEnum getCustomerScope() {
        return customerScope == null ? Ppl13weekForecastCustomerScopeEnum.ALL : customerScope;
    }

    public Ppl13weekForecastBillTypeEnum getBillTypeEnum() {
        return billTypeEnum == null ? Ppl13weekForecastBillTypeEnum.ALL : billTypeEnum;
    }

}
