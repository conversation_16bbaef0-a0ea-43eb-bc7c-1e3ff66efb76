package cloud.demand.app.modules.p2p.longterm.service.impl;

import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.longterm.dto.LongTermUnifiedVersionDTO;
import cloud.demand.app.modules.p2p.longterm.entity.LongTermUnifiedVersionDO.DemandYearMonth;
import cloud.demand.app.modules.p2p.longterm.entity.LongTermUnifiedVersionEventDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupStatusEnum;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionStatusEnum;
import cloud.demand.app.modules.p2p.longterm.service.LongTermVersionService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermVersionApprovalService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermVersionGroupService;
import cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version.UnificatedVersionDTO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LongTermVersionServiceImpl implements LongTermVersionService {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DictService dictService;
    @Resource
    private LongtermVersionGroupService longtermVersionGroupService;
    @Resource
    private LongtermVersionApprovalService longtermVersionApprovalService;

    @Autowired
    private Alert alert;

    /**
     * 此方法无需开启事物 ，调用层统一事物
     * @param versionDTO
     */
    @Override
    public void createLongTermVersion(LongTermUnifiedVersionDTO versionDTO) {

        LongtermVersionDO longtermVersionDO = new LongtermVersionDO();
        longtermVersionDO.setVersionCode(versionDTO.getVersionCode());
        longtermVersionDO.setCreator(versionDTO.getCreateUser());
        longtermVersionDO.setVersionYearMonth(DateUtils.chineseYearMonthToEnglishYearMonth(versionDTO.getVersionYearMonth()));
        versionDTO.parseJsonDemand();
        DemandYearMonth industryLongTermDemand = versionDTO.getIndustryLongTermDemand();
        DemandYearMonth extraYearMonth = versionDTO.getExtraDemand();
        longtermVersionDO.setDemandBeginYear(industryLongTermDemand.getBeginYear());
        longtermVersionDO.setDemandBeginMonth(industryLongTermDemand.getBeginMonth());
        longtermVersionDO.setDemandEndYear(industryLongTermDemand.getEndYear());
        longtermVersionDO.setDemandEndMonth(industryLongTermDemand.getEndMonth());
        longtermVersionDO.setExtraBeginYear(extraYearMonth.getBeginYear());
        longtermVersionDO.setExtraBeginMonth(extraYearMonth.getBeginMonth());
        longtermVersionDO.setExtraEndYear(extraYearMonth.getEndYear());
        longtermVersionDO.setExtraEndMonth(extraYearMonth.getEndMonth());
        longtermVersionDO.setExtraIndustry(versionDTO.getExtraIndustry());
        longtermVersionDO.setStatus(LongtermVersionStatusEnum.CREATED.getCode());


        if (StringUtils.isNotBlank(versionDTO.getRelevantPplVersion())){
            UnificatedVersionDTO pplVersionDO = demandDBHelper.getOne(UnificatedVersionDTO.class, "where version_code = ?",
                    versionDTO.getRelevantPplVersion());
            if (pplVersionDO == null){
                throw new BizException("不存在versionCode为" + versionDTO.getRelevantPplVersion() + "的13周版本");
            }
            pplVersionDO.parseJsonDemand();
            longtermVersionDO.setPpl13weekVersionCode(versionDTO.getRelevantPplVersion());

            String startYearMonth = pplVersionDO.getPplDemand().getBeginYear() + "-" + DateUtils.fillZeroMonthOrDay(pplVersionDO.getPplDemand().getBeginMonth());
            String endYearMonth = pplVersionDO.getPplDemand().getEndYear() + "-" + DateUtils.fillZeroMonthOrDay(pplVersionDO.getPplDemand().getEndMonth());
            longtermVersionDO.setPpl13weekStartYearMonth(startYearMonth);
            longtermVersionDO.setPpl13weekEndYearMonth(endYearMonth);
        }

        demandDBHelper.insert(longtermVersionDO);

    }

    @Override
    public void updateLongTermVersion(LongTermUnifiedVersionDTO versionDTO) {
        LongtermVersionDO longtermVersionDO = demandDBHelper.getOne(LongtermVersionDO.class, "where version_code = ?",
                versionDTO.getVersionCode());
        DemandYearMonth industryLongTermDemand = versionDTO.getIndustryLongTermDemand();
        DemandYearMonth extraYearMonth = versionDTO.getExtraDemand();
        longtermVersionDO.setDemandBeginYear(industryLongTermDemand.getBeginYear());
        longtermVersionDO.setDemandBeginMonth(industryLongTermDemand.getBeginMonth());
        longtermVersionDO.setDemandEndYear(industryLongTermDemand.getEndYear());
        longtermVersionDO.setDemandEndMonth(industryLongTermDemand.getEndMonth());
        longtermVersionDO.setExtraBeginYear(extraYearMonth.getBeginYear());
        longtermVersionDO.setExtraBeginMonth(extraYearMonth.getBeginMonth());
        longtermVersionDO.setExtraEndYear(extraYearMonth.getEndYear());
        longtermVersionDO.setExtraEndMonth(extraYearMonth.getEndMonth());
        longtermVersionDO.setExtraIndustry(versionDTO.getExtraIndustry());
        if (StringUtils.isNotBlank(versionDTO.getRelevantPplVersion())){
            UnificatedVersionDTO pplVersionDO = demandDBHelper.getOne(UnificatedVersionDTO.class, "where version_code = ?",
                    versionDTO.getRelevantPplVersion());
            if (pplVersionDO == null){
                throw new BizException("不存在versionCode为" + versionDTO.getRelevantPplVersion() + "的13周版本");
            }
            pplVersionDO.parseJsonDemand();
            longtermVersionDO.setPpl13weekVersionCode(versionDTO.getRelevantPplVersion());

            String startYearMonth = pplVersionDO.getPplDemand().getBeginYear() + "-" + DateUtils.fillZeroMonthOrDay(pplVersionDO.getPplDemand().getBeginMonth());
            String endYearMonth = pplVersionDO.getPplDemand().getEndYear() + "-" + DateUtils.fillZeroMonthOrDay(pplVersionDO.getPplDemand().getEndMonth());
            longtermVersionDO.setPpl13weekStartYearMonth(startYearMonth);
            longtermVersionDO.setPpl13weekEndYearMonth(endYearMonth);
        }
    }

    /**
     * 此方法无需开启事物 ，调用层统一事物
     * @param versionCode
     */
    @Override
    public void startLongTermVersion(String versionCode) {

        LongtermVersionDO longtermVersionDO =
                demandDBHelper.getOne(LongtermVersionDO.class, "where version_code = ?", versionCode);

        if (longtermVersionDO == null){
            throw new BizException("不存在versionCode为" + versionCode + "的中长期版本");
        }

        longtermVersionDO.setStatus(LongtermVersionStatusEnum.PROCESS.getCode());
        demandDBHelper.update(longtermVersionDO);

        // 生成versionGroup
        boolean result = longtermVersionGroupService.initVersionGroup(versionCode);
        if (!result) {
            log.error("init version group fail, version code:{}", versionCode);
        } else {
            result = longtermVersionGroupService.initVersionGroupItem(versionCode, null);
            if (!result) {
                log.error("init version group item fail, version code:{}", versionCode);
            }
        }
    }

    @Override
    public void sendNotSubmitMsg() {
        // 查询进行中版本
        LongtermVersionDO versionDO = demandDBHelper.getOne(LongtermVersionDO.class,
                "where status = ?", LongtermVersionStatusEnum.PROCESS.getCode());

        if (versionDO == null) {
            return;
        }
        LongTermUnifiedVersionDTO unifiedVersionDTO = demandDBHelper.getOne(LongTermUnifiedVersionDTO.class,
                "where version_code = ?", versionDO.getVersionCode());

        List<LongtermVersionGroupDO> notSubmitGroup  = demandDBHelper.getAll(LongtermVersionGroupDO.class,
                "where version_code = ? and status in (?)",
                versionDO.getVersionCode(),
                Arrays.asList(LongtermVersionGroupStatusEnum.CREATE.getCode(),
                        LongtermVersionGroupStatusEnum.IN_SUBMIT.getCode()));
        Map<String, Object> templateParams = new HashMap<>();
        List<LongTermUnifiedVersionEventDO> eventDOList = unifiedVersionDTO.getEventList().stream()
                .filter(v -> v.getEventCode().equals(CrpEventEnum.long_term_audit_deadline.getCode())).collect(
                        Collectors.toList());
        Date deadline = eventDOList.get(0).getDeadline();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String parseDate = sdf.format(deadline);
        templateParams.put("auditDeadline",parseDate);
        for (LongtermVersionGroupDO groupDO : notSubmitGroup) {
            dictService.eventNotice(CrpEventEnum.long_term_not_submit.getCode(), null,null,templateParams,groupDO.getCurrentProcessor());
        }
    }

    @Override
    public void sendItemErrorMsg() {
        // 查询进行中版本
        LongtermVersionDO versionDO = demandDBHelper.getOne(LongtermVersionDO.class,
                "where status = ?", LongtermVersionStatusEnum.PROCESS.getCode());
        if (versionDO == null) {
            return;
        }
        // 查询版本分组
        List<LongtermVersionGroupDO> groupDOS = demandDBHelper.getAll(LongtermVersionGroupDO.class,
                "where version_code = ? and status != ?",
                versionDO.getVersionCode(), LongtermVersionGroupStatusEnum.REJECT.getCode());
        if (ListUtils.isEmpty(groupDOS) ) {
            return;
        }
        // 查询最新预测数据
        List<Integer> records = demandDBHelper.getRaw(Integer.class,
                "select max(id) from longterm_version_group_record where version_group_id in (?) group by version_group_id",
                ListUtils.transform(groupDOS, LongtermVersionGroupDO::getId));
        if (ListUtils.isEmpty(records)) {
            return;
        }
        List<LongtermVersionGroupRecordItemDO> itemDOS = demandDBHelper.getAll(LongtermVersionGroupRecordItemDO.class,
                "where version_group_record_id in (?)", records);

        // 总开关，获取有多少种通知需要发送
        int model = 0;
        List<String> list1 = new ArrayList<>();
        // 判断机，有新的模式就加一个if，注意，遵循1，2，4，8二进制进位规律
        for (LongtermVersionGroupRecordItemDO itemDO : itemDOS) {
            if ("GPU(裸金属&CVM)".equals(itemDO.getProduct()) && (StringTools.isBlank(itemDO.getGpuType()) || "(空值)".equals(itemDO.getGpuType()))) {
                model = model | 1;
                list1.add(itemDO.getInstanceFamily() + ":" + itemDO.getInstanceType() + ":" + itemDO.getGpuType());
            }
        }
        // 最后判断，如果有需要发送的告警，就发送
        if ((model & 1) == 1) {
            // 发送GPU产品的GPU卡型信息缺失的告警
            alert.sendRtx("jackycjchen;keithyu",
                    "中长期行业录入-GPU卡型缺失告警",
                    String.format("版本号:%s, 存在GPU卡型信息缺失的数据，请关注并及时配置GPU卡型信息。\n+实例族:实例类型:GPU卡型\n" + String.join("\n", list1),
                            versionDO.getVersionCode()));
        }
    }

    @Override
    public void closeLongTermVersion(String versionCode) {
        LongtermVersionDO longtermVersionDO =
                demandDBHelper.getOne(LongtermVersionDO.class, "where version_code = ?", versionCode);

        if (longtermVersionDO == null){
            throw new BizException("不存在versionCode为" + versionCode + "的中长期版本");
        }

//        List<LongtermVersionGroupDO> notFinishList = demandDBHelper.getAll(LongtermVersionGroupDO.class,
//                "where version_code = ? and status != ?",
//                versionCode, LongtermVersionGroupStatusEnum.DONE.getCode());
//        if (ListUtils.isNotEmpty(notFinishList)){
//            throw new BizException("当前版本存在未完成的审批" + notFinishList.size() + "个，明细："
//                    + StringTools.join(ListUtils.transform(notFinishList, o ->
//                    "行业部门:" + o.getIndustryDept() + ",业务分组:" + o.getBizGroup()), ";"));
//        }
        // 将未完成的版本分组全部完成
        longtermVersionApprovalService.allGroupToStatus(versionCode, LongtermVersionGroupStatusEnum.DONE, "");

        longtermVersionDO.setStatus(LongtermVersionStatusEnum.DONE.getCode());
        demandDBHelper.update(longtermVersionDO);
    }
    @Override
    public LongtermVersionDO getPreviousDoneVersion(String versionCode) {
        return demandDBHelper.getOne(LongtermVersionDO.class,
                "where version_code<? and status=? order by version_code desc",
                versionCode, LongtermVersionStatusEnum.DONE.getCode());
    }
}
