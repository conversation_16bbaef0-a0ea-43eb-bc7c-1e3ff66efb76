package cloud.demand.app.modules.p2p.ppl13week.enums;

import java.util.Objects;
import lombok.Getter;

@Getter
public enum DraftStatusEnum {

    /**
     *
     */
    UPDATE("update", "调整"),
    /**
     *
     */
    INSERT("insert", "新增"),

    DELETED("delete", "取消"),

    ORIGINAL("original", "无变化"),

    ;
    final private String code;
    final private String name;

    DraftStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DraftStatusEnum getByCode(String code) {
        for (DraftStatusEnum e : DraftStatusEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        DraftStatusEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}
