package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.CbsStockPlanCommenReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.CbsStockPlanRsq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.DescribeCbsStockPlanTaskReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.DescribeCbsStockPlanTaskRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.SaveCbsStockPlanReq;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyApiLogDO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.json.JSON;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.nutz.json.Json;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class QcbsApiServiceImpl implements cloud.demand.app.modules.p2p.ppl13week.service.QcbsApiService {

    @Resource
    private DBHelper demandDBHelper;


    @SneakyThrows
    public static String getSignature(long timestamp) {
        // 定义密钥和值
        String accessName = accessnameDP.get();
        String accessKey = accesskeyDP.get();

        // 获取当前时间戳
        accessKey += timestamp;

        MessageDigest md = MessageDigest.getInstance("SHA-512");
        md.update(accessName.getBytes(StandardCharsets.UTF_8));
        byte[] bytes = md.digest(accessKey.getBytes(StandardCharsets.UTF_8));
        StringBuilder sb = new StringBuilder();
        for (byte aByte : bytes) {
            sb.append(Integer.toString((aByte & 0xff) + 0x100, 16).substring(1));
        }
        return sb.toString();
    }


    private static final Supplier<String> accessnameDP =
            DynamicProperty.create("qcbs.Accessname",
                    "crp_access");
    private static final Supplier<String> accesskeyDP =
            DynamicProperty.create("qcbs.Accesskey",
                    "5619541c-b8j7-0a5k-b5c6-33749bc5b78h");


    @Override
    public DescribeCbsStockPlanTaskRsp describeCbsStockPlanTask(DescribeCbsStockPlanTaskReq req) {
        req.setAction("DescribeCbsStockPlanTask");
        return callQcbs(req, DescribeCbsStockPlanTaskRsp.class);
    }

    @Override
    public CbsStockPlanRsq saveCbsStockPlan(SaveCbsStockPlanReq req) {
        req.setAction("SaveCbsStockPlan");
        return callQcbs(req, CbsStockPlanRsq.class);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void logQcbsApi(String url, String method, String req, String resp) {
        try {
            PplStockSupplyApiLogDO logDO = new PplStockSupplyApiLogDO();
            logDO.setUrl(url);
            logDO.setMethod(method);
            logDO.setReq(req);
            logDO.setResp(resp);

            demandDBHelper.insert(logDO);
        } catch (Throwable e) {
            log.error("logQcbsApi insert error: {}", e.getMessage());
            log.error("logQcbsApi fail, url:{}, method:{}, req:{}, resp:{}", url, method, req, resp);
        }
    }

    public <K> K callQcbs(CbsStockPlanCommenReq req, Class<K> clz) {

        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .readTimeout(60, TimeUnit.SECONDS)
                    .connectTimeout(60, TimeUnit.SECONDS)
//                .sslSocketFactory(SSLSocketClient.getSSLSocketFactory(), SSLSocketClient.getX509TrustManager())
                    .build();

            MediaType mediaType = MediaType.parse("application/json");
            // 字段值为null时，就不要传该字段过去
            String json = com.alibaba.fastjson.JSON.toJSONString(req);
            RequestBody body = RequestBody.create(mediaType, json);
            long timestamp = System.currentTimeMillis();
            Request request = new Request.Builder()
                    .url("http://qcbs.production.polaris/api/v3")
                    .post(body)
                    .addHeader("host", "qcbs.woa.com")
                    .addHeader("content-type", "application/json")
                    .addHeader("accessname", accessnameDP.get())
                    .addHeader("timestamp", String.valueOf(timestamp))
                    .addHeader("signature", getSignature(timestamp))
                    .build();

            Response response = null;
            response = client.newCall(request).execute();

            if (response.code() != 200) {
                log2db("CBS", req.getAction(), Json.toJson(req), response.toString());
                throw BizException.makeThrow("调用CBS 对冲接口失败： resonse != 200,header:%s; data:%s;",
                        response.headers(),
                        response.body());
            }

            ResponseBody resBody = response.body();

            assert resBody != null;
            String retData = resBody.string();
            log2db("CBS", req.getAction(), Json.toJson(req), "res: " + response + ";  body:" +retData);
            log.info("callQcbs resBody : {}", retData);
            return JSON.parse(retData, clz);
        } catch (Exception e) {
            String ex = ExceptionUtils.getStackTrace(e);
            log2db("CBS", req.getAction(), Json.toJson(req), ex);
            throw BizException.makeThrow("调用CBS 对冲接口失败： %s", ex);
        }
    }


    private void log2db(String url, String method, String req, String resp) {
        try {
            logQcbsApi(url, method, req, resp);
        } catch (Throwable e) {
            log.error("logQcbsApi fail", e);
        }
    }


}
