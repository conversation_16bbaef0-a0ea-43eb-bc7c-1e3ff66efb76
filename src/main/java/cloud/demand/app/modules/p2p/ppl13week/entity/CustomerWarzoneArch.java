package cloud.demand.app.modules.p2p.ppl13week.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("customer_warzone_arch")
public class CustomerWarzoneArch {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    @Column(value = "customer_short_name")
    private String customerShortName;

    @Column(value = "war_zone")
    private String warZone;

    @Column(value = "arch")
    private String arch;
}
