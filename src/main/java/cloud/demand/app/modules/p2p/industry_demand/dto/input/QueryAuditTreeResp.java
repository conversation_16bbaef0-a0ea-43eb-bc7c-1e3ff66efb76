package cloud.demand.app.modules.p2p.industry_demand.dto.input;

import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandVersionInputGroupDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandInputCustomerGroupStatusEnum;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandInputGroupTypeEnum;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandInputWarZoneGroupStatusEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
public class QueryAuditTreeResp {

    private List<WarZone> warZones;

    @Data
    public static class WarZone {
        /**战区名称*/
        private String warZoneName;
        private String status;
        private String statusName;
        /**录入分组id*/
        private Long inputGroupId;
        /**当前处理人*/
        private String currentHandler;

        private Boolean isAllowApprove;
        private String notAllowApproveTips; // 不能审批的原因

        /**客户列表*/
        private List<Customer> customers;
    }

    @Data
    public static class Customer {
        /**客户名称*/
        private String customerName;
        private String status;
        private String statusName;
        /**录入分组id*/
        private Long inputGroupId;
        /**当前处理人*/
        private String currentHandler;

        private Boolean isAllowApprove;
        private String notAllowApproveTips; // 不能审批的原因
    }

    public static QueryAuditTreeResp from(List<IndustryDemandVersionInputGroupDO> groups,
                                          boolean isMainStatusReject,
                                          String mainCurrentHandler) {
        QueryAuditTreeResp resp = new QueryAuditTreeResp();

        List<IndustryDemandVersionInputGroupDO> warGroups = ListUtils.filter(groups,
                o -> Objects.equals(o.getGroupType(), IndustryDemandInputGroupTypeEnum.IND_WAR_ZONE_PRODUCT.getCode()));
        List<IndustryDemandVersionInputGroupDO> customerGroups = ListUtils.filter(groups,
                o -> Objects.equals(o.getGroupType(), IndustryDemandInputGroupTypeEnum.IND_CUSTOMER_PRODUCT.getCode()));

        Map<String, List<IndustryDemandVersionInputGroupDO>> wars =
                ListUtils.toMapList(warGroups, o -> o.getWarZoneName(), o -> o);
        Map<String, List<IndustryDemandVersionInputGroupDO>> warCustomers =
                ListUtils.toMapList(customerGroups, o -> o.getWarZoneName(), o -> o);

        List<WarZone> warZones = new ArrayList<>();
        resp.setWarZones(warZones);
        for (Map.Entry<String, List<IndustryDemandVersionInputGroupDO>> war : wars.entrySet()) {
            WarZone warZone = new WarZone();
            warZones.add(warZone);
            warZone.setWarZoneName(war.getKey());
            // 此时只有一个产品，所以只有一个状态
            IndustryDemandVersionInputGroupDO g = war.getValue().get(0);
            warZone.setStatus(g.getStatus());
            warZone.setStatusName(IndustryDemandInputWarZoneGroupStatusEnum.getNameByCode(g.getStatus()));
            warZone.setInputGroupId(g.getId());

            // 主状态是驳回状态时，且当前结点是已完成状态
            if (isMainStatusReject
                    && IndustryDemandInputWarZoneGroupStatusEnum.PASS.getCode().equals(g.getStatus())) {
                warZone.setIsAllowApprove(false);
                // warZone.setCurrentHandler(mainCurrentHandler);
                warZone.setNotAllowApproveTips("请驳回战区下的客户分组");
            } else {
                warZone.setCurrentHandler(g.getCurrentHandler());
                if (!StringTools.isIn(g.getStatus(),
                        IndustryDemandInputWarZoneGroupStatusEnum.WAR_DIRECTOR_APPROVE.getCode(),
                        IndustryDemandInputWarZoneGroupStatusEnum.WAR_BIZ_APPROVE.getCode())) {
                    warZone.setIsAllowApprove(false);
                    warZone.setNotAllowApproveTips("当前状态不允许审批");
                } else if (StringTools.isNotBlank(g.getCurrentHandler())
                        && !g.getCurrentHandler().contains(LoginUtils.getUserName())) {
                    warZone.setIsAllowApprove(false);
                    warZone.setNotAllowApproveTips("您不是审批人，无法审批");
                } else {
                    warZone.setIsAllowApprove(true);
                }
            }

            List<IndustryDemandVersionInputGroupDO> customers = warCustomers.get(war.getKey());
            warZone.setCustomers(ListUtils.transform(customers, o -> {
                Customer customer = new Customer();
                customer.setCustomerName(o.getCustomerName());
                customer.setStatus(o.getStatus());
                customer.setStatusName(IndustryDemandInputCustomerGroupStatusEnum.getNameByCode(o.getStatus()));
                customer.setInputGroupId(o.getId());

                // 主状态是驳回状态时且当前结点是已完成状态
                if (isMainStatusReject
                        && IndustryDemandInputCustomerGroupStatusEnum.PASS.getCode().equals(o.getStatus())) {
                    customer.setIsAllowApprove(true);
                    customer.setCurrentHandler(mainCurrentHandler);
                } else {
                    customer.setCurrentHandler(o.getCurrentHandler());

                    // 战区如果处于驳回状态，那么PASS也可以被审批(驳回)
                    List<String> canApproveStatus = ListUtils.newList(
                            IndustryDemandInputCustomerGroupStatusEnum.WAR_LEADER_APPROVE.getCode(),
                            IndustryDemandInputCustomerGroupStatusEnum.INIT.getCode());
                    if (IndustryDemandInputWarZoneGroupStatusEnum.REJECT.getCode().equals(g.getStatus())) {
                        canApproveStatus.add(IndustryDemandInputCustomerGroupStatusEnum.PASS.getCode());
                    }

                    if (!canApproveStatus.contains(o.getStatus())) {
                        customer.setIsAllowApprove(false);
                        customer.setNotAllowApproveTips("当前状态不允许审批");
                    } else if (StringTools.isNotBlank(o.getCurrentHandler())
                            && !o.getCurrentHandler().contains(LoginUtils.getUserName())) {
                        customer.setIsAllowApprove(false);
                        customer.setNotAllowApproveTips("您不是审批人，无法审批");
                    } else {
                        customer.setIsAllowApprove(true);
                    }
                }

                return customer;
            }));
        }

        return resp;
    }

}
