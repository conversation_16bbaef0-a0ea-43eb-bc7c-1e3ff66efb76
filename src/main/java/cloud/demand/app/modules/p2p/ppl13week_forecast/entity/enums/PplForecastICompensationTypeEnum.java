package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum PplForecastICompensationTypeEnum {

    /**
     * 改变预测前的输入量
     */
    INPUT("预测前干预干预输入量"),

    /**
     * 改变预测后的输出量
     */
    OUTPUT("预测后干预预测量");

    private String showName;

    public static PplForecastICompensationTypeEnum getByCode(String code) {
        for (PplForecastICompensationTypeEnum e : PplForecastICompensationTypeEnum.values()) {
            if (Objects.equals(code, e.name())) {
                return e;
            }
        }
        return null;
    }
}
