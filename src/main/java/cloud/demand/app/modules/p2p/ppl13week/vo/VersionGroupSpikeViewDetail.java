package cloud.demand.app.modules.p2p.ppl13week.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
public class VersionGroupSpikeViewDetail extends VersionGroupSpikeViewDetailKey {

    @Column(value = "customer_short_name1")
    private String customerShortName;

    @Column(value = "customer_uin1")
    private String customerUin;

    @Column(value = "forecast_num")
    private BigDecimal forecastNum;

    /**
     * 标记需求的毛刺属性
     * <br>
     * 0=非毛刺
     * 1=毛刺
     * 2=排除在外的（例如黑名单），即不算毛刺，也不算非毛刺
     */
    @Column(value = "is_spike")
    private Integer isSpike;

}