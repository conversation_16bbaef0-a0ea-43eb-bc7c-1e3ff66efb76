package cloud.demand.app.modules.p2p.ppl13week.entity;

import cloud.demand.app.modules.p2p.ppl13week.entity.base.PplItemBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_item")
public class PplItemDO extends PplItemBaseDO {

    @Column(value = "forecast_model_detail_id")
    private Long forecastModelDetailId;

    @Column(value = "forecast_model_source_type")
    private String forecastModelSourceType;

    @Column(value = "is_comd", insertValueScript = "0")
    private Boolean isComd;

    @Column(value = "source_ppl_id")
    private String sourcePplId;

    /**
     * 延期需求原来的开始购买日期
     */
    @Column(value = "expired_begin_buy_date")
    private LocalDate expiredBeginBuyDate;


    /**
     * 订单明细id<br/>Column: [order_number_id]
     */
    @Column(value = "order_number_id")
    private String orderNumberId;

    /**
     * ppl是否已过期,判断标准， 当前需求日期已小于最新云运管版本开始需求年月
     */
    @Column(value = "is_expired")
    private Boolean isExpired;

}