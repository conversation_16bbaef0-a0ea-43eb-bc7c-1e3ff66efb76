package cloud.demand.app.modules.p2p.industry_demand.enums;

import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import java.util.Objects;
import lombok.Getter;

/**
 * 行业需求管理 - 角色枚举
 *
 * @see IndustryDemandAuthDO#getRole()
 */
@Getter
public enum IndustryDemandAuthRoleEnum {

    UNKNOWN("UNKNOWN", "未知角色"),

//    INDUSTRY_INTERFACE("INDUSTRY_INTERFACE", "行业接口人"),

    INDUSTRY_APPROVE("INDUSTRY_APPROVE", "行业审批人"),

    PRODUCT_INTERFACE("PRODUCT_INTERFACE", "产品审批人"),

    COMD_INTERFACE("COMD_INTERFACE", "云运管总监"),

    WAR_ZONE_INPUT("WAR_ZONE_INPUT", "战区录入人"),

    WAR_ZONE_LEADER("WAR_ZONE_LEADER", "战区Leader"),

    WAR_ZONE_DIRECTOR("WAR_ZONE_DIRECTOR", "战区总监"),

    WAR_ZONE_BIZ("WAR_ZONE_BIZ", "战区商务"),

    ////////////////////////////////////////////////////////////

    ADMIN("ADMIN", "管理员"),

    ///////////////////////////////////////////////////////////

    PPL_INDUSTRY_MEDDLE("PPL_INDUSTRY_MEDDLE", "13周预测提交人"), // 原来叫：13周行业干预人

    PPL_INDUSTRY_APPROVE("PPL_INDUSTRY_APPROVE", "13周数据关注人"), // 原来叫：13周行业审批人

    PPL_PRODUCT_APPROVE("PPL_PRODUCT_APPROVE", "13周产品审批人"), // 用于PAAS产品审批

    PPL_COMD_INTERFACE("PPL_COMD_INTERFACE", "13周预测确认人"), // 原来叫：13周云运管审批人

    // 内部流程新加
    CENTER_PMO("CENTER_PMO", "中心PMO"),

    CENTER_DIRECTOR("CENTER_DIRECTOR", "中心总监"),

    INDUSTRY_DIRECTOR("INDUSTRY_DIRECTOR", "行业运营总监"),

    INDUSTRY_GM("INDUSTRY_GM", "行业GM"),

    TECH_DIRECTOR("TECH_DIRECTOR", "技术中心总监"),

    TECH_INTERFACE("TECH_INTERFACE", "技术中心"),

    CUSTOMER_LEADER("CUSTOMER_LEADER", "客户Leader"),

    VERSION_OWNER("VERSION_OWNER", "版本维护人"),

    INDUSTRY_DATA_FOLLOWER("INDUSTRY_DATA_FOLLOWER", "行业数据关注人"),

    CBS_PRINCIPAL("CBS_PRINCIPAL", "CBS接口人"),

    PRODUCT_PRINCIPAL("PRODUCT_PRINCIPAL", "产品接口人"),

    INDUSTRY_PRINCIPAL("INDUSTRY_PRINCIPAL", "行业接口人"),

    PRODUCT_DATA_FOLLOWER("PRODUCT_DATA_FOLLOWER", "产品数据关注人"),

    INDUSTRY_ATP_CONFIG_ROLE("INDUSTRY_ATP_CONFIG_ROLE", "行业ATP能力配置人"),

    INDUSTRY_ATP_CONSULTANT("INDUSTRY_ATP_CONSULTANT", "行业ATP咨询人"),
    ;
    // INNER_PROCESS_ROLE_LIST 内部流程角色列表 根据角色权限从大到小排序
//    final public static List<IndustryDemandAuthRoleEnum> INNER_PROCESS_ROLE_LIST =
//            Arrays.asList(new IndustryDemandAuthRoleEnum[]{INDUSTRY_GM, TECH_DIRECTOR, TECH_INTERFACE, CENTER_DIRECTOR,
//                    CENTER_PMO, WAR_ZONE_LEADER});

    final private String code;
    final private String name;

    IndustryDemandAuthRoleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static IndustryDemandAuthRoleEnum getByCode(String code) {
        for (IndustryDemandAuthRoleEnum e : IndustryDemandAuthRoleEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }

    public static String getNameByCode(String code) {
        IndustryDemandAuthRoleEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}