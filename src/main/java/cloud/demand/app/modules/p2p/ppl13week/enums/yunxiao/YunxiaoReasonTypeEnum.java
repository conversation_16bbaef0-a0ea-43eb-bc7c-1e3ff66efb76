package cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum YunxiaoReasonTypeEnum {

    GAME("GAME", "新游戏项目上线"),

    NEWBUY("NEWBUY", "新购"),

    UPGRADE("UPGRADE", "业务升级"),

    OTHER("OTHER", "其它"),

    // 云运管提需求新增的

    WINBACK_NEW("WINBACK_NEW", "winback新增"),

    STOCK_SCALE_UP("STOCK_SCALE_UP", "存量扩容"),

    NEW_PROJECT("NEW_PROJECT", "新项目上线"),

    WINBACK_PROJECT("WINBACK_PROJECT", "winback项目"),

    INST_EX_NORMAL("INST_EX_NORMAL", "机型置换-正常代次更替"),

    ;

    final private String code;
    final private String name;

    YunxiaoReasonTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static YunxiaoReasonTypeEnum getByCode(String code) {
        for (YunxiaoReasonTypeEnum e : YunxiaoReasonTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        YunxiaoReasonTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}