package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Table("ppl_forecast_predict_result_for_ziyan_split")
@JsonIgnoreProperties(ignoreUnknown = true)
public class PplForecastPredictResultForZiyanSplitDO extends PplForecastPredictResultDO{

    @Column(value = "result_id")
    private Long resultId;

    @Column(value = "output_version_id")
    private Long outputVersionId;

    /** 海关<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "device_group")
    private String deviceGroup;

    @Column(value = "project_name")
    String projectName;

    @Column(value = "custom_bg_name")
    String customBgName;

    @Column(value = "note")
    String note;
}