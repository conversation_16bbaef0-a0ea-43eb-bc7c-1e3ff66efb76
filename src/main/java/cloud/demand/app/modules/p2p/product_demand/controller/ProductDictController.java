package cloud.demand.app.modules.p2p.product_demand.controller;

import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.product_demand.dto.ListAllModuleBusinessTypeNameReq;
import cloud.demand.app.modules.p2p.product_demand.service.ProductDemandDictService;
import cloud.demand.app.modules.rrp_remake.service.DictService;
import cloud.demand.app.web.model.common.DownloadBean;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;

@Tag(name = "产品全年需求模块-字典模块")
@JsonrpcController("/product/annual/api")
@Slf4j
public class ProductDictController {

    @Resource
    ProductDemandDictService productDemandDictService;
    @Resource
    DictService rrpRemakeDictService;

    @RequestMapping
    public Object listAllIndustryName() {
        return productDemandDictService.listAllIndustryName();
    }

    @RequestMapping
    public Object listAllCustomerName(@JsonrpcParam ListAllCustomerNameReq req) {
        return productDemandDictService.listAllCustomerName(req.getIndustryName());
    }

    @RequestMapping
    public Object listAllReason(@JsonrpcParam ListAllReasonReq req) {
        return productDemandDictService.listAllReasonMap(req.getReasonType()).keySet();
    }

    @RequestMapping
    public Object listAllReasonType() {
        return new HashSet<>(productDemandDictService.listAllReasonMap().values());
    }

    @RequestMapping
    public Object listAllDeviceTypeName(@JsonrpcParam PlanProductDTO req) {
        if (req == null || req.getPlanProduct() == null) {
            return productDemandDictService.listAllDeviceTypeName(null);
        } else {
            return productDemandDictService.listAllDeviceTypeName(req.planProduct);
        }
    }

    @RequestMapping
    public Object listAllObsProjectType(@JsonrpcParam PlanProductDTO req) {
        return rrpRemakeDictService.listObsProjectTypeByPlanProduct(req.getPlanProduct());
    }

    @RequestMapping
    public Object listAllRegionName() {
        return productDemandDictService.listAllRegionName();
    }

    @RequestMapping
    public Object listAllZoneName(@JsonrpcParam ListAllZoneReq req) {
        return productDemandDictService.listAllZoneName(req.getRegionName());
    }

    @RequestMapping
    public Object listAllCampusName(@JsonrpcParam ListAllCampusReq req) {
        return productDemandDictService.listAllCampusName(req.getZoneName());
    }

    @RequestMapping
    public Object listAllModuleBusinessTypeName(@JsonrpcParam ListAllModuleBusinessTypeNameReq req) {
        if (req == null || req.getCampus() == null) {
            return productDemandDictService.listAllModuleBusinessTypeName(null);
        } else {
            return productDemandDictService.listAllModuleBusinessTypeName(req.getCampus());
        }
    }

    @RequestMapping
    public Object listAllPlanProduct() {
        return productDemandDictService.listAllPlanProduct();
    }

    @RequestMapping
    public Object listAll13WVersion() {
        return productDemandDictService.listAll13WVersion();
    }

    @RequestMapping
    public DownloadBean exportFormal() {
        FileNameAndBytesDTO fileNameAndBytesDTO = productDemandDictService.exportFormal();
        return new DownloadBean(fileNameAndBytesDTO.getFileName(), fileNameAndBytesDTO.getBytes());
    }

    @Data
    static class ListAllCustomerNameReq {

        private List<String> industryName;
    }

    @Data
    static class ListAllZoneReq {

        private List<String> regionName;
    }

    @Data
    static class ListAllCampusReq {

        private List<String> zoneName;
    }

    @Data
    static class ListAllReasonReq {

        private List<String> reasonType;
    }

    @Data
    static class PlanProductDTO {

        private String planProduct;
    }
}
