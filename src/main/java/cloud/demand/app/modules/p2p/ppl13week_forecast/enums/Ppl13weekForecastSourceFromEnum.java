package cloud.demand.app.modules.p2p.ppl13week_forecast.enums;

import cloud.demand.app.modules.forecast_compute.enums.SerialIntervalEnum;
import lombok.Getter;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * 13周预测任务的状态枚举
 * <AUTHOR>
 */
@Getter
public enum Ppl13weekForecastSourceFromEnum {

    /**
     * 行业，默认
     */
    MONTH_LATEST("MONTH_LATEST", "按月的存量预测最新值"),

    /**
     * 行业，默认
     */
    MONTH_CUR("MONTH_CUR", "按月的存量预测"),

    /**
     * 行业，默认
     */
    MONTH_RATE("MONTH_RATE", "按月比例预测预测"),

    /**
     * 行业，默认
     */
    MONTH("MONTH", "按月预测"),

    /**
     * 内领， 后来加的
     */
    WEEK_AVG("WEEK_AVG", "周均预测"),
    /**
     * 内领， 后来加的
     */
    WEEK_MAX("WEEK_MAX", "周峰预测"),

    /**
     * 按周预测，按周滚动，2023-12-25加入
     */
    WEEK("WEEK", "按周预测")

    ;

    final private String code;
    final private String name;

    @NotNull
    public static Ppl13weekForecastSourceFromEnum getFromSerialInterval(SerialIntervalEnum serialInterval) {
        return serialInterval == SerialIntervalEnum.WEEK ?
                Ppl13weekForecastSourceFromEnum.WEEK : Ppl13weekForecastSourceFromEnum.MONTH;
    }

    Ppl13weekForecastSourceFromEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }


    public static Ppl13weekForecastSourceFromEnum getByCode(String code) {
        for (Ppl13weekForecastSourceFromEnum e : Ppl13weekForecastSourceFromEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        Ppl13weekForecastSourceFromEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}