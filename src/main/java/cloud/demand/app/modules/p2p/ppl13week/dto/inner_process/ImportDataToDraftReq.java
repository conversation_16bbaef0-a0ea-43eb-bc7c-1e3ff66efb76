package cloud.demand.app.modules.p2p.ppl13week.dto.inner_process;

import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import java.util.List;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ToString
@Data
public class ImportDataToDraftReq {

    @NotBlank
    private String industryDept;

    @NotBlank
    private String product;
//
//    @NotBlank
//    String startYearMonth;
//
//    String endYearMonth;

    private List<String> customer;

    List<GroupItemDTO> groupItemDTOList;

//    private Boolean isCover = Boolean.FALSE; // true 为覆盖 false 为追加

}
