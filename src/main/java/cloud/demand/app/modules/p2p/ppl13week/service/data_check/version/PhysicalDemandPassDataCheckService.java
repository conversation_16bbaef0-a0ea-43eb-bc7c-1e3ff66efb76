package cloud.demand.app.modules.p2p.ppl13week.service.data_check.version;


import cloud.demand.app.modules.p2p.ppl13week.dto.DataCheckRsp;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionEventDO;
import cloud.demand.app.modules.p2p.ppl13week.service.data_check.AbstractDataCheckService;
import cloud.demand.app.modules.rrp_new_feature.service.AutoDeliveryDemandForecastService;
import cloud.demand.app.modules.rrp_remake.entity.RrpConfigDO;
import cloud.demand.app.modules.rrp_remake.service.VersionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PhysicalDemandPassDataCheckService extends AbstractDataCheckService {

    @Resource
    private AutoDeliveryDemandForecastService autoDeliveryDemandForecastService;
    @Resource
    private VersionService versionService;

    @Override
    public boolean support(String eventCode) {
        return false;
    }

    @Override
    public boolean isNeedToExecute(Map<String, Object> context) {
        // 获取大版本请求数据
        UnificatedVersionData unificatedVersionReqData = getUnificatedVersionReqData(context);
        Map<String, UnificatedVersionEventDO> codeToEvent = unificatedVersionReqData.getUnificatedVersionDTO()
                .getEventList().stream()
                .collect(Collectors.toMap(UnificatedVersionEventDO::getEventCode, v -> v));
        return false;
    }

    @Override
    public DataCheckRsp execute(Map<String, Object> context) {
        UnificatedVersionData unificatedVersionReqData = getUnificatedVersionReqData(context);
        DataCheckRsp dataCheckRsp = new DataCheckRsp();

        RrpConfigDO rrpConfig = versionService.getRrpConfig(unificatedVersionReqData.getUnificatedVersionDTO().getVersionCode());
        if (rrpConfig == null) {
            throw new BizException(
                    String.format("未找到版本编码%s对应的产品13周需求版本",
                            unificatedVersionReqData.getUnificatedVersionDTO().getVersionCode()));
        }

        Map<String, Object> result =
                autoDeliveryDemandForecastService.w13CompareErpAndRRP(Math.toIntExact(rrpConfig.getId()),
                        false,
                        null,
                        true);
        List passDetail = (List) result.get("passDetail");
        if (passDetail.size() > 0) {
            dataCheckRsp.setIsPass(false);
            dataCheckRsp.setMsg("产品13周传递后对账失败，请检查产品13周传递后对账");
        } else {
            dataCheckRsp.setIsPass(true);
            dataCheckRsp.setMsg("产品13周传递后对账成功");
        }

        finishUnificatedVersionEvent(dataCheckRsp, unificatedVersionReqData.getEvent());
        return dataCheckRsp;
    }
}
