package cloud.demand.app.modules.p2p.ppl13week_forecast.service;


import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryForecastCommonReq;

/**
 * <AUTHOR>
 */
public interface Ppl13weekOpsService {

    /**
     * 清理数据
     * @param req 删除参数
     * @return str
     */
    String cleanDataByTaskId(QueryForecastCommonReq req);

    /**
     * 同步刷新cbs头部客户名单
     */
    void syncCbsHeaderCustomer();

}
