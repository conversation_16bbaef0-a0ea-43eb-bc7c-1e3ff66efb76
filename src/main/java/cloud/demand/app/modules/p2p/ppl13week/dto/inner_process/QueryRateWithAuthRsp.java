package cloud.demand.app.modules.p2p.ppl13week.dto.inner_process;

import cloud.demand.app.modules.mrpv2.web.QueryTableRsp;
import com.pugwoo.wooutils.collect.ListUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class QueryRateWithAuthRsp {

//    private Map<String, Result> addDataMap = new HashMap<>();
//    private Map<String, Result> returnDataMap = new HashMap<>();

    private Map<String, Result> dataMap = new HashMap<>();

    private String start;
    private String end;

    private String aimAccuracy;

    private String industryDept;

    private String summaryRate;


    @Data
    public static class Result {

        private String rate;

        private String conclusion;

        private BigDecimal totalForecastTotalNew532 = new BigDecimal(0);
        private BigDecimal totalExecute = new BigDecimal(0);
        private BigDecimal totalDeviationValue532 = new BigDecimal(0);
        ;

        // k : yearMonth
        private Map<String, Integer> forecastNumMap = new HashMap<>();
        private Map<String, Double> executeNumMap = new HashMap<>();

        private Map<String, Result> childrenDataMap = new HashMap<>();
    }


    public static void buildConclusion(Result result) {
        final double[] maxDiffValue = {0};
        AtomicReference<String> yearMonth = new AtomicReference<>("");
        ;
        AtomicReference<Integer> forecastNum = new AtomicReference<>(0);
        AtomicReference<Double> executeNum = new AtomicReference<>(0.0);
        ;
        Map<String, Integer> forecastNumMap = result.getForecastNumMap();
        Map<String, Double> executeNumMap = result.getExecuteNumMap();
        forecastNumMap.forEach((k, v) -> {
            Integer forecastValue = Math.abs(v);
            Double executeValue = Math.abs(executeNumMap.get(k) != null ? executeNumMap.get(k) : 0.0);
            if (Math.abs(v - executeValue) > maxDiffValue[0]) {
                yearMonth.set(k);
                forecastNum.set(forecastValue);
                executeNum.set(executeValue);
                maxDiffValue[0] = Math.abs(forecastValue - executeValue);
            }
        });
        executeNumMap.forEach((k, v) -> {
            if (forecastNumMap.get(k) == null) {
                Double executeValue = Math.abs(v);
                // 只有执行有，预测没有的， 才没处理过
                if (executeValue > maxDiffValue[0]) {
                    yearMonth.set(k);
                    forecastNum.set(0);
                    executeNum.set(executeValue);
                }
            }
        });
        if (forecastNum.get() > executeNum.get()) {
            result.setConclusion("预测未执行");
        } else if (forecastNum.get() > 0 && forecastNum.get() < executeNum.get()) {
            result.setConclusion("预测不足");
        } else if (forecastNum.get() == 0 && forecastNum.get() < executeNum.get()) {
            result.setConclusion("完全无预测");
        }
    }

    public static void buildTotalDeviationValue532(Result result) {
        BigDecimal bigDecimal = result.getTotalForecastTotalNew532().subtract(result.getTotalExecute()).abs()
                .setScale(2, BigDecimal.ROUND_HALF_UP);
        result.setTotalDeviationValue532(bigDecimal);
    }


    public void buildSummaryRate(QueryTableRsp queryTableRsp) {
        if (queryTableRsp != null && ListUtils.isNotEmpty(queryTableRsp.getData())) {
            for (Map datum : queryTableRsp.getData()) {
                if (datum.get("origin_v2_avg_forecast_match_rate_core_by_day@总计") != null) {
                    this.summaryRate = datum.get("origin_v2_avg_forecast_match_rate_core_by_day@总计").toString();
                }
            }
            if (this.summaryRate != null) {
                return;
            }
        }

        if (ListUtils.isEmpty(this.dataMap)) {
            return;
        }
        List<Result> executeList = new ArrayList<>();
        BigDecimal totalAllExecute = new BigDecimal(0);
        BigDecimal summaryRate = new BigDecimal(0);
        this.dataMap.forEach((k, v) -> {
            if (v == null || v.getTotalExecute().compareTo(BigDecimal.ZERO) == 0) {
                // 如果没有该客户的结果 或 该客户执行量为0 不计入范围内
                return;
            }
            executeList.add(v);
        });
        if (ListUtils.isEmpty(executeList)) {
            return;
        }
        for (Result result : executeList) {
            totalAllExecute = totalAllExecute.add(result.getTotalExecute());
        }

        for (Result customerResult : executeList) {
            BigDecimal customerExecute = customerResult.getTotalExecute();
            BigDecimal customerRate = new BigDecimal(StringUtils.isBlank(customerResult.getRate()) ? "0" :
                    customerResult.getRate().substring(0, customerResult.getRate().length() - 2));
            // 准确率 * 权重（客户执行量 / 架构师执行量）
            summaryRate = summaryRate.add(
                    customerRate.multiply(customerExecute.divide(totalAllExecute, RoundingMode.HALF_UP)));
        }

        this.summaryRate = summaryRate.setScale(2, RoundingMode.HALF_UP) + "%";
    }


}
