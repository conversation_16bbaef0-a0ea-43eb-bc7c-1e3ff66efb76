package cloud.demand.app.modules.p2p.longterm.vo;


import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VersionGroupVO {

    LongtermVersionDO versionDO;
    LongtermVersionGroupDO versionGroupDO;
}
