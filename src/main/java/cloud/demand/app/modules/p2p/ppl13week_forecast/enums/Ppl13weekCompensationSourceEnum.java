package cloud.demand.app.modules.p2p.ppl13week_forecast.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 模型预测补偿来源枚举
 */
@Getter
public enum Ppl13weekCompensationSourceEnum {

    NEW_INSTANCE_TYPE("NEW_INSTANCE_TYPE", "新机型");

    final private String code;
    final private String name;

    Ppl13weekCompensationSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Ppl13weekCompensationSourceEnum getByCode(String code) {
        for (Ppl13weekCompensationSourceEnum e : Ppl13weekCompensationSourceEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        Ppl13weekCompensationSourceEnum e = getByCode(code);
        return e == null ? (code == null ? "" : code) : e.getName();
    }

}