package cloud.demand.app.modules.p2p.ppl13week.controller;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ApproveVersionGroupByAdminReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.CheckStockSupplyReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.CheckStockSupplyResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.CreateVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.StartFinishVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.UpdateVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplConvertPhysicalServerService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionService;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.util.YuntiUtils;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@JsonrpcController("/ppl13week")
@Slf4j
public class PplVersionController {

    @Autowired
    private PplVersionService pplVersionService;
    @Resource
    private PplConvertPhysicalServerService pplConvertPhysicalServerService;

    @RequestMapping
    public QueryVersionResp queryVersionList(@JsonrpcParam QueryVersionReq req) {
        return pplVersionService.queryVersionList(req);
    }

    @RequestMapping
    public QueryVersionResp queryAllVersionList() {
        return pplVersionService.queryAllVersionList();
    }

    @RequestMapping
    public Map<String, Object> createVersion(@JsonrpcParam @Valid CreateVersionReq req,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        pplVersionService.createVersion(req);
        return MapUtils.of("result", "success");
    }

    @RequestMapping
    public List<Map<String, Object>> queryVersionType() {
        return ListUtils.transform(PplVersionTypeEnum.values(), o -> MapUtils.of(
                "code", o.getCode(), "name", o.getName()
        ));
    }

    @RequestMapping
    public Map<String, Object> updateVersion(@JsonrpcParam @Valid UpdateVersionReq req,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        pplVersionService.updateVersion(req);
        return MapUtils.of("result", "success");
    }

    @RequestMapping
    public Map<String, Object> startApprove(@JsonrpcParam @Valid StartFinishVersionReq req,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        pplVersionService.startApprove(req.getId());
        return MapUtils.of("result", "success");
    }

    @RequestMapping
    public Map<String, Object> finishApprove(@JsonrpcParam @Valid StartFinishVersionReq req,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        pplVersionService.finishApprove(req.getId());
        return MapUtils.of("result", "success");
    }

    @RequestMapping
    public Map<String, Object> forceFinishApprove(@JsonrpcParam @Valid StartFinishVersionReq req,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        pplVersionService.forceFinishApprove(req.getId());
        return MapUtils.of("result", "success");
    }


    @RequestMapping
    public Map<String, Object> changeVersionGroupStatus(@JsonrpcParam @Valid ApproveVersionGroupByAdminReq req,
            BindingResult bindingResult) {
        pplVersionService.changeVersionGroupStatus(req);
        return MapUtils.of("result", "success");
    }

    @RequestMapping
    public PplVersionDO getByVersionCode(@JsonrpcParam QueryVersionReq req) {
        if (req.getVersionCode() == null) {
            throw new WrongWebParameterException("【versionCode】不能为空");
        }
        return pplVersionService.getByVersionCode(req.getVersionCode());
    }

    @RequestMapping
    public CheckStockSupplyResp checkVersionGroup(@JsonrpcParam CheckStockSupplyReq req) {
        if (req.getVersionCode() == null) {
            throw new WrongWebParameterException("【versionCode】不能为空");
        }
        return pplVersionService.checkVersionGroup(req.getVersionCode());
    }

    @RequestMapping
    public Object queryDeviceDemandByVersion(@JsonrpcParam CheckStockSupplyReq req) {
        if (req.getVersionCode() == null) {
            throw new WrongWebParameterException("【versionCode】不能为空");
        }
        return pplConvertPhysicalServerService.queryDeviceDemandByVersion(req.getVersionCode());
    }

}
