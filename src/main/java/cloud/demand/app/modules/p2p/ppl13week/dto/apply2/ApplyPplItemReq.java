package cloud.demand.app.modules.p2p.ppl13week.dto.apply2;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ApplyPplItemReq {

    /**
     * 云霄提交页面的【应用角色】
     */
    @NotBlank(message = "应用角色必须提供")
    private String appRole;
    /**
     * 云霄提交页面的【预约单分类】
     */
    @NotBlank(message = "预约单分类必须提供")
    private String orderCategory;

    private String projectName;

    /**
     * 云霄预约单号
     */
    @NotBlank(message = "云霄预约单号必须提供")
    private String yunxiaoOrderId;

    @NotBlank(message = "行业必须提供")
    private String industryDept;

    // 页面匹配上的所有ppl单（无论用户是否勾选）
    private List<String> matchedPpl;

    // 根据主key匹配上的所有ppl单（后台自行设置）
    private List<String> matchedPplOrders;


    /**
     * 预约的资源和pplId
     */
    @Valid
    private List<ApplyItem> applyItem;

    @Data
    public static class ApplyItem {

        @NotNull(message = "云霄预约单明细id必须提供")
        private Long yunxiaoDetailId;

        /**
         * 选择预约的pplid
         */
        private String pplId;

        /**
         * 这个由后台根据pplId来查出来
         */
        private PplItemDO pplItemDO;

        // **       系统为预约单再次匹配到的预测明细，需更新的ppl  -- 无需前端传递，接口内部使用字段
        private List<PplItemDO> updatePplItemDOs = new ArrayList<>();

        // **       系统为预约单再次匹配到的预测明细，需拆单出来新增的ppl  -- 无需前端传递，接口内部使用字段
        private List<PplItemDO> splitPplItemDOs = new ArrayList<>();

        /**
         * 【必须】需要自动补充的核心数，也就是计划外的核心数
         */
        private Long needAutoFillCore;

        /**
         * 【必须】需要自动补充的gpu卡数，也就是计划外的gpu卡数
         */
        private BigDecimal needAutoFillGpu;

        /**
         * 【必须】PPL剩余核心数，需要拆单
         */
        private Long leftPplCore = 0L;

        /**
         * 【必须】PPL剩余卡数，需要拆单
         */
        private BigDecimal leftPplGpu = BigDecimal.ZERO;
    }
}
