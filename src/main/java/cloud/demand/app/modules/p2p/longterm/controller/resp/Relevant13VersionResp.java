package cloud.demand.app.modules.p2p.longterm.controller.resp;

import java.util.List;
import lombok.Data;

@Data
public class Relevant13VersionResp {

    private List<VersionYear> versionYearList;



    @Data
    public static class VersionYear {
        private String relevantVersion;

        private Boolean isFinish;

        private Integer demandBeginYear;

        private Integer demandEndYear;
    }

}
