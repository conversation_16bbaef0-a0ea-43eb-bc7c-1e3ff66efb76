package cloud.demand.app.modules.p2p.product_demand.service;

import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.product_demand.entity.CustomerConfigDO;
import cloud.demand.app.modules.p2p.product_demand.entity.ServerPartsExtendedInfoDO;
import cloud.demand.app.modules.p2p.product_demand.service.impl.ProductDemandDictServiceImpl.FullCampusBaseDictData;
import cloud.demand.app.modules.p2p.product_demand.service.impl.ProductDemandDictServiceImpl.ZoneBaseDictData;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产品全年预测字典接口
 */
public interface ProductDemandDictService {

    /**
     * 获取所有行业-客户配置条目
     *
     * @return 行业-客户配置条目
     */
    List<CustomerConfigDO> listAllCustomerConfigDO();

    /**
     * 获取所有行业名
     *
     * @return 行业名集合
     */
    Set<String> listAllIndustryName();

    /**
     * 获取所有客户名
     *
     * @return 客户名集合
     */
    Set<String> listAllCustomerName(List<String> industryName);

    /**
     * 获取所有需求原因
     *
     * @return 需求原因集合
     */
    Map<String, String> listAllReasonMap();

    /**
     * 获取所有需求原因
     *
     * @return 需求原因集合
     */
    Map<String, String> listAllReasonMap(List<String> reasonType);

    /**
     * 获取规划产品所属部门的所有设备类型
     *
     * @Param planProduct 规划产品 如果为null，查所有机型
     *
     * @return 设备类型集合
     */
    Set<String> listAllDeviceTypeName(String planProduct);

    /**
     * 获取所有region
     *
     * @return region集合
     */
    List<String> listAllRegionName();

    /**
     * 获取所有zone
     *
     * @param regionName region列表
     * @return zone集合
     */
    List<String> listAllZoneName(List<String> regionName);

    /**
     * 获取所有campus
     *
     * @param zoneName zone列表
     * @return campus集合
     */
    List<String> listAllCampusName(List<String> zoneName);

    List<ZoneBaseDictData> listAllZoneData(List<String> regionName);

    List<FullCampusBaseDictData> listAllCampusData(List<String> zoneName);

    List<FullCampusBaseDictData> listCampusDataByCampusName(List<String> campusName);

    Set<String> listAllPlanProduct();

    List<String> listAll13WVersion();

    Map<String, Set<String>> industryCustomerMap();

    FileNameAndBytesDTO exportFormal();

    /**
     * 获取所有物理机设备类型的配置信息表
     *
     * @return 物理机设备类型配置信息表
     */
    Map<String, ServerPartsExtendedInfoDO> getAllServerConfigInfo();

    Map<String, List<String>> buildCampus2ModuleBusinessTypeNameMap();

    List<String> listAllModuleBusinessTypeName(String campus);
}
