package cloud.demand.app.modules.p2p.ppl13week.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * PPL-ID事件类型枚举，这个是PPL-ID资源的变化的最大分类
 */
@Getter
public enum PplItemEventTypeEnum {

    PPL_INPUT("PPL_INPUT", "PPL录入"),

    PPL_CHANGE("PPL_CHANGE", "PPL变更"),

    PPL_APPLY("PPL_APPLY", "PPL预约"),

    PPL_VERSION_INPUT("PPL_VERSION_INPUT", "PPL版本审批提交"),

    PPL_VERSION_AUDIT("PPL_VERSION_AUDIT", "PPL版本审批"),

    PPL_REVERSE_SYNC("PPL_REVERSE_SYNC", "预约单反向同步")

    ;

    final private String code;
    final private String name;

    PplItemEventTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PplItemEventTypeEnum getByCode(String code) {
        for (PplItemEventTypeEnum e : PplItemEventTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        PplItemEventTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}