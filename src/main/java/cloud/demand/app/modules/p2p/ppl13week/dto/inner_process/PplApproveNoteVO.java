package cloud.demand.app.modules.p2p.ppl13week.dto.inner_process;


import com.pugwoo.dbhelper.annotation.Column;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class PplApproveNoteVO {

    @Column(value = "customer_short_name")
    private String customerShortName;
    @Column(value = "ppl_order")
    private String pplOrder;
    @Column(value = "version_id")
    private Long versionId;
    @Column(value = "node_code")
    private String nodeCode;
    @Column(value = "operate_user")
    private String operateUser;
    @Column(value = "approve_note")
    private String approveNote;
    @Column(value = "approve_result")
    private String approveResult;
    @Column(value = "approve_time")
    private Date approveTime;
}
