package cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply;

import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryPplStockSupplySummaryRsp {


    List<SummaryItem> data;

    @Data
    public static class SummaryItem {

        /**
         * 年月
         */
        String yearMonth;
        /**
         * 地域
         */
        String regionName;
        /**
         * 机型 如SA3
         */
        String instanceType;

        /**
         * 需求类型
         */
        String demandType;

        /**
         * 预测-报备量A
         */
        BigDecimal forecastDemand;
        /**
         * 预测-预约量B
         */
        BigDecimal forecastApplied;

        /**
         * 预测-未预约C
         */
        BigDecimal forecastValid;

        /**
         * 预约量 E
         */
        BigDecimal appliedDemand;

        /**
         * 预约-已锁定 F
         */
        BigDecimal appliedLock;

        /**
         * 预约-未锁定 G
         */
        BigDecimal appliedGap;
        /**
         * 实际对冲量- C-G
         */
        BigDecimal deployDemand;
        /**
         * 对冲结果 现网满足
         */
        BigDecimal stockSatisfy;
        /**
         * 对冲结果 搬迁满足
         */
        BigDecimal stockMove;
        /**
         * 对冲结果 采购满足
         */
        BigDecimal stockBuy;
        /**
         * 对冲结果 现网满足
         */
        BigDecimal stockSuggest;
        /**
         * 对冲结果 无法满足
         */
        BigDecimal stockFail;
    }
}
