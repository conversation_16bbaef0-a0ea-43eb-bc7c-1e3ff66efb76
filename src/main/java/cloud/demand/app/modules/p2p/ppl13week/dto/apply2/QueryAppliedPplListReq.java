package cloud.demand.app.modules.p2p.ppl13week.dto.apply2;

import cloud.demand.app.common.utils.DateUtils;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;

import java.util.List;

@Data
public class QueryAppliedPplListReq {

    /**
     * 需求年月
     */
    private String startYearMonth;
    /**
     * 需求年月
     */
    private String endYearMonth;
    /**
     * 客户uin
     */
    private List<String> customerUin;
    /**
     * 客户简称
     */
    private List<String> customerShortName;
    /**
     * 云霄预约单id
     */
    private List<String> yunxiaoOrderId;

    /**
     * 实例类型/机型
     */
    private List<String> instanceType;

    /**
     * 地域类型，单选，值为：国内、境外
     */
    private String regionType;

    /**
     * 地域
     */
    private List<String> regionName;

    /**
     * 产品
     */
    private List<String> product;

    private String industryDept;

    /**
     * 需求类型
     */
    private List<String> demandType;

    /**
     * 适用于表：ppl_item_applied
     */
    public WhereSQL toWhereSQL() {
        WhereSQL whereSQL = new WhereSQL();

        if (StringTools.isNotBlank(startYearMonth)) {
            DateUtils.YearMonth start = DateUtils.parse(startYearMonth);
            if (start != null) {
                whereSQL.and("year>? or year=? and month>=?", start.getYear(), start.getYear(), start.getMonth());
            }
        }
        if (StringTools.isNotBlank(endYearMonth)) {
            DateUtils.YearMonth end = DateUtils.parse(endYearMonth);
            if (end != null) {
                whereSQL.and("year<? or year=? and month<=?", end.getYear(), end.getYear(), end.getMonth());
            }
        }

        if (ListUtils.isNotEmpty(customerShortName)) {
            whereSQL.and("customer_short_name in (?)", customerShortName);
        }
        if (ListUtils.isNotEmpty(customerUin)) {
            whereSQL.and("customer_uin in (?)", customerUin);
        }

        if (ListUtils.isNotEmpty(yunxiaoOrderId)) {
            whereSQL.and("yunxiao_order_id in (?)", yunxiaoOrderId);
        }

        if (ListUtils.isNotEmpty(instanceType)) {
            whereSQL.and("instance_type in (?)", instanceType);
        }

        if (ListUtils.isNotEmpty(regionName)) {
            whereSQL.and("region_name in (?)", regionName);
        }

        if (ListUtils.isNotEmpty(product)) {
            whereSQL.and("product in (?)", product);
        }

        if (ListUtils.isNotEmpty(demandType)) {
            whereSQL.and("demand_type in (?)", demandType);
        }

        if (StringTools.isNotBlank(industryDept)) {
            whereSQL.and("industry_dept=?", industryDept);
        }

        return whereSQL;
    }

}
