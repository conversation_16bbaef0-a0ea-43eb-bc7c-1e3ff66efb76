package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req;


import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Arrays;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;
import yunti.boot.exception.BizException;


/**
 * <AUTHOR>
 */
@Data
@ToString
public class QueryRateViewDetailReq extends DateReq{

    /**
     * 任务Id， 一次一个id 或者多个id
     */
    @NotEmpty(message = "taskIds不能为空")
    private List<Long> taskIds;

    /**
     * 默认全量
     */
    private List<String> ginsFamilies;
    /**
     * 默认全量
     */
    private List<String> regionNames;


    /**
     * 开发debug 字段
     */
    private Boolean useSwap;

}
