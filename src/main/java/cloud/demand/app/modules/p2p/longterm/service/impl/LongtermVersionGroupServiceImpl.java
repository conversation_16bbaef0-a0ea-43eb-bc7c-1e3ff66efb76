package cloud.demand.app.modules.p2p.longterm.service.impl;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.ListUtils2;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.industry_cockpit.v3.enums.AppRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandProductEnumDO;
import cloud.demand.app.modules.p2p.industry_demand.service.TodoService;
import cloud.demand.app.modules.p2p.industry_demand.service.TodoService.ApprovalData;
import cloud.demand.app.modules.p2p.industry_demand.service.TodoService.ApprovalMessageBody;
import cloud.demand.app.modules.p2p.longterm.controller.req.OperateLongtermAuditReq;
import cloud.demand.app.modules.p2p.longterm.dto.LongTermHistoryScale;
import cloud.demand.app.modules.p2p.longterm.dto.LongtermPpl13weekDataDTO;
import cloud.demand.app.modules.p2p.longterm.dto.VersionGroupProgressResp;
import cloud.demand.app.modules.p2p.longterm.dto.VersionGroupProgressResp.Node;
import cloud.demand.app.modules.p2p.longterm.dto.YearQuarterMonth;
import cloud.demand.app.modules.p2p.longterm.entity.LongTermTodoRecordDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupReasonDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupRecordDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionHistoryItemDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionHistoryTaskDO;
import cloud.demand.app.modules.p2p.longterm.entity.vo.LongtermVersionGroupReasonVO;
import cloud.demand.app.modules.p2p.longterm.entity.vo.LongtermVersionGroupRecordItemVO;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermConstants;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupItemSourceTypeEnum;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupItemTimeUnitEnum;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupStatusEnum;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionStatusEnum;
import cloud.demand.app.modules.p2p.longterm.service.LongTermVersionService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermVersionApprovalService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermVersionGroupService;
import cloud.demand.app.modules.p2p.longterm.service.impl.inner_dto.YearlyProductSumDTO;
import cloud.demand.app.modules.p2p.longterm.service.impl.inner_dto.YearlyProductSumRow;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplConfigProductEnumDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LongtermVersionGroupServiceImpl implements LongtermVersionGroupService {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private DBHelper prodReadOnlyCkStdCrpDBHelper;
    @Resource
    private DictService dictService;
    @Resource
    private DBHelper cdCommonDbHelper;

    @Resource
    private PplCommonService pplCommonService;

    @Resource
    private TodoService todoService;

    @Resource
    private LongtermVersionApprovalService approvalService;
    @Resource
    private LongTermVersionService longTermVersionService;


    Supplier<String> domainSupplier = DynamicProperty.create("app.industry.domain", "");

    private static final Supplier<String> onlyComdGroup =
            DynamicProperty.create("longterm.only_comd_group", "false");

    static ExecutorService executor = Executors.newFixedThreadPool(8);

    @Override
    @Synchronized(keyScript = "args[0]")
    @Transactional(value = "demandTransactionManager")
    public boolean initVersionGroup(String versionCode) {
        // 1. 拿到版本信息并确定group还没生成
        LongtermVersionDO versionDO = getVersionDO(versionCode);
        if (versionDO == null) {
            log.error("version code:{} not exist", versionCode);
            return false;
        }
        List<LongtermVersionGroupDO> groups = demandDBHelper.getAll(LongtermVersionGroupDO.class,
                "where version_code=?", versionCode);
        if (ListUtils.isNotEmpty(groups)) {
            log.warn("version code:{} has init version group, return success", versionCode);
            return true; // 初始化过了就当做是成功了，即可重入
        }

        // 2. 开始初始化group
        Map<String, Set<String>> industryTopCustomers = dictService.queryIndustryToTopCommonCustomerName();
        List<IndustryDemandProductEnumDO> industryList = null;
        if (onlyComdGroup.get() != null && onlyComdGroup.get().equals("true")) {
            industryList = demandDBHelper.getAll(IndustryDemandProductEnumDO.class,
                    "where industry='云运营管理部' group by industry");
        } else {
            industryList = demandDBHelper.getAll(IndustryDemandProductEnumDO.class,
                    "where industry not in ('中长尾', '云运营管理部') group by industry");
        }

        Set<String> industrySet = ListUtils.toSet(industryList, IndustryDemandProductEnumDO::getIndustry);
        List<LongtermVersionGroupDO> groupDOS = new ArrayList<>();
        if (industrySet.contains("内部业务部")) {
            List<PplConfigProductEnumDO> paasProducts = demandDBHelper.getAll(
                    PplConfigProductEnumDO.class, "where flag='PAAS'");
            for (PplConfigProductEnumDO product : paasProducts) {
                LongtermVersionGroupDO groupDO = new LongtermVersionGroupDO();
                groupDO.setVersionCode(versionCode);
                groupDO.setIndustryDept("内部业务部");
                groupDO.setBizGroup(product.getProductName());
                groupDO.setStatus(LongtermVersionGroupStatusEnum.CREATE.getCode());
                groupDO.setCurrentProcessor(
                        approvalService.getNodeCodeProcessor(groupDO.getStatus(),groupDO.getIndustryDept(),groupDO.getBizGroup()));
                groupDO.setTopCustomers(StringTools.join(",", industryTopCustomers.get("内部业务部")));
                groupDOS.add(groupDO);
            }
            industrySet.remove("内部业务部");
        }

        // 处理剩余的部门
        for (String industry : industrySet) {
            LongtermVersionGroupDO groupDO = new LongtermVersionGroupDO();
            groupDO.setVersionCode(versionCode);
            groupDO.setIndustryDept(industry);
            groupDO.setBizGroup("");
            groupDO.setStatus(LongtermVersionGroupStatusEnum.CREATE.getCode());
            groupDO.setCurrentProcessor(approvalService.getNodeCodeProcessor(groupDO.getStatus(),groupDO.getIndustryDept(),groupDO.getBizGroup()));
            groupDO.setTopCustomers(StringTools.join(",", industryTopCustomers.get(industry)));
            groupDOS.add(groupDO);
        }

        demandDBHelper.insertBatchWithoutReturnId(groupDOS);
        return true;
    }

    private LongtermVersionDO getVersionDO(String versionCode) {
        if (versionCode == null) {
            return null;
        }
        return demandDBHelper.getOne(LongtermVersionDO.class, "where version_code=?", versionCode);
    }

    private LongtermVersionDO getPreviousDoneVersionDO(String versionCode) {
        if (versionCode == null) {
            return null;
        }
        return demandDBHelper.getOne(LongtermVersionDO.class,
                "where version_code<? and status=? order by version_code desc",
                versionCode, LongtermVersionStatusEnum.DONE.getCode());
    }

    @Override
    @Synchronized(namespace = "INIT-VERSION-GROUP-ITEM")
    @Transactional(value = "demandTransactionManager")
    public boolean initVersionGroupItem(String versionCode, Long groupId)  {
        // 1. 检查版本是否存在及record是否已经产生
        LongtermVersionDO versionDO = getVersionDO(versionCode);
        if (versionDO == null) {
            log.error("version code:{} not exist", versionCode);
            return false;
        }
        WhereSQL groupWhere = new WhereSQL();
        WhereSQL recordWhere = new WhereSQL();
        groupWhere.and("version_code = ?", versionCode);
        recordWhere.and("version_code = ?", versionCode);
        if (groupId != null)  {
            groupWhere.and("id = ?", groupId);
            recordWhere.and("version_group_id = ?", groupId);
        }
        List<LongtermVersionGroupDO> groups = demandDBHelper.getAll(LongtermVersionGroupDO.class,
                groupWhere.getSQL(), groupWhere.getParams());
        if (ListUtils.isEmpty(groups)) {
            log.error("version code:{} groups not init yet", versionCode);
            return false;
        }
        List<LongtermVersionGroupRecordDO> recordDOS = demandDBHelper.getAll(LongtermVersionGroupRecordDO.class,
                recordWhere.getSQL(), recordWhere.getParams());
        if (ListUtils.isNotEmpty(recordDOS)) {
            log.warn("version code:{} has init version group record, return success", versionCode);
            return true; // 初始化过了就当做是成功了，即可重入
        }

        // 2. 从上一个版本里拿干预前的结果
        LongtermVersionDO previousDoneVersionDO = getPreviousDoneVersionDO(versionCode);
        List<LongtermVersionGroupRecordItemVO> previousItems = getPreviousDoneRecordItemDOs(previousDoneVersionDO,versionDO.getPpl13weekEndYearMonth());
        // 3. 拿13周的结果
        List<LongtermPpl13weekDataDTO> ppl13WeekData = getPpl13WeekData(versionDO, groups);

        // 4. 组合数据
        List<LongtermVersionGroupRecordItemVO> mergedItem = merge(versionDO, previousItems, ppl13WeekData,
                previousDoneVersionDO == null ? "" : previousDoneVersionDO.getVersionCode());

        // 5. 按分组存储，并设置上版本信息
        Map<String, LongtermVersionGroupDO> groupMap = ListUtils.toMap(groups,
                o -> StringTools.join("@", o.getIndustryDept(), o.getBizGroup()), o -> o);
        recordDOS = initGroupFirstRecord(groups);
        demandDBHelper.insert(recordDOS);
        Map<Long, Long> groupId2RecordId = ListUtils.toMap(recordDOS, LongtermVersionGroupRecordDO::getVersionGroupId, BaseDO::getId);
        mergedItem.forEach(item -> {
            item.setId(null);
            item.setVersionCode(versionCode);
            String key = StringTools.join("@", item.getIndustryDept(), item.getBizGroup());
            LongtermVersionGroupDO groupDO = groupMap.get(key);
            if (groupDO != null) {
                item.setVersionGroupId(groupDO.getId());
                item.setVersionGroupRecordId(groupId2RecordId.get(groupDO.getId()));
            }
        });

        // 5.1 最细粒度下数据聚合并保存
        mergedItem = clearDim(mergedItem);

        // 丢弃group id为空的数据
        mergedItem = ListUtils.filter(mergedItem, o -> o.getVersionGroupId() != null);


        Map<Long, List<LongtermVersionGroupRecordItemDO>> groupItemMap = mergedItem.stream()
                .filter(v -> v.getVersionGroupId() != null)
                .collect(Collectors.groupingBy(LongtermVersionGroupRecordItemDO::getVersionGroupId));

        // 5.2 查询历史规模数据
        List<LongtermVersionGroupRecordItemDO> historyScaleItemList = new ArrayList<>();
        // 创建Callable任务
        String startYearMonth = cloud.demand.app.common.utils.DateUtils.getYearMonthStr(
                versionDO.getDemandBeginYear(), versionDO.getDemandBeginMonth());
        List<String> statTimes = new ArrayList<>();
        while(cloud.demand.app.common.utils.DateUtils.compare(startYearMonth,versionDO.getVersionYearMonth()) < 0){
            // 每个月去查最后一天的月切数据 分三部分去查
            statTimes.add(cloud.demand.app.common.utils.DateUtils.getLastDayByYearMonth(startYearMonth));
            startYearMonth = cloud.demand.app.common.utils.DateUtils.yearMonthAddMonth(startYearMonth,1);
        }
        List<Callable<List<LongtermVersionGroupRecordItemDO>>> tasks = new ArrayList<>();
        groupMap.forEach((k, v) -> {
            tasks.add(() -> queryHistoryScaleDataByTask(versionDO, v, groupItemMap.get(v.getId()), groupId2RecordId,  statTimes));
        });

        // 提交所有任务并等待完成
        try {
            List<Future<List<LongtermVersionGroupRecordItemDO>>> futures = executor.invokeAll(tasks);
            for (Future<List<LongtermVersionGroupRecordItemDO>> future : futures) {
                // 将每个任务的结果合并到主列表
                historyScaleItemList.addAll(future.get());
            }
        } catch (InterruptedException | ExecutionException e) {
            // 异常处理（根据业务场景决定是否抛出或记录日志）
            Thread.currentThread().interrupt(); // 重置中断状态
            throw new RuntimeException("中长期多线程查询历史执行数据失败", e);
        }

        demandDBHelper.insertBatchWithoutReturnId(mergedItem);
        demandDBHelper.insertBatchWithoutReturnId(historyScaleItemList);

        // 6. 处理reason的继承
        if (previousDoneVersionDO != null) {
            demandDBHelper.delete(LongtermVersionGroupReasonDO.class, "where version_code=?", versionCode);
            List<LongtermVersionGroupReasonVO> previousReasons = demandDBHelper.getAll(
                    LongtermVersionGroupReasonVO.class, "where version_code=?", previousDoneVersionDO.getVersionCode());
            previousReasons.forEach(reason -> {
                reason.setId(null);
                reason.setVersionCode(versionCode);
                String key = StringTools.join("@", reason.getIndustryDept(), reason.getBizGroup());
                LongtermVersionGroupDO groupDO = groupMap.get(key);
                if (groupDO != null) {
                    reason.setVersionGroupId(groupDO.getId());
                }
            });
            demandDBHelper.insertBatchWithoutReturnId(previousReasons);
        }

        // 7. 生成需要初始化过去两年执行的任务
        List<LongtermVersionHistoryTaskDO> taskDOList = new ArrayList<>();
        for (LongtermVersionGroupDO group : groups) {
            taskDOList.add(LongtermVersionHistoryTaskDO.from(group));
        }
        demandDBHelper.insertBatchWithoutReturnId(taskDOList);

        return true;
    }


    public List<LongtermVersionGroupRecordItemDO> queryHistoryScaleDataByTask(LongtermVersionDO longtermVersionDO,
                                                                              LongtermVersionGroupDO group,
                                                                              List<LongtermVersionGroupRecordItemDO> longtermVersionGroupRecordItemDOS,
                                                                              Map<Long, Long> groupId2RecordId,
                                                                              List<String> statTimes){
        if (ListUtils.isEmpty(statTimes)){
            return new ArrayList<>();
        }

        Map<String, List<String>> map = dictService.queryCommonCustomerName2ShortNameByIndustryDept(group.getIndustryDept());
        Map<String, Set<String>> industryToCustomerShortName = dictService.queryIndustryToCustomerShortName();

        // top客户
        Set<String> topCustomer = new HashSet<>();
        if (StringUtils.isNotBlank(group.getTopCustomers())){
            List<String> topCustomerShortNames = Arrays.asList(group.getTopCustomers().split(","));
            for (String s : topCustomerShortNames) {
                if (map.containsKey(s)) {
                    topCustomer.addAll(map.get(s));
                }else {
                    topCustomer.add(s);
                }
            }
        }


        // 2普通客户
        Set<String> mediumCustomer = new HashSet<>();
        // 2.1底表数据中有的客户
        if (ListUtils.isNotEmpty(longtermVersionGroupRecordItemDOS)){
            Set<String> filter = longtermVersionGroupRecordItemDOS.stream()
                    .filter(s -> StringUtils.isNotBlank(s.getCustomerShortName())
                            && !LongtermConstants.UNKNOWN_CUSTOMER_NAME.equals(s.getCustomerShortName()))
                    .map(LongtermVersionGroupRecordItemDO::getCustomerShortName).collect(
                            Collectors.toSet());
            mediumCustomer.addAll(filter);
        }
        // 2.2策略表中行业配置的客户
        if (industryToCustomerShortName.containsKey(group.getIndustryDept())){
            mediumCustomer.addAll(industryToCustomerShortName.get(group.getIndustryDept()));
        }

        // 2.3 历史底表数据中有的客户
        List<String> raw = demandDBHelper.getRaw(String.class,
                "select distinct customer_short_name from longterm_version_group_record_item where deleted = 0"
                        + " and customer_short_name != '' and customer_short_name != '其他客户' and"
                        + " version_group_id in (select id from longterm_version_group where industry_dept = ?)",group.getIndustryDept());
        if (ListUtils.isNotEmpty(raw)){
            mediumCustomer.addAll(raw);
        }

        // 去掉top客户
        mediumCustomer = mediumCustomer.stream()
                .filter(s -> StringUtils.isNotBlank(s) && !topCustomer.contains(s)).collect(Collectors.toSet());

        List<LongtermVersionGroupRecordItemDO> result = new ArrayList<>();

        if (StringUtils.isBlank(group.getBizGroup())){
            // 云行业分别去查下面五个产品
            result.addAll(initHistoryScaleDataByProduct(group,statTimes,Ppl13weekProductTypeEnum.CVM.getName(),new ArrayList<>(topCustomer),new ArrayList<>(mediumCustomer)));
            result.addAll(initHistoryScaleDataByProduct(group,statTimes,Ppl13weekProductTypeEnum.GPU.getName(),new ArrayList<>(topCustomer),new ArrayList<>(mediumCustomer)));
            result.addAll(initHistoryScaleDataByProduct(group,statTimes,Ppl13weekProductTypeEnum.BM.getName(),new ArrayList<>(topCustomer),new ArrayList<>(mediumCustomer)));
            result.addAll(initHistoryScaleDataByProduct(group,statTimes,Ppl13weekProductTypeEnum.EMR.getName(),new ArrayList<>(topCustomer),new ArrayList<>(mediumCustomer)));
            result.addAll(initHistoryScaleDataByProduct(group,statTimes,Ppl13weekProductTypeEnum.EKS.getName(),new ArrayList<>(topCustomer),new ArrayList<>(mediumCustomer)));
        }else {
            if (Ppl13weekProductTypeEnum.EMR.getName().equals(group.getBizGroup())
            || Ppl13weekProductTypeEnum.EKS.getName().equals(group.getBizGroup())){
                // 内部业务部查对应业务分组的产品 当前仅查EMR和EKS
                result.addAll(initHistoryScaleDataForInnerDept(group,statTimes,group.getBizGroup()));
            }
        }

        if (ListUtils.isEmpty(result)){
            return new ArrayList<>();
        }

        for (LongtermVersionGroupRecordItemDO longtermVersionGroupRecordItemDO : result) {
            longtermVersionGroupRecordItemDO.setVersionCode(longtermVersionDO.getVersionCode());
            longtermVersionGroupRecordItemDO.setVersionGroupId(group.getId());
            longtermVersionGroupRecordItemDO.setVersionGroupRecordId(groupId2RecordId.get(group.getId()));
        }
        return result;
    }

    @Override
    @Synchronized(namespace = "INIT-VERSION-GROUP-ITEM")
    @Transactional(transactionManager = "demandTransactionManager", rollbackFor = Exception.class)
    public void initHistoryScaleData(Long versionGroupId){

        LongtermVersionGroupDO groupDO = demandDBHelper.getByKey(LongtermVersionGroupDO.class, versionGroupId);
        LongtermVersionGroupRecordDO recordDO = demandDBHelper.getOne(LongtermVersionGroupRecordDO.class,
                "where id = (select max(id) from longterm_version_group_record where deleted = 0 and version_group_id = ?)",
                versionGroupId);
        LongtermVersionDO longtermVersionDO = demandDBHelper.getOne(LongtermVersionDO.class, "where version_code = ?",
                groupDO.getVersionCode());
        String startYearMonth = cloud.demand.app.common.utils.DateUtils.getYearMonthStr(
                longtermVersionDO.getDemandBeginYear(), longtermVersionDO.getDemandBeginMonth());
        List<String> statTimes = new ArrayList<>();
        while(cloud.demand.app.common.utils.DateUtils.compare(startYearMonth,longtermVersionDO.getVersionYearMonth()) < 0){
            // 每个月去查最后一天的月切数据 分三部分去查
            statTimes.add(cloud.demand.app.common.utils.DateUtils.getLastDayByYearMonth(startYearMonth));
            startYearMonth = cloud.demand.app.common.utils.DateUtils.yearMonthAddMonth(startYearMonth,1);
        }

        if (ListUtils.isEmpty(statTimes)){
            return;
        }

        LongtermVersionGroupDO group = demandDBHelper.getOne(LongtermVersionGroupDO.class, "where id=?", versionGroupId);

        Map<String, List<String>> map = dictService.queryCommonCustomerName2ShortNameByIndustryDept(group.getIndustryDept());
        Map<String, Set<String>> industryToCustomerShortName = dictService.queryIndustryToCustomerShortName();

        // top客户
        Set<String> topCustomer = new HashSet<>();
        if (StringUtils.isNotBlank(group.getTopCustomers())){
            List<String> topCustomerShortNames = Arrays.asList(group.getTopCustomers().split(","));
            for (String s : topCustomerShortNames) {
                if (map.containsKey(s)) {
                    topCustomer.addAll(map.get(s));
                }else {
                    topCustomer.add(s);
                }
            }
        }

        // 2普通客户
        Set<String> mediumCustomer = new HashSet<>();
        List<LongtermVersionGroupRecordItemDO> longtermVersionGroupRecordItemDOS = queryItemList(versionGroupId);
        // 2.1底表数据中有的客户
        if (ListUtils.isNotEmpty(longtermVersionGroupRecordItemDOS)){
            Set<String> filter = longtermVersionGroupRecordItemDOS.stream()
                    .filter(s -> StringUtils.isNotBlank(s.getCustomerShortName()))
                    .map(LongtermVersionGroupRecordItemDO::getCustomerShortName).collect(
                            Collectors.toSet());
            if (ListUtils.isNotEmpty(filter)){
                for (String s : filter) {
                    if (map.containsKey(s)) {
                        mediumCustomer.addAll(map.get(s));
                    }else {
                        mediumCustomer.add(s);
                    }
                }
            }
        }
        // 2.2策略表中行业配置的客户
        if (industryToCustomerShortName.containsKey(group.getIndustryDept())){
            mediumCustomer.addAll(industryToCustomerShortName.get(group.getIndustryDept()));
        }

        // 去掉top客户
        mediumCustomer = mediumCustomer.stream()
                .filter(s -> StringUtils.isNotBlank(s) && !topCustomer.contains(s)).collect(Collectors.toSet());

        List<LongtermVersionGroupRecordItemDO> result = new ArrayList<>();

        if (StringUtils.isBlank(group.getBizGroup())){
            // 云行业分别去查下面五个产品
            result.addAll(initHistoryScaleDataByProduct(group,statTimes,Ppl13weekProductTypeEnum.CVM.getName(),new ArrayList<>(topCustomer),new ArrayList<>(mediumCustomer)));
            result.addAll(initHistoryScaleDataByProduct(group,statTimes,Ppl13weekProductTypeEnum.GPU.getName(),new ArrayList<>(topCustomer),new ArrayList<>(mediumCustomer)));
            result.addAll(initHistoryScaleDataByProduct(group,statTimes,Ppl13weekProductTypeEnum.BM.getName(),new ArrayList<>(topCustomer),new ArrayList<>(mediumCustomer)));
            result.addAll(initHistoryScaleDataByProduct(group,statTimes,Ppl13weekProductTypeEnum.EMR.getName(),new ArrayList<>(topCustomer),new ArrayList<>(mediumCustomer)));
            result.addAll(initHistoryScaleDataByProduct(group,statTimes,Ppl13weekProductTypeEnum.EKS.getName(),new ArrayList<>(topCustomer),new ArrayList<>(mediumCustomer)));
        }else {
            // 内部业务部查对应业务分组的产品
            result.addAll(initHistoryScaleDataForInnerDept(group,statTimes,group.getBizGroup()));
        }

        if (ListUtils.isEmpty(result)){
            return;
        }
        for (LongtermVersionGroupRecordItemDO longtermVersionGroupRecordItemDO : result) {
            longtermVersionGroupRecordItemDO.setVersionCode(longtermVersionDO.getVersionCode());
            longtermVersionGroupRecordItemDO.setVersionGroupId(versionGroupId);
            longtermVersionGroupRecordItemDO.setVersionGroupRecordId(recordDO.getId());
        }

        demandDBHelper.insertBatchWithoutReturnId(result);

        // 生成需要初始化过去两年执行的任务
        demandDBHelper.insert(LongtermVersionHistoryTaskDO.from(group));
    }

    @Override
    @Synchronized(namespace = "INIT-VERSION-HISTORY-ITEM", keyScript = "args[0].versionGroupId",
            waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Transactional(transactionManager = "demandTransactionManager", rollbackFor = Exception.class)
    public void initPass2YearHistoryItem(LongtermVersionHistoryTaskDO historyTaskDO) {
        LongtermVersionDO versionDO = demandDBHelper.getOne(LongtermVersionDO.class, "where version_code = ?", historyTaskDO.getVersionCode());
        if (versionDO == null) {
            throw new BizException("版本不存在" + historyTaskDO.getVersionCode());
        }
        LongtermVersionGroupDO groupDO = demandDBHelper.getOne(LongtermVersionGroupDO.class, "where id = ?", historyTaskDO.getVersionGroupId());
        if (groupDO == null)  {
            throw new BizException(String.format("版本【%s】的【%s】预测流程不存在", historyTaskDO.getVersionCode()), historyTaskDO.getIndustryDept());
        }
        Long recordId = demandDBHelper.getRawOne(Long.class, "select max(id) from longterm_version_group_record where version_group_id = ?", historyTaskDO.getVersionGroupId());
        if (recordId == null || recordId == 0) {
            throw new BizException(String.format("版本【%s】的【%s】预测流程没有有效的预测明细组", historyTaskDO.getVersionCode(), historyTaskDO.getIndustryDept()));
        }
        List<LongtermVersionGroupRecordItemDO> itemDOS = demandDBHelper.getAll(LongtermVersionGroupRecordItemDO.class, "where version_group_record_id = ?", recordId);
        // 辅助数据
        Map<Long, Long> groupId2RecordId = new HashMap<>();
        groupId2RecordId.put(groupDO.getId(), recordId);
        String flagYM = cloud.demand.app.common.utils.DateUtils.getYearMonthStr(
                versionDO.getDemandBeginYear() - 2, 1);
        List<String> statTimes = new ArrayList<>();
        while(cloud.demand.app.common.utils.DateUtils.compare(flagYM, YearMonth.of(versionDO.getDemandBeginYear(), 1).toString()) < 0){
            // 每个月去查最后一天的月切数据 分三部分去查
            statTimes.add(cloud.demand.app.common.utils.DateUtils.getLastDayByYearMonth(flagYM));
            flagYM = cloud.demand.app.common.utils.DateUtils.yearMonthAddMonth(flagYM,1);
        }
        List<LongtermVersionGroupRecordItemDO> tempRes =
                queryHistoryScaleDataByTask(versionDO, groupDO, itemDOS, groupId2RecordId, statTimes);
        Map<String, LongtermVersionHistoryItemDO> historyMap = new HashMap<>();
        for (LongtermVersionGroupRecordItemDO itemDO : tempRes) {
            LongtermVersionHistoryItemDO historyItemDO = historyMap.get(itemDO.buildHistoryItemKey());
            if (historyItemDO == null) {
                historyItemDO = LongtermVersionHistoryItemDO.from(historyTaskDO, itemDO);
            } else {
                historyItemDO.add(itemDO);
            }
            historyMap.put(itemDO.buildHistoryItemKey(), historyItemDO);
        }
        historyTaskDO.setStatus("DONE");
        demandDBHelper.delete(LongtermVersionHistoryItemDO.class, "where version_group_id = ?", historyTaskDO.getVersionGroupId());
        demandDBHelper.insertBatchWithoutReturnId(historyMap.values());
        demandDBHelper.update(historyTaskDO);
    }

    /**
     * 聚合最细维度的数据，将数据为0的清理掉
     */
    private List<LongtermVersionGroupRecordItemVO> clearDim(List<LongtermVersionGroupRecordItemVO> list) {
        if (list == null) {
            return new ArrayList<>();
        }

        List<LongtermVersionGroupRecordItemVO> result = new ArrayList<>();
        list.forEach(o -> {
            if (o.getCoreNum().compareTo(BigDecimal.ZERO)==0 && o.getGpuNum().compareTo(BigDecimal.ZERO) == 0) {
                o.setDeleted(true);
                result.add(o); // 插入数据库只是为了debug用，方便查问题
            }
        });

        list = ListUtils.filter(list, o -> o.getDeleted() == null || !o.getDeleted());
        Map<String, List<LongtermVersionGroupRecordItemVO>> map = ListUtils.toMapList(list,
                o -> o.getVersionGroupRecordId() + "@" + o.groupKey(), o -> o);
        map.forEach((key, itemList) -> {
            if (itemList.size() > 1) {
                // 合并
                LongtermVersionGroupRecordItemVO one = itemList.get(0);
                one.setCoreNum(NumberUtils.sum(itemList, LongtermVersionGroupRecordItemVO::getCoreNum));
                one.setGpuNum(NumberUtils.sum(itemList, LongtermVersionGroupRecordItemVO::getGpuNum));
                result.add(one);
            } else {
                result.add(itemList.get(0));
            }
        });
        return result;
    }

    @Override
    public String generateGroupSummaryForMoaAudit(LongtermVersionGroupDO groupDO) {
        // 1. 找到上一版本对应的group
        LongtermVersionDO previousVersionDO = longTermVersionService.getPreviousDoneVersion(groupDO.getVersionCode());
        LongtermVersionGroupDO previousGroupDO = null;
        if (previousVersionDO != null) {
            previousGroupDO = demandDBHelper.getOne(LongtermVersionGroupDO.class,
                    "where version_code=? and industry_dept=? and biz_group=?", previousVersionDO.getVersionCode(),
                    groupDO.getIndustryDept(), groupDO.getBizGroup());
        }

        // 2. 统计并比较，按年，再按产品
        List<YearlyProductSumDTO> summary = summaryProduct(groupDO);
        List<YearlyProductSumDTO> previousSummary = summaryProduct(previousGroupDO);

        List<String> indexStr = ListUtils.of("一", "二", "三", "四", "五", "六", "七", "八", "九", "十");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < summary.size(); i++) {
            sb.append(indexStr.get(i)).append("、").append(summary.get(i).getYear()).append("年").append("\n");
            List<YearlyProductSumDTO.ProductItem> products = summary.get(i).getProducts();
            for (YearlyProductSumDTO.ProductItem productItem : products) {
                sb.append(productItem.getProduct()).append("全年需求预测净增量 ").append(format(productItem.getNetNum())).append(productItem.getUnit());
                // 找上期的值
                YearlyProductSumDTO.ProductItem previousItem = findProductItem(previousSummary, summary.get(i).getYear(), productItem.getProduct());
                if (previousItem == null) {
                    sb.append("(上期无数据)");
                } else{
                    sb.append(", 较上期");
                    int diff = productItem.getNetNum() - previousItem.getNetNum();
                    if (diff > 0) {
                        sb.append("增加").append(format(diff)).append(productItem.getUnit());
                    } else if (diff == 0) {
                        sb.append("无变化");
                    } else {
                        sb.append("减少").append(format(-diff)).append(productItem.getUnit());
                    }
                }
                sb.append(products.indexOf(productItem) < products.size() - 1 ? "; " : "。");
            }
            sb.append("\n");
        }
        return sb.toString();
    }

    private YearlyProductSumDTO.ProductItem findProductItem(List<YearlyProductSumDTO> previousSummary,
            int year, String product) {
        for (YearlyProductSumDTO yearlyProductSum : previousSummary) {
            if (Objects.equals(year, yearlyProductSum.getYear())) {
                for (YearlyProductSumDTO.ProductItem productItem : yearlyProductSum.getProducts()) {
                    if (Objects.equals(product, productItem.getProduct())) {
                        return productItem;
                    }
                }
            }
        }
        return null;
    }

    private static String format(Integer number) {
        if (number == null) {
            return "";
        }
        NumberFormat numberFormat = NumberFormat.getInstance();
        return numberFormat.format(number);
    }

    private List<YearlyProductSumDTO> summaryProduct(LongtermVersionGroupDO groupDO) {
        if (groupDO == null) {
            return new ArrayList<>();
        }
        LongtermVersionGroupRecordDO latestRecordDO = demandDBHelper.getOne(LongtermVersionGroupRecordDO.class,
                "where version_group_id=? order by id desc", groupDO.getId());
        if (latestRecordDO == null) {
            return new ArrayList<>();
        }

        String sql = ORMUtils.getSql("/sql/longterm/get_yearly_product_summary_data.sql");
        Map<String, Object> params = new HashMap<>();
        params.put("versionGroupRecordId", latestRecordDO.getId());
        List<YearlyProductSumRow> rows = demandDBHelper.getRaw(YearlyProductSumRow.class, sql, params);

        if ("V_202503".equals(groupDO.getVersionCode())){
            // 202503 特殊处理 仅发送GPU数据的MOA
            rows.removeIf(o -> !"GPU(裸金属&CVM)".equals(o.getProduct()));
        }

        List<YearlyProductSumDTO> result = new ArrayList<>();
        Map<Integer, List<YearlyProductSumRow>> years = ListUtils.toMapList(rows, YearlyProductSumRow::getDemandYear, o -> o);
        for (Map.Entry<Integer, List<YearlyProductSumRow>> year : years.entrySet()) {
            YearlyProductSumDTO dto = new YearlyProductSumDTO();
            dto.setYear(year.getKey());
            dto.setProducts(ListUtils.transform(year.getValue(), YearlyProductSumRow::toItem));
            ListUtils.sortAscNullFirst(dto.getProducts(), o -> {
                if (o.getProduct() == null) {
                    return 99;
                } else if (o.getProduct().startsWith("CVM")) {
                    return 1;
                } else if (o.getProduct().startsWith("GPU")) {
                    return 2;
                } else if (o.getProduct().startsWith("裸金属")) {
                    return 3;
                } else {
                    return 4;
                }
            });
            result.add(dto);
        }

        ListUtils.sortAscNullFirst(result, YearlyProductSumDTO::getYear);
        return result;
    }

    @Override
    public void sendTodoAndMoa(LongtermVersionGroupDO groupDO) {

        LongtermVersionDO longtermVersionDO = demandDBHelper.getOne(LongtermVersionDO.class, "where version_code = ?",
                groupDO.getVersionCode());
        String yearDemandRange = longtermVersionDO.getDemandBeginYear() + "年 ～ " + longtermVersionDO.getDemandEndYear() + "年";
        String summary = generateGroupSummaryForMoaAudit(groupDO);
        List<TodoService.ListView> listViewList = Arrays.asList(
                new TodoService.ListView("行业部门", groupDO.getIndustryDept()),
                new TodoService.ListView("业务分组", groupDO.getBizGroup()),
                new TodoService.ListView("录入范围", yearDemandRange),
                new TodoService.ListView("审批节点", LongtermVersionGroupStatusEnum.getNameByCode(groupDO.getStatus())),
                new TodoService.ListView("本周期资源量", summary));

        // 原本想用上面有含义的来表示这个orderId,也能直接查取，结果发现要表示出来实在是太长了。。。
        // 待办的数据表长度都只有50，可能会撑爆，不好直接改别人的,因此直接用uuid
        String orderId = UUID.randomUUID().toString();
        ApprovalData approvalData = new ApprovalData();
        approvalData.setOrderId(orderId);
        approvalData.setTaskId(orderId);
        approvalData.setSourceApp("行业中长期预测");
        approvalData.setSourceEvent("审批");
        approvalData.setSourceAppCnName("行业中长期预测");
        approvalData.setActivity("中长期分组待审批");
        approvalData.setHandler(groupDO.getCurrentProcessor());
        approvalData.setFormUrl(domainSupplier.get() + "/long-term/approval/" + groupDO.getId().toString());
        approvalData.setIsCallBackApi(0);
        approvalData.setIsPushMyoa(1);
        approvalData.setCallBackUrl("http://localhost/cloud-demand-app/api/NOT-VALID");
        approvalData.setListView(listViewList);
        approvalData.setSystem("CRP");
        approvalData.setIsRemind(0);

        executor.execute(() -> {
            pplCommonService.createTodo(approvalData);
        });

        // 记录日志
        LongTermTodoRecordDO longTermTodoRecordDO = new LongTermTodoRecordDO();
        longTermTodoRecordDO.setVersionId(longtermVersionDO.getId());
        longTermTodoRecordDO.setVersionGroupId(groupDO.getId());
        longTermTodoRecordDO.setNodeCode(groupDO.getStatus());
        longTermTodoRecordDO.setIndustryDept(groupDO.getIndustryDept());
        longTermTodoRecordDO.setBizGroup(groupDO.getBizGroup());
        longTermTodoRecordDO.setDetail(listViewList.toString());
        longTermTodoRecordDO.setBizKey(orderId);
        longTermTodoRecordDO.setAcceptUser(groupDO.getCurrentProcessor());
        longTermTodoRecordDO.setPushStatus(1);
        demandDBHelper.insert(longTermTodoRecordDO);
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    @TaskLog(taskName = "long_term_acceptTodoCallback")
    public void acceptTodoCallback(ApprovalMessageBody messageBody) {
        boolean pass = (messageBody.getApproveResult() == 0);
        LongTermTodoRecordDO longTermTodoRecordDO = demandDBHelper.getOne(LongTermTodoRecordDO.class, "where biz_key = ? and push_status = 1",
                messageBody.getApproverOrder());
        if (longTermTodoRecordDO == null){
            return;
        }
        LongtermVersionGroupDO groupDO = demandDBHelper.getOne(LongtermVersionGroupDO.class, "where id = ?",
                longTermTodoRecordDO.getVersionGroupId());
        if (groupDO == null) {
            return;
        }
        LongtermVersionGroupRecordDO latestRecordDO = demandDBHelper.getOne(LongtermVersionGroupRecordDO.class,
                "where version_group_id=? order by id desc", longTermTodoRecordDO.getVersionGroupId());
        if (latestRecordDO == null) {
            return;
        }
//        if (!Objects.equals(latestRecordDO.getId(), longTermTodoRecordDO.getId())) {
//            return; // 审批节点已经在其它地方处理过了，这里就不再处理了
//        }

        OperateLongtermAuditReq req = new OperateLongtermAuditReq();
        req.setGroupId(groupDO.getId());
        req.setGroupRecordId(latestRecordDO.getId());
        req.setApproveResult(pass);
        req.setApproveNote(messageBody.getApproveMemo());
        req.setOperateUserByMoa(messageBody.getApprover());

        try {
            approvalService.operateLongtermAudit(req);
        } catch (Exception e){
            longTermTodoRecordDO.setErrorMessage(e.getMessage());
            longTermTodoRecordDO.setPushStatus(3);
            demandDBHelper.update(latestRecordDO);
            AlarmRobotUtil.doAlarm("acceptTodoCallback","中长期待办回调异常"+ e.getMessage(),null,false);
            throw e;
        }
    }

    @Override
    public void completeTodo(Long versionGroupId, String completeGroupStatus,String operateUser) {
        LongTermTodoRecordDO record = demandDBHelper.getOne(LongTermTodoRecordDO.class,
                "where version_group_id = ? and node_code = ? and push_status = 1", versionGroupId,
                completeGroupStatus);
        if (record == null){
            return;
        }
        record.setPushStatus(2);
        try {
            todoService.callTodoSubmitTaskResult(record.getBizKey(), operateUser,
                    Boolean.TRUE, "用户已审批，完成待办");
        } catch (Exception e) {
            log.error("回调待办中心失败，orderId:{}", record.getBizKey(), e);
            record.setPushStatus(3);
            record.setErrorMessage(e.getMessage());
        }
        demandDBHelper.update(record);
    }

    @Override
    public void completeVersionAllTodo(String versionCode) {
        LongtermVersionDO longtermVersionDO = demandDBHelper.getOne(LongtermVersionDO.class, "where version_code = ?", versionCode);
        if (longtermVersionDO == null){
            return;
        }
        List<LongTermTodoRecordDO> all = demandDBHelper.getAll(LongTermTodoRecordDO.class,
                "where version_id = ? and push_status = 1", longtermVersionDO.getId());
        if (ListUtils.isEmpty(all)){
            return;
        }
        for (LongTermTodoRecordDO record : all) {
            executor.execute(() -> {
                record.setPushStatus(2);
                try {
                    todoService.callTodoSubmitTaskResult(record.getBizKey(), "system",
                            Boolean.TRUE, "到达审批截止时间，完成待办");
                } catch (Exception e) {
                    log.error("回调待办中心失败，orderId:{}", record.getBizKey(), e);
                    record.setPushStatus(3);
                    record.setErrorMessage(e.getMessage());
                }
                demandDBHelper.update(record);
            });
        }
    }

    @Override
    public VersionGroupProgressResp queryGroupProgress(Long groupId) {
        LongtermVersionGroupDO groupDO = demandDBHelper.getOne(LongtermVersionGroupDO.class, "where id = ?", groupId);
        LongtermVersionGroupRecordDO firstNode = demandDBHelper.getRawOne(LongtermVersionGroupRecordDO.class,
                "select * from longterm_version_group_record"
                        + " where id in (select max(id) from longterm_version_group_record "
                        + " where deleted = 0 and version_group_id = ? and version_group_node_code in (?))", groupId,
                Arrays.asList(LongtermVersionGroupStatusEnum.CREATE.getCode(),
                        LongtermVersionGroupStatusEnum.IN_SUBMIT.getCode(),
                        LongtermVersionGroupStatusEnum.REJECT.getCode()));
        if (groupDO == null || firstNode == null){
            throw new BizException("数据异常，请联系oliverychen解决");
        }
        List<String> nodeCodeList =
                LongtermVersionGroupStatusEnum.getNodeCodeList(groupDO, Objects.requireNonNull(
                        LongtermVersionGroupStatusEnum.getByCode(firstNode.getVersionGroupNodeCode())));

        // 查每个节点的最新一次记录
        List<LongtermVersionGroupRecordDO> all = demandDBHelper.getRaw(LongtermVersionGroupRecordDO.class,
                "select * from longterm_version_group_record where deleted = 0 and id in ("
                        + " select max(id) from longterm_version_group_record where deleted = 0 and"
                        + " version_group_id = ? group by version_group_node_code)",groupId);
        Map<String, LongtermVersionGroupRecordDO> nodeCodeMap = all.stream()
                .collect(Collectors.toMap(LongtermVersionGroupRecordDO::getVersionGroupNodeCode, v -> v));

        VersionGroupProgressResp resp = new VersionGroupProgressResp();
        List<Node> nodeList = new ArrayList<>();
        Boolean flag = Boolean.TRUE;
        for (String nodeCode : nodeCodeList) {
            // 如果等于当前状态 设置为FALSE 后面的全部未完成
            if (nodeCode.equals(groupDO.getStatus())){
                flag = Boolean.FALSE;
            }
            Node node = new Node();
            node.setNodeName(LongtermVersionGroupStatusEnum.getFinishNameByCode(nodeCode));
            node.setNodeCode(nodeCode);
            if (flag) {
                node.setIsDone(Boolean.TRUE);
                LongtermVersionGroupRecordDO longtermVersionGroupRecordDO = nodeCodeMap.get(nodeCode);
                if (longtermVersionGroupRecordDO != null) {
                    node.setOperateUser(longtermVersionGroupRecordDO.getOperateUser());
                    node.setTime(longtermVersionGroupRecordDO.getApproveEndTime());
                    node.setApproveNote(longtermVersionGroupRecordDO.getApproveNote());
                }
            } else {
                node.setIsDone(Boolean.FALSE);
            }
            nodeList.add(node);
        }
        resp.setNodeList(nodeList);

        if (groupDO.getStatus().equals(LongtermVersionGroupStatusEnum.REJECT.getCode())) {
            List<LongtermVersionGroupRecordDO> rejectList = all.stream()
                    .filter(v -> v.getApproveResult() != null && v.getApproveResult().equals("审批驳回")).collect(Collectors.toList());
            if(ListUtils.isNotEmpty(rejectList)){
                LongtermVersionGroupRecordDO rejectRecord = rejectList.get(0);
                Node rejectNode = new Node();
                rejectNode.setNodeCode(rejectRecord.getVersionGroupNodeCode());
                rejectNode.setNodeName(LongtermVersionGroupStatusEnum.getNameByCode(rejectRecord.getVersionGroupNodeCode()));
                rejectNode.setTime(rejectRecord.getApproveEndTime());
                rejectNode.setOperateUser(rejectRecord.getOperateUser());
                rejectNode.setApproveNote(rejectRecord.getApproveNote());
                resp.setRejectNode(rejectNode);
            }
        }
        return resp;
    }

    @Override
    public List<LongtermVersionGroupRecordItemDO> queryItemList(Long groupId) {
        return demandDBHelper.getAll(LongtermVersionGroupRecordItemDO.class,
                "where version_group_record_id in (select max(id) from longterm_version_group_record"
                        + " where deleted = 0 and version_group_id = ? )", groupId);
    }

    private List<LongtermVersionGroupRecordDO> initGroupFirstRecord(List<LongtermVersionGroupDO> groups) {
        return ListUtils.transform(groups, group -> {
            LongtermVersionGroupRecordDO recordDO = new LongtermVersionGroupRecordDO();
            recordDO.setVersionCode(group.getVersionCode());
            recordDO.setVersionGroupId(group.getId());
            recordDO.setVersionGroupNodeCode(LongtermVersionGroupStatusEnum.CREATE.getCode());
            recordDO.setVersionGroupName(LongtermVersionGroupStatusEnum.CREATE.getName());
            recordDO.setApproveStartTime(new Date());
            recordDO.setOperateUser("system");
            return recordDO;
        });
    }


    private List<LongtermVersionGroupRecordItemVO> merge(LongtermVersionDO versionDO,
            List<LongtermVersionGroupRecordItemVO> previousItems,
            List<LongtermPpl13weekDataDTO> ppl13WeekData,
            String previousVersionCode) {

        // 因为下面的处理过程会修改List<LongtermVersionGroupRecordItemVO>，所以复制一份
        List<LongtermVersionGroupRecordItemVO> longtermItems = JSON.clone(previousItems, LongtermVersionGroupRecordItemVO.class);
        longtermItems.forEach(o -> o.setAutomateNote("继承自" + previousVersionCode));

        // 1. 先把13周的原样转成item
        YearMonth ppl13weekStartYearMonth = versionDO.parsePpl13weekStartYearMonth();
        List<LongtermVersionGroupRecordItemVO> ppl13s = ListUtils.transform(ppl13WeekData, o -> LongtermPpl13weekDataDTO.toItem(o, ppl13weekStartYearMonth));
        List<LongtermVersionGroupRecordItemVO> result = new ArrayList<>(ppl13s);
        // 1.1 拆分按年录入到按季度录入（如有）因为下面要根据季度做对冲
        splitYearToQuarter(versionDO, ppl13WeekData, longtermItems);

        // 2. 再将上一版的数据，减去ppl13周的量，再把和ppl13周重叠的月拆月，规则：按必填字段必须相同，选填字段可以不同的规则进行合并
        // 2.1 top客户
        mergeTopCustomers(longtermItems, ppl13WeekData);
        // 2.2 非top客户且非待定客户
        mergeNonTopCustomers(longtermItems, ppl13WeekData);
        // 2.3 待定客户：客户名称为空或者叫待定，这部分逻辑要看同一个Q中，处理完后的量和原来的中长期比，是否比原来的大
        handleUnknownCustomer(previousItems, longtermItems, ppl13WeekData);

        // 3.2 拆分和13周重叠的Q变成月，不要含13周的月（特别的，如果整个Q都是13周，那么原来的中长期该Q废弃）
        splitQuarterToMonth(versionDO, ppl13WeekData, longtermItems);

        result.addAll(longtermItems); // 这里不要去掉core或gpu=0的数据（保持为deleted），方便排查问题
        return result;
    }

    /**查找13周范围内的季度和月份的映射关系*/
    private Map<String, Set<Integer>> getPpl13weekQuarterToMonths(LongtermVersionDO versionDO) {
        LocalDate begin = LocalDate.of(versionDO.getDemandBeginYear(), versionDO.getDemandBeginMonth(), 1);
        YearMonth end = versionDO.parsePpl13weekEndYearMonth();
        if (end == null) {
            throw BizException.makeThrow("ppl13weekEndYearMonth is null");
        }
        List<YearQuarterMonth> quarterMonths = new ArrayList<>();
        while(DateUtils.format(begin, "yyyy-MM").compareTo(end.toString()) <= 0) {
            YearQuarterMonth yqm = new YearQuarterMonth();
            yqm.setYear(begin.getYear());
            yqm.setQuarter((begin.getMonthValue() + 2) / 3);
            yqm.setMonth(begin.getMonthValue());
            quarterMonths.add(yqm);

            begin = begin.plusMonths(1);
        }

        return ListUtils.toMapSet(quarterMonths,
                o -> StringTools.join("@", o.getYear(), o.getQuarter()), YearQuarterMonth::getMonth);
    }

    private void appendAutomateNote(LongtermVersionGroupRecordItemDO item, String note) {
        if (StringTools.isNotBlank(item.getAutomateNote())) {
            if (StringTools.isNotBlank(note)) {
                item.setAutomateNote(item.getAutomateNote() + ";" + note);
            }
        } else {
            item.setAutomateNote(note);
        }
    }

    private void splitYearToQuarter(LongtermVersionDO versionDO,
                                    List<LongtermPpl13weekDataDTO> ppl13WeekData,
                                    List<LongtermVersionGroupRecordItemVO> longtermItems) {

        // 获取本版本录入的所有年份，包括：常规录入，35年录入
        List<Integer> allVersionDemandYears = versionDO.getAllDemandYears();
        // 不是在本版本录入的年份的预测剔除
        longtermItems.removeIf(item -> !allVersionDemandYears.contains(item.getDemandYear()));

        // 获取山版本按年录入的所有年份
        Set<Integer> yearsByYear = longtermItems.stream()
                .filter(o -> LongtermVersionGroupItemTimeUnitEnum.YEAR.getCode().equals(o.getTimeUnit()))
                .map(LongtermVersionGroupRecordItemVO::getDemandYear)
                .collect(Collectors.toSet());
        if (ListUtils.isEmpty(yearsByYear)) {
            log.info("上版本不存在按照年份录入的数据");
            return;
        }

        // 获取上版本按年录入的年份中，已经滚动到了常规录入的年份
        List<Integer> versionDONormalDemandYears = versionDO.getNormalDemandYears();
        List<Integer> needSplitYears = yearsByYear.stream()
                .filter(versionDONormalDemandYears::contains)
                .collect(Collectors.toList());
        if (ListUtils.isEmpty(needSplitYears)) {
            log.info("上版本按年份录入的年份中，没有已经滚动到了常规录入的年份");
            return;
        }

        Iterator<LongtermVersionGroupRecordItemVO> iterator = longtermItems.iterator();
        List<LongtermVersionGroupRecordItemVO> tempAddList = new ArrayList<>();
        while (iterator.hasNext()) {
            LongtermVersionGroupRecordItemVO item = iterator.next();
            if (item.getTimeUnit().equals(LongtermVersionGroupItemTimeUnitEnum.YEAR.getCode())
                    && needSplitYears.contains(item.getDemandYear())) {
                // 移除年度数据
                iterator.remove();
                if (item.getCoreNum().compareTo(BigDecimal.ZERO) == 0 && item.getGpuNum().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                } else {
                    // 拆分季度
                    for (int i = 1; i <= 4; i++) {
                        LongtermVersionGroupRecordItemVO newItem = new LongtermVersionGroupRecordItemVO();
                        BeanUtils.copyProperties(item, newItem);
                        // 年份不变，给上季度信息，月份数据保持为0
                        newItem.setDemandQuarter(i);
                        newItem.setTimeUnit(LongtermVersionGroupItemTimeUnitEnum.QUARTER.getCode());
                        newItem.setCoreNum(item.getCoreNum().divide(BigDecimal.valueOf(4)));
                        newItem.setGpuNum(item.getGpuNum().divide(BigDecimal.valueOf(4)));
                        newItem.setAutomateNote(item.getAutomateNote() + ";" + "继承年度数据拆分到季度");
                        tempAddList.add(newItem);
                    }
                }
            }
        }
        longtermItems.addAll(tempAddList);


    }

    private void splitQuarterToMonth(LongtermVersionDO versionDO,
            List<LongtermPpl13weekDataDTO> ppl13WeekData,
            List<LongtermVersionGroupRecordItemVO> longtermItems) {
        Map<String, Set<Integer>> quarterMonths = getPpl13weekQuarterToMonths(versionDO);

        for (Map.Entry<String, Set<Integer>> e : quarterMonths.entrySet()) {
            int year = Integer.parseInt(e.getKey().split("@")[0]);
            int quarter = Integer.parseInt(e.getKey().split("@")[1]);
            if (e.getValue().size() == 3) { // 整个Q都是13周，此时删除lognterms该Q的数据
                longtermItems.forEach(o -> {
                    if (o.getDemandQuarter() == quarter && o.getDemandYear() == year) {
                        appendAutomateNote(o, "因" + quarter + "季度整季度为13周范围，故由上期" + o.getCoreNum() + "核设置为0"
                                + (o.getGpuNum().intValue() > 0 ? "," + o.getGpuNum() + "卡设置为0" : ""));
                        o.setCoreNum(BigDecimal.ZERO);
                        o.setGpuNum(BigDecimal.ZERO);
                        o.setDeleted(true);
                    }
                });
            } else if (e.getValue().size() == 2) {
                List<Integer> m = negate(quarter, e.getValue());
                if (m.size() != 1) {
                    throw new BizException(
                            "拆解季节失败，月份异常，quarter:" + quarter + ",13周月份:" + JSON.toJson(e.getValue()));
                }
                Integer month = m.get(0);
                longtermItems.forEach(o -> {
                    if (o.getDemandQuarter() == quarter && o.getDemandYear() == year) {
                        o.setDemandMonth(month); // 全部归为这个月份
                        o.setSourceType(LongtermVersionGroupItemSourceTypeEnum.LONGTERM_INPUT.getCode()); // 全部归为13周录入，因此此时已经没有办法区分是13周录入还是非13周录入，在上一版两者就已经耦合在一起了
                        o.setTimeUnit(LongtermVersionGroupItemTimeUnitEnum.MONTH.getCode());
                        appendAutomateNote(o, "因" + quarter + "季度有2个月为13周范围，故上期录入从" + quarter + "季度设置为" + month + "月");
                    }
                });
            } else if (e.getValue().size() == 1){ // size == 1
                List<Integer> m = negate(quarter, e.getValue());
                if (m.size() != 2) {
                    throw new BizException(
                            "拆解季节失败，月份异常，quarter:" + quarter + ",13周月份:" + JSON.toJson(e.getValue()));
                }
                List<LongtermVersionGroupRecordItemVO> newItems = new ArrayList<>();
                longtermItems.forEach(o -> {
                    if (o.getDemandQuarter() == quarter && o.getDemandYear() == year) { // 一拆为二，不管是不是季节，都平分到最后2个月份，且类型为中长期录入
                        BigDecimal coreOri = o.getCoreNum();
                        BigDecimal gpuOri = o.getGpuNum();
                        if (coreOri.compareTo(BigDecimal.ZERO) == 0 && gpuOri.compareTo(BigDecimal.ZERO) == 0) {
                            o.setDeleted(true);
                            return;
                        }

                        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);

                        o.setSourceType(LongtermVersionGroupItemSourceTypeEnum.LONGTERM_INPUT.getCode()); // 因上一版已耦合，此处无法再区分13周录入和非13周录入
                        o.setDemandMonth(m.get(0));
                        o.setTimeUnit(LongtermVersionGroupItemTimeUnitEnum.MONTH.getCode());

                        o.setCoreNum(o.getCoreNum().divide(BigDecimal.valueOf(2)));
                        o.setGpuNum(BigDecimal.valueOf(o.getGpuNum().intValue() / 2));

                        LongtermVersionGroupRecordItemVO newItem = JSON.clone(o);
                        newItem.setId(null);
                        newItem.setDemandMonth(m.get(1));
                        newItem.setCoreNum(coreOri.subtract(o.getCoreNum()));
                        newItem.setGpuNum(gpuOri.subtract(o.getGpuNum()));

                        Integer week13Month = e.getValue().iterator().next();
                        appendAutomateNote(o,  "因" + quarter + "季度" + week13Month + "月为13周范围，故上期录入" + quarter
                                + "季度平拆为" + m.get(0) + "月" + m.get(1) + "月,数量由" + coreOri + "核调整为" + o.getCoreNum() + "核，"
                                + gpuOri + "卡调整为" + o.getGpuNum() + "卡" +
                                "(" + uuid + ")");
                        appendAutomateNote(newItem, "因" + quarter + "季度" + week13Month + "月为13周范围，故上期录入" + quarter
                                + "季度平拆为" + m.get(0) + "月" + m.get(1) + "月，此条为拆分新增(" + uuid + ")");
                        newItems.add(newItem);
                    }
                });
                longtermItems.addAll(newItems);
            }
        }
    }

    private List<Integer> negate(int quarter, Set<Integer> months) {
        List<Integer> allMonths = new ArrayList<>();
        switch (quarter) {
            case 1:
                allMonths.addAll(Arrays.asList(1, 2, 3));
                break;
            case 2:
                allMonths.addAll(Arrays.asList(4, 5, 6));
                break;
            case 3:
                allMonths.addAll(Arrays.asList(7, 8, 9));
                break;
            case 4:
                allMonths.addAll(Arrays.asList(10, 11, 12));
                break;
        }
        return ListUtils.subtract(allMonths, ListUtils.toList(months));
    }

    private void mergeTopCustomers(List<LongtermVersionGroupRecordItemVO> longtermItems,
            List<LongtermPpl13weekDataDTO> ppl13WeekData) {
        List<LongtermVersionGroupRecordItemVO> matchedPreviousItems = ListUtils.filter(longtermItems,
                o -> Objects.equals(o.getIsTopCustomer(), 1));
        List<LongtermPpl13weekDataDTO> matchedPpl13WeekData = ListUtils.filter(ppl13WeekData,
                o -> Objects.equals(o.getIsTopCustomer(), 1));

        // top客户，必填字段为：客户简称、境内外、地域、可用区、实例族、实例类型、需求类型，按季度处理
        Map<String, List<LongtermVersionGroupRecordItemVO>> previousItemsMap = ListUtils.toMapList(matchedPreviousItems,
                o -> StringTools.join("@", o.getCustomerShortName(),
                        o.getCustomhouseTitle(), o.getRegionName(), o.getZoneName(), o.getInstanceFamily(),
                        o.getInstanceType(),
                        o.getDemandType(), o.getDemandYear(), o.getDemandQuarter()), o -> o);
        Map<String, List<LongtermPpl13weekDataDTO>> ppl13WeekDataMap = ListUtils.toMapList(matchedPpl13WeekData,
                o -> StringTools.join("@", o.getCommonCustomerShortName(),
                        o.getCustomhouseTitle(), o.getRegionName(), o.getZoneName(), o.getInstanceFamily(),
                        o.getInstanceType(),
                        o.getDemandType(), o.getYear(), o.getQuarter()), o -> o);

        doMerge(previousItemsMap, ppl13WeekDataMap);
    }

    private void mergeNonTopCustomers(List<LongtermVersionGroupRecordItemVO> longtermItems,
            List<LongtermPpl13weekDataDTO> ppl13WeekData) {
        List<LongtermVersionGroupRecordItemVO> matchedPreviousItems = ListUtils.filter(longtermItems,
                o -> Objects.equals(o.getIsTopCustomer(), 0)
                        && StringTools.isNotBlank(o.getCustomerShortName())
                        && !LongtermConstants.UNKNOWN_CUSTOMER_NAME.equals(o.getCustomerShortName()));
        List<LongtermPpl13weekDataDTO> matchedPpl13WeekData = ListUtils.filter(ppl13WeekData,
                o -> Objects.equals(o.getIsTopCustomer(), 0)
                        && StringTools.isNotBlank(o.getCommonCustomerShortName())
                        && !LongtermConstants.UNKNOWN_CUSTOMER_NAME.equals(o.getCommonCustomerShortName()));

        // 非top客户且非待定客户，必填字段为：客户简称、境内外、地域、实例族、需求类型，按季度处理
        Map<String, List<LongtermVersionGroupRecordItemVO>> previousItemsMap = ListUtils.toMapList(matchedPreviousItems,
                o -> StringTools.join("@", o.getCustomerShortName(),
                        o.getCustomhouseTitle(), o.getRegionName(), o.getInstanceFamily(),
                        o.getDemandType(), o.getDemandYear(), o.getDemandQuarter()), o -> o);
        Map<String, List<LongtermPpl13weekDataDTO>> ppl13WeekDataMap = ListUtils.toMapList(matchedPpl13WeekData,
                o -> StringTools.join("@", o.getCommonCustomerShortName(),
                        o.getCustomhouseTitle(), o.getRegionName(), o.getInstanceFamily(),
                        o.getDemandType(), o.getYear(), o.getQuarter()), o -> o);

        doMerge(previousItemsMap, ppl13WeekDataMap);
    }

    private void handleUnknownCustomer(
            List<LongtermVersionGroupRecordItemVO> previousItems /*这个是原始的数据*/,
            List<LongtermVersionGroupRecordItemVO> longtermItems,
            List<LongtermPpl13weekDataDTO> ppl13WeekData) {

        Map<String, List<LongtermVersionGroupRecordItemVO>> previousItemsMap = ListUtils.toMapList(previousItems,
                o -> StringTools.join("@", o.getDemandYear(), o.getDemandQuarter(), o.getDemandType()),
                o -> o);

        List<LongtermVersionGroupRecordItemVO> longtermKnownItems = ListUtils.filter(longtermItems,
                o -> StringTools.isNotBlank(o.getCustomerShortName())
                        && !LongtermConstants.UNKNOWN_CUSTOMER_NAME.equals(o.getCustomerShortName()));
        List<LongtermVersionGroupRecordItemVO> longtermUnknownItems = ListUtils.filter(longtermItems,
                o -> StringTools.isBlank(o.getCustomerShortName())
                        || LongtermConstants.UNKNOWN_CUSTOMER_NAME.equals(o.getCustomerShortName()));

        Map<String, List<LongtermVersionGroupRecordItemVO>> longtermKnownItemsMap = ListUtils.toMapList(
                longtermKnownItems,
                o -> StringTools.join("@", o.getDemandYear(), o.getDemandQuarter(), o.getDemandType()),
                o -> o);
        Map<String, List<LongtermVersionGroupRecordItemVO>> longtermUnknownItemsMap = ListUtils.toMapList(
                longtermUnknownItems,
                o -> StringTools.join("@", o.getDemandYear(), o.getDemandQuarter(), o.getDemandType()),
                o -> o);
        Map<String, List<LongtermPpl13weekDataDTO>> ppl13WeekDataMap = ListUtils.toMapList(ppl13WeekData,
                o -> StringTools.join("@", o.getYear(), o.getQuarter(), o.getDemandType()),
                o -> o);

        // ppl13WeekDataMap + longtermKnowItems >= previousItemsMap 则不保留longtermUnknownItems
        for (Map.Entry<String, List<LongtermVersionGroupRecordItemVO>> e : longtermUnknownItemsMap.entrySet()) {
            String key = e.getKey();
            String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
            // cpu core
            BigDecimal core = NumberUtils.sum(ppl13WeekDataMap.get(key), LongtermPpl13weekDataDTO::getTotalCore)
                    .add(NumberUtils.sum(longtermKnownItemsMap.get(key), LongtermVersionGroupRecordItemVO::getCoreNum));
            BigDecimal originCore = NumberUtils.sum(previousItemsMap.get(key), LongtermVersionGroupRecordItemVO::getCoreNum);
            BigDecimal needFillCore = originCore.subtract(core); // 需要保留的待定客户的量
            for (LongtermVersionGroupRecordItemVO d : e.getValue()) {
                if (d.getCoreNum().compareTo(BigDecimal.ZERO) > 0) {
                    if (needFillCore.compareTo(BigDecimal.ZERO) <= 0) {
                        appendAutomateNote(d, key + "季度本期总量" + core + "核，上期总量" + originCore + "核,故" +
                                "调减待定客户核心数" + d.getCoreNum() + "为0(" + uuid + ")");
                        d.setCoreNum(BigDecimal.ZERO);
                    } else {
                        if (d.getCoreNum().compareTo(needFillCore) > 0) {
                            appendAutomateNote(d, key + "季度本期总量" + core + "核，上期总量" + originCore + "核,故" +
                                    "调减待定客户核心数" + d.getCoreNum() + "为" + needFillCore + "(" + uuid + ")");
                            d.setCoreNum(needFillCore);
                            needFillCore = BigDecimal.ZERO;
                        } else {
                            appendAutomateNote(d, key + "季度本期总量" + core + "核，上期总量" + originCore + "核,故" +
                                    "调减待定客户核心数" + d.getCoreNum() + "为" + d.getCoreNum() + "(" + uuid + ")");
                            needFillCore = needFillCore.subtract(d.getCoreNum());
                        }
                    }
                }
            }

            // gpu card
            int gpuNum = NumberUtils.sum(ppl13WeekDataMap.get(key), LongtermPpl13weekDataDTO::getTotalGpuNum).intValue()
                    + NumberUtils.sum(longtermKnownItemsMap.get(key), LongtermVersionGroupRecordItemVO::getGpuNum).intValue();
            int originGpuNum = NumberUtils.sum(previousItemsMap.get(key), LongtermVersionGroupRecordItemVO::getGpuNum).intValue();
            int needFillGpuNum = originGpuNum - gpuNum; // 需要保留的待定客户的量
            for (LongtermVersionGroupRecordItemVO d : e.getValue()) {
                if (d.getGpuNum().compareTo(BigDecimal.ZERO)  > 0) {
                    if (needFillGpuNum <= 0) {
                        appendAutomateNote(d, key + "季度新总量" + gpuNum + "，原始总量" + originGpuNum + "，" +
                                "调减待定客户GPU数量" + d.getGpuNum() + "为0(" + uuid + ")");
                        d.setGpuNum(BigDecimal.ZERO);
                    } else {
                        if (d.getGpuNum().intValue() >= needFillGpuNum) {
                            appendAutomateNote(d, key + "季度新总量" + gpuNum + "，原始总量" + originGpuNum + "，" +
                                    "调减待定客户GPU数量" + d.getGpuNum() + "为" + needFillGpuNum + "(" + uuid + ")");
                            d.setGpuNum(BigDecimal.valueOf(needFillGpuNum));
                            needFillGpuNum = 0;
                        } else {
                            appendAutomateNote(d, key + "季度新总量" + gpuNum + "，原始总量" + originGpuNum + "，" +
                                    "调减待定客户GPU数量" + d.getGpuNum() + "为" + d.getGpuNum() + "(" + uuid + ")");
                            needFillGpuNum -= d.getGpuNum().intValue();
                        }
                    }
                }
            }
        }
    }

    private void doMerge(Map<String, List<LongtermVersionGroupRecordItemVO>> previousItemsMap,
            Map<String, List<LongtermPpl13weekDataDTO>> ppl13WeekDataMap) {
        for (Map.Entry<String, List<LongtermVersionGroupRecordItemVO>> e : previousItemsMap.entrySet()) {
            String key = e.getKey();
            String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
            List<LongtermPpl13weekDataDTO> ppl13weekData = ppl13WeekDataMap.get(key);
            if (ListUtils.isEmpty(ppl13weekData)) { //  没有13周就不用处理了
                continue;
            }

            BigDecimal ppl13weekTotalCore = NumberUtils.sum(ppl13weekData, LongtermPpl13weekDataDTO::getTotalCore);
            BigDecimal ppl13weekTotalCoreOri = ppl13weekTotalCore;
            BigDecimal ppl13weekTotalGpuNum = NumberUtils.sum(ppl13weekData, LongtermPpl13weekDataDTO::getTotalGpuNum);
            BigDecimal ppl13weekTotalGpuNumOri = ppl13weekTotalGpuNum;
            String pplIds = StringTools.join(",", ListUtils.transform(ppl13weekData, LongtermPpl13weekDataDTO::getPplIds));

            // 尽量按月份顺序扣减
            List<LongtermVersionGroupRecordItemVO> value = e.getValue();
            ListUtils.sortAscNullFirst(value, LongtermVersionGroupRecordItemDO::getDemandYear, LongtermVersionGroupRecordItemDO::getDemandMonth);
            for (LongtermVersionGroupRecordItemVO item : value) {
                if (ppl13weekTotalCore.compareTo(BigDecimal.ZERO) == 0 && ppl13weekTotalGpuNum.compareTo(BigDecimal.ZERO) == 0) { // 扣完了，不需要再扣了
                    break;
                }

                String automateNote = "";

                if (ppl13weekTotalCore.compareTo(BigDecimal.ZERO) > 0) {
                    if (ppl13weekTotalCore.compareTo(item.getCoreNum()) >= 0) {
                        automateNote += "本期13周总" + ppl13weekTotalCoreOri +"核,故上期从" + item.getCoreNum() + "减到" + 0;
                        ppl13weekTotalCore = ppl13weekTotalCore.subtract(item.getCoreNum());
                        item.setCoreNum(BigDecimal.ZERO);
                    } else {
                        automateNote += "本期13周总" + ppl13weekTotalCoreOri +"核,故上期从" + item.getCoreNum() + "减到" + (item.getCoreNum().subtract(ppl13weekTotalCore));
                        item.setCoreNum(item.getCoreNum().subtract(ppl13weekTotalCore));
                        ppl13weekTotalCore = BigDecimal.ZERO;
                    }
                }
                if (ppl13weekTotalGpuNum.compareTo(BigDecimal.ZERO) > 0) {
                    if (ppl13weekTotalGpuNum.compareTo(item.getGpuNum()) >= 0) {
                        automateNote += ",本期13周总" + ppl13weekTotalGpuNumOri +"卡,故上期从" + item.getGpuNum() + "减到" + 0;
                        ppl13weekTotalGpuNum = ppl13weekTotalGpuNum.subtract(item.getGpuNum());
                        item.setGpuNum(BigDecimal.ZERO);
                    } else {
                        automateNote += ",本期13周总" + ppl13weekTotalGpuNumOri +"卡,故上期从" + item.getGpuNum() + "减到" + (item.getGpuNum().subtract(ppl13weekTotalGpuNum));
                        item.setGpuNum(item.getGpuNum().subtract(ppl13weekTotalGpuNum));
                        ppl13weekTotalGpuNum = BigDecimal.ZERO;
                    }
                }

                if (StringTools.isNotBlank(automateNote)) {
                    automateNote += ",相关ppl:" + pplIds + "(" + uuid + ")";
                }
                appendAutomateNote(item, automateNote);
            }
        }
    }

    /**这里要上一个版本，干预前的item*/
    private List<LongtermVersionGroupRecordItemVO> getPreviousDoneRecordItemDOs(LongtermVersionDO previousDoneVersionDO,
            String currentVersionPpl13weekEndYearMonth) {
        if (previousDoneVersionDO == null) {
            return new ArrayList<>();
        }

        WhereSQL whereSQL = new WhereSQL();

        // 找到上一版本的所有分组
        List<LongtermVersionGroupDO> versionGroups = demandDBHelper.getAll(LongtermVersionGroupDO.class,
                "where version_code=?", previousDoneVersionDO.getVersionCode());

        List<Long> latestRecordIds = new ArrayList<>();
        // 找到每个分组的干预前的最后recordId
        for (LongtermVersionGroupDO groupDO : versionGroups) {
            LongtermVersionGroupRecordDO recordDO = demandDBHelper.getOne(LongtermVersionGroupRecordDO.class,
                    "where version_group_id=? and version_group_node_code in (?) order by id desc",
                    groupDO.getId(),
                    ListUtils.transform(LongtermVersionGroupStatusEnum.getBeforeComdInterveneStatus(), LongtermVersionGroupStatusEnum::getCode));
            if (recordDO != null) {
                latestRecordIds.add(recordDO.getId());
            }
        }

        // case1: 如果某个季度的最后一个月小于等于13周的结束年月，那么不需要比较，直接以13周的为准
        // case2: 如果某个季度部分最后一个月大于13周的结束年月，走原有逻辑，不足的将上季度补齐

        // 因此将不需要参与比较上版本月份，直接过滤掉
        String[] split = currentVersionPpl13weekEndYearMonth.split("-");
        Integer year = Integer.valueOf(split[0]);
        Integer month = Integer.valueOf(split[1]);
        // 计算季度
        Integer quarter = (month - 1) / 3 + 1; // 算出当前月份所在季度
        boolean isLastMonthOfQuarter = (month % 3) == 0; // 当前月份是否是最后一个月

        if (isLastMonthOfQuarter) {
            // 计算下个季度
            int nextQuarter = quarter == 4 ? 1 : quarter + 1;
            int nextYear = quarter == 4 ? year + 1 : year;

            whereSQL.and("demand_year > ? or (demand_year = ? and demand_quarter >= ?)",nextYear,nextYear,nextQuarter);
        } else {
            whereSQL.and("demand_year > ? or (demand_year = ? and demand_quarter >= ?)",year,year,quarter);
        }

        whereSQL.and("version_group_record_id in (?)", latestRecordIds);
        whereSQL.and("source_type in (?)", Arrays.asList(LongtermVersionGroupItemSourceTypeEnum.LONGTERM_INPUT.getCode(),
                LongtermVersionGroupItemSourceTypeEnum.PPL_13_WEEK.getCode()));
        return demandDBHelper.getAll(LongtermVersionGroupRecordItemVO.class,
                whereSQL.getSQL(),whereSQL.getParams());
    }

    @Override
    public List<LongtermPpl13weekDataDTO> getPpl13WeekData(LongtermVersionDO versionDO,
            List<LongtermVersionGroupDO> groups) {

        List<String> topCustomers = getTopCustomers(groups);

        List<PplConfigProductEnumDO> products = demandDBHelper.getAll(PplConfigProductEnumDO.class);
        List<PplConfigProductEnumDO> paasProducts = ListUtils.filter(products,
                product -> "PAAS".equals(product.getFlag()));
        List<PplConfigProductEnumDO> nonPaasProducts = ListUtils.filter(products,
                product -> !"PAAS".equals(product.getFlag()));

        String sql = ORMUtils.getSql("/sql/longterm/get_ppl_13week_data.sql");
        Map<String, Object> params = new HashMap<>();

        if (StringTools.isBlank(versionDO.getPpl13weekVersionCode())) {
            // 没有绑定13周，不处理13周数据
            log.warn("longterm version code {} not bind 13week ppl, will not handle", versionDO.getVersionCode());
            return new ArrayList<>();
        }
        params.put("versionCode", versionDO.getPpl13weekVersionCode());

        YearMonth versionStartYearMonth = versionDO.parseVersionStartYearMonth();
        if (versionStartYearMonth == null) {
            // 13周开始年月不存在则不处理
            log.error("longterm version {} 13week ppl start year month is null, will not hanlde",
                    versionDO.getVersionCode());
            return new ArrayList<>();
        }
        params.put("beginYear", versionStartYearMonth.getYear());
        params.put("beginMonth", versionStartYearMonth.getMonthValue());

        YearMonth ppl13WeekEndYearMonth = versionDO.parsePpl13weekEndYearMonth();
        if (ppl13WeekEndYearMonth == null) {
            // 13周结束年月不存在则不处理
            log.error("longterm version {} 13week ppl end year month is null, will not hanlde",
                    versionDO.getVersionCode());
            return new ArrayList<>();
        }
        params.put("endYear", ppl13WeekEndYearMonth.getYear());
        params.put("endMonth", ppl13WeekEndYearMonth.getMonthValue());
        params.put("topCustomers", topCustomers);
        params.put("paasProduct", ListUtils.transform(paasProducts, PplConfigProductEnumDO::getProductName));
        params.put("nonPaasProduct", ListUtils.transform(nonPaasProducts, PplConfigProductEnumDO::getProductName));

        sql = sql.replace("${INSTANCE_TYPE_2_INSTANCE_FAMILY_CASE_WHEN}", getInstanceTypeToInstanceFamilyCaseWhen());

        return ckcldStdCrpDBHelper.getRaw(LongtermPpl13weekDataDTO.class, sql, params);
    }

    private List<LongtermVersionGroupRecordItemDO> initHistoryScaleDataForInnerDept(LongtermVersionGroupDO group,
            List<String> statTimes, String product) {
        Map<String, Object> baseParams = new HashMap<>();
        WhereSQL baseSQL = new WhereSQL();
        buildBaseWhereSql(baseSQL,baseParams,group,statTimes,product);

        WhereSQL whereSQL = baseSQL.copy();
        Map<String, Object> params = new HashMap<>(baseParams);


        String sql = ORMUtils.getSql("/sql/longterm/history_scale/other_customer.sql");

        sql = sql.replace("${FILTER}", whereToAnd(whereSQL.getSQL()));
        sql = sql.replace("${INSTANCE_TYPE_2_INSTANCE_FAMILY_CASE_WHEN}", getInstanceTypeToInstanceFamilyCaseWhen());

        List<LongTermHistoryScale> raw = ckcldStdCrpDBHelper.getRaw(LongTermHistoryScale.class, sql, params);

        if (ListUtils.isEmpty(raw)){
            return new ArrayList<>();
        }

        List<LongtermVersionGroupRecordItemDO> tempRes = ListUtils.transform(raw, v -> LongtermVersionGroupRecordItemDO.convertFromOther(v, product));
        // 聚合一下后返回
        List<LongtermVersionGroupRecordItemDO> res = new ArrayList<>();
        ListUtils2.groupThenAccept(tempRes, LongtermVersionGroupRecordItemDO::buildOtherCustomerKey, (key, list) -> {
            LongtermVersionGroupRecordItemDO newOne = new LongtermVersionGroupRecordItemDO();
            LongtermVersionGroupRecordItemDO itemDO = list.get(0);
            newOne.setProduct(itemDO.getProduct());
            newOne.setPaasProduct(itemDO.getPaasProduct());
            newOne.setGpuType(itemDO.getGpuType());
            newOne.setInstanceFamily(itemDO.getInstanceFamily());
            newOne.setTimeUnit(itemDO.getTimeUnit());
            newOne.setCustomerShortName(itemDO.getCustomerShortName());
            newOne.setIsTopCustomer(itemDO.getIsTopCustomer());
            newOne.setCustomhouseTitle(itemDO.getCustomhouseTitle());
            newOne.setRegionName(itemDO.getRegionName());
            newOne.setSourceType(itemDO.getSourceType());
            newOne.setIsMigrate(itemDO.getIsMigrate());
            newOne.setDemandYear(itemDO.getDemandYear());
            newOne.setDemandQuarter(itemDO.getDemandQuarter());
            newOne.setDemandMonth(itemDO.getDemandMonth());
            newOne.setDemandType(itemDO.getDemandType());
            newOne.setCoreNum(BigDecimal.ZERO);
            newOne.setGpuNum(BigDecimal.ZERO);
            list.forEach(v -> {
                newOne.setCoreNum(newOne.getCoreNum().add(v.getCoreNum()));
                newOne.setGpuNum(newOne.getGpuNum().add(v.getGpuNum()));
            });
            res.add(newOne);
        });
        return res;
    }


    private List<LongtermVersionGroupRecordItemDO> initHistoryScaleDataByProduct(LongtermVersionGroupDO group,
            List<String> statTimes, String product,
            List<String> topCustomer,List<String> mediumCustomer) {
        Map<String, Object> baseParams = new HashMap<>();
        WhereSQL baseSQL = new WhereSQL();
        buildBaseWhereSql(baseSQL,baseParams,group,statTimes,product);

        List<LongtermVersionGroupRecordItemDO> result = new ArrayList<>();

        result.addAll(getItemByTopCustomer(baseSQL,baseParams,topCustomer,product));
        result.addAll(getItemByMediumCustomer(baseSQL,baseParams,mediumCustomer,product));
        result.addAll(getItemByOtherCustomer(baseSQL,baseParams,topCustomer,mediumCustomer,product));

        return result;
    }


    private List<LongtermVersionGroupRecordItemDO> getItemByTopCustomer(
            WhereSQL baseSQL, Map<String, Object> baseParams,List<String> topCustomer,String product){

        if (ListUtils.isEmpty(topCustomer)) {
            return new ArrayList<>();
        }

        WhereSQL whereSQL = baseSQL.copy();
        Map<String, Object> params = new HashMap<>(baseParams);

        whereSQL.and("customer_short_name in (:customerShortName)");
        params.put("customerShortName", topCustomer);

        String sql = ORMUtils.getSql("/sql/longterm/history_scale/top_customer.sql");

        sql = sql.replace("${FILTER}", whereToAnd(whereSQL.getSQL()));
        sql = sql.replace("${INSTANCE_TYPE_2_INSTANCE_FAMILY_CASE_WHEN}", getInstanceTypeToInstanceFamilyCaseWhen());
        sql = sql.replace("${CUSTOMER_SHORT_NAME_2_COMMON_CUSTOMER_SHORT_NAME_CASE_WHEN}",
                getCustomerShortNameToCommonCustomerShortNameCaseWhen(params.get("industryDept").toString()));
        List<LongTermHistoryScale> raw = ckcldStdCrpDBHelper.getRaw(LongTermHistoryScale.class, sql, params);

        if (ListUtils.isEmpty(raw)){
            return new ArrayList<>();
        }

        // top客户 去重维度和聚合维度是对齐的，直接转换后返回
        return ListUtils.transform(raw, v -> LongtermVersionGroupRecordItemDO.convertFromTop(v, product));
    }

    private static String whereToAnd(String whereSql) {
        if (StringTools.isBlank(whereSql)) {
            return "";
        }
        whereSql = whereSql.trim();
        if (whereSql.toLowerCase().startsWith("where")) {
            whereSql = whereSql.substring("where".length());
        }
        if (StringTools.isNotBlank(whereSql)) {
            whereSql = " AND (" + whereSql + ")";
        }
        return whereSql;
    }

    private List<LongtermVersionGroupRecordItemDO> getItemByMediumCustomer(
            WhereSQL baseSQL, Map<String, Object> baseParams,List<String> mediumCustomer,String product){

        if (ListUtils.isEmpty(mediumCustomer)) {
            return new ArrayList<>();
        }
        WhereSQL whereSQL = baseSQL.copy();
        Map<String, Object> params = new HashMap<>(baseParams);

        whereSQL.and("customer_short_name in (:customerShortName)");
        params.put("customerShortName", mediumCustomer);


        String sql = ORMUtils.getSql("/sql/longterm/history_scale/medium_customer.sql");

        sql = sql.replace("${FILTER}", whereToAnd(whereSQL.getSQL()));
        sql = sql.replace("${INSTANCE_TYPE_2_INSTANCE_FAMILY_CASE_WHEN}", getInstanceTypeToInstanceFamilyCaseWhen());
        sql = sql.replace("${CUSTOMER_SHORT_NAME_2_COMMON_CUSTOMER_SHORT_NAME_CASE_WHEN}",
                getCustomerShortNameToCommonCustomerShortNameCaseWhen(params.get("industryDept").toString()));
        List<LongTermHistoryScale> raw = ckcldStdCrpDBHelper.getRaw(LongTermHistoryScale.class, sql, params);

        if (ListUtils.isEmpty(raw)){
            return new ArrayList<>();
        }

        List<LongtermVersionGroupRecordItemDO> tempRes = ListUtils.transform(raw,
                (v) -> LongtermVersionGroupRecordItemDO.convertFromMedium(v, product)
        );

        // 聚合一下后返回
        List<LongtermVersionGroupRecordItemDO> res = new ArrayList<>();
        ListUtils2.groupThenAccept(tempRes, LongtermVersionGroupRecordItemDO::buildMediumCustomerKey, (key, list) -> {
            LongtermVersionGroupRecordItemDO newOne = new LongtermVersionGroupRecordItemDO();
            LongtermVersionGroupRecordItemDO itemDO = list.get(0);
            newOne.setProduct(itemDO.getProduct());
            newOne.setPaasProduct(itemDO.getPaasProduct());
            newOne.setGpuType(itemDO.getGpuType());
            newOne.setInstanceFamily(itemDO.getInstanceFamily());
            newOne.setTimeUnit(itemDO.getTimeUnit());
            newOne.setCustomerShortName(itemDO.getCustomerShortName());
            newOne.setIsTopCustomer(itemDO.getIsTopCustomer());
            newOne.setCustomhouseTitle(itemDO.getCustomhouseTitle());
            newOne.setRegionName(itemDO.getRegionName());
            newOne.setSourceType(itemDO.getSourceType());
            newOne.setIsMigrate(itemDO.getIsMigrate());
            newOne.setDemandYear(itemDO.getDemandYear());
            newOne.setDemandQuarter(itemDO.getDemandQuarter());
            newOne.setDemandMonth(itemDO.getDemandMonth());
            newOne.setDemandType(itemDO.getDemandType());
            newOne.setCoreNum(BigDecimal.ZERO);
            newOne.setGpuNum(BigDecimal.ZERO);
            list.forEach(v -> {
                newOne.setCoreNum(newOne.getCoreNum().add(v.getCoreNum()));
                newOne.setGpuNum(newOne.getGpuNum().add(v.getGpuNum()));
                    });
            res.add(newOne);
        });
        return res;
    }



    private List<LongtermVersionGroupRecordItemDO> getItemByOtherCustomer(
            WhereSQL baseSQL, Map<String, Object> baseParams,
            List<String> topCustomer,List<String> mediumCustomer,
            String product){

        Set<String> notInCustomerShortName = new HashSet<>();
        notInCustomerShortName.addAll(topCustomer);
        notInCustomerShortName.addAll(mediumCustomer);

        WhereSQL whereSQL = baseSQL.copy();
        Map<String, Object> params = new HashMap<>(baseParams);

        whereSQL.and("customer_short_name not in (:customerShortName)");
        params.put("customerShortName", new ArrayList<>(notInCustomerShortName));


        String sql = ORMUtils.getSql("/sql/longterm/history_scale/other_customer.sql");

        sql = sql.replace("${FILTER}", whereToAnd(whereSQL.getSQL()));
        sql = sql.replace("${INSTANCE_TYPE_2_INSTANCE_FAMILY_CASE_WHEN}", getInstanceTypeToInstanceFamilyCaseWhen());

        List<LongTermHistoryScale> raw = ckcldStdCrpDBHelper.getRaw(LongTermHistoryScale.class, sql, params);

        if (ListUtils.isEmpty(raw)){
            return new ArrayList<>();
        }

        List<LongtermVersionGroupRecordItemDO> tempRes = ListUtils.transform(raw, v -> LongtermVersionGroupRecordItemDO.convertFromOther(v, product));
        // 聚合一下后返回
        List<LongtermVersionGroupRecordItemDO> res = new ArrayList<>();
        ListUtils2.groupThenAccept(tempRes, LongtermVersionGroupRecordItemDO::buildOtherCustomerKey, (key, list) -> {
            LongtermVersionGroupRecordItemDO newOne = new LongtermVersionGroupRecordItemDO();
            LongtermVersionGroupRecordItemDO itemDO = list.get(0);
            newOne.setProduct(itemDO.getProduct());
            newOne.setPaasProduct(itemDO.getPaasProduct());
            newOne.setGpuType(itemDO.getGpuType());
            newOne.setInstanceFamily(itemDO.getInstanceFamily());
            newOne.setTimeUnit(itemDO.getTimeUnit());
            newOne.setCustomerShortName(itemDO.getCustomerShortName());
            newOne.setIsTopCustomer(itemDO.getIsTopCustomer());
            newOne.setCustomhouseTitle(itemDO.getCustomhouseTitle());
            newOne.setRegionName(itemDO.getRegionName());
            newOne.setSourceType(itemDO.getSourceType());
            newOne.setIsMigrate(itemDO.getIsMigrate());
            newOne.setDemandYear(itemDO.getDemandYear());
            newOne.setDemandQuarter(itemDO.getDemandQuarter());
            newOne.setDemandMonth(itemDO.getDemandMonth());
            newOne.setDemandType(itemDO.getDemandType());
            newOne.setCoreNum(BigDecimal.ZERO);
            newOne.setGpuNum(BigDecimal.ZERO);
            list.forEach(v -> {
                newOne.setCoreNum(newOne.getCoreNum().add(v.getCoreNum()));
                newOne.setGpuNum(newOne.getGpuNum().add(v.getGpuNum()));
            });
            res.add(newOne);
        });
        return res;
    }

    private void buildBaseWhereSql(WhereSQL whereSQL,Map<String, Object> params,LongtermVersionGroupDO group,List<String> statTimes,String product){

        whereSQL.and("stat_time in (:statTime)");
        params.put("statTime", statTimes);
        params.put("longTermProduct",product);
        params.put("industryDept", group.getIndustryDept());

        if (!group.getIndustryDept().equals(IndustryDeptEnum.INNER_DEPT.getName())){
            whereSQL.and("industry_dept = :industryDept");

            if (group.getIndustryDept().equals(IndustryDeptEnum.SMART_INDUSTRY_ONE.getName())){
                // 一部只查企业 不查个人
                whereSQL.and("customer_type = 1");
            }
        }

        switch (product) {
            case "CVM&CBS":
                whereSQL.and("product = :product");
                whereSQL.and("app_role not in (:appRole)");
                params.put("product", "CVM");
                params.put("appRole", Arrays.asList(AppRoleEnum.LH.getName(), AppRoleEnum.EMR.getName(),AppRoleEnum.EKS.getName()));
                break;
            case "GPU(裸金属&CVM)":
                whereSQL.and("product = :product");
                params.put("product", "GPU");
                break;
            case "裸金属":
                whereSQL.and("product = :product");
                params.put("product", "裸金属");
                break;
            case "弹性MapReduce":
                whereSQL.and("product = :product");
                whereSQL.and("app_role = :appRole");
                params.put("product", "CVM");
                params.put("appRole",  AppRoleEnum.EMR.getName());
                break;
            case "EKS官网":
                whereSQL.and("product = :product");
                whereSQL.and("app_role = :appRole");
                whereSQL.and("app_id not in (:appId)");
                params.put("product", "CVM");
                params.put("appRole",  AppRoleEnum.EKS.getName());
                params.put("appId",  Arrays.asList("1258344706","1251316161"));
                break;
            case "CSIG容器平台" :
            case "数据湖DLC" :
            case "Elasticsearch Service" :
            case "云数据仓库" :
                whereSQL.and("product = :product");
                whereSQL.and("app_role not in (:appRole)");
                whereSQL.and("uin in (:uin)");
                params.put("product", "CVM");
                params.put("appRole", Arrays.asList(AppRoleEnum.LH.getName(), AppRoleEnum.EMR.getName(),AppRoleEnum.EKS.getName()));

                Map<String, List<String>> map = dictService.queryPassWhiteListUin();
                List<String> uinList = new ArrayList<>();
                if (map.get(product) != null){
                    uinList = map.get(product);
                }
                params.put("uin", uinList);
                break;
            default:
                throw new WrongWebParameterException("未知的产品类型");
        }

    }

    private String getInstanceTypeToInstanceFamilyCaseWhen() {
        List<Map> list = cdCommonDbHelper.getRaw(Map.class,
                "select distinct a.ginsfamily,b.ginskingdom_name\n" +
                        "from static_ginstype a left join static_ginsfamily b on a.ginsphylum=b.ginsphylum");
        Map<String, List<String>> instanceFamily2InstanceType = ListUtils.toMapList(list,
                o -> o.get("ginskingdom_name").toString(), o -> o.get("ginsfamily").toString());

        StringBuilder sb = new StringBuilder();
        if (MapUtils.isEmpty(instanceFamily2InstanceType)) {
            return "instance_type";
        } else {
            sb.append("(case ");
            for (Map.Entry<String, List<String>> entry : instanceFamily2InstanceType.entrySet()) {
                sb.append(" when instance_type in (");
                sb.append(StringTools.join(ListUtils.transform(entry.getValue(), o -> "'" + o.trim() + "'"), ","));
                sb.append(" ) then '").append(entry.getKey()).append("' ");
            }
            sb.append(" else instance_type end)");
        }
        return sb.toString();
    }

    private String getCustomerShortNameToCommonCustomerShortNameCaseWhen(String industryDept) {
        Map<String, List<String>> map = dictService.queryCommonCustomerName2ShortNameByIndustryDept(industryDept);

        StringBuilder sb = new StringBuilder();
        if (MapUtils.isEmpty(map)) {
            return "customer_short_name";
        } else {
            sb.append("(case ");
            for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                sb.append(" when customer_short_name in (");
                sb.append(StringTools.join(ListUtils.transform(entry.getValue(), o -> "'" + o.trim() + "'"), ","));
                sb.append(" ) then '").append(entry.getKey()).append("' ");
            }
            sb.append(" else customer_short_name end)");
        }
        return sb.toString();
    }

    private List<String> getTopCustomers(List<LongtermVersionGroupDO> groups) {
        Set<String> result = new HashSet<>();
        for (LongtermVersionGroupDO group : groups) {
            if (StringTools.isNotBlank(group.getTopCustomers())) {
                result.addAll(ListUtils.transform(group.getTopCustomers().split(","), o -> o));
            }
        }
        return ListUtils.transform(ListUtils.filter(result, StringTools::isNotBlank), String::trim);
    }

}
