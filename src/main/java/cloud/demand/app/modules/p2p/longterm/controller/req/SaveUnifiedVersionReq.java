package cloud.demand.app.modules.p2p.longterm.controller.req;

import cloud.demand.app.modules.p2p.longterm.dto.LongTermUnifiedVersionDTO;
import com.tencent.rainbow.util.JsonUtil;
import lombok.Data;

@Data
public class SaveUnifiedVersionReq extends LongTermUnifiedVersionDTO {

    private Integer extraDemandBegYear;
    private Integer extraDemandEndYear;

    public void buildExtraDemandInfo() {
        DemandYearMonth extraDemand = new DemandYearMonth();
        extraDemand.setBeginYear(this.getExtraDemandBegYear());
        extraDemand.setEndYear(this.getExtraDemandEndYear());
        extraDemand.setBeginMonth(1);
        extraDemand.setEndMonth(12);
        this.setExtraDemand(extraDemand);
        this.setExtraIndustry(String.join(";", this.getExtraIndustryList()));
        this.setExtraYearMonth(JsonUtil.prettyToString(this.getExtraDemand()));
    }

    public void clearExtraDemandInfo() {
        this.setExtraDemand(null);
        this.setExtraIndustry(null);
        this.setExtraYearMonth(null);
    }
}
