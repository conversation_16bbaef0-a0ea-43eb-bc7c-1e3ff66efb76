package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp;

import java.util.List;
import java.util.Map;
import lombok.Data;


@Data
public class QueryPpl13weekAdjustParamsRsp {


    private List<String> ginsFamily;
    /**
     * 可用区
     */
    private List<String> zoneName;

    /**
     * regino
     */
    private List<String> regionName;

    /**
     * 新增 NEW  或者  退回  RET
     * <p>
     * 传code
     */
    private List<String> type;


    /**
     * 国家到城市的map
     */
    Map<String, List<String>> customhouseTitleToRegionName;

}
