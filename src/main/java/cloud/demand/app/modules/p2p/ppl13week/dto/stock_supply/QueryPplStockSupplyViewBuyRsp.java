package cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply;


import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyReqDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyRspHostNumSumVO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyRspHostNumSumVO.PplStockSupplyRspVO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class QueryPplStockSupplyViewBuyRsp {


    public static List<Item> trans(Map<String, List<PplStockSupplyRspHostNumSumVO>> groupData) {
        return ListUtils.transform(groupData.entrySet(),
                (o) -> QueryPplStockSupplyViewBuyRsp.trans1(o.getValue()));
    }

    private static Item trans1(List<PplStockSupplyRspHostNumSumVO> value) {
        if (Lang.isEmpty(value)) {
            return null;
        }

        List<String> yearMonthDict = value.stream().map((o) -> o.getPplStockSupplyReqDO().getYearMonth()).distinct()
                .collect(Collectors.toList());

        Item ret = trans2(value.get(0));
            new TotalNum();
        List<YearMonthDetailNum> details = Lang.list();
        for (String yearMonth : yearMonthDict) {
            List<PplStockSupplyRspHostNumSumVO> oneMonth = value.stream()
                    .filter((o) -> Strings.equals(o.getPplStockSupplyReqDO().getYearMonth(), yearMonth)).collect(
                            Collectors.toList());
            BigDecimal deviceNum = NumberUtils.sum(oneMonth, (o) -> o.getPplStockSupplyRspVO().getHostNumSum());
            BigDecimal coreNum = NumberUtils.sum(oneMonth, (o) -> o.getPplStockSupplyRspVO().getInstanceTotalCore());
            details.add(new YearMonthDetailNum(yearMonth, deviceNum, coreNum.intValue()));
        }

        BigDecimal totalDeviceNum = NumberUtils.sum(details, YearMonthDetailNum::getNum);
        BigDecimal totalCoreNum = NumberUtils.sum(details, YearMonthDetailNum::getCoreNum);

        ret.setDetails(details);
        ret.setTotal(new TotalNum(totalDeviceNum,totalCoreNum.intValue()));
        return ret;
    }

    private static Item trans2(PplStockSupplyRspHostNumSumVO source) {
        Item item = new Item();
        setItem(item, source.getPplStockSupplyReqDO());
        setItem(item, source.getPplStockSupplyRspVO());
        return item;
    }

    private static void setItem(Item item, PplStockSupplyRspVO pplStockSupplyRsqDO) {
        if (pplStockSupplyRsqDO == null) {
            log.warn("pplStockSupplyReqDO is null");
            return;
        }
        item.setCountryName(pplStockSupplyRsqDO.getCountryName());
        item.setRegionName(pplStockSupplyRsqDO.getRegionName());
        item.setZoneName(pplStockSupplyRsqDO.getZoneName());
        item.setPlanProductName(pplStockSupplyRsqDO.getPlanProductName());
        item.setDeviceType(pplStockSupplyRsqDO.getHostType());
    }
    private static void setItem(Item item, PplStockSupplyReqDO pplStockSupplyReqDO) {
        if (pplStockSupplyReqDO == null) {
            log.warn("pplStockSupplyReqDO is null");
            return;
        }

        item.setPplId(pplStockSupplyReqDO.getPplId());
        item.setIndustryDept(pplStockSupplyReqDO.getIndustryDept());
        item.setCustomerShortName(pplStockSupplyReqDO.getCustomerShortName());
    }

    @Data
    public static class Item {

        String pplId;

        String industryDept;

        String customerShortName;

        String countryName;

        String regionName;

        String zoneName;

        String planProductName;

        String deviceType;

        List<YearMonthDetailNum> details;
        TotalNum total;

    }

    List<Item> items;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class YearMonthDetailNum {

        String yearMonth;
        BigDecimal num;
        Integer coreNum;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TotalNum {

        BigDecimal num;
        Integer coreNum;
    }


}
