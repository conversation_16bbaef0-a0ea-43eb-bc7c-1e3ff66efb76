package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Table("yunti_demand_cvm_item_forecast")
public class YuntiDemandCvmItemForecastDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 对数用的，预测任务id<br/>Column: [split_id] */
    @Column(value = "split_id")
    private Long splitId;

    @Column(value = "output_version_id")
    private Long outputVersionId;

    /** 预测的需求年<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** 预测的需求月<br/>Column: [month] */
    @Column(value = "month")
    private Integer month;

    /** 预测的需求年<br/>Column: [year] */
    @Column(value = "holiday_year")
    private Integer holidayYear;

    /** 预测的需求月<br/>Column: [month] */
    @Column(value = "holiday_month")
    private Integer holidayMonth;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "device_group")
    private String deviceGroup;

    /** 机型<br/>Column: [gins_family] */
    @Column(value = "gins_family")
    private String ginsFamily;

    @Column(value = "project_name")
    private String projectName;

    @Column(value = "custom_bg_name")
    private String customBgName;

    /** 预测值<br/>Column: [core_num] */
    @Column(value = "core_num")
    private BigDecimal coreNum;

}