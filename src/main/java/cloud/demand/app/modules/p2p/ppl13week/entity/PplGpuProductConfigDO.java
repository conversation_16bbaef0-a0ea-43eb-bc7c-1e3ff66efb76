package cloud.demand.app.modules.p2p.ppl13week.entity;

// package a.b.c;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_gpu_product_config")
public class PplGpuProductConfigDO {

    /**
     * id<br/>Column: [id]
     */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * 删除标记<br/>Column: [deleted]
     */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    /**
     * 创建时间<br/>Column: [create_time]
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * 更新时间<br/>Column: [update_time]
     */
    @Column(value = "update_time")
    private Date updateTime;

    /**
     * 产品形态<br/>Column: [gpu_product_type]
     */
    @Column(value = "gpu_product_type")
    private String gpuProductType;

    /**
     * 卡型<br/>Column: [gpu_type]
     */
    @Column(value = "gpu_type")
    private String gpuType;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @Column(value = "instance_type")
    private String instanceType;

}
