package cloud.demand.app.modules.p2p.longterm.service.impl;

import cloud.demand.app.common.config.CacheConfiguration.SynchronizedHiSpeedCache1Second;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupRecordDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.longterm.service.DatabaseAccess;
import cloud.demand.app.modules.p2p.longterm.vo.VersionGroupRecordVO;
import cloud.demand.app.modules.p2p.longterm.vo.VersionGroupVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import yunti.boot.exception.BizException;

@Component
public class DatabaseAccessImpl implements DatabaseAccess {

    @Resource
    private DBHelper demandDBHelper;

    @Override
    @HiSpeedCache(keyScript = "args[0]", expireSecond = 10)
    @SynchronizedHiSpeedCache1Second
    public LongtermVersionDO queryVersion(String versionCode) {
        return getVersion(versionCode, true);
    }

    @Override
    public LongtermVersionDO getVersion(String versionCode, boolean throwException) {
        LongtermVersionDO version = demandDBHelper.getOne(LongtermVersionDO.class,
                "where version_code=?", versionCode);
        if (version == null && throwException) {
            throw BizException.makeThrow("中长期版本不存在 code: " + versionCode);
        }
        return version;
    }

    @Override
    @HiSpeedCache(keyScript = "args[0]", expireSecond = 10)
    @SynchronizedHiSpeedCache1Second
    public VersionGroupVO queryVersionAndGroup(Long groupId) {
        LongtermVersionGroupDO group = getGroup(groupId,true);
        LongtermVersionDO version = queryVersion(group.getVersionCode());
        return new VersionGroupVO(version, group);
    }

    @Override
    public LongtermVersionGroupDO getGroup(Long groupId, boolean throwIfNull) {
        LongtermVersionGroupDO group = demandDBHelper.getOne(LongtermVersionGroupDO.class, "where id=?", groupId);
        if (group == null && throwIfNull) {
            throw BizException.makeThrow("中长期分组不存在 id: " + groupId);
        }
        return group;
    }

    @HiSpeedCache(keyScript = "args[0]", expireSecond = 10)
    @SynchronizedHiSpeedCache1Second
    @Override
    public VersionGroupRecordVO queryVersionAndGroupRecord(Long recordId) {
        LongtermVersionGroupRecordDO groupRecord = getGroupRecord(recordId, true);
        VersionGroupVO versionGroupVO = queryVersionAndGroup(groupRecord.getVersionGroupId());
        return new VersionGroupRecordVO(versionGroupVO, groupRecord);
    }

    @Override
    public LongtermVersionGroupRecordDO getGroupRecord(Long recordId, boolean throwIfNull) {
        LongtermVersionGroupRecordDO one =
                demandDBHelper.getOne(LongtermVersionGroupRecordDO.class, "where id=?", recordId);
        if (one == null && throwIfNull) {
            throw BizException.makeThrow("中长期分组记录不存在 id: " + recordId);
        }
        return one;
    }

    @Override
    public List<LongtermVersionGroupRecordItemDO> getItems(Long recordId) {
        return demandDBHelper.getAll(LongtermVersionGroupRecordItemDO.class,
                "where version_group_record_id=?", recordId);
    }

    @Override
    public List<LongtermVersionGroupRecordItemDO> getItems(Long recordId,String product) {
        if (product == null) {
            return demandDBHelper.getAll(LongtermVersionGroupRecordItemDO.class,
                    "where version_group_record_id=?", recordId);
        }
        return demandDBHelper.getAll(LongtermVersionGroupRecordItemDO.class,
                "where version_group_record_id=? and product=?", recordId, product);
    }




}