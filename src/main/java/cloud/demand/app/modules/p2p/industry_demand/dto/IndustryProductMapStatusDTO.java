package cloud.demand.app.modules.p2p.industry_demand.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Objects;

@Data
@AllArgsConstructor
public class IndustryProductMapStatusDTO {

    private String industry;
    private String product;
    private String step;
    private String status;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        IndustryProductMapStatusDTO that = (IndustryProductMapStatusDTO) o;
        return Objects.equals(industry, that.industry) && Objects.equals(product, that.product)
                && Objects.equals(status, that.status);
    }

    @Override
    public int hashCode() {
        return Objects.hash(industry, product, status);
    }

    private Boolean hasAuth;

}
