package cloud.demand.app.modules.p2p.industry_demand.entity;

import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SatisfactionDTO {

    @NotNull(message = "年份不能为空")
    private Integer year;
    @NotNull(message = "月份不能为空")
    private Integer month;
    /**
     * 库存满足量
     */
    @NotNull(message = "库存满足量不能为空")
    private Integer storageNum;
    /**
     * 采购满足量
     */
    @NotNull(message = "采购满足量不能为空")
    private Integer supplyNum;

    public String toDateStr() {
        return year + "-" + (month < 10 ? "0" : "") + month;
    }
}
