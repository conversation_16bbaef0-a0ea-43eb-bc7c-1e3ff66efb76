package cloud.demand.app.modules.p2p.longterm.service.impl.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.style.AbstractCellStyleStrategy;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;

import java.awt.*;
import java.util.Map;

public class DownloadExcelStyleHandler extends AbstractCellStyleStrategy implements CellWriteHandler {


    // 样式定义
    private XSSFCellStyle headCellStyleWhiteFontOrangeBackground; // 橙底白字
    private XSSFCellStyle headCellStyleGrayFontOrangeBackground;  // 浅橙灰字
    private XSSFCellStyle headCellStyleGrayFontGrayBackground;    // 灰底灰字

    // 颜色定义
    private static final XSSFColor ORANGE_BG = new XSSFColor(new Color(225, 91, 39));    // 橙色背景
    private static final XSSFColor LIGHT_ORANGE_BG = new XSSFColor(new Color(244, 186, 156)); // 浅橙色背景
    private static final XSSFColor GRAY_BG = new XSSFColor(new Color(110, 110, 110));    // 灰色背景

    // 边框样式配置
    private static final BorderStyle DEFAULT_BORDER = BorderStyle.THIN;
    private static final IndexedColors BORDER_COLOR = IndexedColors.BLACK;

    private Map<String, String> headMap;

    public DownloadExcelStyleHandler(Map<String, String> headMap) {
        this.headMap = headMap;
    }

    @Override
    protected void initCellStyle(Workbook workbook) {
        // 公共字体配置
        Font baseFont = workbook.createFont();
        baseFont.setFontName("等线 (正文)");

        // ================= 橙底白字 =================
        headCellStyleWhiteFontOrangeBackground = createCellStyle(workbook,
                ORANGE_BG,
                createFont(workbook, baseFont, (short)12, IndexedColors.WHITE, true));

        // ================= 浅橙灰字 =================
        headCellStyleGrayFontOrangeBackground = createCellStyle(workbook,
                LIGHT_ORANGE_BG,
                createFont(workbook, baseFont, (short)12, IndexedColors.GREY_50_PERCENT, false));

        // ================= 灰底灰字 =================
        headCellStyleGrayFontGrayBackground = createCellStyle(workbook,
                GRAY_BG,
                createFont(workbook, baseFont, (short)12, IndexedColors.GREY_25_PERCENT, false));
    }

    // 样式构建工具方法
    private XSSFCellStyle createCellStyle(Workbook workbook, XSSFColor bgColor, Font font) {
        XSSFCellStyle style = (XSSFCellStyle) workbook.createCellStyle();
        style.setWrapText(true);
        style.setFillForegroundColor(bgColor);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        applyBorder(style, DEFAULT_BORDER, BORDER_COLOR);
        return style;
    }

    /**
     * 统一边框配置方法
     * @param style       目标样式
     * @param borderType  边框类型
     * @param color       边框颜色
     */
    private void applyBorder(XSSFCellStyle style, BorderStyle borderType, IndexedColors color) {
        style.setBorderTop(borderType);
        style.setTopBorderColor(color.getIndex());

        style.setBorderBottom(borderType);
        style.setBottomBorderColor(color.getIndex());

        style.setBorderLeft(borderType);
        style.setLeftBorderColor(color.getIndex());

        style.setBorderRight(borderType);
        style.setRightBorderColor(color.getIndex());
    }

    // 字体构建工具方法
    private Font createFont(Workbook workbook, Font baseFont, short size, IndexedColors color, boolean bold) {
        Font font = workbook.createFont();
        font.setFontName(baseFont.getFontName());
        font.setFontHeightInPoints(size);
        font.setColor(color.getIndex());
        font.setBold(bold);
        return font;
    }

    @Override
    protected void setHeadCellStyle(Cell cell, Head head, Integer relativeRowIndex) {

        cell.getSheet().setColumnWidth(cell.getColumnIndex(), 15 * 256);

        if (headMap != null) {
            if (cell.getRowIndex() == 0) {
                String s = headMap.get(cell.getStringCellValue());
                if (s != null) {
                    setHeadCellStyle(cell, s);
                }
            } else {
                setHeadCellStyle(cell, cell.getStringCellValue());
            }
        } else {
            cell.setCellStyle(headCellStyleGrayFontGrayBackground);
        }

    }

    private void setHeadCellStyle(Cell cell, String value) {

        if (value.contains("*必填") || value.contains("本次填写") ) {
            cell.setCellStyle(headCellStyleWhiteFontOrangeBackground);
        } else if (value.contains("选填")) {
            cell.setCellStyle(headCellStyleGrayFontOrangeBackground);
        } else {
            cell.setCellStyle(headCellStyleGrayFontGrayBackground);
        }
    }


    @Override
    protected void setContentCellStyle(Cell cell, Head head, Integer relativeRowIndex) {

    }

}