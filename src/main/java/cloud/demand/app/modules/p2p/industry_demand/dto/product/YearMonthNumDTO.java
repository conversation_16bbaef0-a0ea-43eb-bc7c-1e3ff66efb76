package cloud.demand.app.modules.p2p.industry_demand.dto.product;

import lombok.Data;

@Data
public class YearMonthNumDTO {

    private int year;
    private int month;
    private Integer num;
    private String chineseName;
    private String standardName;

    public YearMonthNumDTO(int year, int month, Integer num) {
        this.year = year;
        this.month = month;
        this.num = num;
        this.chineseName = year + "年" + month + "月";
        this.standardName = year + "-" + (month < 10 ? "0" : "") + month;
    }
}
