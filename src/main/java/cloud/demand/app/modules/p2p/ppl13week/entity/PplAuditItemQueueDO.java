package cloud.demand.app.modules.p2p.ppl13week.entity;


import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_audit_item_queue")
public class PplAuditItemQueueDO extends BaseDO {

    @Column(value = "industry_dept")
    private String industryDept;

    @Column(value = "product")
    private String product;

    @Column(value = "version_id")
    private Long versionId;

    @Column(value = "ppl_order")
    private String pplOrder;

    @Column(value = "item_json")
    private String itemJson;

    /**
     * 0-未消费;1-已消费;2-消费失败
     */
    @Column(value = "status")
    private Integer status;

    @Column(value = "consume_time")
    private String consumeTime;


}
