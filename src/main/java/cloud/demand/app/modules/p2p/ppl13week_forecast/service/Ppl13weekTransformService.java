package cloud.demand.app.modules.p2p.ppl13week_forecast.service;


import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastConfigHolidayWeekDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.YuntiDemandCvmItemForecastDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryPpl13weekTransformReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryPpl13weekTransformStatusRsp;
import com.google.common.collect.ImmutableMap;
import io.vavr.Tuple2;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;


/**
 * 干预下发相关的接口放到这里
 *
 * <AUTHOR>
 */

public interface Ppl13weekTransformService {

    /**
     *
     * @param req req
     * @return ret
     */
    QueryPpl13weekTransformStatusRsp queryTransFormStatus(QueryPpl13weekTransformReq req);


    /**
     * 查询date的最新拆分数据
     * @param date date
     * @return list
     */
    List<YuntiDemandCvmItemForecastDO> getSplitResultByDate(LocalDate date);

    /**
     * 转换一份数据到yunti 库里面，按照月份来覆盖
     * @param req 请求参数
     * @return  data
     */
    ImmutableMap<String, String> createZiyanTransForm(QueryPpl13weekTransformReq req);

    /**
     * 下发到 crp
     * @param req req
     * @return ret success info
     */
    ImmutableMap<String, String> createTransForm(QueryPpl13weekTransformReq req);

    Map<Tuple2<Integer, Integer>, List<PplForecastConfigHolidayWeekDO>> getWeekData();

    /**
     * 更新周数据的权值
     */
    void updateWeekData();

}
