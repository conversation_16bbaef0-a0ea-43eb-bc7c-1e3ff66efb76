package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.modules.common.service.TaskLogService;
import cloud.demand.app.modules.p2p.common.P2PInstanceModelParse;
import cloud.demand.app.modules.p2p.industry_demand.config.DynamicProperties;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.TodoService;
import cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version.UnificatedVersionDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ApproveVersionGroupByAdminReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.CheckStockSupplyResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.CreateVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionResp.VersionDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.UpdateVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplConfigProductEnumDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionDO.DemandYearMonth;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionGroupStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.inner_process.PplInnerProcessVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplItemService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionGroupService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionService;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplItemWithOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupInfoVO;
import cloud.demand.app.web.model.common.Page;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import com.tencent.rainbow.util.JsonUtil;
import io.vavr.Tuple2;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class PplVersionServiceImpl implements PplVersionService {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private PplCommonService pplCommonService;
    @Resource
    private PplDictService pplDictService;
    @Resource
    private PplVersionGroupService pplVersionGroupService;
    @Resource
    private PplItemService pplItemService;
    @Resource
    private TaskLogService taskLogService;
    @Resource
    private DBHelper resourcedbDBHelper;

    Supplier<String> domainSupplier = DynamicProperty.create("app.industry.domain", "");

    private static final DynamicProperty<String> testEnv = DynamicProperty.create("test_env", "");

    static ExecutorService executor = Executors.newFixedThreadPool(10);

    @Resource
    Alert alert;

    @Override
    public QueryVersionResp queryVersionList(QueryVersionReq req) {
        if (req.getPage() == null) {
            req.setPage(new Page());
        }

        WhereContent whereContent = new WhereContent();
        getPplAuth(LoginUtils.getUserName(), whereContent);
        whereContent.order("id desc");
        PageData<PplVersionDO> pageData = demandDBHelper.getPage(PplVersionDO.class,
                req.getPage().getPageIndex(), req.getPage().getSize(), whereContent.getSql(), whereContent.getParams());

        QueryVersionResp resp = new QueryVersionResp();
        resp.setTotal((int) pageData.getTotal());
        resp.setVersionList(ListUtils.transform(pageData.getData(), VersionDTO::from));
        return resp;
    }

    @Override
    public QueryVersionResp queryAllVersionNoAuth() {
        List<PplVersionDO> versions = demandDBHelper.getAll(PplVersionDO.class, "order by id desc");
        QueryVersionResp resp = new QueryVersionResp();
        resp.setTotal(versions.size());
        resp.setVersionList(ListUtils.transform(versions, VersionDTO::from));
        return resp;
    }

    public void getPplAuth(String userName, WhereContent whereContent) {

        // 管理员不健权
        String approveUser = pplCommonService.getApproveUser(Lang.list(
//                IndustryDemandAuthRoleEnum.PPL_INDUSTRY_MEDDLE,
                IndustryDemandAuthRoleEnum.ADMIN));
        if (approveUser.contains(userName)) {
            return;
        }

        //1、登陆人=录单人；
        //2、登陆人所在OA部门，与PPL单“行业部门”一致
        //1和2任意满足一个都可
        WhereContent auth = new WhereContent("1=0");
        auth.addOr(ORMUtils.getColumnByMethod(PplVersionDO::getCreator) + "=?", userName);

//        List<String> industryDept = pplDictService.queryIndustryDept(userName);
//        if (!Lang.isEmpty(industryDept) && Strings.isNotBlank(industryDept.get(0))) {
//            auth.addOr("industry_dept=?", industryDept.get(0));
//        }
//
//        List<cloud.demand.app.modules.p2p.ppl13week.entity.IndustryDemandAuthDO> authRole =
//                pplCommonService.getAuthRole(LoginUtils.getUserName());

//        ArrayList<IndustryDemandAuthRoleEnum> roleList = Lang.list(
//                IndustryDemandAuthRoleEnum.PPL_INDUSTRY_MEDDLE, IndustryDemandAuthRoleEnum.PPL_INDUSTRY_APPROVE,
//                IndustryDemandAuthRoleEnum.PPL_PRODUCT_APPROVE, IndustryDemandAuthRoleEnum.PPL_COMD_INTERFACE);
//        List<String> roleCodeList = ListUtils.transform(roleList, IndustryDemandAuthRoleEnum::getCode);
//        for (cloud.demand.app.modules.p2p.ppl13week.entity.IndustryDemandAuthDO industryDemandAuthDO : authRole) {
//            if (Strings.isNotBlank(industryDemandAuthDO.getIndustry()) &&
//                    roleCodeList.contains(industryDemandAuthDO.getRole())) {
//                String industrys = industryDemandAuthDO.getIndustry();
//                if (Strings.isNotBlank(industrys)) {
//                    List<String> deptIndustrys = Lang.list(industrys.split(";"));
//                    if (Lang.isNotEmpty(deptIndustrys)) {
//                        auth.orIn(PplVersionDO::get, deptIndustrys);
//                    }
//                }
//            }
//        }

//        log.info("login_user: {}, auth where: {}", userName, auth.toString());

        whereContent.addAnd(auth);
    }

    @Synchronized(throwExceptionIfNotGetLock = false)
    @Override
    @Transactional(value = "demandTransactionManager")
    public void createVersion(CreateVersionReq req) {
        pplCommonService.requireRole(IndustryDemandAuthRoleEnum.ADMIN);
        if (demandDBHelper.isExist(PplVersionDO.class, "where version_code=?", req.getVersionCode())) {
            throw new WrongWebParameterException("版本" + req.getVersionCode() + "已经存在，新增失败");
        }
        demandDBHelper.insert(req.createDO());
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void createVersionByUnificatedVersion(UnificatedVersionDTO unificatedVersionDTO) {
        CreateVersionReq req = new CreateVersionReq();
        req.setVersionName(unificatedVersionDTO.getVersionCode());
        req.setVersionCode(unificatedVersionDTO.getVersionCode());
        req.setVersionType(unificatedVersionDTO.getIsLastMonthWeek() ? "MONTH" : "WEEK");

        unificatedVersionDTO.parseJsonDemand();
        DemandYearMonth demandYearMonth = unificatedVersionDTO.getPplDemand();
        req.setDemandBeginYear(demandYearMonth.getBeginYear());
        req.setDemandBeginMonth(demandYearMonth.getBeginMonth());
        req.setDemandEndYear(demandYearMonth.getEndYear());
        req.setDemandEndMonth(demandYearMonth.getEndMonth());
        req.setOverseasDemandBeginYear(demandYearMonth.getOverseasBeginYear());
        req.setOverseasDemandBeginMonth(demandYearMonth.getOverseasBeginMonth());
        req.setDeadline(unificatedVersionDTO.getDeadlineByCode("ppl_enter_deadline"));
        createVersion(req);
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void updateVersion(UpdateVersionReq req) {
        pplCommonService.requireRole(IndustryDemandAuthRoleEnum.ADMIN);

        PplVersionDO version = getPplVersion(req.getId());

        // 判断如果修改了versionCode，是否和已有的重复
        if (StringTools.isNotBlank(req.getVersionCode())) {
            PplVersionDO db = demandDBHelper.getOne(PplVersionDO.class,
                    "where version_code=?", req.getVersionCode());
            if (db != null && !Objects.equals(version.getId(), db.getId())) {
                throw new WrongWebParameterException("要修改的版本编码:" + req.getVersionCode() + "已存在");
            }
        }

        PplVersionStatusEnum status = PplVersionStatusEnum.getByCode(version.getStatus());
        if (status == PplVersionStatusEnum.CREATED) { // 不是已创建状态的版本，只能编辑备注
            req.updateDO(version);
        } else if (status == PplVersionStatusEnum.PROCESS && version.getDeadline().after(new Date())) {
            version.setDeadline(req.getDeadline());
            version.setNote(req.getNote());
        } else {
            version.setNote(req.getNote());
        }

        demandDBHelper.update(version);
    }

    @Override
    public void updateVersionByUnificatedVersion(UnificatedVersionDTO unificatedVersionDTO) {
        PplVersionDO pplVersion = demandDBHelper.getOne(PplVersionDO.class, "where version_code = ? ",
                unificatedVersionDTO.getVersionCode());
        if (pplVersion == null) {
            throw new BizException("未初始化版本");
        }
        UpdateVersionReq req = new UpdateVersionReq();
        req.setId(pplVersion.getId());
        req.setDeadline(unificatedVersionDTO.getDeadlineByCode("ppl_enter_deadline"));
        updateVersion(req);
    }

    @Override
    public void startApprove(Long id) {
//        pplCommonService.requireRole(IndustryDemandAuthRoleEnum.PPL_COMD_INTERFACE);

        PplVersionDO pplVersion = getPplVersion(id);
        if (!PplVersionStatusEnum.CREATED.getCode().equals(pplVersion.getStatus())) {
            throw new BizException("版本并非处于已创建状态，不允许开启审批");
        }
        // 同一时刻只能存在一个审批中的
        if (demandDBHelper.isExist(PplVersionDO.class, "where status=?",
                PplVersionStatusEnum.PROCESS.getCode())) {
            throw new BizException("当前已经有版本处于处理中了，不允许同时开启审批多个版本");
        }

        // 1. 查询版本对应的ppl单
        List<PplItemWithOrderVO> orders = getPplItemForApprove(pplVersion);
        if (ListUtils.isEmpty(orders)) {
            // 继承可以有空数据了，
//            throw new BizException("当前要启动的版本时间范围下，没有已生效的PPL需求，请检查时间范围是否正确");
        }

        List<String> depts = orders.stream().map((o) -> o.getPplOrderDO().getIndustryDept()).distinct()
                .collect(Collectors.toList());

        // 2. 聚合行业部门和产品，生成version_group；创建2份order_record和order_item
        Map<String, List<PplItemWithOrderVO>> groups = ListUtils.groupBy(orders,
                o -> StringTools.join("@", o.getPplOrderDO().getIndustryDept(), o.getProduct()));

//        String deptSql = "select distinct industry\n"
//                + "from industry_demand_product_enum where deleted=0";
//        List<String> allIndustryDept = demandDBHelper.getRaw(String.class, deptSql);

        List<String> allIndustryDept = DynamicProperties.getIndustryDept();
        Set<String> allDept = new HashSet<>(allIndustryDept);
        allDept.addAll(depts);
        // 22-12-30，当前逻辑各个行业需要 生成 1个CVM&CBS的组 和 4个PAAS级产品组 + 1个GPU
        List<String> productList = pplDictService.queryAllProductEnum(null, Boolean.TRUE).stream().map(
                        PplConfigProductEnumDO::getProductName)
                .collect(Collectors.toList());

        //处理所有数据
        for (String dept : allDept) {
            if (Strings.isBlank(dept)) {
                continue;
            }
            for (String product : productList) {
                // 定时任务需保证大事务，不追求速度，不进行多线程处理
                initVersionGroup(dept, product, pplVersion, groups);
//                if (isNeedExecutor) {
//                    // 如果是页面调用，要求快需要多线程
//                    executor.execute(() -> {
//                        initVersionGroup(dept, product, pplVersion, groups);
//                    });
//                } else {
//                    // 定时任务需保证大事务，不进行多线程处理
//                    initVersionGroup(dept, product, pplVersion, groups);
//                }
            }
        }



        // 3. 最后将版本状态修改为审批中；pplOrder的状态也改
        pplVersion.setStatus(PplVersionStatusEnum.PROCESS.getCode());
        pplVersion.setStartAuditTime(new Date());
        demandDBHelper.update(pplVersion);

        // 4. 同步yuntippl数据
        pplVersionGroupService.syncYunTiPpl(pplVersion.getVersionCode());

    }

    public void initVersionGroup(String dept, String product, PplVersionDO pplVersion,
            Map<String, List<PplItemWithOrderVO>> groups) {
        try {
//            List<String> cosOrDataBase = ListUtils.newArrayList(Ppl13weekProductTypeEnum.COS.getName(),
//                    Ppl13weekProductTypeEnum.DATABASE.getName());
//            if (cosOrDataBase.contains(product)) {
//                // COS、数据库 不建立13周版本分组
//                return;
//            }
            PplVersionGroupDO groupDO = new PplVersionGroupDO();
            groupDO.setVersionCode(pplVersion.getVersionCode());
            groupDO.setIndustryDept(dept);
            groupDO.setProduct(product);
            groupDO.setStatus(PplVersionGroupStatusEnum.CREATE.getCode());

            PplInnerProcessVersionDO latestVersionDO;
            if (Ppl13weekProductTypeEnum.BM.getName().equals(product)) {
                latestVersionDO = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                        "where id = (select max(id) from ppl_inner_process_version where industry_dept = ? and product like concat('%',?,'%') and product != 'GPU(裸金属&CVM)' and status = ? )",
                        dept, product, PplInnerProcessVersionStatusEnum.DONE.getCode());
            } else {
                latestVersionDO = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                        "where id = (select max(id) from ppl_inner_process_version where industry_dept = ? and product like concat('%',?,'%') and status = ? )",
                        dept, product, PplInnerProcessVersionStatusEnum.DONE.getCode());
            }

            groupDO.setInnerVersionId(latestVersionDO == null ? 0 : latestVersionDO.getId());
            groupDO.setIsExtendLastVersion(null);
            groupDO.setSyncVersionTime(new Date());
            if (latestVersionDO != null) {
                PplVersionGroupDO versionGroupDO = demandDBHelper.getOne(PplVersionGroupDO.class,
                        "where inner_version_id = ? and product = ? ", latestVersionDO.getId(), product);
                groupDO.setIsExtendLastVersion(versionGroupDO != null ? Boolean.FALSE : Boolean.TRUE);
            }

            demandDBHelper.insert(groupDO);
            List<PplItemWithOrderVO> list = new ArrayList<>();
            if (groups.get(StringTools.join("@", dept, product)) != null) {
                list = groups.get(StringTools.join("@", dept, product));
            }
            // 创建两种类型的数据
            GroupRecordAndItem groupRecordAndItem = savePplRecordAndItemForCreate(groupDO, list);

            // 这里对版本的 item ,初始化打标
            if (Ppl13weekProductTypeEnum.CVM.getName().equals(groupDO.getProduct())
                    && !Objects.equals(groupDO.getIndustryDept(), IndustryDeptEnum.LONG_TAIL.getName())) {
                List<PplVersionGroupRecordItemDO> itemList = groupRecordAndItem.getItems();
                pplVersionGroupService.setThresholds(itemList);
                itemList.stream()
                        .collect(Collectors.groupingBy(PplVersionGroupRecordItemDO::getIsSpike))
                        .forEach((k, v) -> {
                            List<Long> ids = v.stream().map(PplVersionGroupRecordItemDO::getId)
                                    .collect(Collectors.toList());
                            demandDBHelper.executeRaw("update ppl_version_group_record_item set is_spike = ? "
                                    + "where id in (?)", k, ids);
                        });
            }

            //对CBS进行打标
            if (!Ppl13weekProductTypeEnum.DATABASE.getName().equals(groupDO.getProduct())) {
                List<PplVersionGroupRecordItemDO> itemList = groupRecordAndItem.getItems();
                pplVersionGroupService.setThresholdsForCBS(itemList);
                itemList.stream()
                        .collect(Collectors.groupingBy(PplVersionGroupRecordItemDO::getCbsIsSpike))
                        .forEach((k, v) -> {
                            List<Long> ids = v.stream().map(PplVersionGroupRecordItemDO::getId)
                                    .collect(Collectors.toList());
                            demandDBHelper.executeRaw("update ppl_version_group_record_item set cbs_is_spike = ? "
                                    + "where id in (?)", k, ids);
                        });
            }


        } catch (Exception e) {
            AlarmRobotUtil.doAlarm("startApprove", "生成13周初始数据失败: " + e.getMessage(), null, false);
            alert.sendRtx("oliverychen", "生成13周初始数据失败", e.getMessage());
        }
    }

    @Override
    public void startApproveByVersionCode(String versionCode) {
        PplVersionDO one = demandDBHelper.getOne(PplVersionDO.class, "where version_code = ?", versionCode);
        if (one == null) {
            throw new BizException("未初始化13周版本,无法启动");
        }
        if (!PplVersionStatusEnum.CREATED.getCode().equals(one.getStatus())) {
            // 代表已经启动过了
            return;
        }
        startApprove(one.getId());
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    @Synchronized(namespace = "ppl-finishApprove", waitLockMillisecond = 1500, keyScript = "args[0]", throwExceptionIfNotGetLock = false)
    public void finishApprove(Long id) {
        pplCommonService.requireRole(IndustryDemandAuthRoleEnum.PPL_COMD_INTERFACE);

        PplVersionDO pplVersion = getPplVersion(id);
        YearMonth startYearMonth = new YearMonth(pplVersion.getDemandBeginYear(), pplVersion.getDemandBeginMonth());

        // 0、把所有未完成的GPU分组直接完成
        demandDBHelper.executeRaw("update ppl_version_group set status = ? "
                        + "where deleted = 0 and version_code = ? and product = ? and status in (?) ",
                PplVersionGroupStatusEnum.DONE.getCode(), pplVersion.getVersionCode(),
                Ppl13weekProductTypeEnum.GPU.getName(), Arrays.asList(PplVersionGroupStatusEnum.STOCK_SUPPLY.getCode(),
                        PplVersionGroupStatusEnum.COMD_APPROVE.getCode()));

        // 1. 检查所有分组的状态是否已经结束
        if (PplVersionStatusEnum.DONE.getCode().equals(pplVersion.getStatus())) {
            return; // 关闭状态再次调用关闭，认为成功
        }
        if (PplVersionStatusEnum.PROCESS.getCode().equals(pplVersion.getStatus())) {
            List<PplVersionGroupDO> groups = demandDBHelper.getAll(PplVersionGroupDO.class,
                    "where version_code=? and status not in (?)", pplVersion.getVersionCode(),
                    ListUtils.newArrayList(PplVersionGroupStatusEnum.DONE.getCode(),
                            PplVersionGroupStatusEnum.IN_SUBMIT.getCode(),
                            PplVersionGroupStatusEnum.PRE_SUBMIT.getCode(),
                            PplVersionGroupStatusEnum.CREATE.getCode(),
                            PplVersionGroupStatusEnum.REJECT.getCode()));
            if (!groups.isEmpty()) {
                throw new BizException("当前版本存在未完成的审批" + groups.size() + "个，明细："
                        + StringTools.join(ListUtils.transform(groups, o ->
                        "行业部门:" + o.getIndustryDept() + ",产品:" + o.getProduct()), ";"));
            }
        }
        // 2.将所有待录入状态和系统生成状态的分组 逻辑删除
        demandDBHelper.executeRaw(
                "update ppl_version_group set deleted = 1 where version_code =?"
                        + " and (status = 'PRE_SUBMIT' or status = 'CREATE' or status = 'IN_SUBMIT' or status = 'REJECT')",
                pplVersion.getVersionCode());

//        23-11-28 运管干预数据，不再刷新最新版pplItem中
//        // 3. 删除当前需求年月范围内的 之前版本 干预的ppl_item
//        demandDBHelper.executeRaw("update ppl_item a JOIN ppl_order b on a.ppl_order = b.ppl_order "
//                        + "  set a.deleted = 1 "
//                        + " WHERE a.deleted = 0 and b.deleted = 0 and a.begin_buy_date >= ? and b.source = ?",
//                startYearMonth.getFirstDay(), PplOrderSourceTypeEnum.COMD_INTERVENE.getCode());
//        // 被干预的ppl数据置为没被干预
//        demandDBHelper.executeRaw("update ppl_item set is_comd = 0"
//                + " WHERE deleted = 0 and begin_buy_date >= ?", startYearMonth.getFirstDay());

        // 4. 将所有已完成的版本组的最新的record应用到item中，并为item增加一条record记录, 同时回刷数据到版本组的最后一次record
        List<PplVersionGroupInfoVO> doneGroups = demandDBHelper.getAll(PplVersionGroupInfoVO.class,
                "where version_code=? and status = 'DONE'", pplVersion.getVersionCode());
        for (PplVersionGroupInfoVO doneGroup : doneGroups) {
            // 大人时代变了，现在是后台任务执行了，再用多线程，没保证事务，是要被杀头的。
//            executor.execute(() -> {
            Long doneRecordId = doneGroup.getRecords().get(doneGroup.getRecords().size() - 1).getId();
            pplVersionGroupService.applyToItem(doneGroup);
//                pplVersionGroupService.snapshotItemStatus(demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
//                        "where version_group_record_id=?", doneRecordId)); // 不需要回刷状态到版本中
//            });
        }

        // 5. 结束版本
        pplVersion.setStatus(PplVersionStatusEnum.DONE.getCode());
        pplVersion.setEndAuditTime(new Date());
        demandDBHelper.update(pplVersion);

        // 6. 完成当前版本的所有未完成待办
        resourcedbDBHelper.executeRaw("update todo_order set status = 3 , operate_info = '版本关闭，系统自动关闭所有未完成待办' "
                + "where task_order_id like ? and status = 1 ", "%" + pplVersion.getVersionCode() + "%");

    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void finishApproveByVersionCode(String versionCode) {
        PplVersionDO one = demandDBHelper.getOne(PplVersionDO.class, "where version_code = ?", versionCode);
        if (one == null) {
            throw new BizException("未初始化13周版本,无法关闭");
        }
        if (!PplVersionStatusEnum.PROCESS.getCode().equals(one.getStatus())) {
            // 代表已经关闭过了
            return;
        }
        finishApprove(one.getId());
    }

    @Override
    @Transactional("demandTransactionManager")
    public void forceFinishApprove(Long id) {
        if (Strings.isBlank(testEnv.get())) {
            // 生产环境不能调用
            throw new BizException("该方法仅允许在测试环境调用");
        }
        PplVersionDO pplVersion = getPplVersion(id);
        demandDBHelper.executeRaw("update ppl_version_group set status = ?"
                        + " where deleted = 0 and status != ? and version_code = ?", PplVersionGroupStatusEnum.DONE.getCode(),
                PplVersionGroupStatusEnum.DONE.getCode(), pplVersion.getVersionCode());
        finishApprove(id);
    }

    private PplVersionDO getPplVersion(Long id) {
        PplVersionDO version = demandDBHelper.getOne(PplVersionDO.class, "where id=?", id);
        if (version == null) {
            throw new WrongWebParameterException("版本ID:" + id + "不存在");
        }
        return version;
    }

    private List<PplItemWithOrderVO> getPplItemForApprove(PplVersionDO pplVersion) {
        WhereSQL whereSQL = new WhereSQL();

        // 2024年12月24日
        // 继承范围仅保留 用户录入、模型预测
        // 海外地域和国内地域分开选择需求年月范围继承

        // 海外地域
        List<String> overSeaRegions = pplDictService.queryAllRegionName(true);

        whereSQL.and(" ppl_order in (select ppl_order from ppl_order "
                + "where deleted=0 and source in (?))",ListUtils.newList(PplOrderSourceTypeEnum.IMPORT.getCode(),
                        PplOrderSourceTypeEnum.FORECAST.getCode()));
        // 新增和退回
        whereSQL.and("begin_buy_date >= ?",pplVersion.getInCountryDemandYearMonthFirstDay());
        List<PplItemWithOrderVO> all = demandDBHelper.getAll(PplItemWithOrderVO.class, whereSQL.getSQL(),
                whereSQL.getParams());

        // 实例数量需大于0
        whereSQL.and("instance_num > 0");

        // 如果行业录入的 海外需求不满足海外年月 需要过滤掉。
        all.removeIf( o -> {
            return o.getPplOrderDO().getSource().equals(PplOrderSourceTypeEnum.IMPORT.getCode())
                    && !o.getDemandType().equals(PplDemandTypeEnum.RETURN.getCode())
                    && overSeaRegions.contains(o.getRegionName()) && !pplVersion.isSatisfyOverseasYearMonth(
                    o.getBeginBuyDate());
        });
        return all;
    }

    private void saveGroupAndSetId(Map<String, List<PplItemWithOrderVO>> groups, String versionCode) {
        for (Map.Entry<String, List<PplItemWithOrderVO>> group : groups.entrySet()) {
            PplVersionGroupDO groupDO = new PplVersionGroupDO();
            groupDO.setVersionCode(versionCode);
            groupDO.setIndustryDept(group.getValue().get(0).getPplOrderDO().getIndustryDept());
            groupDO.setProduct(group.getValue().get(0).getProduct());
            groupDO.setStatus(PplVersionGroupStatusEnum.CREATE.getCode());
            demandDBHelper.insert(groupDO);

            GroupRecordAndItem recordAndItems = savePplRecordAndItemForCreate(groupDO, group.getValue());
            // 轮转order版本状态为周版本的审批第一个状态（复制一份order_record和order_item）
            moveRecordToFirstApproveNode(recordAndItems.getRecord(), recordAndItems.getItems());
        }
    }

    private void moveRecordToFirstApproveNode(PplVersionGroupRecordDO record, List<PplVersionGroupRecordItemDO> items) {
        Integer recordVersion = pplCommonService.getVersionGroupRecordVersion();

        PplVersionGroupRecordDO recordNew = new PplVersionGroupRecordDO();
        recordNew.setVersionGroupId(record.getVersionGroupId());
        recordNew.setRecordVersion(recordVersion);
        recordNew.setStatus(
                PplVersionGroupStatusEnum.getNextStatus(PplVersionGroupStatusEnum.CREATE.getCode(), "CVM&CBS", false));
        demandDBHelper.insert(recordNew);

        PplVersionGroupDO groupDO = demandDBHelper.getByKey(PplVersionGroupDO.class, record.getVersionGroupId());
        groupDO.setStatus(
                PplVersionGroupStatusEnum.getNextStatus(PplVersionGroupStatusEnum.CREATE.getCode(), "CVM&CBS", false));
        groupDO.setCurrentProcessor(PplVersionGroupStatusEnum.getProcessor(pplCommonService,
                groupDO.getStatus(), groupDO.getIndustryDept(), groupDO.getProduct()));
        demandDBHelper.update(groupDO);

        // 创建待办 23-6-27 13周录入不发送待办
//        createTodoTask(groupDO, recordNew);

        ListUtils.forEach(items, o -> {
            o.setId(null); // 清除id，相当于复制
            o.setCreateTime(null);
            o.setRecordVersion(recordVersion);
            o.setVersionGroupRecordId(recordNew.getId());
        });

        demandDBHelper.insert(items);
    }

    @Override
    public void createTodoTask(PplVersionGroupDO groupDO, PplVersionGroupRecordDO recordNew) {
        TodoService.ApprovalData approvalData = new TodoService.ApprovalData();

        String taskId = StringTools.join("-",
                groupDO.getVersionCode(), groupDO.getIndustryDept(), groupDO.getProduct(),
                groupDO.getStatus(), recordNew.getId());

        // 这里订单号也用任务id，不然结束了一个任务就创建不了下一个任务，怎么试都没用
        approvalData.setOrderId(taskId);
        approvalData.setTaskId(taskId);
        approvalData.setSourceApp("PPL13WeekDemand-Version");
        approvalData.setSourceEvent(PplVersionGroupStatusEnum.getNameByCode(groupDO.getStatus()));
        approvalData.setSourceAppCnName("PPL13周需求-版本管理-");
        approvalData.setActivity(PplVersionGroupStatusEnum.getNameByCode(groupDO.getStatus()));
        approvalData.setHandler(groupDO.getCurrentProcessor());
        String url = domainSupplier.get() + "/13ppl/view-approval/" + groupDO.getId();
        approvalData.setFormUrl(url);
        approvalData.setIsCallBackApi(0);

        List<TodoService.ListView> listViewList = Arrays.asList(
                new TodoService.ListView("【特别说明】", "请访问PC端链接进行审批，暂不支持在待办界面或移动端审批"),
                new TodoService.ListView("链接", url),
                new TodoService.ListView("版本", groupDO.getVersionCode()),
                new TodoService.ListView("行业部门", groupDO.getIndustryDept()),
                new TodoService.ListView("产品", groupDO.getProduct()));
        approvalData.setListView(listViewList);

        approvalData.setCallBackUrl("http://localhost/cloud-demand-app/api/NOT-VALID"); // 必填，但不需要，故填不存在的

        pplCommonService.createTodo(approvalData);
    }

    @Override
    public QueryVersionResp queryAllVersionList() {
        QueryVersionResp resp = new QueryVersionResp();
        WhereContent whereContent = new WhereContent();
        whereContent.orderDesc("id");
        List<PplVersionDO> versionList = demandDBHelper.getAll(PplVersionDO.class, whereContent.getSql(),
                whereContent.getParams());
        resp.setTotal(versionList.size());
        resp.setVersionList(ListUtils.transform(versionList, VersionDTO::from));
        return resp;
    }

    @Override
    public Boolean checkIsExist(String versionCode) {
        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplVersionDO::getVersionCode, versionCode);
        List<PplVersionDO> versionList = demandDBHelper.getAll(PplVersionDO.class, whereContent.getSql(),
                whereContent.getParams());
        return !CollectionUtils.isEmpty(versionList);
    }

    @Override
    public PplVersionDO getByVersionCode(String versionCode) {
        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplVersionDO::getVersionCode, versionCode);
        return demandDBHelper.getOne(PplVersionDO.class, whereContent.getSql(), whereContent.getParams());
    }

    @Override
    @Transactional("demandTransactionManager")
    public void changeVersionGroupStatus(ApproveVersionGroupByAdminReq req) {
        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplVersionGroupDO::getVersionCode, req.getVersionCode());
        List<PplVersionGroupDO> all = demandDBHelper.getAll(PplVersionGroupDO.class, whereContent.getSql(),
                whereContent.getParams());
        for (PplVersionGroupDO versionGroupDO : all) {
            pplVersionGroupService.approveVersionGroupByAdmin(req.getAdminChangeStatus(), versionGroupDO);
        }
    }

    @Data
    public class GroupRecordAndItem {

        private PplVersionGroupRecordDO record;
        private List<PplVersionGroupRecordItemDO> items;
        private PplVersionGroupDO groupDO;
    }

    @Override
    public GroupRecordAndItem savePplRecordAndItemForCreate(PplVersionGroupDO groupDO,
            List<PplItemWithOrderVO> list) {
        Integer recordVersion = pplCommonService.getVersionGroupRecordVersion();

        PplVersionGroupRecordDO recordDO = new PplVersionGroupRecordDO();
        recordDO.setVersionGroupId(groupDO.getId());
        recordDO.setRecordVersion(recordVersion);

        List<PplItemWithOrderVO> filter = ListUtils.filter(list, (o) ->
                o.getInstanceNum() != null && o.getInstanceNum() > 0);
        if (Lang.isNotEmpty(filter)) {
            recordDO.setStatus(PplVersionGroupStatusEnum.CREATE.getCode());
            groupDO.setStatus(PplVersionGroupStatusEnum.CREATE.getCode());
        } else {
            recordDO.setStatus(PplVersionGroupStatusEnum.PRE_SUBMIT.getCode());
            groupDO.setStatus(PplVersionGroupStatusEnum.PRE_SUBMIT.getCode());
        }
//        recordDO.setOperateUser("SYSTEM");
//        recordDO.setApproveTime(new Date());
//        recordDO.setApproveResult(PplVersionGroupApproveResultEnum.PASS.getCode());
//        recordDO.setApproveNote("开启版本自动继承数据");
        // 插入 record
        demandDBHelper.insert(recordDO);

        // 插入数据
        List<PplVersionGroupRecordItemDO> recordItems = ListUtils.transform(list, o -> {
            PplVersionGroupRecordItemDO d = new PplVersionGroupRecordItemDO();
            d.setStatus(o.getStatus());
            d.setVersionGroupId(groupDO.getId());
            d.setRecordVersion(recordVersion);
            d.setVersionGroupRecordId(recordDO.getId());
            d.setPplOrder(o.getPplOrder());
            d.setPplId(o.getPplId());
            d.setParentPplId(o.getParentPplId());
            d.setProduct(o.getProduct());
            d.setDemandType(o.getDemandType());
            d.setDemandScene(o.getDemandScene());
            d.setProjectName(o.getProjectName());
            d.setBillType(o.getBillType());
            d.setWinRate(o.getWinRate());
            d.setImportantDemand(o.getImportantDemand());
            d.setBeginBuyDate(o.getBeginBuyDate());
            d.setEndBuyDate(o.getEndBuyDate());
            d.setBeginElasticDate(o.getBeginElasticDate());
            d.setEndElasticDate(o.getEndElasticDate());
            d.setNote(o.getNote());
            d.setRegionName(o.getRegionName());
            d.setZoneName(o.getZoneName());
            d.setIsStrongDesignateZone(o.getIsStrongDesignateZone());
            d.setInstanceType(o.getInstanceType());
            d.setInstanceModel(o.getInstanceModel());
            d.setInstanceNum(o.getInstanceNum());

            Tuple2<Integer, Integer> cpuRam = P2PInstanceModelParse.parseInstanceModel(
                    o.getInstanceModel());
            if (o.getInstanceNum() != null) {
                d.setTotalCore(o.getInstanceNum() * cpuRam._1);
            }
            d.setAlternativeInstanceType(o.getAlternativeInstanceType());
            d.setAffinityType(o.getAffinityType());
            d.setAffinityValue(o.getAffinityValue());
            d.setSystemDiskType(o.getSystemDiskType());
            d.setSystemDiskStorage(o.getSystemDiskStorage());
            d.setSystemDiskNum(o.getSystemDiskNum());
            d.setDataDiskType(o.getDataDiskType());
            d.setDataDiskStorage(o.getDataDiskStorage());
            d.setDataDiskNum(o.getDataDiskNum());
            int systemDiskNum = o.getSystemDiskNum() == null ? 0 : o.getSystemDiskNum();
            int systemDiskStorage = o.getSystemDiskStorage() == null ? 0 : o.getSystemDiskStorage();
            int dataDiskNum = o.getDataDiskNum() == null ? 0 : o.getDataDiskNum();
            int dataDiskStorage = o.getDataDiskStorage() == null ? 0 : o.getDataDiskStorage();
            int instanceNum = o.getInstanceNum() == null ? 0 : o.getInstanceNum();
            d.setTotalDisk(((systemDiskNum * systemDiskStorage) + (dataDiskNum * dataDiskStorage)) * instanceNum);
            /*
             * 继承版本，使用创建版本的人作为 creator
             */
            d.setCreator(o.getPplOrderDO().getSubmitUser());

            //gpu
            d.setGpuProductType(o.getGpuProductType());
            d.setGpuType(o.getGpuType());
            d.setGpuNum(o.getGpuNum());
            d.setIsAcceptAdjust(o.getIsAcceptAdjust());
            d.setAcceptGpu(o.getAcceptGpu());
            d.setTotalGpuNum(o.getTotalGpuNum());
            d.setBizScene(o.getBizScene());
            d.setBizDetail(o.getBizDetail());
            d.setServiceTime(o.getServiceTime());

            d.setSourcePplId(o.getSourcePplId());

            d.setInstanceNumApplyAfter(o.getInstanceNumApplyAfter());
            d.setTotalCoreApplyAfter(o.getTotalCoreApplyAfter());
            d.setTotalGpuNumApplyAfter(o.getTotalGpuNumApplyAfter());
            d.setYunxiaoOrderId(o.getYunxiaoOrderId());
            d.setPlacementGroup(o.getPlacementGroup());

            // database & cos
            d.setDatabaseName(o.getDatabaseName());
            d.setMoreThanOneAZ(o.getMoreThanOneAZ());
            d.setDatabaseStorageType(o.getDatabaseStorageType());
            d.setDeployType(o.getDeployType());
            d.setFrameworkType(o.getFrameworkType());
            d.setSliceNum(o.getSliceNum());
            d.setReplicaNum(o.getReplicaNum());
            d.setReadOnlyNum(o.getReadOnlyNum());
            d.setDatabaseSpecs(o.getDatabaseSpecs());
            d.setDatabaseStorage(o.getDatabaseStorage());
            d.setTotalDatabaseStorage(o.getTotalDatabaseStorage());
            d.setTotalMemory(o.getTotalMemory());
            d.setInstanceModelCoreNum(o.getInstanceModelCoreNum());
            d.setInstanceModelRamNum(o.getInstanceModelRamNum());

            d.setCosStorageType(o.getCosStorageType());
            d.setCosAZ(o.getCosAZ());
            d.setCosStorage(o.getCosStorage());
            d.setTotalCosStorage(o.getTotalCosStorage());
            d.setBandwidth(o.getBandwidth());
            d.setQps(o.getQps());

            return d;
        });
        demandDBHelper.insertBatchWithoutReturnId(recordItems);

        // 更新 version
        groupDO.setCurrentProcessor(PplVersionGroupStatusEnum.getProcessor(pplCommonService,
                groupDO.getStatus(), groupDO.getIndustryDept(), groupDO.getProduct()));
        demandDBHelper.update(groupDO);

        GroupRecordAndItem result = new GroupRecordAndItem();
        result.setGroupDO(groupDO);
        result.setRecord(recordDO);
        result.setItems(recordItems);
        return result;
    }

    @Override
    public CheckStockSupplyResp checkVersionGroup(String versionCode) {
        PplVersionDO pplVersionDO = demandDBHelper.getOne(PplVersionDO.class, "where version_code = ? ", versionCode);
//        List<PplVersionGroupDO> notSubmit = demandDBHelper.getAll(PplVersionGroupDO.class,
//                "where version_code = ? and product != ? and status not in (?) ", versionCode,
//                Ppl13weekProductTypeEnum.GPU.getName(),
//                Arrays.asList(PplVersionGroupStatusEnum.IN_SUBMIT.getCode(),
//                        PplVersionGroupStatusEnum.COMD_INTERVENE.getCode()),
//                PplVersionGroupStatusEnum.STOCK_SUPPLY.getCode());
        CheckStockSupplyResp resp = new CheckStockSupplyResp();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (pplVersionDO != null && pplVersionDO.getDeadline() != null && pplVersionDO.getDeadline()
                .after(new Date())) {
            resp.setCheckOne("本期需求未录入完成，截止时间为: " + formatter.format(pplVersionDO.getDeadline())
                    + "，如有特殊情况需要启动对冲，请联系dreamxin提前结束录入；");
        }
        List<PplVersionGroupDO> comdGroup = demandDBHelper.getAll(PplVersionGroupDO.class,
                "where version_code = ? and product not in (?) and status = ?", versionCode,
                Arrays.asList(Ppl13weekProductTypeEnum.GPU.getName(),
                        Ppl13weekProductTypeEnum.BM.getName()),
                PplVersionGroupStatusEnum.COMD_INTERVENE.getCode());
        if (!CollectionUtils.isEmpty(comdGroup)) {
            resp.setCheckTwo("当前有 " + comdGroup.size() + " 个分组的需求未干预，开始对冲后，将无法继续干预，是否确认？");
        } else {
            resp.setCheckTwo("所有提交分组均已干预");
        }
        return resp;
    }

    @Override
    public void checkPplIsExpired() {
        PplVersionDO versionDO = demandDBHelper.getOne(PplVersionDO.class, "where status = ?",
                PplVersionStatusEnum.PROCESS.getCode());
        LocalDate versionBeginDate = LocalDate.of(versionDO.getDemandBeginYear(), versionDO.getDemandBeginMonth(), 1);
        WhereSQL whereSQL = new WhereSQL();
        // 没有被预约的
        whereSQL.and("t1.status != ?", PplItemStatusEnum.APPLIED.getCode());
        // 开始购买日期小于当前ppl版本日期的
        whereSQL.and("t1.begin_buy_date < ?", versionBeginDate);
        // 只查用户录入的
        whereSQL.and("t2.source = ?", PplOrderSourceTypeEnum.IMPORT.getCode());
        List<Long> ids = demandDBHelper.getRaw(Long.class,
                "select t1.id from cloud_demand.ppl_item t1 left join cloud_demand.ppl_order t2 on t1.ppl_order = t2.ppl_order"
                        + " where t2.deleted = 0 and t2.source = ? and t1.deleted = 0 and t1.status != ?"
                        + " and t1.is_expired = 0 and t1.begin_buy_date < ?", PplOrderSourceTypeEnum.IMPORT.getCode(),
                PplItemStatusEnum.APPLIED.getCode(), versionBeginDate);

        if (ListUtils.isNotEmpty(ids)) {
            demandDBHelper.executeRaw("update ppl_item set is_expired = 1 where deleted = 0 and id in (?)", ids);
        }
    }

}
