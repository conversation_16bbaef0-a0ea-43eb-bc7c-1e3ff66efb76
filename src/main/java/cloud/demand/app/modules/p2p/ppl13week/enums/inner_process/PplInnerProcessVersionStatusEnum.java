package cloud.demand.app.modules.p2p.ppl13week.enums.inner_process;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum PplInnerProcessVersionStatusEnum {

    NEW("NEW", "待启动"),

    PROCESSING("PROCESSING", "生效中"),

    DONE("DONE", "已完结"),

    ;

    final private String code;
    final private String name;

    PplInnerProcessVersionStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PplInnerProcessVersionStatusEnum getByCode(String code) {
        for (PplInnerProcessVersionStatusEnum e : PplInnerProcessVersionStatusEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        PplInnerProcessVersionStatusEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}