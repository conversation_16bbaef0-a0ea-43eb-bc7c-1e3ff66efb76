package cloud.demand.app.modules.p2p.ppl13week.entity;

// package a.b.c;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("ppl_config_demand_scene")
public class PplConfigDemandSceneDO {

    /** id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private Date createTime;

    /** 更新时间<br/>Column: [update_time] */
    @Column(value = "update_time")
    private Date updateTime;

    /** 客户类型,存量客户,winback客户<br/>Column: [customer_type] */
    @Column(value = "customer_type")
    private String customerType;

    /** 需求类型<br/>Column: [demand_type] */
    @Column(value = "demand_type")
    private String demandType;

    /** 需求场景<br/>Column: [demand_scene] */
    @Column(value = "demand_scene")
    private String demandScene;

    /** 云霄需求代号<br/>Column: [yunxiao_reason_type] */
    @Column(value = "yunxiao_reason_type")
    private String yunxiaoReasonType;

}