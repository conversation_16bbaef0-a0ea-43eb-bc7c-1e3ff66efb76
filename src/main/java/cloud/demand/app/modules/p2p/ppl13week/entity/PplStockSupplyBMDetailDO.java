package cloud.demand.app.modules.p2p.ppl13week.entity;

// package a.b.c;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.common.excel.GroupConstant;
import cloud.demand.app.common.excel.convert.MatchTypeName2CodeConvert;
import cloud.demand.app.common.excel.core.annotation.DotExcelEntity;
import cloud.demand.app.common.excel.core.annotation.DotExcelField;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Table("ppl_stock_supply_bm_detail")
@DotExcelEntity
public class PplStockSupplyBMDetailDO extends BaseDO {

    /** 对冲批次id<br/>Column: [supply_gpu_id] */
    @Column(value = "supply_bm_id")
    private Long supplyBmId;

    /** ''ppl需求id<br/>Column: [ppl_id] */
    @Column(value = "ppl_id")
    @DotExcelField(group = GroupConstant.STOCK_SUPPLY_BM_IMPORT, excelColumnName = "pplId")
    private String pplId;

    /** 满足的类型，枚举,  */
    @Column(value = "match_type")
    @DotExcelField(group = GroupConstant.STOCK_SUPPLY_BM_IMPORT, excelColumnName = "满足方式",
            converter = MatchTypeName2CodeConvert.class)
    private String matchType;

    /** 可用区，看看要不要存id<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    @DotExcelField(group = GroupConstant.STOCK_SUPPLY_BM_IMPORT, excelColumnName = "满足可用区")
    private String zoneName;

    /** 实际满足的机型，库存满足时提供<br/>Column: [match_instance_type] */
    @Column(value = "match_instance_type")
    @DotExcelField(group = GroupConstant.STOCK_SUPPLY_BM_IMPORT, excelColumnName = "满足机型")
    private String matchInstanceType;

    /** 如果是采购，表示采购对应的母机的机型<br/>Column: [host_type] */
    @Column(value = "host_type")
    @DotExcelField(group = GroupConstant.STOCK_SUPPLY_BM_IMPORT, excelColumnName = "满足的母机机型")
    private String hostType;

    /** 如果是采购，表示采购对应的母机的数量（可能是小数）<br/>Column: [host_num] */
    @Column(value = "host_num")
    @DotExcelField(group = GroupConstant.STOCK_SUPPLY_BM_IMPORT, excelColumnName = "满足的母机台数")
    private BigDecimal hostNum;

    @Column(value = "match_total_core")
    @DotExcelField(group = GroupConstant.STOCK_SUPPLY_BM_IMPORT, excelColumnName = "满足核心数")
    private Integer matchTotalCore;

    @Column(value = "remark")
    @DotExcelField(group = GroupConstant.STOCK_SUPPLY_BM_IMPORT, excelColumnName = "原因")
    private String remark;

}