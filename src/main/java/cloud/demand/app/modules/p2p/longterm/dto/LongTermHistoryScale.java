package cloud.demand.app.modules.p2p.longterm.dto;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class LongTermHistoryScale {

    @Column(value = "stat_time")
    private String statTime;

    @Column(value = "year")
    private Integer year;

    @Column(value = "quarter")
    private Integer quarter;

    @Column(value = "month")
    private Integer month;

    @Column(value = "common_customer_short_name")
    private String commonCustomerShortName;

    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "product_group")
    private String productGroup;

    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "change_bill_core_from_last_month1")
    private BigDecimal changeBillCoreFromLastMonth;

    @Column(value = "change_bill_gpu_from_last_month1")
    private BigDecimal changeBillGpuFromLastMonth;

//    @Column(value = "demand_type")
//    private String demandType;

}
