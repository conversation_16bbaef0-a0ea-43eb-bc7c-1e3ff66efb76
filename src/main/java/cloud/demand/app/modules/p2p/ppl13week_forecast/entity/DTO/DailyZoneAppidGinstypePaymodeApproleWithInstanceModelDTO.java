package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class DailyZoneAppidGinstypePaymodeApproleWithInstanceModelDTO
        extends DailyZoneAppidGinstypePaymodeApproleDTO{


    @Column(value = "ginstype")
    private String ginstype;


    @Column(value = "zone_name")
    private String zoneName;

}
