package cloud.demand.app.modules.p2p.product_demand.controller;

import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.ValidateResultDTO;
import cloud.demand.app.modules.p2p.product_demand.Constant;
import cloud.demand.app.modules.p2p.product_demand.dto.ExtendsBudgetDTO;
import cloud.demand.app.modules.p2p.product_demand.dto.ForceRollBackDemandGroupReq;
import cloud.demand.app.modules.p2p.product_demand.dto.ListProductDemandGroupReq;
import cloud.demand.app.modules.p2p.product_demand.dto.QueryDemandInfoReq;
import cloud.demand.app.modules.p2p.product_demand.dto.SaveDTO;
import cloud.demand.app.modules.p2p.product_demand.dto.SaveItemDTO;
import cloud.demand.app.modules.p2p.product_demand.dto.SummaryStatisticReq;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionDO;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionGroupDO;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionGroupWithVersionVO;
import cloud.demand.app.modules.p2p.product_demand.enums.ProductDemandAuthRoleStatusEnum;
import cloud.demand.app.modules.p2p.product_demand.enums.ProductDemandGroupStatusEnum;
import cloud.demand.app.modules.p2p.product_demand.enums.ProductDemandVersionStatusEnum;
import cloud.demand.app.modules.p2p.product_demand.service.ProductDemandAuthService;
import cloud.demand.app.modules.p2p.product_demand.service.ProductDemandService;
import cloud.demand.app.modules.p2p.product_demand.service.ProductDemandVersionService;
import cloud.demand.app.modules.p2p.product_demand.service.ProductTodoService;
import cloud.demand.app.web.model.common.DownloadBean;
import com.google.common.collect.ImmutableList;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;
import yunti.boot.security.CurrentUser;
import yunti.boot.security.TofUser;
import yunti.boot.util.YuntiUtils;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Tag(name = "产品全年需求模块-需求模块")
@JsonrpcController("/product/annual/api")
@Slf4j
public class ProductDemandController {

    @Resource
    ProductDemandVersionService productDemandVersionService;
    @Resource
    ProductDemandService productDemandService;
    @Resource
    ProductDemandAuthService productDemandAuthService;
    @Resource
    ProductTodoService productTodoService;

    @RequestMapping
    public Object decodeExcel(@RequestParam("file") MultipartFile file,
            @RequestParam(name = "version") String version) {
        return productDemandService.decodeDemandExcel(file, version);
    }

    @RequestMapping
    public Object extendsBudget(@JsonrpcParam @Valid ExtendsBudgetDTO req,
                                BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        return productDemandService.extendsBudget(req);
    }

    @RequestMapping
    public Object save(@JsonrpcParam @Valid SaveDTO saveDTO,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        ProductDemandVersionDO versionDO = productDemandVersionService.getVersion(saveDTO.getVersion());
        if (versionDO == null) {
            throw new BizException("找不到对应的需求版本");
        }
        // 无关月份将被忽略
        List<YearMonth> yearMonthList = versionDO.actualForecastYearMonthList();
        // saveDTO的data转saveItemDTO
        List<SaveItemDTO> saveItemDTOS = SaveItemDTO.mapToDTO(saveDTO.getData(), yearMonthList);
        return productDemandService.overrideSave(versionDO, saveDTO.getPlanProduct(), saveItemDTOS);
    }

    @RequestMapping
    public Object saveAndSubmit(@JsonrpcParam @Valid SaveDTO saveDTO,
            BindingResult bindingResult, @CurrentUser TofUser user) {
        ValidateResultDTO resultDTO = (ValidateResultDTO) save(saveDTO, bindingResult);
        if (!resultDTO.isHasError()) {
            productTodoService.startProductDemandApproveFlow(resultDTO.getGroupId(), user.getUsername());
        }
        return resultDTO;
    }

    @RequestMapping
    public Object queryCurrentVersionPlanProductMapStatus(@CurrentUser TofUser user) {
        return productDemandService.queryCurrentVersionPlanProductMapStatus(
                user.getUsername());
    }

    @RequestMapping
    public Object forceRollBackDemandGroup(@JsonrpcParam @Valid ForceRollBackDemandGroupReq req,
                                           @CurrentUser TofUser user,
                                           BindingResult bindingResult) {

        YuntiUtils.throwErrorsIfAny(bindingResult);
        if (!productDemandAuthService.hasAdmin(user.getUsername())) {
            throw new BizException("当前用户非管理员，无权限强制打回");
        }
        ProductDemandVersionGroupDO versionGroupDO = productDemandService.getGroupById(req.getGroupId());
        if (versionGroupDO == null || !versionGroupDO.getStatus().equals(req.getCurStatus())) {
            throw new BizException("单据状态已发送变化，请刷新页面后重试.");
        }
        ProductDemandVersionDO version = productDemandVersionService.getVersion(versionGroupDO.getDemandVersion());
        if (version == null) {
            throw new BizException("所属版本配置不存在");
        }
        if (!ProductDemandVersionStatusEnum.ENABLED.getCode().equals(version.getStatus())) {
            throw new BizException("所属版本不在开放期，不允许强制打回");
        }
        if (!ProductDemandGroupStatusEnum.COMPLETED.getCode().equals(versionGroupDO.getStatus())) {
            throw new BizException("当前步骤不允许强制打回");
        }
        productDemandService.forceRollBackDemandGroup(versionGroupDO, req.getApproveMemo(), user.getUsername());
        return ImmutableList.of();
    }

    @RequestMapping
    public Object listGroup(@CurrentUser TofUser user, @JsonrpcParam @Valid ListProductDemandGroupReq req,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        Boolean isCOMD = productDemandAuthService.hasCOMDRole(user.getUsername());
        if (!isCOMD) {
            // 如果不是 COMD 的同事，会进这里，列出这个人有哪些规划产品的权限
            Set<String> planProductSet = productDemandAuthService.listMyPlanProduct(user.getUsername(),
                    ProductDemandAuthRoleStatusEnum.listAll());
            // 如果一个规划产品都没有，就没数据
            if (CollectionUtils.isEmpty(planProductSet)) {
                return ImmutableList.of();
            }
            // 检查筛选项
            if (CollectionUtils.isEmpty(req.getPlanProduct())) {
                // 如果没有筛选项，手动添加一个筛选项，只能看到有权限的规划产品
                req.setPlanProduct(new LinkedList<>(planProductSet));
            } else {
                // 如果有筛选项，取交集
                req.getPlanProduct().removeIf(p -> !planProductSet.contains(p));
                // 如果交集里一个规划产品都没有，就没数据
                if (CollectionUtils.isEmpty(req.getPlanProduct())) {
                    return ImmutableList.of();
                }
            }
        }
        return productDemandService.listGroup(req).stream()
                .map(ListGroupItem::toItem)
                .collect(Collectors.toList());
    }

    @RequestMapping
    public Object queryDemandInfoReq(@CurrentUser TofUser user, @JsonrpcParam QueryDemandInfoReq req) {
        return productDemandService.queryDemandInfoReq(req, user.getUsername());
    }

    @RequestMapping
    public DownloadBean exportDemandInfoExcel(@CurrentUser TofUser user, @JsonrpcParam QueryDemandInfoReq req) {
        FileNameAndBytesDTO fileNameAndBytesDTO = productDemandService.exportDemandDetail(req, user.getUsername());
        return new DownloadBean(fileNameAndBytesDTO.getFileName(), fileNameAndBytesDTO.getBytes());
    }

    @RequestMapping
    public DownloadBean exportTemplate() {
        FileNameAndBytesDTO fileNameAndBytesDTO = productDemandService.exportTemplate();
        return new DownloadBean(fileNameAndBytesDTO.getFileName(), fileNameAndBytesDTO.getBytes());
    }

    @RequestMapping
    public Object queryDemandGroupSummaryByIndustryName(
            @JsonrpcParam @Valid QueryDemandGroupSummaryByIndustryNameReq req,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        SummaryStatisticReq innerReq = new SummaryStatisticReq();
        innerReq.setGroupId(req.getGroupId());
        innerReq.setLastVersion(req.getLastVersion());
        innerReq.setField("industryName");
        return productDemandService.summaryStatisticByField(innerReq);
    }

    @RequestMapping
    public Object queryDemandSummaryByField(@JsonrpcParam @Valid QueryDemandSummaryByFieldReq req,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        SummaryStatisticReq innerReq = QueryDemandSummaryByFieldReq.copy(req);
        return productDemandService.summaryStatisticByField(innerReq);
    }

    @RequestMapping
    public Object listVersionDemandItems(@JsonrpcParam @Valid ListVersionDemandItemsReq req,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        SummaryStatisticReq innerReq = ListVersionDemandItemsReq.copy(req);
        return productDemandService.listVersionDemandItems(innerReq);
    }

    @RequestMapping
    public Object summaryStatistic(@JsonrpcParam @Valid MySummaryStatisticReq req,
            BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        SummaryStatisticReq innerReq = MySummaryStatisticReq.copy(req);
        return productDemandService.summaryStatistic(innerReq);
    }

    @RequestMapping
    public Object listComparableVersionList() {
        return productDemandService.listComparableVersionList();
    }

    @Data
    static class MySummaryStatisticReq {

        @NotNull(message = "需求分组ID不能为空")
        private Long groupId;
        private String lastVersion;
        private List<String> industryName;
        private List<String> customerName;
        private List<String> zoneName;
        private List<String> deviceTypeName;
        private List<String> reason;
        private List<String> reasonType;

        public static SummaryStatisticReq copy(MySummaryStatisticReq req) {
            SummaryStatisticReq summaryStatisticReq = new SummaryStatisticReq();
            summaryStatisticReq.setGroupId(req.getGroupId());
            summaryStatisticReq.setLastVersion(req.getLastVersion());
            summaryStatisticReq.setIndustryName(req.getIndustryName());
            summaryStatisticReq.setCustomerName(req.getCustomerName());
            summaryStatisticReq.setZoneName(req.getZoneName());
            summaryStatisticReq.setDeviceTypeName(req.getDeviceTypeName());
            summaryStatisticReq.setReason(req.getReason());
            summaryStatisticReq.setReasonType(req.getReasonType());
            return summaryStatisticReq;
        }
    }

    @Data
    static class QueryDemandGroupSummaryByIndustryNameReq {

        @NotNull(message = "需求分组ID不能为空")
        private Long groupId;
        private String lastVersion;
    }

    @Data
    static class ListVersionDemandItemsReq {

        @NotEmpty(message = "版本号不能为空")
        private String version;
        private List<String> industryName;
        private List<String> customerName;
        private List<String> zoneName;
        private List<String> deviceTypeName;
        private List<String> reasonType;
        private List<String> reason;
        private List<String> planProduct;
        private Boolean core;

        public static SummaryStatisticReq copy(ListVersionDemandItemsReq one) {
            SummaryStatisticReq summaryStatisticReq = new SummaryStatisticReq();
            summaryStatisticReq.setVersion(one.getVersion());
            summaryStatisticReq.setIndustryName(one.getIndustryName());
            summaryStatisticReq.setCustomerName(one.getCustomerName());
            summaryStatisticReq.setZoneName(one.getZoneName());
            summaryStatisticReq.setDeviceTypeName(one.getDeviceTypeName());
            summaryStatisticReq.setReason(one.getReason());
            summaryStatisticReq.setPlanProduct(one.getPlanProduct());
            summaryStatisticReq.setCore(one.getCore());
            summaryStatisticReq.setReasonType(one.getReasonType());
            return summaryStatisticReq;
        }
    }

    @Data
    static class QueryDemandSummaryByFieldReq {

        @NotEmpty(message = "版本号不能为空")
        private String version;
        private String lastVersion;
        private List<String> industryName;
        private List<String> customerName;
        private List<String> zoneName;
        private List<String> deviceTypeName;
        private List<String> reasonType;
        private List<String> reason;
        private List<String> planProduct;
        @NotEmpty(message = "汇总字段不能为空")
        private String field;
        private Boolean core;

        public static SummaryStatisticReq copy(QueryDemandSummaryByFieldReq one) {
            SummaryStatisticReq summaryStatisticReq = new SummaryStatisticReq();
            summaryStatisticReq.setVersion(one.getVersion());
            summaryStatisticReq.setLastVersion(one.getLastVersion());
            summaryStatisticReq.setIndustryName(one.getIndustryName());
            summaryStatisticReq.setCustomerName(one.getCustomerName());
            summaryStatisticReq.setZoneName(one.getZoneName());
            summaryStatisticReq.setDeviceTypeName(one.getDeviceTypeName());
            summaryStatisticReq.setReason(one.getReason());
            summaryStatisticReq.setPlanProduct(one.getPlanProduct());
            summaryStatisticReq.setField(one.getField());
            summaryStatisticReq.setCore(one.getCore());
            summaryStatisticReq.setReasonType(one.getReasonType());
            return summaryStatisticReq;
        }
    }


    @Data
    static class ListGroupItem {

        private Long groupId;
        private String demandVersion;
        private String versionName;
        private String planProduct;
        private String submitUser;
        private String status;
        private Integer forecastFromYear;
        private Integer forecastFromMonth;
        private Integer forecastToYear;
        private Integer forecastToMonth;

        public static ListGroupItem toItem(ProductDemandVersionGroupWithVersionVO vo) {
            ListGroupItem listGroupItem = new ListGroupItem();
            listGroupItem.setGroupId(vo.getId());
            listGroupItem.setDemandVersion(vo.getDemandVersion());
            listGroupItem.setVersionName(vo.getVersionDO().getName());
            listGroupItem.setPlanProduct(vo.getPlanProduct());
            listGroupItem.setSubmitUser(StringUtils.isEmpty(vo.getSubmitUser())
                    ? Constant.SYSTEM_INIT_CREATOR : vo.getSubmitUser());
            listGroupItem.setStatus(ProductDemandGroupStatusEnum.getNameByCode(vo.getStatus()));
            if (vo.getVersionDO() != null) {
                ProductDemandVersionDO version = vo.getVersionDO();
                listGroupItem.setForecastFromMonth(version.getForecastFromMonth());
                listGroupItem.setForecastFromYear(version.getForecastFromYear());
                listGroupItem.setForecastToMonth(version.getForecastToMonth());
                listGroupItem.setForecastToYear(version.getForecastToYear());
            }
            return listGroupItem;

        }
    }
}
