package cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao;

import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import lombok.Getter;

import java.util.Objects;

@Getter
public enum YunxiaoOrderTypeEnum {

    Normal("Normal", "正常"),

    Elastic("Elastic", "弹性")
    ;

    final private String code;
    final private String name;

    YunxiaoOrderTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static YunxiaoOrderTypeEnum fromDemandType(String demandType) {
        PplDemandTypeEnum e = PplDemandTypeEnum.getByCode(demandType);
        if (e == PplDemandTypeEnum.ELASTIC) {
            return Elastic;
        } else {
            return Normal; // 默认
        }
    }

    public static String toDemandType(YunxiaoOrderTypeEnum e) {
        if (e == null) {
            return "";
        }
        switch (e) {
            case Normal:
                return PplDemandTypeEnum.NEW.getCode();
            case Elastic:
                return PplDemandTypeEnum.ELASTIC.getCode();
            default:
                return "";
        }
    }

    public static YunxiaoOrderTypeEnum getByCode(String code) {
        for (YunxiaoOrderTypeEnum e : YunxiaoOrderTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        YunxiaoOrderTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}