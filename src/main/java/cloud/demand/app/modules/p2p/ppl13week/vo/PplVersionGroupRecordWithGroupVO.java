package cloud.demand.app.modules.p2p.ppl13week.vo;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

@Data
public class PplVersionGroupRecordWithGroupVO extends PplVersionGroupRecordDO {


    @RelatedColumn(localColumn = "version_group_id", remoteColumn = "id")
    private PplVersionGroupDO groupDO;

}
