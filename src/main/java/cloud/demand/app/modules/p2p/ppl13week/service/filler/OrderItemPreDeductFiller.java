package cloud.demand.app.modules.p2p.ppl13week.service.filler;

public interface OrderItemPreDeductFiller {

    String provideOrderNumber();

    String provideZoneName();

    String provideInstanceModel();

    int provideInstanceNum();

    /**
     *  填充字段：申请预扣实例数
     */
    void fillPreDeductNum(Integer preDeductNum);

    /**
     *  填充字段：申请预扣核心数
     */
    void fillPreDeductCore(Integer preDeductCore);

    /**
     *  填充字段：实际预扣实例数
     */
    void fillActualPreDeductNum(Integer actualPreDeductNum);

    /**
     *  填充字段：实际预扣核心数
     */
    void fillActualPreDeductCore(Integer actualPreDeductCore);

    /**
     *  填充字段：申请预扣GPU卡数
     */
    default void fillPreDeductGpuNum(Integer preDeductGpuNum) {

    }

    /**
     *  填充字段：实际预扣GPU卡数
     */
    default void fillActualPreDeductGpuNum(Integer actualPreDeductGpuNum) {

    }

}
