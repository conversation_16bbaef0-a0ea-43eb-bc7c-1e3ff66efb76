package cloud.demand.app.modules.p2p.ppl13week.dto.version;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 云运管使用模型预测的结果进行一键干预
 */
@Data
public class ComdInterveneByForecastModelReq {

    /**
     * 当前版本的分组id
     */
    @NotNull(message = "版本分组id不能为空")
    private Long versionGroupId;

    /**
     * 是否保留客户维度，如果保留uin，即不去重；如果不保留，即去重。
     */
    private Boolean isKeepUin;

    /**
     * 覆盖内外部业务类型：
     * 值：外部业务、内部业务
     * 如果没有值，那就是不区分，等于是全部
     */
    private String bizRangeType;

    /**
     * 新增退回类型：NEW 新增 RETURN 退回，空值表示所有
     */
    private String demandType;

    /**
     * 是否覆盖所有部门
     */
    private Boolean isCoverAllDept;

    /**
     * 覆盖的开始年月，格式yyyyMM
     */
    private String startYearMonth;

    /**
     * 覆盖的结束年月，格式yyyyMM
     */
    private String endYearMonth;

}
