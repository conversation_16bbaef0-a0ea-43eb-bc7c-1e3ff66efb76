package cloud.demand.app.modules.p2p.longterm.service.impl;

import cloud.demand.app.common.utils.EStream;
import cloud.demand.app.common.utils.ListUtils2;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermGroupItemDTO;
import cloud.demand.app.modules.p2p.longterm.controller.req.ParseImportExcelReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.QueryLongtermDictReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.SaveLongtermGroupItemReq;
import cloud.demand.app.modules.p2p.longterm.controller.resp.DownloadExcelResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.ImportFromExcelResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.QueryLongtermImportDictResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.SaveLongtermGroupItemResp;
import cloud.demand.app.modules.p2p.longterm.dto.excel.ExcelOutputDTO;
import cloud.demand.app.modules.p2p.longterm.dto.excel.ExcelOutputFixColumnAccessor;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupItemSourceTypeEnum;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupItemTimeUnitEnum;
import cloud.demand.app.modules.p2p.longterm.key_accessor.LongtermImportItemKeyAccessor;
import cloud.demand.app.modules.p2p.longterm.service.DatabaseAccess;
import cloud.demand.app.modules.p2p.longterm.service.LongtermDictService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermGroupItemService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermVersionImportService;
import cloud.demand.app.modules.p2p.longterm.service.impl.excel.DownloadExcelDataBuilder;
import cloud.demand.app.modules.p2p.longterm.service.impl.excel.DownloadExcelHeaderBuilder;
import cloud.demand.app.modules.p2p.longterm.service.impl.excel.DownloadExcelHeaderBuilder.HeaderInfoDTO;
import cloud.demand.app.modules.p2p.longterm.service.impl.excel.DownloadExcelSheetHandler;
import cloud.demand.app.modules.p2p.longterm.service.impl.excel.DownloadExcelSheetHandler.ColumnInfo;
import cloud.demand.app.modules.p2p.longterm.service.impl.excel.DownloadExcelStyleHandler;
import cloud.demand.app.modules.p2p.longterm.service.impl.excel.TrimStringToIntegerConverter;
import cloud.demand.app.modules.p2p.longterm.vo.VersionGroupRecordVO;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@Slf4j
public class LongtermVersionImportServiceImpl implements LongtermVersionImportService {

    @Resource
    DatabaseAccess databaseAccess;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DictService dictService;

    @Resource
    private LongtermDictService longtermDictService;

    @Resource
    LongtermGroupItemService longtermGroupItemService;

    @SneakyThrows
    @Override
    public QueryLongtermImportDictResp queryLongtermImportDict() {

        CompletableFuture<List<String>> gpuTypeFuture
                = CompletableFuture.supplyAsync(() -> longtermDictService.queryGpuType());
        CompletableFuture<Set<String>> regionNameFuture
                = CompletableFuture.supplyAsync(() -> longtermDictService.queryRegionNameToZoneName().keySet());
        Set<String> instanceFamily = longtermDictService.queryInstanceFamilyToInstanceType().keySet();

        Map<String, List<String>> productToInstanceFamily = longtermDictService.queryProductToInstanceFamily();

        Set<String> regionName = new HashSet<>(regionNameFuture.get());
        Map<String, List<String>> crMap = longtermDictService.getCustomhouseTitleToRegionName(regionName);
        regionName.add("随机地域");

        List<String> gpuType = gpuTypeFuture.get();
        QueryLongtermImportDictResp resp = new QueryLongtermImportDictResp();
        resp.setCustomhouseTitleToRegionName(crMap);
        resp.setRegionName(EStream.of(regionName).sorted().toList());
        resp.setInstanceFamily(EStream.of(instanceFamily).sorted().toList());
        resp.setGpuType(EStream.of(gpuType).sorted().toList());
        resp.setProductToInstanceFamily(productToInstanceFamily);
        return resp;
    }



    @Override
    @SneakyThrows
    @Synchronized(namespace = "longtermSaveItem", keyScript = "args[0].groupId")
    public ImportFromExcelResp importFromExcel(ParseImportExcelReq req) {


        Long recordId = req.getRecordId();
        VersionGroupRecordVO vgrInfo = databaseAccess.queryVersionAndGroupRecord(recordId);
        boolean if35 = vgrInfo.getVersionDO().getExtraIndustries().contains(vgrInfo.getVersionGroupDO().getIndustryDept());
        // 解析Excel文件
        List<ExcelOutputDTO> excelDataList = parseExcelAndCheckHeaders(req, vgrInfo, if35);
        // check 数据,校验数据范围是不是对的,一次性返回数据
        ValidDaterList errors = checkExcelColumnValue(excelDataList, vgrInfo, if35);
        // 这里重新获取到excel的表头解析,转换成save 的接口
        List<LongtermGroupItemDTO> items = transExcelDtoToSaveDto(req, vgrInfo, excelDataList, if35);

        if (items.isEmpty() && !req.getIsDelAll()) {
            throw BizException.makeThrow("excel中未解析到数据,为防止误删数据，请保留一条数据，并把预测值清空或改为0");
        }

        SaveLongtermGroupItemResp saveResp = new SaveLongtermGroupItemResp();
        saveResp.setSuccess(false);
        if (errors.isEmpty()) {
            SaveLongtermGroupItemReq saveReq = new SaveLongtermGroupItemReq();
            saveReq.setGroupId(vgrInfo.getVersionGroupDO().getId());
            saveReq.setGroupRecordId(vgrInfo.getVersionGroupRecordDO().getId());
            saveReq.setProduct(req.getProduct());
            saveReq.setIsDelAll(req.getIsDelAll());
            saveReq.setItems(items);
            saveResp = longtermGroupItemService.saveLongtermGroupItem(saveReq);
        }

        ImportFromExcelResp resp = new ImportFromExcelResp();
        resp.setSaveResp(saveResp);
        resp.setExcelDataList(excelDataList);
        resp.setItems(items);
        resp.setErrors(errors);
        return resp;
    }

    private boolean hasInput(ExcelOutputDTO excel, List<Integer> longTermIndexList) {
        for (Integer index : longTermIndexList) {
            if (DownloadExcelDataBuilder.getValueByIndex(excel, index) != null) {
                return true;
            }
        }
        return false;
    }

    private @NotNull ValidDaterList checkExcelColumnValue(
            List<ExcelOutputDTO> excelDataList,
            VersionGroupRecordVO vgrInfo, boolean if35) {
        ValidDaterList errors = new ValidDaterList();
        String emptyTemplate = "[%s]字段是必填项,不可以为空.";
        String inValidTmpl = "[%s]字段的值[%s],未在策略表中查找到,请检查或联系 fireflychen 配置";

        List<String> customhouseTitleDict = Lang.list("境内", "境外");
        List<String> demandTypeDict = PplDemandTypeEnum.names();
        List<String> demandTypeDict2 = PplDemandTypeEnum.names2();

        Set<String> topCustomers = vgrInfo.getVersionGroupDO().parseTopCustomersList();

        Map<String, List<String>> regionInfoMap = longtermDictService.queryRegionNameToZoneName();
        Map<String, List<String>> instanceInfoMap = longtermDictService.queryInstanceFamilyToInstanceType();
        List<String> gpuTypes = longtermDictService.queryGpuType();
        Map<String, List<String>> productToInstanceFamily = longtermDictService.queryProductToInstanceFamily();

        // 获取中长期开始录入的下标集合
        List<Integer> longTermIndexList = new ArrayList<>();
        Map<String, Integer> indexMap = DownloadExcelDataBuilder.buildExcelYQMIndexMap(vgrInfo, if35, DownloadExcelDataBuilder.getPpl13Month(vgrInfo));
        HeaderInfoDTO excelHeadInfo = DownloadExcelHeaderBuilder.getExcelHead(vgrInfo,
                vgrInfo.getVersionDO().getExtraIndustries().contains(vgrInfo.getVersionGroupDO().getIndustryDept()));
        for (DownloadExcelHeaderBuilder.HeaderInfoItemDTO header : excelHeadInfo.getDataHeaders()) {
            if (LongtermVersionGroupItemSourceTypeEnum.LONGTERM_INPUT.equals(header.getSourceTypeEnum())
            || LongtermVersionGroupItemSourceTypeEnum.PPL_13_WEEK.equals(header.getSourceTypeEnum())
            || LongtermVersionGroupItemSourceTypeEnum.ORDER.equals(header.getSourceTypeEnum())) {
                longTermIndexList.add(indexMap.get(DownloadExcelDataBuilder.getYQMKey(header.getDemandYear(), header.getDemandQuarter(), header.getDemandMonth())));
            }
        };

        for (ExcelOutputDTO excel : excelDataList) {

            // 判断本条预测有没有填写中长期录入部分，如果没有，那么不校验
            // 判断中长期录入的下标们有没有录入，如果没有，那就没录入
            if (!hasInput(excel, longTermIndexList)) {
                continue;
            }


            // 非GPU gpu卡需要为空
            boolean productNotBlank = Strings.isNotBlank(excel.getProduct());
            boolean productNotGpu = !Strings.equals(excel.getProduct(), Ppl13weekProductTypeEnum.GPU.getName());
            boolean gpuTypeNotBlank = Strings.isNotBlank(excel.getGpuType());

            errors.addIfEmpty(excel.getCustomhouseTitle(), emptyTemplate, "境内外");
            errors.addIfEmpty(excel.getCustomerShortName(), emptyTemplate, "客户简称");
            errors.addIfEmpty(excel.getRegionName(), emptyTemplate, "地域");
            if (productNotGpu) {
                errors.addIfEmpty(excel.getInstanceFamily(), emptyTemplate, "实例族");
            }
            errors.addIfEmpty(excel.getDemandType(), emptyTemplate, "需求类型");
            boolean isTopCustomer = topCustomers.contains(excel.getCustomerShortName());
            if (isTopCustomer) {
                if (productNotGpu) {
                    errors.addIfEmpty(excel.getInstanceType(), emptyTemplate, "大客户的实例类型");
                }
                errors.addIfEmpty(excel.getZoneName(), emptyTemplate, "大客户的可用区");
            }
            String product = excel.getProduct();
            if (product != null && product.contains("GPU")) {
                errors.addIfEmpty(excel.getGpuType(), emptyTemplate, "GPU产品的GPU卡型");
                errors.addIfNotContain(gpuTypes, excel.getGpuType(),
                        inValidTmpl, "GPU卡型", excel.getGpuType());
            }

            errors.addIfNotContain(customhouseTitleDict, excel.getCustomhouseTitle(),
                    inValidTmpl,  "境内外", excel.getCustomhouseTitle());

            if (Strings.equals(excel.getCustomhouseTitle(), "境外")) {
                errors.addIfNotContain(regionInfoMap.keySet(), excel.getRegionName(),
                        inValidTmpl, "地域", excel.getRegionName());
            } else {
                Set<String> regionSet = new HashSet<>(regionInfoMap.keySet());
                regionSet.add("随机地域"); // 境内随机地域
                errors.addIfNotContain(regionSet, excel.getRegionName(), inValidTmpl, "地域",
                        excel.getRegionName());
            }
            if (!Strings.isBlank(excel.getRegionName())) {
                List<String> zoneNameDict = regionInfoMap.getOrDefault(excel.getRegionName(), new ArrayList<>());
                zoneNameDict.add("随机可用区");
                errors.addIfNotContain(zoneNameDict, excel.getZoneName(), inValidTmpl,  "可用区",
                        excel.getZoneName());
            }
            if (Strings.isNotBlank(excel.getInstanceFamily())) {
                List<String> instanceFamily = productToInstanceFamily.get(excel.getProduct());
                String tmpl = "[%s]字段的值[%s],未在策略表中查找到,请查看产品-机型族字典sheet检查或联系 dongqinfu 配置";
                errors.addIfNotContain(instanceFamily, excel.getInstanceFamily(),
                        tmpl,  "机型族", excel.getInstanceFamily());
            }
            if (Strings.isNotBlank(excel.getInstanceType())) {
                List<String> instanceType = instanceInfoMap.get(excel.getInstanceFamily());
                errors.addIfNotContain(instanceType, excel.getInstanceType(),
                        inValidTmpl, "实例大类", excel.getInstanceType());
            }

            if (Strings.isNotBlank(excel.getDemandType())
                    && (!demandTypeDict.contains(excel.getDemandType()) && !demandTypeDict2.contains(
                    excel.getDemandType()))) {
                errors.add(String.format(inValidTmpl,  "需求类型", excel.getDemandType()));
            }

            if (productNotBlank && productNotGpu && gpuTypeNotBlank) {
                String format = "[%s]字段的值[%s],录入产品%s中,不属于GPU，存在非空GPU卡型，GPU卡型请保持为空";
                errors.add(String.format(format, "GPU卡型", excel.getGpuType(), excel.getProduct()));
            }

            boolean isPaasProduct = Strings.equals(excel.getProduct(), "PAAS产品");
            if (!isPaasProduct && Strings.isNotBlank(excel.getPaasProduct())){
                String format = "[%s]字段的值[%s], %s产品此字段请保持为空。";
                errors.add(String.format(format, "PAAS产品", excel.getPaasProduct(), excel.getProduct()));
            }

        }
        return errors;
    }

    public static class ValidDaterList extends ArrayList<String> {

        public void addIfEmpty(String column, String format, Object... args) {
            if (Strings.isBlank(column)) {
                this.add(String.format(format, args));
            }
        }

        public void addIfNotContain(Collection<String> dict, String column, String format, Object... args) {
            if (!Strings.isBlank(column) && dict != null && !dict.contains(column)) {
                this.add(String.format(format, args));
            }
        }
    }

    private static @NotNull List<ExcelOutputDTO> parseExcelAndCheckHeaders(ParseImportExcelReq req,
            VersionGroupRecordVO vgrInfo, boolean if35) throws IOException {

        List<List<String>> finalHeads = new ArrayList<>();
        HeaderInfoDTO excelHeadInfo = DownloadExcelHeaderBuilder.getExcelHead(vgrInfo,
                vgrInfo.getVersionDO().getExtraIndustries().contains(vgrInfo.getVersionGroupDO().getIndustryDept()));
        List<List<String>> easyExcelHeaders = excelHeadInfo.getEasyExcelHeaders();
        // check 表头
        EasyExcel.read(req.getExcelFile().getInputStream(), new AnalysisEventListener<Object>() {
                    @Override
                    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                        List<String> head = IntStream.range(0, headMap.size())
                                .mapToObj(headMap::get).collect(Collectors.toList());
                        finalHeads.add(head);
                    }
                    @Override
                    public void invoke(Object data, AnalysisContext context) {}
                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {}
                }).headRowNumber(2)
                .registerConverter(new TrimStringToIntegerConverter())
                .sheet(DownloadExcelHeaderBuilder.SHEET_NAME).doRead();
        List<List<String>> realExcelHeads = rotate(finalHeads);
        for (int i = 0; i < easyExcelHeaders.size(); i++) {
            if (!Strings.equals(realExcelHeads.get(i).get(0), easyExcelHeaders.get(i).get(0))) {
                throw BizException.makeThrow("Excel 第%s列导入的表头不是导出的表头(导入表头: %s, 导出表头应该: %s),"
                                + "请先导出然后用导出的EXCEL填写后导入数据.",
                        convertToExcelColumn(i), realExcelHeads.get(i).get(0), easyExcelHeaders.get(i).get(0)
                );
            }
        }

        Map<String, ExcelOutputDTO> result = new HashMap<>();
        EasyExcel.read(req.getExcelFile().getInputStream(), ExcelOutputDTO.class, new AnalysisEventListener<Object>() {
            @Override
            public void invoke(Object data, AnalysisContext context) {
                if (!(data instanceof ExcelOutputFixColumnAccessor)) {
                    throw BizException.makeThrow("Excel 解析数据出现问题, 请联系firefly解决");
                }
                // Check if all fields are null or empty strings
                boolean allFieldsNullOrEmpty = true;
                for (Field field : data.getClass().getDeclaredFields()) {
                    field.setAccessible(true);
                    try {
                        // trim 前后的空白字符串
                        Object value = field.get(data);
                        if (value instanceof BigDecimal) {
                            field.set(data, ((BigDecimal) value).abs()); // 无论什么数据都转为正数
                        } else if (value instanceof String && Strings.isNotBlank((String) value)) {
                            field.set(data, Strings.trim((String) value));
                        }
                        // 全是空的直接不要数据
                        if (value != null && !(value instanceof String && ((String) value).isEmpty())) {
                            allFieldsNullOrEmpty = false;
                            break;
                        }
                    } catch (IllegalAccessException e) {
                        log.error("获取字段值失败: {}", e.getMessage());
                    }
                }
                if (allFieldsNullOrEmpty) {
                    return;
                }
                ExcelOutputDTO newData = (ExcelOutputDTO) data;
                String dataKey = newData.buildKey();
                ExcelOutputDTO oldData = result.get(dataKey);
                if (oldData != null) {
                    newData.addNum(oldData);
                }
                result.put(dataKey, newData);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).headRowNumber(2)
                .registerConverter(new TrimStringToIntegerConverter())
                .sheet(DownloadExcelHeaderBuilder.SHEET_NAME).doRead();
        return new ArrayList<>(result.values());
    }

    private static String convertToExcelColumn(int columnNumber) {
        StringBuilder columnName = new StringBuilder();
        while (columnNumber >= 0) {
            // append the current character to columnName string
            columnName.insert(0, (char) ('A' + columnNumber % 26));
            // update the columnNumber
            columnNumber = columnNumber / 26 - 1;
        }
        return columnName.toString();
    }

    private static @NotNull List<LongtermGroupItemDTO> transExcelDtoToSaveDto(
            ParseImportExcelReq req,
            VersionGroupRecordVO vgrInfo,
            List<ExcelOutputDTO> excelDataList, boolean if35) {

        List<LongtermGroupItemDTO> retItems = new ArrayList<>();

        HeaderInfoDTO excelHead = DownloadExcelHeaderBuilder.getExcelHead(vgrInfo,
                    vgrInfo.getVersionDO().getExtraIndustries().contains(vgrInfo.getVersionGroupDO().getIndustryDept()));
        List<DownloadExcelHeaderBuilder.HeaderInfoItemDTO> dataHeaders = excelHead.getDataHeaders();

        Map<Integer, List<Integer>> ppl13MonthMap = DownloadExcelDataBuilder.getPpl13Month(vgrInfo);
        Map<String, Integer> indexMap = DownloadExcelDataBuilder.buildExcelYQMIndexMap(vgrInfo, if35, ppl13MonthMap);
        for (ExcelOutputDTO excelData : excelDataList) {
            for (Map.Entry<String, Integer> entry : indexMap.entrySet()) {
                if (entry.getKey().contains("-1")) {
                    // 如果是辅助用的汇总列，那么跳过
                    continue;
                }
                LongtermVersionGroupItemSourceTypeEnum sourceTypeEnum = dataHeaders.get(entry.getValue() - 1).getSourceTypeEnum();
                BigDecimal excelVal = DownloadExcelDataBuilder.getValueByIndex(excelData, entry.getValue());
                if (excelVal == null)  {
                    // 为空的直接跳过
                    continue;
                }
                LongtermGroupItemDTO one = newSaveDto(req, excelData, sourceTypeEnum);
                Integer[] yqmByKey = DownloadExcelDataBuilder.getYQMByKey(entry.getKey());
                one.setTimeInfo(dataHeaders.get(entry.getValue()-1).getTimeUnitEnum(), yqmByKey[0], yqmByKey[1], yqmByKey[2]);
                setExcelInputValByProduct(req, one, excelVal);
                retItems.add(one);
            }
        }

        // 转换为名字为 CODE
        EStream.of(retItems).forEach((i) -> i.setDemandType(PplDemandTypeEnum.getCodeByName(i.getDemandType())));
        return retItems;
    }

    private static @NotNull LongtermGroupItemDTO newSaveDto(
            ParseImportExcelReq req,
            ExcelOutputDTO excelData,
            LongtermVersionGroupItemSourceTypeEnum sourceTypeEnum) {
        LongtermGroupItemDTO one = new LongtermGroupItemDTO();
        one.setProduct(req.getProduct());
        one.setSourceType(sourceTypeEnum.getCode());
        LongtermImportItemKeyAccessor.copy(excelData, one);
        one.setNote(excelData.getNote());
        return one;
    }

    private static void setExcelInputValByProduct(ParseImportExcelReq req, LongtermGroupItemDTO one,
            BigDecimal coreOrGpu) {
        if (req.getProduct() != null && req.getProduct().contains("GPU")) {
            one.setGpuNum(coreOrGpu);
        } else if (one.getProduct() != null && one.getProduct().contains("GPU")) {
            one.setGpuNum(coreOrGpu);
        } else {
            one.setCoreNum(coreOrGpu);
        }
    }


    public static <T> List<List<T>> rotate(List<List<T>> matrix) {
        int n = matrix.size();
        int m = matrix.get(0).size();
        List<List<T>> rotatedMatrix = new ArrayList<>();
        // 初始化 rotatedMatrix
        for (int i = 0; i < m; i++) {
            rotatedMatrix.add(new ArrayList<>());
        }
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < m; j++) {
                // 将 matrix 的行转为 rotatedMatrix 的列
                rotatedMatrix.get(j).add(matrix.get(i).get(j));
            }
        }
        return rotatedMatrix;
    }


    @Override
    public DownloadExcelResp downloadExcel(ParseImportExcelReq req) {
        Long recordId = req.getRecordId();

        // 获取数据
        VersionGroupRecordVO vgrInfo = databaseAccess.queryVersionAndGroupRecord(recordId);
        boolean if35 = vgrInfo.getVersionDO().getExtraIndustries().contains(vgrInfo.getVersionGroupDO().getIndustryDept());
        List<Object> excelData = getExcelData(vgrInfo, req, if35);

        // 构造excel
        HeaderInfoDTO excelHeadInfo = DownloadExcelHeaderBuilder.getExcelHead(vgrInfo, if35);
        List<List<String>> excelHead = excelHeadInfo.getEasyExcelHeaders();

        Map<String, List<String>> regionInfoMap = longtermDictService.queryRegionNameToZoneName();
        Map<String, List<String>> instanceInfoMap = longtermDictService.queryInstanceFamilyToInstanceType();
        List<String> gpuTypes = longtermDictService.queryGpuType();
        Map<String, List<String>> productToInstanceFamily = longtermDictService.queryProductToInstanceFamily();
        List<ProductInstanceFamily> instanceFamilyConfig = new ArrayList<>();
        productToInstanceFamily.forEach(
                (k, v) -> v.forEach(i -> instanceFamilyConfig.add(new ProductInstanceFamily(k, i)))
        );
        instanceFamilyConfig.add(0, new ProductInstanceFamily("产品", "可录入机型族"));

        Map<String, ColumnInfo> column2dictMap = new HashMap<>();
        column2dictMap.put("A", new ColumnInfo("产品", Lang.list(
                Ppl13weekProductTypeEnum.CVM.getName(), Ppl13weekProductTypeEnum.GPU.getName(),
                Ppl13weekProductTypeEnum.BM.getName(), Ppl13weekProductTypeEnum.PAAS.getName()
        ), "提示", "进行录入的时候，请和页面产品保持一致，其它产品数据将忽略"));

        column2dictMap.put("B", new ColumnInfo("境内外", Lang.list("境内", "境外")));
        List<String> regionName = EStream.of(regionInfoMap.keySet()).sorted().toList();
        List<String> regionNameDup = Lang.list();
        List<String> zoneName = EStream.of(regionName).map((key) -> {
            List<String> values = regionInfoMap.get(key);
            regionNameDup.addAll(Collections.nCopies(values.size(), key));
            return values;
        }).flatMap(Collection::stream).toList();
        regionNameDup.add("随机地域");

        column2dictMap.put("D", new ColumnInfo("地域", regionNameDup, "示例：",
                "广州\n深圳\n上海\n更多名称请打开字典sheet查看"));
        zoneName.add("随机可用区");
        column2dictMap.put("E", new ColumnInfo("可用区", zoneName, "提示：", "请确保和地域映射关系正确"));

        List<String> instanceFamily = instanceInfoMap.keySet().stream().sorted().collect(Collectors.toList());
        List<String> instanceFamilyDup = Lang.list();
        List<String> instanceType = EStream.of(instanceFamily).map((key) -> {
            List<String> values = instanceInfoMap.get(key);
            instanceFamilyDup.addAll(Collections.nCopies(values.size(), key));
            return values;
        }).flatMap(Collection::stream).toList();
        column2dictMap.put("F",
                new ColumnInfo("机型族", instanceFamilyDup, "示例：",
                        "标准型(SA5、S8等)\n" + "计算型(C6、CN3等)\n" + "内存型(MA5、M8等)\n" +
                                "更多类型请打开产品-机型族字典\nsheet查看"
                ));
        column2dictMap.put("G", new ColumnInfo("实例类型", instanceType, "示例：", "请确保和实例族映射关系正确"));
        column2dictMap.put("H",
                new ColumnInfo("PAAS产品", Lang.list(
                        Ppl13weekProductTypeEnum.EMR.getName(), Ppl13weekProductTypeEnum.ES.getName(),
                        Ppl13weekProductTypeEnum.CDW.getName(), Ppl13weekProductTypeEnum.EKS.getName(),
                        Ppl13weekProductTypeEnum.DLC.getName(), Ppl13weekProductTypeEnum.CS.getName()
                )));
        column2dictMap.put("I", new ColumnInfo("GPU卡型", gpuTypes.stream().sorted().collect(Collectors.toList())));
        column2dictMap.put("J",
                new ColumnInfo("需求类型", PplDemandTypeEnum.names().stream().sorted().collect(Collectors.toList())));
        column2dictMap.put("K",
                new ColumnInfo("是否迁移需求", Arrays.asList("是","否")));

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(out)
                .registerWriteHandler(new DownloadExcelStyleHandler(excelHeadInfo.buildHeadMap()))// 设置颜色
                .registerWriteHandler(new DownloadExcelSheetHandler(column2dictMap)) // 设置字典
                .automaticMergeHead(Boolean.FALSE)
                .build();
        WriteSheet dictWriteSheet = EasyExcel.writerSheet(1, DownloadExcelHeaderBuilder.DICT_SHEET_NAME)
                .build();
        WriteSheet productDictWriteSheet =
                EasyExcel.writerSheet(2, DownloadExcelHeaderBuilder.PRODUCT_INSTANCE_FAMILY_SHEET_NAME)
                        .build();
        WriteSheet writeSheet = EasyExcel.writerSheet(0, DownloadExcelHeaderBuilder.SHEET_NAME)
                .head(excelHead).build();
        excelWriter
                .write(excelData, writeSheet)
                .write(null, dictWriteSheet)
                .write(instanceFamilyConfig, productDictWriteSheet)
                .finish();

        // 构造返回数据
        DownloadExcelResp downloadExcelResp = new DownloadExcelResp();
        downloadExcelResp.setBytes(out.toByteArray());

        String fileName = vgrInfo.getVersionGroupDO().getVersionCode();
        fileName += "_" + vgrInfo.getVersionGroupDO().getIndustryDept();
        if (Strings.isNotBlank(vgrInfo.getVersionGroupDO().getBizGroup())) {
            fileName += "_" + vgrInfo.getVersionGroupDO().getBizGroup();
        }
        if (Strings.isNotBlank(req.getProduct())) {
            fileName += "_" + req.getProduct();
        } else {
            fileName += "_全部产品";
        }
        fileName += "_" + DateUtils.format(new Date(), "yyyyMMdd_HHmmss") + ".xlsx";
        downloadExcelResp.setFileName(fileName);
        return downloadExcelResp;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProductInstanceFamily {

        private String product;
        private String instanceFamily;
    }

    @Override
    public Object queryLongtermImportZoneNameDict(QueryLongtermDictReq req) {
        String regionName = req.getRegionName();
        Map<String, List<String>> regionNameMap = longtermDictService.queryRegionNameToZoneName();
        if (Strings.isEmpty(regionName)) {
            return ImmutableMap.of("zoneName",
                    regionNameMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
        }
        List<String> zoneName = regionNameMap.getOrDefault(regionName, new ArrayList<>());
        zoneName.add("随机可用区");
        return ImmutableMap.of("zoneName", zoneName);
    }


    @Override
    public Object queryLongtermImportInstanceTypeDict(QueryLongtermDictReq req) {
        String instanceFamily = req.getInstanceFamily();
        Map<String, List<String>> instanceTypeMap = longtermDictService.queryInstanceFamilyToInstanceType();
        if (Strings.isEmpty(instanceFamily)) {
            return ImmutableMap.of("instanceFamily",
                    instanceTypeMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
        }
        List<String> zoneName = instanceTypeMap.getOrDefault(instanceFamily, new ArrayList<>());
        return ImmutableMap.of("instanceFamily", zoneName);
    }

    private @NotNull
    List<Object> getExcelData(VersionGroupRecordVO vgrInfo, ParseImportExcelReq req, boolean if35) {

        List<LongtermVersionGroupRecordItemDO> items = databaseAccess.getItems(req.getRecordId(), req.getProduct());
        // 获取ppl13周的范围
        Map<Integer, List<Integer>> ppl13MonthMap = DownloadExcelDataBuilder.getPpl13Month(vgrInfo);

        Map<String, ExcelOutputDTO> excelData = new HashMap<>();
        Map<String, Integer> indexMap = DownloadExcelDataBuilder.buildExcelYQMIndexMap(vgrInfo, if35, ppl13MonthMap);

        Function<LongtermVersionGroupRecordItemDO, BigDecimal> getVal = (o) -> {
            boolean isGpu = o.getProduct() != null && o.getProduct().contains("GPU");
            boolean isReturn = PplDemandTypeEnum.RETURN.getCode().equals(o.getDemandType());
            BigDecimal num = isGpu ? o.getGpuNum() : o.getCoreNum();
            return num == null ? BigDecimal.ZERO : isReturn ? num.negate() : num;
        };


        // 先通过主控字段分组，然后处理
        ListUtils2.groupThenAccept(items, LongtermImportItemKeyAccessor::keyStr, (key, list) -> {

            for (int year = vgrInfo.getVersionDO().getDemandBeginYear(); year <= vgrInfo.getVersionDO().getDemandEndYear(); year++) {
                // 获取当年的数据
                int finalYear = year;
                List<LongtermVersionGroupRecordItemDO> values = list.stream()
                        .filter((o) -> Objects.equals(o.getDemandYear(), finalYear)).collect(Collectors.toList());

                if (ListUtils.isEmpty(values)) {
                    continue;
                }

                ExcelOutputDTO excelDto = excelData.getOrDefault(key, DownloadExcelDataBuilder.getExcelDto(values.get(0)));
                excelData.put(key, excelDto);
                // 全年的总和
                BigDecimal total = BigDecimal.ZERO;

                // 季度数据设置
                List<LongtermVersionGroupRecordItemDO> quarterList =
                        ListUtils.filter(values, (o) -> LongtermVersionGroupItemTimeUnitEnum.QUARTER.getCode().equals(o.getTimeUnit()));
                for (LongtermVersionGroupRecordItemDO item : quarterList) {
                    BigDecimal num = getVal.apply(item);
                    total = total.add(num);
                    DownloadExcelDataBuilder.setColumn(excelDto, indexMap, DownloadExcelDataBuilder.getYQMKey(finalYear, item.getDemandQuarter(), 0), num);
                };

                // 月数据设置
                List<LongtermVersionGroupRecordItemDO> monthList =
                        ListUtils.filter(values, (o) -> LongtermVersionGroupItemTimeUnitEnum.MONTH.getCode().equals(o.getTimeUnit()));
                Map<Integer, List<LongtermVersionGroupRecordItemDO>> groupByQ = ListUtils2.group(monthList, LongtermVersionGroupRecordItemDO::getDemandQuarter);
                for (Map.Entry<Integer, List<LongtermVersionGroupRecordItemDO>> entry : groupByQ.entrySet()) {
                    // 季度汇总
                    BigDecimal totalQ = BigDecimal.ZERO;
                    for (LongtermVersionGroupRecordItemDO item : entry.getValue()) {
                        BigDecimal num = getVal.apply(item);
                        total = total.add(num);
                        totalQ = totalQ.add(num);
                        DownloadExcelDataBuilder.setColumn(excelDto, indexMap, DownloadExcelDataBuilder.getYQMKey(finalYear, item.getDemandQuarter(), item.getDemandMonth()), num);
                    }
                    // 季度汇总设置
                    DownloadExcelDataBuilder.setColumn(excelDto, indexMap, DownloadExcelDataBuilder.getYQMKey(finalYear, entry.getKey(), -1), totalQ);
                }
                // 年度汇总设置
                DownloadExcelDataBuilder.setColumn(excelDto, indexMap, DownloadExcelDataBuilder.getYQMKey(finalYear, -1, -1), total);
                // 存储的时候,每一行都会存储数据
                excelDto.setNote(values.get(0).getNote());
            }

            if (if35) {
                for (int year = vgrInfo.getVersionDO().getExtraBeginYear(); year <= vgrInfo.getVersionDO().getExtraEndYear(); year++) {
                    // 获取当年的数据
                    int finalYear = year;
                    List<LongtermVersionGroupRecordItemDO> values = list.stream()
                            .filter((o) -> Objects.equals(o.getDemandYear(), finalYear)).collect(Collectors.toList());

                    if (ListUtils.isEmpty(values)) {
                        continue;
                    }

                    // 由于是按年录入的，所以只有一条
                    if (values.size() > 1) {
                        throw new BizException(String.format("维度【%s】在%s按年录入存在%s条数据", key, finalYear, values.size()));
                    }

                    ExcelOutputDTO excelDto = excelData.getOrDefault(key, DownloadExcelDataBuilder.getExcelDto(values.get(0)));
                    excelData.put(key, excelDto);
                    DownloadExcelDataBuilder.setColumn(excelDto, indexMap, DownloadExcelDataBuilder.getYQMKey(finalYear, 0, 0), getVal.apply(values.get(0)));

                }
            }

        });

        List<Object> result = new ArrayList<>(excelData.values());

        // 转换数据库中的  CODE 为名称
        EStream.of(result).forEach((o) -> {
            LongtermImportItemKeyAccessor i = (LongtermImportItemKeyAccessor) o;
            i.setDemandType(PplDemandTypeEnum.getNameByCode(i.getDemandType()));
        });

        return  result;
    }




}
