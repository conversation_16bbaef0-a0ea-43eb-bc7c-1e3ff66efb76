package cloud.demand.app.modules.p2p.longterm.controller.resp;

import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermGroupItemDTO;
import cloud.demand.app.modules.p2p.longterm.dto.excel.ExcelOutputDTO;
import lombok.Data;

import java.util.List;


@Data
public class ImportFromExcelResp {

    /**
     *  debug excel 转换成的dto需求明细
     */
    private List<LongtermGroupItemDTO> items;


    // errors 空的时候,调用保存接口返回的结果, errors 不为空的时候,这里是返回false
    private SaveLongtermGroupItemResp saveResp;

    /**
     * 错误明细
     */
    private List<String> errors;

    // debug 解析excel的dto
    private List<ExcelOutputDTO> excelDataList;

}