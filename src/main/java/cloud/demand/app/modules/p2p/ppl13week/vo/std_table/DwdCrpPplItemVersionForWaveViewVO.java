package cloud.demand.app.modules.p2p.ppl13week.vo.std_table;

import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplWaveDiskTypeEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Table("dwd_crp_ppl_item_version_cf")
public class DwdCrpPplItemVersionForWaveViewVO {

    /**
     * 版本代号<br/>Column: [version_code]
     */
    @Column(value = "version_code")
    private String versionCode;


    /**
     * 版本开始-年<br/>Column: [version_begin_year]
     */
    @Column(value = "version_begin_year")
    private Integer versionBeginYear;

    /**
     * 版本开始-月<br/>Column: [version_begin_month]
     */
    @Column(value = "version_begin_month")
    private Integer versionBeginMonth;

    /**
     * 版本结束-年<br/>Column: [version_end_year]
     */
    @Column(value = "version_end_year")
    private Integer versionEndYear;

    /**
     * 版本结束-月<br/>Column: [version_end_month]
     */
    @Column(value = "version_end_month")
    private Integer versionEndMonth;


    /**
     * 需求类型，NEW新增，ELASTIC弹性，RETURN退回<br/>Column: [demand_type]
     */
    @Column(value = "demand_type")
    private String demandType;

    /**
     * 需求所属产品，例如CVM&CBS<br/>Column: [product]
     */
    @Column(value = "product")
    private String product;


    /**
     * ppl需求总核心数<br/>Column: [total_core]
     */
    @Column(value = "total_core")
    private Integer totalCore;

    /**
     * ppl来源<br/>Column: [source]
     */
    @Column(value = "source")
    private String source;


    /**
     * ppl需求-年<br/>Column: [year]
     */
    @Column(value = "year")
    private Integer year;

    /**
     * ppl需求-月<br/>Column: [month]
     */
    @Column(value = "month")
    private Integer month;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept")
    private String industryDept;


    /**
     * GPU总卡数<br/>Column: [total_gpu_num]
     */
    @Column(value = "total_gpu_num")
    private BigDecimal totalGpuNum;


    /**
     * 是否被干预，0未被干预，1被干预
     */
    @Column(value = "is_comd")
    private Integer isComd;

    @Column(value = "cbs_is_spike")
    private Integer cbsIsSpike;

    @Column(value = "data_disk_type")
    private String dataDiskType;

    @Column(value = "system_disk_type")
    private String systemDiskType;

    @Column(value = "system_capacity")
    private BigDecimal systemCapacity;

    @Column(value = "data_capacity")
    private BigDecimal dataCapacity;

    public BigDecimal totalCoreGet() {
        return this.totalCore == null ? null : new BigDecimal(totalCore.toString());
    }

    public BigDecimal totalGpuNumGet() {
        return this.totalGpuNum;
    }


    /**
     * 获取年月
     */
    public String getYearMonth() {
        Integer year = getYear();
        Integer month = getMonth();
        return year + "-" + (month >= 10 ? month : "0" + month);
    }

    /**
     * 按照净增计算：如果{@link #demandTypeEnumGet()} 是退回需求{@link PplDemandTypeEnum#RETURN}，则返回 0 - 总核数(或总卡数) <br/>
     * 不按净增计算：直接返回 总核数(或总卡数) <br/>
     *
     * @param calcNetGrowth 是否按净增计算，true表示按净增计算
     */
    public BigDecimal calc(boolean calcNetGrowth) {
        boolean isReturn = PplDemandTypeEnum.RETURN.getCode().equals(this.getDemandType());
        if (Ppl13weekProductTypeEnum.GPU.getName().equals(this.getProduct())) {
            BigDecimal totalGpuNum = totalGpuNumGet();
            if (totalGpuNum == null) {
                return BigDecimal.ZERO;
            }
            return calcNetGrowth && isReturn ? BigDecimal.ZERO.subtract(totalGpuNum) : totalGpuNum;
        } else {
            BigDecimal totalCore = totalCoreGet();
            if (totalCore == null) {
                return BigDecimal.ZERO;
            }
            return calcNetGrowth && isReturn ? BigDecimal.ZERO.subtract(totalCore) : totalCore;
        }
    }


    public BigDecimal calCBS(boolean calcNetGrowth, String diskType) {
        boolean isReturn = PplDemandTypeEnum.RETURN.getCode().equals(this.getDemandType());
        if (diskType.equals(PplWaveDiskTypeEnum.SYSTEM_DISK.getName())) {
            return calcNetGrowth && isReturn ? BigDecimal.ZERO.subtract(systemCapacity) : systemCapacity;
        }else {
            return calcNetGrowth && isReturn ? BigDecimal.ZERO.subtract(dataCapacity) : dataCapacity;
        }
    }

}
