package cloud.demand.app.modules.p2p.ppl13week.entity;

// package a.b.c;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyDataSourceEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_stock_supply_req")
public class PplStockSupplyReqDO extends BaseDO {

    /**
     * 批次号<br/>Column: [supply_id]
     */
    @Column(value = "supply_id")
    private Long supplyId;

    /**
     * 类型： CVM CBS<br/>Column: [type]
     */
    @Column(value = "type")
    private String type;

    /**
     * ppl单号<br/>Column: [ppl_order]
     */
    @Column(value = "ppl_order")
    private String pplOrder;

    /**
     * ppl需求id<br/>Column: [ppl_id]
     */
    @Column(value = "ppl_id")
    private String pplId;

    /**
     * ppl_version_group_record的版本<br/>Column: [record_version]
     */
    @Column(value = "record_version")
    private Integer recordVersion;

    /**
     * 版本group record id<br/>Column: [version_group_record_id]
     */
    @Column(value = "version_group_record_id")
    private Long versionGroupRecordId;

    /**
     * 客户uin<br/>Column: [customer_uin]
     */
    @Column(value = "customer_uin", insertValueScript = "''")
    private String customerUin;

    /**
     * 地域，看看要不要存id<br/>Column: [region_name]
     */
    @Column(value = "region_name", insertValueScript = "''")
    private String regionName;

    /**
     * 可用区，看看要不要存id<br/>Column: [zone_name]
     */
    @Column(value = "zone_name", insertValueScript = "''")
    private String zoneName;

    @Column(value = "region", insertValueScript = "''")
    private String region;

    /**
     * 可用区，看看要不要存id<br/>Column: [zone_name]
     */
    @Column(value = "zone", insertValueScript = "''")
    private String zone;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @Column(value = "instance_type")
    private String instanceType;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    @Column(value = "instance_model")
    private String instanceModel;

    /**
     * 可接受的其它实例类型，分号隔开<br/>Column: [alternative_instance_type]
     */
    @Column(value = "alternative_instance_type", insertValueScript = "''")
    private String alternativeInstanceType;

    /**
     * 实例数量<br/>Column: [instance_num]
     */
    @Column(value = "instance_num")
    private Integer instanceNum;

    /**
     * 开始购买日期    "demandDate": "yyyy-MM-dd" // 需求日期<br/>Column: [begin_buy_date]
     */
    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    /**
     * 需求类型<br/>Column: [demand_type]
     */
    @Column(value = "demand_type")
    private String demandType;

    /**
     * 需求年月<br/>Column: [year_month]
     */
    @Column(value = "year_month", insertValueScript = "''")
    private String yearMonth;

    /**
     * 总容量G<br/>Column: [disk_total_size]
     */
    @Column(value = "disk_total_size")
    private Integer diskTotalSize;

    /**
     * 满足的磁盘类型，需要和需求的磁盘类型一一对应 <br/>Column: [disk_type]
     */
    @Column(value = "disk_type")
    private String diskType;

    @Column(value = "disk_type_name")
    private String diskTypeName;
    /**
     * 磁盘块数<br/>Column: [disk_num]
     */
    @Column(value = "disk_num")
    private Integer diskNum;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept", insertValueScript = "''")
    private String industryDept;

    /**
     * 客户类型,存量客户,winback客户<br/>Column: [customer_type]
     */
    @Column(value = "customer_type", insertValueScript = "''")
    private String customerType;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name", insertValueScript = "''")
    private String customerShortName;

    /**
     * 战区<br/>Column: [war_zone]
     */
    @Column(value = "war_zone", insertValueScript = "''")
    private String warZone;

    /**
     * 国家名字<br/>Column: [country_name]
     */
    @Column(value = "country_name", insertValueScript = "''")
    private String countryName;

    @Column(value = "campus", insertValueScript = "''")
    private String campus;

    /**
     * 实例数量 优先级，数字越小越优先 <br/>Column: [priority]
     */
    @Column(value = "priority")
    private Integer priority;

    /**
     * 退回的 item_id <br/>Column: [group_record_item_id]
     */
    @Column(value = "item_id")
    private Long itemId;


    /**
     * 分组里item的id<br/>Column: [group_record_item_id]
     */
    @Column(value = "group_record_item_id")
    private Long groupRecordItemId;

    @Column(value = "record_item_json")
    private String recordItemJson;

    /**
     * 满足的总核心数<br/>Column: [instance_total_core]
     */
    @Column(value = "instance_total_core", insertValueScript = "0")
    private Integer instanceTotalCore;

    /**
     * 实例类别  实例类型名称的前缀<br/>Column: [instance_total_core]
     */
    @Column(value = "instance_type_name_prefix")
    private String instanceTypeNamePrefix;

    /**
     * 需求场景<br/>Column: [instance_total_core]
     */
    @Column(value = "demand_scene")
    private String demandScene;

    /**
     * 结束购买日期<br/>Column: [instance_total_core]
     */
    @Column(value = "end_buy_date")
    private LocalDate endBuyDate;
    /**
     * 赢率<br/>Column: [instance_total_core]
     */
    @Column(value = "win_rate")
    private BigDecimal winRate;
    /**
     * 需求场景<br/>Column: [instance_total_core]
     */
    @Column(value = "app_role", insertValueScript = "''")
    private String appRole;

    /**
     * 预约的实例数量<br/>Column: [instance_num]
     */
    @Column(value = "apply_instance_num")
    private Integer applyInstanceNum;

    /**
     * 预约的实例总核心数<br/>Column: [total_core]
     */
    @Column(value = "apply_total_core")
    private Integer applyTotalCore;

    /**
     * 通常为ppl的备注字段
     */
    @Column(value = "remark", insertValueScript = "''")
    private String remark;

    /**
     * 0-未预约PPL，1-已预约缺口 <br/>Column: [source]
     */
    @Column(value = "source")
    private Integer source = 0;


    @Column(value = "product")
    private String product;

    /**
     * 共识状态
     */
    @Column("consensus_status")
    private String consensusStatus;

    /**
     *  CBS单实例IO(MB/s)
     */
    @Column(value = "cbs_io")
    private Integer cbsIo;

    /**
     *  data_source
     *  数据来源: 下发对冲 STOCK_SUPPLY / 已锁定采购 ORDER_BUY
     */
    @Column(value = "data_source")
    private String dataSource = PplStockSupplyDataSourceEnum.STOCK_SUPPLY.getCode();

    /**
     * 关联订单号
     * 当 数据来源 是 订单已锁定采购时 才有值
     */
    @Column(value = "order_number")
    private String orderNumber;
}