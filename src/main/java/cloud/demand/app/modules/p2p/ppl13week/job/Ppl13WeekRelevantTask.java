package cloud.demand.app.modules.p2p.ppl13week.job;

import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.SyncOuterService;
import cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version.UnificatedVersionDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionEventDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.UnificatedVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionGroupService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.UnificatedVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.data_check.DataCheckServiceAdapter;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import yunti.boot.config.DynamicProperty;

import javax.annotation.Resource;
import java.lang.reflect.UndeclaredThrowableException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Supplier;

@Slf4j
@Service
public class Ppl13WeekRelevantTask {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private PplVersionGroupService pplVersionGroupService;
    @Resource
    Alert alert;
    @Resource
    DictService dictService;

    @Resource
    private UnificatedVersionService unificatedVersionService;

    @Resource
    private PplVersionService pplVersionService;

    @Resource
    private DataCheckServiceAdapter dataCheckServiceAdapter;


    @Resource
    private SyncOuterService syncOuterService;

    private static DynamicProperty<String> testEnv = DynamicProperty.create("test_env", "");
    private static Supplier<String> domainSupplier = DynamicProperty.create("app.product.domain", "");


    static ExecutorService executor = Executors.newFixedThreadPool(8);

//    统一大版本调度
//    /**
//     * 到达版本截止录入时间，自动流转节点。
//     */
//    @Synchronized(waitLockMillisecond = 100)
//    @Scheduled(cron = "0 */1 * * * * ")
//    public void checkVersionDeadline() throws InterruptedException {
//        Thread.sleep(1000);
//        log.info("begin checkVersionDeadline");
//        pplVersionGroupService.checkVersionDeadline();
//        log.info("end checkVersionDeadline");
//    }

    /**
     * 在版本截止录入时间
     * 给 处于录入中节点的13周录入用户 发送邮件确认提醒
     * 每天一次
     *
     * @throws InterruptedException
     */
//    @Synchronized(waitLockMillisecond = 100)
//    @Scheduled(cron = "0,0 0,0 10 * * * ")
//    @Transactional("demandTransactionManager")
//    public void sendInSubmitMail() throws InterruptedException {
//        Thread.sleep(1000);
//        log.info("begin sendInsubmitMail");
//        pplVersionGroupService.sendInSubmitMail();
//        log.info("end sendInsubmitMail");
//    }

    /**
     * 大版本统一处理定时任务 行业13周版本 产品13周版本 产品全年版本
     * 每分钟轮询一次
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 */1 * * * * ")
    public void unificatedVersionScheduled() throws InterruptedException {
        Thread.sleep(1000);
        UnificatedVersionDTO unificatedVersionDTO = demandDBHelper.getOne(UnificatedVersionDTO.class,
                "where status = ?",
                UnificatedVersionStatusEnum.PROCESS.getCode());
        if (unificatedVersionDTO == null) {
            return;
        }
        log.info("begin unificatedVersionScheduled");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        List<String> dataCheckEvent = Arrays.asList(CrpEventEnum.DATA_CHECK_STOCK_SUPPLY_DONE.getCode());

        for (UnificatedVersionEventDO eventDO : unificatedVersionDTO.getEventList()) {
            // ⚠️⚠️⚠️需注意，定时任务每分钟轮询一次 下列方法都需加锁，且保证只执行一次 避免重复调用
            if (eventDO.getIsDone()) {
                continue;
            }
            Date deadline = eventDO.getDeadline();
            String eventCode = eventDO.getEventCode();
            if (CrpEventEnum.VERSION_START_TIME.getCode().equals(eventCode) && deadline.before(new Date())) {
                // 开启版本
                executor.execute(() -> {
                    try {
                        unificatedVersionService.startUnificatedVersion(unificatedVersionDTO, eventDO);
                    } catch (Exception e) {
                        e.printStackTrace();
                        alert.sendRtx("oliverychen;kaijiazhang;jackycjchen", "启动统一版本失败", getException(e).getMessage());
                    }
                });
            }

            if (eventCode.equals(CrpEventEnum.PPL_ENTER_DEADLINE.getCode()) && deadline.before(new Date())) {
                // ppl录入截止
                executor.execute(() -> {
                    try {
                        unificatedVersionService.ppl13WeekEnterDeadline(eventDO);
                        String time = "库存对冲开启时";
                        if (unificatedVersionDTO.getDeadlineByCode(CrpEventEnum.STOCK_SUPPLY_DEADLINE.getCode())
                                != null) {
                            time = formatter.format(unificatedVersionDTO.getDeadlineByCode(
                                    CrpEventEnum.STOCK_SUPPLY_DEADLINE.getCode()));
                        }
                        String msg = "您好，本周录入已截止，请及时前往CRP进行干预，" + time
                                + " 将截止干预。（<a href='https://crp.woa.com/13ppl/view'>点击前往13周需求审批。</a>）";
                        dictService.eventNotice(eventDO.getEventCode(), null, msg);
                    } catch (Exception e) {
                        e.printStackTrace();
                        alert.sendRtx("oliverychen;dotyou", "ppl录入截止方法调用失败", e.getMessage());
                    }
                });
            }

            if (eventCode.equals(CrpEventEnum.STOCK_SUPPLY_DEADLINE.getCode()) && deadline.before(new Date())) {
                // ppl启动库存对冲
                executor.execute(() -> {
                    try {
                        unificatedVersionService.startStockSupply(unificatedVersionDTO, eventDO);
                        dictService.eventNotice(eventDO.getEventCode(), null, null);
                    } catch (Exception e) {
                        e.printStackTrace();
                        alert.sendRtx("oliverychen", "ppl启动库存对冲失败", e.getMessage());
                    }

                });
            }

            if (eventCode.equals(CrpEventEnum.DEVICE_DEMAND_VERSION_ENTER_DEADLINE.getCode()) && deadline.before(new Date())) {
                executor.execute(() -> {
                    try {
                        unificatedVersionService.deviceEnterDeadLine(unificatedVersionDTO, eventDO);
                    } catch (Exception e) {
                        e.printStackTrace();
                        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        alert.sendRtx("oliverychen;kaijiazhang;jackycjchen", "物理机需求预测版本截止录入失败",
                                currentTime + "错误信息" + getException(e).getMessage());
                    }

                });
            }

            if (eventCode.equals(CrpEventEnum.DEVICE_DEMAND_VERSION_APPROVE_DEADLINE.getCode()) && deadline.before(new Date())) {
                executor.execute(() -> {
                    try {
                        unificatedVersionService.deviceApproveDeadLine(unificatedVersionDTO, eventDO);
                    } catch (Exception e) {
                        e.printStackTrace();
                        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        alert.sendRtx("oliverychen;kaijiazhang;jackycjchen", "物理机需求预测版本截止审批失败",
                                currentTime + "错误信息" + getException(e).getMessage());
                    }

                });
            }

            if (eventCode.equals(CrpEventEnum.DEVICE_DEMAND_VERSION_REVIEW_DEADLINE.getCode()) && deadline.before(new Date())) {
                executor.execute(() -> {
                    try {
                        unificatedVersionService.deviceReviewDeadLine(unificatedVersionDTO, eventDO);
                    } catch (Exception e) {
                        e.printStackTrace();
                        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        alert.sendRtx("oliverychen;kaijiazhang;jackycjchen", "物理机需求预测版本会审结束失败",
                                currentTime + "错误信息" + getException(e).getMessage());
                    }

                });
            }

            if (eventCode.equals(CrpEventEnum.DEVICE_DEMAND_VERSION_PASS_DEADLINE.getCode()) && deadline.before(new Date())) {
                executor.execute(() -> {
                    try {
                        unificatedVersionService.devicePassDeadLine(unificatedVersionDTO, eventDO);
                    } catch (Exception e) {
                        e.printStackTrace();
                        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        alert.sendRtx("oliverychen;kaijiazhang;jackycjchen", "物理机需求预测版本生效失败",
                                currentTime + "错误信息" + getException(e).getMessage());
                    }

                });
            }

            if (eventCode.equals(CrpEventEnum.STOCK_SUPPLY_DONE.getCode())
                    && deadline != null && deadline.before(new Date())) {
                // 检查库存对冲完成是否有意外
                executor.execute(() -> {
                    alert.sendRtx("oliverychen;jackycjchen", "下发行业物理机预测到产品13周中,请持续关注",
                            "下发行业物理机预测到产品13周中,请持续关注");

                });
            }

            if (CrpEventEnum.VERSION_CLOSE_TIME.getCode().equals(eventCode) && deadline.before(new Date())) {
                // 版本关闭时间
                executor.execute(() -> {
                    try {
                        unificatedVersionService.closeUnificatedVersion(unificatedVersionDTO, eventDO);
                        dictService.eventNotice(eventCode, null, null);
                    } catch (Exception e) {
                        e.printStackTrace();
                        alert.sendRtx("oliverychen;kaijiazhang;jackycjchen", "统一版本关闭失败", getException(e).getMessage());
                    }

                });
            }

            // 数据对账事件处理
            if (dataCheckEvent.contains(eventCode)) {
                Map<String, Object> context = new HashMap<>();
                context.put("unificatedVersion", unificatedVersionDTO);
                context.put("event", eventDO);
                // 数据检查
                executor.execute(() -> {
                    try {
                        dataCheckServiceAdapter.execute(eventCode, context);
                    } catch (Exception e) {
                        e.printStackTrace();
                        alert.sendRtx("oliverychen;kaijiazhang;jackycjchen",
                                CrpEventEnum.getNameByCode(eventCode) + " -- 数据检查失败", e.getMessage());
                    }
                });
            }

            // ⚠️⚠️⚠️需注意，定时任务每分钟轮询一次 上面方法都需加锁，且保证只执行一次 避免重复调用
        }

        log.info("end unificatedVersionScheduled");
    }

    /**
     * 获取异常
     * @param e 异常
     * @return
     */
    public static Throwable getException(Throwable e) {
        if (e instanceof UndeclaredThrowableException){
            UndeclaredThrowableException ue = (UndeclaredThrowableException)e;
            return getException(ue.getUndeclaredThrowable());
        }else {
            return e;
        }
    }


    /**
     * 初始化版本, 确保至少有四个未启动的大版本
     * 每天凌晨2点检查
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0,0 0,0 2 * * * ")
    void checkUnificatedVersionInit() throws InterruptedException {
        Thread.sleep(1000);
        Boolean isTest = org.apache.logging.log4j.util.Strings.isNotBlank(testEnv.get());
        if (isTest) {
            // 测试环境不自动创建
            return;
        }
        try {
            unificatedVersionService.checkUnificatedVersionInit();
        } catch (Exception e) {
            alert.sendRtx("oliverychen", null, e.getMessage());
        }
    }

    /**
     * 更新当前ppl过期情况
     * 每天凌晨2点处理
     */
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0,0 0,0 2 * * * ")
    void checkPplIsExpired() throws InterruptedException {
        Thread.sleep(1000);
        Boolean isTest = org.apache.logging.log4j.util.Strings.isNotBlank(testEnv.get());
        if (isTest) {
            // 测试环境不处理
            return;
        }
        pplVersionService.checkPplIsExpired();
    }

    /**
     * 更新idc风险可用园区
     * 每天凌晨2点处理
     */
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0,0 0,0 2 * * * ")
    void syncIdcRiskZone() throws InterruptedException {
        Thread.sleep(1000);
        log.info("start syncIdcRiskZone");
        syncOuterService.syncIdcRiskZone();
        log.info("end syncIdcRiskZone");
    }

}
