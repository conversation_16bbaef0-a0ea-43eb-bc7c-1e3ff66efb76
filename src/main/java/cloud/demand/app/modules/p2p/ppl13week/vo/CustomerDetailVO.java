package cloud.demand.app.modules.p2p.ppl13week.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CustomerDetailVO {

    @Column(value = "year")
    private Integer year;

    @Column(value = "month")
    private Integer month;

    @Column(value = "customer_short_name")
    private String customerShortName;

    @Column(value = "customer_uin")
    private String customerUin;

    @Column(value = "industry_dept")
    private String industryDept;

    @Column(value = "war_zone")
    private String warZone;

    @Column(value = "demand_type")
    private String demandType;

    @Column(value = "demand_scene")
    private String demandScene;

    @Column(value = "bill_type")
    private String billType;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "instance_model")
    private String instanceModel;

    @Column(value = "yunxiao_order_status")
    private String yunxiaoOrderStatus;

    @Column(value = "demandTotalCore")
    private BigDecimal demandTotalCore;

    @Column(value = "appliedTotalCore")
    private BigDecimal appliedTotalCore;

    @Column(value = "demandTotalDisk")
    private BigDecimal demandTotalDisk;

    @Column(value = "appliedTotalDisk")
    private BigDecimal appliedTotalDisk;

    @Column(value = "demandTotalInstanceNum")
    private BigDecimal demandTotalInstanceNum;

    @Column(value = "appliedTotalInstanceNum")
    private BigDecimal appliedTotalInstanceNum;

    @Column(value = "demandTotalGpuNum")
    private BigDecimal demandTotalGpuNum;

    @Column(value = "appliedTotalGpuNum")
    private BigDecimal appliedTotalGpuNum;

    @Column(value = "category")
    private String category;

    private String customhouseTitle;

    private String areaName;
}
