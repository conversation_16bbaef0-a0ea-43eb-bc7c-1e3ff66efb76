package cloud.demand.app.modules.p2p.longterm.service;

import cloud.demand.app.modules.p2p.longterm.controller.resp.QueryPpl13WeekVersionByVersionYmResp;
import cloud.demand.app.modules.p2p.longterm.controller.req.LongTermUnifiedVersionReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.QueryPpl13WeekVersionByVersionYmReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.SaveUnifiedVersionReq;
import cloud.demand.app.modules.p2p.longterm.controller.resp.Relevant13VersionResp;
import cloud.demand.app.modules.p2p.longterm.dto.CreateLongTermVersionReq;
import cloud.demand.app.modules.p2p.longterm.dto.LongTermUnifiedVersionDTO;
import cloud.demand.app.modules.p2p.longterm.dto.LongTermVersionProgressResp;
import cloud.demand.app.modules.p2p.longterm.entity.LongTermUnifiedVersionEventDO;
import java.util.List;

public interface LongtermUnifiedVersionService {


    void firstInit();

    /**
     * 查询版本列表
     * @param req
     * @return
     */
    List<LongTermUnifiedVersionDTO> queryUnificatedVersionList(LongTermUnifiedVersionReq req);

    /**
     * 查询版本进度
     * @param id
     * @return
     */
    LongTermVersionProgressResp queryUnifiedVersionProgress(Long id);

    /**
     * 查询版本详情
     * @param id
     * @return
     */
    LongTermUnifiedVersionDTO getUnifiedVersionDTOById(Long id);


    /**
     * 查询版本详情 通过版本号
     * @param versionCode
     * @return
     */
    LongTermUnifiedVersionDTO getUnifiedVersionDTOByVersionCode(String versionCode);

    /**
     * 查询进行中的版本
     * @return
     */
    LongTermUnifiedVersionDTO getProcessUnifiedVersion();

    /**
     * 创建中长期大版本
     * @return
     */
    void createUnifiedVersion(CreateLongTermVersionReq req);

    /**
     * 删除中长期大版本
     * @param id
     */
    void deleteUnifiedVersion(Long id);

    /**
     * 保存大版本
     * @param version
     */
    void saveUnifiedVersion(SaveUnifiedVersionReq version);

    /**
     * 开启大版本
     * @param version
     * @param event
     */
    void startUnifiedVersion(LongTermUnifiedVersionDTO version,
            LongTermUnifiedVersionEventDO event);

    /**
     * 发送开版本消息
     * @param version
     */
    void sendOpenVersionMsg(LongTermUnifiedVersionDTO version);


    /**
     * 行业GM截止
     * @param version
     * @param event
     */
    void gmAuditDeadline(LongTermUnifiedVersionDTO version,
            LongTermUnifiedVersionEventDO event);

    /**
     * 关闭大版本
     * @param version
     * @param event
     */
    void closeUnifiedVersion(LongTermUnifiedVersionDTO version,
            LongTermUnifiedVersionEventDO event);


    Relevant13VersionResp queryRelevantVersion();

    /**
     * 通过版本年月查询13周版本得到概要信息
     * @param req
     * @return
     */
    List<QueryPpl13WeekVersionByVersionYmResp> queryPpl13WeekVersionByVersionYm(QueryPpl13WeekVersionByVersionYmReq req);
}
