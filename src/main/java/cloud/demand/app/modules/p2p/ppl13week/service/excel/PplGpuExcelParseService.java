package cloud.demand.app.modules.p2p.ppl13week.service.excel;

import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.ObjUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.QueryInfoByUinRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplGpuImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplGpuProductConfigDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplGpuRegionZoneDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.impl.PplImportServiceImpl.MyList;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisStopException;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class PplGpuExcelParseService extends Abstract13WeekPplExcelParseService {

    @Resource
    private PplDictService pplDictService;
    @Resource
    private DictService dictService;

    @Override
    public boolean support(String productType) {
        return Ppl13weekProductTypeEnum.GPU.getName().equals(productType);
    }

    @Override
    public List<PplImportExcelDTO> decodeExcel(MultipartFile file, String product) {
        List<PplImportExcelDTO> data = new LinkedList<>();
        try {
            EasyExcel.read(
                    file.getInputStream(), PplGpuImportExcelDTO.class,
                    new AnalysisEventListener<PplGpuImportExcelDTO>() {
                        @Override
                        public void invoke(PplGpuImportExcelDTO o, AnalysisContext analysisContext) {
                            if (ObjUtils.allFieldIsNull(o)) {
                                log.info("读到第一个空行，结束");
                                throw new ExcelAnalysisStopException();
                            }
                            PplImportExcelDTO i = PplGpuImportExcelDTO.copyToNewDTO(o);
                            data.add(i);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        }
                    }
            ).sheet(0).headRowNumber(2).doRead();
        } catch (Exception e) {
            log.error("decode excel error:", e);
            throw new BizException("文件解析失败");
        }
        return data;
    }

    @Override
    public PplItemImportRsp execute(MultipartFile file, YearMonth startYearMonth, YearMonth endYearMonth,
            String product) {

        //解析excel
        List<PplImportExcelDTO> uploadData = decodeExcel(file, product);

        //获取uin
        List<String> uins = uploadData.stream()
                .map(PplImportExcelDTO::getCustomerUin)
                .filter(Strings::isNotBlank)
                .distinct().collect(Collectors.toList());
        HashMap<String, QueryInfoByUinRsp> uinMapInfo = queryInfoByUinRspHashMap(uins);
        List<GroupItemDTO> retData = Lang.list();
        List<PplItemImportRsp.ErrorMessage> errors = new MyList<>();
        Map<String, PplGpuProductConfigDO> gpuConfigMap = pplDictService.queryGpuConfigMap();
        List<PplGpuRegionZoneDO> pplGpuRegionZoneDOS = pplDictService.queryGpuInstanceList();

        // 腾讯云的region和zone map
        Map<String, String> regionNameMap = dictService.getRegionNameMap();
        Map<String, String> zoneNameMap = dictService.getZoneNameMap();

        for (int i = 0; i < uploadData.size(); i++) {
            PplImportExcelDTO oneData = uploadData.get(i);
            int row = i + 3;
            int beginErrorSize = errors.size();
            PplItemImportRsp.ErrorMessage error = null;

            error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getCustomerTypeName,
                    CustomerTypeEnum.names());
            errors.add(error);

            //校验uin
            QueryInfoByUinRsp queryInfoByUinRsp = checkUin(errors, oneData, row, uinMapInfo);

            CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getByName(oneData.getCustomerTypeName());

            //校验需求
            checkDemandAndBillType(errors, oneData, row);

            //校验业务场景和详情
            checkBizSceneAndDetail(errors, oneData, row);

            //校验使用时长
            error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getServiceTime);
            errors.add(error);

            //校验赢率 GPU赢率必填
            error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getWinRate);
            errors.add(error);
            BigDecimal winRate = null;
            if (error == null) {
                winRate = checkWinRate(errors, oneData, row);
            }



            //各时间参数校验
            DateDTO dateDTO = checkTime(errors, oneData, row, startYearMonth, endYearMonth);

            // GPU相关校验 地域/可用区/实例类型/核心数/内存数
            GpuDTO gpuDTO = checkGpuRelevant(errors, oneData, row, regionNameMap, zoneNameMap,
                    pplGpuRegionZoneDOS);

            oneData.setGpuType(gpuDTO.getGpuType());
            gpuCheck(errors, oneData, row);

            Integer coreNum = NumberUtils.parseInt(oneData.getInstanceModelCpuCore());
            Integer ramNum = NumberUtils.parseInt(oneData.getInstanceModelRam());
            BigDecimal gpuNum = NumberUtils.parseBigDecimal(oneData.getGpuNum());

            // 如果该条ppl没有产生error 则直接转换为 DTO
            if (beginErrorSize == errors.size()) {

                GroupItemDTO tmp = GroupItemDTO.trans(oneData);
                retData.add(tmp);
                if (customerTypeEnum != null) {
                    tmp.setCustomerType(customerTypeEnum.getCode());
                }
                tmp.setYear(dateDTO.getYear());
                tmp.setMonth(dateDTO.getMonth());
                tmp.setYearMonth(dateDTO.getYearMonth());
                tmp.setCustomerShortName(oneData.getCustomerShortName());
                if (queryInfoByUinRsp != null) {
                    if (Strings.isNotBlank(queryInfoByUinRsp.getCustomerShortName())) {
                        tmp.setCustomerShortName(queryInfoByUinRsp.getCustomerShortName());
                    }
                    if (Strings.isBlank(tmp.getCustomerShortName())) {
                        tmp.setCustomerTypeName(tmp.getCustomerName());
                    }
                    tmp.setIndustry(queryInfoByUinRsp.getIndustry());
                    tmp.setCustomerSource(queryInfoByUinRsp.getCustomerSource());
                    tmp.setWarZone(queryInfoByUinRsp.getWarZone());
                    tmp.setCustomerName(queryInfoByUinRsp.getCustomerName());
                }
                tmp.setWinRate(winRate);
                tmp.setBeginBuyDate(dateDTO.getBeginBuyDateRet());
                tmp.setEndBuyDate(dateDTO.getEndBuyDateRet());
                tmp.setBeginElasticDate(dateDTO.getBeginElasticDateRet());
                tmp.setEndElasticDate(dateDTO.getEndElasticDateRet());

                tmp.setInstanceModelCoreNum(coreNum);
                tmp.setInstanceModelRamNum(ramNum);
                tmp.setGpuNum(gpuNum);
                tmp.setStatus(PplItemStatusEnum.VALID.getCode());
                tmp.setStatusName(PplItemStatusEnum.VALID.getName());

                tmp.setGpuType(gpuDTO.getGpuType());
                tmp.setGpuProductType(gpuDTO.getGpuProductType());
                tmp.setInstanceModel(gpuDTO.getInstanceModel());
                tmp.setTotalCoreNum(gpuDTO.getTotalCore());
                tmp.setTotalDiskNum(gpuDTO.getTotalDisk());
                tmp.setTotalGpuNum(gpuDTO.getTotalGpu());

                tmp.setProduct(product);

            }
        }

        return new PplItemImportRsp(errors.size() == 0, errors, retData);
    }
}
