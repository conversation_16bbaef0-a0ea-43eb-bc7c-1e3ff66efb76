package cloud.demand.app.modules.p2p.industry_demand.service;

import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.QueryDemandInfoReq;
import cloud.demand.app.modules.p2p.industry_demand.dto.QueryDemandSummaryInfoReq;
import cloud.demand.app.modules.p2p.industry_demand.dto.ValidateResultDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.product.BaseProductDTO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandVersionDO;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IndustryCustomerDemandService {

    /**
     * 覆盖（旧的删除，新的插入）某个版本的《战略客户部-客户-产品》的需求预测信息
     * 如果这个版本的行业-产品还没有创建，则先创建分组
     *
     * @param versionDO 版本实体
     * @param customerName 客户名
     * @param product 产品
     * @param warZoneName 战区名
     * @param data 产品需求预测信息
     * @param tClass 产品DTO的类
     * @return 如果校验失败，返回校验信息
     */
    ValidateResultDTO save(IndustryDemandVersionDO versionDO, String customerName, String product,
            String warZoneName,
            List<? extends BaseProductDTO> data,
            Class<? extends BaseProductDTO> tClass);

    /**
     * 保存收入目标
     *
     * @param annualIncomeTarget 年度收入目标
     * @param lastAnnualIncomeTarget 去年年度收入目标
     * @param annualIncomeIncreaseRate 年度收入目标增速
     * @param req 定位groupId的筛选器
     */
    void saveIncome(Long annualIncomeTarget, Long lastAnnualIncomeTarget, String annualIncomeIncreaseRate,
            String annualIncomeTargetRate,
            QueryDemandInfoReq req, String username);

    /**
     * 查询预测详情（不包含汇总分析）
     *
     * @param req 筛选项
     * @return 预测详情
     */
    Map<String, Object> queryDemandInfo(QueryDemandInfoReq req, String username);

    /**
     * 渲染结果为excel
     *
     * @param req 查询参数
     * @return 渲染后的excel的二进制
     */
    FileNameAndBytesDTO exportDemandDetail(QueryDemandInfoReq req);

    /**
     * 查询资源汇总情况 - 客户维度
     *
     * @param req 筛选项
     * @return 预测的汇总详情（含满足情况）
     */
    List<Map<String, Object>> queryDemandSummaryInfoReqByCustomer(QueryDemandSummaryInfoReq req, String username);

    /**
     * 查询资源汇总情况
     *
     * @param req 筛选项
     * @return 预测的汇总详情（含满足情况）
     */
    List<Map<String, Object>> queryDemandSummaryInfoReq(QueryDemandSummaryInfoReq req, String username);

    Map<String, Object> queryDemandSummaryInfoInWarZoneView(QueryDemandSummaryInfoReq req, String username);

    Set<String> queryUserBelongIndustryList(String username);
}
