package cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao;

import java.util.Arrays;
import java.util.List;
import lombok.Getter;

import java.util.Objects;

/**
 * 云霄订单分类枚举
 */
@Getter
public enum YunxiaoOrderCategoryEnum {

    CVM("CVM", "CVM"),

    GPU("GPU", "GPU"),

    FPGA("FPGA", "FPGA"),

    BARE_METAL("BARE_METAL", "裸金属"),

    ;

    final private String code;
    final private String name;


    YunxiaoOrderCategoryEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    static final List<String> cvmCategory = Arrays.asList(CVM.code, GPU.code,BARE_METAL.code);

    public static List<String> getCvmCategory() {
        return cvmCategory;
    }

    public static YunxiaoOrderCategoryEnum getByCode(String code) {
        for (YunxiaoOrderCategoryEnum e : YunxiaoOrderCategoryEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        YunxiaoOrderCategoryEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

    /**
     * 从ppl的product转成云霄的orderCategory code
     */
    public static String transFromPplProduct(String pplProduct) {
        if (Objects.equals(pplProduct, "GPU(裸金属&CVM)")) {
            return GPU.getCode();
        } else if (Objects.equals(pplProduct, "裸金属")) {
            return BARE_METAL.getCode();
        } else {
            return CVM.getCode(); // 默认是CVM，目前有：CVM&CBS、弹性MapReduce、Elasticsearch Service、云数据仓库、容器服务
        }
    }

}