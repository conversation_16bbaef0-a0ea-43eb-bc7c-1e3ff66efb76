package cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * 取消预扣单请求体
 */
@Data
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DestroyPreDeductOrderReq {

    private List<Integer> reservationFormId;

    private Integer count;

    private String operator;
}
