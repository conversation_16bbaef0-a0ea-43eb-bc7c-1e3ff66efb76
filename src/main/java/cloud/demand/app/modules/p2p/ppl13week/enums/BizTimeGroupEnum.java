package cloud.demand.app.modules.p2p.ppl13week.enums;

import java.util.Objects;
import lombok.Getter;

@Getter
public enum BizTimeGroupEnum {

    DEMAND_FORECAST("DEMAND_FORECAST", "需求预测"),

    LONG_TERM_FORECAST("LONG_TERM_FORECAST", "中长期需求预测"),
    ;

    private final String code;
    private final String name;

    BizTimeGroupEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static BizTimeGroupEnum getByCode(String code) {
        for (BizTimeGroupEnum e : BizTimeGroupEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static BizTimeGroupEnum getByName(String name) {
        for (BizTimeGroupEnum e : BizTimeGroupEnum.values()) {
            if (Objects.equals(name, e.getName())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        BizTimeGroupEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

    public static String getCodeByName(String name) {
        BizTimeGroupEnum e = getByName(name);
        return e == null ? "" : e.getCode();
    }

}
