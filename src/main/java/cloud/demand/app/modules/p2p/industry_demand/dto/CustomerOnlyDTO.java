package cloud.demand.app.modules.p2p.industry_demand.dto;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class CustomerOnlyDTO {


    /** 通用客户简称 */
    private String commonCustomerName;

    private List<Item> items;

    public static List<CustomerOnlyDTO> create(List<Item> uinCustomerList, List<Item2> commonCustomerList)   {
        List<CustomerOnlyDTO> result = new ArrayList<>();
        if (ListUtils.isEmpty(commonCustomerList)) {
            if (ListUtils.isEmpty(uinCustomerList)) {
                return result;
            }
            CustomerOnlyDTO dto = new CustomerOnlyDTO();
            dto.setItems(uinCustomerList);
            result.add(dto);
            return result;
        } else {
            Map<String, List<Item>> map = ListUtils.groupBy(uinCustomerList, Item::getCustomerShortName);
            for (Item2 common : commonCustomerList) {
                CustomerOnlyDTO dto = new CustomerOnlyDTO();
                dto.setCommonCustomerName(common.commonCustomerName);
                dto.setItems(map.get(common.customerShortName));
                result.add(dto);
            }
        }
        return result;
    }


    @Data
    public static class Item {
        @Column("uin")
        private String uin;

        /** 客户简称 */
        @Column("customer_short_name")
        private String customerShortName;

    }

    @Data
    public static class Item2 {

        /** 通用客户简称 */
        @Column("common_customer_name")
        private String commonCustomerName;

        /** 客户简称 */
        @Column("customer_short_name")
        private String customerShortName;
    }

}
