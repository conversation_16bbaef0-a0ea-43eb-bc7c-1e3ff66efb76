package cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply;


import lombok.Data;

@Data
public class QueryStockSupplyStatusRsp {


    Long stockId;
    Long supplyGpuId;

    String status;

    String operator;

    String createTime;
    String finishTime;
    Boolean isShowGpuButton;

    String cvmStatus;
    String cbsStatus;

    String cvmErrorMsg;
    String cbsErrorMsg;

    Boolean isFinishStockSupply;



}
