package cloud.demand.app.modules.p2p.longterm.entity;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.UnificatedVersionStatusEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Data
@ToString
@Table("long_term_unified_version")
public class LongTermUnifiedVersionDO extends BaseDO {

    /** 版本编码<br/>Column: [version_code] */
    @Column(value = "version_code")
    private String versionCode;

    /** 版本所属年月<br/>Column: [version_year_month] */
    @Column(value = "version_year_month")
    private String versionYearMonth;

    /** 版本状态<br/>Column: [status] */
    @Column(value = "status")
    private String status;

    /** 创建人<br/>Column: [create_user] */
    @Column(value = "create_user")
    private String createUser;

    /** 最后更新人<br/>Column: [update_user] */
    @Column(value = "update_user")
    private String updateUser;

    /** 行业中长期需求范围<br/>Column: [industry_long_term_year_month] */
    @Column(value = "industry_long_term_year_month")
    private String industryLongTermYearMonth;

    /** 额外支持的3-5年时间范围<br/>Column: [extra_year_month] */
    @Column(value = "extra_year_month")
    private String extraYearMonth;

    /** 额外支持的3-5年使用的行业范围<br/>Column: [extra_industry] */
    @Column(value = "extra_industry")
    private String extraIndustry;

    /** 关联13周数据版本<br/>Column: [relevant_ppl_version] */
    @Column(value = "relevant_ppl_version")
    private String relevantPplVersion;

    /** 备注<br/>Column: [remark] */
    @Column(value = "remark")
    private String remark;

    @Data
    @ToString
    public static class DemandYearMonth {

        Integer beginYear;
        Integer beginMonth;
        Integer endYear;
        Integer endMonth;

        public String getBeginYearMonth() {
            return this.beginYear + (this.beginMonth < 10 ? "-0" : "-") + this.beginMonth;
        }

        /**
         * 返回2024年6月～2024年12月
         *
         * @return
         */
        public String getChineseYearMonth() {
            return this.beginYear + "年" + this.beginMonth + "月 ～ " + this.endYear + "年" + this.endMonth + "月";
        }

        public String getEndYearMonth() {
            return this.endYear + (this.endMonth < 10 ? "-0" : "-") + this.endMonth;
        }

        public List<String> buildDemandYearMonth() {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            YearMonth startYearMonth = YearMonth.parse(getBeginYearMonth(), formatter);
            YearMonth endYearMonth = YearMonth.parse(getEndYearMonth(), formatter);

            List<String> yearMonths = new ArrayList<>();
            while (!startYearMonth.isAfter(endYearMonth)) {
                yearMonths.add(startYearMonth.format(formatter));
                startYearMonth = startYearMonth.plusMonths(1);
            }
            return yearMonths;
        }

    }

    public String getStatusName(){
        return UnificatedVersionStatusEnum.getNameByCode(this.getStatus());
    }

}