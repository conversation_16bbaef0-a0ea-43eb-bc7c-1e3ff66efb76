package cloud.demand.app.modules.p2p.product_demand.service.impl;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.entity.rrp.RRPDemandItemDO;
import cloud.demand.app.entity.shuttle.DeviceApplyDO;
import cloud.demand.app.modules.cvmjxc.service.others.RrpBaseInfoService;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.ValidateResultDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.ValidateResultItemDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.product.YearMonthNumDTO;
import cloud.demand.app.modules.p2p.industry_demand.utils.excel_write.CommonWriteHandler;
import cloud.demand.app.modules.p2p.industry_demand.utils.excel_write.CommonWriteHandler.Address;
import cloud.demand.app.modules.p2p.product_demand.config.DynamicProperties;
import cloud.demand.app.modules.p2p.product_demand.dto.DemandNumAndCoreNumDTO;
import cloud.demand.app.modules.p2p.product_demand.dto.ExtendsBudgetDTO;
import cloud.demand.app.modules.p2p.product_demand.dto.ListProductDemandGroupReq;
import cloud.demand.app.modules.p2p.product_demand.dto.PlanProductMapStatusDTO;
import cloud.demand.app.modules.p2p.product_demand.dto.QueryDemandInfoReq;
import cloud.demand.app.modules.p2p.product_demand.dto.SaveItemDTO;
import cloud.demand.app.modules.p2p.product_demand.dto.SummaryStatisticReq;
import cloud.demand.app.modules.p2p.product_demand.entity.ObsBudgetDeviceRollDataDO;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandApproveRecordDO;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionDO;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionGroupDO;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionGroupWithVersionVO;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionItemBackupRecordDO;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionItemDO;
import cloud.demand.app.modules.p2p.product_demand.entity.ServerPartsExtendedInfoDO;
import cloud.demand.app.modules.p2p.product_demand.enums.ProductDemandAuthRoleStatusEnum;
import cloud.demand.app.modules.p2p.product_demand.enums.ProductDemandGroupStatusEnum;
import cloud.demand.app.modules.p2p.product_demand.enums.ProductDemandVersionStatusEnum;
import cloud.demand.app.modules.p2p.product_demand.service.ProductDemandAuthService;
import cloud.demand.app.modules.p2p.product_demand.service.ProductDemandDictService;
import cloud.demand.app.modules.p2p.product_demand.service.ProductDemandService;
import cloud.demand.app.modules.p2p.product_demand.service.ProductDemandVersionService;
import cloud.demand.app.modules.p2p.product_demand.service.impl.ProductDemandDictServiceImpl.BaseDictData;
import cloud.demand.app.modules.p2p.product_demand.service.impl.ProductDemandDictServiceImpl.FullCampusBaseDictData;
import cloud.demand.app.modules.p2p.product_demand.service.impl.ProductDemandDictServiceImpl.ZoneBaseDictData;
import cloud.demand.app.modules.rrp_remake.service.DictService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class ProductDemandServiceImpl implements ProductDemandService {

    private final String[] alphabets = new String[]{"A", "B", "C", "D", "E", "F", "G", "H"};
    private final String[] backupAlphabets = new String[]{"J", "K", "L", "M", "N", "O", "P", "Q"};

    @Resource
    DBHelper demandDBHelper;
    @Resource
    DBHelper backupDBHelper;
    @Resource
    DBHelper shuttleDBHelper;
    @Resource
    DBHelper purchasereportDBHelper;
    @Resource
    ProductDemandVersionService productDemandVersionService;
    @Resource
    ProductDemandDictService productDemandDictService;
    @Resource
    ProductDemandAuthService productDemandAuthService;
    @Resource
    RrpBaseInfoService rrpBaseInfoService;
    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    DictService rrpRemakeDictService;
    @Resource
    cloud.demand.app.modules.common.service.DictService dictService;

    private ValidateResultItemDTO appendItemError(String error, ValidateResultItemDTO validateResultItemDTO) {
        if (validateResultItemDTO == null) {
            validateResultItemDTO = new ValidateResultItemDTO();
        }
        validateResultItemDTO.appendError(error);
        return validateResultItemDTO;
    }

    private ValidateResultItemDTO assertInDict(String filedName, Object value,
                                               Collection<?> dictSet,
                                               ValidateResultItemDTO validateResultItemDTO) {
        if (value == null) {
            return validateResultItemDTO;
        }
        if (value instanceof String) {
            String str = (String) value;
            if (!StringUtils.isBlank(str) && !dictSet.contains(str)) {
                return appendItemError(filedName + "[" + value + "]不在字典范围内", validateResultItemDTO);
            }
        } else {
            if (!dictSet.contains(value)) {
                return appendItemError(filedName + "[" + value + "]不在字典范围内", validateResultItemDTO);
            }
        }
        return validateResultItemDTO;
    }

    private ValidateResultItemDTO assertNotBlank(String filedName, Object value,
                                                 ValidateResultItemDTO validateResultItemDTO) {
        if (value instanceof String) {
            String str = (String) value;
            if (StringUtils.isBlank(str)) {
                return appendItemError(filedName + "不能为空", validateResultItemDTO);
            }
        } else if (value == null) {
            return appendItemError(filedName + "不能为空", validateResultItemDTO);
        }
        return validateResultItemDTO;
    }

    private Boolean hasThisPlanProductOrCOMDAuth(String username, String planProduct, boolean withOnlySee) {
        Set<ProductDemandAuthRoleStatusEnum> roles = new HashSet<>();
        roles.add(ProductDemandAuthRoleStatusEnum.PRODUCT_GM);
        roles.add(ProductDemandAuthRoleStatusEnum.PRODUCT_DIRECTOR);
        roles.add(ProductDemandAuthRoleStatusEnum.PRODUCT_MANAGER);
        if (withOnlySee) {
            roles.add(ProductDemandAuthRoleStatusEnum.DEMAND_MENTION);
            roles.add(ProductDemandAuthRoleStatusEnum.DEMAND_IMPORTER);
        }
        Set<String> planProductSet = productDemandAuthService.listMyPlanProduct(username,
                roles);
        return (!CollectionUtils.isEmpty(planProductSet) && planProductSet.contains(planProduct))
                || productDemandAuthService.hasCOMDRole(username);
    }

    private Boolean hasThisPlanProductOrCOMDAuth(String username, String planProduct) {
        return hasThisPlanProductOrCOMDAuth(username, planProduct, false);
    }

    private ProductDemandVersionGroupWithVersionVO getGroupDO(Long groupId, String planProduct,
                                                              String version) {
        ProductDemandVersionGroupWithVersionVO groupDO;
        if (groupId != null) {
            groupDO = demandDBHelper.getOne(ProductDemandVersionGroupWithVersionVO.class,
                    "where id = ?", groupId);
        } else if (planProduct != null && version != null) {
            groupDO = demandDBHelper.getOne(ProductDemandVersionGroupWithVersionVO.class,
                    "where demand_version = ? and plan_product = ?",
                    version, planProduct);
        } else {
            throw new BizException("请求时，传分组id或版本号与规划产品产品的组合参数，其他情况无效");
        }
        return groupDO;
    }

    @Override
    @Transactional("demandTransactionManager")
    public ValidateResultDTO overrideSave(ProductDemandVersionDO versionDO, String planProduct,
                                          List<SaveItemDTO> data) {
        // 全局校验
        ValidateResultDTO validateResultDTO = new ValidateResultDTO();
        // 检查当前用户有没有规划产品的录入权限
        String username = LoginUtils.getUserName();
        // 校验当前版本是否开启
        if (versionDO.getStatus().equals(ProductDemandVersionStatusEnum.DISABLED.getCode())) {
            validateResultDTO.appendGlobalError("当前版本[" + versionDO.getDemandVersion() + "]已关闭，无法保存");
        }
        ProductDemandVersionGroupDO groupDO = demandDBHelper.getOne(ProductDemandVersionGroupDO.class,
                "where demand_version = ? and plan_product = ?",
                versionDO.getDemandVersion(), planProduct);
        // 校验时间是否处于当前版本的开放时间内（双重保障）
        LocalDate now = DateUtils.toLocalDate(new Date());
        if (versionDO.getDemandImportOpenDate().isAfter(now)
                || versionDO.getDemandImportCloseDate().isBefore(now)) {
            if (!(groupDO != null && groupDO.getStatus().equals(ProductDemandGroupStatusEnum.REJECT.getCode()))) {
                // 如果没提交过的就不能提交了
                // 提交过但被驳回的，版本关闭前还可以修改后再提交
                validateResultDTO.appendGlobalError("当前版本的预测录入时间已结束");
            }
        }
        // 因为审批过程中也可以更改（保存）数据，所以不再校验当前的流程状态能否保存
        // 校验当前分组是否可保存
        boolean isApproving = false;
        if (groupDO != null) {
            for (int i = 0; i < ProductDemandGroupStatusEnum.APPROVING_ENUM_ARRAY.length; i++) {
                if (groupDO.getStatus().equals(ProductDemandGroupStatusEnum.APPROVING_ENUM_ARRAY[i].getCode())) {
                    isApproving = true;
                    break;
                }
            }
        }

        if (isApproving) {
            boolean auth = hasThisPlanProductOrCOMDAuth(username, planProduct);
            if (!auth) {
                validateResultDTO.appendGlobalError("您没有当前规划产品[" + planProduct + "]的编辑权限");
            }
        } else {
            Set<String> planProductSet = productDemandAuthService.listMyPlanProduct(username,
                    ImmutableSet.of(ProductDemandAuthRoleStatusEnum.DEMAND_IMPORTER,
                            ProductDemandAuthRoleStatusEnum.PRODUCT_MANAGER));
            if (!planProductSet.contains(planProduct) && !productDemandAuthService.hasCOMDRole(username,
                    ProductDemandAuthRoleStatusEnum.COMD_MANAGER)) {
                validateResultDTO.appendGlobalError("您没有当前规划产品[" + planProduct + "]的录入权限，请联系管理员添加["
                        + ProductDemandAuthRoleStatusEnum.DEMAND_IMPORTER.getName() + "]权限");
            }
        }

        if (!CollectionUtils.isEmpty(data)) {
            // 局部校验
            int rowId = 1;
            Map<String, List<Integer>> obsMap = rrpRemakeDictService.buildObsProjectType2DemandYearByPlanProduct(planProduct);
            Map<String, List<String>> campus2ModBizType = productDemandDictService.buildCampus2ModuleBusinessTypeNameMap();
            Map<String, String> reasonMap = productDemandDictService.listAllReasonMap();
            Set<String> reasonSet = new HashSet<>(reasonMap.keySet());
            Map<String, Set<String>> industryCustomerMap = productDemandDictService.industryCustomerMap();
            List<String> regionSet = productDemandDictService.listAllRegionName();
            Map<String, String> zoneRegionMap = productDemandDictService.listAllZoneData(null)
                    .stream()
                    .collect(Collectors.toMap(BaseDictData::getName, ZoneBaseDictData::getRegionName,
                            (e1, e2) -> e1));
            List<FullCampusBaseDictData> fullCampusBaseDictData = productDemandDictService.listAllCampusData(null);
            Map<String, String> campusZoneMap = ListUtils.toMap(fullCampusBaseDictData,
                    BaseDictData::getName, FullCampusBaseDictData::getZoneName);
            Set<String> deviceTypeSet = productDemandDictService.listAllDeviceTypeName(planProduct);
            for (SaveItemDTO saveItemDTO : data) {
                saveItemDTO.setRowId(rowId);
                ValidateResultItemDTO validateResultItemDTO = null;
                // 每个年月都要录需求，不录的给0，录的不能是负数
                StringJoiner sj = new StringJoiner(",");
                saveItemDTO.getYearMonthValueList().forEach(e -> {
                    if (e.getNum() == null) {
                        e.setNum(0);
                    } else if (e.getNum() < 0) {
                        sj.add(e.getStandardName());
                    }
                });
                if (!StringUtils.isBlank(sj.toString())) {
                    validateResultItemDTO = appendItemError("以下时间的需求量是负数：" + sj, validateResultItemDTO);
                }
                // 项目类型，非空，在字典内
                validateResultItemDTO = assertNotBlank("obs项目类型", saveItemDTO.getObsProjectType(),
                        validateResultItemDTO);
                List<Integer> demandYears = obsMap.get(saveItemDTO.getObsProjectType());
                if (ListUtils.isEmpty(demandYears)) {
                    validateResultItemDTO = appendItemError("规划产品所属部门不支持obs项目类型："
                            + saveItemDTO.getObsProjectType() + "请联系wendyzjzhou进行配置", validateResultItemDTO);
                } else {
                    List<Integer> validDemandYear = saveItemDTO.getValidDemandYear();
                    for (Integer demandYear : validDemandYear) {
                        if (!demandYears.contains(demandYear)) {
                            validateResultItemDTO = appendItemError("规划产品所属部门不支持obs项目类型："
                                    + saveItemDTO.getObsProjectType() + "请联系wendyzjzhou进行配置", validateResultItemDTO);
                        }
                    }
                }
                // 业务类型，非空，在字典内
                validateResultItemDTO = assertNotBlank("业务类型", saveItemDTO.getModuleBusinessTypeName(),
                        validateResultItemDTO);
                if (!campus2ModBizType.getOrDefault(saveItemDTO.getCampusName(), new ArrayList<>()).contains(saveItemDTO.getModuleBusinessTypeName())) {
                    validateResultItemDTO = appendItemError("Campus[" + saveItemDTO.getCampusName()
                                    + "]不支持业务类型[" + saveItemDTO.getModuleBusinessTypeName() + "]",
                            validateResultItemDTO);
                }
                // 需求原因，非空，在字典内
                validateResultItemDTO = assertNotBlank("需求原因", saveItemDTO.getReason(), validateResultItemDTO);
                validateResultItemDTO = assertInDict("需求原因", saveItemDTO.getReason(), reasonSet,
                        validateResultItemDTO);
                // 行业名，非空，在字典内
                validateResultItemDTO = assertNotBlank("行业", saveItemDTO.getIndustryName(), validateResultItemDTO);
                validateResultItemDTO = assertInDict("行业", saveItemDTO.getIndustryName(),
                        industryCustomerMap.keySet(),
                        validateResultItemDTO);
                // 客户名，非空，在字典内，要和行业有关联
                validateResultItemDTO = assertNotBlank("客户", saveItemDTO.getCustomerName(), validateResultItemDTO);
                if (!StringUtils.isBlank(saveItemDTO.getCustomerName()) && !StringUtils.isBlank(
                        saveItemDTO.getIndustryName())) {
                    Set<String> customerName = industryCustomerMap.get(saveItemDTO.getIndustryName());
                    if (customerName != null && !customerName.contains(saveItemDTO.getCustomerName())) {
                        validateResultItemDTO = appendItemError("填写的客户[" + saveItemDTO.getCustomerName()
                                + "]与行业[" + saveItemDTO.getIndustryName() + "]的对应关系不正确", validateResultItemDTO);
                    }
                }
                // region，非空，在字典内
                validateResultItemDTO = assertNotBlank("Region", saveItemDTO.getRegionName(), validateResultItemDTO);
                validateResultItemDTO = assertInDict("Region", saveItemDTO.getRegionName(), regionSet,
                        validateResultItemDTO);
                if ("待定".equals(saveItemDTO.getRegionName())) {
                    validateResultItemDTO = appendItemError("您选择的region【待定】所属国家含义不清，请选择需求目标国家下有意义的region",
                            validateResultItemDTO);
                }
                // zone，非空，在字典内，和region对应的上
                validateResultItemDTO = assertNotBlank("Zone", saveItemDTO.getZoneName(), validateResultItemDTO);
                validateResultItemDTO = assertInDict("Zone", saveItemDTO.getZoneName(), zoneRegionMap.keySet(),
                        validateResultItemDTO);
                if (!StringUtils.isBlank(saveItemDTO.getZoneName()) && !StringUtils.isBlank(
                        saveItemDTO.getRegionName())) {
                    String matchRegion = zoneRegionMap.get(saveItemDTO.getZoneName());
                    if (matchRegion != null && !saveItemDTO.getRegionName().equals(matchRegion)) {
                        validateResultItemDTO = appendItemError("Zone[" + saveItemDTO.getZoneName()
                                        + "]所属的Region应为[" + matchRegion + "]",
                                validateResultItemDTO);
                    }
                }
                // campus，非空，在字典内，和campus对应的上
                validateResultItemDTO = assertNotBlank("Campus", saveItemDTO.getCampusName(), validateResultItemDTO);
                validateResultItemDTO = assertInDict("Campus", saveItemDTO.getCampusName(), campusZoneMap.keySet(),
                        validateResultItemDTO);
                if (!StringUtils.isBlank(saveItemDTO.getCampusName()) && !StringUtils.isBlank(
                        saveItemDTO.getZoneName())) {
                    String matchZone = campusZoneMap.get(saveItemDTO.getCampusName());
                    if (matchZone != null && !saveItemDTO.getZoneName().equals(matchZone)) {
                        validateResultItemDTO = appendItemError("Campus[" + saveItemDTO.getCampusName()
                                        + "]所属的Zone应为[" + matchZone + "]",
                                validateResultItemDTO);
                    }
                }
                // 设备类型，非空，在字典内
                validateResultItemDTO = assertNotBlank("设备类型", saveItemDTO.getDeviceTypeName(),
                        validateResultItemDTO);
                if (!deviceTypeSet.contains(saveItemDTO.getDeviceTypeName())) {
                    validateResultItemDTO = appendItemError("规划产品所属部门没有设备类型[" + saveItemDTO.getDeviceTypeName()
                                    + "]的资源权限，请联系kaijiazhang/dandiechen",
                            validateResultItemDTO);
                }
                if (validateResultItemDTO != null) {
                    validateResultDTO.appendItem(validateResultItemDTO);
                    validateResultItemDTO.setIndex(rowId);
                }
                rowId += 1;
            }
        }
        if (validateResultDTO.isHasError()) {
            // 提前返回校验结果，不更新DB
            return validateResultDTO;
        }
        String originStatus = ProductDemandGroupStatusEnum.INPUT.getCode();
        // 需求分组检查，如果有分组则更新，无则插入
        if (groupDO == null) {
            groupDO = new ProductDemandVersionGroupDO();
            groupDO.setStatus(ProductDemandGroupStatusEnum.INPUT.getCode());
            groupDO.setDemandVersion(versionDO.getDemandVersion());
            groupDO.setPlanProduct(planProduct);
        } else {
            originStatus = groupDO.getStatus();
        }
        if (StringUtils.isBlank(groupDO.getSubmitUser())) {
            groupDO.setSubmitUser(username);
        }
        if (groupDO.getSubmitTime() == null) {
            groupDO.setSubmitTime(new Date());
        }
        if (StringUtils.isBlank(groupDO.getStatus())
                || groupDO.getStatus().equals(ProductDemandGroupStatusEnum.SYSTEM_INIT.getCode())) {
            groupDO.setStatus(ProductDemandGroupStatusEnum.INPUT.getCode());
        }
        demandDBHelper.insertOrUpdate(groupDO);
        long count = demandDBHelper.getCount(ProductDemandVersionItemDO.class,
                "where demand_group_id = ?", groupDO.getId());
        if (count > 0) {
            // 如果存在已经录入过的数据，备份一下
            ProductDemandVersionItemBackupRecordDO backupRecordDO = new ProductDemandVersionItemBackupRecordDO();
            backupRecordDO.setDemandGroupId(groupDO.getId());
            backupRecordDO.setCreator(username);
            backupRecordDO.setDemandGroupStatus(originStatus);
            demandDBHelper.insert(backupRecordDO);
            // 需求预测覆盖式入库
            demandDBHelper.updateAll(ProductDemandVersionItemDO.class,
                    "set deleted = 1, backup_id = ?",
                    "where demand_group_id = ?", backupRecordDO.getId(), groupDO.getId());
        }
        if (!CollectionUtils.isEmpty(data)) {
            // 如果传递空数组就清空，不插入数据
            // 去关联资源中台的国内外表
            List<Map> landResult = purchasereportDBHelper.getRaw(Map.class,
                    "select z.AreaName, a.country\n"
                            + "from Zone z\n"
                            + "left join Area a on z.AreaId = a.AreaId\n"
                            + "group by z.AreaName, a.country");
            Map<String, String> landMap = ListUtils.toMap(landResult,
                    e -> (String) e.get("AreaName"),
                    e -> (String) e.get("country"));
            List<ProductDemandVersionItemDO> result = new ArrayList<>();
            for (SaveItemDTO dto : data) {
                for (YearMonthNumDTO numDTO : dto.getYearMonthValueList()) {
                    ProductDemandVersionItemDO itemDO = SaveItemDTO.toDO(dto, numDTO);
                    itemDO.setPlanProduct(planProduct);
                    itemDO.setDemandGroupId(groupDO.getId());
                    itemDO.setDemandVersion(versionDO.getDemandVersion());
                    String regionType = landMap.getOrDefault(itemDO.getRegionName(), "未知");
                    itemDO.setRegionType(regionType);
                    result.add(itemDO);
                }
            }
            demandDBHelper.insertBatchWithoutReturnId(result);
        }
        validateResultDTO.setGroupId(groupDO.getId());
        return validateResultDTO;
    }

    @Override
    public List<PlanProductMapStatusDTO> queryCurrentVersionPlanProductMapStatus(String username) {
        Set<String> planProductSet = productDemandAuthService.listMyPlanProduct(username,
                ImmutableSet.of(ProductDemandAuthRoleStatusEnum.DEMAND_IMPORTER,
                        ProductDemandAuthRoleStatusEnum.PRODUCT_MANAGER));
        ProductDemandVersionDO versionDO = productDemandVersionService.getCurrentVersion();
        if (versionDO == null || (CollectionUtils.isEmpty(planProductSet) && !productDemandAuthService.hasCOMDRole(
                username,
                ProductDemandAuthRoleStatusEnum.COMD_MANAGER))) {
            return ImmutableList.of();
        }
        List<ProductDemandVersionGroupDO> groupDOS = demandDBHelper.getAll(ProductDemandVersionGroupDO.class,
                "where demand_version = ?", versionDO.getDemandVersion());
        Map<String, String> groupsStatus = groupDOS
                .stream()
                .collect(Collectors.toMap(ProductDemandVersionGroupDO::getPlanProduct,
                        e -> ProductDemandGroupStatusEnum.getNameByCode(e.getStatus()),
                        (e1, e2) -> e1));
        return planProductSet.stream()
                .map(d -> {
                    String status = groupsStatus.getOrDefault(d,
                            ProductDemandGroupStatusEnum.NOT_INPUT.getName());
                    return new PlanProductMapStatusDTO(d, status);
                })
                .sorted(Comparator.comparing(o -> ProductDemandGroupStatusEnum.getByName(o.getStatus()).getSortIdx()))
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductDemandVersionGroupWithVersionVO> listGroup(ListProductDemandGroupReq req) {
        WhereContent whereContent = new WhereContent();
        whereContent.addAnd("v.deleted = ?", 0);
        whereContent.addAnd("g.deleted = ?", 0);
        whereContent.orderDesc("v.id");
        whereContent.orderDesc("g.id");
        if (req != null) {
            if (!CollectionUtils.isEmpty(req.getStatus())) {
                whereContent.addAnd("g.status in (?)",
                        req.getStatus().stream()
                                .map(ProductDemandGroupStatusEnum::getCodeByName)
                                .collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(req.getVersion())) {
                whereContent.addAnd("g.demand_version in (?)", req.getVersion());
            }
            if (!CollectionUtils.isEmpty(req.getPlanProduct())) {
                whereContent.addAnd("g.plan_product in (?)", req.getPlanProduct());
            }
            if (!CollectionUtils.isEmpty(req.getVersionName())) {
                whereContent.addAnd("v.name in (?)", req.getVersionName());
            }
            if (req.getForecastFrom() != null) {
                int year = DateUtils.getYear(req.getForecastFrom());
                int month = DateUtils.getMonth(req.getForecastFrom());
                whereContent.addAnd("v.forecast_from_year > ? "
                                + "or (v.forecast_from_year = ? and v.forecast_from_month >= ?)",
                        year, year, month);
            }
            if (req.getForecastTo() != null) {
                int year = DateUtils.getYear(req.getForecastTo());
                int month = DateUtils.getMonth(req.getForecastTo());
                whereContent.addAnd("v.forecast_to_year < ? "
                                + "or (v.forecast_to_year = ? and v.forecast_to_month <= ?)",
                        year, year, month);
            }
        }
        String sql =
                "select g.*,v.name,v.forecast_from_year,v.forecast_from_month,v.forecast_to_year,v.forecast_to_month"
                        + " from product_demand_version_group g left join product_demand_version v "
                        + "on g.demand_version = v.demand_version ";
        return demandDBHelper.getRaw(ProductDemandVersionGroupWithVersionVO.class,
                sql + whereContent.getSql(), whereContent.getParams());
    }

    @Override
    public ProductDemandVersionGroupDO getGroupById(Long id) {
        return demandDBHelper.getByKey(ProductDemandVersionGroupDO.class, id);
    }

    @Override
    public Map<String, Object> queryDemandInfoReq(QueryDemandInfoReq req, String username) {
        ProductDemandVersionGroupWithVersionVO groupDO = getGroupDO(req.getGroupId(), req.getPlanProduct(),
                req.getVersion());
        if (groupDO == null) {
            throw new BizException("找不到该版本录入的规划产品需求信息");
        }
        // 权限控制
        if (!hasThisPlanProductOrCOMDAuth(username, groupDO.getPlanProduct(), true)) {
            throw new BizException("您没有权限查看[" + groupDO.getPlanProduct() + "]的规划产品信息");
        }
        List<ProductDemandVersionItemDO> itemDOS = demandDBHelper.getAll(ProductDemandVersionItemDO.class,
                "where demand_group_id = ?", groupDO.getId());
        return buildResp(itemDOS, groupDO.getVersionDO(), groupDO.getPlanProduct());
    }

    private Map<String, Object> buildResp(List<ProductDemandVersionItemDO> itemDOS,
                                          ProductDemandVersionDO versionDO,
                                          String planProduct){
        // 拉取核心数
        Map<String, ServerPartsExtendedInfoDO> serverConfigInfoMap = productDemandDictService.getAllServerConfigInfo();
        if (serverConfigInfoMap == null) {
            throw new BizException("拉取核心数失败");
        }
        // 转换成2维表格
        // 区分不出单次录入完全重复key的情况，所以要用DB的rowId字段
        Map<String, List<ProductDemandVersionItemDO>> preMap = ListUtils.groupBy(itemDOS,
                ProductDemandVersionItemDO::getWithRemarkPreMapKey);
        List<YearMonth> yearMonthList = versionDO.actualForecastYearMonthList();
        String[] commonHeader = new String[]{
                "obsProjectType", "moduleBusinessTypeName", "industryName", "customerName", "regionType", "regionName",
                "zoneName", "campusName", "deviceTypeName", "reason", "remark"};
        String[] header = new String[commonHeader.length + yearMonthList.size()];
        System.arraycopy(commonHeader, 0, header, 0, commonHeader.length);
        for (int i = commonHeader.length; i < header.length; i++) {
            header[i] = yearMonthList.get(i - commonHeader.length).toDateStr();
        }
        List<Map<String, Object>> data = new LinkedList<>();
        for (String key : preMap.keySet()) {
            Map<String, Object> d = new HashMap<>();
            String[] k = key.split("@", -1);
            for (int i = 0; i < commonHeader.length; i++) {
                // + 1是为了跳过row_id
                d.put(header[i], k[i + 1].equals("null") ? null : k[i + 1]);
            }
            for (ProductDemandVersionItemDO pdo : preMap.get(key)) {
                String ym = pdo.toDateStr();
                ServerPartsExtendedInfoDO serverPartsExtendedInfoDO = serverConfigInfoMap.get(pdo.getDeviceTypeName());
                if (serverPartsExtendedInfoDO == null) {
                    throw new BizException(String.format("机型【%s】核心数配置缺失", pdo.getDeviceTypeName()));
                }
                int coreNum = pdo.getDemandNum() * serverPartsExtendedInfoDO.getCpuLogicCore();
                DemandNumAndCoreNumDTO numAndCoreNumDTO = (DemandNumAndCoreNumDTO) d.get(ym);
                if (numAndCoreNumDTO == null) {
                    numAndCoreNumDTO = new DemandNumAndCoreNumDTO(pdo.getDemandNum(), coreNum);
                }else {
                    numAndCoreNumDTO.setDemandNum(numAndCoreNumDTO.getDemandNum() + pdo.getDemandNum());
                    numAndCoreNumDTO.setCoreNum(numAndCoreNumDTO.getCoreNum() + coreNum);
                }
                d.put(ym, numAndCoreNumDTO);
            }
            data.add(d);
        }
        return MapUtils.of("header", header,
                "data", data,
                "version", versionDO.getDemandVersion(),
                "planProduct", planProduct);

    }

    private List<Object> givenSampleData(int ymSize) {
        String[] obsProjectType = new String[]{"常规项目", "机房裁撤"};
        String[] reason = new String[]{"客户常规增长", "IDC裁撤"};
        String[] industryName = new String[]{"常规", "云产品"};
        String[] customerName = new String[]{"常规", "云产品其它"};
        String[] regionName = new String[]{"华东", "华南"};
        String[] zoneName = new String[]{"上海", "广州"};
        String[] campusName = new String[]{"上海-宝信", "广州-华新园"};
        String[] deviceTypeName = new String[]{"Y0-MS52-25G", "Y0-MI52-25G"};
        String[] remark = new String[]{"示例数据", "示例数据"};
        List<Object> data = new LinkedList<>();
        for (int i = 0; i < 2; i++) {
            Map<String, Object> m = MapUtils.of(
                    "obsProjectType", obsProjectType[i],
                    "reason", reason[i],
                    "industryName", industryName[i],
                    "customerName", customerName[i],
                    "regionName", regionName[i],
                    "zoneName", zoneName[i],
                    "campusName", campusName[i],
                    "deviceTypeName", deviceTypeName[i],
                    "remark", remark[i]);
            for (int j = 0; j < ymSize; j++) {
                m.put("m" + (j + 1), 10);
            }
            data.add(m);
        }
        return data;
    }

    private FileNameAndBytesDTO excelMaker(List itemDTOS, List<YearMonth> yearMonthList, String fileName) {
        InputStream templateIn = IOUtils.readClasspathResourceInputStream("excel/product_demand/template.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        Map<String, Collection<DataWrapper>> dataWrapperMap = dataWrapper();
        ExcelWriter excelWriter = EasyExcel.write(out)
                .registerWriteHandler(commonWriteHandler(dataWrapperMap))
                .withTemplate(templateIn).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        // 如果是横向的，加上这个
        FillConfig fillConfig = FillConfig.builder().direction(WriteDirectionEnum.HORIZONTAL).build();
        FillConfig dictConfig = FillConfig.builder().forceNewRow(true).build();

        List<Map<String, Object>> ym = new ArrayList<>();
        ListUtils.forEach(yearMonthList, o -> {
            ym.add(MapUtils.of("yearMonth", o.getYear() + "年" + o.getMonth() + "月"));
        });
        Set<String> obsProjectTypeSet = DynamicProperties.obsProjectTypeEnumSet();
        String projText = String.join(",", obsProjectTypeSet);
        Map<String, Object> projTextWarp = ImmutableMap.of("v", projText);
        excelWriter.fill(new FillWrapper("projText", ImmutableList.of(projTextWarp)), writeSheet);
        excelWriter.fill(new FillWrapper("ym", ym), fillConfig, writeSheet);
        // 新增两行示例数据
        excelWriter.fill(new FillWrapper("item", itemDTOS), writeSheet);
        WriteSheet dictSheet = EasyExcel.writerSheet("字典").build();
        dataWrapperMap.forEach((k, v) -> {
            excelWriter.fill(new FillWrapper(k, v), dictConfig, dictSheet);
        });
        excelWriter.finish();
        FileNameAndBytesDTO fileNameAndBytesDTO = new FileNameAndBytesDTO();
        fileNameAndBytesDTO.setBytes(out.toByteArray());
        fileNameAndBytesDTO.setFileName(fileName);
        return fileNameAndBytesDTO;
    }

    @Override
    public FileNameAndBytesDTO exportTemplate() {
        ProductDemandVersionDO productDemandVersionDO = productDemandVersionService.getCurrentVersion();
        if (productDemandVersionDO == null) {
            throw new BizException("当前没有开启任何版本，没有模板可以导出");
        }
        List<YearMonth> yearMonthList = productDemandVersionDO.actualForecastYearMonthList();
        return excelMaker(givenSampleData(yearMonthList.size()), yearMonthList, "产品全年需求录入模板.xlsx");
    }

    @Override
    public FileNameAndBytesDTO exportDemandDetail(QueryDemandInfoReq req, String username) {
        ProductDemandVersionGroupWithVersionVO groupDO = getGroupDO(req.getGroupId(), req.getPlanProduct(),
                req.getVersion());
        if (groupDO == null) {
            throw new BizException("找不到该版本录入的该规划产品的需求信息");
        }
        // 权限控制
        if (!hasThisPlanProductOrCOMDAuth(username, groupDO.getPlanProduct(), true)) {
            throw new BizException("您没有权限查看[" + groupDO.getPlanProduct() + "]的规划产品信息");
        }
        ProductDemandVersionDO productDemandVersionDO = groupDO.getVersionDO();
        List<YearMonth> yearMonthList = productDemandVersionDO.actualForecastYearMonthList();
        List<ProductDemandVersionItemDO> itemDOS = demandDBHelper.getAll(ProductDemandVersionItemDO.class,
                "where demand_group_id = ?", groupDO.getId());
        Map<String, List<ProductDemandVersionItemDO>> preMap = ListUtils.groupBy(itemDOS,
                ProductDemandVersionItemDO::getWithRemarkPreMapKey);
        List<Map<String, Object>> excelItems = new LinkedList<>();
        for (Entry<String, List<ProductDemandVersionItemDO>> e : preMap.entrySet()) {
            Map<String, Object> map = e.getValue().get(0).transToExcelMap();
            // 处理月份
            Map<String, ProductDemandVersionItemDO> ymMap = ListUtils.toMap(e.getValue(),
                    ProductDemandVersionItemDO::toDateStr, Function.identity());
            int i = 0;
            for (; i < yearMonthList.size(); i++) {
                ProductDemandVersionItemDO itemDO = ymMap.get(yearMonthList.get(i).toDateStr());
                map.put("m" + (i + 1), itemDO == null ? "" : itemDO.getDemandNum());
            }

            excelItems.add(map);
        }
        return excelMaker(excelItems, yearMonthList,
                "需求明细-" + groupDO.getDemandVersion() + "-" + groupDO.getPlanProduct() + "-"
                        + DateUtils.format(
                        new Date(), "yyyyMMddHHmmss") + ".xlsx");
    }

    @Override
    public List<Map<String, Object>> decodeDemandExcel(MultipartFile multipartFile, String version) {
        List<Map<String, Object>> result = new LinkedList<>();
        try {
            ProductDemandVersionDO versionDO = demandDBHelper.getOne(ProductDemandVersionDO.class,
                    "where demand_version = ?", version);
            // 拉取核心数
            Map<String, ServerPartsExtendedInfoDO> coreMap = productDemandDictService.getAllServerConfigInfo();
            List<YearMonth> yearMonthList = null;
            if (versionDO != null) {
                yearMonthList = versionDO.actualForecastYearMonthList();
            }
            List<YearMonth> finalYearMonthList = yearMonthList;
            EasyExcel.read(multipartFile.getInputStream(),
                    new AnalysisEventListener<Map<Integer, String>>() {
                        @Override
                        public void invoke(Map<Integer, String> data, AnalysisContext context) {
                            if (CollectionUtils.isEmpty(data)
                                    || CollectionUtils.isEmpty(data.values()
                                    .stream()
                                    .filter(e -> !StringUtils.isBlank(e))
                                    .collect(Collectors.toSet()))) {
                                // 全空的，代表遇到行尾了，结束
                                return;
                            }
                            Map<String, Object> dataMap = new HashMap<>();
                            dataMap.put("obsProjectType", data.get(0));
                            dataMap.put("moduleBusinessTypeName", data.get(1));
                            dataMap.put("reason", data.get(2));
                            dataMap.put("industryName", data.get(3));
                            dataMap.put("customerName", data.get(4));
                            dataMap.put("regionName", data.get(5));
                            dataMap.put("zoneName", data.get(6));
                            dataMap.put("campusName", data.get(7));
                            dataMap.put("deviceTypeName", data.get(8));
                            dataMap.put("remark", data.get(9));

                            ServerPartsExtendedInfoDO infoDO = coreMap.get((String) dataMap.get("deviceTypeName"));
                            if (infoDO == null) {
                                throw new BizException(String.format("机型【%s】核心数配置缺失", dataMap.get("deviceTypeName")));
                            }

                            if (!CollectionUtils.isEmpty(finalYearMonthList)) {
                                int ymStartIdx = 10;
                                for (YearMonth ym : finalYearMonthList) {
                                    Integer amount = NumberUtils.parseInt(data.get(ymStartIdx++));
                                    if (amount == null) {
                                        dataMap.put(ym.toDateStr(), new DemandNumAndCoreNumDTO(0, 0));
                                    } else {
                                        dataMap.put(ym.toDateStr(), new DemandNumAndCoreNumDTO(amount, amount * infoDO.getCpuLogicCore()));
                                    }
                                }
                            }
                            result.add(dataMap);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                        }
                    }).sheet(0).headRowNumber(2).doRead();
        } catch (Exception e) {
            log.error("decode excel error:", e);
            throw new BizException("文件解析失败:" + e.getMessage());
        }
        return result;
    }

    private Set<DataWrapper> strCollection2DW(Collection<String> set) {
        return set.stream()
                .map(DataWrapper::new)
                .collect(Collectors.toSet());
    }

    private Map<String, Collection<DataWrapper>> dataWrapper() {
        Map<String, Collection<DataWrapper>> map = new HashMap<>();
        try {
            int i = 0;
            map.put(alphabets[i++].toLowerCase(), strCollection2DW(rrpRemakeDictService.listObsProjectTypeByPlanProduct(null)));
            map.put(alphabets[i++].toLowerCase(), strCollection2DW(productDemandDictService.listAllModuleBusinessTypeName(null)));
            map.put(alphabets[i++].toLowerCase(), strCollection2DW(
                    productDemandDictService.listAllReasonMap().keySet()));
            map.put(alphabets[i++].toLowerCase(), strCollection2DW(productDemandDictService.listAllIndustryName()));
            map.put(alphabets[i++].toLowerCase(), strCollection2DW(productDemandDictService.listAllCustomerName(null)));
            map.put(alphabets[i++].toLowerCase(), strCollection2DW(productDemandDictService.listAllRegionName()));
            map.put(alphabets[i++].toLowerCase(), strCollection2DW(productDemandDictService.listAllZoneName(null)));
            map.put(alphabets[i++].toLowerCase(), strCollection2DW(productDemandDictService.listAllCampusName(null)));
            map.put(alphabets[i].toLowerCase(), strCollection2DW(productDemandDictService.listAllDeviceTypeName(null)));
        } catch (Exception e) {
            log.error("drop map build failed: ", e);
        }
        return map;
    }

    private CommonWriteHandler commonWriteHandler(Map<String, Collection<DataWrapper>> dataMap) {
        Map<String, Address> map = new HashMap<>();
        for (int i = 0; i < alphabets.length; i++) {
            String alphabet = alphabets[i];
            Collection<DataWrapper> dataList = dataMap.get(alphabet.toLowerCase());
            if (!CollectionUtils.isEmpty(dataList)) {
                map.put(alphabet, new Address(i, alphabet, dataList.size(), backupAlphabets[i]));
            }
        }
        return new CommonWriteHandler(map, true);
    }

    private List<SummaryStatisticItem> getW13SummaryStatistic(ProductDemandVersionGroupWithVersionVO groupDO,
                                                              SummaryStatisticReq req, Map<String, ServerPartsExtendedInfoDO> serverConfigInfoMap) {
        // 加载13周数据
        String w13Version = groupDO.getVersionDO().getW13DemandVersion();
        List<String> yearMonths = groupDO.getVersionDO().week13YearMonthList().stream()
                .map(YearMonth::toDateStr)
                .collect(Collectors.toList());
        WhereContent whereContent = new WhereContent();
        if (!CollectionUtils.isEmpty(req.getIndustryName())) {
            whereContent.addAnd("p.industry in (?)", req.getIndustryName());
        }
        if (!CollectionUtils.isEmpty(req.getCustomerName())) {
            whereContent.addAnd("p.customer_name in (?)", req.getCustomerName());
        }
        if (!CollectionUtils.isEmpty(req.getZoneName())) {
            whereContent.addAnd("p.zone in (?)", req.getZoneName());
        }
        if (!CollectionUtils.isEmpty(req.getDeviceTypeName())) {
            whereContent.addAnd("p.deviceType in (?)", req.getDeviceTypeName());
        }
        if (!CollectionUtils.isEmpty(req.getReason())) {
            whereContent.addAnd("p.reason in (?)", req.getReason());
        }
        if (!CollectionUtils.isEmpty(req.getReasonType())) {
            whereContent.addAnd("p.reason_type in (?)", req.getReasonType());
        }
        whereContent.addAnd("c.plan_version = ? and p.proj_set_name = ? and p.month in (?) "
                + "and (p.backup is null or p.backup = '') "
                + "and f.currentStep not in (6, 7) "
                + "and f.product = ? ", w13Version, "非自研上云", yearMonths, groupDO.getPlanProduct());
        String rrpSql = "select p.`month`, p.deviceType, "
                + "sum(p.amount + ifnull(p.curplan_adjust, 0)) amount "
                + "from product_info p left join flow_info f on f.id = p.flowId "
                + "left join rrp_config c on c.id = f.rrp_config_id "
                + whereContent.getSql()
                + "group by p.`month`,p.deviceType";
        List<RRPDemandItemDO> rrpDemandItemDOS = rrpDBHelper.getRaw(RRPDemandItemDO.class,
                rrpSql, whereContent.getParams());

        Map<String, DemandNumAndCoreNumDTO> resultMap = rrpDemandItemDOS.stream().reduce(new HashMap<>(), (r, i) -> {
            String[] ym = i.getYearMonthText().split("-");
            String key = ym[0] + "年" + Integer.parseInt(ym[1]) + "月";
            DemandNumAndCoreNumDTO origin = r.getOrDefault(key, new DemandNumAndCoreNumDTO());
            ServerPartsExtendedInfoDO serverPartsExtendedInfoDO = serverConfigInfoMap.get(i.getDeviceType());
            if (serverPartsExtendedInfoDO == null) {
                throw new BizException(String.format("机型【%s】核心数配置缺失", i.getDeviceType()));
            }
            origin.add(i.getAmount(), i.getAmount() * serverPartsExtendedInfoDO.getCpuLogicCore());
            r.put(key, origin);
            return r;
        }, (e1, e2) -> e1);
        List<SummaryStatisticItem> result = new ArrayList<>();
        resultMap.forEach((k, v) -> {
            result.add(new SummaryStatisticItem(k, 0, v.getDemandNum(), v.getDemandNum(), 0, v.getCoreNum(), v.getCoreNum()));
        });
        return result;
    }

    private List<SummaryStatisticItem> getHistoryExecutedNum(ProductDemandVersionDO versionDO, SummaryStatisticReq req,
                                                             String planProduct, Map<String, ServerPartsExtendedInfoDO> serverConfigInfoMap) {
        List<YearMonth> ym = versionDO.historyYearMonthList();
        if (CollectionUtils.isEmpty(ym)) {
            return new ArrayList<>();
        }
        // 获取资源中台汇总的采购单数据，计算出已执行量和未执行量
        // 获取全部的采购单数据，待会再根据条件过滤并group by
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("plan_product = ?", planProduct);
        // 行业和客户的筛选在下面做
        if (!CollectionUtils.isEmpty(req.getZoneName())) {
            whereSQL.and("city in (?)", req.getZoneName());
        }
        if (!CollectionUtils.isEmpty(req.getDeviceTypeName())) {
            whereSQL.and("deviceType in (?)", req.getDeviceTypeName());
        }
        if (!CollectionUtils.isEmpty(req.getReason())) {
            whereSQL.and("pur_reason in (?)", req.getReason());
        }
        if (!CollectionUtils.isEmpty(req.getReasonType())) {
            Map<String, String> reasonMap = productDemandDictService.listAllReasonMap();
            List<String> reasonTypes = new ArrayList<>();
            for (String reason : reasonMap.keySet()) {
                if (req.getReasonType().contains(reasonMap.get(reason))) {
                    reasonTypes.add(reason);
                }
            }
            if (!CollectionUtils.isEmpty(reasonTypes)) {
                whereSQL.and("pur_reason in (?)", reasonTypes);
            }
        }
        // 调拨类型 = 全部(1,100)
        // 这里加0是为了查出主单，用主单的信息补充行业和客户信息
        whereSQL.and("order_type in (0, 1, 100)");
        // 单据状态和资源组大报表的逻辑一致，即不是（打回重填、星云拒绝、挂起、ERP拒绝）
        whereSQL.and("status not in (0, 3, 5, 8000127)");
        // 需求月份 in 版本必填月份
        whereSQL.and("plan_month in (?)", ym.stream().map(YearMonth::toDateStr).collect(Collectors.toList()));
        // 项目类型 = 非自研上云
        whereSQL.and(rrpBaseInfoService.generateNotZysySQLCondition(rrpBaseInfoService.getRawPlanProductToBusiness()));
        List<DeviceApplyDO> rawDeviceApplyDOS = shuttleDBHelper.getAll(DeviceApplyDO.class,
                whereSQL.getSQL(), whereSQL.getParams());
        Map<String, DeviceApplyDO> baseDeviceApplyDOMap = rawDeviceApplyDOS
                .stream().filter(e -> e.getOrderType().equals(0))
                .collect(Collectors.toMap(DeviceApplyDO::getId, Function.identity(),
                        (e1, e2) -> e1));
        Stream<DeviceApplyDO> stream = rawDeviceApplyDOS.stream()
                .filter(e -> !e.getOrderType().equals(0))
                .peek(e -> {
                    DeviceApplyDO baseDO = baseDeviceApplyDOMap.get(e.getId());
                    if (baseDO != null) {
                        e.setIndustry(baseDO.getIndustry());
                        e.setCustomerName(baseDO.getCustomerName());
                    } else {
                        if (e.getIndustry() == null) {
                            e.setIndustry("");
                        }
                        if (e.getCustomerName() == null) {
                            e.setCustomerName("");
                        }
                    }
                });
        if (!CollectionUtils.isEmpty(req.getIndustryName())) {
            stream = stream.filter(e -> req.getIndustryName().contains(e.getIndustry()));
        }
        if (!CollectionUtils.isEmpty(req.getCustomerName())) {
            stream = stream.filter(e -> req.getCustomerName().contains(e.getCustomerName()));
        }
        Map<String, DemandNumAndCoreNumDTO> resultMap = stream.reduce(new HashMap<>(),
                (r, i) -> {
                    String planMonth = i.getPlanMonth();
                    DemandNumAndCoreNumDTO origin = r.getOrDefault(planMonth, new DemandNumAndCoreNumDTO());
                    ServerPartsExtendedInfoDO serverPartsExtendedInfoDO = serverConfigInfoMap.get(i.getDeviceType());
                    if (serverPartsExtendedInfoDO == null) {
                        throw new BizException(String.format("机型【%s】核心数配置缺失", i.getDeviceType()));
                    }
                    origin.add(i.getTotalNum(), i.getTotalNum() * serverPartsExtendedInfoDO.getCpuLogicCore());
                    r.put(planMonth, origin);
                    return r;
                },
                (e1, e2) -> e1);
        // 此时的行业和客户应该是补好了
        List<SummaryStatisticItem> result = new ArrayList<>();
        resultMap.forEach((k, v) -> {
            String[] ymt = k.split("-");
            result.add(
                    new SummaryStatisticItem(NumberUtils.parseInt(ymt[0]) + "年" + NumberUtils.parseInt(ymt[1]) + "月",
                            0, v.getDemandNum(), v.getDemandNum(), 0, v.getCoreNum(), v.getCoreNum()));
        });
        return result;
    }

    private List<SummaryStatisticItem> getFullYearDemandItem(ProductDemandVersionGroupWithVersionVO groupDO,
                                                             SummaryStatisticReq req, Map<String, ServerPartsExtendedInfoDO> serverConfigInfoMap) {
        // 加载全年预测item成结构化的item
        WhereContent whereContent = new WhereContent();
        whereContent.addAnd("demand_group_id = ?", groupDO.getId());
        if (!CollectionUtils.isEmpty(req.getIndustryName())) {
            whereContent.addAnd("industry_name in (?)", req.getIndustryName());
        }
        if (!CollectionUtils.isEmpty(req.getCustomerName())) {
            whereContent.addAnd("customer_name in (?)", req.getCustomerName());
        }
        if (!CollectionUtils.isEmpty(req.getZoneName())) {
            whereContent.addAnd("zone_name in (?)", req.getZoneName());
        }
        if (!CollectionUtils.isEmpty(req.getDeviceTypeName())) {
            whereContent.addAnd("device_type_name in (?)", req.getDeviceTypeName());
        }
        if (!CollectionUtils.isEmpty(req.getReason())) {
            whereContent.addAnd("reason in (?)", req.getReason());
        }
        if (!CollectionUtils.isEmpty(req.getReasonType())) {
            Map<String, String> reasonMap = productDemandDictService.listAllReasonMap();
            List<String> reasonTypes = new ArrayList<>();
            for (String reason : reasonMap.keySet()) {
                if (req.getReasonType().contains(reasonMap.get(reason))) {
                    reasonTypes.add(reason);
                }
            }
            if (!CollectionUtils.isEmpty(reasonTypes)) {
                whereContent.addAnd("reason in (?)", reasonTypes);
            }
        }
        List<ProductDemandVersionItemDO> itemDOS = demandDBHelper.getAll(ProductDemandVersionItemDO.class,
                whereContent.getSql(), whereContent.getParams());
        Map<String, DemandNumAndCoreNumDTO> resultMap = itemDOS.stream().reduce(new HashMap<>(), (r, i) -> {
            String key = i.getDemandYear() + "年" + i.getDemandMonth() + "月";
            DemandNumAndCoreNumDTO origin = r.getOrDefault(key, new DemandNumAndCoreNumDTO());
            ServerPartsExtendedInfoDO serverPartsExtendedInfoDO = serverConfigInfoMap.get(i.getDeviceTypeName());
            if (serverPartsExtendedInfoDO == null) {
                throw new BizException(String.format("机型【%s】核心数配置缺失", i.getDeviceTypeName()));
            }
            origin.add(i.getDemandNum(), i.getDemandNum() * serverPartsExtendedInfoDO.getCpuLogicCore());
            r.put(key, origin);
            return r;
        }, (e1, e2) -> e1);
        List<SummaryStatisticItem> result = new ArrayList<>();
        resultMap.forEach((k, v) -> {
            result.add(new SummaryStatisticItem(k, 0, v.getDemandNum(), v.getDemandNum(), 0, v.getCoreNum(), v.getCoreNum()));
        });
        return result;
    }

    private Map<String, Map<String, SummaryStatisticItem>> combineSummary(
            ProductDemandVersionGroupWithVersionVO groupDO,
            SummaryStatisticReq req,
            Map<String, ServerPartsExtendedInfoDO> serverConfigInfoMap) {
        List<SummaryStatisticItem> detail = getFullYearDemandItem(groupDO, req, serverConfigInfoMap);
        List<SummaryStatisticItem> week13 = getW13SummaryStatistic(groupDO, req, serverConfigInfoMap);
        Set<String> week13YearMonth = groupDO.getVersionDO().week13YearMonthList()
                .stream()
                .map(YearMonth::toChineseDateStr)
                .collect(Collectors.toSet());
        List<SummaryStatisticItem> history = getHistoryExecutedNum(groupDO.getVersionDO(), req,
                groupDO.getPlanProduct(), serverConfigInfoMap);
        Set<String> historyYearMonth = groupDO.getVersionDO().historyYearMonthList()
                .stream()
                .map(YearMonth::toChineseDateStr)
                .collect(Collectors.toSet());
        // 填充13周，再填充历史执行量，最后填充全年
        // 不属于自己的月份，补0，原来的逻辑不对
        AtomicLong totalDetail = new AtomicLong();
        AtomicLong totalHistory = new AtomicLong();
        AtomicLong total13Week = new AtomicLong();
        AtomicLong coreTotalDetail = new AtomicLong();
        AtomicLong coreTotalHistory = new AtomicLong();
        AtomicLong coreTotal13Week = new AtomicLong();
        Map<String, SummaryStatisticItem> week13Result = initSummaryMap(groupDO.getVersionDO().week13YearMonthList());
        week13.forEach(e -> {
            week13Result.put(e.getTitle(), e);
            total13Week.addAndGet(e.getValue());
            coreTotal13Week.addAndGet(e.getCoreValue());
        });
        if (!CollectionUtils.isEmpty(week13YearMonth)) {
            for (String ym : week13YearMonth) {
                if (!week13Result.containsKey(ym)) {
                    week13Result.put(ym, new SummaryStatisticItem(ym));
                }
            }
        }
        Map<String, SummaryStatisticItem> historyResult = initSummaryMap(groupDO.getVersionDO().historyYearMonthList());
        history.forEach(e -> {
            historyResult.put(e.getTitle(), e);
            totalHistory.addAndGet(e.getValue());
            coreTotalHistory.addAndGet(e.getCoreValue());
        });
        if (!CollectionUtils.isEmpty(historyYearMonth)) {
            for (String ym : historyYearMonth) {
                if (!historyResult.containsKey(ym)) {
                    historyResult.put(ym, new SummaryStatisticItem(ym));
                }
            }
        }
        Map<String, SummaryStatisticItem> detailResult = initSummaryMap(groupDO.getVersionDO().actualForecastYearMonthList());
        detail.forEach(e -> {
            if (!week13Result.containsKey(e.getTitle()) && !historyResult.containsKey(e.getTitle())) {
                detailResult.put(e.getTitle(), e);
                totalDetail.addAndGet(e.getValue());
                coreTotalDetail.addAndGet(e.getCoreValue());
            }
        });
        Map<String, SummaryStatisticItem> totalResult = new HashMap<>();
        long total = total13Week.get() + totalHistory.get() + totalDetail.get();
        long coreTotal = coreTotal13Week.get() + coreTotalHistory.get() + coreTotalDetail.get();
        totalResult.put("年度合计",
                new SummaryStatisticItem("年度合计", 0, total, total, 0, coreTotal, coreTotal));
        totalResult.put("历史执行",
                new SummaryStatisticItem("历史执行", 0, totalHistory.get(), totalHistory.get(), 0, coreTotalHistory.get(), coreTotalHistory.get()));
        totalResult.put("13周合计",
                new SummaryStatisticItem("13周合计", 0, total13Week.get(), total13Week.get(), 0, coreTotal13Week.get(), coreTotal13Week.get()));
        totalResult.put("本次录入合计",
                new SummaryStatisticItem("本次录入合计", 0, totalDetail.get(), totalDetail.get(), 0, coreTotalDetail.get(), coreTotalDetail.get()));
        Map<String, Map<String, SummaryStatisticItem>> result = new HashMap<>();
        result.put("total", totalResult);
        result.put("history", historyResult);
        result.put("week13", week13Result);
        result.put("detail", detailResult);
        return result;
    }

    private Map<String, SummaryStatisticItem> initSummaryMap(List<YearMonth> yearMonthList) {

        Map<String, SummaryStatisticItem> result = new HashMap<>();
        for (YearMonth ym : yearMonthList) {
            result.put(ym.toChineseDateStr(), new SummaryStatisticItem(ym.toChineseDateStr()));
        }
        return result;
    }

    private Map<String, Object> toFinalSummaryResult(
            Map<String, Map<String, SummaryStatisticItem>> tempResult, String lastVersion) {
        Map<String, Object> result = new HashMap<>();

        for (Entry<String, Map<String, SummaryStatisticItem>> entry : tempResult.entrySet()) {
            Map<String, SummaryStatisticItem> m = entry.getValue();
            String k = entry.getKey();
            List<SummaryStatisticItem> temp = new ArrayList<>(m.values());
            if (k.equals("total")) {
                ListUtils.sortAscNullLast(temp, o -> {
                    if (o.getTitle().equals("年度合计")) {
                        return 1;
                    }
                    if (o.getTitle().equals("历史执行")) {
                        return 2;
                    }
                    if (o.getTitle().equals("13周合计")) {
                        return 3;
                    }
                    if (o.getTitle().equals("本次录入合计")) {
                        return 4;
                    }
                    return 999;
                });
            } else {
                ListUtils.sortAscNullLast(temp, e -> DateUtils.parse(e.getTitle(), "yyyy年MM月"));
            }

            result.put(k, temp);
            result.put("lastVersion", lastVersion);
        }
        return result;
    }

    @Override
    public Map<String, Object> summaryStatistic(SummaryStatisticReq req) {
        Long groupId = req.getGroupId();
        ProductDemandVersionGroupWithVersionVO groupDO = demandDBHelper.getOne(
                ProductDemandVersionGroupWithVersionVO.class,
                "where id = ?", groupId);
        // 权限控制
        String username = LoginUtils.getUserName();
        if (!hasThisPlanProductOrCOMDAuth(username, groupDO.getPlanProduct(), true)) {
            throw new BizException("您没有权限查看[" + groupDO.getPlanProduct() + "]的规划产品信息");
        }
        List<ProductDemandVersionDO> versionDOS = demandDBHelper.getAll(ProductDemandVersionDO.class,
                "order by id desc");
        // 拉取核心数
        Map<String, ServerPartsExtendedInfoDO> serverConfigInfoMap = productDemandDictService.getAllServerConfigInfo();
        // 先算出本期结果
        Map<String, Map<String, SummaryStatisticItem>> nowResult = combineSummary(groupDO, req, serverConfigInfoMap);
        if (CollectionUtils.isEmpty(versionDOS)) {
            // 非本版本的版本列表为空，返回本期结果
            return toFinalSummaryResult(nowResult, "");
        }
        String lastVersion = req.getLastVersion();
        if (lastVersion != null) {
            // 不能与本版本的版本号相同，返回本期结果(diff设置为0,prev 设置跟value一样）
            if (lastVersion.equals(groupDO.getDemandVersion())) {
                Map<String, Object> result = toFinalSummaryResult(nowResult, lastVersion);
                for (Entry<String, Object> entry : result.entrySet()) {
                    if (entry.getKey().equals("lastVersion")) {
                        continue;
                    }
                    List<SummaryStatisticItem> list = (List<SummaryStatisticItem>) entry.getValue();
                    list.forEach(e -> {
                        e.setPrev(e.getValue());
                        e.setDiff(0);
                        e.setCorePrev(e.getCoreValue());
                        e.setCoreDiff(0);
                    });
                }
                return result;
            } else if (!versionDOS.stream().map(ProductDemandVersionDO::getDemandVersion).collect(Collectors.toSet())
                    .contains(lastVersion)) {
                // 看是否在版本列表里，抛异常
                throw new BizException("未知的版本号参与比较");
            }
        } else {
            // 不传版本号的情况默认给个最近的版本号
            lastVersion = versionDOS.get(0).getDemandVersion();
        }
        ProductDemandVersionGroupWithVersionVO lastGroupDO = demandDBHelper.getOne(
                ProductDemandVersionGroupWithVersionVO.class,
                "where plan_product = ? and demand_version = ?",
                groupDO.getPlanProduct(), lastVersion);
        if (lastGroupDO == null) {
            return toFinalSummaryResult(nowResult, lastVersion);
        } else {
            Map<String, Map<String, SummaryStatisticItem>> lastResult = combineSummary(lastGroupDO, req, serverConfigInfoMap);
            // 对比两期结果
            Map<String, Map<String, SummaryStatisticItem>> finalResult = new HashMap<>();
            for (String key : nowResult.keySet()) {
                Map<String, SummaryStatisticItem> now = nowResult.get(key);
                Map<String, SummaryStatisticItem> last = lastResult.get(key);
                Map<String, SummaryStatisticItem> finalM = new HashMap<>();
                // 以本期的为准
                for (String subKey : now.keySet()) {
                    SummaryStatisticItem subNow = now.get(subKey);
                    SummaryStatisticItem subLast = last.get(subKey);
                    SummaryStatisticItem subFinal = SummaryStatisticItem.copy(subNow);
                    if (subLast != null) {
                        subFinal.setPrev(subLast.getValue());
                        subFinal.setDiff(subFinal.getValue() - subLast.getValue());
                        subFinal.setCorePrev(subLast.getCoreValue());
                        subFinal.setCoreDiff(subFinal.getCoreValue() - subLast.getCoreValue());
                    }
                    finalM.put(subKey, subFinal);
                }
                finalResult.put(key, finalM);
            }
            return toFinalSummaryResult(finalResult, lastVersion);
        }
    }

    @Override
    public Map<String, Object> summaryStatisticByField(SummaryStatisticReq req) {
        String username = LoginUtils.getUserName();
        Map<String, ServerPartsExtendedInfoDO> coreMap = productDemandDictService.getAllServerConfigInfo();
        String field = req.getField();
        Set<String> fields = ImmutableSet.of("industryName", "customerName", "planProduct", "deviceTypeName");
        if (!fields.contains(field)) {
            throw new BizException("汇总字段须在" + fields + "内");
        }
        Function<ProductDemandVersionItemDO, String> fn = (item) -> {
            try {
                Field f = item.getClass().getDeclaredField(field);
                f.setAccessible(true);
                return (String) f.get(item);
            } catch (Exception e) {
                throw new ITException(e);
            }
        };
        // 有版本号优先用版本号
        List<ProductDemandVersionItemDO> itemDOS;
        List<ProductDemandVersionDO> versionDOS;
        String version = req.getVersion();
        Long groupId = req.getGroupId();
        WhereContent defaultWhere = new WhereContent();
        WhereContent lastWhere = new WhereContent();
        if (!StringUtils.isBlank(version)) {
            // 权限控制，只能看自己有规划产品权限的
            Boolean isCOMD = productDemandAuthService.hasCOMDRole(username);
            if (!CollectionUtils.isEmpty(req.getIndustryName())) {
                defaultWhere.addAnd("industry_name in (?)", req.getIndustryName());
                lastWhere.addAnd("industry_name in (?)", req.getIndustryName());
            }
            if (!CollectionUtils.isEmpty(req.getCustomerName())) {
                defaultWhere.addAnd("customer_name in (?)", req.getCustomerName());
                lastWhere.addAnd("customer_name in (?)", req.getCustomerName());
            }
            if (!CollectionUtils.isEmpty(req.getZoneName())) {
                defaultWhere.addAnd("zone_name in (?)", req.getZoneName());
                lastWhere.addAnd("zone_name in (?)", req.getZoneName());
            }
            if (!CollectionUtils.isEmpty(req.getDeviceTypeName())) {
                defaultWhere.addAnd("device_type_name in (?)", req.getDeviceTypeName());
                lastWhere.addAnd("device_type_name in (?)", req.getDeviceTypeName());
            }
            if (!CollectionUtils.isEmpty(req.getReason())) {
                defaultWhere.addAnd("reason in (?)", req.getReason());
                lastWhere.addAnd("reason in (?)", req.getReason());
            }
            if (!CollectionUtils.isEmpty(req.getReasonType())) {
                Map<String, String> reasonMap = productDemandDictService.listAllReasonMap();
                List<String> reasonTypes = new ArrayList<>();
                for (String reason : reasonMap.keySet()) {
                    if (req.getReasonType().contains(reasonMap.get(reason))) {
                        reasonTypes.add(reason);
                    }
                }
                if (!CollectionUtils.isEmpty(reasonTypes)) {
                    defaultWhere.addAnd("reason in (?)", reasonTypes);
                    lastWhere.addAnd("reason in (?)", reasonTypes);
                }
            }
            if (!isCOMD) {
                Set<String> planProductSet = productDemandAuthService.listMyPlanProduct(username,
                        ProductDemandAuthRoleStatusEnum.listAll());
                if (CollectionUtils.isEmpty(planProductSet)) {
                    // 无权限的话强制关联一个false的sql语句
                    defaultWhere.addAnd("1 = 0");
                    lastWhere.addAnd("1 = 0");
                }
                if (CollectionUtils.isEmpty(req.getPlanProduct())) {
                    req.setPlanProduct(new LinkedList<>(planProductSet));
                } else {
                    req.getPlanProduct().removeIf(p -> !planProductSet.contains(p));
                    if (CollectionUtils.isEmpty(req.getPlanProduct())) {
                        // 所选的规划产品都无权限的话强制关联一个false的sql语句
                        defaultWhere.addAnd("1 = 0");
                        lastWhere.addAnd("1 = 0");
                    }
                }
            }
            if (!CollectionUtils.isEmpty(req.getPlanProduct())) {
                defaultWhere.addAnd("plan_product in (?)", req.getPlanProduct());
                lastWhere.addAnd("plan_product in (?)", req.getPlanProduct());
            }

            versionDOS = demandDBHelper.getAll(ProductDemandVersionDO.class,
                    "order by id desc");

            // 过滤掉驳回的，把本版本驳回的流程干掉
            List<ProductDemandVersionGroupDO> reject = demandDBHelper.getAll(ProductDemandVersionGroupDO.class,
                    "where demand_version = ? and status = ?",
                    version, ProductDemandGroupStatusEnum.REJECT.getCode());
            if (!CollectionUtils.isEmpty(reject)) {
                defaultWhere.addAnd("demand_group_id not in (?)",
                        reject.stream().map(BaseDO::getId).collect(Collectors.toSet()));
            }

            defaultWhere.addAnd("demand_version = ?", version);

            itemDOS = demandDBHelper.getAll(ProductDemandVersionItemDO.class,
                    defaultWhere.getSql(), defaultWhere.getParams());

        } else if (groupId != null) {
            ProductDemandVersionGroupWithVersionVO groupDO = demandDBHelper.getOne(
                    ProductDemandVersionGroupWithVersionVO.class,
                    "where id = ?", groupId);
            // 权限控制
            if (!hasThisPlanProductOrCOMDAuth(username, groupDO.getPlanProduct(), true)) {
                throw new BizException("您没有权限查看[" + groupDO.getPlanProduct() + "]的规划产品信息");
            }
            versionDOS = demandDBHelper.getAll(ProductDemandVersionDO.class,
                    "order by id desc", groupDO.getDemandVersion());

            defaultWhere.addAnd("demand_group_id = ?", groupId);

            itemDOS = demandDBHelper.getAll(ProductDemandVersionItemDO.class,
                    defaultWhere.getSql(), defaultWhere.getParams());
            version = groupDO.getDemandVersion();

            lastWhere.addAnd("plan_product = ?", groupDO.getPlanProduct());
        } else {
            throw new BizException("版本号或分组ID必须提供其中一个参数");
        }

        Map<String, ServerPartsExtendedInfoDO> finalCoreMap = coreMap;
        Map<String, SummaryStatisticItem> defaultResult = itemDOS.stream()
                .reduce(new HashMap<>(), (m, i) -> {
                    String key = fn.apply(i);
                    SummaryStatisticItem item = m.get(key);
                    long demandNum = i.getDemandNum();
                    ServerPartsExtendedInfoDO infoDO = finalCoreMap.get(i.getDeviceTypeName());
                    if (infoDO == null) {
                        throw new BizException(String.format("机型【%s】核心数配置缺失", i.getDeviceTypeName()));
                    }
                    long coreNum = demandNum * infoDO.getCpuLogicCore();
                    if (item == null) {
                        item = new SummaryStatisticItem(key, 0L, demandNum, demandNum, 0L, coreNum, coreNum);
                        m.put(key, item);
                    } else {
                        item.setValue(item.getValue() + demandNum);
                        item.setDiff(item.getDiff() + demandNum);
                        item.setCoreValue(item.getCoreValue() + coreNum);
                        item.setCoreDiff(item.getCoreDiff() + coreNum);
                    }
                    return m;
                }, (m1, m2) -> m1);
        if (CollectionUtils.isEmpty(versionDOS)) {
            Collection<SummaryStatisticItem> result = defaultResult.values();
            result.forEach(e -> {
                e.setPrev(e.getValue());
                e.setDiff(0);
                e.setCorePrev(e.getCoreValue());
                e.setCoreDiff(0);
            });
            // 除了本版本没有任何版本，那就返回本期的情况
            return MapUtils.of("data", defaultResult.values(),
                    "lastVersion", version);
        }

        String lastVersion = req.getLastVersion();
        if (lastVersion != null) {
            // 不能与本版本的版本号相同，比较起来没有意义(prev设置和value一样，diff设置为0）
            if (lastVersion.equals(version)) {
                Collection<SummaryStatisticItem> result = defaultResult.values();
                result.forEach(e -> {
                    e.setPrev(e.getValue());
                    e.setDiff(0);
                    e.setCorePrev(e.getCoreValue());
                    e.setCoreDiff(0);
                });
                return MapUtils.of("data", defaultResult.values(),
                        "lastVersion", lastVersion);
            } else if (!versionDOS.stream().map(ProductDemandVersionDO::getDemandVersion).collect(Collectors.toSet())
                    .contains(lastVersion)) {
                // 看是否在版本列表里，不在的话抛异常
                throw new BizException("未知的版本号参与对比");
            }
        } else {
            // 不传版本号的情况默认给个最近的版本号
            lastVersion = versionDOS.get(0).getDemandVersion();
        }

        // 过滤掉驳回的，把本版本驳回的流程干掉
        List<ProductDemandVersionGroupDO> reject = demandDBHelper.getAll(ProductDemandVersionGroupDO.class,
                "where demand_version = ? and status = ?",
                lastVersion, ProductDemandGroupStatusEnum.REJECT.getCode());
        if (!CollectionUtils.isEmpty(reject)) {
            lastWhere.addAnd("demand_group_id not in (?)",
                    reject.stream().map(BaseDO::getId).collect(Collectors.toSet()));
        }
        lastWhere.addAnd("demand_version = ?", lastVersion);
        List<ProductDemandVersionItemDO> lastItemDOS = demandDBHelper.getAll(ProductDemandVersionItemDO.class,
                lastWhere.getSql(), lastWhere.getParams());
        Map<String, SummaryStatisticItem> lastResult = lastItemDOS.stream()
                .reduce(new HashMap<>(), (m, i) -> {
                    String key = fn.apply(i);
                    SummaryStatisticItem item = m.get(key);
                    long demandNum = i.getDemandNum();
                    ServerPartsExtendedInfoDO infoDO = finalCoreMap.get(i.getDeviceTypeName());
                    if (infoDO == null) {
                        throw new BizException(String.format("机型【%s】核心数配置缺失", i.getDeviceTypeName()));
                    }
                    long coreNum = demandNum * infoDO.getCpuLogicCore();
                    if (item == null) {
                        item = new SummaryStatisticItem(key, demandNum, 0, -demandNum, coreNum, 0, -coreNum);
                        m.put(key, item);
                    } else {
                        item.setPrev(item.getPrev() + demandNum);
                        item.setDiff(item.getDiff() - demandNum);
                        item.setCorePrev(item.getCorePrev() + coreNum);
                        item.setCoreDiff(item.getCoreDiff() - coreNum);
                    }
                    return m;
                }, (m1, m2) -> m1);
        // 合并两份result
        Set<String> keys = new HashSet<>(defaultResult.keySet());
        keys.addAll(lastResult.keySet());
        List<SummaryStatisticItem> finalResult = new LinkedList<>();
        for (String key : keys) {
            SummaryStatisticItem lastItem = lastResult.get(key);
            SummaryStatisticItem defaultItem = defaultResult.get(key);
            SummaryStatisticItem finalItem = new SummaryStatisticItem(key);
            if (lastItem != null) {
                finalItem.setPrev(lastItem.getPrev());
                finalItem.setDiff(lastItem.getDiff());
                finalItem.setCorePrev(lastItem.getCorePrev());
                finalItem.setCoreDiff(lastItem.getCoreDiff());
            }
            if (defaultItem != null) {
                finalItem.setValue(defaultItem.getValue());
                finalItem.setDiff(finalItem.getDiff() + defaultItem.getDiff());
                finalItem.setCoreValue(defaultItem.getCoreValue());
                finalItem.setCoreDiff(finalItem.getCoreDiff() + defaultItem.getCoreDiff());
            }
            finalResult.add(finalItem);
        }
        return MapUtils.of("data", finalResult, "lastVersion", lastVersion);
    }

    @Override
    public List<Map<String, Object>> listVersionDemandItems(SummaryStatisticReq req) {
        Map<String, ServerPartsExtendedInfoDO> coreMap = productDemandDictService.getAllServerConfigInfo();
        String version = req.getVersion();
        if (StringUtils.isBlank(version)) {
            return ImmutableList.of();
        }
        WhereContent whereContent = new WhereContent();
        whereContent.addAnd("demand_version = ?", version);
        if (!CollectionUtils.isEmpty(req.getIndustryName())) {
            whereContent.addAnd("industry_name in (?)", req.getIndustryName());
        }
        if (!CollectionUtils.isEmpty(req.getCustomerName())) {
            whereContent.addAnd("customer_name in (?)", req.getCustomerName());
        }
        if (!CollectionUtils.isEmpty(req.getZoneName())) {
            whereContent.addAnd("zone_name in (?)", req.getZoneName());
        }
        if (!CollectionUtils.isEmpty(req.getDeviceTypeName())) {
            whereContent.addAnd("device_type_name in (?)", req.getDeviceTypeName());
        }
        if (!CollectionUtils.isEmpty(req.getReason())) {
            whereContent.addAnd("reason in (?)", req.getReason());
        }
        if (!CollectionUtils.isEmpty(req.getReasonType())) {
            Map<String, String> reasonMap = productDemandDictService.listAllReasonMap();
            List<String> reasonTypes = new ArrayList<>();
            for (String reason : reasonMap.keySet()) {
                if (req.getReasonType().contains(reasonMap.get(reason))) {
                    reasonTypes.add(reason);
                }
            }
            if (!CollectionUtils.isEmpty(reasonTypes)) {
                whereContent.addAnd("reason in (?)", reasonTypes);
            }
        }
        // 过滤掉只有自己规划产品权限的
        String username = LoginUtils.getUserName();
        Boolean isCOMD = productDemandAuthService.hasCOMDRole(username);
        if (!isCOMD) {
            Set<String> planProductSet = productDemandAuthService.listMyPlanProduct(username,
                    ProductDemandAuthRoleStatusEnum.listAll());
            if (CollectionUtils.isEmpty(planProductSet)) {
                // 无权限的话强制关联一个false的sql语句
                whereContent.addAnd("1 = 0");
            }
            if (CollectionUtils.isEmpty(req.getPlanProduct())) {
                req.setPlanProduct(new LinkedList<>(planProductSet));
            } else {
                req.getPlanProduct().removeIf(p -> !planProductSet.contains(p));
                if (CollectionUtils.isEmpty(req.getPlanProduct())) {
                    // 所选的规划产品都无权限的话强制关联一个false的sql语句
                    whereContent.addAnd("1 = 0");
                }
            }
        }
        if (!CollectionUtils.isEmpty(req.getPlanProduct())) {
            whereContent.addAnd("plan_product in (?)", req.getPlanProduct());
        }
        // 过滤掉驳回的，把本版本驳回的流程干掉
        List<ProductDemandVersionGroupDO> reject = demandDBHelper.getAll(ProductDemandVersionGroupDO.class,
                "where demand_version = ? and status = ?",
                version, ProductDemandGroupStatusEnum.REJECT.getCode());
        if (!CollectionUtils.isEmpty(reject)) {
            whereContent.addAnd("demand_group_id not in (?)",
                    reject.stream().map(BaseDO::getId).collect(Collectors.toSet()));
        }
        List<ProductDemandVersionItemDO> itemDOS = demandDBHelper.getAll(ProductDemandVersionItemDO.class,
                whereContent.getSql(), whereContent.getParams());
        if (CollectionUtils.isEmpty(itemDOS)) {
            return ImmutableList.of();
        }
        Map<String, List<ProductDemandVersionItemDO>> groupMap = ListUtils.toMapList(itemDOS,
                e -> String.join("@", e.getRemark(), e.getPlanProduct(), e.getWithoutRowIdPreMapKey()),
                Function.identity());
        Map<String, DemandNumAndCoreNumDTO> totalMap = new HashMap<>();
        Map<String, Map<String, Object>> resultMap = new HashMap<>();
        groupMap.forEach((k, v) -> {
            for (ProductDemandVersionItemDO p : v) {
                Map<String, Object> origin = resultMap.get(k);
                if (origin == null) {
                    origin = new HashMap<>();
                    origin.put("planProduct", p.getPlanProduct());
                    origin.put("obsProjectType", p.getObsProjectType());
                    origin.put("moduleBusinessTypeName", p.getModuleBusinessTypeName());
                    origin.put("reason", p.getReason());
                    origin.put("industryName", p.getIndustryName());
                    origin.put("customerName", p.getCustomerName());
                    origin.put("regionName", p.getRegionName());
                    origin.put("zoneName", p.getZoneName());
                    origin.put("campusName", p.getCampusName());
                    origin.put("deviceTypeName", p.getDeviceTypeName());
                    origin.put("remark", p.getRemark());
                    resultMap.put(k, origin);
                }
                String ym = p.getDemandYear() + "-" + (p.getDemandMonth() < 10 ? "0" + p.getDemandMonth()
                        : p.getDemandMonth());
                DemandNumAndCoreNumDTO numAndCoreNumDTO = (DemandNumAndCoreNumDTO) origin.getOrDefault(ym, new DemandNumAndCoreNumDTO());
                ServerPartsExtendedInfoDO infoDO = coreMap.get(p.getDeviceTypeName());
                if (infoDO == null) {
                    throw new BizException(String.format("机型【%s】核心数配置缺失", p.getDeviceTypeName()));
                }
                numAndCoreNumDTO.add(p.getDemandNum(), p.getDemandNum() * infoDO.getCpuLogicCore());
                origin.put(ym, numAndCoreNumDTO);

                DemandNumAndCoreNumDTO originTotal = totalMap.getOrDefault(ym, new DemandNumAndCoreNumDTO());
                originTotal.add(p.getDemandNum(), p.getDemandNum() * infoDO.getCpuLogicCore());
                totalMap.put(ym, originTotal);
            }
        });
        // 算总计，并排序
        List<Map<String, Object>> rawResult = new ArrayList<>(resultMap.values());
        ListUtils.sortAscNullLast(rawResult,
                e -> (String) e.get("planProduct"),
                e -> (String) e.get("obsProjectType"),
                e -> (String) e.get("moduleBusinessTypeName"),
                e -> (String) e.get("industryName"),
                e -> (String) e.get("customerName"),
                e -> (String) e.get("regionName"),
                e -> (String) e.get("zoneName"),
                e -> (String) e.get("campusName"),
                e -> (String) e.get("deviceTypeName"),
                e -> (String) e.get("reason"),
                e -> (String) e.get("remark"));
        Map<String, Object> total = MapUtils.of(
                "planProduct", "合计",
                "obsProjectType", "",
                "moduleBusinessTypeName", "",
                "reason", "",
                "industryName", "",
                "customerName", "",
                "regionName", "",
                "zoneName", "",
                "campusName", "",
                "deviceTypeName", "",
                "remark", ""
        );
        total.putAll(totalMap);
        rawResult.add(total);
        Collections.reverse(rawResult);
        return rawResult;
    }

    @Override
    public List<String> listComparableVersionList() {
        List<ProductDemandVersionDO> versionDOS = demandDBHelper.getAll(ProductDemandVersionDO.class,
                "order by id desc");
        List<String> result = new ArrayList<>();
        versionDOS.forEach(e -> {
            result.add(e.getDemandVersion());
        });
        return result;
    }

    @Override
    public void forceRollBackDemandGroup(ProductDemandVersionGroupDO versionGroupDO, String approveMemo, String username) {
        int i = demandDBHelper.executeRaw("update product_demand_version_group set status = ? where id = ?"
                , ProductDemandGroupStatusEnum.SYSTEM_INIT.getCode(), versionGroupDO.getId());
        if (i == 1) {
            ProductDemandApproveRecordDO logDO = new ProductDemandApproveRecordDO();
            logDO.setDemandGroupId(versionGroupDO.getId());
            logDO.setStatus(versionGroupDO.getStatus());
            logDO.setApprover(username);
            logDO.setApproveMsg(approveMemo);
            logDO.setAction("强制打回");
            demandDBHelper.insert(logDO);
        }
    }

    private List<Integer[]> getYearMonths(Integer begYear,
                                          Integer begMonth,
                                          Integer endYear,
                                          Integer endMonth){
        List<Integer[]> yearMonthList = new ArrayList<>();
        // 从开始年月循环到结束年月
        int currentYear = begYear;
        int currentMonth = begMonth;
        while (currentYear < endYear || (currentYear == endYear && currentMonth <= endMonth)) {
            yearMonthList.add(new Integer[]{currentYear, currentMonth});
            currentMonth++;
            if (currentMonth > 12) {
                currentMonth = 1;
                currentYear++;
            }
        }
        return yearMonthList;
    }

    @Override
    public Object extendsBudget(ExtendsBudgetDTO req) {
        // 判断版本是否存在，且是否开放
        ProductDemandVersionDO version = productDemandVersionService.getVersion(req.getVersion());
        if (version == null || !ProductDemandVersionStatusEnum.ENABLED.getCode().equals(version.getStatus())) {
            throw new BizException("所属版本不存在，或者版本已经关闭");
        }

        // 拉取预算数据
        String[] beginYearMonth = req.getBeginYearMonth().split("-");
        String[] endYearMonth = req.getEndYearMonth().split("-");

        // 拉通时间必须属于版本时间窗口
        Integer beginYear = Math.max(Integer.parseInt(beginYearMonth[0]), version.getForecastFromYear());
        Integer beginMonth = Math.max(Integer.parseInt(beginYearMonth[1]), version.getForecastFromMonth());
        Integer endYear = Math.min(Integer.parseInt(endYearMonth[0]), version.getForecastToYear());
        Integer endMonth = Math.min(Integer.parseInt(endYearMonth[1]), version.getForecastToMonth());

        List<Integer[]> yms = getYearMonths(beginYear, beginMonth, endYear, endMonth);

        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and(" plan_product_name = ? ", req.getPlanProduct());
        whereSQL.and(" budget_type = ? ", "滚动预算");
        whereSQL.and(" bg_type_name = ? ", "云业务");
        whereSQL.and(" data_type_name = ? ", "申领量");
        whereSQL.and(" (year, month) in (?) ", yms);
        List<ObsBudgetDeviceRollDataDO> budgetDos = backupDBHelper.getAll(ObsBudgetDeviceRollDataDO.class, whereSQL.getSQL(), whereSQL.getParams());

        // 拉取erp的city2zone的映射
        Map<String, String> erpCity2Zone = dictService.getErpCity2Zone();
        Map<String, String> erpZone2Region = dictService.getErpZone2Region();

        // 转换成预测数据
        List<ProductDemandVersionItemDO> itemDOS = new ArrayList<>();
        for (ObsBudgetDeviceRollDataDO budgetDo : budgetDos) {
            if (budgetDo.getDeviceAmount() <= 0) {
                continue;
            }
            ProductDemandVersionItemDO itemDO = ProductDemandVersionItemDO.transFromBudget(budgetDo);
            itemDO.setZoneName(erpCity2Zone.getOrDefault(budgetDo.getCityName(), budgetDo.getCityName()));
            itemDO.setRegionName(erpZone2Region.getOrDefault(itemDO.getZoneName(), ""));
            itemDOS.add(itemDO);
        }

        // 加载系统内的预测数据（加载没在预算拉通范围的）
        List<ProductDemandVersionItemDO> oldDemand = demandDBHelper.getAll(ProductDemandVersionItemDO.class,
                " where demand_version = ?" +
                        "  and plan_product = ?" +
                        "  and (demand_year, demand_month) not in (?) and demand_num>0"
                , req.getVersion(), req.getPlanProduct(), yms);

        if (ListUtils.isNotEmpty(oldDemand)) {
            itemDOS.addAll(oldDemand);
        }

        return buildResp(itemDOS, version, req.getPlanProduct());
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SummaryStatisticItem {

        private String title = "";
        private long prev = 0L;
        private long value = 0L;
        private long diff = 0L;

        private long corePrev = 0L;
        private long coreValue = 0L;
        private long coreDiff = 0L;

        public SummaryStatisticItem(String title) {
            this.title = title;
        }

        public static SummaryStatisticItem copy(SummaryStatisticItem other) {
            SummaryStatisticItem summaryStatisticItem = new SummaryStatisticItem();
            summaryStatisticItem.setTitle(other.getTitle());
            summaryStatisticItem.setPrev(other.getPrev());
            summaryStatisticItem.setValue(other.getValue());
            summaryStatisticItem.setDiff(other.getDiff());
            summaryStatisticItem.setCorePrev(other.getCorePrev());
            summaryStatisticItem.setCoreValue(other.getCoreValue());
            summaryStatisticItem.setCoreDiff(other.getCoreDiff());
            return summaryStatisticItem;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DataWrapper {

        String v;

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            DataWrapper that = (DataWrapper) o;
            return Objects.equals(v, that.v);
        }

        @Override
        public int hashCode() {
            return Objects.hash(v);
        }
    }
}

