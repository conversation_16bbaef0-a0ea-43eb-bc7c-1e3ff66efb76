package cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class WithSqlBuilder {
    private final List<String> withClauses;
    private final List<Object> params;

    public WithSqlBuilder() {
        withClauses = new ArrayList<>();
        params = new ArrayList<>();
    }

    public static WithSqlBuilder with(String alias, String subquery, Object... parameters) {
        WithSqlBuilder builder = new WithSqlBuilder();
        builder.withClauses.add(alias + " AS (" + subquery + ")");
        builder.params.addAll(Arrays.asList(parameters));
        return  builder;
    }

    public WithSqlBuilder add(String alias, String withSql, Object... parameters) {
        withClauses.add(alias + " AS (" + withSql + ")");
        params.addAll(Arrays.asList(parameters));
        return this;
    }

    public String getSql() {
        return "WITH " + String.join(", ", withClauses);
    }

    public Object[] getParams() {
        return params.toArray();
    }
}