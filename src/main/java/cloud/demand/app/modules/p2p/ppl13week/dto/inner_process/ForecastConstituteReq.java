package cloud.demand.app.modules.p2p.ppl13week.dto.inner_process;

import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.orm.SingleWhere;
import cloud.demand.app.common.utils.orm.WhereType;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ForecastConstituteResp.ForecastConstituteDetail;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ForecastConstituteResp.ForecastConstituteGroupItem;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.Getter;
import org.nutz.lang.Strings;
import yunti.boot.exception.BizException;

@Data
public class ForecastConstituteReq {

    public static final String INDUSTRY_DEPT_ALL_WAR_ZONE = "全行业";

    private Long versionId;

    private List<String> bizRange;

    /**
     * {@link #bizRange} 的纬度是是否为客户简称，true表示是客户简称，false表示是战区
     */
    private boolean bizRangeIsCustomer;

    @SingleWhere(type = WhereType.IN, columnName = "ppl_item.demand_type")
    private List<String> demandType;

    @SingleWhere(type = WhereType.IN, columnName = "ppl_item.product")
    private List<String> product;

    @SingleWhere(type = WhereType.EQUALS, columnName = "ppl_item.gpu_type")
    private String gpuType;

    @SingleWhere(type = WhereType.EQUALS, columnName = "ppl_item.instance_type")
    private String instanceType;

    @SingleWhere(type = WhereType.EQUALS, columnName = "ppl_item.region_name")
    private String regionName;

    @SingleWhere(type = WhereType.EQUALS, columnName = "ppl_item.zone_name")
    private String zoneName;

    @SingleWhere(type = WhereType.EQUALS, columnName = "ppl_order.war_zone")
    private String warZone;

    @SingleWhere(type = WhereType.EQUALS, columnName = "ppl_order.customer_short_name")
    private String customerShortName;

    @SingleWhere(type = WhereType.EQUALS,
            columnName = "DATE_FORMAT(ppl_item.begin_buy_date, '%Y-%m')")
    private String demandYearMonth;

    @SingleWhere(type = WhereType.EQUALS, columnName = "ppl_item.database_name")
    private String databaseName;

    @SingleWhere(type = WhereType.EQUALS, columnName = "ppl_item.cos_storage_type")
    private String cosStorageType;

    /**
     * 境内外
     */
    private String customhouseTitle;

    private GroupCondition groupBy;

    /**
     *  指标名称 <br/>
     *  数据库可选 total_memory， total_core,  total_database_storage, 默认total_database_storage <br/>
     *  COS可选 total_cos_storage，默认total_cos_storage <br/>
     *  GPU可选 total_core, total_gpu_num，默认total_gpu_num <br/>
     *  CVM、PASS产品可选 total_core, total_memory，默认total_core <br/>
     */
    private String indexName;

    /**
     * 是否需求沟通阶段
     */
    private boolean preSubmit;

    private String industryDept;

    public void indexNameHandler() {
        if (Strings.isNotBlank(this.indexName)) {
            List<String> indexNameList = ListUtils.newArrayList("total_cos_storage", "total_database_storage",
                    "total_memory", "total_core", "total_gpu_num");
            if (!indexNameList.contains(this.indexName)) {
                throw BizException.makeThrow("查询的指标名称不合法");
            }
            return;
        }
        String productName = ListUtils.isNotEmpty(this.product) ? this.product.get(0) : null;
        if (Ppl13weekProductTypeEnum.COS.getName().equals(productName)) {
            this.indexName = "total_cos_storage";
        } else if (Ppl13weekProductTypeEnum.DATABASE.getName().equals(productName)) {
            this.indexName = "total_database_storage";
        } else if (Ppl13weekProductTypeEnum.GPU.getName().equals(productName)) {
            this.indexName = "total_gpu_num";
        } else {
            this.indexName = "total_core";
        }
    }

    public WhereContent whereContent() {
        return whereContent(versionId);
    }

    public WhereContent whereContent(Long versionIdParam) {
        WhereContent where = new WhereContent()
                .addAndAllSingleWhereAnnotationField(this)
                .andIfValueNotEmpty("ppl_order.industry_dept = ?", this.industryDept)
                .addAnd(" ppl_item.deleted = 0 and ppl_order.deleted = 0 ")
                .addAnd(" ppl_item.audit_record_id in ( "
                        + "     select max(id) as id from ppl_order_audit_record "
                        + "     where deleted = 0 and version_id = ? group by version_id,ppl_order "
                        + " ) ", versionIdParam);
        bizRangeHandler(where);
        return where;
    }

    public WhereContent whereContentForPreSubmit() {
        WhereContent where = new WhereContent()
                .addAndAllSingleWhereAnnotationField(this)
                .andIfValueNotEmpty("ppl_order.industry_dept = ?", this.industryDept)
                .addAnd(" ppl_item.deleted = 0 and ppl_order.deleted = 0 "
                        + " and ppl_item.draft_status = 'PRE_SUBMIT' "
                        + " and ppl_order.draft_status = 'PRE_SUBMIT' "); // 查询草稿单预提交
        bizRangeHandler(where);
        return where;
    }

    private void bizRangeHandler(WhereContent where) {
        if (ListUtils.isNotEmpty(this.bizRange)) {
            List<String> rangeList = new ArrayList<>();
            for (String s : this.bizRange) {
                if (s.equals(INDUSTRY_DEPT_ALL_WAR_ZONE)) {
                    rangeList = null;
                    break;
                }
                rangeList.add(s);
            }
            if (bizRangeIsCustomer) {
                where.andInIfValueNotEmpty("ppl_order.customer_short_name", rangeList);
            } else {
                where.andInIfValueNotEmpty("ppl_order.war_zone", rangeList);
            }
        }
    }

    @Getter
    public enum GroupCondition {

        gpuType(item -> item.getGpuType() == null ? "" : item.getGpuType()),

        instanceType(item -> item.getInstanceType() == null ? "" : item.getInstanceType()),

        regionName(item -> item.getRegionName() == null ? "" : item.getRegionName()),

        zoneName(item -> item.getZoneName() == null ? "" : item.getZoneName()),

        warZone(item -> item.getWarZone() == null ? "" : item.getWarZone()),

        customerShortName(item -> item.getCustomerShortName() == null ? "" : item.getCustomerShortName()),

        demandYearMonth(item -> item.getDemandYearMonth() == null ? "" : item.getDemandYearMonth()),

        customhouseTitle(item -> item.getCustomhouseTitle() == null ? "" : item.getCustomhouseTitle()),

        databaseName(item -> item.getDatabaseName() == null ? "" : item.getDatabaseName()),

        cosStorageType(item -> item.getCosStorageType() == null ? "" : item.getCosStorageType());

        private GroupCondition(Function<ForecastConstituteDetail, String> groupBy) {
            this.groupBy = groupBy;
        }

        private final Function<ForecastConstituteDetail, String> groupBy;
    }

    /**
     * 对输入的 {@link ForecastConstituteDetail} 数据按分组条件 {@link #getGroupBy()}
     * 分组计算 {@link ForecastConstituteDetail#getTotal()} 的sum值
     *
     * @return 分组计算之后的结果
     */
    public ForecastConstituteResp group(List<ForecastConstituteDetail> details) {
        ForecastConstituteResp result = new ForecastConstituteResp();
        result.setGroupCondition(this.groupBy.name());
        if (ListUtils.isEmpty(details)) {
            result.setTotal(BigDecimal.ZERO);
            result.setItems(new ArrayList<>());
            return result;
        }

        BigDecimal total = BigDecimal.ZERO;
        for (ForecastConstituteDetail detail : details) {
            if (detail == null) {
                continue;
            }
            total = total.add(detail.getTotal() == null ? BigDecimal.ZERO : detail.getTotal());
        }
        result.setTotal(total);

        BigDecimal totalValue = total;
        List<ForecastConstituteGroupItem> groupRes = details.stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(this.groupBy.getGroupBy(),
                        Collectors.summarizingDouble(
                                value -> value.getTotal() == null ? 0d : value.getTotal().doubleValue())))
                .entrySet().stream()
                .map(entry -> new ForecastConstituteGroupItem(entry.getKey(),
                        BigDecimal.valueOf(entry.getValue().getSum()), totalValue))
                .collect(Collectors.toList());
        result.setItems(groupRes);
        return result;
    }

}
