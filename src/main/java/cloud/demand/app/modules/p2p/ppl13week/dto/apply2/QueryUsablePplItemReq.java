package cloud.demand.app.modules.p2p.ppl13week.dto.apply2;

import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderTypeEnum;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import yunti.boot.exception.BizException;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;

/**
 * 查询可用的PPL item，这里的请求参数一般都以云霄预约单页面的请求参数为准。
 * 主key：产品、UIN、需求月份、可用区（上海三区）、实例类型(S5)、需求类型（新增/弹性）
 */
@Data
public class QueryUsablePplItemReq {

    /**云霄提交页面的【应用角色】*/
    @NotBlank(message = "应用角色必须提供")
    private String appRole;
    /**云霄提交页面的【预约单分类】*/
    @NotBlank(message = "预约单分类必须提供")
    private String orderCategory;

    /**客户uin*/
    @NotBlank(message = "客户uin必须提供")
    private String uin;
    /**需求开始时间，这里要用的是年月*/
    @NotBlank(message = "需求开始时间必须提供")
    private String expectTime;

    /**可用区(中文名称，例如广州三区)*/
    @NotBlank(message = "可用区必须提供")
    private String zoneName;

    /**实例大类(S5)*/
    @NotBlank(message = "实例大类必须提供")
    private String instanceFamily;

    /**订单类型(需求类型)，普通Normal/弹性Elastic */
    @NotBlank(message = "订单类型(需求类型)必须提供")
    private String orderType;

    /**这个where条件只适用于ppl_item表
     * @param regionName 需要调用者再提供地域名称，以匹配地域下的随机可用区需求
     * */
    public WhereSQL toWhereSQL(String regionName) {
        WhereSQL whereSQL = new WhereSQL();
        // 现在限定只有CVM，后续放开这里再根据appRole和orderCategory来定
        whereSQL.and("product=?", Ppl13weekProductTypeEnum.CVM.getName());
        whereSQL.and("ppl_order in (select ppl_order from ppl_order where deleted=0 and customer_uin=?)", uin);

        // 需求日期需要同一月份
        Date expectDate = DateUtils.parse(expectTime);
        LocalDate firstDayOfMonth = getFirstDayOfMonth(expectDate);
        LocalDate lastDayOfMonth = getLastDayOfMonth(expectDate);
        whereSQL.and("begin_buy_date>=? and begin_buy_date<=?", firstDayOfMonth, lastDayOfMonth);

        WhereSQL zoneCondition = new WhereSQL();
        zoneCondition.or("zone_name=?", zoneName);
        if (StringTools.isNotBlank(regionName)) {
            zoneCondition.or("zone_name='随机可用区' and region_name=?", regionName);
        }
        whereSQL.and(zoneCondition);

        whereSQL.and("instance_type=?", instanceFamily);

        if (YunxiaoOrderTypeEnum.Normal.getCode().equals(orderType)) {
            whereSQL.and("demand_type=?", PplDemandTypeEnum.NEW.getCode());
        } else if (YunxiaoOrderTypeEnum.Elastic.getCode().equals(orderType)) {
            whereSQL.and("demand_type=?", PplDemandTypeEnum.ELASTIC.getCode());
        } else {
            throw new BizException("不支持的订单类型：" + orderType);
        }

        return whereSQL;
    }

    private static LocalDate getFirstDayOfMonth(Date date) {
        // 获得date所在月份的第一天的日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return DateUtils.toLocalDate(calendar.getTime());
    }

    private static LocalDate getLastDayOfMonth(Date date) {
            // 获得date所在月份的最后一天的日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return DateUtils.toLocalDate(calendar.getTime());
    }

}
