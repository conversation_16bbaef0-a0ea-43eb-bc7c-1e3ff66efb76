package cloud.demand.app.modules.p2p.ppl13week.enums;

import java.util.Objects;
import lombok.Getter;

/**
 * 推送数据记录状态枚举
 */
@Getter
public enum PplPushDataStatusEnum {

    SENDING("SENDING", "推送中"),

    FAILED("FAILED", "推送失败"),

    SUCCESS("SUCCESS", "推送成功");

    final private String code;
    final private String name;

    PplPushDataStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PplOrderStatusEnum getByCode(String code) {
        for (PplOrderStatusEnum e : PplOrderStatusEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        PplOrderStatusEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }
}
