package cloud.demand.app.modules.p2p.ppl13week.dto.version;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class PplItemStockSupplyExportCvmDTO {


    private String pplId;

    private String customerTypeName;

    private String customerUin;

    private String customerShortName;

    /**
     * 需求类型
     */
    private String demandTypeName;

    /**
     * 需求场景<br/>Column: [demand_scene]
     */
    private String demandScene;

    /**
     * 项目名称<br/>Column: [project_name]
     */
    private String projectName;

    /**
     * 计费模式<br/>Column: [bill_type]
     */
    private String billType;

    /**
     * 赢率，百分比<br/>Column: [win_rate]
     */
    private String winRate;


    /**
     * 开始购买日期<br/>Column: [begin_buy_date]
     */
    private String beginBuyDate;

    /**
     * 结束购买日期<br/>Column: [end_buy_date]
     */
    private String endBuyDate;


    /**
     * 弹性开始日期<br/>Column: [begin_elastic_date]
     */
    private String beginElasticDate;

    /**
     * 弹性结束日期<br/>Column: [end_elastic_date]
     */
    private String endElasticDate;

    /**
     * 特殊备注说明<br/>Column: [note]
     */
    private String note;

    /**
     * 地域，看看要不要存id<br/>Column: [region_name]
     */
    private String regionName;

    /**
     * 可用区，看看要不要存id<br/>Column: [zone_name]
     */
    private String zoneName;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    private String instanceType;


    /**
     * 实例规格<br/>Column: [instance_model]
     */
    private String instanceModel;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    private Integer instanceModelCpuCore;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    private Integer instanceModelRam;


    /**
     * 实例数量<br/>Column: [instance_num]
     */
    private Integer instanceNum;

    /**
     * 实例数量<br/>Column: [instance_num]
     */
    private Integer totalCoreNum;


    private String yearMonth;


    private String matchType;


    private String matchInstanceType;

    private String matchZone;

    private Integer matchCoreNum;

    private String hostType;
    private BigDecimal hostNum;

    private String stockRemark;

    private String industryDept;

    private String product;

    private String groupStatus;

    private String bizId;

    private String source;

}
