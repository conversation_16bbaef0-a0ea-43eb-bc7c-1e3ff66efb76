package cloud.demand.app.modules.p2p.ppl13week.dto.order.req;

import cloud.demand.app.web.model.common.Page;
import com.pugwoo.dbhelper.annotation.Column;
import java.util.List;
import lombok.Data;

@Data
public class QueryPplListReq {

    Page page;

    /** PPL单号(考虑单号可能有英文，目前可以与id保持一致)<br/>Column: [ppl_order] */
    private List<String> pplOrder;

    /** 来源,行业录入,自动创建<br/>Column: [source] */
    private List<String> source;

    private List<String> yearMonth;

    /** 行业<br/>Column: [industry] */
    private List<String> industry;

    private List<String> industryDept;

    /** 客户类型,存量客户,winback客户<br/>Column: [customer_type] */
    private List<String>  customerType;

    /** 客户名称<br/>Column: [customer_name] */
    private List<String> customerName;

    /** 客户uin<br/>Column: [customer_uin] */
    private List<String> customerUin;

    /** 客户简称<br/>Column: [customer_name] */
    @Column(value = "customer_short_name")
    private List<String> customerShortName;

    /** 战区<br/>Column: [customer_name] */
    private List<String> warZone;

    /**
     * ppl单状态<br/>Column: [status]
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderStatusEnum
     */
    private String status;

    /** 提单人<br/>Column: [submit_user] */
    private List<String> submitUser;

    private String createTimeStart;
    private String createTimeEnd;



}
