package cloud.demand.app.modules.p2p.ppl13week.dto.order.req;

import com.sun.istack.NotNull;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class QueryApproveNoteReq {

    private List<String> nodeCodeList;
    @NotBlank
    private String industryDept;

    @NotNull
    private Long versionId;

    private List<String> warZone;

    private String product;

    private List<String> customerShortName;
}
