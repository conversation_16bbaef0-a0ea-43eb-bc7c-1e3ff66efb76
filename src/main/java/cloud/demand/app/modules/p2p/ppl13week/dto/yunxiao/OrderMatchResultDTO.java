package cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao;

import cloud.demand.app.modules.tencent_cloud_utils.dto.yunxiao.BaseDTO;
import java.util.List;
import lombok.Data;

@Data
public class OrderMatchResultDTO extends BaseDTO {

    private MatchData data;

    @lombok.Data
    public static class MatchData {

        Integer currentReservedCount;
        Integer currentReservedCpuCount;
        Double matchRate;
        List<ResultItem> items;
        Integer totalApplyCount;
        Integer totalApplyCpuCount;
        Integer totalReservedCount;
        Integer totalReservedCpuCount;
    }

    @lombok.Data
    public static class ResultItem {

        Integer count;
        Integer cpu;
        Integer cpuCount;
        String deviceClass;
        Integer memory;
        String reservedInstanceType;
        String reservedZone;
        Integer totalCount;
        Integer totalCpuCount;
    }
}

