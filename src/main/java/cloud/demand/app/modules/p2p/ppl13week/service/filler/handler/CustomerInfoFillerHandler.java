package cloud.demand.app.modules.p2p.ppl13week.service.filler.handler;

import cloud.demand.app.common.utils.BatchUtil;
import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.CustomerInfoFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerHandler;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CustomerInfoFillerHandler implements FillerHandler<CustomerInfoFiller> {

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Override
    public int getExecOrder() {
        return FillerHandler.CustomerInfoFillerHandlerOrder;
    }

    @Override
    public void fill(List<CustomerInfoFiller> result) {
        // 改为分批操作，提高效率(之前的做法查询dwd_txy_appid_info_cf全表数据量太大了)，降低内存消耗
        BatchUtil.syncBatchExec(result, 20000, this::fillOneBatch);
    }

    private void fillOneBatch(List<CustomerInfoFiller> oneBatchList) {
        Map<String, List<CustomerInfoFiller>> groupData = oneBatchList.stream()
                .collect(Collectors.groupingBy(CustomerInfoFiller::provideCustomerUin));
        String sql = "select toString(uin) as uin, cid, gname, war_zone, uin_type, customer_type, customer_short_name "
                + "from dwd_txy_appid_info_cf where uin in (?)";
        List<Map> infoList = ckcldStdCrpDBHelper.getRaw(Map.class, sql, groupData.keySet());

        if (ListUtils.isEmpty(infoList)) {
            return;
        }
        String cid; // 客户集团id
        String gName; // 客户集团
        String uin;
        String warZone; // 磐石战区
        Integer uinType;
        Integer customerType;
        String customerShortName;
        List<CustomerInfoFiller> cfDOList;
        for (Map item : infoList) {
            if (item == null || MapUtils.getString(item, "uin") == null) {
                continue;
            }
            uin = MapUtils.getString(item, "uin");
            cfDOList = groupData.get(uin);
            if (ListUtils.isEmpty(cfDOList)) {
                continue;
            }
            cid = StdUtils.handleStr(MapUtils.getString(item, "cid"));
            gName = StdUtils.handleStr(MapUtils.getString(item, "gname"));
            warZone = StdUtils.handleStr(MapUtils.getString(item, "war_zone"));
            uinType = MapUtils.getInteger(item, "uin_type");
            customerType = MapUtils.getInteger(item, "customer_type");
            customerShortName = StdUtils.handleStr(MapUtils.getString(item, "customer_short_name"));
            for (CustomerInfoFiller data : cfDOList) {
                data.fillCId(cid);
                data.fillCName(gName);
                data.fillPanShiWarZone(warZone);
                data.fillUinType(uinType);
                data.fillCustomerSocietyType(customerType);
                data.fillCustomerShortName(customerShortName);
            }
        }
    }
}
