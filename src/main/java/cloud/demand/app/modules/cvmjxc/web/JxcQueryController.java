package cloud.demand.app.modules.cvmjxc.web;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.modules.cvmjxc.service.AllProductSummaryService;
import cloud.demand.app.modules.cvmjxc.service.cvm.CalculateCVMIndicatorService;
import cloud.demand.app.modules.cvmjxc.web.model.QueryAllProductSummaryReq;
import cloud.demand.app.modules.cvmjxc.web.model.QueryAllProductSummaryResp;
import cloud.demand.app.modules.cvmjxc.web.model.QueryJxcDetailReq;
import cloud.demand.app.web.model.common.Page;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

/**
 * 进销存相关查询接口
 */
@JsonrpcController("/jxc")
@Slf4j
public class JxcQueryController {

    @Resource
    private AllProductSummaryService allProductSummaryService;

    @Resource
    private CalculateCVMIndicatorService calculateCVMIndicatorService;

    /**
     * 产品汇总
     */
    @RequestMapping
    public QueryAllProductSummaryResp queryAllProductSummary(@JsonrpcParam QueryAllProductSummaryReq req) {
        if (req == null) {
            throw new WrongWebParameterException("missing request body");
        }
        if ((StringTools.isBlank(req.getStartDate()) || DateUtils.parse(req.getStartDate()) == null)) {
            throw new WrongWebParameterException("parameter startDate is missing:" + req.getStartDate());
        }
        if ((StringTools.isBlank(req.getEndDate()) || DateUtils.parse(req.getEndDate()) == null)) {
            throw new WrongWebParameterException("parameter endDate is missing:" + req.getEndDate());
        }

        return allProductSummaryService.queryAllProductSummary(req);
    }

    /**
     * CVM进销存 转出明细查询
     */
    @RequestMapping
    public Object queryJxcErpTransferDetail(@JsonrpcParam QueryJxcDetailReq req) {
        String statTime = req.getStatTime();
        Page page = req.getPage();
        return calculateCVMIndicatorService.queryJxcErpTransferDetail(statTime, page, req.getProductType());
    }

    /**
     * CVM进销存 退回明细查询
     */
    @RequestMapping
    public Object queryJxcErpReturnDetail(@JsonrpcParam QueryJxcDetailReq req) {
        String statTime = req.getStatTime();
        Page page = req.getPage();
        return calculateCVMIndicatorService.queryJxcErpReturnDetail(statTime, page, req.getProductType());
    }

}
