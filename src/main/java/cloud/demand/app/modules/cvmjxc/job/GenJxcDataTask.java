package cloud.demand.app.modules.cvmjxc.job;

import cloud.demand.app.modules.cvmjxc.service.CalculateService;
import cloud.demand.app.modules.cvmjxc.service.cmongo.CalculateCMongoIndicatorService;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;

@Slf4j
@Service
public class GenJxcDataTask {

    @Autowired
    private CalculateService calculateService;
    @Resource
    private CalculateCMongoIndicatorService cMongoIndicatorService;

    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 40 8 * * ?")
    public void run() {
        log.info("begin GenJxcDataTask");
        String yesterday = DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
        calculateService.genJxcData(yesterday);
        log.info("end GenJxcDataTask");
    }

    /**
     * Cos部分数据每天9点前才能拿到完整数据，因此定时任务需要二次调用
     */
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 15 9 * * ?")
    public void run2() {
        log.info("begin GenJxcDataTask");
        String yesterday = DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
        calculateService.genJxcData(yesterday);
        log.info("end GenJxcDataTask");
    }


    /**
     * CMongo快照数据获取，他们的库中不会保留历史切片数据，且每6h刷新一次
     * 经过沟通，我们在计算每天的进销存时，10点取当天数据即可
     * 这里每天10点15分备份当天数据到我们星云库中，保证可以重跑历史数据
     */
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 15 10 * * ?")
    public String cmongoSnap() {
        cMongoIndicatorService.genCmongoBaseSnapshotData();
        return "done";
    }

    /**
     * CMongo部分数据需要等cmongoSnap()方法切片出当天的数据后才能生成进销存概览数据
     * 因此这里单独在10点半再跑一次当天的进销存
     */
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 30 10 * * ?")
    public void run3() {
        log.info("begin GenJxcDataTask");
        String yesterday = DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
        calculateService.genJxcData(yesterday);
        log.info("end GenJxcDataTask");
    }

    /**
     * Cos部分数据每天13点30多点才能拿到完整数据，因此定时任务需要再次调用
     * cos数据现在每天14点20才执行完成，因此定时任务改为14:35再次调用
     */
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 35 14 * * ?")
    public void run4() {
        log.info("begin GenJxcDataTask");
        String yesterday = DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
        calculateService.genJxcData(yesterday);
        log.info("end GenJxcDataTask");
    }
}
