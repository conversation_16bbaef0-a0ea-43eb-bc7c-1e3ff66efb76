package cloud.demand.app.modules.cvmjxc.web.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateAccUserData {
    Integer id;
    @JsonProperty("CVM")
    BigDecimal cvm;
    @JsonProperty("GPU")
    BigDecimal gpu;
    @JsonProperty("CBS")
    BigDecimal cbs;
    @JsonProperty("CDB")
    BigDecimal cdb;
    @JsonProperty("COS")
    BigDecimal cos;
    @JsonProperty("METAL")
    BigDecimal metal;
    @JsonProperty("NETWORK")
    BigDecimal network;

    public BigDecimal getCvm() {
        return cvm;
    }

    public BigDecimal getGpu() {
        return gpu;
    }

    public BigDecimal getCbs() {
        return cbs;
    }

    public BigDecimal getCdb() {
        return cdb;
    }

    public BigDecimal getMetal() {
        return metal;
    }

    public BigDecimal getNetwork() {
        return network;
    }
}
