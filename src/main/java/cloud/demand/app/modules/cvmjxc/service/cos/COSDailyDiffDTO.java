package cloud.demand.app.modules.cvmjxc.service.cos;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * cos的上架业务量/上架物理量的天差异
 */
@Data
public class COSDailyDiffDTO {

    @Column("region_name")
    private String regionName;

    /**
     * 值：已上架物理量 已上架业务量
     */
    @Column("indicator_name")
    private String indicatorName;

    @Column("diff")
    private BigDecimal diff;

}
