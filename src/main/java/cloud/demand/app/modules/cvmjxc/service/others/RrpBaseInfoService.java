package cloud.demand.app.modules.cvmjxc.service.others;

import cloud.demand.app.modules.cvmjxc.model.BusinessTypeDTO;

import java.util.List;

/**
 * 和rrp python相关的基础服务
 */
public interface RrpBaseInfoService {

    /**
     * 查询规划产品对应的自研业务类型(原始的）
     */
    List<BusinessTypeDTO> getRawPlanProductToBusiness();

    /**
     * 查询规划产品对应的自研业务类型
     */
    List<BusinessTypeDTO> getPlanProductToBusiness();

    /**
     * 生成非自研上云的where查询条件
     */
    String generateNotZysySQLCondition(List<BusinessTypeDTO> list);

    /**
     * 生成非自研上云的where查询条件,字段名字自选
     */
    String generateNotZysySQLCondition(String busi1, String busi2, String busi3, List<BusinessTypeDTO> list);

}
