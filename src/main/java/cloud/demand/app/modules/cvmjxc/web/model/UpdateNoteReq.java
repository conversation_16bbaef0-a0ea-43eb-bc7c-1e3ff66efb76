package cloud.demand.app.modules.cvmjxc.web.model;

import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class UpdateNoteReq {

    Integer id;
    @NotNull
    private ProductTypeEnum product;
    private String category;
    @NotEmpty
    private String indicatorName;
    private String note;
}
