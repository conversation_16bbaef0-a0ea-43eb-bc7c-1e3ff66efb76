package cloud.demand.app.modules.cvmjxc.model;

import cloud.demand.app.entity.rrp.ReportCvmJxcDO;
import cloud.demand.app.entity.rrp.ReportPurchaseOrderDetailDO;
import cloud.demand.app.modules.common.enums.IndicatorEnum;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;
import org.nutz.lang.Strings;

import java.math.BigDecimal;
import java.util.Objects;

@Data
public class ReportPurchaseOrderDetailSumVO extends ReportPurchaseOrderDetailDO {

    @Column(value = "num", computed = "count(*)")
    private BigDecimal num;

    @Column(value = "coreNum", computed = "sum(logic_core_num)")
    private BigDecimal coreNum;

    @Column(value = "saleCoreNum", computed = "sum(sale_core)")
    private BigDecimal saleCoreNum;

    @Column(value = "logicNum", computed = "sum(logic_num)")
    private BigDecimal logicNum;



    private void fillCommonFields(ReportCvmJxcDO d, String statTime){
        d.setStatTime(DateUtils.parse(statTime));
        d.setCoreNum(coreNum);
        d.setNum(num);
        // 扩展维度
        d.setCustomhouseTitle(getTxyCustomhouseTitle());
        d.setAreaName(getTxyAreaName());
        d.setRegionName(getTxyRegionName());
        d.setZoneName(getTxyZoneName());
        d.setDeviceType(getActualDeviceType());
        d.setCpuPlatform(getCpuPlatform());
    }

    public ReportCvmJxcDO toForCvm(IndicatorEnum indicator, String statTime) {
        ReportCvmJxcDO d = indicator.toReportCvmJxcDO();
        fillCommonFields(d, statTime);
        d.setLogicNum(saleCoreNum);
        d.setProductType(ProductTypeEnum.CVM.getCode());
        return d;
    }

    public ReportCvmJxcDO toForMetal(IndicatorEnum indicator, String statTime) {
        ReportCvmJxcDO d = indicator.toReportCvmJxcDO();
        fillCommonFields(d, statTime);
        d.setLogicNum(saleCoreNum);
        d.setProductType(ProductTypeEnum.METAL.getCode());
        return d;
    }

    public ReportCvmJxcDO toForGpu(IndicatorEnum indicator, String statTime) {
        ReportCvmJxcDO d = indicator.toReportCvmJxcDO();
        fillCommonFields(d, statTime);
        d.setLogicNum(logicNum);
        d.setProductType(ProductTypeEnum.GPU.getCode());
        fillServerCategory(d);
        return d;
    }

    public ReportCvmJxcDO toForCos(IndicatorEnum indicator, String statTime) {
        ReportCvmJxcDO d = indicator.toReportCvmJxcDO();
        fillCommonFields(d, statTime);
        d.setLogicNum(logicNum);
        d.setProductType(ProductTypeEnum.COS.getCode());
        return d;
    }



    private void fillServerCategory(ReportCvmJxcDO jxcDO){
        if (Strings.isBlank((getProduct()))){
            return;
        }
        if (Objects.equals(getProduct(), "腾讯云CVM")){
            jxcDO.setServerCategory("CVM");
        }else if(Objects.equals(getProduct(), "腾讯云黑石租赁") || Objects.equals(getProduct(), "腾讯云裸金属CBM")){
            jxcDO.setServerCategory("裸金属");
        }else {
            jxcDO.setServerCategory(getProduct());
        }
    }

}
