package cloud.demand.app.modules.plan_detail.model.inventory;

import cloud.demand.app.modules.plan_detail.model.CommonDTO;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 线上库存：好差呆
 */
@Data
public class GoodBadUglyCoreDTO extends CommonDTO {

     @Column("cvmtype")
     private String deviceName;

     @Column("zoneid")
     private Long zoneId;

     /** 好料，核心数*/
     @Column("cpucore_good")
     private Long goodCpuCore;

     /** 差料，核心数*/
     @Column(value = "cpucore_bad")
     private Long badCpuCore;

     /** 呆料，核心数*/
     @Column(value = "cpucore_ugly")
     private Long uglyCpuCore;

      /** 好料，GPU卡数*/
      @Column("gpucard_good")
      private BigDecimal goodGpuCard;

      /** 差料，核心数*/
      @Column(value = "gpucard_bad")
      private BigDecimal badGpuCard;

      /** 呆料，核心数*/
      @Column(value = "gpucard_ugly")
      private BigDecimal uglyGpuCard;


    /** 弹性好料，核心数*/
    @Column("cpucore_good_buffer")
    private Long goodBufferCpuCore;

    /** 弹性差料，核心数*/
    @Column(value = "cpucore_bad_buffer")
    private Long badBufferCpuCore;

    /** 弹性呆料，核心数*/
    @Column(value = "cpucore_ugly_buffer")
    private Long uglyBufferCpuCore;

 }
