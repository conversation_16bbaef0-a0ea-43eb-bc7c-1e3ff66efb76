package cloud.demand.app.modules.plan_detail.service.impl;

import cloud.demand.app.entity.rrp.ReportPlanDetailDO;
import cloud.demand.app.modules.common.enums.ComputeTypeEnum;
import cloud.demand.app.modules.common.enums.PlanIndicatorEnum;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.plan_detail.enums.PlanDetailMetalIndicatorEnum;
import cloud.demand.app.modules.plan_detail.model.inventory.GoodBadUglyCoreDTO;
import cloud.demand.app.modules.plan_detail.model.inventory.GoodBadUglyMetalDTO;
import cloud.demand.app.modules.plan_detail.service.PlanDetailCommonService;
import cloud.demand.app.modules.plan_detail.service.PlanInventoryDetailService;
import cloud.demand.app.modules.common.service.TaskLogService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.nutz.lang.Lang;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class PlanInventoryDetailServiceImpl implements PlanInventoryDetailService {

    @Resource
    private DBHelper planDBHelper;
    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    PlanDetailCommonService planDetailCommonService;
    @Resource
    private TaskLogService taskLogService;

    @Override
    public void getAndSaveCvmInventoryData(String statTime) {
        planDetailCommonService.saveCvmDataToDB(generateCvmOnlineInventoryData(statTime));
    }

    @Override
    public void getAndSaveMetalInventoryData(String statTime) {
        planDetailCommonService.saveMetalDataToDB(generateMetalOnlineInventoryData(statTime));
    }

    /**
     * 生成 线上库存-好差呆 数据，包括CVM产品的CPU、GPU类型
     */
    private List<ReportPlanDetailDO> generateCvmOnlineInventoryData(String statTime){
        List<ReportPlanDetailDO> result = new ArrayList<>();
        ArrayList<ComputeTypeEnum> computeTypes = Lang.list(ComputeTypeEnum.CPU, ComputeTypeEnum.GPU);
        ArrayList<PlanIndicatorEnum> indicators =
                Lang.list(PlanIndicatorEnum.INVENTORY_GOOD, PlanIndicatorEnum.INVENTORY_BAD, PlanIndicatorEnum.INVENTORY_UGLY);

        for (ComputeTypeEnum computeType : computeTypes) {
            List<GoodBadUglyCoreDTO> dtoList = getCvmOnlineInventoryDTOList(statTime, ProductTypeEnum.CVM, computeType);
            if (ListUtils.isEmpty(dtoList)){
                for (PlanIndicatorEnum e : indicators) {
                    ReportPlanDetailDO planDetailDO = e.toReportPlanDetailDO();
                    planDetailDO.setStatTime(DateUtils.parse(statTime));
                    planDetailDO.setProductType(ProductTypeEnum.CVM.getCode());
                    planDetailDO.setCores(BigDecimal.ZERO);
                    planDetailDO.setComputeType(computeType.getCode());
                    result.add(planDetailDO);
                    continue;
                }
            }

            for (GoodBadUglyCoreDTO dto : dtoList) {
                for (PlanIndicatorEnum indicator : indicators) {
                    ReportPlanDetailDO planDetailDO = indicator.toReportPlanDetailDO();
                    planDetailCommonService.fillReportPlanDetailDO(planDetailDO, ProductTypeEnum.CVM, computeType, dto, statTime);
                    Long cpuCores = 0L;
                    BigDecimal gpuCards = BigDecimal.ZERO;
                    Long cpuCoresBuffer = 0L;
                    switch (indicator){
                        case INVENTORY_GOOD:
                            cpuCores = dto.getGoodCpuCore();
                            cpuCoresBuffer = handleNegativeNum(dto.getGoodBufferCpuCore());
                            gpuCards = gpuCards.add(dto.getGoodGpuCard());
                            break;
                        case INVENTORY_BAD:
                            cpuCores = dto.getBadCpuCore();
                            cpuCoresBuffer = handleNegativeNum(dto.getBadBufferCpuCore());
                            gpuCards = gpuCards.add(dto.getBadGpuCard());
                            break;
                        case INVENTORY_UGLY:
                            cpuCores = dto.getUglyCpuCore();
                            cpuCoresBuffer = handleNegativeNum(dto.getUglyBufferCpuCore());
                            gpuCards = gpuCards.add(dto.getUglyGpuCard());
                            break;
                    }
                    planDetailDO.setCores(new BigDecimal(cpuCores));
                    planDetailDO.setLogicNum(gpuCards);
                    planDetailDO.setBufferCores(new BigDecimal(cpuCoresBuffer == null ? 0 : cpuCoresBuffer));
                    result.add(planDetailDO);
                }
            }

            // clean重复数据，这里直接删除
            rrpDBHelper.delete(ReportPlanDetailDO.class,
                    "where stat_time = ? and indicator_code in (?) and product_type = 'CVM' and compute_type = ?",
                    statTime, ListUtils.transform(indicators, PlanIndicatorEnum::getCode), computeType.getCode());
        }
        return result;
    }

    private Long handleNegativeNum(Long num){
        return (num == null || num < 0) ? 0 : num;
    }


    /**
     * 获取GoodBadUglyCoreDTO对象
     */
    private List<GoodBadUglyCoreDTO> getCvmOnlineInventoryDTOList(String statTime, ProductTypeEnum productType,
                                                               ComputeTypeEnum computeType){
        String sql = "select cpu.zoneid, cpu.cvmtype, " +
                "       sum(cpucore_good) as `cpucore_good`," +
                "       sum(cpucore_bad)  as `cpucore_bad`," +
                "       sum(cpucore_ugly)  as `cpucore_ugly`," +
                "       ifnull(sum(gpucard_good), 0)  as `gpucard_good`," +
                "       ifnull(sum(gpucard_bad), 0)  as `gpucard_bad`," +
                "       ifnull(sum(gpucard_ugly), 0) as `gpucard_ugly`";

        String bufferSql = ", ifnull(sum(cpucore_good_buffer), 0) `cpucore_good_buffer`," +
                "       ifnull(sum(cpucore_bad_buffer), 0) `cpucore_bad_buffer`," +
                "       ifnull(sum(cpucore_ugly_buffer), 0) `cpucore_ugly_buffer`";

        String fromTable = " from daily_cvm_cpu_good_bad_and_ugly cpu" +
                " left join daily_cvm_gpu_good_bad_and_ugly gpu" +
                " on cpu.stattime = gpu.stattime and cpu.zoneid = gpu.zoneid and cpu.cvmtype = gpu.cvmtype" +
                " WHERE cpu.stattime = ?";
        String condCvm = " and cpu.cvmtype in (select cvmtype from static_cvmtype where gpucard = 0)";
        String condGpu = " and cpu.cvmtype in (select cvmtype from static_cvmtype where gpucard > 0)";
        String groupBy = " group by zoneid, cvmtype";

        StringBuilder builder = new StringBuilder();
        if (Objects.equals(productType, ProductTypeEnum.CVM)) {
            if (Objects.equals(computeType, ComputeTypeEnum.CPU)){
                builder.append(sql).append(bufferSql).append(fromTable).append(condCvm).append(groupBy);
            }else if (Objects.equals(computeType, ComputeTypeEnum.GPU)){
                builder.append(sql).append(fromTable).append(condGpu).append(groupBy);
            }
        }

        List<GoodBadUglyCoreDTO> raw = planDBHelper.getRaw(GoodBadUglyCoreDTO.class, builder.toString(), statTime);
        if (ListUtils.isEmpty(raw)){
            return Lang.list();
        }
        return raw;
    }

    /**
     * 获取GoodBadUglyMetalDTO(for 裸金属）对象
     */
    private List<GoodBadUglyMetalDTO> getMetalOnlineInventoryDTOList(String statTime, ComputeTypeEnum computeType){
        String sql = "select zoneid, cvmtype, " +
                " sum(hostcnt_good) as `hostcnt_good`," +
                " sum(hostcnt_bad) as `hostcnt_bad`," +
                " sum(hostcnt_ugly) as `hostcnt_ugly`";
        String fromTable = " from daily_cpm_good_bad_and_ugly where stattime = ?";
        String condCpu = " and cvmtype in (select cvmtype from static_cvmtype where gpucard = 0)";
        String condGpu = " and cvmtype in (select cvmtype from static_cvmtype where gpucard > 0)";
        String groupBy = " group by zoneid, cvmtype";

        StringBuilder builder = new StringBuilder();
        if (Objects.equals(computeType, ComputeTypeEnum.CPU)){
            builder.append(sql).append(fromTable).append(condCpu).append(groupBy);
        }else if (Objects.equals(computeType, ComputeTypeEnum.GPU)){
            builder.append(sql).append(fromTable).append(condGpu).append(groupBy);
        }

        List<GoodBadUglyMetalDTO> raw = planDBHelper.getRaw(GoodBadUglyMetalDTO.class, builder.toString(), statTime);
        if (ListUtils.isEmpty(raw)){
            return Lang.list();
        }
        return raw;
    }


    /**
     * 生成 线上库存-好差呆 数据，包括黑石产品的CPU、GPU类型
     */
    private List<ReportPlanDetailDO> generateMetalOnlineInventoryData(String statTime){
        List<ReportPlanDetailDO> result = new ArrayList<>();
        ArrayList<ComputeTypeEnum> computeTypes = Lang.list(ComputeTypeEnum.CPU, ComputeTypeEnum.GPU);
        ArrayList<PlanDetailMetalIndicatorEnum> indicators =
                Lang.list(PlanDetailMetalIndicatorEnum.INVENTORY_GOOD, PlanDetailMetalIndicatorEnum.INVENTORY_BAD,
                        PlanDetailMetalIndicatorEnum.INVENTORY_UGLY);

        for (ComputeTypeEnum computeType : computeTypes) {
            List<GoodBadUglyMetalDTO> dtoList = getMetalOnlineInventoryDTOList(statTime, computeType);
            if (ListUtils.isEmpty(dtoList)){
                for (PlanDetailMetalIndicatorEnum e : indicators) {
                    ReportPlanDetailDO planDetailDO = e.toReportPlanDetailDO();
                    planDetailDO.setStatTime(DateUtils.parse(statTime));
                    planDetailDO.setProductType(ProductTypeEnum.METAL.getCode());
                    planDetailDO.setNum(BigDecimal.ZERO);
                    planDetailDO.setComputeType(computeType.getCode());
                    planDetailDO.setCores(BigDecimal.ZERO);
                    result.add(planDetailDO);
                }
                String msg = "metalOnlineInventoryData("+computeType.getCode()+") not ready for statTime:{" + statTime + "}, ignore";
                taskLogService.genRunLog("GenPlanDetailTask", "generateMetalOnlineInventoryData", msg);
                continue;
            }
            for (GoodBadUglyMetalDTO dto : dtoList) {
                for (PlanDetailMetalIndicatorEnum indicator : indicators) {
                    ReportPlanDetailDO planDetailDO = indicator.toReportPlanDetailDO();
                    planDetailCommonService.fillReportPlanDetailDO(planDetailDO, ProductTypeEnum.METAL, computeType, dto, statTime);
                    BigDecimal num = BigDecimal.ZERO;
                    switch (indicator){
                        case INVENTORY_GOOD:
                            num = dto.getHostGood();
                            break;
                        case INVENTORY_BAD:
                            num = dto.getHostBad();
                            break;
                        case INVENTORY_UGLY:
                            num = dto.getHostUgly();
                            break;
                    }
                    planDetailDO.setNum(num);
                    result.add(planDetailDO);
                }
            }

            // clean重复数据，这里直接删除
            rrpDBHelper.delete(ReportPlanDetailDO.class,
                    "where stat_time = ? and indicator_code in (?) and product_type = '裸金属' and compute_type = ?",
                    statTime, ListUtils.transform(indicators, PlanDetailMetalIndicatorEnum::getCode), computeType.getCode());
        }
        return result;
    }







}
