package cloud.demand.app.modules.plan_detail.service;

import cloud.demand.app.entity.rrp.ReportPlanDetailDO;
import cloud.demand.app.modules.common.enums.ComputeTypeEnum;
import cloud.demand.app.modules.common.enums.PlanIndicatorEnum;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.plan_detail.model.CommonDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * 包含一些公共方法
 */
public interface PlanDetailCommonService {

    /**
     * 覆盖planDetail的数据
     * @param result 新的planDetail的数据，必须要有值，当没有值时认为本次替换有异常，不进行替换
     * @param replaceStatTime 要替换的日期，目前必填
     * @param replaceProductType 要替换的产品类型，目前必填
     * @param replaceComputeType 要替换的计算类型CPU/GPU，选填，不填表示不指定
     * @param replaceIndicatorCodes 要替换的指标，选填，不填表示不指定
     */
    void replacePlanDetail(List<ReportPlanDetailDO> result,
                           String replaceStatTime,
                           String replaceProductType,
                           String replaceComputeType,
                           List<String> replaceIndicatorCodes);

    /**
     * 给所有生成的Cvm detail刷上对应的逻辑核心数和母机台数并插入DB
     */
    void saveCvmDataToDB(List<ReportPlanDetailDO> list);

    /**
     * 给所有生成的裸金属产品 detail刷上对应的逻辑核心数和母机台数并插入DB
     */
    void saveMetalDataToDB(List<ReportPlanDetailDO> list);

    /**
     * 给所有生成的腾讯云 detail ：先删除历史数据，再新增插入DB
     */
    void deleteAndSaveCloudDataToDB(String statTime, ProductTypeEnum productTypeEnum, List<ReportPlanDetailDO> list);


    /**
     *  查询PLAN系统中母机机型对应的逻辑核心数与erp系统中该机型的逻辑核心数并保存到DB中
     */
    void diffLogicCore();

    /**
     * 填充除枚举对象及指标值外的所有属性
     */
    void fillReportPlanDetailDO(ReportPlanDetailDO source, ProductTypeEnum productType, ComputeTypeEnum computeType,
                                CommonDTO dto, String statTime);

    /**
     * 当指标底数为0时，生成对应的指标的值为0
     */
    void genZeroReportPlanDetailDO(ArrayList<PlanIndicatorEnum> indicators, List<ReportPlanDetailDO> result,
                                           String statTime, ComputeTypeEnum computeType);
}
