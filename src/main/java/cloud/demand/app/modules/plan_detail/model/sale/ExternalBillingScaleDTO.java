package cloud.demand.app.modules.plan_detail.model.sale;

import cloud.demand.app.modules.plan_detail.model.CommonDTO;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 外部计费规模的DTO
 */
@Data
public class ExternalBillingScaleDTO extends CommonDTO {

    @Column("cvmtype")
    private String deviceName;

    @Column("zoneid")
    private Long zoneId;

    @Column("CVM计费规模")
    private BigDecimal cvmBillingScaleCore;

    @Column("Lighthouse规模")
    private BigDecimal lhScaleCore;

    @Column("cvm_gpu")
    private BigDecimal cvmGpu;

    @Column("lh_gpu")
    private BigDecimal lhGpu;

}
