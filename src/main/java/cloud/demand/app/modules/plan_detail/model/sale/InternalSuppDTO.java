package cloud.demand.app.modules.plan_detail.model.sale;

import cloud.demand.app.modules.plan_detail.model.CommonDTO;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 内部领用-支撑区SUPP数据DTO
 */
@Data
public class InternalSuppDTO extends CommonDTO {

    @Column("stdtype")
    private String deviceName;

    @Column("zoneid")
    private Long zoneId;

    @Column("支撑区核心数")
    private BigDecimal freeCpuSupp;

    @Column("gpu")
    private BigDecimal gpu;


}
