package cloud.demand.app.modules.physical_device.controller;

import cloud.demand.app.modules.physical_device.model.JiaPingCommonReq;
import cloud.demand.app.modules.physical_device.service.OfferInfo2ThirdPartyService;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@JsonrpcController("/physical/info/api")
public class OfferThirdPartyController {

    @Resource
    OfferInfo2ThirdPartyService offerInfo2ThirdPartyService;

    @RequestMapping
    public Object physicalDemandDataForJiaPing(@JsonrpcParam JiaPingCommonReq req) {
        return offerInfo2ThirdPartyService.physicalDemandDataForJiaPing(req);
    }

    @RequestMapping
    public Object purchaseForecastDataForJiaPing(@JsonrpcParam JiaPingCommonReq req) {
        return offerInfo2ThirdPartyService.purchaseForecastDataForJiaPing(req);
    }
}
