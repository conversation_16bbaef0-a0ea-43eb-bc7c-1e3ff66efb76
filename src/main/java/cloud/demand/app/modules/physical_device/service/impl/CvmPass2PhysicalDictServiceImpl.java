package cloud.demand.app.modules.physical_device.service.impl;

import cloud.demand.app.modules.physical_device.service.CvmPass2PhysicalDictService;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import yunti.boot.client.JsonrpcClient;
import yunti.boot.config.DynamicProperty;

@Service
public class CvmPass2PhysicalDictServiceImpl implements CvmPass2PhysicalDictService {

    @Resource
    JsonrpcClient jsonrpcClient;

    DynamicProperty<String> clojureAppUrl = DynamicProperty.create("app.url.physical.clojure", "");

    public String clojureAppUrl() {
        return clojureAppUrl.get();
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600, keyScript = "args[0]")
    public Map<String, List<Integer>> queryMonth2WeekList(int year) {
        Map map = jsonrpcClient.jsonrpc("getWeekDistribution")
                .params(ImmutableMap.of("year", year))
                .uri(clojureAppUrl())
                .timeout("10s")
                .postForObject(Map.class);
        return (Map<String, List<Integer>>) map.get("week_distributed");
    }

    @Override
    public Integer[] queryTodayYearMonthWeek() {
        Map map = jsonrpcClient.jsonrpc("getTodayDate")
                .params(ImmutableMap.of())
                .uri(clojureAppUrl())
                .timeout("10s")
                .postForObject(Map.class);
        return new Integer[]{(Integer) map.get("year"), (Integer) map.get("month"), (Integer) map.get("week")};
    }
}
