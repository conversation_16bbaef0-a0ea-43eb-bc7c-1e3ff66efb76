package cloud.demand.app.modules.order.dto.resp;

import cloud.demand.app.modules.order.dto.req.OrderSaveReq.OtherInstance;
import cloud.demand.app.modules.order.dto.req.OrderSaveReq.OtherZone;
import cloud.demand.app.modules.order.entity.OrderItemDO;
import cloud.demand.app.modules.order.enums.OrderBillTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoDiskTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.OrderAdvanceWeekFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.OrderItemPreDeductFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.OrderItemSupplyPlanFiller;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@Data
@ToString
public class OrderItemDTO extends OrderItemDO implements OrderItemPreDeductFiller, OrderItemSupplyPlanFiller,
        OrderAdvanceWeekFiller {

    /**
     * 置放群组，多个置放群组用英文分号隔开<br/>Column: [placement_group]
     */
    private List<String> placementGroupList;

    /**
     * 备选可用区
     */
    private List<OtherZone> otherZoneList;

    private List<OtherInstance> otherInstanceList;

    private OrderItemDTO beforeConsensusItem;

    private String systemDiskTypeName;

    private String dataDiskTypeName;

    // 系统盘总大小，实例数量*单台系统盘大小
    private Integer systemTotalDisk;

    // 单台数据盘大小，单台数据盘块数*单数据盘磁盘容量
    private Integer dataDiskOneInstanceStorage;

    // 数据盘总大小，实例数量*单台数据盘大小
    private Integer dataTotalDisk;

    // 申请预扣实例数
    private int preDeductNum;

    // 申请预扣核心数
    private int preDeductCore;

    // 实际预扣实例数
    private int actualPreDeductNum;

    // 实际预扣核心数
    private int actualPreDeductCore;

    // 计费模式名称
    private String billTypeName;

    // 备选可用区名称，多个可用区用,分隔
    private String otherZoneNamesJoin;

    // 备选实例规格，多个实例规格用,分隔
    private String otherInstanceModelsJoin;

    private String customhouseTitle;

    // 供应方案-大盘满足核心数
    private Integer satisfySupplyCore;

    // 供应方案-采购满足核心数
    private Integer buySupplyCore;

    // 供应方案-搬迁满足核心数
    private Integer moveSupplyCore;

    // 订单满足度-订单满足总核心数
    private BigDecimal orderItemMatchedTotalCore;

    // 订单满足度-满足率
    private BigDecimal orderItemMatchedRate;

    // 计划预扣卡数
    private Integer preDeductGpuNum;

    // 实际预扣卡数
    private Integer actualPreDeductGpuNum;
    /** 提单提前期 */
    private Integer advanceWeek;
    /** 0～4周核心数 */
    private Integer core1To4Week;
    /** 5～8周核心数 */
    private Integer core5To8Week;
    /** 9～12周核心数 */
    private Integer core9To12Week;
    /** 13周核心数 */
    private Integer core13Week;
    /** 0～4周卡数 */
    private Integer gpu1To4Week;
    /** 5～8周卡数 */
    private Integer gpu5To8Week;
    /** 9～12周卡数 */
    private Integer gpu9To12Week;
    /** 13周卡数 */
    private Integer gpu13Week;

    // 供应方案是否已满足 for 数据库
    private Boolean isAlreadySatisfy;


    public static OrderItemDTO buildDTO(OrderItemDTO item) {
        if (StringUtils.isNotBlank(item.getPlacementGroup())) {
            item.setPlacementGroupList(
                    new ArrayList<>(Arrays.asList(item.getPlacementGroup().split(";"))));
        }

        if (StringUtils.isNotBlank(item.getOtherZone())) {
            item.setOtherZoneList(
                    JSON.parseToList(item.getOtherZone(), OtherZone.class));
            List<String> names = ListUtils.transform(item.getOtherZoneList(), OtherZone::getName);
            item.setOtherZoneNamesJoin(String.join(",", names));
        }
        if (StringUtils.isNotBlank(item.getOtherInstance())) {
            item.setOtherInstanceList(JSON.parseToList(item.getOtherInstance(), OtherInstance.class));
            List<String> names = new ArrayList<>();
            for (OtherInstance instance : item.getOtherInstanceList()) {
                if (instance == null || ListUtils.isEmpty(instance.getOtherInstanceModel())) {
                    continue;
                }
                names.addAll(instance.getOtherInstanceModel());
            }
            item.setOtherInstanceModelsJoin(String.join(",", names));
        }

        YunxiaoDiskTypeEnum sys = YunxiaoDiskTypeEnum.getByCode(item.getSystemDiskType());
        item.setSystemDiskTypeName(sys == null ? item.getSystemDiskType() : sys.getName());

        YunxiaoDiskTypeEnum dataDisk = YunxiaoDiskTypeEnum.getByCode(item.getDataDiskType());
        item.setDataDiskTypeName(dataDisk == null ? item.getDataDiskType() : dataDisk.getName());

        item.setBillTypeName(OrderBillTypeEnum.getNameByCode(item.getBillType(), item.getBillType()));

        int instanceNumInt = item.getInstanceNum() == null ? 0 : item.getInstanceNum();
        int sysDiskStorageInt = item.getSystemDiskStorage() == null ? 0 : item.getSystemDiskStorage();
        int dataDiskStorageInt = item.getDataDiskStorage() == null ? 0 : item.getDataDiskStorage();
        int dataDiskNumInt = item.getDataDiskNum() == null ? 0 : item.getDataDiskNum();
        item.setSystemTotalDisk(instanceNumInt * sysDiskStorageInt);
        item.setDataDiskOneInstanceStorage(dataDiskNumInt * dataDiskStorageInt);
        item.setDataTotalDisk(instanceNumInt * item.getDataDiskOneInstanceStorage());

        return item;
    }

    public static List<OrderItemDO> toItemDO(List<OrderItemDTO> list) {
        List<OrderItemDO> result = new ArrayList<>();
        for (OrderItemDTO orderItemDTO : list) {
            OrderItemDO orderItemDO = orderItemDTO;
            result.add(orderItemDO);
        }
        return result;
    }

    @Override
    public String provideOrderNumberId() {
        return this.getOrderNumberId();
    }

    @Override
    public Integer provideTotalGpu() {
        return getTotalGpuNum();
    }

    @Override
    public void fillAdvanceWeek(Integer advanceWeek) {
        this.advanceWeek = advanceWeek;
    }

    @Override
    public void fillAdvanceWeekCore1to4(Integer core) {
        this.core1To4Week = core;
    }

    @Override
    public void fillAdvanceWeekCore5to8(Integer core) {
        this.core5To8Week = core;
    }

    @Override
    public void fillAdvanceWeekCore9to12(Integer core) {
        this.core9To12Week = core;
    }

    @Override
    public void fillAdvanceWeekCore13(Integer core) {
        this.core13Week = core;
    }

    @Override
    public void fillAdvanceWeekGpu1to4(Integer gpuNum) {
        this.gpu1To4Week = gpuNum;
    }

    @Override
    public void fillAdvanceWeekGpu5to8(Integer gpuNum) {
        this.gpu5To8Week = gpuNum;
    }

    @Override
    public void fillAdvanceWeekGpu9to12(Integer gpuNum) {
        this.gpu9To12Week = gpuNum;
    }

    @Override
    public void fillAdvanceWeekGpu13(Integer gpuNum) {
        this.gpu13Week = gpuNum;
    }

    @Override
    public String provideOrderNumber() {
        return this.getOrderNumber();
    }

    @Override
    public String provideZoneName() {
        return this.getZoneName();
    }

    @Override
    public String provideInstanceModel() {
        return this.getInstanceModel();
    }

    @Override
    public int provideInstanceNum() {
        return this.getInstanceNum() == null ? 0 : this.getInstanceNum();
    }

    @Override
    public void fillPreDeductNum(Integer preDeductNum) {
        this.preDeductNum = preDeductNum == null ? 0 : preDeductNum;
    }

    @Override
    public void fillPreDeductCore(Integer preDeductCore) {
        this.preDeductCore = preDeductCore == null ? 0 : preDeductCore;
    }

    @Override
    public void fillActualPreDeductNum(Integer actualPreDeductNum) {
        this.actualPreDeductNum = actualPreDeductNum == null ? 0 : actualPreDeductNum;
    }

    @Override
    public void fillActualPreDeductCore(Integer actualPreDeductCore) {
        this.actualPreDeductCore = actualPreDeductCore == null ? 0 : actualPreDeductCore;
    }

    @Override
    public void fillPreDeductGpuNum(Integer preDeductGpuNum) {
        this.preDeductGpuNum = preDeductGpuNum == null ? 0 : preDeductGpuNum;
    }

    @Override
    public void fillActualPreDeductGpuNum(Integer actualPreDeductGpuNum) {
        this.actualPreDeductGpuNum = actualPreDeductGpuNum == null ? 0 : actualPreDeductGpuNum;
    }

    @Override
    public void fillSatisfySupplyCore(Integer satisfySupplyCore) {
        this.satisfySupplyCore = satisfySupplyCore == null ? 0 : satisfySupplyCore;
    }

    @Override
    public void fillBuySupplyCore(Integer buySupplyCore) {
        this.buySupplyCore = buySupplyCore == null ? 0 : buySupplyCore;
    }

    @Override
    public void fillMoveSupplyCore(Integer moveSupplyCore) {
        this.moveSupplyCore = moveSupplyCore == null ? 0 : moveSupplyCore;
    }

    @Override
    public void fillSatisfySupplyInstanceNum(Integer satisfySupplyInstanceNum) {
        // non
    }

    @Override
    public void fillBuySupplyInstanceNum(Integer buySupplyInstanceNum) {
        // non
    }

    @Override
    public void fillMoveSupplyInstanceNum(Integer moveSupplyInstanceNum) {
        // non
    }

    @Override
    public void fillOrderItemTotalMatchedCore(BigDecimal totalMatchedCore) {
        this.orderItemMatchedTotalCore = totalMatchedCore == null ? BigDecimal.ZERO : totalMatchedCore;
    }

    @Override
    public Integer provideOrderItemTotalCore() {
        return this.getTotalCore() == null ? 0 : this.getTotalCore();
    }

    @Override
    public void fillOrderItemTotalMatchedRate(BigDecimal totalMatchedRate) {
        this.orderItemMatchedRate = totalMatchedRate == null ? BigDecimal.ZERO : totalMatchedRate;
    }
}
