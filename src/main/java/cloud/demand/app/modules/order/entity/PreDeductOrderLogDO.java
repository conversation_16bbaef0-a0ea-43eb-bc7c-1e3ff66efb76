package cloud.demand.app.modules.order.entity;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import org.nutz.lang.Strings;

/**
 * 预扣订单日志，这个表相当于是一个日志关联关系表，详细的日志信息在订单日志和流程日志中
 */
@Data
@Table("pre_deduct_order_log")
public class PreDeductOrderLogDO  extends BaseDO {

    /**
     * 订单号
     */
    @Column("order_number")
    private String orderNumber;

    /**
     * 预扣计划号
     */
    @Column("pre_deduct_plan_number")
    private String preDeductPlanNumber;

    /**
     * 订单日志id
     */
    @Column(value = "order_log_id")
    private Long orderLogId;

    /**
     * 流程节点记录id<br/>Column: [flow_node_record_id]
     */
    @Column(value = "flow_node_record_id")
    private Long flowNodeRecordId;


    public static PreDeductOrderLogDO createForInsert(OrderLogDO orderLog, String preDeductPlanNumber) {
        if (orderLog == null || Strings.isBlank(preDeductPlanNumber)) {
            return null;
        }
        PreDeductOrderLogDO res = new PreDeductOrderLogDO();
        res.setOrderLogId(orderLog.getId());
        res.setFlowNodeRecordId(orderLog.getFlowNodeRecordId());
        res.setOrderNumber(orderLog.getOrderNumber());
        res.setPreDeductPlanNumber(preDeductPlanNumber);
        return res;
    }

}
