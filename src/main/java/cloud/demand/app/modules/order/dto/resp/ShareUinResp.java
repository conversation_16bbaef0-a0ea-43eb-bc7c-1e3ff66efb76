package cloud.demand.app.modules.order.dto.resp;

import cloud.demand.app.modules.p2p.industry_demand.dto.UinNameDTO;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class ShareUinResp {

    /**
     *  订单uin信息
     */
    private UinNameDTO orderUin;

    /**
     *  与订单uin 相同客户简称或相同通用客户简称的 云霄配置的可共享预扣uin数据
     */
    private List<UinNameDTO> valid = new ArrayList<>();

    /**
     * 云霄配置的可共享预扣uin数据，但是与订单uin客户简、通用客户简称不同
     */
    private List<UinNameDTO> invalid = new ArrayList<>();

}
