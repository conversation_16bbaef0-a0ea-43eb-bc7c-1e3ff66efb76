package cloud.demand.app.modules.order.enums;

/**
 *   <a href="https://doc.weixin.qq.com/sheet/e3_AQAAmwbNAMwR0pL0wkZRMyeEBhvIC?scode=AJEAIQdfAAoaF0F3KhAQAAmwbNAMw&tab=BB08J2">
 *       订单号前缀</a>
 */
public enum OrderNumberPrefixEnum {

    ON("ON", "通过预测转换的订单"),

    OE("OE", "用户直接提交的订单")

    ;

    private final String code;

    private final String name;

    OrderNumberPrefixEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
