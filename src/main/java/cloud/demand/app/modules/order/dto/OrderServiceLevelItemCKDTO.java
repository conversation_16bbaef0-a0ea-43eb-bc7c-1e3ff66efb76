package cloud.demand.app.modules.order.dto;

import cloud.demand.app.modules.order.entity.std_table.DwdCrpOrderServiceLevelDetailDO;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

@Data
public class OrderServiceLevelItemCKDTO extends DwdCrpOrderServiceLevelDetailDO {

    private String orderLabel;

    private String orderNodeCode;

    @Data
    public static class OrderData {

        @Column("order_number")
        private String orderNumber;

        @Column("order_label")
        private String orderLabel;

        @Column("order_node_code")
        private String orderNodeCode;

    }

}
