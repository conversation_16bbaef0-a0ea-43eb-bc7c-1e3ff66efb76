package cloud.demand.app.modules.order.dto.resp.supply.satisfaction;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class StockDTO extends Dimension {

    // 总库存
    @Column("allStock")
    private BigDecimal allStock;

    // 线上库存
    private BigDecimal onlineStock;

    // 在途库存
    private BigDecimal onWayStock;

    // 可用库存
    private Integer availableStock;


    @Data
    public static class StockDetailDTO extends StockDTO {

        // 采购单号
        @Column("quotaId")
        private String quotaId;

    }

    public static List<StockDTO> mergeSum(List<StockDTO> online, List<StockDetailDTO> onWay) {
        Map<String, StockDTO> dataMap = new HashMap<>();
        for (StockDTO data : online) {
            if (data == null) {
                continue;
            }
            String key = mergeKey(data);
            StockDTO res = dataMap.get(key);
            if (res == null) {
                res = new StockDTO();
                res.setInstanceType(data.getInstanceType());
                res.setZoneName(data.getZoneName());
                res.setAllStock(BigDecimal.ZERO);
                res.setOnlineStock(BigDecimal.ZERO);
                res.setOnWayStock(BigDecimal.ZERO);
            }
            if (data.getAllStock() != null) {
                res.setAllStock(res.allStock.add(data.allStock));
                res.setOnlineStock(res.onlineStock.add(data.allStock));
            }
            dataMap.put(key, res);
        }
        for (StockDTO data : onWay) {
            if (data == null) {
                continue;
            }
            String key = mergeKey(data);
            StockDTO res = dataMap.get(key);
            if (res == null) {
                res = new StockDTO();
                res.setInstanceType(data.getInstanceType());
                res.setZoneName(data.getZoneName());
                res.setAllStock(BigDecimal.ZERO);
                res.setOnlineStock(BigDecimal.ZERO);
                res.setOnWayStock(BigDecimal.ZERO);
            }
            if (data.getAllStock() != null) {
                res.setAllStock(res.allStock.add(data.allStock));
                res.setOnWayStock(res.onWayStock.add(data.allStock));
            }
            dataMap.put(key, res);
        }
        return new ArrayList<>(dataMap.values());
    }

}
