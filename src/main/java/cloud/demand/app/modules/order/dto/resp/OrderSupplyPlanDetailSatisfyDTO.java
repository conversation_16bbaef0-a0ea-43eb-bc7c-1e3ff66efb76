package cloud.demand.app.modules.order.dto.resp;

import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailSatisfyDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

@Data
public class OrderSupplyPlanDetailSatisfyDTO extends OrderSupplyPlanDetailSatisfyDO {

    @RelatedColumn(localColumn = "supply_detail_id", remoteColumn = "id")
    private OrderSupplyPlanDetailDO supplyDetail;

}
