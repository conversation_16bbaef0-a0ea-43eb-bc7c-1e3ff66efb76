package cloud.demand.app.modules.order.dto.req;

import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.service.function.CreateOrderAfterPushFlowFunction;
import cloud.demand.app.modules.order.service.function.OrderCurrentProcessorCreator;
import lombok.Data;

@Data
public class GlobalApprovalReq {

    // 业务id，非空
    private String bizId;

    // 流程编码， 非空
    private String flowCode;

    // 当前审批节点，非空
    private String currentNodeCode;

    // 订单号，非空
    private String orderNumber;

    // 审批结果，非空，true表示审批通过，false表示审批驳回
    private boolean approvalResult;

    // 审批结果名称，用于填充节点操作名称，为空或不传时取节点链中配置的操作名称。 例： 加急延期-云运管审批-审批通过
    private String approvalResultName;

    // 审批备注
    private String approvalRemark;

    // 操作事件
    private String operateEvent;

    // 审批人，非空
    private String approvalUser;

    // 当前操作时的订单记录，非空
    private OrderInfoDO order;

    /**
     *  订单优先级，在主流程云运管审批中赋值，默认低优先级，低优先级，中优先级，高优先级
     */
    private String orderLevelName;

    /**
     *  审批时引起订单状态变化可自定义的订单记录生成方法，为null时默认是从原订单复制生成 <br/>
     *  必须返回 新的 订单对象 <br/>
     *  必须返回 新的 订单对象 <br/>
     *  必须返回 新的 订单对象 <br/>
     *  此接口内不要对生成的订单进行 insert 操作 <br/>
     *  此接口内不要对生成的订单进行 insert 操作 <br/>
     *  此接口内不要对生成的订单进行 insert 操作 <br/>
     */
    private CreateOrderAfterPushFlowFunction orderCreator;

    /**
     *  生成订单当前可处理人，此时的订单信息暂未 insert 进数据库，设置完当前可处理人之后再 insert 订单信息
     */
    private OrderCurrentProcessorCreator processorCreator;

    /**
     *  为订单设置驳回日志id，默认false不设置， true表示设置，为true时还需要满足一下内置条件 <br/>
     *  内置条件：1，是审批操作；2，是审批驳回 <br/>
     * @see OrderInfoDO#setRefuseLogId(Long)
     */
    private boolean refuseLogIdSet = false;

    /**
     * 为 true 时可跳过处理人判断
     */
    private boolean isSystem = false;

}
