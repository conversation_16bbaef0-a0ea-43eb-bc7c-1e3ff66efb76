package cloud.demand.app.modules.order.dto.resp.service_level;

import cloud.demand.app.modules.order.dto.req.ServiceLevelQueryReq;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class OfficialWebsiteServiceLevelOverviewResp {

    // 官网购买数据
    private List<OfficialWebsiteBuyDTO> buyList;

    // 官网售罄数据
    private List<OfficialWebsiteSoldOutDTO> soldList;

    // 官网购买成功率
    private BigDecimal buySuccessRate;

    // 官网售罄率
    private BigDecimal soldOutRate;

    // 官网服务水平
    private BigDecimal serviceLevel;

    // 查询条件
    private ServiceLevelQueryReq queryReq;

}
