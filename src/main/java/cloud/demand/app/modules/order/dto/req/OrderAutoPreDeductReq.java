package cloud.demand.app.modules.order.dto.req;

import java.time.LocalDate;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class OrderAutoPreDeductReq {

    @NotBlank
    private String orderNumber;

    /**
     *  true 表示开启自动预扣；  false 表示关闭自动预扣
     */
    @NotNull
    private Boolean autoPreDeduct;

    /**
     * 自动预扣时，预扣开始日期
     */
    private LocalDate autoPreDeductBeginDate;

    /**
     * 自动预扣时，预扣结束日期
     */
    private LocalDate autoPreDeductEndDate;

}
