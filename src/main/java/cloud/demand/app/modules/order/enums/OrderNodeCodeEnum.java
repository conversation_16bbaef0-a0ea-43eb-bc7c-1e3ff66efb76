package cloud.demand.app.modules.order.enums;

import lombok.Getter;
import org.nutz.lang.Strings;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 订单流程节点枚举类
 */
@Getter
public enum OrderNodeCodeEnum {

    // 主流程节点枚举

    /**
     * 开始
     */
    node_begin("node_begin", "开始", OrderFlowEnum.ORDER_MAIN),

    /**
     * 需求提出
     */
    node_order_propose("node_order_propose", "需求提出", OrderFlowEnum.ORDER_MAIN),

    /**
     * 需求审批
     */
    node_order_approval("node_order_approval", "需求审批", OrderFlowEnum.ORDER_MAIN),

    /**
     * 满足方式评估
     */
    node_order_match_way("node_order_match_way", "满足方式评估", OrderFlowEnum.ORDER_MAIN),

    /**
     * 交付供应
     */
    node_order_supply("node_order_supply", "交付供应", OrderFlowEnum.ORDER_MAIN),

    /**
     * 履约跟踪
     */
    node_order_following("node_order_following", "履约跟踪", OrderFlowEnum.ORDER_MAIN),

    /**
     * 订单关闭
     */
    node_order_close("node_order_close", "订单关闭", OrderFlowEnum.ORDER_MAIN),

    /**
     * 订单取消
     */
    node_order_cancel("node_order_cancel", "订单取消", OrderFlowEnum.ORDER_MAIN),

    // 供应方案制定子流程节点

    /**
     * 首次CBS审批
     */
    node_cbs_approval("node_cbs_approval", "首次CBS审批", OrderFlowEnum.ORDER_SUPPLY_PLAN_FORMULATE),

    /**
     * 方案制定
     */
    node_cvm_create_plan("node_cvm_create_plan", "方案制定",
            OrderFlowEnum.ORDER_SUPPLY_PLAN_FORMULATE, OrderFlowEnum.ORDER_SUPPLY_PLAN_MODIFY),

    /**
     * 行业共识
     */
    node_plan_consensus("node_plan_consensus", "行业共识",
            OrderFlowEnum.ORDER_SUPPLY_PLAN_FORMULATE, OrderFlowEnum.ORDER_SUPPLY_PLAN_MODIFY),

    // 修改需求、加急延期子流程节点

    /**
     * 提交申请 （修改需求、加急延期）
     */
    node_modify_demand_begin("node_modify_demand_begin", "提交申请",
            OrderFlowEnum.ORDER_UPDATE_DEMAND, OrderFlowEnum.ORDER_URGENT_DELAY),

    /**
     * 云运管审批 （修改需求、加急延期）
     */
    node_cloud_manage_approval("node_cloud_manage_approval", "云运管审批",
            OrderFlowEnum.ORDER_UPDATE_DEMAND, OrderFlowEnum.ORDER_URGENT_DELAY),

    /**
     * 修改生效 （修改需求、加急延期）
     */
    node_modify_demand_available("node_modify_demand_available", "修改生效",
            OrderFlowEnum.ORDER_UPDATE_DEMAND, OrderFlowEnum.ORDER_URGENT_DELAY),

    /**
     * 云运管审批
     */
    comd_approval("comd_approval", "云运管审批", OrderFlowEnum.ORDER_CANCEL, OrderFlowEnum.ORDER_WITHHOLDING),

    product_approval("product_approval", "产品审批", OrderFlowEnum.ORDER_WITHHOLDING, OrderFlowEnum.EKS_ORDER_WITHHOLDING, OrderFlowEnum.EMR_ORDER_WITHHOLDING),

    eks_approval("eks_approval", "EKS审批", OrderFlowEnum.EKS_ORDER_WITHHOLDING),

    emr_approval("emr_approval", "EMR审批", OrderFlowEnum.EMR_ORDER_WITHHOLDING),

    database_product_approval("database_product_approval", "数据库产品审批", OrderFlowEnum.DATABASE_ORDER_CANCEL),

    ;

    /**
     * 节点编码
     */
    private final String code;
    /**
     * 节点名称
     */
    private final String name;
    /**
     * 节点所属流程
     */
    private final OrderFlowEnum[] flow;

    // 可以绑定星云订单的状态
    public static List<OrderNodeCodeEnum> canBindXYPurchaseOrder = Arrays.asList(node_order_supply,node_order_following);
    // 可以绑定星云订单的状态 code 与DB交互
    public static List<String> canBindXYPurchaseOrderCode = Arrays.asList(node_order_supply.getCode(),node_order_following.getCode());

    OrderNodeCodeEnum(String code, String name, OrderFlowEnum... flow) {
        this.code = code;
        this.name = name;
        this.flow = flow;
    }

    public static OrderNodeCodeEnum getByCode(String code) {
        for (OrderNodeCodeEnum e : OrderNodeCodeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        OrderNodeCodeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

    public static String getCodeByName(String name) {
        for (OrderNodeCodeEnum e : OrderNodeCodeEnum.values()) {
            if (Strings.equals(name, e.getName())) {
                return e.getCode();
            }
        }
        return "";
    }

}
