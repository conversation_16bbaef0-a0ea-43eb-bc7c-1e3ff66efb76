package cloud.demand.app.modules.order.dto.resp;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.time.LocalDate;

@Data
public class QueryPplOrderItemForXyVO {

    @Column(value = "id")
    private Long id;

    @Column(value = "order_number")
    private String orderNumber;

    @Column(value = "customer_short_name")
    private String customerShortName;

    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    @Column(value = "order_node_code")
    private String orderNodeCode;

    @Column(value = "match_type_name")
    private String matchTypeName;

    @Column(value = "after_consensus_order_number_id")
    private String afterConsensusOrderNumberId;

    @Column(value = "supply_biz_type")
    private int supplyBizType;

    @Column(value = "supply_biz_id")
    private String supplyBizId;

    @Column(value = "supply_zone_name")
    private String supplyZoneName;

    @Column(value = "supply_instance_type")
    private String supplyInstanceType;

    @Column(value = "supply_core_num")
    private Integer supplyCoreNum;
}
