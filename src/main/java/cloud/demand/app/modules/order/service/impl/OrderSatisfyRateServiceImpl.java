package cloud.demand.app.modules.order.service.impl;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.flow.constant.FlowNodeDefaultReturnValueEnum;
import cloud.demand.app.modules.flow.dto.FlowNodeRecordWithFlowInfoDTO;
import cloud.demand.app.modules.flow.service.FlowService;
import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.operation_view.inventory_health.enums.InventoryHealthZoneType;
import cloud.demand.app.modules.operation_view.inventory_health.service.InventoryHealthConfigService;
import cloud.demand.app.modules.order.constant.OrderConstant;
import cloud.demand.app.modules.order.dto.ElasticCycleConfig;
import cloud.demand.app.modules.order.dto.OrderServiceLevelItemCKDTO;
import cloud.demand.app.modules.order.dto.PreDeductGridSimpleDTO;
import cloud.demand.app.modules.order.dto.req.BackupServiceLevelDetailReq;
import cloud.demand.app.modules.order.dto.req.ConsensusReq;
import cloud.demand.app.modules.order.dto.req.ConsensusReq.ConsensusItem;
import cloud.demand.app.modules.order.dto.req.OrderFlowNodeValueSetReq;
import cloud.demand.app.modules.order.dto.req.ServiceLevelQueryReq;
import cloud.demand.app.modules.order.dto.req.SupplyPlanDetailMatchCoreUpdateReq;
import cloud.demand.app.modules.order.dto.req.SupplyPlanDetailMatchCoreUpdateReq.Item;
import cloud.demand.app.modules.order.dto.resp.CycleElasticPerformanceResp.BuyItem;
import cloud.demand.app.modules.order.dto.resp.OrderDetailResp;
import cloud.demand.app.modules.order.dto.resp.OrderSupplyPlanDetailWithPlanDTO;
import cloud.demand.app.modules.order.dto.resp.OrderSupplyPlanWithDetailDTO;
import cloud.demand.app.modules.order.dto.resp.OrderSupplySatisfyModifiedRecordResp;
import cloud.demand.app.modules.order.dto.resp.OrderSupplySatisfyModifiedRecordResp.VersionItem;
import cloud.demand.app.modules.order.dto.resp.satisfy.GlobalSatisfyGapDTO;
import cloud.demand.app.modules.order.dto.resp.satisfy.IndustrySatisfyGapDTO;
import cloud.demand.app.modules.order.dto.resp.service_level.OfficialWebsiteBuyDTO;
import cloud.demand.app.modules.order.dto.resp.service_level.OfficialWebsiteServiceLevelOverviewResp;
import cloud.demand.app.modules.order.dto.resp.service_level.OfficialWebsiteSoldOutDTO;
import cloud.demand.app.modules.order.dto.resp.service_level.OfficialWebsiteSoldOutDTO.OfficialWebsiteSoldOutDTODetail;
import cloud.demand.app.modules.order.dto.resp.service_level.OrderServiceLeveResp;
import cloud.demand.app.modules.order.dto.resp.service_level.OrderServiceLeveResp.OrderItemMatchCore;
import cloud.demand.app.modules.order.dto.resp.service_level.OrderServiceLeveResp.OrderItemTag;
import cloud.demand.app.modules.order.dto.resp.service_level.OrderServiceLeveResp.OrderServiceLevelDTO;
import cloud.demand.app.modules.order.dto.resp.supply.DeliverRecordDTO;
import cloud.demand.app.modules.order.dto.resp.supply.DeliverRecordDTO.DeliverItem;
import cloud.demand.app.modules.order.dto.resp.supply.OrderItemMatchCoreDTO;
import cloud.demand.app.modules.order.dto.resp.supply.satisfaction.DemandDTO;
import cloud.demand.app.modules.order.dto.resp.supply.satisfaction.DemandDTO.DemandDetailDTO;
import cloud.demand.app.modules.order.dto.resp.supply.satisfaction.Dimension;
import cloud.demand.app.modules.order.dto.resp.supply.satisfaction.StockDTO;
import cloud.demand.app.modules.order.dto.resp.supply.satisfaction.StockDTO.StockDetailDTO;
import cloud.demand.app.modules.order.entity.OrderConsensusDemandDetailDO;
import cloud.demand.app.modules.order.entity.OrderCycleElasticMonthSatisfyDO;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.entity.OrderItemSatisfyRateDO;
import cloud.demand.app.modules.order.entity.OrderSatisfyRateDO;
import cloud.demand.app.modules.order.entity.OrderServiceLevelSummaryDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailSatisfyDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailSatisfyVersionDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanVersionDO;
import cloud.demand.app.modules.order.entity.PreDeductOrderItemDO;
import cloud.demand.app.modules.order.entity.std_table.AwsConsensusOrderPerformanceTrackDfDO;
import cloud.demand.app.modules.order.entity.std_table.DwdCrpOrderServiceLevelDetailDO;
import cloud.demand.app.modules.order.enums.OrderAvailableStatusEnum;
import cloud.demand.app.modules.order.enums.OrderElasticType;
import cloud.demand.app.modules.order.enums.OrderFlowEnum;
import cloud.demand.app.modules.order.enums.OrderLabelEnum;
import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.order.enums.OrderStatusEnum;
import cloud.demand.app.modules.order.enums.OrderSupplyPlanMatchTypeEnum;
import cloud.demand.app.modules.order.enums.OrderTypeEnum;
import cloud.demand.app.modules.order.enums.PreDeductOrderStatusEnum;
import cloud.demand.app.modules.order.enums.SupplyPlanVersionStatusEnum;
import cloud.demand.app.modules.order.service.OrderCommonService;
import cloud.demand.app.modules.order.service.OrderFlowService;
import cloud.demand.app.modules.order.service.OrderSatisfyRateService;
import cloud.demand.app.modules.order.service.PerformanceTrackService;
import cloud.demand.app.modules.order.service.PreDeductOrderService;
import cloud.demand.app.modules.order.service.SupplyPlanQueryService;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionDto;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderCategoryEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.log4j.Log4j;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

@Service
@Log4j
public class OrderSatisfyRateServiceImpl implements OrderSatisfyRateService {

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DBHelper shuttleDBHelper;

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private DBHelper ckcldStdCrpSwapDBHelper;

    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    private FlowService flowService;

    @Resource
    private OrderFlowService orderFlowService;

    @Resource
    private SupplyPlanQueryService supplyPlanQueryService;

    @Resource
    private PreDeductOrderService preDeductOrderService;

    @Resource
    private FillerService fillerService;

    @Resource
    private InventoryHealthConfigService inventoryHealthConfigService;

    @Resource
    private DictService dictService;

    @Resource
    private MrpV2DictService mrpV2DictService;

    @Resource
    private PermissionService permissionService;

    @Resource
    private PerformanceTrackService  performanceTrackService;

    @Override
    public List<StockDTO> queryStockForOrderSatisfaction(LocalDate statDate, Integer statDays) {
        String dateSql = "SELECT max(stat_time) FROM cloud_demand.dws_inventory_health_supply_summary_df";
        LocalDate maxStatDate = ckcldDBHelper.getRawOne(LocalDate.class, dateSql);
        if (statDate == null || statDate.isAfter(maxStatDate)) {
            statDate = maxStatDate;
        }
        if (statDays == null) {
            statDays = 14;
        }
        // 获取线上库存的聚合数据
        String onlineStockSql = ORMUtils.getSql("/sql/order/supply/query_online_stock_data.sql");
        List<StockDTO> onlineStockList = ckcldDBHelper.getRaw(StockDTO.class, onlineStockSql, statDate);
        // 获取采购在途的明细数据，有预计交付时间的，计算日期<=预计到货日期<=计算日期+14
        LocalDate now = LocalDate.now();
        String buySql = ORMUtils.getSql("/sql/order/supply/query_stock_buy_data.sql");
        List<StockDetailDTO> buyDetailList = ckcldDBHelper.getRaw(StockDetailDTO.class, buySql,
                statDate, statDate, statDate.plusDays(statDays));
        // 获取采购在途的采购单号
        Set<String> quotaIds = ListUtils.toSet(buyDetailList, StockDetailDTO::getQuotaId);
        // 在星云采购单数据中查询上面的采购单号数据，剔除掉已关联到预约单、订单的数据
        String quotaSql = ORMUtils.getSql("/sql/order/supply/query_quota.sql");
        List<String> availableQuotaIds = shuttleDBHelper.getRaw(String.class, quotaSql, quotaIds);
        // 获取过滤之后的采购在途明细数据
        if (ListUtils.isEmpty(availableQuotaIds)) {
            buyDetailList = new ArrayList<>();
        } else {
            buyDetailList.removeIf(t -> !availableQuotaIds.contains(t.getQuotaId()));
        }
        // 采购在途明细数据生成聚合数据 ，并与线上库存的聚合数据合并的得到结果
        return StockDTO.mergeSum(onlineStockList, buyDetailList);
    }

    @Override
    public List<DemandDTO> queryDemandForOrderSatisfaction(LocalDate statDate, Integer statDays) {
        // 获取需要进行满足度计算的 共识需求
        String consensusDemandSql = ORMUtils.getSql("/sql/order/supply/query_calc_satisfy_consensus_demand.sql");
        LocalDate now = LocalDate.now();
        if (statDate == null) {
            statDate = now;
        }
        if (statDays == null) {
            statDays = 14;
        }
        LocalDate end = statDate.plusDays(statDays);
        List<String> consensusDemands = demandDBHelper.getRaw(String.class, consensusDemandSql, statDate, end);
        if (ListUtils.isEmpty(consensusDemands)) {
            return new ArrayList<>();
        }
        // 针对每一条共识需求，获取其大盘满足需求量以及预扣和履约量
        List<DemandDetailDTO> details = genConsensusDemandSatisfyData(consensusDemands, statDate, end);
        // 聚合合并
        return DemandDTO.buildByDemandDetails(details);
    }

    public List<DemandDetailDTO> genConsensusDemandSatisfyData(List<String> consensusDemandIds, LocalDate statDate, LocalDate end) {
        List<DemandDetailDTO> result = new ArrayList<>();
        if (ListUtils.isEmpty(consensusDemandIds)) {
            return result;
        }
        //通过共识需求id获取共识需求明细数据
        List<OrderConsensusDemandDetailDO> all = demandDBHelper.getAll(OrderConsensusDemandDetailDO.class,
                "where consensus_demand_version_id in (?)", consensusDemandIds);
        Map<String, List<OrderConsensusDemandDetailDO>> orderList = ListUtils.toMapList(all, o -> o.getOrderNumber(),
                o -> o);

        //获取订单维度下的供应方案明细，预扣明细以及履约明细
        for (Entry<String, List<OrderConsensusDemandDetailDO>> entry : orderList.entrySet()) {
            String orderNumber = entry.getKey();
            List<OrderConsensusDemandDetailDO> value = entry.getValue();
            //查询此订单下最新的供应方案明细,只获取大盘满足部分以及共识开始购买时间落在范围内的
            OrderSupplyPlanVersionDO version = supplyPlanQueryService.getAvailableVersion(orderNumber);
            List<OrderSupplyPlanWithDetailDTO> details = supplyPlanQueryService.getAllSupplyPlanWithDetails(
                    version.getId(), orderNumber);
            details = details.stream().filter(o -> o.getMatchType().equals(OrderSupplyPlanMatchTypeEnum.SATISFY.getCode())).collect(
                    Collectors.toList());

            List<OrderSupplyPlanDetailDO> detailInfos = new ArrayList<>();
            for (OrderSupplyPlanWithDetailDTO detail : details) {
                if (ListUtils.isNotEmpty(detail.getDetails())) {
                    detailInfos.addAll(detail.getDetails());
                }
            }
            detailInfos = detailInfos.stream().filter(o -> !o.getConsensusBeginBuyDate().isBefore(statDate)
                    && !o.getConsensusBeginBuyDate().isAfter(end) && o.getSupplyCoreNum() > 0).collect(Collectors.toList());
            //获取预扣数据
            List<PreDeductOrderItemDO> itemList = preDeductOrderService.queryPreDeductItems(orderNumber,
                    PreDeductOrderStatusEnum.VALID_STATUS);

            //获取履约量数据
            LocalDate trackDate = ckcldStdCrpDBHelper.getRawOne(LocalDate.class, "select max(stat_time) from aws_consensus_order_performance_track_df");
            if (trackDate == null || trackDate.isAfter(statDate)) {
                trackDate = statDate;
            }
            List<AwsConsensusOrderPerformanceTrackDfDO> performanceList = ckcldStdCrpDBHelper.getAll(
                    AwsConsensusOrderPerformanceTrackDfDO.class, "where order_number = ? and stat_time = ?",
                    orderNumber, DateUtils.formatDate(trackDate));
            Map<String, List<AwsConsensusOrderPerformanceTrackDfDO>> performanceMap = ListUtils.toMapList(performanceList,
                    o -> String.join("@", o.getZoneName(), o.getInstanceType(),
                            DateUtils.formatDate(o.getBeginBuyDate())), o -> o);
            //将共识需求和供应方案以及预扣按照可用区+实例类型聚合
            Map<String, List<OrderConsensusDemandDetailDO>> consensusMap = ListUtils.toMapList(value,
                    o -> String.join("@", o.getDemandZoneName(), o.getDemandInstanceType()), o -> o);
            Map<String, List<OrderSupplyPlanDetailDO>> supplyMap = ListUtils.toMapList(detailInfos,
                    o -> String.join("@", o.getSupplyZoneName(), o.getSupplyInstanceType()), o -> o);
            Map<String, List<PreDeductOrderItemDO>> deductMap = ListUtils.toMapList(itemList,
                    o -> String.join("@", o.getZoneName(), o.getInstanceType()), o -> o);
            for (Entry<String, List<OrderSupplyPlanDetailDO>> supplyEntry : supplyMap.entrySet()) {
                String key = supplyEntry.getKey();
                List<OrderSupplyPlanDetailDO> supplyList = supplyEntry.getValue();
                List<OrderConsensusDemandDetailDO> demandList = consensusMap.get(key);

                //预扣数据，用于后续分配
                List<PreDeductOrderItemDO> deductList = deductMap.get(key);
                BigDecimal deductCore = NumberUtils.sum(deductList, PreDeductOrderItemDO::getTotalReservedCpuCount);

                //根据共识开始时间进行聚合并生成DemandDetailDTO
                Map<LocalDate, OrderConsensusDemandDetailDO> consensFinalMap = ListUtils.toMap(demandList,
                        OrderConsensusDemandDetailDO::getConsensusBeginBuyDate, o -> o);
                Map<LocalDate, List<OrderSupplyPlanDetailDO>> supplyFinalMap = ListUtils.toMapList(supplyList,
                        OrderSupplyPlanDetailDO::getConsensusBeginBuyDate, o -> o);
                List<DemandDetailDTO> tempList = new ArrayList<>();
                for (Entry<LocalDate, OrderConsensusDemandDetailDO> demandEntry : consensFinalMap.entrySet()) {
                    DemandDetailDTO demandDetailDTO = new DemandDetailDTO();
                    OrderConsensusDemandDetailDO demand = demandEntry.getValue();
                    demandDetailDTO.setOrderNumber(demand.getOrderNumber());
                    demandDetailDTO.setBeginBuyDate(demand.getConsensusBeginBuyDate());
                    demandDetailDTO.setZoneName(demand.getDemandZoneName());
                    demandDetailDTO.setInstanceType(demand.getDemandInstanceType());
                    List<OrderSupplyPlanDetailDO> orderSupplyPlanDetailDOS = supplyFinalMap.get(demandEntry.getKey());
                    demandDetailDTO.setSatisfySupplyCore(NumberUtils.sum(orderSupplyPlanDetailDOS,
                            OrderSupplyPlanDetailDO::getSupplyCoreNum));
                    tempList.add(demandDetailDTO);
                }
                tempList.sort(Comparator.comparing(o -> DateUtils.formatDate(o.getBeginBuyDate())));
                for(int i = 0; i < tempList.size(); i++) {
                    DemandDetailDTO demandDetailDTO = tempList.get(i);
                    BigDecimal satisfySupplyCore = demandDetailDTO.getSatisfySupplyCore();
                    if (deductCore.compareTo(satisfySupplyCore) > 0) {
                        demandDetailDTO.setSatisfySupplyPreDeductCore(satisfySupplyCore);
                        deductCore = deductCore.subtract(satisfySupplyCore);
                    }else {
                        if (deductCore.compareTo(BigDecimal.ZERO) > 0) {
                            demandDetailDTO.setSatisfySupplyPreDeductCore(deductCore);
                            deductCore = BigDecimal.ZERO;
                        }
                    }
                }
                //填充履约量数据
                for (DemandDetailDTO detail : tempList) {
                    String detailKey = String.join("@", detail.getZoneName(), detail.getInstanceType(), DateUtils.formatDate(detail.getBeginBuyDate()));
                    List<AwsConsensusOrderPerformanceTrackDfDO> performance = performanceMap.get(
                            detailKey);
                    detail.setSatisfySupplyKeepPromiseCore(NumberUtils.sum(performance,
                            AwsConsensusOrderPerformanceTrackDfDO::getBuyTotalCore));
                }
                if (ListUtils.isNotEmpty(tempList)) {
                    result.addAll(tempList);
                }
            }
        }
        return result;
    }


    @Override
    public void compensateOrderSatisfactionCalc() {
        // 查询已经到了开始购买日期，且已经制定了供应方案的，却没有主要的满足度信息
        String dateSql = ORMUtils.getSql("/sql/order/supply/query_lose_calc_satisfy_date.sql");
        List<LocalDate> beginBuyDates = demandDBHelper.getRaw(LocalDate.class, dateSql, LocalDate.now());
        if (ListUtils.isEmpty(beginBuyDates)) {
            return;
        }
        OrderSatisfyRateService service = SpringUtil.getBean(OrderSatisfyRateService.class);
        for (LocalDate beginBuyDate : beginBuyDates) {
            try {
                service.orderSatisfactionCalc(beginBuyDate, 14);
            } catch (Exception e) {
                // 告警
                String msg = StrUtil.format("补偿计算【{}】的订单满足度失败，异常信息【{}:{}】",
                        beginBuyDate, e.getClass().getName(), e.getMessage());
                log.warn(msg, e);
                AlarmRobotUtil.doAlarm("orderSatisfactionCalc", msg, null, false);
            }
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = "crp.order.orderSatisfactionCalc", keyScript = "args[0]",waitLockMillisecond = 1000)
    public void orderSatisfactionCalc(LocalDate statDate, Integer statDays) {
        if (statDate == null) {
            statDate = LocalDate.now();
        }
        if (statDays == null) {
            statDays = 14;
        }
        // 库存数据
        List<StockDTO> stockList = queryStockForOrderSatisfaction(statDate, statDays);
        Map<String, StockDTO> stockMap = ListUtils.toMap(stockList, Dimension::mergeKey, Function.identity());
        // 需求数据
        List<DemandDTO> demandList = queryDemandForOrderSatisfaction(statDate, statDays);
        Set<String> orderNumbers = new HashSet<>();
        for (DemandDTO demandDTO : demandList) {
            if (demandDTO != null && demandDTO.getDetails() != null) {
                demandDTO.getDetails().forEach(t -> orderNumbers.add(t.getOrderNumber()));
            }
        }
        if (ListUtils.isEmpty(orderNumbers)) {
            return;
        }

        // 获取订单信息
        List<OrderDetailResp> orders = orderCommonService.queryAvailableOrderDetails(new ArrayList<>(orderNumbers));
        Map<String, OrderInfoDO> orderMap = new HashMap<>();
        for (OrderDetailResp order : orders) {
            if (order == null) {
                continue;
            }
            orderMap.put(order.getOrderNumber(), order);
        }

        List<OrderSatisfyRateDO> satisfyResult = new ArrayList<>();
        List<OrderItemSatisfyRateDO> itemResult = new ArrayList<>();
        Set<String> needHandlerOrderNumbers = new HashSet<>(); // 需要打标的订单号，计算日期 = 开始购买日期的
        for (DemandDTO demandDTO : demandList) {
            String key = Dimension.mergeKey(demandDTO);
            StockDTO stock = stockMap.get(key);
            // 需求、库存进行对冲
            demandDTO.stock(stock);
            // 将对冲结果生成orderSatisfyRate数据上
            List<OrderItemSatisfyRateDO> orderItemRates = demandDTO.shareToOrderItems(statDate, orderMap);
            for (OrderItemSatisfyRateDO itemRate : orderItemRates) {
                if (itemRate.getMainResult()) {
                    needHandlerOrderNumbers.add(itemRate.getOrderNumber());
                }
                itemResult.add(itemRate);
            }
            // 满足度日切片数据
            satisfyResult.add(demandDTO.convertToSatisfyRateDO(statDate, statDays));
        }

        // 先逻辑删除旧的数据
        demandDBHelper.executeRaw("update order_item_satisfy_rate set deleted = 1 "
                        + " where calc_date = ? and deleted = 0 and match_type = 'SATISFY' and order_number in (?)",
                statDate, orderNumbers);
        if (ListUtils.isNotEmpty(needHandlerOrderNumbers)) {
            // 再将待重新插入的打标订单数据 的历史打标信息改为不打标，防止修改订单开始购买日期引起重复打标的满足度数据
            demandDBHelper.executeRaw("update order_item_satisfy_rate set main_result = 0 "
                            + " where match_type = 'SATISFY' and main_result = 1 and deleted = 0 and order_number in (?) ",
                    needHandlerOrderNumbers);
        }
        // 保存订单明细级别的满足度计算数据
        demandDBHelper.insert(itemResult);

        // 先逻辑删除旧的数据
        demandDBHelper.executeRaw("update order_satisfy_rate set deleted = 1 where begin_date = ? and deleted = 0 ",
                statDate);
        // 保存库存维度数据：用于校验库存、需求对冲过程和对冲剩余结果
        demandDBHelper.insert(satisfyResult);

        // 将订单明细级别的满足度计算数据分摊到供应方案明细上
        needHandlerOrderNumbers.forEach(this::calcSupplyPlanDetailMatchCore);

        OrderSatisfyRateService orderSatisfyRateService = SpringUtil.getBean(OrderSatisfyRateService.class);
        needHandlerOrderNumbers.forEach(orderNumber -> {
            // 等待10秒，异步进行共识需求满足量更新
            SupplyPlanOperateServiceImpl.scheduledExecutor.schedule(
                    () -> orderSatisfyRateService.updateOrderConsensusByNumChange(orderNumber),
                    10, TimeUnit.SECONDS);
        });
    }

    @Override
    @Transactional("demandTransactionManager")
    public void orderCumulativeDeductCal(LocalDate statDate) {
        //找出所有开始日期在statDate之前的订单明细满足数据
        List<OrderItemSatisfyRateDO> rateList = demandDBHelper.getAll(OrderItemSatisfyRateDO.class,
                "where begin_buy_date < ? and main_result = 1 and match_type = ? ",
                DateUtils.formatDate(statDate), OrderSupplyPlanMatchTypeEnum.SATISFY.getCode());
        Set<String> orderNumbers = new HashSet<>();
        for (OrderItemSatisfyRateDO rate : rateList) {
            orderNumbers.add(rate.getOrderNumber());
        }

        // 仅处理新增类型的订单、一次性弹性的订单
        // 移除日弹性、周弹性、月弹性订单，这些有别的计算方式
        String sql = ORMUtils.getSql("/sql/order/satisfy/remove_cycle_elastic_order.sql");
        List<String> rangeOrders = demandDBHelper.getRaw(String.class, sql,
                OrderElasticType.ONCE_TIME.getTypeName(), orderNumbers);
        orderNumbers =  new HashSet<>(rangeOrders);
        rateList.removeIf(o -> !rangeOrders.contains(o.getOrderNumber()));

        //找出所有订单的预扣开始时间在当天之前的生效数据
        List<PreDeductOrderItemDO> dudectList = demandDBHelper.getAll(PreDeductOrderItemDO.class,
                "where order_number in (?) and start_pre_deduct_date <= ? and status in (?)",
                new ArrayList<>(orderNumbers), DateUtils.formatDate(statDate), PreDeductOrderStatusEnum.VALID_STATUS);
        Map<String, List<PreDeductOrderItemDO>> deductMap = ListUtils.toMapList(dudectList,
                o -> String.join("@", o.getOrderNumber(), o.getZoneName(), o.getInstanceType()), o -> o);

        Map<String, List<OrderItemSatisfyRateDO>> rateMap = ListUtils.toMapList(rateList,
                o -> String.join("@", o.getOrderNumber(), o.getZoneName(), o.getInstanceType()), o -> o);

        Set<String> changeOrderNumbers = new HashSet<>();
        for (Entry<String, List<OrderItemSatisfyRateDO>> entry : rateMap.entrySet()) {
            String key = entry.getKey();
            List<OrderItemSatisfyRateDO> value = entry.getValue();
            List<PreDeductOrderItemDO> preDeductList = deductMap.get(key);
            BigDecimal deductCore = NumberUtils.sum(preDeductList, PreDeductOrderItemDO::getTotalReservedCpuCount);
            BigDecimal deductGpu = NumberUtils.sum(preDeductList, PreDeductOrderItemDO::totalGpuActualCountGetter);
            value.sort(Comparator.comparing(o -> DateUtils.formatDate(o.getBeginBuyDate())));
            for (int i = 0; i < value.size(); i++) {
                OrderItemSatisfyRateDO rate = value.get(i);
                BigDecimal normalTotalMatchCore = rate.getTotalMatchCore();
                BigDecimal normalTotalMatchGpu = rate.getTotalMatchGpu();
                BigDecimal tempCore;
                if (deductCore.compareTo(rate.getConsensusCore()) > 0) {
                    tempCore = rate.getConsensusCore();
                    deductCore = deductCore.subtract(rate.getConsensusCore());
                    rate.setTotalMatchCore(tempCore.max(rate.getTotalMatchCore()));
                    rate.setCumulativePreDeductCore(tempCore);
                }else {
                    if (deductCore.compareTo(BigDecimal.ZERO) > 0) {
                        tempCore = deductCore;
                        deductCore = BigDecimal.ZERO;
                        rate.setTotalMatchCore(tempCore.max(rate.getTotalMatchCore()));
                        rate.setCumulativePreDeductCore(tempCore);
                    }else {
                        rate.setCumulativePreDeductCore(BigDecimal.ZERO);
                    }
                }

                BigDecimal tempGpu;
                if (deductGpu.compareTo(rate.getConsensusGpu()) > 0) {
                    tempGpu = rate.getConsensusGpu();
                    deductGpu = deductGpu.subtract(rate.getConsensusGpu());
                    rate.setTotalMatchGpu(tempGpu.max(rate.getTotalMatchGpu()));
                    rate.setCumulativePreDeductGpu(rate.getTotalMatchGpu());
                }else {
                    if (deductGpu.compareTo(BigDecimal.ZERO) > 0) {
                        tempGpu = deductGpu;
                        deductGpu = BigDecimal.ZERO;
                        rate.setTotalMatchGpu(tempGpu.max(rate.getTotalMatchGpu()));
                        rate.setCumulativePreDeductGpu(rate.getTotalMatchGpu());
                    }else {
                        rate.setCumulativePreDeductGpu(BigDecimal.ZERO);
                    }
                }
                if (normalTotalMatchCore != null && rate.getTotalMatchCore() != null
                        && normalTotalMatchCore.compareTo(rate.getTotalMatchCore()) != 0) {
                    changeOrderNumbers.add(rate.getOrderNumber());
                }
                if (normalTotalMatchGpu != null && rate.getTotalMatchGpu() != null
                        && normalTotalMatchGpu.compareTo(rate.getTotalMatchGpu()) != 0) {
                    changeOrderNumbers.add(rate.getOrderNumber());
                }
            }
        }
        demandDBHelper.update(rateList);
        if (changeOrderNumbers.size() > 0) {
            OrderSatisfyRateService orderSatisfyRateService = SpringUtil.getBean(OrderSatisfyRateService.class);
            SupplyPlanOperateServiceImpl.scheduledExecutor.schedule(
                    () -> {
                        for (String changeOrderNumber : changeOrderNumbers) {
                            try {
                                orderSatisfyRateService.calcSupplyPlanDetailMatchCore(changeOrderNumber);
                            } catch (Exception e) {
                                String msg = StrUtil.format("calcSupplyPlanDetailMatchCore 异常，订单号【{}】, 异常信息【{}】",
                                        changeOrderNumber, ExceptionUtil.getMessage(e));
                                log.error(msg, e);
                                AlarmRobotUtil.doAlarm("calcSupplyPlanDetailMatchCore", msg, null, false);
                            }
                        }
                    },
                    10, TimeUnit.SECONDS);
        }
    }

    @Override
    public void calcGpuMatchRate(LocalDate minBeginBuyDate) {
        if (minBeginBuyDate == null) {
            minBeginBuyDate = LocalDate.now();
        }
        String sql = ORMUtils.getSql("/sql/order/supply/query_calc_gpu_satisfy_order_number.sql");
        List<String> orderNumbers = demandDBHelper.getRaw(String.class, sql, minBeginBuyDate);
        if (ListUtils.isEmpty(orderNumbers)) {
            return;
        }
        OrderSatisfyRateService orderSatisfyRateService = SpringUtil.getBean(OrderSatisfyRateService.class);
        for (String orderNumber : orderNumbers) {
            try {
                orderSatisfyRateService.calcGpuSatisfyOneOrder(orderNumber);
            } catch (Exception e) {
                String msg = StrUtil.format("calcGpuSatisfyOneOrder 异常，订单号【{}】, 异常信息【{}】",
                        orderNumber, ExceptionUtil.getMessage(e));
                log.error(msg, e);
                AlarmRobotUtil.doAlarm("calcGpuSatisfyOneOrder", msg, null, false);
            }
            try {
                refreshSatisfyForNotSatisfyMatchOneOrder(orderNumber);
            } catch (Exception e) {
                String msg = StrUtil.format(
                        "refreshSatisfyForNotSatisfyMatchOneOrder 计算采购/搬迁满足供应方案的满足度异常，"
                                + "订单号【{}】, 异常信息【{}】",
                        orderNumber, ExceptionUtil.getMessage(e));
                AlarmRobotUtil.doAlarm("refreshSatisfyForNotSatisfyMatchOneOrder",
                        msg, null, false);
                log.error(msg, e);
            }
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    @TaskLog(taskName = "calcGpuSatisfyOneOrder")
    public void calcGpuSatisfyOneOrder(String orderNumber) {
        OrderInfoDO order = orderCommonService.getOrderInfo(orderNumber, OrderAvailableStatusEnum.AVAILABLE.getCode());
        if (order == null || !Ppl13weekProductTypeEnum.GPU.getName().equals(order.getProduct())) {
            return;
        }
        if (order.cycleElasticReturnTrue()) {
            // 周期性弹性计算
            calcElasticOrderSatisfyOneOrder(orderNumber);
            return;
        }

        OrderSupplyPlanVersionDO version = supplyPlanQueryService.getAvailableVersion(orderNumber);
        if (version == null) {
            return;
        }
        List<OrderSupplyPlanDetailWithPlanDTO>  supplyPlanDetails = supplyPlanQueryService
                .getListDetailWithPlanByVersionId(version.getId(), orderNumber);
        supplyPlanDetails.removeIf(o -> o.getPlan() == null
                || !Objects.equals(o.getPlan().getMatchType(), OrderSupplyPlanMatchTypeEnum.SATISFY.getCode()));

        List<PreDeductOrderItemDO> dudectList = demandDBHelper.getAll(PreDeductOrderItemDO.class,
                "where order_number = ? and status in (?)",
                orderNumber, PreDeductOrderStatusEnum.VALID_STATUS);

        List<OrderItemSatisfyRateDO>  itemResult = OrderItemSatisfyRateDO.fromGpuConsensusDetails(supplyPlanDetails,
                order, LocalDate.now(), dudectList);

        // 先逻辑删除旧的数据
        demandDBHelper.executeRaw("update order_item_satisfy_rate set deleted = 1 "
                        + " where deleted = 0 and match_type = 'SATISFY' and product = ? and order_number = ? ",
                Ppl13weekProductTypeEnum.GPU.getName(), orderNumber);
        // 保存订单明细级别的满足度计算数据
        demandDBHelper.insertBatchWithoutReturnId(itemResult);

        // 将订单明细级别的满足度计算数据分摊到供应方案明细上
        calcSupplyPlanDetailMatchCore(orderNumber);
    }

    @Override
    @Transactional("demandTransactionManager")
    public void opsOrderForError2_21(String orderNumber) {
        String sql = "update order_item_satisfy_rate set total_match_core = total_system_match_core  "
                + " where deleted  = 0 and main_result  = 1 and total_system_match_core != 0 and total_match_core = 0 "
                + " and match_type = 'SATISFY' and order_number = ?";
        int row = demandDBHelper.executeRaw(sql, orderNumber);
        if (row > 0) {
            calcSupplyPlanDetailMatchCore(orderNumber);
        }
    }

    /**
     * 计算出供应方案明细的满足度信息
     */
    @Override
    @Transactional("demandTransactionManager")
    public void calcSupplyPlanDetailMatchCore(String orderNumber) {
        // 获取当前生效的供应方案版本
        OrderSupplyPlanVersionDO version = supplyPlanQueryService.getAvailableVersion(orderNumber);
        if (version == null) {
            return;
        }
        List<OrderSupplyPlanWithDetailDTO> plans = supplyPlanQueryService.getAllSupplyPlanWithDetails(
                version.getId(), orderNumber);
        if (ListUtils.isEmpty(plans)) {
            return;
        }

        // 查询满足核心数，分摊到订单明细的每个供应方案明细
        List<OrderItemSatisfyRateDO> satisfyRates = queryOrderItemSatisfy(orderNumber);
        OrderInfoDO order = orderCommonService.getOrderInfo(orderNumber, OrderAvailableStatusEnum.AVAILABLE.getCode());
        // CVM、GPU、裸金属才计算满足度
        List<String> categorys = ListUtils.newArrayList(YunxiaoOrderCategoryEnum.CVM.getCode(),
                YunxiaoOrderCategoryEnum.GPU.getCode(), YunxiaoOrderCategoryEnum.BARE_METAL.getCode());
        if (order == null || !categorys.contains(order.getOrderCategory())) {
            return;
        }
        if (ListUtils.newArrayList(Ppl13weekProductTypeEnum.CDC.getName(),
                Ppl13weekProductTypeEnum.CDZ.getName(),
                Ppl13weekProductTypeEnum.TEZ.getName(),
                Ppl13weekProductTypeEnum.ES.getName(),
                Ppl13weekProductTypeEnum.CDW.getName(),
                Ppl13weekProductTypeEnum.DATABASE.getName()).contains(order.getProduct())) {
            // 这些产品暂不计算满足度
            return;
        }
        if (ListUtils.isEmpty(satisfyRates)) {
            if (order.getBeginBuyDate() == null || order.getBeginBuyDate().isAfter(LocalDate.now())) {
                return;
            }
        }
        Map<String, OrderItemSatisfyRateDO> satisfyMap = ListUtils.toMap(satisfyRates, o ->
                String.join("@", o.getZoneName(), o.getInstanceType(),
                        DateUtils.formatDate(o.getBeginBuyDate()), o.getMatchType()),
                Function.identity());

        OrderSupplyPlanDetailSatisfyVersionDO satisfyVersion = OrderSupplyPlanDetailSatisfyVersionDO
                .createBySystemCalc(orderNumber);
        demandDBHelper.insert(satisfyVersion);

        // 有人工修改满足度记录的方案明细
        String comdSupplySql = ORMUtils.getSql("/sql/order/supply/query_comd_supply_satisfy_detail_ids.sql");
        List<Long> comdSupplyDetailIds = demandDBHelper.getRaw(Long.class, comdSupplySql, orderNumber);
        // 有人工修改满足度记录的方案明细对应的有效的满足度记录
        WhereSQL where = new WhereSQL()
                .and(" available = 1 ")
                .and(" supply_detail_id in (?) ", comdSupplyDetailIds);
        List<OrderSupplyPlanDetailSatisfyDO> normalSatisfyList = demandDBHelper.getAll(
                OrderSupplyPlanDetailSatisfyDO.class, where.getSQL(), where.getParams());
        Map<Long, OrderSupplyPlanDetailSatisfyDO> normalSatisfyMap = ListUtils.toMap(normalSatisfyList,
                OrderSupplyPlanDetailSatisfyDO::getSupplyDetailId, Function.identity());

        boolean isCycle = order.cycleElasticReturnTrue();
        boolean isGpu = Ppl13weekProductTypeEnum.GPU.getName().equals(order.getProduct());
        List<OrderSupplyPlanDetailSatisfyDO> insertSatisfyList = new ArrayList<>();
        List<OrderSupplyPlanDetailDO> notSatisfyMatchDetails = new ArrayList<>();
        for (OrderSupplyPlanWithDetailDTO plan : plans) {
            if (OrderSupplyPlanMatchTypeEnum.SATISFY.getCode().equals(plan.getMatchType()) || isCycle) {
                // 仅大盘满足进行满足核心数的分摊、 周期性弹性订单不区分满足方式都用此方式分摊
                for (OrderSupplyPlanDetailDO detail : plan.getDetails()) {
                    OrderSupplyPlanDetailSatisfyDO satisfyData =  new OrderSupplyPlanDetailSatisfyDO();
                    satisfyData.setSupplyDetailId(detail.getId());
                    satisfyData.setAvailable(true);
                    satisfyData.setSatisfyVersionId(satisfyVersion.getId());
                    satisfyData.setOrderNumber(detail.getOrderNumber());
                    satisfyData.setOrderNumberId(detail.getAfterConsensusOrderNumberId());

                    OrderItemSatisfyRateDO satisfy = satisfyMap.get(String.join("@",
                            detail.getSupplyZoneName(), detail.getSupplyInstanceType(),
                            DateUtils.formatDate(detail.getConsensusBeginBuyDate()),
                            plan.getMatchType()));
                    handlerSatisfyData(satisfy, satisfyData, detail, isGpu);

                    if (!isGpu && comdSupplyDetailIds.contains(detail.getId())) {
                        // 有人工修改满足度记录的方案明细，直接使用其原始有效的满足度结果
                        OrderSupplyPlanDetailSatisfyDO normal = normalSatisfyMap.get(detail.getId());
                        if (normal != null) {
                            satisfyData.setTotalResultMatchCore(normal.getTotalResultMatchCore());
                        }
                    }
                    insertSatisfyList.add(satisfyData);
                }
            } else {
                notSatisfyMatchDetails.addAll(plan.getDetails());
            }
        }
        // 写入大盘满足供应方案明细的满足度信息
        insertSatisfyListAndUpdateNormalToNotAvailable(insertSatisfyList, true);
        if (!isCycle) {
            // 非周期性弹性订单，采购搬迁满足方式的满足度计算
            // 写入非大盘满足的供应方案明细的满足度信息
            calcSupplyPlanDetailMatchCoreForNotSatisfyMatch(notSatisfyMatchDetails, satisfyVersion);
        }
    }

    private void handlerSatisfyData(OrderItemSatisfyRateDO satisfy, OrderSupplyPlanDetailSatisfyDO satisfyData,
            OrderSupplyPlanDetailDO detail, boolean isGpu) {
        if (satisfy == null) {
            satisfyData.setMatchCore(BigDecimal.ZERO);
            satisfyData.setPreDeductCore(BigDecimal.ZERO);
            satisfyData.setKeepPromiseCore(BigDecimal.ZERO);
            satisfyData.setWaitStockCore(BigDecimal.ZERO);
            satisfyData.setOrderItemConsensusCore(BigDecimal.ZERO);
            satisfyData.setTotalSystemMatchCore(BigDecimal.ZERO);
            satisfyData.setTotalResultMatchCore(BigDecimal.ZERO);
            satisfyData.setTotalResultMatchGpu(BigDecimal.ZERO);
            satisfyData.setTotalSystemMatchGpu(BigDecimal.ZERO);
        } else {
            // 方案明细供应核心数占订单明细共识需求核心数的比例
            BigDecimal rate = BigDecimal.ZERO;
            if (satisfy.getConsensusCore().compareTo(BigDecimal.ZERO) != 0) {
                rate = new BigDecimal(detail.getSupplyCoreNum())
                        .divide(satisfy.getConsensusCore(), 4, RoundingMode.HALF_UP);
            }
            BigDecimal gpuRate = BigDecimal.ZERO;
            if (isGpu && satisfy.getConsensusGpu().compareTo(BigDecimal.ZERO) != 0) {
                gpuRate = new BigDecimal(detail.getSupplyGpuNum())
                        .divide(satisfy.getConsensusGpu(), 4, RoundingMode.HALF_UP);
            }
            // 按比例分摊
            satisfyData.setMatchCore(satisfy.getMatchCore().multiply(rate));
            satisfyData.setPreDeductCore(satisfy.getPreDeductCore().multiply(rate));
            satisfyData.setKeepPromiseCore(satisfy.getKeepPromiseCore().multiply(rate));
            satisfyData.setWaitStockCore(satisfy.getWaitStockCore().multiply(rate));
            satisfyData.setOrderItemConsensusCore(satisfy.getConsensusCore());
            BigDecimal totalSystemMatchCore = satisfy.getTotalSystemMatchCore().multiply(rate);
            satisfyData.setTotalSystemMatchCore(totalSystemMatchCore);
            BigDecimal totalMatchCore = satisfy.getTotalMatchCore().multiply(rate);
            satisfyData.setTotalResultMatchCore(totalMatchCore);
            BigDecimal totalMatchGpu = satisfy.getTotalMatchGpu().multiply(gpuRate);
            satisfyData.setTotalSystemMatchGpu(totalMatchGpu);
            satisfyData.setTotalResultMatchGpu(totalMatchGpu);
        }
    }

    @Override
    public void opsCalcForNotSatisfyMatch(String inputOrderNumber) {
        List<String> orderNumbers = new ArrayList<>();
        if (StringUtils.isBlank(inputOrderNumber)) {
            String sql = ORMUtils.getSql("/sql/order/supply/no_satisfy_data_order_number.sql");
            orderNumbers = demandDBHelper.getRaw(String.class, sql);
        } else {
            orderNumbers.add(inputOrderNumber);
        }
        if (ListUtils.isEmpty(orderNumbers)) {
            return;
        }
        for (String orderNumber : orderNumbers) {
            OrderSupplyPlanVersionDO version = supplyPlanQueryService.getAvailableVersion(orderNumber);
            if (version == null) {
                continue;
            }
            List<OrderSupplyPlanWithDetailDTO> plans = supplyPlanQueryService.getAllSupplyPlanWithDetails(
                    version.getId(), orderNumber);
            if (ListUtils.isEmpty(plans)) {
                continue;
            }
            List<OrderSupplyPlanDetailDO> notSatisfyMatchDetails = new ArrayList<>();
            for (OrderSupplyPlanWithDetailDTO plan : plans) {
                if (!OrderSupplyPlanMatchTypeEnum.SATISFY.getCode().equals(plan.getMatchType())) {
                    notSatisfyMatchDetails.addAll(plan.getDetails());
                }
            }
            if (ListUtils.isEmpty(notSatisfyMatchDetails)) {
                continue;
            }
            try {
                OrderSatisfyRateService orderSatisfyRateService = SpringUtil.getBean(OrderSatisfyRateService.class);
                orderSatisfyRateService.calcSupplyPlanDetailMatchCoreForNotSatisfyMatch(notSatisfyMatchDetails, null);
            } catch (Exception e) {
                // 告警
                String msg = StrUtil.format("计算订单号【{}】非大盘满足的供应方案计算生成方案明细的满足度信息失败，异常信息【{}:{}】",
                        orderNumber, e.getClass().getName(), e.getMessage());
                log.warn(msg, e);
                AlarmRobotUtil.doAlarm("calcSupplyPlanDetailMatchCoreForNotSatisfyMatch", msg, null, false);
            }
        }
    }

    @Override
    public void refreshSatisfyForNotSatisfyMatch(boolean includeCloseOrder) {
        // 获取采购、搬迁满足的订单
        String sql = ORMUtils.getSql("/sql/order/satisfy/not_satisfy_data_order_number.sql");
        List<String> nodeList = ListUtils.newArrayList(OrderNodeCodeEnum.node_order_supply.getCode(),
                OrderNodeCodeEnum.node_order_following.getCode());
        if (includeCloseOrder) {
            nodeList.add(OrderNodeCodeEnum.node_order_close.getCode());
        }
        List<String> orderNumbers = demandDBHelper.getRaw(String.class, sql,
                OrderAvailableStatusEnum.AVAILABLE.getCode(),
                SupplyPlanVersionStatusEnum.available.getCode(),
                ListUtils.newArrayList(OrderSupplyPlanMatchTypeEnum.MOVE.getCode(),
                        OrderSupplyPlanMatchTypeEnum.BUY.getCode()),
                nodeList);
        for (String orderNumber : orderNumbers) {
            try {
                refreshSatisfyForNotSatisfyMatchOneOrder(orderNumber);
            } catch (Exception e) {
                AlarmRobotUtil.doAlarm("refreshSatisfyForNotSatisfyMatch",
                        orderNumber + " 计算采购/搬迁满足供应方案的满足度异常，" + ExceptionUtil.getMessage(e),
                        null, false);
            }
        }
    }

    @Override
    public void refreshSatisfyForNotSatisfyMatchOneOrder(String orderNumber) {
        OrderSupplyPlanVersionDO version = supplyPlanQueryService.getAvailableVersion(orderNumber);
        List<OrderSupplyPlanDetailWithPlanDTO> supplys = supplyPlanQueryService.getListDetailWithPlanByVersionId(
                version.getId(), orderNumber);
        calcSupplyPlanDetailMatchCoreForNotSatisfyMatch(supplys, null);
    }

    /**
     *  非大盘满足的供应方案计算生成方案明细的满足度信息
     * @param details 非大盘满足的供应方案明细
     * @param satisfyVersion 方案明细满足度版本信息，为null时会自动创建
     */
    @Override
    @Transactional("demandTransactionManager")
    public void calcSupplyPlanDetailMatchCoreForNotSatisfyMatch(List<? extends OrderSupplyPlanDetailDO> details,
            OrderSupplyPlanDetailSatisfyVersionDO satisfyVersion) {
        if (ListUtils.isEmpty(details)) {
            return;
        }
        List<Long> planIds = ListUtils.transform(details, OrderSupplyPlanDetailDO::getPlanId);
        // 非大盘满足的供应方案以及满足方式
        List<Long> notSatisfyMatchPlanIds = notSatisfyMatchPlanIds(planIds);
        if (ListUtils.isEmpty(notSatisfyMatchPlanIds)) {
            return;
        }
        // 待处理的供应方案明细（非大盘满足的）
        List<OrderSupplyPlanDetailDO> waitHandlers = new ArrayList<>();
        for (OrderSupplyPlanDetailDO detail : details) {
            if (notSatisfyMatchPlanIds.contains(detail.getPlanId())) {
                waitHandlers.add(detail);
            }
        }
        if (ListUtils.isEmpty(waitHandlers)) {
            return;
        }

        String orderNumber = waitHandlers.get(0).getOrderNumber();

        //获取orderNumber的开始购买时间
        OrderInfoDO orderInfo = orderCommonService.getOrderInfo(orderNumber,
                OrderAvailableStatusEnum.AVAILABLE.getCode());
        // CVM、GPU、裸金属才计算满足度
        List<String> categorys = ListUtils.newArrayList(YunxiaoOrderCategoryEnum.CVM.getCode(),
                YunxiaoOrderCategoryEnum.GPU.getCode(), YunxiaoOrderCategoryEnum.BARE_METAL.getCode());
        if (orderInfo == null || !categorys.contains(orderInfo.getOrderCategory())) {
            return;
        }
        if (ListUtils.newArrayList(Ppl13weekProductTypeEnum.CDC.getName(),
                Ppl13weekProductTypeEnum.CDZ.getName(),
                Ppl13weekProductTypeEnum.TEZ.getName(),
                Ppl13weekProductTypeEnum.ES.getName(),
                Ppl13weekProductTypeEnum.CDW.getName(),
                Ppl13weekProductTypeEnum.DATABASE.getName()).contains(orderInfo.getProduct())) {
            // 这些产品暂不计算满足度
            return;
        }
        if (orderInfo.cycleElasticReturnTrue()) {
            // 周期性弹性不用此方式
            return;
        }
        //获取所有planId的实际到货情况
        List<DeliverRecordDTO> totalRecord = new ArrayList<>();
        for (Long planId : notSatisfyMatchPlanIds) {
            List<DeliverRecordDTO> deliverRecordDTOS = supplyPlanQueryService.deliverTrack(planId);
            if (ListUtils.isNotEmpty(deliverRecordDTOS)) {
                totalRecord.addAll(deliverRecordDTOS);
            }
        }

        boolean isGpu = Ppl13weekProductTypeEnum.GPU.getName().equals(orderInfo.getProduct());
        Map<String, DeliverRecordDTO> recordMap = ListUtils.toMap(totalRecord,
                o -> String.join("@", o.getBizId(), o.getInstanceType(), o.getZoneName()), o -> o);

        List<OrderSupplyPlanDetailSatisfyDO> insertSatisfyList = new ArrayList<>();
        Map<String, List<OrderSupplyPlanDetailDO>> bizIdToDetail = new HashMap<>();
        Map<String, Integer> supplyNum = new HashMap<>();
        // 采购搬迁满足，根据关联采购单/M单状态计算：
        for (OrderSupplyPlanDetailDO detail : waitHandlers) {
            //首先初始化所有的供应方案满足明细
            OrderSupplyPlanDetailSatisfyDO satisfyData =  new OrderSupplyPlanDetailSatisfyDO();
            satisfyData.setSupplyDetailId(detail.getId());
            satisfyData.setAvailable(true);
            satisfyData.setOrderNumber(detail.getOrderNumber());
            satisfyData.setOrderNumberId(detail.getAfterConsensusOrderNumberId());
            BigDecimal totalMatchCore = BigDecimal.ZERO;
            satisfyData.setMatchCore(totalMatchCore);
            satisfyData.setTotalSystemMatchCore(totalMatchCore);
            satisfyData.setTotalResultMatchCore(totalMatchCore);
            satisfyData.setTotalSystemMatchGpu(BigDecimal.ZERO);
            satisfyData.setTotalResultMatchGpu(BigDecimal.ZERO);
            insertSatisfyList.add(satisfyData);
            String supplyBizId = detail.getSupplyBizId();
            String key = String.join("@", supplyBizId, detail.getSupplyInstanceType(), detail.getSupplyZoneName());
            List<OrderSupplyPlanDetailDO> detailList = bizIdToDetail.getOrDefault(key, new ArrayList<>());
            detailList.add(detail);
            bizIdToDetail.put(key, detailList);
            if (isGpu) {
                supplyNum.put(String.valueOf(detail.getId()), detail.getSupplyGpuNum());
            } else {
                supplyNum.put(String.valueOf(detail.getId()), detail.getSupplyCoreNum());
            }
        }

        Map<String, OrderSupplyPlanDetailSatisfyDO> satisfyMap = ListUtils.toMap(insertSatisfyList,
                o -> String.valueOf(o.getSupplyDetailId()), o -> o);

        //根据交付情况更新满足值
        for (Entry<String, DeliverRecordDTO> entry : recordMap.entrySet()) {
            DeliverRecordDTO value = entry.getValue();
            String bizId = value.getBizId();
            String key = String.join("@", bizId, value.getInstanceType(), value.getZoneName());
            List<OrderSupplyPlanDetailDO> detailDOList = bizIdToDetail.get(key);
            if (ListUtils.isNotEmpty(detailDOList) && ListUtils.isNotEmpty(value.getActualDeliverList())) {
                //获取到货情况
                List<DeliverItem> actualDeliverList = value.getActualDeliverList();
                Map<LocalDate, AtomicInteger> actualNumMap = actualDeliverList.stream()
                        .filter(o -> !o.getDate().isAfter(DateUtils.today()))
                        .collect(Collectors.toMap(DeliverItem::getDate, o -> new AtomicInteger(o.getNum())));
                List<Entry<LocalDate, AtomicInteger>> entryList = new ArrayList<>(actualNumMap.entrySet());
                ListUtils.sortAscNullLast(entryList, Entry::getKey);
                detailDOList.sort(Comparator.comparing(o -> DateUtils.formatDate(o.getConsensusBeginBuyDate())));
                A:for (int i = 0; i < detailDOList.size(); i++) {
                    OrderSupplyPlanDetailDO detail = detailDOList.get(i);
                    for (Entry<LocalDate, AtomicInteger> atomicIntegerEntry : entryList) {
                        Integer needNum = supplyNum.get(String.valueOf(detail.getId()));
                        if (needNum <= 0) {
                            continue A;
                        }
                        LocalDate actualDate = atomicIntegerEntry.getKey();
                        if (actualDate.isAfter(detail.getConsensusBeginBuyDate())) {
                            continue;
                        }
                        OrderSupplyPlanDetailSatisfyDO satisfy = satisfyMap.get(
                                String.valueOf(detail.getId()));
                        int actualNum = atomicIntegerEntry.getValue().get();
                        if (actualNum <= 0) {
                            continue;
                        }
                        if (actualNum >= needNum) {
                            actualNum -= needNum;
                            supplyNum.put(String.valueOf(detail.getId()), 0);
                            matchCalc(satisfy, needNum, detail, isGpu);
                        }else {
                            matchCalc(satisfy, actualNum, detail, isGpu);
                            needNum -= actualNum;
                            supplyNum.put(String.valueOf(detail.getId()), needNum);
                            actualNum = 0;
                        }
                        atomicIntegerEntry.getValue().set(actualNum);
                    }
                }
            }
        }

        Map<Long, OrderSupplyPlanDetailSatisfyDO> newMap = ListUtils.toMap(insertSatisfyList,
                OrderSupplyPlanDetailSatisfyDO::getSupplyDetailId, Function.identity());

        List<OrderSupplyPlanDetailSatisfyDO> normals = demandDBHelper.getAll(OrderSupplyPlanDetailSatisfyDO.class,
                "where order_number = ? and available = 1 and deleted = 0 ", orderNumber);
        boolean allSame = true;
        for (OrderSupplyPlanDetailSatisfyDO normal : normals) {
            OrderSupplyPlanDetailSatisfyDO newData = newMap.remove(normal.getSupplyDetailId());
            if (newData == null) {
                continue;
            }
            if (isGpu) {
                if (normal.getTotalResultMatchGpu().compareTo(newData.getTotalResultMatchGpu()) != 0) {
                    // 存在满足量不一致的情况，说明采购、搬迁交付量有变化
                    allSame = false;
                    break;
                }
            } else {
                if (normal.getTotalResultMatchCore().compareTo(newData.getTotalResultMatchCore()) != 0) {
                    // 存在满足量不一致的情况，说明采购、搬迁交付量有变化
                    allSame = false;
                    break;
                }
            }
        }
        if (allSame && ListUtils.isEmpty(newMap)) {
            // 相同供应方案版本，且满足度信息一致，不做任何处理
            return;
        }

        if (satisfyVersion == null) {
            satisfyVersion = OrderSupplyPlanDetailSatisfyVersionDO.createBySystemCalc(orderNumber);
        }
        if (satisfyVersion.getId() == null) {
            demandDBHelper.insert(satisfyVersion);
        }
        for (OrderSupplyPlanDetailSatisfyDO item : insertSatisfyList) {
            item.setSatisfyVersionId(satisfyVersion.getId());
        }
        insertSatisfyListAndUpdateNormalToNotAvailable(insertSatisfyList, true);
    }

    private void matchCalc(OrderSupplyPlanDetailSatisfyDO satisfy, int num,
            OrderSupplyPlanDetailDO detail, boolean isGpu) {
        BigDecimal core = new BigDecimal(num);
        if (isGpu) {
            BigDecimal numBig =  BigDecimal.valueOf(num);
            core =  NumberUtils.divide(detail.getSupplyOneCpuNum(), detail.getSupplyOneGpuNum(), 6)
                    .multiply(numBig);
            satisfy.setTotalSystemMatchGpu(satisfy.getTotalSystemMatchGpu().add(numBig));
            satisfy.setTotalResultMatchGpu(satisfy.getTotalResultMatchGpu().add(numBig));
        }
        satisfy.setMatchCore(satisfy.getMatchCore().add(core));
        satisfy.setTotalResultMatchCore(satisfy.getMatchCore());
        satisfy.setTotalSystemMatchCore(satisfy.getMatchCore());
    }

    private List<Long> notSatisfyMatchPlanIds(List<Long> planIds) {
        if (ListUtils.isEmpty(planIds)) {
            return new ArrayList<>();
        }
        String sql = "select id from order_supply_plan where id in (?) and deleted = 0 and match_type != ?";
        return demandDBHelper.getRaw(Long.class, sql, planIds, OrderSupplyPlanMatchTypeEnum.SATISFY.getCode());
    }

    private void insertSatisfyListAndUpdateNormalToNotAvailable(List<OrderSupplyPlanDetailSatisfyDO> insertList,
            boolean reCalcOrderItemSatisfy) {
        if (ListUtils.isEmpty(insertList)) {
            return;
        }
        List<Long> updateDetailIds = ListUtils.transform(insertList,
                OrderSupplyPlanDetailSatisfyDO::getSupplyDetailId);
        demandDBHelper.executeRaw("update order_supply_plan_detail_satisfy set available = 0 "
                        + "where supply_detail_id in (?) and deleted = 0 and available = 1",
                updateDetailIds);
        demandDBHelper.insert(insertList);
        if (reCalcOrderItemSatisfy) {
            String orderNumber = insertList.get(0).getOrderNumber();
            // 根据供应方案明细的满足度信息向上汇聚计算订单明细的满足度信息
            supplyDetailSatisfyToOrderItemSatisfy(orderNumber);
        }
    }


    private List<OrderItemSatisfyRateDO> queryOrderItemSatisfy(String orderNumber) {
        WhereSQL where = new WhereSQL()
                .and(" order_number = ? ", orderNumber)
                .and(" main_result = 1 ");
        return demandDBHelper.getAll(OrderItemSatisfyRateDO.class, where.getSQL(), where.getParams());
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0].orderNumber",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000)
    public void deliverConsensus(ConsensusReq req) {
        String orderNumber = req.getOrderNumber();
        OrderDetailResp order = orderCommonService.getOrderDetail(orderNumber,
                OrderAvailableStatusEnum.AVAILABLE.getCode());
        if (order == null) {
            throw BizException.makeThrow("未能查询到生效的订单信息，订单号【%s】", orderNumber);
        }

        List<Long> ids = ListUtils.transform(req.getItems(), ConsensusItem::getPlanDetailId);
        String updateSql = "update order_supply_plan_detail set deliver_consensus = ? , deliver_consensus_over = ? "
                + " where order_number = ? and id in (?) ";
        demandDBHelper.executeRaw(updateSql, req.getConsensusResult(), true, req.getOrderNumber(), ids);

        List<OrderSupplyPlanDetailWithPlanDTO> details = supplyPlanQueryService.getListDetailWithPlan(ids);
        // 写入非大盘满足的供应方案明细的满足度信息
        calcSupplyPlanDetailMatchCoreForNotSatisfyMatch(details, null);

        FlowNodeRecordWithFlowInfoDTO currentNodeData = flowService.queryProcessingFlowNode(
                orderNumber, OrderFlowEnum.ORDER_MAIN.getCode());

        OrderFlowNodeValueSetReq spinReq = new OrderFlowNodeValueSetReq();
        spinReq.setNotShowLogInMainFlow(true);
        spinReq.setOrder(order);
        spinReq.setNodeCode(currentNodeData.getNodeCode());
        spinReq.setOperateEvent("交付共识修改");
        if (req.getConsensusResult()) {
            spinReq.setOperateName(StrUtil.format("交付共识接受，供应方案明细id：【{}】", ids.toString()));
        } else {
            spinReq.setOperateName(StrUtil.format("交付共识拒绝，供应方案明细id：【{}】", ids.toString()));
        }
        spinReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        spinReq.setBizId(orderNumber);
        // 设置返回值 999 表示主流程在当前节点自旋一次
        spinReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        spinReq.setOperateUser(LoginUtils.getUserNameWithSystem());
        orderFlowService.nodeRecordSetReturnValueAndPushFlow(spinReq);
    }

    @Override
    public List<OrderSupplyPlanDetailSatisfyDO> queryAvailableSupplyDetailSatisfyList(List<Long> supplyDetailIds) {
        if (ListUtils.isEmpty(supplyDetailIds)) {
            return new ArrayList<>();
        }
        WhereSQL where = new WhereSQL();
        where.and(" supply_detail_id in (?) ", supplyDetailIds);
        where.and(" available = 1 ");
        return demandDBHelper.getAll(OrderSupplyPlanDetailSatisfyDO.class, where.getSQL(), where.getParams());
    }

    @Override
    @Transactional("demandTransactionManager")
    public void modifySupplyPlanDetailMatchCore(SupplyPlanDetailMatchCoreUpdateReq req) {
        List<Long> ids = ListUtils.transform(req.getItems(), Item::getSupplyPlanDetailId);
        if (ListUtils.isEmpty(ids)) {
            return;
        }
        OrderInfoDO order = orderCommonService.getOrderInfo(req.getOrderNumber(), OrderAvailableStatusEnum.AVAILABLE.getCode());
        if (order == null) {
             throw BizException.makeThrow("未能查询到生效的订单信息，订单号【%s】", req.getOrderNumber());
        }
        if (Ppl13weekProductTypeEnum.GPU.getName().equals(order.getProduct())) {
             throw BizException.makeThrow("GPU订单不支持修改供应方案明细的满足度");
        }

        Map<Long, BigDecimal> map = ListUtils.toMap(req.getItems(), Item::getSupplyPlanDetailId, Item::getMatchCore);
        List<OrderSupplyPlanDetailSatisfyDO> details = queryAvailableSupplyDetailSatisfyList(ids);

        String user = LoginUtils.getUserNameWithSystem();
        OrderSupplyPlanDetailSatisfyVersionDO satisfyVersion = OrderSupplyPlanDetailSatisfyVersionDO
                .createByUser(req, user);
        demandDBHelper.insert(satisfyVersion);

        List<OrderSupplyPlanDetailSatisfyDO> insertSatisfyList = new ArrayList<>();
        for (OrderSupplyPlanDetailSatisfyDO detail : details) {
            BigDecimal matchCore = map.get(detail.getSupplyDetailId());
            if (matchCore == null) {
                continue;
            }
            OrderSupplyPlanDetailSatisfyDO satisfyData =  new OrderSupplyPlanDetailSatisfyDO();
            BeanUtils.copyProperties(detail, satisfyData);
            satisfyData.setId(null);
            satisfyData.setCreateTime(null);
            satisfyData.setUpdateTime(null);
            satisfyData.setDeleted(false);
            satisfyData.setSatisfyVersionId(satisfyVersion.getId());
            satisfyData.setAvailable(true);
            satisfyData.setTotalResultMatchCore(matchCore);
            insertSatisfyList.add(satisfyData);
        }
        insertSatisfyListAndUpdateNormalToNotAvailable(insertSatisfyList, true);
    }

    @Override
    public OrderSupplySatisfyModifiedRecordResp querySatisfyModifiedRecord(Long supplyPlanDetailId) {
        String sql = "select DISTINCT satisfy_version_id from order_supply_plan_detail_satisfy "
                + " where supply_detail_id = ? and deleted = 0 ";
        List<Long> satisfyVersionIds = demandDBHelper.getRaw(Long.class, sql, supplyPlanDetailId);
        if (ListUtils.isEmpty(satisfyVersionIds)) {
            return OrderSupplySatisfyModifiedRecordResp.create(null);
        }
        WhereSQL where = new WhereSQL().and(" id in (?) ", satisfyVersionIds);
        List<VersionItem> items = demandDBHelper.getAll(VersionItem.class, where.getSQL(), where.getParams());
        return OrderSupplySatisfyModifiedRecordResp.create(items);
    }

    /**
     *  根据供应方案明细的满足度信息，向上汇聚计算出不同满足方式下的订单明细满足度信息
     */
    @Override
    public void supplyDetailSatisfyToOrderItemSatisfy(String orderNumber) {
        // 订单明细、满足方式的满足核心数（由供应方案明细汇聚计算出的满足核心数）
        String matchSql = ORMUtils.getSql("/sql/order/supply/query_order_item_match_core.sql");
        List<OrderItemMatchCoreDTO> details = demandDBHelper.getRaw(OrderItemMatchCoreDTO.class,
                matchSql, orderNumber);

        // 获取现有的订单明细满足信息
        WhereSQL where = new WhereSQL()
                .and(" order_number = ? ", orderNumber)
                .and(" main_result = 1 ");
        List<OrderItemSatisfyRateDO> orderItemSatisfyList = demandDBHelper
                .getAll(OrderItemSatisfyRateDO.class, where.getSQL(), where.getParams());

        // 获取订单明细信息
        OrderDetailResp order = orderCommonService.getOrderDetail(orderNumber,
                OrderAvailableStatusEnum.AVAILABLE.getCode());

        List<OrderItemSatisfyRateDO> deleteList = new ArrayList<>();
        orderItemSatisfyList.forEach(item -> {
            OrderItemSatisfyRateDO deleteItem = new OrderItemSatisfyRateDO();
            deleteItem.setId(item.getId());
            deleteList.add(deleteItem);
        });
        List<OrderItemSatisfyRateDO> insertList = new ArrayList<>();
        for (OrderItemMatchCoreDTO match : details) {
            boolean hasMatch = false;
            for (OrderItemSatisfyRateDO item : orderItemSatisfyList) {
                String key1 = String.join("@",
                        match.getSupplyZoneName(), match.getSupplyInstanceType(), DateUtils.formatDate(match.getConsensusBeginBuyDate()));
                String key2 = String.join("@",
                        item.getZoneName(), item.getInstanceType(), DateUtils.formatDate(item.getBeginBuyDate()));
                if (Objects.equals(key1, key2) && Objects.equals(item.getMatchType(), match.getMatchType())) {
                    if (item.getId() == null) {
                        // id 为空，表示已经匹配过了，不能再匹配
                        continue;
                    }
                    // 维度相同，使用由供应方案明细汇聚计算出的满足核心数
                    item.setTotalMatchCore(match.getTotalMatchCore());
                    item.setTotalMatchGpu(match.getTotalMatchGpu());
                    item.setConsensusCore(match.getConsensusCoreNum());
                    item.setConsensusGpu(match.getConsensusGpuNum());
                    hasMatch = true;
                    item.setId(null);
                    item.setCreateTime(null);
                    item.setUpdateTime(null);
                    item.setDeleted(false);
                    insertList.add(item);
                    break;
                }
            }
            if (!hasMatch) {
                // 没有匹配到原有的订单明细满足信息，则创建新的
                OrderItemSatisfyRateDO item = new OrderItemSatisfyRateDO();
                item.setOrderNumber(orderNumber);
                item.setInstanceType(match.getSupplyInstanceType());
                item.setZoneName(match.getSupplyZoneName());
                item.setMatchType(match.getMatchType());
                item.setBeginBuyDate(match.getConsensusBeginBuyDate());
                OrderSupplyPlanMatchTypeEnum matchType = OrderSupplyPlanMatchTypeEnum.getByCode(match.getMatchType());
                item.setMatchTypeName(matchType == null ? match.getMatchType() : matchType.getName());
                item.setTotalMatchCore(match.getTotalMatchCore());
                item.setTotalSystemMatchCore(match.getTotalMatchCore());
                item.setTotalMatchGpu(match.getTotalMatchGpu());
                item.setConsensusCore(match.getConsensusCoreNum());
                item.setConsensusGpu(match.getConsensusGpuNum());
                item.setMainResult(true);
                // 计算日期统一为订单的开始购买日期
                item.setCalcDate(order.getBeginBuyDate());
                item.setAppId(order.getAppId());
                item.setCustomerUin(order.getCustomerUin());
                item.setOrderType(order.getOrderType());
                item.setZone(match.getSupplyZone());
                item.setRegion(match.getSupplyRegion());
                item.setRegionName(match.getSupplyRegionName());
                item.setWarZone(order.getWarZone());
                item.setProduct(order.getProduct());
                item.setCustomerShortName(order.getCustomerShortName());
                item.setIndustryDept(order.getIndustryDept());
                insertList.add(item);
            }
        }
        demandDBHelper.delete(deleteList);
        demandDBHelper.insertBatchWithoutReturnId(insertList);

        OrderSatisfyRateService orderSatisfyRateService = SpringUtil.getBean(OrderSatisfyRateService.class);
        // 等待10秒，异步进行共识需求满足量更新
        SupplyPlanOperateServiceImpl.scheduledExecutor.schedule(
                () -> orderSatisfyRateService.updateOrderConsensusByNumChange(orderNumber),
                10, TimeUnit.SECONDS);
    }

    @Override
    public OfficialWebsiteServiceLevelOverviewResp officialWebsiteServiceLevelOverview(ServiceLevelQueryReq req) {
        OfficialWebsiteServiceLevelOverviewResp resp = new OfficialWebsiteServiceLevelOverviewResp();
        resp.setQueryReq(req);

        CountDownLatch latch = new CountDownLatch(1);
        // 异步获取官网售罄数据
        SupplyPlanOperateServiceImpl.executor.execute(
                () -> asyncQueryAndSetOfficialWebsiteSoldOutData(latch, req, resp));

        // 获取官网购买数据
        List<OfficialWebsiteBuyDTO> buy = officialWebsiteBuyData(req);
        resp.setBuyList(buy);

        // 计算官网服务水平-整体购买成功率
        BigDecimal applyCore = BigDecimal.ZERO;
        BigDecimal successCore = BigDecimal.ZERO;
        for (OfficialWebsiteBuyDTO buyDTO : buy) {
            if (buyDTO.getApplyCore() != null) {
                applyCore = applyCore.add(buyDTO.getApplyCore());
            }
            if (buyDTO.getSuccessBuyCore() != null) {
                successCore = successCore.add(buyDTO.getSuccessBuyCore());
            }
        }
        // 购买成功率=成功购买核心数/申请核数
        BigDecimal buySuccessRate = BigDecimal.ZERO.compareTo(applyCore) == 0 ? BigDecimal.ONE
                : successCore.divide(applyCore, 4, RoundingMode.HALF_UP);
        resp.setBuySuccessRate(buySuccessRate);

//        // 计算官网服务水平-整体售罄率
//        BigDecimal soldOutTotal = BigDecimal.ZERO;
//        BigDecimal soldTotal = BigDecimal.ZERO;
        try {
            latch.await();
        } catch (InterruptedException e) {
            // non
        }
//        // 官网售罄数据
//        List<OfficialWebsiteSoldOutDTO> soldOut = resp.getSoldList();
//        for (OfficialWebsiteSoldOutDTO outDTO : soldOut) {
//            if (outDTO.getSoldOutTotal() != null) {
//                soldOutTotal = soldOutTotal.add(outDTO.getSoldOutTotal());
//            }
//            if (outDTO.getSoldTotal() != null) {
//                soldTotal = soldTotal.add(outDTO.getSoldTotal());
//            }
//        }
//        // 售罄率=售罄的规格数/可售规格数
//        BigDecimal soldOutRate = BigDecimal.ZERO.compareTo(soldTotal) == 0 ? BigDecimal.ONE
//                : soldOutTotal.divide(soldTotal, 4, RoundingMode.HALF_UP);
//        resp.setSoldOutRate(soldOutRate);

        // 计算整体官网服务水平 = 70% * 整体购买成功率 + 30% *（1 - 整体售罄率）
        BigDecimal serviceLevel = BigDecimal.valueOf(0.7).multiply(buySuccessRate)
                .add(BigDecimal.valueOf(0.3).multiply(BigDecimal.ONE.subtract(resp.getSoldOutRate())));
        resp.setServiceLevel(serviceLevel);
        return resp;
    }

    private void asyncQueryAndSetOfficialWebsiteSoldOutData(CountDownLatch latch, ServiceLevelQueryReq req,
            OfficialWebsiteServiceLevelOverviewResp resp) {
        try {
            List<OfficialWebsiteSoldOutDTO> soldOut = officialWebsiteSoldOutData(req);
            resp.setSoldList(soldOut);
            // 计算官网服务水平-整体售罄率
            BigDecimal soldOutTotal = BigDecimal.ZERO;
            BigDecimal soldTotal = BigDecimal.ZERO;
            // 官网售罄数据
            for (OfficialWebsiteSoldOutDTO outDTO : soldOut) {
                if (outDTO.getSoldOutTotal() != null) {
                    soldOutTotal = soldOutTotal.add(outDTO.getSoldOutTotal());
                }
                if (outDTO.getSoldTotal() != null) {
                    soldTotal = soldTotal.add(outDTO.getSoldTotal());
                }
            }
            // 售罄率=售罄的规格数/可售规格数
            BigDecimal soldOutRate = BigDecimal.ZERO.compareTo(soldTotal) == 0 ? BigDecimal.ONE
                    : soldOutTotal.divide(soldTotal, 4, RoundingMode.HALF_UP);
            resp.setSoldOutRate(soldOutRate);
        } finally {
            latch.countDown();
        }
    }

    @Override
    public List<OfficialWebsiteBuyDTO> officialWebsiteBuyData(ServiceLevelQueryReq req) {
        req.paramsCheck();
        WhereSQL where = req.toWhereForOfficialWebsiteBuyData();
        String sql = ORMUtils.getSql("/sql/order/service_level/query_official_website_buy_data.sql");
        sql = sql.replace("${where}", where.getSQL());

        String date = LocalDate.now().plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE);
        if (req.getMainZone() != null) {
            // 获取主力可用区
            Map<String, List<String>> map = inventoryHealthConfigService.getZoneConfigMap(date);
            Set<String> mainZoneNames = new HashSet<>(
                    map.getOrDefault(InventoryHealthZoneType.PRINCIPAL.getCode(), ListUtils.newList()));
            if (req.getMainZone()) {
                where.and(" zone_name in (?) ", mainZoneNames);
            } else {
                where.and(" zone_name not in (?) ", mainZoneNames);
            }
        }
        if (req.getMainInstanceType() != null) {
            // 获取主力实例类型
            Map<String, List<String>> instanceMap = inventoryHealthConfigService.getInstanceTypeConfigMap(date);
            Set<String> mainInstance = new HashSet<>(
                    instanceMap.getOrDefault(InventoryHealthZoneType.PRINCIPAL.getCode(), ListUtils.newList()));
            if (req.getMainInstanceType()) {
                where.and(" gins_family in (?) ", mainInstance);
            } else {
                where.and(" gins_family not in (?) ", mainInstance);
            }
        }
        // 获取新机型
        List<String> newList = mrpV2DictService.getNewGenerationInstanceType();
        if (req.getNewOlderInstanceType() != null) {
            if (req.getNewOlderInstanceType()) {
                where.and(" gins_family in (?) ", newList);
            } else {
                where.and(" gins_family not in (?) ", newList);
            }
        }
        Object[] params = ArrayUtil.addAll(new Object[]{newList}, where.getParams());
        List<OfficialWebsiteBuyDTO> result = ckcldDBHelper.getRaw(OfficialWebsiteBuyDTO.class,
                sql, params);
        return result;
    }

    @Override
    public List<OfficialWebsiteSoldOutDTO> officialWebsiteSoldOutData(ServiceLevelQueryReq req) {
        req.paramsCheck();
        WhereSQL where = req.toWhereForOfficialWebsiteSoldOutData();
        String sql = ORMUtils.getSql("/sql/order/service_level/query_official_website_sold_out_data.sql");
        sql = sql.replace("${where}", where.getSQL());
        List<OfficialWebsiteSoldOutDTODetail> list = ckcldDBHelper.getRaw(OfficialWebsiteSoldOutDTODetail.class,
                sql, where.getParams());
        // 异步进行数据填充
        fillerService.fillAsyncAndExcludeSomeFiller(list);
        List<OfficialWebsiteSoldOutDTODetail> fitlerList = req.filterForOfficialWebsiteSoldOutData(list);
        return OfficialWebsiteSoldOutDTO.merge(fitlerList);
    }

    @Override
    public List<OrderServiceLevelDTO> orderServiceLevelDetailWithAuth(ServiceLevelQueryReq req) {
        req.paramsCheck();
        Tuple2<Boolean, String> authCheck = addAuthParams(req);
        if (!authCheck._1) {
            // 没有数据权限
            return new ArrayList<>();
        }
        return orderServiceLevelDetail(req);
    }

    /**
     *  根据数据权限范围处理查询入参。
     * @return left： false表示某个查询入参没有任何数据权限（不用进行查询，没有数据权限）。  right：没有数据权限的原因
     */
    private Tuple2<Boolean, String> addAuthParams(ServiceLevelQueryReq req) {
        String user = LoginUtils.getUserNameWithSystem();
        // 管理员可以看所有数据
        if (permissionService.checkIsAdmin(user)) {
            return Tuple.of(true, null);
        }
        // 行业数据关注人可以查询配置权限范围内的行业、产品、战区、客户
        UserPermissionDto auth = permissionService.getPermissionByUserAndRole(
                IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode(), user);
        if (auth == null) {
            return Tuple.of(false, String.format("登陆用户【%s】暂无管理员和行业数据关注人权限", user));
        }
        if (auth.getIsAllIndustry() == null || !auth.getIsAllIndustry()) {
            if (ListUtils.isNotEmpty(auth.getIndustry())) {
                if (ListUtils.isEmpty(req.getIndustryDeptList())) {
                    // 查询入参 部门为空，直接查询权限部门
                    req.setIndustryDeptList(auth.getIndustry());
                } else {
                    // auth的industry权限，与req的industry权限取交集
                    List<String> deptList = ListUtils.intersection(auth.getIndustry(), req.getIndustryDeptList());
                    if (ListUtils.isEmpty(deptList)) {
                        return Tuple.of(false, StrUtil.format(
                                "登陆用户【{}】暂无行业【{}】的数据权限", user, req.getIndustryDeptList()));
                    } else {
                        req.setIndustryDeptList(deptList);
                    }
                }
            }
        }
        if (auth.getIsAllProduct() == null || !auth.getIsAllProduct()) {
            if (ListUtils.isNotEmpty(auth.getProduct())) {
                if (req.getProduct() == null) {
                    throw BizException.makeThrow("查询产品不能为空");
                }
                if (!auth.getProduct().contains(req.getProduct())) {
                    return Tuple.of(false, String.format("登陆用户【%s】暂无【%s】产品数据权限", user, req.getProduct()));
                }
            }
        }
        if (auth.getIsAllWarZone() == null || !auth.getIsAllWarZone()) {
            if (ListUtils.isNotEmpty(auth.getWarZone())) {
                if (ListUtils.isEmpty(req.getWarZones())) {
                    req.setWarZones(auth.getWarZone());
                } else {
                    // auth的战区权限，与req的战区参数取交集
                    List<String> warZoneList = ListUtils.intersection(auth.getWarZone(), req.getWarZones());
                    if (ListUtils.isEmpty(warZoneList)) {
                        return Tuple.of(false, StrUtil.format(
                                "登陆用户【{}】暂无战区【{}】的数据权限", user, req.getWarZones()));
                    } else {
                        req.setWarZones(warZoneList);
                    }
                }
            }
        }
        if (auth.getIsAllCustomer() == null || !auth.getIsAllCustomer()) {
            if (ListUtils.isNotEmpty(auth.getCustomer())) {
                if (ListUtils.isEmpty(req.getCommonCustomerShortNames())) {
                    req.setCommonCustomerShortNames(auth.getCustomer());
                } else {
                    // auth的通用客户简称权限，与req的通用客户简称参数取交集
                    List<String> customers = ListUtils.intersection(auth.getCustomer(), req.getCommonCustomerShortNames());
                    if (ListUtils.isEmpty(customers)) {
                        return Tuple.of(false, StrUtil.format(
                                "登陆用户【{}】暂无客户【{}】（通用客户简称）的数据权限", user, req.getCommonCustomerShortNames()));
                    } else {
                        req.setCommonCustomerShortNames(customers);
                    }
                }

                Map<String, List<IndustryDemandIndustryWarZoneDictDO>> map = dictService.queryCommonCustomerMap();
                // 通用客户简称 转 客户简称
                Set<String> customerShortNameAuths = new HashSet<>();
                for (String commonCustomer : auth.getCustomer()) {
                    List<IndustryDemandIndustryWarZoneDictDO> warZoneList = map.get(commonCustomer);
                    if (ListUtils.isNotEmpty(warZoneList)) {
                        customerShortNameAuths.addAll(ListUtils.toSet(warZoneList,
                                IndustryDemandIndustryWarZoneDictDO::getCustomerName));
                    }
                }
                if (ListUtils.isEmpty(req.getCustomerShortNames())) {
                    req.setCustomerShortNames(new ArrayList<>(customerShortNameAuths));
                } else {
                    // auth的 客户权限，与req的客户参数取交集，这里都用客户简称
                    List<String> names = ListUtils.intersection(req.getCustomerShortNames(),
                            new ArrayList<>(customerShortNameAuths));
                    if (ListUtils.isEmpty(names)) {
                        return Tuple.of(false, StrUtil.format(
                                "登陆用户【{}】暂无客户【{}】（客户简称）的数据权限", user, req.getCustomerShortNames()));
                    } else {
                        req.setCustomerShortNames(names);
                    }
                }
            }
        }
        return Tuple.of(true, null);
    }

    @Override
    public List<OrderServiceLevelDTO> orderServiceLevelDetail(ServiceLevelQueryReq req) {
        req.paramsCheck();
        List<OrderServiceLevelDTO> allData = new ArrayList<>();
        if (req.getUseCkData()) {
            List<OrderServiceLevelDTO> ckData = orderServiceLevelDetailFromCk(req);
            allData.addAll(ckData);
        }
        List<OrderServiceLevelDTO> oldData = orderServiceLevelDetailOldData(req, true);
        List<OrderServiceLevelDTO> newData = orderServiceLevelDetailNewData(req);
        allData.addAll(oldData);
        allData.addAll(newData);
        return allData;
    }

    private List<OrderServiceLevelDTO> orderServiceLevelDetailFromCk(ServiceLevelQueryReq req) {
        String statDateSql = "select max(stat_date) from std_crp.dwd_crp_order_service_level_detail";
        LocalDate statDate = ckcldStdCrpDBHelper.getRawOne(LocalDate.class, statDateSql);
        if (statDate == null) {
            return new ArrayList<>();
        }

        String maxYearMonthSql = "select max(year_month) from std_crp.dwd_crp_order_service_level_detail where stat_date = ?";
        String maxYearMonth = ckcldStdCrpDBHelper.getRawOne(String.class, maxYearMonthSql, statDate);
        if (Strings.isBlank(maxYearMonth)) {
            return new ArrayList<>();
        }
        YearMonth max = YearMonth.parse(maxYearMonth, DateTimeFormatter.ofPattern("yyyyMM"));
        req.setCkMaxYearMonth(max);
        if (max.isBefore(req.getBeginMonth())) {
            return new ArrayList<>();
        }
        WhereSQL where = req.toCKWhereForOrderServiceLevelDetail(statDate);
        if (where == null) {
            return new ArrayList<>();
        }
        List<OrderServiceLevelItemCKDTO> list = ckcldStdCrpDBHelper.getAll(OrderServiceLevelItemCKDTO.class,
                where.getSQL(), where.getParams());
        if (ListUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<OrderServiceLevelDTO> result = new ArrayList<>(list.size());
        for (OrderServiceLevelItemCKDTO detailDO : list) {
            OrderServiceLevelDTO dto = OrderServiceLevelDTO.from(detailDO);
            result.add(dto);
        }
        return result;
    }

    private List<OrderServiceLevelDTO> orderServiceLevelDetailNewData(ServiceLevelQueryReq req) {
        // 这里只查询 用预扣块计算满足量的周期性弹性订单 数据 （5月份及以后的周期性弹性订单才根据预扣块计算了弹性月度满足量）
        List<OrderItemMatchCore> cycleDatas = orderServiceLevelDetailNewDataForCycle(req);
        // 这里是常规计算的订单满足量 数据
        List<OrderItemMatchCore> normalDatas = orderServiceLevelDetailNewDataForNormal(req, cycleDatas);
        List<OrderItemMatchCore> orderItems = ListUtils.isEmpty(normalDatas) ? new ArrayList<>() : new ArrayList<>(normalDatas);
        if (ListUtils.isNotEmpty(cycleDatas)) {
            orderItems.addAll(cycleDatas);
        }
        // 解决相同订单、可用区、实例类型的orderNumberId使用重复的满足度信息的问题
        OrderItemMatchCore.resolveRepeatMatchCore(orderItems);
        // 异步进行数据填充
        fillerService.fillAsyncAndExcludeSomeFiller(orderItems);
        List<OrderItemMatchCore> filterItems = req.filterForOrderServiceLevelDetail(orderItems);
        List<String> orderNumberIds = ListUtils.transform(filterItems, OrderItemMatchCore::getOrderNumberId);

        // 查询出订单明细的提前期信息
        String advanceSql = "select order_number_id, real_tag_result from ppl_order_tag_log "
                + " where deleted = 0 and order_number_id in (?)";
        List<OrderItemTag> advanceList = demandDBHelper.getRaw(OrderItemTag.class, advanceSql, orderNumberIds);

        return OrderServiceLeveResp.merge(filterItems, advanceList, req);
    }

    private List<OrderItemMatchCore> orderServiceLevelDetailNewDataForNormal(ServiceLevelQueryReq req,
            List<OrderItemMatchCore> cycleDatas) {
        // 使用共识开始购买日期做查询范围
        WhereSQL where = req.toWhereForOrderServiceLevelDetail(
                true, false, false, "c.consensus_begin_buy_date");
        if (where == null) {
            return new ArrayList<>();
        }
        if (ListUtils.isNotEmpty(cycleDatas)) {
            Set<String> cycleOrderNumbers = ListUtils.toSet(cycleDatas, OrderItemMatchCore::getOrderNumber);
            // 此 where 不查询 用预扣块计算满足量的周期性弹性订单
            where.and("a.order_number not in (?)", cycleOrderNumbers);
        }
        LocalDate date_05_01 = LocalDate.of(2025,5,1);
        // 2025-05-01及之后的周期性弹性订单的满足量不在此处查询，在 orderServiceLevelDetailNewDataForCycle 查询
        WhereSQL cycleWhere = new WhereSQL();
        cycleWhere.and("a.begin_buy_date < ? "
                        + "or (a.begin_buy_date >= ? and a.order_type = ? ) "
                        + "or (a.begin_buy_date >= ? and a.order_type = ? and a.elastic_type = ?) ",
                date_05_01,
                date_05_01, OrderTypeEnum.NEW.getCode(),
                date_05_01, OrderTypeEnum.ELASTIC.getCode(), OrderElasticType.ONCE_TIME.getTypeName());
        where.and(cycleWhere);
        String sql = ORMUtils.getSql("/sql/order/service_level/query_order_item_match_core_dotyou.sql");
        sql = sql.replace("${where}", where.getSQL());
        return demandDBHelper.getRaw(OrderItemMatchCore.class, sql, where.getParams());
    }

    private List<OrderItemMatchCore> orderServiceLevelDetailNewDataForCycle(ServiceLevelQueryReq req) {
        if (ListUtils.isNotEmpty(req.getOrderTypeList())
                && !req.getOrderTypeList().contains(OrderTypeEnum.ELASTIC.getCode())) {
            return new ArrayList<>();
        }
        if (ListUtils.isNotEmpty(req.getElasticTypeList())
                && !CollectionUtil.containsAny(req.getElasticTypeList(), OrderElasticType.cycleElasticTypes)) {
            return new ArrayList<>();
        }
        // 使用周期性弹性订单的月度满足量开始购买日期做查询范围
        WhereSQL cycleWhere = req.toWhereForOrderServiceLevelDetail(
                true, false, true, "c.begin_buy_date");
        if (cycleWhere == null) {
            return new ArrayList<>();
        }
        // 5月份及以后的周期性弹性订单才根据预扣块计算了弹性月度满足量
        LocalDate date_05_01 = LocalDate.of(2025,5,1);
        cycleWhere.and("a.begin_buy_date >= ? ", date_05_01);
        // 周期性弹性订单使用 周期性弹性月度满足量
        String sql = ORMUtils.getSql("/sql/order/service_level/query_order_item_match_core_cycle_elastic.sql");
        sql = sql.replace("${where}", cycleWhere.getSQL());
        return demandDBHelper.getRaw(OrderItemMatchCore.class, sql, cycleWhere.getParams());
    }

    private List<OrderServiceLevelDTO> orderServiceLevelDetailOldData(ServiceLevelQueryReq req, boolean separateNewOldData) {
        // 旧数据使用订单开始购买日期
        WhereSQL where = req.toWhereForOrderServiceLevelDetail(separateNewOldData,
                true, false, "a.begin_buy_date");
        if (where == null) {
            return new ArrayList<>();
        }
        String sql = ORMUtils.getSql("/sql/order/service_level/query_order_item_match_core.sql");
        sql = sql.replace("${where}", where.getSQL());

        List<OrderItemMatchCore> orderItems = demandDBHelper.getRaw(OrderItemMatchCore.class, sql, where.getParams());
        // 异步进行数据填充
        fillerService.fillAsyncAndExcludeSomeFiller(orderItems);
        List<OrderItemMatchCore> filterItems = req.filterForOrderServiceLevelDetail(orderItems);
        List<String> orderNumberIds = ListUtils.transform(filterItems, OrderItemMatchCore::getOrderNumberId);

        // 查询出订单明细的提前期信息
        String advanceSql = "select order_number_id, real_tag_result from ppl_order_tag_log "
                + " where deleted = 0 and order_number_id in (?)";
        List<OrderItemTag> advanceList = demandDBHelper.getRaw(OrderItemTag.class, advanceSql, orderNumberIds);

        return OrderServiceLeveResp.merge(filterItems, advanceList, req);
    }

    @Override
    public OrderServiceLeveResp orderServiceLevelOverview(ServiceLevelQueryReq req) {
        return orderServiceLevelOverview(req, true);
    }

    private OrderServiceLeveResp orderServiceLevelOverview(ServiceLevelQueryReq req, boolean checkAuth) {
        List<OrderServiceLevelDTO> details = checkAuth ? orderServiceLevelDetailWithAuth(req)
                : orderServiceLevelDetail(req);
        return OrderServiceLeveResp.create(details, req);
    }

    @Override
    @Synchronized(waitLockMillisecond = 2000, throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "backupServiceLevelDetail", timeout = 60 * 3)
    public void backupServiceLevelDetail(BackupServiceLevelDetailReq req) {
        if (req.getEndMonth() == null) {
            throw BizException.makeThrow("结束月份不能为空");
        }
        LocalDate statDate = LocalDate.now();
        YearMonth begin = YearMonth.of(2024, 6);
        YearMonth end = req.getEndMonth();
        ckcldStdCrpSwapDBHelper.executeRaw(
                "ALTER TABLE std_crp_swap.dwd_crp_order_service_level_detail_local ON CLUSTER default_cluster DROP PARTITION ?",
                statDate);
        int itemSize = 0;
        while (!end.isBefore(begin)) {
            YearMonth yearMonth = begin;
            int size = insertServiceLevelDetailToCkSwap(yearMonth, statDate, req.isUseCkData());
            itemSize += size;
            begin = begin.plusMonths(1);
        }

        String itemCountSql = "select count(1) from std_crp_swap.dwd_crp_order_service_level_detail "
                + " where stat_date = ?";
        waitWrite(30, 5, itemCountSql, ckcldStdCrpSwapDBHelper, itemSize, statDate);

        DBList.ckcldStdCrpDBHelper.executeRaw(
                "ALTER TABLE std_crp.dwd_crp_order_service_level_detail_local "
                        + "ON CLUSTER default_cluster REPLACE PARTITION ? "
                        + "FROM std_crp_swap.dwd_crp_order_service_level_detail_local",
                statDate);
    }

    private void waitWrite(int times, int sleepSeconds, String countSql, DBHelper dbHelper, int count, Object... params)  {
        boolean isOk = false;
        int trueCount = 0;
        int allSleepSeconds = times * sleepSeconds;
        try {
            while (times > 0) {
                Thread.sleep(sleepSeconds * 1000L);
                trueCount = dbHelper.getRawOne(Integer.class, countSql, params);
                if (trueCount == count) {
                    isOk = true;
                    break;
                }
                times--;
            }
        } catch (InterruptedException e) {
            // non
        }
        if (!isOk) {
            throw BizException.makeThrow("检查数据写入量失败，等待时长【%s】秒，应写入量【%s】，实际写入量【%s】，实际写入量检查sql【%s】",
                    allSleepSeconds, count, trueCount, countSql);
        }
    }

    private int insertServiceLevelDetailToCkSwap(YearMonth month, LocalDate statDate, boolean useCkData) {
        int size = 0;
        for (String product : ServiceLevelQueryReq.PRODUCT_LIST) {
            ServiceLevelQueryReq req = new ServiceLevelQueryReq();
            req.setBeginMonth(month);
            req.setEndMonth(month);
            req.setProduct(product);
            req.setUseCkData(useCkData);
            List<OrderServiceLevelDTO> resp = orderServiceLevelDetail(req);
            List<DwdCrpOrderServiceLevelDetailDO> list = new ArrayList<>(resp.size());
            for (OrderServiceLevelDTO levelDTO : resp) {
                DwdCrpOrderServiceLevelDetailDO item = levelDTO.toDwdCrpOrderServiceLevelDetailDO(statDate, product);
                list.add(item);
            }
            fillerService.fill(list);
            ckcldStdCrpSwapDBHelper.insertBatchWithoutReturnId(list);
            size += list.size();
        }

        return size;
    }

    @Override
    @Synchronized(waitLockMillisecond = 2000, throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "syncSimpleOrderServiceLevelSummary", timeout = 60 * 2)
    public void syncSimpleOrderServiceLevelSummary() {
        List<OrderServiceLevelSummaryDO> list = new ArrayList<>();
        LocalDate now = LocalDate.now();
        YearMonth begin = YearMonth.of(2024, 6);
        YearMonth end = YearMonth.of(now.getYear(), now.getMonthValue());
        while (!end.isBefore(begin)) {
            YearMonth yearMonth = begin;
            addServiceLevelOneMonth(yearMonth, now, list);
            begin = begin.plusMonths(1);
        }
        ckcldStdCrpDBHelper.executeRaw("ALTER TABLE dwd_crp_order_service_level_summary_local "
                + " ON CLUSTER default_cluster "
                + " DROP PARTITION ? ", now);
        ckcldStdCrpDBHelper.insertBatchWithoutReturnId(list);
    }

    private void addServiceLevelOneMonth(YearMonth month, LocalDate statDate,
            List<OrderServiceLevelSummaryDO> list) {
        for (String product : ServiceLevelQueryReq.PRODUCT_LIST) {
            // 全地域
            OrderServiceLevelSummaryDO all = createServiceLevel(month, statDate, product, null);
            list.add(all);
            // 境内
            OrderServiceLevelSummaryDO domestic = createServiceLevel(month, statDate, product, "境内");
            list.add(domestic);
            // 境外
            OrderServiceLevelSummaryDO overseas = createServiceLevel(month, statDate, product, "境外");
            list.add(overseas);
        }
    }

    private OrderServiceLevelSummaryDO createServiceLevel(YearMonth month, LocalDate statDate, String product,
            String customhouseTitle) {
        ServiceLevelQueryReq req = new ServiceLevelQueryReq();
        req.setBeginMonth(month);
        req.setEndMonth(month);
        req.setProduct(product);
        // 默认剔除内部业务部
        req.setRejectIndustryDeptList(ListUtils.newArrayList(IndustryDeptEnum.INNER_DEPT.getName()));
        req.setOrderNodeCodeList(ListUtils.newArrayList(
                OrderNodeCodeEnum.node_order_following.getCode(),
                OrderNodeCodeEnum.node_order_close.getCode()));
        // MCK16宫格订单服务水平数据改成新增&一次性弹性范围
        req.setOrderTypeList(ListUtils.newArrayList(OrderTypeEnum.NEW.getCode(), OrderTypeEnum.ELASTIC.getCode()));
        req.setElasticTypeList(ListUtils.newArrayList(OrderElasticType.ONCE_TIME.getTypeName()));
        if (StrUtil.isNotBlank(customhouseTitle)) {
            req.setCustomhouseTitle(customhouseTitle);
        }
        req.setOrderLabel(ListUtils.newArrayList(OrderLabelEnum.NO_TAG.getLabel()));
        OrderServiceLeveResp resp = orderServiceLevelOverview(req, false);
        OrderServiceLevelSummaryDO summary = new OrderServiceLevelSummaryDO();
        summary.setYearMonth(month);
        summary.setConsensusCore(resp.getCoreItem().getTotalConsensusCore());
        summary.setMatchCore(resp.getCoreItem().getTotalMatchCore());
        summary.setServiceLevel(resp.getCoreItem().getServiceLevel());
        if (StrUtil.isNotBlank(customhouseTitle)) {
            summary.setCustomhouseTitle(customhouseTitle);
        } else {
            summary.setCustomhouseTitle("全地域");
        }
        summary.setProduct(product);
        summary.setStatDate(statDate);
        return summary;
    }

    @Override
    public void industrySatisfyGapAlert() {
        String statDateSql = "select max(calc_date) from order_item_satisfy_rate "
                + "where match_type = ? and deleted = 0 ";
        LocalDate statDate = demandDBHelper.getRawOne(LocalDate.class, statDateSql,
                OrderSupplyPlanMatchTypeEnum.SATISFY.getCode());
        String sql = ORMUtils.getSql("/sql/order/satisfy/industry_satisfy_gap.sql");
        List<IndustrySatisfyGapDTO> list = demandDBHelper.getRaw(IndustrySatisfyGapDTO.class, sql, statDate,
                OrderSupplyPlanMatchTypeEnum.SATISFY.getCode(), OrderAvailableStatusEnum.AVAILABLE.getCode(),
                ListUtils.newArrayList(OrderStatusEnum.PROCESS.getCode()));
        if (ListUtils.isEmpty(list)) {
            return;
        }

        Map<String, List<IndustrySatisfyGapDTO>> map = ListUtils.toMapList(list,
                IndustrySatisfyGapDTO::getIndustryDept, Function.identity());
        map.forEach((industryDept, itemList) -> {
            if (ListUtils.isEmpty(itemList) || Strings.isBlank(industryDept)) {
                return;
            }
            Map<String, Object> params = new HashMap<>();
            if (itemList.size() == 1) {
                IndustrySatisfyGapDTO item = itemList.get(0);
                if (item.getGapOrderCount() <= 0) {
                    // 行业缺口订单 <= 0, 不发送通知
                    return;
                }
            }
            params.put("list", itemList);
            params.put("industryDept", industryDept);
            params.put("gapOrderCount", NumberUtils.sum(itemList, IndustrySatisfyGapDTO::getGapOrderCount));
            params.put("totalGapCore", NumberUtils.sum(itemList, IndustrySatisfyGapDTO::getTotalGapCore));
            // 在通知配置表中配置需要通知的角色-行业接口人
            dictService.eventNotice(CrpEventEnum.order_satisfy_alert_industry.getCode(),
                    null, null, params, null, industryDept);
        });
    }

    @Override
    public void globalSatisfyGapAlert() {
        String statDateSql = "select max(calc_date) from order_item_satisfy_rate "
                + "where match_type = ? and deleted = 0 ";
        LocalDate statDate = demandDBHelper.getRawOne(LocalDate.class, statDateSql,
                OrderSupplyPlanMatchTypeEnum.SATISFY.getCode());
        String sql = ORMUtils.getSql("/sql/order/satisfy/global_satisfy_gap.sql");
        List<GlobalSatisfyGapDTO> list = demandDBHelper.getRaw(GlobalSatisfyGapDTO.class, sql, statDate,
                OrderSupplyPlanMatchTypeEnum.SATISFY.getCode(), OrderAvailableStatusEnum.AVAILABLE.getCode(),
                ListUtils.newArrayList(OrderStatusEnum.PROCESS.getCode()));
        if (ListUtils.isEmpty(list)) {
            return;
        }
        if (list.size() == 1) {
            GlobalSatisfyGapDTO item = list.get(0);
            if (item.getGapOrderCount() <= 1) {
                // 所有行业缺口订单 <= 1, 不发送通知
                return;
            }
        }

        Map<String, Object> params = new HashMap<>();
        params.put("list", list);
        params.put("gapOrderCount", NumberUtils.sum(list, GlobalSatisfyGapDTO::getGapOrderCount));
        params.put("totalGapCore", NumberUtils.sum(list, GlobalSatisfyGapDTO::getTotalGapCore));
        // 在通知配置表中配置需要通知的角色-云运管接口人
        dictService.eventNotice(CrpEventEnum.order_satisfy_alert_global.getCode(),
                null, null, params, null);
    }

    /**
     * 通过订单明细满足度信息获取共识需求满足量信息
     * @param orderNumber
     */
    @Override
    @Transactional("demandTransactionManager")
    @TaskLog(taskName = "updateOrderConsensusByNumChange", timeout = 10)
    public void updateOrderConsensusByNumChange(String orderNumber) {
        //通过订单号查询最新的共识需求明细
        List<OrderConsensusDemandDetailDO> all = demandDBHelper.getAll(OrderConsensusDemandDetailDO.class,
                "where order_number = ? and available_status = ?", orderNumber,
                OrderAvailableStatusEnum.AVAILABLE.getCode());
        if (ListUtils.isEmpty(all)) {
            return;
        }

        //从order_item_satisfy_rate表获取满足量
        //1.获取三种满足量数据
        String satisfySql = ORMUtils.getSql("/sql/order/supply/query_order_item_match_core_by_zone_instance.sql");
        List<SatisfyDTO> satisfyList = demandDBHelper.getRaw(SatisfyDTO.class, satisfySql, orderNumber);
        Map<String, List<SatisfyDTO>> satisfyMap = ListUtils.toMapList(satisfyList,
                o -> String.join("@", o.getZoneName(), o.getInstanceType(), DateUtils.formatDate(o.getBeginBuyDate())),
                o -> o);

        OrderInfoDO order = orderCommonService.getOrderInfo(orderNumber, OrderAvailableStatusEnum.AVAILABLE.getCode());
        Map<String, AwsConsensusOrderPerformanceTrackDfDO> trackMap = new HashMap<>();
        Map<String, BuyItem> buyItemMap = new HashMap<>();
        if (order.cycleElasticReturnTrue()) {
            // 获取周期性弹性履约数据
            List<BuyItem> buyItems = performanceTrackService.queryCycleElasticBuyItem(orderNumber);
            buyItemMap = ListUtils.toMap(buyItems, BuyItem::key, Function.identity());
        } else {
            //获取最新履约量数据
            LocalDate trackDate = ckcldStdCrpDBHelper.getRawOne(LocalDate.class, "select max(stat_time) from aws_consensus_order_performance_track_df");
            if (trackDate == null || trackDate.isAfter(DateUtils.today())) {
                trackDate = DateUtils.today();
            }
            List<AwsConsensusOrderPerformanceTrackDfDO> trackList =ckcldStdCrpDBHelper.getAll(AwsConsensusOrderPerformanceTrackDfDO.class, "where order_number = ? and stat_time = ?", orderNumber, DateUtils.formatDate(trackDate));
            trackMap = ListUtils.toMap(trackList,
                    o -> String.join("@", o.getZoneName(), o.getInstanceType(), DateUtils.formatDate(o.getBeginBuyDate())),
                    o -> o);
        }

        Map<String, List<OrderConsensusDemandDetailDO>>  detailMap = ListUtils.toMapList(all,
                o -> String.join("@", o.getDemandZoneName(), o.getDemandInstanceType()),
                    Function.identity());
        //更新共识需求满足量数据与履约量数据
        for (OrderConsensusDemandDetailDO detail : all) {
            String key = String.join("@", detail.getDemandZoneName(), detail.getDemandInstanceType(),
                    DateUtils.formatDate(detail.getConsensusBeginBuyDate()));
            String keyNoDate = String.join("@", detail.getDemandZoneName(), detail.getDemandInstanceType());
            List<OrderConsensusDemandDetailDO> dimList = detailMap.get(keyNoDate);
            dimList.remove(detail);
            List<SatisfyDTO> satisfyDTOS = satisfyMap.get(key);
            AwsConsensusOrderPerformanceTrackDfDO performanceTrackDTO = trackMap.get(key);
            if (ListUtils.isNotEmpty(satisfyDTOS)) {
                detail.setSatisfySupplyCpuNum(NumberUtils.sum(satisfyDTOS.stream().filter(o -> o.getMatchType().equals(
                                OrderSupplyPlanMatchTypeEnum.SATISFY.getCode())).collect(Collectors.toList()),
                        SatisfyDTO::getTotalNum));
                detail.setSatisfySupplyGpuNum(NumberUtils.sum(satisfyDTOS.stream().filter(o -> o.getMatchType().equals(
                                OrderSupplyPlanMatchTypeEnum.SATISFY.getCode())).collect(Collectors.toList()),
                        SatisfyDTO::getTotalGpuNum));
                detail.setCumulativePreDeductCpuNum(NumberUtils.sum(satisfyDTOS.stream().filter(o -> o.getMatchType().equals(
                                OrderSupplyPlanMatchTypeEnum.SATISFY.getCode())).collect(Collectors.toList()),
                        SatisfyDTO::getCumulativeNum));
                detail.setCumulativePreDeductGpuNum(NumberUtils.sum(satisfyDTOS.stream().filter(o -> o.getMatchType().equals(
                                OrderSupplyPlanMatchTypeEnum.SATISFY.getCode())).collect(Collectors.toList()),
                        SatisfyDTO::getCumulativeGpuNum));
                detail.setMoveSupplyCpuNum(NumberUtils.sum(satisfyDTOS.stream().filter(o -> o.getMatchType().equals(
                                OrderSupplyPlanMatchTypeEnum.MOVE.getCode())).collect(Collectors.toList()),
                        SatisfyDTO::getTotalNum));
                detail.setMoveSupplyGpuNum(NumberUtils.sum(satisfyDTOS.stream().filter(o -> o.getMatchType().equals(
                                OrderSupplyPlanMatchTypeEnum.MOVE.getCode())).collect(Collectors.toList()),
                        SatisfyDTO::getTotalGpuNum));
                detail.setBuySupplyCpuNum(NumberUtils.sum(satisfyDTOS.stream().filter(o -> o.getMatchType().equals(
                                OrderSupplyPlanMatchTypeEnum.BUY.getCode())).collect(Collectors.toList()),
                        SatisfyDTO::getTotalNum));
                detail.setBuySupplyGpuNum(NumberUtils.sum(satisfyDTOS.stream().filter(o -> o.getMatchType().equals(
                                OrderSupplyPlanMatchTypeEnum.BUY.getCode())).collect(Collectors.toList()),
                        SatisfyDTO::getTotalGpuNum));
            }
            //三种满足量加和
            BigDecimal totalSatisfyNum = detail.getSatisfySupplyCpuNum().add(detail.getMoveSupplyCpuNum())
                    .add(detail.getBuySupplyCpuNum());
            BigDecimal totalSatisfyGpuNum = detail.getSatisfySupplyGpuNum().add(detail.getMoveSupplyGpuNum())
                    .add(detail.getBuySupplyGpuNum());
            //履约量
            BigDecimal performanceNum = BigDecimal.ZERO;
            BigDecimal performanceGpu = BigDecimal.ZERO;
            if (order.cycleElasticReturnTrue()) {
                String cycleKey = BuyItem.key(detail.getDemandInstanceType(), detail.getDemandZoneName());
                BuyItem buyItem = buyItemMap.get(cycleKey);
                if (buyItem != null) {
                    // 周期性履约数据 没有共识开始购买日期维度， 这里需要进行履约量分配
                    performanceNum = buyItem.getTotalBuyCore();
                    if (performanceNum.compareTo(totalSatisfyNum) > 0) {
                        if (ListUtils.isEmpty(dimList)) {
                            buyItem.setTotalBuyCore(BigDecimal.ZERO);
                        } else {
                            buyItem.setTotalBuyCore(performanceNum.subtract(totalSatisfyNum));
                            performanceNum = totalSatisfyNum;
                        }
                    } else {
                        buyItem.setTotalBuyCore(BigDecimal.ZERO);
                    }
                    detail.setBuyNum(performanceNum);
                    performanceGpu = buyItem.getTotalBuyGpu();
                    if (performanceGpu.compareTo(totalSatisfyGpuNum) > 0) {
                        if (ListUtils.isEmpty(dimList)) {
                            buyItem.setTotalBuyGpu(BigDecimal.ZERO);
                        } else {
                            buyItem.setTotalBuyGpu(performanceGpu.subtract(totalSatisfyGpuNum));
                            performanceGpu = totalSatisfyGpuNum;
                        }
                    } else {
                        buyItem.setTotalBuyGpu(BigDecimal.ZERO);
                    }
                    detail.setBuyGpuNum(performanceGpu);
                }
            } else {
                performanceNum = performanceTrackDTO == null ? BigDecimal.ZERO : performanceTrackDTO.getBuyTotalCore();
                detail.setBuyNum(performanceNum);
                performanceGpu = performanceTrackDTO == null ? BigDecimal.ZERO : performanceTrackDTO.getBuyTotalGpu();
                detail.setBuyGpuNum(performanceGpu);
            }

            //最终的满足量
            BigDecimal maxValue = totalSatisfyNum.max(performanceNum);
            BigDecimal maxValueGpu = totalSatisfyGpuNum.max(performanceGpu);
            detail.setSatisfiedCpuNum(maxValue.min(BigDecimal.valueOf(detail.getConsensusDemandCpuNum())));
            detail.setSatisfiedGpuNum(maxValueGpu.min(BigDecimal.valueOf(detail.getConsensusDemandGpuNum())));
            //待满足量
            BigDecimal waitSatisfy = BigDecimal.valueOf(detail.getConsensusDemandCpuNum())
                    .subtract(detail.getSatisfiedCpuNum());
            BigDecimal waitSatisfyGpu = BigDecimal.valueOf(detail.getConsensusDemandGpuNum())
                    .subtract(detail.getSatisfiedGpuNum());
            detail.setWaitSatisfiedCpuNum(waitSatisfy.max(BigDecimal.ZERO));
            detail.setWaitSatisfiedGpuNum(waitSatisfyGpu.max(BigDecimal.ZERO));
            //已满足待履约量：max(满足量 - 履约量,0)
            BigDecimal satisfiedWait = detail.getSatisfiedCpuNum().subtract(performanceNum);
            BigDecimal satisfiedWaitGpu = detail.getSatisfiedGpuNum().subtract(performanceGpu);
            detail.setSatisfiedWaitBuyNum(satisfiedWait.max(BigDecimal.ZERO));
            detail.setSatisfiedWaitBuyGpuNum(satisfiedWaitGpu.max(BigDecimal.ZERO));
            //待履约量 = max(共识需求-履约量,0)
            BigDecimal totalWaitSatisfy = BigDecimal.valueOf(detail.getConsensusDemandCpuNum())
                    .subtract(detail.getBuyNum());
            BigDecimal totalWaitSatisfyGpu = BigDecimal.valueOf(detail.getConsensusDemandGpuNum())
                    .subtract(detail.getBuyGpuNum());
            detail.setTotalWaitBuyNum(totalWaitSatisfy.max(BigDecimal.ZERO));
            detail.setTotalWaitBuyGpuNum(totalWaitSatisfyGpu.max(BigDecimal.ZERO));
        }
        demandDBHelper.update(all);
    }

    @Override
    public void correctRegionByZoneNameForOrderItemSatisfyRate() {
        List<StaticZoneDO> allZoneInfo = dictService.getAllZoneInfo();
        Map<String, StaticZoneDO> zoneMap = ListUtils.toMap(allZoneInfo, StaticZoneDO::getZoneName, o -> o);
        String sql = "select id,zone_name,region,region_name from order_item_satisfy_rate where deleted = 0";
        List<OrderItemSatisfyRateDO> satisfyList = demandDBHelper.getRaw(OrderItemSatisfyRateDO.class, sql);
        List<OrderItemSatisfyRateDO> updateList = new ArrayList<>();
        for (OrderItemSatisfyRateDO rateDO : satisfyList) {
            StaticZoneDO zoneDO = zoneMap.get(rateDO.getZoneName());
            if (zoneDO == null) {
                continue;
            }
            if (!Objects.equals(rateDO.getRegion(), zoneDO.getApiRegion())
                    || !Objects.equals(rateDO.getRegionName(), zoneDO.getRegionName())) {
                rateDO.setRegion(zoneDO.getApiRegion());
                rateDO.setRegionName(zoneDO.getRegionName());
                updateList.add(rateDO);
            }
        }
        if (ListUtils.isNotEmpty(updateList)) {
            demandDBHelper.update(updateList);
        }
    }

    @Override
    @TaskLog(taskName = "calcElasticOrderSatisfy", timeout = 60 * 10)
    public void calcElasticOrderSatisfy(LocalDate minDate, LocalDate maxDate) {
        if (minDate == null || maxDate == null) {
            return;
        }
        LocalDate five = LocalDate.of(2025,5,1);
        if (minDate.isBefore(five)) {
            minDate = five;
        }
        if (maxDate.isBefore(minDate)) {
            return;
        }
        String sql = ORMUtils.getSql("/sql/order/satisfy/query_cycle_elastic_order.sql");
        List<String> orderNumbers = demandDBHelper.getRaw(String.class, sql, maxDate, minDate);
        calcElasticOrderSatisfyForOrderNumbers(orderNumbers);
        calcCycleElasticOrderMonthSatisfyForOrderNumbers(orderNumbers);
    }

    @Override
    public void calcElasticOrderSatisfyForOrderNumbers(List<String> orderNumbers) {
        if (ListUtils.isEmpty(orderNumbers)) {
            return;
        }
        OrderSatisfyRateService orderSatisfyRateService = SpringUtil.getBean(OrderSatisfyRateService.class);
        for (String orderNumber : orderNumbers) {
            try {
                orderSatisfyRateService.calcElasticOrderSatisfyOneOrder(orderNumber);
            }  catch (Exception e) {
                String msg = StrUtil.format("周期性弹性订单【{}】计算满足度异常, {},",
                        orderNumber, ExceptionUtil.getMessage(e));
                log.error(msg, e);
                AlarmRobotUtil.doAlarm("calcElasticOrderSatisfyForOrderNumbers",
                        msg, null, false);
            }
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    @TaskLog(taskName = "calcElasticOrderSatisfyOneOrder", timeout = 30)
    public void calcElasticOrderSatisfyOneOrder(String orderNumber) {
        // 获取订单信息，检查是否为弹性订单，是否为日弹性、周弹性、月弹性， 仅日弹性、周弹性、月弹性的订单可以使用此方法计算生成满足度
        // 获取订单对应的预扣信息
        // 1. 获取订单信息
        OrderDetailResp order = orderCommonService.queryOrderDetail(orderNumber);
        if (order == null) {
            return;
        }
        // 2. 检查是否为周期性弹性订单
        if (!order.cycleElasticReturnTrue()) {
            return;
        }
        // 3. 获取订单对应的预扣信息
        List<PreDeductGridSimpleDTO> gridList = DBList.ckcldStdCrpDBHelper.getAll(PreDeductGridSimpleDTO.class,
                "where order_number = ?", orderNumber);
        if (ListUtils.isEmpty(gridList)) {
            return;
        }

        OrderSupplyPlanVersionDO version = supplyPlanQueryService.getAvailableVersion(order.getOrderNumber());
        if (version == null) {
            return;
        }
        List<OrderSupplyPlanDetailWithPlanDTO>  supplyPlanDetails = supplyPlanQueryService
                .getListDetailWithPlanByVersionId(version.getId(), order.getOrderNumber());
        ElasticCycleConfig config = orderCommonService.generateElasticPreDeductDTO(order);
        // 4. 根据弹性类型计算满足量
        List<OrderItemSatisfyRateDO> datas = OrderItemSatisfyRateDO.fromPreDeductGridList(supplyPlanDetails,
                order, LocalDate.now().minusDays(1), gridList, config);

        // 处理旧数据
        demandDBHelper.executeRaw("update order_item_satisfy_rate set main_result = 0 "
                        + " where deleted = 0 and main_result = 1 and order_number = ? ",
                order.getOrderNumber());
        // 保存订单明细级别的满足度计算数据
        demandDBHelper.insertBatchWithoutReturnId(datas);

        // 将订单明细级别的满足度计算数据分摊到供应方案明细上
        calcSupplyPlanDetailMatchCore(orderNumber);
    }

    @Override
    public void calcCycleElasticOrderMonthSatisfyForOrderNumbers(List<String> orderNumbers) {
        if (ListUtils.isEmpty(orderNumbers)) {
            return;
        }
        OrderSatisfyRateService orderSatisfyRateService = SpringUtil.getBean(OrderSatisfyRateService.class);
        for (String orderNumber : orderNumbers) {
            try {
                orderSatisfyRateService.calcCycleElasticOrderMonthSatisfy(orderNumber);
            }  catch (Exception e) {
                String msg = StrUtil.format("周期性弹性订单【{}】计算月度满足量异常, {},",
                        orderNumber, ExceptionUtil.getMessage(e));
                log.error(msg, e);
                AlarmRobotUtil.doAlarm("calcCycleElasticOrderMonthSatisfyForOrderNumbers",
                        msg, null, false);
            }
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    @TaskLog(taskName = "calcCycleElasticOrderMonthSatisfy", timeout = 30)
    public void calcCycleElasticOrderMonthSatisfy(String orderNumber) {
        OrderDetailResp order = orderCommonService.queryOrderDetail(orderNumber);
        if (order == null) {
            return;
        }
        // 2. 检查是否为周期性弹性订单
        if (!order.cycleElasticReturnTrue()) {
            return;
        }
        // 3. 获取订单对应的预扣信息
        List<PreDeductGridSimpleDTO> gridList = DBList.ckcldStdCrpDBHelper.getAll(PreDeductGridSimpleDTO.class,
                "where order_number = ?", orderNumber);
        if (ListUtils.isEmpty(gridList)) {
            return;
        }

        OrderSupplyPlanVersionDO version = supplyPlanQueryService.getAvailableVersion(order.getOrderNumber());
        if (version == null) {
            return;
        }
        List<OrderSupplyPlanDetailWithPlanDTO>  supplyPlanDetails = supplyPlanQueryService
                .getListDetailWithPlanByVersionId(version.getId(), order.getOrderNumber());
        ElasticCycleConfig config = orderCommonService.generateElasticPreDeductDTO(order);

        List<OrderCycleElasticMonthSatisfyDO> list = OrderCycleElasticMonthSatisfyDO.fromPreDeductGridList(
                supplyPlanDetails, order, LocalDate.now().minusDays(1), gridList, config);
        String updateSql = "update order_cycle_elastic_month_satisfy set available = 0 where deleted = 0 and order_number = ?";
        demandDBHelper.executeRaw(updateSql, orderNumber);
        demandDBHelper.insertBatchWithoutReturnId(list);
    }

    @Data
    public static class SatisfyDTO {

        @Column("zone_name")
        private String zoneName;

        @Column("instance_type")
        private String instanceType;

        @Column("match_type")
        private String matchType;

        @Column("begin_buy_date")
        private LocalDate beginBuyDate;

        @Column("total_num")
        private BigDecimal totalNum;

        @Column("total_gpu_num")
        private BigDecimal totalGpuNum;

        @Column("cumulative_num")
        private BigDecimal cumulativeNum;

        @Column("cumulative_gpu_num")
        private BigDecimal cumulativeGpuNum;

    }



}
