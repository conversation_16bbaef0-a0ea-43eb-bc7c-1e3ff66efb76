package cloud.demand.app.modules.order.dto.resp;

import cloud.demand.app.modules.order.entity.OrderSupplyPlanVersionDO;
import cn.hutool.core.date.DateUtil;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import java.util.List;
import lombok.Data;

@Data
public class SupplyPlanVersionVO extends OrderSupplyPlanVersionDO {

    private String virtualVersionCode;

    @RelatedColumn(localColumn = "id", remoteColumn = "version_id")
    private List<OrderSupplyPlanWithDetailDTO> plans;

    public void createVirtualVersionCode() {
        this.virtualVersionCode = "V" + DateUtil.format(getCreateTime(), "yyyyMMddHHmm");
    }

}
