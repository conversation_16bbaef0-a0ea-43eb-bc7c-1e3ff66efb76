package cloud.demand.app.modules.order.entity;

import com.pugwoo.dbhelper.annotation.JoinLeftTable;
import com.pugwoo.dbhelper.annotation.JoinRightTable;
import com.pugwoo.dbhelper.annotation.JoinTable;
import com.pugwoo.dbhelper.enums.JoinTypeEnum;
import lombok.Data;

@Data
@JoinTable(joinType = JoinTypeEnum.LEFT_JOIN, on = "t1.order_tag_id = t2.id")
public class PplBasePlanTagLogWithOrderLogVO{

    @JoinLeftTable
    private PplBasePlanTagLogDO pplBasePlanTagLogDO;

    @JoinRightTable
    private PplOrderTagLogDO pplOrderTagLogDO;

}
