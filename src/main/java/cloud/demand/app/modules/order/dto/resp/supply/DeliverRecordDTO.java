package cloud.demand.app.modules.order.dto.resp.supply;

import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailDO;
import cn.hutool.core.map.MapUtil;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.nutz.lang.Strings;

@Data
public class DeliverRecordDTO {

    private String bizId;

    private String instanceType;

    private String zoneName;

    /**
     * 总量。可以是核心数、卡数、实例数等指标
     */
    private int total;

    private List<String> truthBizIds;

    private List<OrderSupplyPlanDetailDO> supplyDetails = new ArrayList<>();

    /**
     * 预计交付时间汇总,如
     * 2023-06-15: 20
     * 2023-07-15: 10
     */
    private List<DeliverItem> expectDeliverList = new ArrayList<>();

    /**
     * 实际交付时间汇总,如
     * 2023-06-15: 20
     * 2023-07-15: 10
     */
    private List<DeliverItem> actualDeliverList = new ArrayList<>();

    /**
     * 模拟对冲满足（大盘满足时）
     */
    private List<DeliverItem> simulateList = new ArrayList<>();

    /**
     * 累计预扣满足（大盘满足时）
     */
    private List<DeliverItem> preDeductList = new ArrayList<>();

    private DeliverRecordDTO() {
    }

    public static List<DeliverRecordDTO> fromSupplyDetails(List<OrderSupplyPlanDetailDO> details,
            Map<String, List<String>> xyPurchaseOrder2QuotaOrder,
            Function<OrderSupplyPlanDetailDO, Integer> indexFuc) {
        if (ListUtils.isEmpty(details)) {
            return new ArrayList<>();
        }
        Map<String, DeliverRecordDTO> map = new HashMap<>();
        if (xyPurchaseOrder2QuotaOrder == null) {
            xyPurchaseOrder2QuotaOrder = MapUtil.newHashMap();
        }
        for (OrderSupplyPlanDetailDO item : details) {
            if (item == null) {
                continue;
            }
            String key = key(item);
            DeliverRecordDTO record = map.get(key);
            if (record == null) {
                record = new DeliverRecordDTO();
                record.setBizId(item.getSupplyBizId());
                record.setInstanceType(item.getSupplyInstanceType());
                record.setZoneName(item.getSupplyZoneName());
                if (Strings.isNotBlank(item.getSupplyBizId())) {
                    List<String> truthBizIds = xyPurchaseOrder2QuotaOrder.get(item.getSupplyBizId());
                    if (ListUtils.isEmpty(truthBizIds)) {
                        record.setTruthBizIds(item.supplyBizIdListGet());
                    } else {
                        record.setTruthBizIds(new ArrayList<>(truthBizIds));
                    }
                } else {
                    record.setTruthBizIds(new ArrayList<>());
                }
            }
            record.getSupplyDetails().add(item);
            Integer indexItem = indexFuc.apply(item);
            indexItem = indexItem == null ? 0 : indexItem;
            record.setTotal(Integer.sum(indexItem, record.getTotal()));
            map.put(key, record);
        }
        return ListUtils.isEmpty(map) ? new ArrayList<>() : new ArrayList<>(map.values());
    }

    private static String key(OrderSupplyPlanDetailDO item) {
        return String.format("%s_%s_%s", item.getSupplyBizId(),
                item.getSupplyInstanceType(), item.getSupplyZoneName());
    }

    /**
     * 将预计交付数据按日期合并排序
     */
    public void mergeAndSortExpectDeliverList() {
        mergeAndSort(expectDeliverList);
    }

    /**
     * 将实际交付数据按日期合并排序
     */
    public void mergeAndSortActualDeliverList() {
        mergeAndSort(actualDeliverList);
    }

    public static void mergeAndSort(List<DeliverItem> items) {
        if (ListUtils.isEmpty(items)) {
            return;
        }
        Map<LocalDate, Integer> groupedItems = items.stream()
                .collect(Collectors.groupingBy(DeliverItem::getDate, Collectors.summingInt(DeliverItem::getNum)));
        items.clear();
        for (Entry<LocalDate, Integer> entry : groupedItems.entrySet()) {
            items.add(new DeliverItem(entry.getKey(), entry.getValue()));
        }
        ListUtils.sortAscNullLast(items, DeliverItem::getDate);
    }


    @Data
    @NoArgsConstructor
    public static class DeliverItem {

        /**
         * 实际\预计交付日期
         */
        private LocalDate date;

        /**
         * 实际\预计交付数量
         */
        private int num;

        public DeliverItem(LocalDate date, int num) {
            this.date = date;
            this.num = num;
        }

    }



}
