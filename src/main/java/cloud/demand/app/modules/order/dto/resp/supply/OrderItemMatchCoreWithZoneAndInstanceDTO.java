package cloud.demand.app.modules.order.dto.resp.supply;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;

@Data
public class OrderItemMatchCoreWithZoneAndInstanceDTO {

    @Column(value = "supply_instance_type")
    private String demandInstanceType;

    @Column(value = "supply_zone_name")
    private String demandZoneName;

    @Column(value = "consensus_begin_buy_date")
    private LocalDate consensusBeginBuyDate;

    @Column(value = "consensus_end_buy_date")
    private LocalDate consensusEndBuyDate;

    @Column("match_type")
    private String matchType;

    @Column("order_number_id")
    private String orderNumberId;

    @Column("total_match_core")
    private BigDecimal totalMatchCore;

}
