package cloud.demand.app.modules.order.dto.req;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class OrderDayScaleReq {

    private String orderNumber;

    /**
     * 通用客户简称
     */
    private String commonCustomerName;

    /**
     * 客户uin
     */
    private String customerUin;

    /**
     * 地域
     */
    private String region;

    /**
     * 实例类型
     */
    private String instanceType;


    /**
     * 开始统计履约时间（Min_t - 7）
     */
    @Column(value = "start_cal_performance_track_date")
    private LocalDate startCalPerformanceTrackDate;

    /**
     * 结束统计履约时间(Max_t + max(42,订单间隔))
     */
    @Column(value = "finish_cal_performance_track_date")
    private LocalDate finishCalPerformanceTrackDate;


}
