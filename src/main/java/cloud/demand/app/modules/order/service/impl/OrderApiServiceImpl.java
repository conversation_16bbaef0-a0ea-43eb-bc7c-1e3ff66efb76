package cloud.demand.app.modules.order.service.impl;

import cloud.demand.app.modules.order.dto.req.OrderQueryReq;
import cloud.demand.app.modules.order.dto.resp.OrderDetailApiResp;
import cloud.demand.app.modules.order.dto.resp.OrderDetailResp;
import cloud.demand.app.modules.order.dto.resp.OrderItemDTO;
import cloud.demand.app.modules.order.dto.resp.OrderItemDetailResp;
import cloud.demand.app.modules.order.service.OrderApiService;
import cloud.demand.app.modules.order.service.OrderCommonService;
import cloud.demand.app.modules.p2p.industry_demand.config.DynamicProperties;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.OrderItemPreDeductFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.OrderItemSupplyPlanFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerService;
import com.alibaba.fastjson.JSON;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple2;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;
import javax.annotation.Resource;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;
import yunti.boot.security.TofUser;

@Service
public class OrderApiServiceImpl implements OrderApiService {

    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    private FillerService fillerService;

    @Override
    public List<OrderDetailApiResp> queryOrderDetailList(OrderQueryReq req, TofUser user) {
        // 限制只能查询一段时间的订单
        req.limitQueryDate(ChronoUnit.MONTHS, 6);

        // 有查询权限的部门
        List<String> authQueryDeptList = checkAndGetIndustryDeptListByAuth(req.getIndustryDept(), user);
        req.setIndustryDept(authQueryDeptList);
        // 有查询权限的订单分类
        List<String> orderCategoryList = checkAndGetOrderCategoryListByAuth(req.getOrderCategory(), user);
        req.setOrderCategory(orderCategoryList);
        req.setSystemQuery(true);

        List<OrderDetailResp> orders = orderCommonService.queryOrder(req);
        List<OrderItemDetailResp> data = OrderDetailResp.toItemDetailResp(orders);
        List<OrderItemDTO> allItem = ListUtils.transform(data, OrderItemDetailResp::getItemInfo);
        fillerService.fill(allItem, OrderItemPreDeductFiller.class);
        fillerService.fill(allItem, OrderItemSupplyPlanFiller.class);
        return OrderDetailApiResp.from(orders);
    }

    private List<String> checkAndGetOrderCategoryListByAuth(List<String> requestOrderCategoryList, TofUser user) {
        Tuple2<Boolean, List<String>> tuple2 = DynamicProperties.authOrderCategory(user);
        if (tuple2._1) {
            // 所有订单分类都有权限，直接返回请求中的订单分类参数
            return requestOrderCategoryList;
        }
        List<String> authOrderCategoryList = tuple2._2;
        if (ListUtils.isEmpty(authOrderCategoryList)) {
            throw BizException.makeThrow("暂无任何订单分类查询权限");
        }
        if (ListUtils.isNotEmpty(requestOrderCategoryList)) {
            Set<String> resList = new HashSet<>();
            Set<String> noAuthList = new HashSet<>();
            for (String s : requestOrderCategoryList) {
                if (authOrderCategoryList.contains(s)) {
                    resList.add(s);
                } else {
                    noAuthList.add(s);
                }
            }
            if (ListUtils.isNotEmpty(noAuthList)) {
                throw BizException.makeThrow("暂无订单分类【%s】的查询权限", Strings.join(",", noAuthList));
            }
            return new ArrayList<>(resList);
        } else {
            return authOrderCategoryList;
        }
    }

    private List<String> checkAndGetIndustryDeptListByAuth(List<String> requestDeptList, TofUser user) {
        Tuple2<Boolean, List<String>> tuple2 = DynamicProperties.authIndustryDept(user);
        if (tuple2._1) {
            // 所有行业部门都有权限，直接返回请求中的行业部门参数
            return requestDeptList;
        }
        List<String> authDeptList = tuple2._2;
        if (ListUtils.isEmpty(authDeptList)) {
            throw BizException.makeThrow("暂无任何部门查询权限");
        }
        if (ListUtils.isNotEmpty(requestDeptList)) {
            Set<String> deptList = new HashSet<>();
            Set<String> noAuthDeptList = new HashSet<>();
            for (String s : requestDeptList) {
                if (authDeptList.contains(s)) {
                    deptList.add(s);
                } else {
                    noAuthDeptList.add(s);
                }
            }
            if (ListUtils.isNotEmpty(noAuthDeptList)) {
                throw BizException.makeThrow("暂无部门【%s】的查询权限", Strings.join(",", noAuthDeptList));
            }
            return new ArrayList<>(deptList);
        } else {
            return authDeptList;
        }
    }

}
