package cloud.demand.app.modules.order.dto.resp;

import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.order.enums.OrderRiskLevelEnum;
import cloud.demand.app.modules.order.enums.OrderSourceEnum;
import cloud.demand.app.modules.order.enums.OrderStatusEnum;
import cloud.demand.app.modules.order.enums.OrderTypeEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;

@Data
public class OrderInfoPageVO {


    /**
     * 业务订单号，一个订单的唯一标识<br/>Column: [order_number]
     */
    @Column(value = "order_number")
    private String orderNumber;

    /**
     * 订单节点 <br/>
     * Column: [order_node_code]
     */
    @Column(value = "order_node_code")
    private String orderNodeCode;

    /**
     * 流程号 <br/>
     * Column: [flow_no]
     */
    @Column(value = "flow_no")
    private String flowNo;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept")
    private String industryDept;

    /**
     * 战区
     * Column: [war_zone]
     */
    @Column(value = "war_zone")
    private String warZone;

    /**
     * 产品
     * Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * 子产品
     */
    @Column(value = "sub_product")
    private String subProduct;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /**
     * 客户名称<br/>Column: [customer_name]
     */
    @Column(value = "customer_name")
    private String customerName;

    /**
     * 客户uin<br/>Column: [customer_uin]
     */
    @Column(value = "customer_uin")
    private String customerUin;

    /**
     * APPID<br/>Column: [app_id]
     */
    @Column(value = "app_id")
    private String appId;

    /**
     * 订单分类<br/>Column: [order_category]
     * CVM、GPU、裸金属、FPGA
     */
    @Column(value = "order_category")
    private String orderCategory;

    /**
     * 应用角色<br/>Column: [app_role]
     */
    @Column(value = "app_role")
    private String appRole;

    /**
     * 单据类型<br/>Column: [order_type]
     */
    @Column(value = "order_type")
    private String orderType;

    /**
     * 单据来源<br/>Column: [order_source]   PPL转单、 紧急订单
     *
     * @see OrderSourceEnum
     */
    @Column(value = "order_source")
    private String orderSource;

    /**
     * 订单状态<br/>Column: [order_status]
     *
     * @see OrderStatusEnum
     */
    @Column(value = "order_status")
    private String orderStatus;

    /**
     * 当前处理人
     */
    @Column(value = "current_processor")
    private String currentProcessor;

    /**
     * 订单关注人
     */
    @Column(value = "order_follower")
    private String orderFollower;

    /**
     * 开始购买日期<br/>Column: [begin_buy_date]
     */
    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    /**
     * 结束购买日期<br/>Column: [end_buy_date]
     */
    @Column(value = "end_buy_date")
    private LocalDate endBuyDate;


    /**
     * 项目名称<br/>Column: [project_name]
     */
    @Column(value = "project_name")
    private String projectName;

    /**
     * 需求场景<br/>Column: [demand_scene]
     */
    @Column(value = "demand_scene")
    private String demandScene;


    /**
     * 需求详情<br/>Column: [demand_detail]
     */
    @Column(value = "demand_detail")
    private String demandDetail;

    /**
     * 提单人<br/>Column: [submit_user]
     */
    @Column(value = "submit_user")
    private String submitUser;

    /**
     * 架构师，订单负责人<br/>Column: [architect]
     */
    @Column(value = "architect")
    private String architect;

    /**
     * 订单风险等级，高风险》评估中》大盘满足》无风险》已交付<br/>Column: [level_name]
     *
     * @see OrderRiskLevelEnum#getRiskName()
     */
    @Column(value = "risk_level")
    private String riskLevel;

    @Column(value = "region_names")
    private String regionNames;

    @Column(value = "instance_types")
    private String instanceTypes;

    @Column(value = "order_total_core")
    private Integer orderTotalCore;

    @Column(value = "order_total_instance_num")
    private Integer orderTotalInstanceNum;

    @Column(value = "order_total_gpu")
    private Integer orderTotalGpu;

    @Column(value = "order_total_database_storage")
    private BigDecimal orderTotalDatabaseStorage;

    @Column(value = "order_total_memory")
    private Integer orderTotalMemory;

    @Column(value = "flow_code")
    private String flowCode;

    @Column(value = "flow_name")
    private String flowName;

    @Column(value = "order_label")
    private String orderLabel;


    public String getOrderStatusName() {
        return OrderStatusEnum.getNameByCode(this.getOrderStatus());
    }

    public String getOrderTypeName() {
        return OrderTypeEnum.getNameByCode(this.getOrderType());
    }

    public String getOrderSourceName() {
        return OrderSourceEnum.getNameByCode(this.getOrderSource());
    }

    public String getOrderNodeCodeName() {
        return OrderNodeCodeEnum.getNameByCode(this.getOrderNodeCode());
    }

    public List<String> getInstanceTypeList() {
        if (StringUtils.isBlank(this.getInstanceTypes())) {
            return new ArrayList<>();
        }
        return Arrays.asList(this.getInstanceTypes().split(";"));
    }

    public List<String> getRegionNameList() {
        if (StringUtils.isBlank(this.getRegionNames())) {
            return new ArrayList<>();
        }
        return Arrays.asList(this.getRegionNames().split(";"));
    }


    /**
     * 获取订单关注人列表
     */
    public List<String> getOrderFollowerList() {
        if (Strings.isBlank(this.orderFollower)) {
            return new ArrayList<>();
        }
        String[] array = this.orderFollower.split(";");
        return ListUtils.newArrayList(array);
    }

}
