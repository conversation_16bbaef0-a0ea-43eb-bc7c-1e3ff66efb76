package cloud.demand.app.modules.order.service.impl;

import cloud.demand.app.modules.flow.entity.FlowInfoDO;
import cloud.demand.app.modules.order.dto.req.GlobalApprovalReq;
import cloud.demand.app.modules.order.dto.req.OrderAutoPreDeductReq;
import cloud.demand.app.modules.order.dto.req.OrderItemPlacementGroupModifyReq;
import cloud.demand.app.modules.order.dto.req.OrderSaveReq;
import cloud.demand.app.modules.order.dto.req.OrderUrgentDelayReq;
import cloud.demand.app.modules.order.dto.resp.OrderInfoWithDetailDTO;
import cloud.demand.app.modules.order.service.OrderOperateService;
import java.util.List;
import java.util.Optional;
import yunti.boot.exception.BizException;

public abstract class AbstractOrderOperateService implements OrderOperateService {

    @Override
    public OrderInfoWithDetailDTO saveNewOrderDraft(OrderSaveReq req) {
        throw BizException.makeThrow("not support saveNewOrderDraft ");
    }

    @Override
    public OrderInfoWithDetailDTO modifyOrderDraft(OrderSaveReq req) {
        throw BizException.makeThrow("not support modifyOrderDraft ");
    }

    @Override
    public OrderInfoWithDetailDTO submitDraft(String orderNumber, String operateName) {
        throw BizException.makeThrow("not support submitDraft ");
    }

    @Override
    public OrderInfoWithDetailDTO saveAndSubmitDraft(OrderSaveReq req) {
        throw BizException.makeThrow("not support saveAndSubmitDraft ");
    }

    @Override
    public void approvalDemandInMainFlow(GlobalApprovalReq req) {
        throw BizException.makeThrow("not support approvalDemandInMainFlow ");
    }

    @Override
    public void stopProcessingSubFlow(String orderNumber, String operateEvent) {
        throw BizException.makeThrow("not support stopProcessingSubFlow ");
    }

    @Override
    public void withdrawOrder(String orderNumber) {
        throw BizException.makeThrow("not support withdrawOrder ");
    }

    @Override
    public void startCancelOrderSubFlow(String orderNumber, String flowRemark) {
        throw BizException.makeThrow("not support startCancelOrderSubFlow ");
    }

    @Override
    public void bizOperate(String parentOrderNumber, String flowCode, FlowInfoDO subFlow) {
        throw BizException.makeThrow("not support bizOperate ");
    }

    @Override
    public void cancelOrderApproval(GlobalApprovalReq req) {
        throw BizException.makeThrow("not support cancelOrderApproval ");
    }

    @Override
    public void preDeductOrderApproval(GlobalApprovalReq req) {
        throw BizException.makeThrow("not support preDeductOrderApproval ");
    }

    @Override
    public void cancelOrderDone(String orderNumber, String operateName) {
        throw BizException.makeThrow("not support cancelOrderDone ");
    }

    @Override
    public void modifyOrder(OrderSaveReq req) {
        throw BizException.makeThrow("not support modifyOrder ");
    }

    @Override
    public void urgentDelay(OrderUrgentDelayReq req) {
        throw BizException.makeThrow("not support urgentDelay ");
    }

    @Override
    public void modifyOrderApproval(GlobalApprovalReq req) {
        throw BizException.makeThrow("not support modifyOrderApproval ");
    }

    @Override
    public void pushToFollowing(String orderNumber) {
        throw BizException.makeThrow("not support pushToFollowing ");
    }

    @Override
    public Optional<String> pushToOrderClose(String orderNumber) {
        throw BizException.makeThrow("not support pushToOrderClose ");
    }

    @Override
    public void syncOrderItemsLateConsensusBeginBuyDate(String orderNumber) {
        throw BizException.makeThrow("not support syncOrderItemsLateConsensusBeginBuyDate ");
    }

    @Override
    public void modifyAutoPreDeduct(OrderAutoPreDeductReq req) {
        throw BizException.makeThrow("not support modifyAutoPreDeduct");
    }

    @Override
    public void modifyPlacementGroup(OrderItemPlacementGroupModifyReq req) {
        throw BizException.makeThrow("not support modifyPlacementGroup");
    }

    @Override
    public void orderAuditOverTimeNotice() {
        // do nothing
    }

    @Override
    public void modifyOrderShareUin(String orderNumber, List<String> shareUinList) {
        throw BizException.makeThrow("not support modifyOrderShareUin");
    }
}
