package cloud.demand.app.modules.order.service.function.impl;

import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.service.function.CreateOrderAfterPushFlowFunction;
import org.springframework.stereotype.Service;

/**
 *  共识拆单
 */
@Service
public class OrderCreatorForConsensusSplitOrder implements CreateOrderAfterPushFlowFunction {

    @Override
    public OrderInfoDO create(OrderInfoDO normalOrder) {
        OrderInfoDO newOrder = normalOrder.copyNewToInsert();
        // 设置共识前的订单id
        newOrder.setBeforeConsensusId(normalOrder.getId());
        // 共识前原始订单id 。
        // 例原始订单A：共识第一次变为B，则共识前原始订单id为A；
        // 然后用户修改订单为B，则共识前原始订单id为B；
        // 然后共识第二次，则共识前原始订单id为B 。
        if (normalOrder.getBeforeConsensusNormalId() == null) {
            newOrder.setBeforeConsensusNormalId(normalOrder.getId());
        } else {
            newOrder.setBeforeConsensusNormalId(normalOrder.getBeforeConsensusNormalId());
        }
        return newOrder;
    }
}
