package cloud.demand.app.modules.order.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("grid_occupy_time")
public class GridOccupyTimeDO extends BaseDO {

    /**
     * 预扣单Id<br/>Column: [reservation_form_id]
     * */
    @Column(value = "reservation_form_id")
    private Integer reservationFormId;

    /** 预扣块id<br/>Column: [gird_id] */
    @Column(value = "grid_id")
    private Long gridId;


    /** 预扣块占用时间<br/>Column: [grid_modified_date] */
    @Column(value = "occupy_time")
    private LocalDateTime occupyTime;

    /**
     * 等待购买小时，  购买的时间 -  预扣块创建时间
     */
    @Column(value = "wait_buy_hour")
    private Integer waitBuyHour;


}