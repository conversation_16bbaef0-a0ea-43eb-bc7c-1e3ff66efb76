package cloud.demand.app.modules.order.dto;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class OrderDayScaleDTOWithoutAppRole {

    @Column(value = "stat_time")
    private LocalDate statTime;

    /**
     * 实例类型
     */
    @Column(value = "instance_type")
    private String instanceType;

    /**
     * 地域
     */
    @Column(value = "region")
    private String region;

    /**
     * 地域
     */
    @Column(value = "region_name")
    private String regionName;


    /**
     * 当天服务规模量
     */
    @Column(value = "cur_service_total")
    private BigDecimal curServiceTotal;

    /**
     * 当天新增服务规模
     */
    @Column(value = "new_service_total")
    private BigDecimal newServiceTotal;

    /**
     * 峰值规模-期初规模
     */
    private BigDecimal historyMaxBuyTotal;

    /**
     * 当天规模-期初规模
     */
    private BigDecimal historyBuyTotal;


}
