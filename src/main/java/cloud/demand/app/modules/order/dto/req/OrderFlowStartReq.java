package cloud.demand.app.modules.order.dto.req;

import cloud.demand.app.modules.flow.req.FlowStartReq;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.service.function.CreateOrderAfterPushFlowFunction;
import cloud.demand.app.modules.order.service.function.CreateOrderItemAfterPushFlowFunction;
import cloud.demand.app.modules.order.service.function.OrderCurrentProcessorCreator;
import lombok.Data;

@Data
public class OrderFlowStartReq extends FlowStartReq {

    /**
     * 当前完整的订单记录
     */
    private OrderInfoDO order;

    /**
     * 自定义的订单记录生成方法，一般用于修改订单业务信息的操作中，为null时默认是从原订单复制生成 <br/>
     * 必须返回 新的 订单对象 <br/>
     * 必须返回 新的 订单对象 <br/>
     * 必须返回 新的 订单对象 <br/>
     * 此接口内不要对生成的订单进行 insert 操作 <br/>
     * 此接口内不要对生成的订单进行 insert 操作 <br/>
     * 此接口内不要对生成的订单进行 insert 操作 <br/>
     */
    private CreateOrderAfterPushFlowFunction orderCreator;

    /**
     * 自定义的订单明细记录生成方法，一般用于修改订单业务信息的操作中，为null时默认是从原订单明细复制生成 <br/>
     * 必须返回 新的 订单明细对象 组成的列表 <br/>
     * 必须返回 新的 订单明细对象 组成的列表 <br/>
     * 必须返回 新的 订单明细对象 组成的列表 <br/>
     * 此接口内不要对生成的订单进行 insert 操作 <br/>
     * 此接口内不要对生成的订单进行 insert 操作 <br/>
     * 此接口内不要对生成的订单进行 insert 操作 <br/>
     */
    private CreateOrderItemAfterPushFlowFunction itemsCreator;

    /**
     *  生成订单当前可处理人，此时的订单信息暂未 insert 进数据库，设置完当前可处理人之后再 insert 订单信息
     */
    private OrderCurrentProcessorCreator processorCreator;

}
