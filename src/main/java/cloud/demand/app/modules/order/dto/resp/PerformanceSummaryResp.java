package cloud.demand.app.modules.order.dto.resp;


import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.modules.order.dto.PerformanceTrackDfDTO;
import cloud.demand.app.modules.order.entity.std_table.AwsOrderPerformanceTrackDfDO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Data;

@Data
public class PerformanceSummaryResp {

    private String lastMonth;

    private String lastMonthPerformanceRate = "0%";

    // 上月履约核心数
    private BigDecimal lastMonthBuyTotalCore;

    private String lastSeason;
    private String lastSeasonPerformanceRate = "0%";
    private List<YearMonthSummary> yearMonthSummaryList;

    private WaitPerformanceTotalCore waitPerformanceTotalCore;

    private PerformanceBuyTotalCore performanceBuyTotalCore;

    @Data
    public static class YearMonthSummary {

        /**
         * 需求年月
         */
        private String yearMonth;

        /**
         * 满足核心数
         */
        private BigDecimal satisfyCore;

        /**
         * 履约核心数
         */
        private BigDecimal buyTotalCore;

        /**
         * 比例
         */
        private BigDecimal rate = new BigDecimal(0);
    }

    @Data
    public static class WaitPerformanceTotalCore {

        /**
         * 待履约核心数
         */
        private BigDecimal waitBuyCore = new BigDecimal(0);

        /**
         * 2周内待履约核心数
         */
        private BigDecimal waitWeek1AndWeek2BuyCore = new BigDecimal(0);

        /**
         * 2周内待履约比例
         */
        private String waitWeek1AndWeek2BuyRate = "0%";

        /**
         * 3～4周内待履约核心数
         */
        private BigDecimal waitWeek3AndWeek4BuyCore = new BigDecimal(0);

        /**
         * 3～4周内待履约比例
         */
        private String waitWeek3AndWeek4BuyRate = "0%";

        /**
         * 5周外待履约核心数
         */
        private BigDecimal waitOutOfWeek5WaitBuyCore = new BigDecimal(0);

        /**
         * 5周外待履约比例
         */
        private String waitOutOfWeek5WaitBuyRate = "0%";
    }

    @Data
    public static class PerformanceBuyTotalCore {

        /**
         * 待履约核心数
         */
        private BigDecimal BuyTotalCore = new BigDecimal(0);

        /**
         * 待履约核心数
         */
        private BigDecimal waitBuyTotalCore = new BigDecimal(0);

        /**
         * 待履约核心比例
         */
        private String waitBuyCoreRate = "0%";

        /**
         * 2周内履约核心数
         */
        private BigDecimal week1AndWeek2BuyCore = new BigDecimal(0);

        /**
         * 2周内履约比例
         */
        private String week1AndWeek2BuyRate = "0%";

        /**
         * 3～4周内履约核心数
         */
        private BigDecimal week3AndWeek4WaitBuyCore = new BigDecimal(0);

        /**
         * 3～4周内履约比例
         */
        private String week3AndWeek4WaitBuyRate = "0%";

        /**
         * 5周外履约核心数
         */
        private BigDecimal outOfWeek5WaitBuyCore = new BigDecimal(0);

        /**
         * 5周外待履约比例
         */
        private String outOfWeek5WaitBuyRate = "0%";
    }


    public static void buildLastMonth(PerformanceSummaryResp resp, List<PerformanceTrackDfDTO> all) {

        String lastYearMonth = DateUtils.getLastYearMonth(LocalDate.now().getYear(), LocalDate.now().getMonthValue());
        resp.setLastMonth(lastYearMonth);
        LocalDate currentMonthFirstDay = DateUtils.getFirstDayOfMonthLocalDate(LocalDate.now());
        LocalDate lastMonthFirstDay = DateUtils.getFirstDayOfMonthLocalDate(lastYearMonth);
        List<PerformanceTrackDfDTO> lastMonth = all.stream().filter(v ->
                (v.getBeginBuyDate().equals(lastMonthFirstDay) || v.getBeginBuyDate().isAfter(lastMonthFirstDay))
                        && v.getBeginBuyDate().isBefore(currentMonthFirstDay)).collect(Collectors.toList());

        BigDecimal lastMonthSatisfyCore = lastMonth.stream().map(PerformanceTrackDfDTO::getSatisfyCore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 同个客户，同个机型，同个地域，同个需求年月，如果有多个订单，需求量取汇总值，履约量取最大值。
        BigDecimal lastMonthBuyCore = lastMonth.stream()
                .collect(Collectors.groupingBy(a -> String.join("@",
                                a.getCustomerShortName(), a.getInstanceType(), a.getRegionName(), a.getYearMonth()),
                        Collectors.mapping(AwsOrderPerformanceTrackDfDO::getBuyTotalCore,
                                Collectors.maxBy(BigDecimal::compareTo)
                        ))).values().stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        BigDecimal lastMonthBuyCore = lastMonth.stream()
//                .map(AwsOrderPerformanceTrackDfDO::getBuyTotalCore)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (lastMonthSatisfyCore.compareTo(new BigDecimal(0)) != 0 &&
                lastMonthBuyCore.compareTo(new BigDecimal(0)) != 0) {
            resp.setLastMonthPerformanceRate(
                    Collections.min(Arrays.asList(new BigDecimal(100),
                            lastMonthBuyCore.divide(lastMonthSatisfyCore, 4, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP))) + "%");
        }

        PerformanceBuyTotalCore performanceBuyTotalCore = new PerformanceBuyTotalCore();
        resp.setPerformanceBuyTotalCore(performanceBuyTotalCore);
        BigDecimal buyTotalCore = lastMonth.stream().map(PerformanceTrackDfDTO::getBuyTotalCore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal waitBuyTotalCore = lastMonth.stream().map(PerformanceTrackDfDTO::getWaitBuyTotalCore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal Week1AndWeek2BuyCore = lastMonth.stream().map(PerformanceTrackDfDTO::getWeek1AndWeek2BuyCore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal Week3AndWeek4BuyCore = lastMonth.stream().map(PerformanceTrackDfDTO::getWeek3AndWeek4BuyCore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal outOfWeek5BuyCore = lastMonth.stream().map(PerformanceTrackDfDTO::getOutOfWeek5BuyCore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal sum = waitBuyTotalCore.add(Week1AndWeek2BuyCore).add(Week3AndWeek4BuyCore).add(outOfWeek5BuyCore);

        if (sum.compareTo(new BigDecimal(0)) == 0) {
            return;
        }

        performanceBuyTotalCore.setBuyTotalCore(buyTotalCore.setScale(0, RoundingMode.HALF_UP));
        performanceBuyTotalCore.setWaitBuyTotalCore(waitBuyTotalCore.setScale(0, RoundingMode.HALF_UP));
        performanceBuyTotalCore.setWaitBuyCoreRate(
                waitBuyTotalCore.divide(sum, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)) + "%");
        performanceBuyTotalCore.setWeek1AndWeek2BuyCore(Week1AndWeek2BuyCore.setScale(0, RoundingMode.HALF_UP));
        performanceBuyTotalCore.setWeek1AndWeek2BuyRate(
                Week1AndWeek2BuyCore.divide(sum, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)) + "%");
        performanceBuyTotalCore.setWeek3AndWeek4WaitBuyCore(Week3AndWeek4BuyCore.setScale(0, RoundingMode.HALF_UP));
        performanceBuyTotalCore.setWeek3AndWeek4WaitBuyRate(
                Week3AndWeek4BuyCore.divide(sum, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)) + "%");
        performanceBuyTotalCore.setOutOfWeek5WaitBuyCore(outOfWeek5BuyCore.setScale(0, RoundingMode.HALF_UP));
        performanceBuyTotalCore.setOutOfWeek5WaitBuyRate(
                outOfWeek5BuyCore.divide(sum, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)) + "%");

    }

    public static void buildLastSeason(PerformanceSummaryResp resp, List<PerformanceTrackDfDTO> all) {
        LocalDate threeMonthAgoDate = LocalDate.now().minusMonths(3);
        int season = (threeMonthAgoDate.getMonthValue() - 1) / 3 + 1;
        resp.setLastSeason(threeMonthAgoDate.getYear() + "Q" + season);
        LocalDate currentQuarterStartDate = DateUtils.getCurrentQuarterStartDate(threeMonthAgoDate);
        LocalDate nextQuarterStartDate = DateUtils.getCurrentQuarterStartDate(LocalDate.now());
        List<PerformanceTrackDfDTO> lastSeason = all.stream()
                .filter(v -> (v.getBeginBuyDate().isAfter(currentQuarterStartDate) || v.getBeginBuyDate()
                        .equals(currentQuarterStartDate))
                        && v.getBeginBuyDate().isBefore(nextQuarterStartDate)).collect(Collectors.toList());
        BigDecimal lastSeasonSatisfyCore = lastSeason.stream().map(PerformanceTrackDfDTO::getSatisfyCore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 同个客户，同个机型，同个地域，同个需求年月，如果有多个订单，需求量取汇总值，履约量取最大值。
        BigDecimal lastSeasonBuyCore = lastSeason.stream()
                .collect(Collectors.groupingBy(a -> String.join("@",
                                a.getCustomerShortName(), a.getInstanceType(), a.getRegionName(), a.getYearMonth()),
                        Collectors.mapping(AwsOrderPerformanceTrackDfDO::getBuyTotalCore,
                                Collectors.maxBy(BigDecimal::compareTo)
                        ))).values().stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        BigDecimal lastSeasonBuyCore = lastSeason.stream()
//                .map(AwsOrderPerformanceTrackDfDO::getBuyTotalCore)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (lastSeasonSatisfyCore.compareTo(new BigDecimal(0)) != 0 &&
                lastSeasonBuyCore.compareTo(new BigDecimal(0)) != 0) {
            resp.setLastSeasonPerformanceRate(
                    Collections.min(Arrays.asList(new BigDecimal(100),
                            lastSeasonBuyCore.divide(lastSeasonSatisfyCore, 4, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP))) + "%");
        }
    }

    public static void buildByGroup(PerformanceSummaryResp resp, List<PerformanceTrackDfDTO> all) {
        Map<String, List<PerformanceTrackDfDTO>> yearMonthMap = all.stream().collect(Collectors.groupingBy(
                v -> v.getBeginBuyDate().getYear() + "-" + v.getBeginBuyDate().getMonthValue()));

        List<YearMonthSummary> yearMonthSummaryList = new ArrayList<>();
        yearMonthMap.forEach((k, v) -> {
            YearMonthSummary yearMonthSummary = new YearMonthSummary();
            BigDecimal monthSatisfyCore = v.stream().map(PerformanceTrackDfDTO::getSatisfyCore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 同个客户，同个机型，同个地域，同个需求年月，如果有多个订单，需求量取汇总值，履约量取最大值。
            BigDecimal monthBuyCore = v.stream()
                    .collect(Collectors.groupingBy(a -> String.join("@",
                                    a.getCustomerShortName(), a.getInstanceType(), a.getRegionName(), a.getYearMonth()),
                            Collectors.mapping(AwsOrderPerformanceTrackDfDO::getBuyTotalCore,
                                    Collectors.maxBy(BigDecimal::compareTo)
                            ))).values().stream()
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal monthBuyCore = v.stream()
//                    .map(AwsOrderPerformanceTrackDfDO::getBuyTotalCore).reduce(BigDecimal.ZERO, BigDecimal::add);
            yearMonthSummary.setYearMonth(k);
            yearMonthSummary.setSatisfyCore(monthSatisfyCore);
            yearMonthSummary.setBuyTotalCore(monthBuyCore);
            if (monthSatisfyCore.compareTo(new BigDecimal(0)) != 0 &&
                    monthBuyCore.compareTo(new BigDecimal(0)) != 0) {
                yearMonthSummary.setRate(
                        Collections.min(Arrays.asList(new BigDecimal(100),
                                monthBuyCore.divide(monthSatisfyCore, 4, RoundingMode.HALF_UP)
                                        .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP))));
            }
            yearMonthSummaryList.add(yearMonthSummary);
        });
        yearMonthSummaryList.sort(Comparator.comparing(YearMonthSummary::getYearMonth));
        resp.setYearMonthSummaryList(yearMonthSummaryList);
    }

    public static void buildWaitPerformance(PerformanceSummaryResp resp, List<PerformanceTrackDfDTO> all) {
        WaitPerformanceTotalCore waitPerformanceTotalCore = new WaitPerformanceTotalCore();
        BigDecimal waitBuyCore = all.stream().map(PerformanceTrackDfDTO::getWaitBuyTotalCore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal waitWeek1AndWeek2BuyCore = all.stream().map(PerformanceTrackDfDTO::getWaitWeek1AndWeek2BuyCore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal waitWeek3AndWeek4BuyCore = all.stream().map(PerformanceTrackDfDTO::getWaitWeek3AndWeek4BuyCore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal outOfWeek5BuyCore = all.stream().map(PerformanceTrackDfDTO::getOutOfWeek5BuyCore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (waitBuyCore.compareTo(new BigDecimal(0)) == 0) {
            return;
        }

        waitPerformanceTotalCore.setWaitBuyCore(waitBuyCore.setScale(0, RoundingMode.HALF_UP));
        waitPerformanceTotalCore.setWaitWeek1AndWeek2BuyCore(
                waitWeek1AndWeek2BuyCore.setScale(0, RoundingMode.HALF_UP));
        waitPerformanceTotalCore.setWaitWeek1AndWeek2BuyRate(
                waitWeek1AndWeek2BuyCore.divide(waitBuyCore, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100))
                        + "%");
        waitPerformanceTotalCore.setWaitWeek3AndWeek4BuyCore(
                waitWeek3AndWeek4BuyCore.setScale(0, RoundingMode.HALF_UP));
        waitPerformanceTotalCore.setWaitWeek3AndWeek4BuyRate(
                waitWeek3AndWeek4BuyCore.divide(waitBuyCore, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100))
                        + "%");
        waitPerformanceTotalCore.setWaitOutOfWeek5WaitBuyCore(outOfWeek5BuyCore.setScale(0, RoundingMode.HALF_UP));
        waitPerformanceTotalCore.setWaitOutOfWeek5WaitBuyRate(
                outOfWeek5BuyCore.divide(waitBuyCore, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)) + "%");
        resp.setWaitPerformanceTotalCore(waitPerformanceTotalCore);
    }
}
