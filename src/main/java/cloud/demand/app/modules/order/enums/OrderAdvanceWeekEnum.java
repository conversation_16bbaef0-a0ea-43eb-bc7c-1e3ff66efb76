package cloud.demand.app.modules.order.enums;

import lombok.Getter;
import yunti.boot.exception.BizException;

/**
 * 订单提前周枚举
 * 提前n周  提前天数
 * 13      [91,.)
 * 12      [84,91)
 * 11      [77,84)
 * 10      [70,77)
 * 9       [63,70)
 * 8       [56,63)
 * 7       [49,56)
 * 6       [42,49)
 * 5       [35,42)
 * 4       [28,35)
 * 3       [21,28)
 * 2       [14,21)
 * 1       [7,14)
 * 0       [0,7)
 */

@Getter
public enum OrderAdvanceWeekEnum {

    W0("提前0周", 0, 0, 7),
    W1("提前1周", 1, 7, 14),
    W2("提前2周", 2, 14, 21),
    W3("提前3周", 3, 21, 28),
    W4("提前4周", 4, 28, 35),
    W5("提前5周", 5, 35, 42),
    W6("提前6周", 6, 42, 49),
    W7("提前7周", 7, 49, 56),
    W8("提前8周", 8, 56, 63),
    W9("提前9周", 9, 63, 70),
    W10("提前10周", 10, 70, 77),
    W11("提前11周", 11, 77, 84),
    W12("提前12周", 12, 84, 91),
    W13("提前13周", 13, 91, Integer.MAX_VALUE);


    OrderAdvanceWeekEnum(String name, Integer advanceWeek, Integer min, Integer max) {
        this.name = name;
        this.advanceWeek = advanceWeek;
        this.min = min;
        this.max = max;
    }

    private final String name;
    private final Integer advanceWeek;
    private final Integer min;
    private final Integer max;

    public static OrderAdvanceWeekEnum getByAdvanceWeek(int advanceWeek) {
        for (OrderAdvanceWeekEnum value : OrderAdvanceWeekEnum.values()) {
            if (value.advanceWeek == advanceWeek) {
                return value;
            }
        }
        throw new BizException("找不到对应的提前周枚举：" + advanceWeek + " 请检查！");
    }

    public static OrderAdvanceWeekEnum getByGapDays(long gapDays) {
        for (OrderAdvanceWeekEnum value : OrderAdvanceWeekEnum.values()) {
            if (gapDays >= value.min && gapDays < value.max) {
                return value;
            }
        }
        return OrderAdvanceWeekEnum.W0;
    }
}
