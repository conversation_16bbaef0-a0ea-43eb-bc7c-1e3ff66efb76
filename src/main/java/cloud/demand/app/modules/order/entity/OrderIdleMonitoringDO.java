package cloud.demand.app.modules.order.entity;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.modules.order.dto.resp.OrderIdleMonitoringDayScaleDTO;
import cloud.demand.app.modules.order.entity.std_table.AwsOrderPerformanceTrackDfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplOrderItemAndInfoCfDO;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@ToString
@Table("order_idle_monitoring")
public class OrderIdleMonitoringDO extends BaseDO implements IOrderIdleMonitoringGroupKey{

    /** 分区键，代表数据版本<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 订单主体信息id<br/>Column: [order_info_id] */
    @Column(value = "order_info_id")
    private Long orderInfoId;

    /** 业务订单号，一个订单的唯一标识<br/>Column: [order_number] */
    @Column(value = "order_number")
    private String orderNumber;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 地域code<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** 地域名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 采购/搬迁核心数<br/>Column: [buy_or_move_core] */
    @Column(value = "buy_or_move_core")
    private Integer buyOrMoveCore;

    /** 当前已履约核心<br/>Column: [buy_total_core] */
    @Column(value = "buy_total_core")
    private Integer buyTotalCore;

    /** 购买比例，履约率<br/>Column: [buy_rate] */
    @Column(value = "buy_rate")
    private BigDecimal buyRate;

    /** 采购/搬迁闲置数量<br/>Column: [buy_or_move_idle_core] */
    @Column(value = "buy_or_move_idle_core")
    private Integer buyOrMoveIdleCore;

    /** 近30天服务日均净值<br/>Column: [recent_thirty_days_avg_net_worth] */
    @Column(value = "recent_thirty_days_avg_net_worth")
    private BigDecimal recentThirtyDaysAvgNetWorth;

    /** 闲置资源盘活天数<br/>Column: [idle_turnover_days] */
    @Column(value = "idle_turnover_days")
    private BigDecimal idleTurnoverDays;

    public static List<OrderIdleMonitoringDO> builder(LocalDate  statTime, List<DwdCrpPplOrderItemAndInfoCfDO> orderItemList, List<AwsOrderPerformanceTrackDfDO> performanceTrackList, List<OrderIdleMonitoringDayScaleDTO> dayScaleList){
        Function<IOrderIdleMonitoringGroupKey, String> groupKeyFunction = (item) -> StringUtils.joinWith("@", item.getRegion(), item.getInstanceType());

        Map<String,DwdCrpPplOrderItemAndInfoCfDO> orderItemMap = orderItemList.stream().collect(Collectors.toMap(o -> groupKeyFunction.apply(o), o -> o,(v1, v2) -> {
            v1.setMoveSupplyCore(v1.getMoveSupplyCore() + v2.getMoveSupplyCore());
            return v1;
        }));

        Map<String,AwsOrderPerformanceTrackDfDO> performanceTrackMap = performanceTrackList.stream().collect(Collectors.toMap(o -> groupKeyFunction.apply(o), o -> o,(v1, v2) -> v1));

        Map<String,List<OrderIdleMonitoringDayScaleDTO>> dayScaleGroup = ListUtils.groupBy(dayScaleList, o -> groupKeyFunction.apply(o));
        Map<String,BigDecimal> dayScaleMap = new HashMap<>();
        dayScaleGroup.forEach((childKey, childValue) -> {
            BigDecimal dayScale = childValue.stream().map(OrderIdleMonitoringDayScaleDTO::getChangeServiceCoreTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            dayScaleMap.put(childKey, SoeCommonUtils.divide(dayScale,new BigDecimal(childValue.size())));
        });

        List<OrderIdleMonitoringDO> ret = ListUtils.newArrayList();
        for(Map.Entry<String,DwdCrpPplOrderItemAndInfoCfDO> entry : orderItemMap.entrySet()){
            OrderIdleMonitoringDO item = new OrderIdleMonitoringDO();

            DwdCrpPplOrderItemAndInfoCfDO orderItem = orderItemMap.get(entry.getKey());
            item.setOrderInfoId(orderItem.getOrderInfoId());
            item.setOrderNumber(orderItem.getOrderNumber());
            item.setCustomhouseTitle(orderItem.getCustomhouseTitle());
            item.setRegion(orderItem.getRegion());
            item.setRegionName(orderItem.getRegionName());
            item.setInstanceType(orderItem.getInstanceType());
            item.setBuyOrMoveCore(orderItem.getBuySupplyCore() + orderItem.getMoveSupplyCore());

            AwsOrderPerformanceTrackDfDO performanceTrack = performanceTrackMap.get(entry.getKey());
            if(performanceTrack != null){
                item.setBuyTotalCore(performanceTrack.getBuyTotalCore().intValue());
                item.setBuyRate(performanceTrack.getBuyRate());
            }else {
                item.setBuyTotalCore(0);
                item.setBuyRate(BigDecimal.ZERO);
            }

            item.setBuyOrMoveIdleCore(Math.max(item.getBuyOrMoveCore() - item.getBuyTotalCore(), 0));
            //过去30天净值
            item.setRecentThirtyDaysAvgNetWorth(dayScaleMap.getOrDefault(entry.getKey(), BigDecimal.ZERO));

            if(item.getRecentThirtyDaysAvgNetWorth().compareTo(BigDecimal.ZERO) <= 0){
                item.setIdleTurnoverDays(new BigDecimal("365"));
            }else {
                item.setIdleTurnoverDays(SoeCommonUtils.divide(new BigDecimal(item.getBuyOrMoveIdleCore()),item.getRecentThirtyDaysAvgNetWorth()));
            }
            item.setStatTime(statTime);
            ret.add(item);
        }
        return ret;
    }

}