package cloud.demand.app.modules.order.dto;

import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.entity.OrderItemDO;
import com.pugwoo.dbhelper.annotation.JoinLeftTable;
import com.pugwoo.dbhelper.annotation.JoinRightTable;
import com.pugwoo.dbhelper.annotation.JoinTable;
import com.pugwoo.dbhelper.enums.JoinTypeEnum;
import lombok.Data;

@Data
@JoinTable(joinType = JoinTypeEnum.LEFT_JOIN, on = "t1.order_info_id = t2.id")
public class OrderItemJoinOrderVO {

    @JoinLeftTable
    private OrderItemDO itemDO;

    @JoinRightTable
    private OrderInfoDO orderInfoDO;
}



