package cloud.demand.app.modules.order.entity;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.order.enums.OrderElasticType;
import cloud.demand.app.modules.order.enums.OrderReportBiteEnum;
import cloud.demand.app.modules.order.enums.OrderRiskLevelEnum;
import cloud.demand.app.modules.order.enums.OrderSourceEnum;
import cloud.demand.app.modules.order.enums.OrderTypeEnum;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;
import yunti.boot.exception.BizException;

@Data
@ToString
@Table("order_info")
public class OrderInfoDO extends BaseDO {

    /**
     * 业务订单号，一个订单的唯一标识<br/>Column: [order_number]
     */
    @Column(value = "order_number")
    private String orderNumber;

    /**
     * 状态生效，available（主流程中生效，一个订单有且只有一个主流程中生效的订单记录），not_available（不生效），
     * available_sub_process（子流程中生效，一个订单只会有一个进行中的子流程，且最多只有一个子流程中生效的订单记录）<br/>
     * Column: [available_status]
     */
    @Column(value = "available_status")
    private String availableStatus;

    /**
     * 订单状态, DRAFT(草稿), PROCESS(进行中), FINISHED(已完结)，CANCELED(已取消) <br/>
     * Column: [order_status]
     */
    @Column(value = "order_status")
    private String orderStatus;

    /**
     * 订单节点 <br/>
     * Column: [order_node_code]
     */
    @Column(value = "order_node_code")
    private String orderNodeCode;

    /**
     * 流程号 <br/>
     * Column: [flow_no]
     */
    @Column(value = "flow_no")
    private String flowNo;

    /**
     * 共识前订单id（只有当前是共识改单生成的才会有这个字段，用于获取共识前数据）<br/>Column: [before_consensus_id]
     */
    @Column(value = "before_consensus_id")
    private Long beforeConsensusId;

    /**
     * 共识前用户原始订单id。<br/> 例原始订单A：共识第一次变为B，此值为A；然后用户修改订单为B，则此值为B；然后共识第二次，此值为B 。<br/>
     * Column: [before_consensus_id]
     */
    @Column(value = "before_consensus_normal_id")
    private Long beforeConsensusNormalId;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept")
    private String industryDept;

    /**
     * 战区
     * Column: [war_zone]
     */
    @Column(value = "war_zone")
    private String warZone;

    /**
     * 产品
     * Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /**
     * 客户名称<br/>Column: [customer_name]
     */
    @Column(value = "customer_name")
    private String customerName;

    /**
     * 客户uin<br/>Column: [customer_uin]
     */
    @Column(value = "customer_uin")
    private String customerUin;

    /**
     * APPID<br/>Column: [app_id]
     */
    @Column(value = "app_id")
    private String appId;

    /**
     * 订单分类<br/>Column: [order_category]
     * CVM、GPU、裸金属、FPGA
     */
    @Column(value = "order_category")
    private String orderCategory;

    /**
     * 应用角色<br/>Column: [app_role]
     */
    @Column(value = "app_role")
    private String appRole;

    /**
     * 单据类型<br/>Column: [order_type]
     */
    @Column(value = "order_type")
    private String orderType;

    /**
     * 单据来源<br/>Column: [order_source]   PPL转单、 紧急订单
     *
     * @see OrderSourceEnum
     */
    @Column(value = "order_source")
    private String orderSource;

    /**
     * 由哪些pplOrder转单而来 【pplOrder级别】
     */
    @Column(value = "source_ppl")
    private String sourcePpl;

    /**
     * 开始购买日期<br/>Column: [begin_buy_date]
     */
    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    /**
     * 结束购买日期<br/>Column: [end_buy_date]
     */
    @Column(value = "end_buy_date")
    private LocalDate endBuyDate;

    /**
     * 周期性弹性日期，1表示1号或者周一，7表示7号或者周日，根据弹性类型是周弹性还是月弹性来判断
     *
     * 只能填一个 可不传为空
     */
    @Column(value = "begin_elastic_cycle")
    private Integer beginElasticCycle;

    /**
     * 周期性弹性日期，1表示1号或者周一，7表示7号或者周日，根据弹性类型是周弹性还是月弹性来判断
     *
     * 只能填一个 可不传为空
     */
    @Column(value = "end_elastic_cycle")
    private Integer endElasticCycle;

    /**
     * 弹性开始日期<br/>Column: [begin_elastic_date]
     */
    @Column(value = "begin_elastic_date")
    private LocalTime beginElasticDate;

    /**
     * 弹性结束日期<br/>Column: [end_elastic_date]
     */
    @Column(value = "end_elastic_date")
    private LocalTime endElasticDate;

    /**
     * 项目类型<br/>Column: [project_type]
     */
    @Column(value = "project_type")
    private String projectType;

    /**
     * 项目名称<br/>Column: [project_name]
     */
    @Column(value = "project_name")
    private String projectName;

    /**
     * 需求场景<br/>Column: [demand_scene]
     */
    @Column(value = "demand_scene")
    private String demandScene;

    /**
     * 需求详情<br/>Column: [demand_detail]
     */
    @Column(value = "demand_detail")
    private String demandDetail;

    /**
     * 提单人<br/>Column: [submit_user]
     */
    @Column(value = "submit_user")
    private String submitUser;

    /**
     * 架构师，订单负责人<br/>Column: [architect]
     */
    @Column(value = "architect")
    private String architect;


    /**
     * 订单关注人<br/>Column: [order_follower]
     */
    @Column(value = "order_follower")
    private String orderFollower;

    @Column(value = "current_processor")
    private String currentProcessor;

    /**
     * 订单优先级，默认低优先级，低优先级，中优先级，高优先级<br/>Column: [level_name]
     */
    @Column(value = "level_name")
    private String levelName;

    /**
     * 提单时间
     * <br/>Column: [submit_time]
     */
    @Column(value = "submit_time")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /**
     * 最后更新时间
     * <br/>Column: [last_update_time]
     */
    @Column(value = "last_update_time")
    private Date lastUpdateTime;

    /**
     * 驳回日志的id，存在此id时，表示当前订单是被驳回的状态
     * <br/>Column: [refuse_log_id]
     */
    @Column(value = "refuse_log_id")
    private Long refuseLogId;

    /**
     * 订单风险等级，高风险》评估中》大盘满足》无风险》已交付<br/>Column: [level_name]
     *
     * @see OrderRiskLevelEnum#getRiskName()
     */
    @Column(value = "risk_level")
    private String riskLevel;

    /**
     * 是否自动预扣，为true时，在到达交付供应节点时会进行自动预扣操作
     * <br/>Column: [auto_pre_deduct]
     */
    @Column(value = "auto_pre_deduct")
    private Boolean autoPreDeduct;

    /**
     * 自动预扣时，预扣开始日期<br/>Column: [auto_pre_deduct_begin_date]
     */
    @Column(value = "auto_pre_deduct_begin_date")
    private LocalDate autoPreDeductBeginDate;

    /**
     * 自动预扣时，预扣结束日期<br/>Column: [auto_pre_deduct_end_date]
     */
    @Column(value = "auto_pre_deduct_end_date")
    private LocalDate autoPreDeductEndDate;

    /**
     * 关闭人<br/>Column: [close_user]
     */
    @Column(value = "close_user")
    private String closeUser;

    /**
     * 关闭时间<br/>Column: [close_date]
     */
    @Column(value = "close_date")
    private LocalDate closeDate;

    /**
     * 关闭原因<br/>Column: [close_reason]
     */
    @Column(value = "close_reason")
    private String closeReason;

    /**
     * 闲置周转天数  <br/>Column: [idle_turnover_days]
     */
    @Column(value = "idle_turnover_days")
    private Integer idleTurnoverDays;

    /**
     * 最晚关闭时间<br/>Column: [latest_order_closing_time]
     */
    @Column(value = "latest_order_closing_time")
    private LocalDate latestOrderClosingTime;

    /**
     * 弹性类型，{@link OrderElasticType#getTypeName()}
     */
    @Column(value = "elastic_type")
    private String elasticType;

    /**
     * 周期性弹性日期，1表示1号或者周一，7表示7号或者周日，根据弹性类型是周弹性还是月弹性来判断，用英文;分隔
     */
    @Column(value = "elastic_cycle_day")
    private String elasticCycleDay;

    /**
     *  预扣备选uin，多个用英文;分隔
     */
    @Column(value = "share_uin")
    private String shareUin;

    /**
     * 子产品
     */
    @Column(value = "sub_product")
    private String subProduct;

    /** 订单标签 */
    @Column(value = "order_label", insertValueScript = "'不打标'")
    private String orderLabel;

    /** 订单原因：order_label = 不统计时必填
     * @see cloud.demand.app.modules.order.enums.OrderLabelReasonEnum
     * */
    @Column(value = "label_reason", insertValueScript = "''")
    private String labelReason;

    /** 备注：订单标签备注 */
    @Column(value = "label_remark", insertValueScript = "''")
    private String labelRemark;

    /** 订单报表统计位数：
     * 1023 最多支持 10 个报表
     * @see cloud.demand.app.modules.order.enums.OrderReportBiteEnum
     * */
    @Column(value = "report_bit_num", insertValueScript = "1023")
    private Integer reportBitNum;

    /** 备注：标签更新人 */
    @Column(value = "label_update_user")
    private String labelUpdateUser;

    /** 备注：标签更新时间 */
    @Column(value = "label_update_time")
    private LocalDateTime labelUpdateTime;

    @JsonIgnore
    public List<String> getReportNameList(){
        return OrderReportBiteEnum.parseBitNum(this.getReportBitNum());
    }

    public void checkForbidModifyField(OrderInfoDO oldOrderInfo) {
        if (oldOrderInfo == null) {
            return;
        }
        if (!Objects.equals(oldOrderInfo.getProduct(), this.getProduct())) {
            throw BizException.makeThrow("产品不允许修改");
        }
        if (!Objects.equals(oldOrderInfo.getAppRole(), this.getAppRole())) {
            throw BizException.makeThrow("应用角色字段不允许修改");
        }
        if (!Objects.equals(oldOrderInfo.getOrderCategory(), this.getOrderCategory())) {
            throw BizException.makeThrow("订单类别不允许修改");
        }
        if (!Objects.equals(oldOrderInfo.getSubProduct(), this.getSubProduct())) {
            throw BizException.makeThrow("子产品不允许修改");
        }
    }

    public List<String> shareUinListGet() {
        if (StrUtil.isBlank(shareUin)) {
            return new ArrayList<>();
        }
        String[] array = shareUin.split(";");
        List<String> res = new ArrayList<>();
        for (String s : array) {
            if (StrUtil.isNotBlank(s)) {
                res.add(s);
            }
        }
        return res;
    }

    public void shareUinSetByList(List<String> shareUinList) {
        if (ListUtils.isEmpty(shareUinList)) {
            this.setShareUin(null);
            return;
        }
        StringJoiner joiner = new StringJoiner(";");
        shareUinList.forEach(joiner::add);
        this.setShareUin(joiner.toString());
    }

    public List<Integer> elasticCycleDayListGet() {
        if (StrUtil.isBlank(elasticCycleDay)) {
            return new ArrayList<>();
        }
        String[] array = this.elasticCycleDay.split(";");
        List<Integer> res = new ArrayList<>();
        for (String s : array) {
            res.add(Integer.parseInt(s));
        }
        return res;
    }

    public void elasticCycleDaySetByList(List<Integer> elasticCycleDayList) {
        if (ListUtils.isEmpty(elasticCycleDayList)) {
            this.setElasticCycleDay(null);
            return;
        }
        StringJoiner joiner = new StringJoiner(";");
        elasticCycleDayList.forEach(integer -> joiner.add(String.valueOf(integer)));
        this.setElasticCycleDay(joiner.toString());
    }


    /**
     * 检查订单优先级参数，不在规定范围内时返回提示信息，返回 null 时表示检查通过
     */
    public static String checkLevelName(String inputLevelName) {
        List<String> levels = ListUtils.newArrayList("低优先级", "中优先级", "高优先级");
        if (levels.contains(inputLevelName)) {
            return null;
        }
        return StrUtil.format("输入订单优先级【{}】不在规定的订单优先级值范围内【{}】",
                inputLevelName, JSON.toJson(levels));
    }

    public OrderInfoDO copyNewToInsert() {
        OrderInfoDO orderNew = new OrderInfoDO();
        BeanUtils.copyProperties(this, orderNew);
        orderNew.clearSomeValueForInsertNew();
        return orderNew;
    }

    public void clearSomeValueForInsertNew() {
        this.setId(null);
        this.setCreateTime(null);
        this.setUpdateTime(null);
        this.setDeleted(false);
        // 清空当前可处理人
        this.setCurrentProcessor(null);
    }

    /**
     * 设置当前可处理人，当前可处理人用英文分号拼接
     *
     * @param userList 当前可处理人
     */
    public void currentProcessorValueSet(List<String> userList) {
        if (ListUtils.isEmpty(userList)) {
            this.setCurrentProcessor(null);
            return;
        }
        // 当前可处理人用英文分号拼接
        StringJoiner joiner = new StringJoiner(";");
        userList.forEach(joiner::add);
        this.setCurrentProcessor(joiner.toString());
    }

    /**
     * 获取当前可处理人
     */
    public List<String> currentProcessorListGet() {
        if (Strings.isBlank(this.currentProcessor)) {
            return new ArrayList<>();
        }
        String[] array = this.currentProcessor.split(";");
        return ListUtils.newArrayList(array);
    }

    /**
     * 获取订单关注人列表
     */
    public List<String> orderFollowerListGet() {
        if (Strings.isBlank(this.orderFollower)) {
            return new ArrayList<>();
        }
        String[] array = this.orderFollower.split(";");
        return ListUtils.newArrayList(array);
    }

    /**
     * 设置订单关注人，订单关注人用英文分号拼接
     *
     * @param userList 订单关注人
     */
    public void orderFollowerListSet(List<String> userList) {
        if (ListUtils.isEmpty(userList)) {
            this.setOrderFollower(null);
            return;
        }
        // 订单关注人用英文分号拼接
        StringJoiner joiner = new StringJoiner(";");
        userList.forEach(joiner::add);
        this.setOrderFollower(joiner.toString());
    }

    /**
     * 获取通知用户 提交人、架构师、订单跟进人
     *
     * @return
     */
    public String getNoticeUser() {
        Set<String> noticeUser = new HashSet<>();
        if (StringUtils.isNotBlank(this.getSubmitUser())) {
            noticeUser.add(this.getSubmitUser());
        }
        if (StringUtils.isNotBlank(this.getArchitect())) {
            noticeUser.add(this.getArchitect());
        }
        if (StringUtils.isNotBlank(this.getOrderFollower())) {
            noticeUser.addAll(orderFollowerListGet());
        }

        if (ListUtils.isNotEmpty(noticeUser)) {
            return String.join(";", noticeUser);
        } else {
            return "";
        }
    }

    /**
     * 构建通用通知模板参数 提单人,订单号，行业部门
     *
     * @return
     */
    public Map<String, Object> buildCommonNoticeTemplate() {

        Map<String, Object> templateParams = new HashMap<>();
        if (StringUtils.isNotBlank(this.getOrderNumber())) {
            templateParams.put("orderNumber", this.getOrderNumber());
        }
        if (StringUtils.isNotBlank(this.getSubmitUser())) {
            templateParams.put("submitUser", this.getSubmitUser());
        }
        if (StringUtils.isNotBlank(this.getIndustryDept())) {
            templateParams.put("industryDept", this.getIndustryDept());
        }
        String userNameWithSystem = LoginUtils.getUserNameWithSystem();
        if (StringUtils.isNotBlank(userNameWithSystem)) {
            templateParams.put("operator", userNameWithSystem);
        }
        return templateParams;

    }

    /**
     *  是否为周期性弹性（包括日弹性、周弹性、月弹性）
     */
    public boolean cycleElasticReturnTrue() {
        if (!OrderTypeEnum.ELASTIC.getCode().equals(this.getOrderType())) {
            return false;
        }
        List<String> elasticTypes = OrderElasticType.cycleElasticTypes;
        return elasticTypes.contains(this.getElasticType());
    }

}
