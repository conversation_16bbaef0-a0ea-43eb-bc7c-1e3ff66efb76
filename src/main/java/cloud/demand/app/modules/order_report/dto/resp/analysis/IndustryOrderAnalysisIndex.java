package cloud.demand.app.modules.order_report.dto.resp.analysis;

import cloud.demand.app.modules.order_report.enums.index.IIndustryOrderAnalysisIndex;
import cloud.demand.app.modules.order_report.model.IConsensusDemandDetail;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class IndustryOrderAnalysisIndex extends IndustryOrderAnalysisField implements IIndustryOrderAnalysisIndex,
        IConsensusDemandDetail {
    /** 共识需求量-cpu核数<br/>Column: [consensus_demand_cpu_num] */
    @Column(value = "consensus_demand_cpu_num")
    @ExcelProperty(value = "共识需求量(核)",index = 29)
    private BigDecimal consensusDemandCpuNum;

    /** 共识需求量-gpu卡数<br/>Column: [consensus_demand_gpu_num] */
    @Column(value = "consensus_demand_gpu_num")
    @ExcelProperty(value = "共识需求量(卡)",index = 30)
    private BigDecimal consensusDemandGpuNum;

    /** 需求量-数据库存储GB<br/>Column: [total_database_storage] */
    @Column(value = "total_database_storage")
    @ExcelProperty(value = "需求量(存储GB)",index = 31)
    private BigDecimal totalDatabaseStorage;

    /** 需求量-数据库内存GB<br/>Column: [total_memory] */
    @Column(value = "total_memory")
    @ExcelProperty(value = "需求量(内存GB)",index = 32)
    private BigDecimal totalMemory;

    /** 已满足量-cpu核数<br/>Column: [satisfied_cpu_num] */
    @Column(value = "satisfied_cpu_num")
    @ExcelProperty(value = "已满足量(核)",index = 33)
    private BigDecimal satisfiedCpuNum;

    /** 已满足量-gpu卡数<br/>Column: [satisfied_gpu_num] */
    @Column(value = "satisfied_gpu_num")
    @ExcelProperty(value = "已满足量(卡)",index = 34)
    private BigDecimal satisfiedGpuNum;

    /** 待满足量-cpu核数<br/>Column: [wait_satisfied_cpu_num] */
    @Column(value = "wait_satisfied_cpu_num")
    @ExcelProperty(value = "待满足量(核)",index = 35)
    private BigDecimal waitSatisfiedCpuNum;

    /** 待满足量-gpu卡数<br/>Column: [wait_satisfied_gpu_num] */
    @Column(value = "wait_satisfied_gpu_num")
    @ExcelProperty(value = "待满足量(卡)",index = 36)
    private BigDecimal waitSatisfiedGpuNum;

    /** 已履约量-cpu核数<br/>Column: [buy_num] */
    @Column(value = "buy_num")
    @ExcelProperty(value = "已履约量(核)",index = 37)
    private BigDecimal buyNum;

    /** 已履约量-gpu卡数<br/>Column: [buy_gpu_num] */
    @Column(value = "buy_gpu_num")
    @ExcelProperty(value = "已履约量(卡)",index = 38)
    private BigDecimal buyGpuNum;

    /**
     * 已满足未履约量-cpu核数 = 满足量 - 已履约量
     * <br/>Column: [satisfied_wait_buy_num] */
    @Column(value = "satisfied_wait_buy_num")
    @ExcelProperty(value = "已满足未履约量(核)",index = 39)
    private BigDecimal satisfiedWaitBuyNum;

    /**
     * 已满足未履约量-gpu卡数 = 满足量 - 已履约量
     * <br/>Column: [satisfied_wait_buy_gpu_num] */
    @Column(value = "satisfied_wait_buy_gpu_num")
    @ExcelProperty(value = "已满足未履约量(卡)",index = 40)
    private BigDecimal satisfiedWaitBuyGpuNum;

    /**
     * 总未履约量-cpu核数 = 共识需求量 - 未履约量
     * <br/>Column: [total_wait_buy_num] */
    @Column(value = "total_wait_buy_num")
    @ExcelProperty(value = "总未履约量(核)",index = 41)
    private BigDecimal totalWaitBuyNum;

    /**
     * 总未履约量-gpu卡数 = 共识需求量 - 未履约量
     * <br/>Column: [total_wait_buy_gpu_num] */
    @Column(value = "total_wait_buy_gpu_num")
    @ExcelProperty(value = "总未履约量(卡)",index = 42)
    private BigDecimal totalWaitBuyGpuNum;

    /**
     * 计划预扣量-cpu核数
     * <br/>Column: [cumulative_pre_deduct_cpu_num] */
    @Column(value = "cumulative_pre_deduct_cpu_num")
    @ExcelProperty(value = "计划预扣量(核)",index = 43)
    private BigDecimal cumulativePreDeductCpuNum;

    /**
     * 计划预扣量-gpu卡数
     * <br/>Column: [cumulative_pre_deduct_gpu_num] */
    @Column(value = "cumulative_pre_deduct_gpu_num")
    @ExcelProperty(value = "计划预扣量(卡)",index = 44)
    private BigDecimal cumulativePreDeductGpuNum;

    /**
     * 计划搬迁满足量-cpu核数
     * <br/>Column: [move_supply_cpu_num] */
    @Column(value = "move_supply_cpu_num")
    @ExcelProperty(value = "计划搬迁满足量(核)",index = 45)
    private BigDecimal moveSupplyCpuNum;

    /**
     * 计划搬迁满足量-gpu卡数
     * <br/>Column: [move_supply_gpu_num] */
    @Column(value = "move_supply_gpu_num")
    @ExcelProperty(value = "计划搬迁满足量(卡)",index = 46)
    private BigDecimal moveSupplyGpuNum;

    /**
     * 计划采购满足量-cpu核数
     * <br/>Column: [buy_supply_cpu_num] */
    @Column(value = "buy_supply_cpu_num")
    @ExcelProperty(value = "计划采购满足量(核)",index = 47)
    private BigDecimal buySupplyCpuNum;

    /**
     * 计划采购满足量-gpu卡数
     * <br/>Column: [buy_supply_gpu_num] */
    @Column(value = "buy_supply_gpu_num")
    @ExcelProperty(value = "计划采购满足量(卡)",index = 48)
    private BigDecimal buySupplyGpuNum;


}
