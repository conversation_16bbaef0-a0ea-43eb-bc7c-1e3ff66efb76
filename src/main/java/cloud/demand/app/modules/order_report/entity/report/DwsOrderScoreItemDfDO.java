package cloud.demand.app.modules.order_report.entity.report;

import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.order_report.dto.resp.report.IOrderScoreRank;
import cloud.demand.app.modules.order_report.enums.field.IOrderScoreField;
import cloud.demand.app.modules.order_report.model.clean.IScoreLevelClean;
import cloud.demand.app.modules.order_report.model.score.*;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;
import teglib.shaded.com.google.common.hash.*;

import java.math.BigDecimal;
import java.nio.charset.Charset;

@Data
@ToString
@Table("dws_order_score_item_df")
public class DwsOrderScoreItemDfDO extends DwdOrderBaseItemDfDO implements IOrderScoreField, IOrderScoreRank,
        IAdvanceWeekScore,
        IBillTypeScore,
        IHistoricalFulfillmentScore,
        IHistoricalUnsatisfiedScore,
        ITotalScore,
        IScoreLevelClean {

    @Column(value = "id")
    private String id;

    @Column(value = "version")
    private String version;

    /**
     * 历史履约等级<br/>Column: [historical_fulfillment_level]
     */
    @Column(value = "historical_fulfillment_level")
    private String historicalFulfillmentLevel;

    /**
     * 提前周得分<br/>Column: [advance_week_score]
     */
    @Column(value = "advance_week_score")
    private BigDecimal advanceWeekScore;

    /**
     * 计费类型得分<br/>Column: [bill_type_score]
     */
    @Column(value = "bill_type_score")
    private BigDecimal billTypeScore;

    /**
     * 历史履约得分<br/>Column: [historical_fulfillment_score]
     */
    @Column(value = "historical_fulfillment_score")
    private BigDecimal historicalFulfillmentScore;

    /**
     * 历史未满足追加得分
     * */
    @Column(value = "historical_unsatisfied_score")
    private BigDecimal historicalUnsatisfiedScore;


    /**
     * 总得分<br/>Column: [total_score]
     */
    @Column(value = "total_score")
    private BigDecimal totalScore;

    /**
     * 调整总得分<br/>Column: [adj_total_score]
     */
    @Column(value = "adj_total_score")
    private BigDecimal adjTotalScore;

    /**
     * 得分档位<br/>Column: [score_level]
     */
    @Column(value = "score_level")
    private String scoreLevel;

    /**
     * 调整得分档位<br/>Column: [adj_score_level]
     */
    @Column(value = "adj_score_level")
    private String adjScoreLevel;

    /**
     * 获取 SipHash24 hash 后的值
     */
    @JsonIgnore
    public static String genSipHash24(DwsOrderScoreItemDfDO item) {
        if (item == null){
            return null;
        }
        // k0：506097522914230528L, k1：1084818905618843912L
        HashFunction hf = Hashing.sipHash24();
        HashCode hash = hf.newHasher()
                .putString(SoeCommonUtils.getString(item.getProduct()), Charset.defaultCharset())
                .putString(SoeCommonUtils.getString(item.getIndustryDept()), Charset.defaultCharset())
                .putString(SoeCommonUtils.getString(item.getCustomerShortName()), Charset.defaultCharset())
                .putString(SoeCommonUtils.getString(item.getUin()), Charset.defaultCharset())
                .putString(SoeCommonUtils.getString(item.getRegionName()), Charset.defaultCharset())
                .putString(SoeCommonUtils.getString(item.getZoneName()), Charset.defaultCharset())
                .putString(SoeCommonUtils.getString(item.getInstanceType()), Charset.defaultCharset())
                .putString(SoeCommonUtils.getString(item.getYearMonth()), Charset.defaultCharset())
                .putString(SoeCommonUtils.getLocalDate(item.getBeginBuyDate()), Charset.defaultCharset())
                .putString(SoeCommonUtils.getLocalDate(item.getEndBuyDate()), Charset.defaultCharset())
                .putString(SoeCommonUtils.getString(item.getBillType()), Charset.defaultCharset())
                .hash();
        return hash.toString();
    }

    /** 生成 id */
    @JsonIgnore
    public static String genId(DwsOrderScoreItemDfDO item){
        return item.getOrderNumber() + "-" + genSipHash24(item);
    }


    /**
     * dwd to dws
     */
    @JsonIgnore
    public static DwsOrderScoreItemDfDO transform(DwdOrderBaseItemDfDO item) {
        DwsOrderScoreItemDfDO ret = new DwsOrderScoreItemDfDO();
//        ret.setId();
//        ret.setHistoricalFulfillmentLevel();
//        ret.setAdvanceWeekScore();
//        ret.setBillTypeScore();
//        ret.setHistoricalFulfillmentScore();
//        ret.setTotalScore();
//        ret.setAdjTotalScore();
        ret.setStatTime(item.getStatTime());
        ret.setProductType(item.getProductType());
        ret.setProduct(item.getProduct());
        ret.setIndustryDept(item.getIndustryDept());
        ret.setWarZone(item.getWarZone());
        ret.setCustomerShortName(item.getCustomerShortName());
        ret.setUin(item.getUin());
        ret.setAppId(item.getAppId());
        ret.setCustomhouseTitle(item.getCustomhouseTitle());
        ret.setCountryName(item.getCountryName());
        ret.setAreaName(item.getAreaName());
        ret.setRegionName(item.getRegionName());
        ret.setZoneName(item.getZoneName());
        ret.setInstanceFamily(item.getInstanceFamily());
        ret.setGenerationInstanceType(item.getGenerationInstanceType());
        ret.setPurchaseInstanceType(item.getPurchaseInstanceType());
        ret.setInstanceType(item.getInstanceType());
        ret.setYearMonth(item.getYearMonth());
        ret.setBeginBuyDate(item.getBeginBuyDate());
        ret.setEndBuyDate(item.getEndBuyDate());
        ret.setBillType(item.getBillType());
        ret.setOrderNumber(item.getOrderNumber());
        ret.setTotalCore(item.getTotalCore());
        ret.setW0(item.getW0());
        ret.setW1(item.getW1());
        ret.setW2(item.getW2());
        ret.setW3(item.getW3());
        ret.setW4(item.getW4());
        ret.setW5(item.getW5());
        ret.setW6(item.getW6());
        ret.setW7(item.getW7());
        ret.setW8(item.getW8());
        ret.setW9(item.getW9());
        ret.setW10(item.getW10());
        ret.setW11(item.getW11());
        ret.setW12(item.getW12());
        ret.setW13(item.getW13());
        ret.setPplIdWInfo(item.getPplIdWInfo());
        ret.setVersionCodeWInfo(item.getVersionCodeWInfo());
        return ret;
    }
}
