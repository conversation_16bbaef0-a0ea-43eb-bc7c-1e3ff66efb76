package cloud.demand.app.modules.order_report.dto.resp.analysis;

import java.math.BigDecimal;
import lombok.Data;

/** 行业订单分析总览 */
@Data
public class IndustryOrderAnalysisSummaryResp {
    private BigDecimal totalCoreNum; // 总核数
    private BigDecimal totalGpuNum; // 总卡数
    private BigDecimal totalStorageNum; // 总存储
    private BigDecimal totalMemoryNum; // 总内存
    private BigDecimal waitCoreNum; // 待审批核数
    private BigDecimal waitGpuNum; // 待审批卡数
    private BigDecimal waitStorageNum; // 待审批存储
    private BigDecimal waitMemoryNum; // 待审批内存
    private BigDecimal waitSatisfiedCoreNum; // 待满足核数
    private BigDecimal waitSatisfiedGpuNum; // 待满足卡数
    private BigDecimal satisfiedCoreNum; // 已满足核数
    private BigDecimal satisfiedGpuNum; // 已满足卡数
    private BigDecimal satisfiedWaitBuyCpuNum; // 已满足待购买核数
    private BigDecimal satisfiedWaitBuyGpuNum; // 已满足待购买卡数
    private BigDecimal closeCoreNum; // 已关闭核数
    private BigDecimal closeGpuNum; // 已关闭卡数
    private BigDecimal closeStorageNum; // 已关闭存储
    private BigDecimal closeMemoryNum; // 已关闭内存
    private BigDecimal cancelCoreNum; // 未通过核数
    private BigDecimal cancelGpuNum; // 未通过卡数
    private BigDecimal cancelStorageNum; // 未通过存储
    private BigDecimal cancelMemoryNum; // 未通过内存
    private BigDecimal supplyStorageNum; // 交付供应存储
    private BigDecimal supplyMemoryNum; // 交付供应内存

}
