package cloud.demand.app.modules.order_report.entity.dict;

import cloud.demand.app.modules.order_report.enums.score.OrderBillTypeEnum;
import cloud.demand.app.modules.soe.entitiy.dict.BasVersionCommonDO;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/** 需求类型得分 */
@Data
@Table("bas_bill_type_score")
public class BasBillTypeScoreDO extends BasVersionCommonDO {
    /** 自增 id<br/>Column: [id] */
    @ExcelIgnore
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 提前周<br/>Column: [advance_week] {@link OrderBillTypeEnum} */
    @Column(value = "bill_type")
    @ExcelProperty(value = "计费类型",index = 0)
    private String billType;

    /** 得分<br/>Column: [score] */
    @Column(value = "score")
    @ContentStyle(dataFormat = 1) // 数字格式
    @ExcelProperty(value = "得分",index = 1)
    private Integer score;

}
