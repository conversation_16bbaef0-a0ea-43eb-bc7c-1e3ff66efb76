package cloud.demand.app.modules.order_report.service;

import cloud.demand.app.modules.order_report.dto.req.change.OrderScoreApprovalReq;
import cloud.demand.app.modules.order_report.dto.req.change.OrderScoreChangeCreateReq;
import cloud.demand.app.modules.order_report.dto.req.change.OrderScoreChangeDetailReq;
import cloud.demand.app.modules.order_report.dto.req.change.OrderScoreChangeReq;
import cloud.demand.app.modules.order_report.dto.resp.change.OrderScoreChangeVo;
import cloud.demand.app.modules.order_report.dto.resp.report.IChangeDetailList;
import cloud.demand.app.modules.order_report.model.OrderChangeSimpleItem;
import com.pugwoo.dbhelper.model.PageData;

import java.util.List;

/** 评分变更 */
public interface OrderScoreChangeService {
    /** 查询列表 */
    PageData<OrderScoreChangeVo> pageList(OrderScoreChangeReq req);

    /** 获取详细 */
    OrderScoreChangeVo getDetail(OrderScoreChangeDetailReq req);

    /** 创建订单变更 */
    void create(OrderScoreChangeCreateReq req);

    /** 审批 */
    void approval(OrderScoreApprovalReq req);

    /** 获取当天的得分变更列表 */
    List<OrderChangeSimpleItem> getNowDayChangeList();

    /** 获取已审批通过的评分变更列表 */
    List<OrderChangeSimpleItem> getApprovalChangeList();

    /** 获取指定 order_id 的变更列表 */
    List<OrderChangeSimpleItem> getListByOrderId(List<String> orderId);

    /** 填充变更明细 */
    void fillChangeDetail(List<? extends IChangeDetailList> data);
}
