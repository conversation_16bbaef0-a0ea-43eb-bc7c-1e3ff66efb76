package cloud.demand.app.modules.order_report.dto.resp.change;

import cloud.demand.app.modules.order_report.entity.change.OrderScoreChangeOrderDO;
import cloud.demand.app.modules.order_report.entity.change.OrderScoreChangeOrderDetailDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

import java.util.List;

@Data
public class OrderScoreChangeVo extends OrderScoreChangeOrderDO {
    @RelatedColumn(localColumn = "order_id", remoteColumn = "order_id")
    private List<OrderScoreChangeOrderDetailDO> details;
}
