package cloud.demand.app.modules.order_report.enums.field;

import cloud.demand.app.modules.mrpv3.enums.field.IProduct;
import cloud.demand.app.modules.mrpv3.enums.field.IStatTime;
import cloud.demand.app.modules.mrpv3.enums.field.IUin;
import cloud.demand.app.modules.soe.model.fields.*;
import cloud.demand.app.modules.sop_review.enums.fields.IInstanceFamily;
import cloud.demand.app.modules.sop_review.enums.fields.IVersion;

public interface IOrderScoreField extends
        IStatTime,
        IVersion,
        IProductType,
        IProduct,
        IIndustryDept,
        IWarZone,
        ICustomerShortName,
        IUin,
        IAppId,
        ICustomhouseTitle,
        ICountryName,
        IAreaName,
        IRegionName,
        IZoneName,
        IInstanceFamily,
        IInstanceType,
        IYearMonth,
        IBeginBuyDate,
        IEndBuyDate,
        IBillType,
        IOrderNumber{
}
