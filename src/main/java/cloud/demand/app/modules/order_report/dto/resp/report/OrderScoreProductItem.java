package cloud.demand.app.modules.order_report.dto.resp.report;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderScoreProductItem {
    private BigDecimal totalCore; // 总核心数
    private String zoneName; // 可用区
    private String countryName; // 国家
    private String instanceType; // 实例类型
    private String yearMonth; // 年月
    private List<OrderScoreRankItem> data; // 排名列表

}
