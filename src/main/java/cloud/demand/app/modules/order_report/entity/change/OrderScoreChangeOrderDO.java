package cloud.demand.app.modules.order_report.entity.change;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
/**
 * 评分变更单-评分报表
 */
@Data
@ToString
@Table("order_score_change_order")
public class OrderScoreChangeOrderDO {
    /** 自增 id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 变更单号（RC 开头）<br/>Column: [order_id] */
    @Column(value = "order_id")
    private String orderId;

    /** 需求年月<br/>Column: [year_month] */
    @Column(value = "year_month")
    private String yearMonth;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 状态：资源总监审批，已驳回，已生效<br/>Column: [status] */
    @Column(value = "status")
    private String status;

    /** 变更类型<br/>Column: [change_type] */
    @Column(value = "change_reason")
    private String changeReason;

    /** 变更原因<br/>Column: [change_result] */
    @Column(value = "change_reason_detail")
    private String changeReasonDetail;

    /** 创建的时间<br/>Column: [create_time] */
    @Column(value = "create_time",setTimeWhenInsert = true)
    private Date createTime;

    /** 创建用户<br/>Column: [create_user] */
    @Column(value = "create_user",insertValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()")
    private String createUser;

    /** 审批的时间<br/>Column: [approval_time] */
    @Column(value = "approval_time")
    private Date approvalTime;

    @Column(value = "approval_user")
    private String approvalUser;

    @Column(value = "approval_remark",insertValueScript = "''")
    private String approvalRemark;
}
