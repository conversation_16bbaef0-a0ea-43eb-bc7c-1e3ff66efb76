package cloud.demand.app.modules.order_report.enums.score;

import lombok.AllArgsConstructor;
import lombok.Getter;

/** 计费模式 */
@Getter
@AllArgsConstructor
public enum OrderBillTypeEnum {
    POSTPAID_BY_HOUR("POSTPAID_BY_HOUR", "按量计费"),

    PREPAID("PREPAID", "包年包月"),

    CDHPAID("CDHPAID", "包销计费"),

    SPOTPAID("SPOTPAID","竞价计费");

    ;
    private final String code;
    private final String name;

    public static String getByCode(String code) {
        for (OrderBillTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public static String getByCodeOrName(String codeOrName){
        for (OrderBillTypeEnum value : values()) {
            if (value.getCode().equals(codeOrName) || value.getName().equals(codeOrName)) {
                return value.getName();
            }
        }
        return null;
    }
}
