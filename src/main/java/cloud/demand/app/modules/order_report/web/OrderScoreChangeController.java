package cloud.demand.app.modules.order_report.web;


import cloud.demand.app.modules.order_report.dto.req.change.OrderScoreApprovalReq;
import cloud.demand.app.modules.order_report.dto.req.change.OrderScoreChangeCreateReq;
import cloud.demand.app.modules.order_report.dto.req.change.OrderScoreChangeDetailReq;
import cloud.demand.app.modules.order_report.dto.req.change.OrderScoreChangeReq;
import cloud.demand.app.modules.order_report.dto.resp.change.OrderScoreChangeVo;
import cloud.demand.app.modules.order_report.service.OrderScoreChangeService;
import cloud.demand.app.modules.sop.domain.ReturnT;
import com.pugwoo.dbhelper.model.PageData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;

/** 订单评分变更 */
@JsonrpcController("/order-report/score-change")
@Slf4j
public class OrderScoreChangeController {
    @Resource
    private OrderScoreChangeService service;

    @RequestMapping
    public PageData<OrderScoreChangeVo> pageList(@JsonrpcParam @Valid OrderScoreChangeReq req){
        return service.pageList(req);
    }

    @RequestMapping
    public OrderScoreChangeVo getDetail(@JsonrpcParam @Valid OrderScoreChangeDetailReq req){
        return service.getDetail(req);
    }

    @RequestMapping
    public ReturnT<String> create(@JsonrpcParam @Valid OrderScoreChangeCreateReq req){
        service.create(req);
        return ReturnT.ok();
    }

    @RequestMapping
    public ReturnT<String> approval(@JsonrpcParam @Valid OrderScoreApprovalReq req){
        service.approval(req);
        return ReturnT.ok();
    }
}
