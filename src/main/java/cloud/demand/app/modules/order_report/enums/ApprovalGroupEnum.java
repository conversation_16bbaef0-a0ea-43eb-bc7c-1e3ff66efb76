package cloud.demand.app.modules.order_report.enums;

import cloud.demand.app.modules.order_report.DynamicProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.function.Supplier;

/** 评审组 */
@Getter
@AllArgsConstructor
public enum ApprovalGroupEnum {

    approval("审批组", DynamicProperties::getApprovalRole),

    admin("管理员", DynamicProperties::getAdminRole),
    ;

    private final String name;

    private final Supplier<List<String>> getUsers;
}
