package cloud.demand.app.modules.order_report.dto.req.analysis;

import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import java.util.Objects;

/** 产品 */
public interface IOrderCategoryReq {
    public List<String> getOrderCategory();

    public default boolean isDatabase(){
        List<String> orderCategory = getOrderCategory();
        return ListUtils.isNotEmpty(orderCategory) && orderCategory.size() == 1 && Objects.equals(orderCategory.get(0),
                Ppl13weekProductTypeEnum.DATABASE.getCode());
    }
}
