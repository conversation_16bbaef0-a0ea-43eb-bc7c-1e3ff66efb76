package cloud.demand.app.modules.order_report.model;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderChangeSimpleItem {

    @Column("order_id")
    private String orderId; // 订单编号
    @Column("dws_id")
    private String dwsId; // dwsId
    @Column("approval_time")
    private Date approvalTime; // 生效时间
    @Column("total_score")
    private BigDecimal totalScore; // 总分
    @Column("adj_total_score")
    private BigDecimal adjTotalScore; // 调整后总分
}
