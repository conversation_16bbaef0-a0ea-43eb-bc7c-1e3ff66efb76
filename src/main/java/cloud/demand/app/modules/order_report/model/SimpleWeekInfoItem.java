package cloud.demand.app.modules.order_report.model;

import cloud.demand.app.modules.order_report.enums.score.AdvanceWeekEnum;
import cloud.demand.app.modules.soe.entitiy.distribute.IWeekNData;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SimpleWeekInfoItem implements IWeekNData {
    /**
     * 提前0周<br/>Column: [w0]
     */
    private BigDecimal w0;

    /**
     * 提前1周<br/>Column: [w1]
     */
    private BigDecimal w1;

    /**
     * 提前2周<br/>Column: [w2]
     */
    private BigDecimal w2;

    /**
     * 提前3周<br/>Column: [w3]
     */
    private BigDecimal w3;

    /**
     * 提前4周<br/>Column: [w4]
     */
    private BigDecimal w4;

    /**
     * 提前5周<br/>Column: [w5]
     */
    private BigDecimal w5;

    /**
     * 提前6周<br/>Column: [w6]
     */
    private BigDecimal w6;

    /**
     * 提前7周<br/>Column: [w7]
     */
    private BigDecimal w7;

    /**
     * 提前8周<br/>Column: [w8]
     */
    private BigDecimal w8;

    /**
     * 提前9周<br/>Column: [w9]
     */
    private BigDecimal w9;

    /**
     * 提前10周<br/>Column: [w10]
     */
    private BigDecimal w10;

    /**
     * 提前11周<br/>Column: [w11]
     */
    private BigDecimal w11;

    /**
     * 提前12周<br/>Column: [w12]
     */
    private BigDecimal w12;

    /**
     * 提前13周<br/>Column: [w13]
     */
    private BigDecimal w13;

    public SimpleWeekInfoItem(IWeekNData data){
        if (data!= null){
            for (AdvanceWeekEnum value : AdvanceWeekEnum.values()) {
                value.getSetter().accept(this,value.getGetter().apply(data));
            }
        }
    }
}
