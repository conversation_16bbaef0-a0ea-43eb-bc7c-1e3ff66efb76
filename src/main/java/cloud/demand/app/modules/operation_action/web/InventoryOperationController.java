package cloud.demand.app.modules.operation_action.web;

import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.operation_action.entity.InventoryReasonDO;
import cloud.demand.app.modules.operation_action.model.*;
import cloud.demand.app.modules.operation_action.service.OperationService;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.exception.BizException;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import java.util.List;

@Slf4j
@JsonrpcController("/inventory-operation")
public class InventoryOperationController {

    @Autowired
    OperationService operationService;

    @Resource
    private DictService dictService;
    @Resource
    private RedisHelper redisHelper;

    @RequestMapping
    public Object listInventory(@JsonrpcParam FilterReq req) {
        String operationViewAlgorithm = redisHelper.getString("operationViewAlgorithm");
        req.setAlgorithm(operationViewAlgorithm);
        val data = operationService.listInventory(req);
        return ImmutableMap.of("data", data);
    }

    @RequestMapping
    public Object listActionLog(@JsonrpcParam QueryLog queryLog) {
        val data = operationService.listActionLog(queryLog);
        return ImmutableMap.of("data", data);

    }

    @RequestMapping
    public Object addAction(@JsonrpcParam AddActionLogReq req) {
        operationService.addLog(req);
        return ImmutableMap.of("result", "ok");
    }

    @RequestMapping
    public Object addActionBatch(@JsonrpcParam AddActionLogBatchReq req) {
        for (AddActionLogReq areq : req.getActions()) {
            operationService.addLog(areq);
        }

        return ImmutableMap.of("result", "ok");
    }

    @RequestMapping
    public Object updateAction(@JsonrpcParam UpdateActionLog req) {

        operationService.updateLog(req);

        return ImmutableMap.of("result", "ok");
    }

    @RequestMapping
    public Object deleteAction(@JsonrpcParam DeleteLogReq req) {
        val record = operationService.getActionById(req.getId());
        if (record != null) {
            operationService.deleteLog(req);
        }
        return ImmutableMap.of("result", "ok");
    }

    @RequestMapping
    public Object queryOrder(@JsonrpcParam OrderReq orderReq) {
        OrderResp resp = operationService.queryErpOrder(orderReq);
        if (resp == null) {
            throw BizException.makeThrow("单据不存在{}", orderReq.getOrderNo());
        }
        return ImmutableMap.of("data", resp);
    }

    @RequestMapping
    public Object queryReturnOrder(@JsonrpcParam ReturnOrderReq orderReq) {
        List<ReturnOrderResp> resp = operationService.queryReturnOrderList(orderReq);
        if (ListUtils.isEmpty(resp)) {
            throw BizException.makeThrow("单据不存在{}", orderReq.getOrderNo());
        }
        return ImmutableMap.of("data", resp);
    }

    @RequestMapping
    public Object queryDeviceCore(@JsonrpcParam QueryDevice req) {
        Integer core = dictService.getDeviceLogicCpuCore(req.deviceType, true);
        return ImmutableMap.of(req.deviceType, core);
    }

    @RequestMapping
    public Object addReason(@JsonrpcParam AddReasonReq req) {
        operationService.addReason(req);
        return ImmutableMap.of("result", "ok");
    }

    @RequestMapping
    public Object updateReason(@JsonrpcParam UpdateReasonReq req) {
        operationService.updateReason(req);
        return ImmutableMap.of("result", "ok");
    }

    @RequestMapping
    public Object deleteReason(@JsonrpcParam DeleteReasonReq req) {
        operationService.deleteReason(req);
        return ImmutableMap.of("result", "ok");
    }

    @RequestMapping
    public Object queryReason(@JsonrpcParam QueryReasonReq req) {
        InventoryReasonDO result = operationService.queryReason(req);

        if (result == null) {
            return false;
        }

        return result;
    }

    @RequestMapping
    public List<String> queryReasonTypes() {
        return operationService.queryReasonTypes();
    }

    @RequestMapping
    public List<InventoryReasonDO> listReason(@JsonrpcParam QueryLog req) {
        return operationService.listReason(req);
    }

    @Data
    public static class QueryDevice {

        String deviceType;
    }
}
