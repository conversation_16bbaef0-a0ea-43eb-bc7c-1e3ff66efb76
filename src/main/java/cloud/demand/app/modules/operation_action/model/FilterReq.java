package cloud.demand.app.modules.operation_action.model;

import com.google.common.collect.Lists;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;

@Data
public class FilterReq {

    String algorithm;

    /**
     * 数据时间
     */
    LocalDate date ;


    /**
     * 开始时间
     */
    LocalDate startDate;
    /**
     * 结束时间
     */
    LocalDate endDate;
    /**
     * 可用区
     */
    private List<String> zoneName;
    /**
     * 实例类型
     */
    private List<String> instanceType;
    /**
     * 好差呆
     */
    private List<String> materialType = Lists.newArrayList("好料");
    /**
     * 库存类型
     */
    private List<String> lineType;

    /**
     * 主力园区
     */
    private List<String> zoneCategory;

    private List<String> areaName;
    private List<String> regionName;

    private List<String> customhouseTitle;

    private List<String> instanceTypeCategory;
}
