package cloud.demand.app.modules.operation_action.service.impl;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.entity.ResPlanHolidayWeekDO;
import cloud.demand.app.modules.operation_action.entity.FutureDemandDo;
import cloud.demand.app.modules.operation_action.entity.InventoryOperationDO;
import cloud.demand.app.modules.operation_action.entity.InventoryReasonDO;
import cloud.demand.app.modules.operation_action.model.*;
import cloud.demand.app.modules.operation_action.service.OperationService;
import cloud.demand.app.modules.operation_view.inventory_health.enums.CustomerCustomGroupEnum;
import cloud.demand.app.modules.operation_view.operation_view2.model.OperationViewReq2;
import cloud.demand.app.modules.operation_view.operation_view2.model.OperationViewResp2;
import cloud.demand.app.modules.operation_view.operation_view2.model.OperationViewResp2.Item;
import cloud.demand.app.modules.operation_view.operation_view2.model.OperationViewResp2.SafetyInventoryResult;
import cloud.demand.app.modules.operation_view.operation_view2.service.OperationViewService2;
import cloud.demand.app.modules.operation_view.operation_view2.service.impl.OperationViewService2Impl;
import cn.hutool.core.bean.BeanUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OperationServiceImpl implements OperationService {

    private static final Map<String, Function<Item, SafetyInventoryResult>> FUNCTION_MAP = new HashMap<>();

    static {
        FUNCTION_MAP.put("historyWeekPeak", Item::getHistoryWeekPeak);
        FUNCTION_MAP.put("historyWeekDiff", Item::getHistoryWeekDiff);
        FUNCTION_MAP.put("futureWeekPeak", Item::getFutureWeekPeak);
        FUNCTION_MAP.put("historyWeekPeakForecastWN", Item::getHistoryWeekPeakForecastWN);
    }

    public static final Supplier<ArrayList<String>> reasonTypes = DynamicProperty.create("app.config.operation-action.reason-types", "[]", o -> JSON.parse(o, ArrayList.class));

    private final ExecutorService threadPool = Executors.newFixedThreadPool(5);
    @Resource
    DBHelper shuttleDBHelper;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper resourcedbDBHelper;
    @Resource
    private DBHelper ckcldDBHelper;
    @Resource
    private DictService dictService;
    @Resource
    private OperationViewService2 operationViewService2;



    @Override
    public List<InventoryDTO> listInventory(FilterReq req) {
        val statDate =
                req.getDate() == null ? LocalDate.now().minusDays(1).toString() : req.getDate().toString();
        OperationViewReq2 req2 = new OperationViewReq2();
        req2.setDate(DateUtils.parse(statDate));
        req2.setZoneName(req.getZoneName());
        req2.setMaterialType(req.getMaterialType());
        req2.setLineType(req.getLineType());
        req2.setInstanceType(req.getInstanceType());
        req2.setCustomhouseTitle(req.getCustomhouseTitle());

        req2.setAreaName(req.getAreaName());
        req2.setRegionName(req.getRegionName());

        req2.setZoneCategory(req.getZoneCategory());
        req2.setInstanceTypeCategory(req.getInstanceTypeCategory());

        req2.setCustomerCustomGroup(CustomerCustomGroupEnum.ALL.getCode());
        Future<OperationViewResp2> all = threadPool.submit(() -> operationViewService2.queryAllProductSummary(req2));

        OperationViewReq2 reqTop = new OperationViewReq2();
        BeanUtil.copyProperties(req2, reqTop);
        reqTop.setCustomerCustomGroup(CustomerCustomGroupEnum.LIST_REPORT.getCode());
        Future<OperationViewResp2> top = threadPool.submit(() -> operationViewService2.queryAllProductSummary(reqTop));

        OperationViewReq2 reqTail = new OperationViewReq2();
        BeanUtil.copyProperties(req2, reqTail);
        reqTail.setCustomerCustomGroup(CustomerCustomGroupEnum.MEDIUM_LONG_TAIL.getCode());
        Future<OperationViewResp2> tail = threadPool.submit(
                () -> operationViewService2.queryAllProductSummary(reqTail));

        // 查询给定日期下所有的原因分析，然后按照 机型 + 可用区 分组
        Future<Map<String, List<InventoryReasonDO>>> reasonMapFuture = listReasonsParallelByInstanceTypeAndZoneName(statDate);

        Map<String, FutureDemandDo> m = getFutureDemand(statDate);
        Map<String, SlaResultItem> slaMap = getSlaMap(statDate);
        Map<String, InventoryDTO> map = new HashMap<>();
        try {
            OperationViewResp2 allRsp = all.get();
            Map<String, List<InventoryReasonDO>> reasonMap = reasonMapFuture.get();
            //把 全部的结果 用 地区+机型为key， 结果存储起来。方便后续单独填充，头部/中长尾信息
            allRsp.getData().forEach(item -> {
                InventoryDTO inv = new InventoryDTO();
                inv.setCustomhouseTitle(item.getCustomhouseTitle());
                inv.setAreaName(item.getAreaName());
                inv.setRegionName(item.getRegionName());
                inv.setZoneName(item.getZoneName());
                inv.setInstanceType(item.getInstanceType());
                inv.setActInv(item.getInvTotalNum());
                inv.setIdleInv(item.getInvTotalNum());
                inv.setScaleBufferInv(item.getBufferSafetyInv());
                if (FUNCTION_MAP.containsKey(req.getAlgorithm())) {
                    SafetyInventoryResult weeklyInvObj = FUNCTION_MAP.get(req.getAlgorithm()).apply(item);
                    if (weeklyInvObj != null) {
                        inv.setSubSafeInv(weeklyInvObj.getMonthlySafetyInv());
                        inv.setSafeInvManualConfig(weeklyInvObj.getSafeInvManualConfig());
                        inv.setSafeInv(weeklyInvObj.getSafetyInv());
                    }
                }
//                inv.setSafeInv(inv.getSubSafeInv().add(inv.getScaleBufferInv()));
                inv.setGapInv(inv.getSafeInv().subtract(inv.getIdleInv()));

                BigDecimal r = BigDecimal.ZERO;
                if (inv.getSafeInv().intValue() > 0 && inv.getIdleInv().intValue() > 0) {
                    r = NumberUtils.divide(inv.getIdleInv(), inv.getSafeInv(), 4);
                }
                inv.setRedundancy(r);

                String k = inv.getZoneName() + "@" + inv.getInstanceType();
                SlaResultItem slaItem = slaMap.get(k);

                if (slaItem != null) {
                    inv.setSla(slaItem.getSla() != null ? slaItem.getSla() : BigDecimal.ONE);
                    inv.setApiSuccessCores(slaItem.getApiSuccessCores());
                    inv.setApiTotalCores(slaItem.getApiTotalCores());
                    inv.setApiSuccessRate(slaItem.getApiSuccessRate());
                    inv.setApiSoldOut(slaItem.getApiSoldOut());
                    inv.setApiSold(slaItem.getApiSold());
                    inv.setApiSoldOutRate(slaItem.getApiSoldOutRate());
                } else {
                    inv.setSla(BigDecimal.ONE);
                }

                //填充需求
                FutureDemandDo fdo = m.get(k);
                if (fdo != null) {
                    inv.setSubDemand4wTop(fdo.getTop());
                    inv.setSubDemand4wTail(fdo.getTail());
                    inv.setDemand4w(fdo.getTotal());
                }

                // 填充原因详情，如果存在的话
                String key = String.join(inv.getInstanceType(), "@", inv.getZoneName());
                List<InventoryReasonDO> reasons = reasonMap.get(key);

                if (reasons != null && reasons.size() > 0) {
                    // 一个 key 下面只应该存在一个结果，如果存在多个说明有误，取第一个
                    if (reasons.size() > 1) {
                        log.info("同一机型可用区超过一个原因", statDate, inv.getInstanceType(), inv.getZoneName());
                    }

                    InventoryReasonDO reason = reasons.get(0);
                    inv.setReasonId(reason.getId());
                    inv.setReasonType(reason.getReasonType());
                    inv.setReasonDetail(reason.getReasonDetail());
                }
                map.put(k, inv);
            });

            top.get().getData().forEach(item -> {
                String k = item.getZoneName() + "@" + item.getInstanceType();
                InventoryDTO dto = map.get(k);
                // 如果全量都查不到。头部有那就是异常 忽略
                if (dto != null) {
                    Function<Item, SafetyInventoryResult> temp = FUNCTION_MAP.get(req.getAlgorithm());
                    if (temp != null) {
                        SafetyInventoryResult weeklyInvObj = temp.apply(item);
                        if (weeklyInvObj != null) {
                            dto.setSubSafeTopInv(weeklyInvObj.getMonthlySafetyInv());
                        }
                    }else {
                        throw new WrongWebParameterException("算法" + req.getAlgorithm() + "尚未配置");
                    }
                }
            });

            tail.get().getData().forEach(item -> {
                String k = item.getZoneName() + "@" + item.getInstanceType();
                InventoryDTO dto = map.get(k);
                // 如果全量都查不到。头部有那就是异常 忽略
                if (dto != null) {
                    Function<Item, SafetyInventoryResult> temp = FUNCTION_MAP.get(req.getAlgorithm());
                    if (temp != null) {
                        SafetyInventoryResult weeklyInvObj = temp.apply(item);
                        if (weeklyInvObj != null) {
                            dto.setSubSafeTopInv(weeklyInvObj.getMonthlySafetyInv());
                        }
                    }else {
                        throw new WrongWebParameterException("算法" + req.getAlgorithm() + "尚未配置");
                    }
                }
            });

        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }

        return map.values().stream().collect(Collectors.toList());
    }

    @Override
    public List<InventoryOperationDO> listActionLog(QueryLog queryLog) {
        WhereSQL cond = new WhereSQL();
        cond.and("inv_date between ? and ?", queryLog.getStartDate(), queryLog.getEndDate());
        OperationViewService2Impl operationViewService21 = SpringUtil.getBean(OperationViewService2Impl.class);
        WhereSQL categoryCond = operationViewService21.genCategoryCondition(queryLog.getZoneCategory(), queryLog.getInstanceTypeCategory(), false, DateUtils.today().toString());
        cond.and(categoryCond);
        if (ListUtils.isNotEmpty(queryLog.getOpAction())) {
            cond.and("op_action in (?)", queryLog.getOpAction());
        }
        if (ListUtils.isNotEmpty(queryLog.getInstanceType())) {
            cond.and("instance_type in (?)", queryLog.getInstanceType());
        }
        if (ListUtils.isNotEmpty(queryLog.getCustomhouseTitle())) {
            cond.and("customhouse_title in (?)", queryLog.getCustomhouseTitle());
        }
        if (ListUtils.isNotEmpty(queryLog.getAreaName())) {
            cond.and("area_name in (?)", queryLog.getAreaName());
        }
        if (ListUtils.isNotEmpty(queryLog.getRegionName())) {
            cond.and("region_name in (?)", queryLog.getRegionName());
        }
        if (ListUtils.isNotEmpty(queryLog.getZoneName())) {
            cond.and("zone_name in (?)", queryLog.getZoneName());
        }

        val ls = demandDBHelper.getAll(InventoryOperationDO.class, cond.getSQL(), cond.getParams());
        // 填充并回写 线上单据的信息

        ls.parallelStream().forEach(s -> {
            OrderResp resp = queryErpOrder(new OrderReq(s.getOpAction(), s.getOpOrder()));
            if (resp != null) {
                if (resp.getOrderStatus() != null) {
                    s.setOpOrderStatus(resp.getOrderStatus());
                }
                if (resp.getInProgressNum() != null) {
                    s.setOpInProgressDeviceNum(BigDecimal.valueOf(resp.getInProgressNum()));
                }
                if (resp.getInProgressCore() != null) {
                    s.setOpInProgressDeviceCore(BigDecimal.valueOf(resp.getInProgressCore()));
                }
                if (resp.getSlaDate() != null) {
                    s.setOpSlaDate(resp.getSlaDate());
                }
                if (resp.getFinishDate() != null) {
                    s.setOpFinishDate(LocalDate.parse(resp.getFinishDate()));
                }
            }
        });

        return ls;
    }

    @Override
    public void deleteLog(DeleteLogReq req) {
        InventoryOperationDO record = getActionById(req.getId());
        if (record != null) {
            demandDBHelper.delete(record);
        }
    }

    @Override
    public void addLog(AddActionLogReq req) {
        InventoryOperationDO ivo = new InventoryOperationDO();

        //通过req同名属性赋值给ivo

        StaticZoneDO sz = dictService.getStaticZoneInfoByName(req.getZoneName());
        if (sz != null) {
            ivo.setRegionName(sz.getRegionName());
            ivo.setAreaName(sz.getAreaName());
            ivo.setCustomhouseTitle(sz.getCustomhouseTitle());
        }

        ivo.setZoneName(req.getZoneName());
        ivo.setInstanceType(req.getInstanceType());
        ivo.setActInv(req.getActInv());
        ivo.setIdleInv(req.getIdleInv());

        ivo.setScaleBufferInv(req.getScaleBufferInv());
        ivo.setSafeInv(req.getSafeInv());
        ivo.setSubSafeInv(req.getSubSafeInv());
        ivo.setSubSafeTailInv(req.getSubSafeTailInv());
        ivo.setSubSafeTopInv(req.getSubSafeTopInv());
        ivo.setGapInv(req.getGapInv());
        ivo.setDemand4w(req.getDemand4w());
        ivo.setScaleDemand4w(req.getScaleDemand4w());

        ivo.setOpAction(req.getOpAction());
        ivo.setActionDate(req.getActionDate());
        ivo.setInvDate(req.getInvDate());
        ivo.setOpDeviceType(req.getOpDeviceType());
        ivo.setOpDeviceNum(req.getOpDeviceNum());
        ivo.setOpDeviceCore(req.getOpDeviceCore());
        ivo.setOpOrder(req.getOpOrder());
        ivo.setOpOrderDate(req.getOpOrderDate());
        ivo.setOpOrderStatus(req.getOpOrderStatus());
        ivo.setOpExpectDate(req.getOpExpectDate());
        ivo.setOpSlaDate(req.getOpSlaDate());
        ivo.setOpFinishDate(req.getOpFinishDate());
        ivo.setOpMark(req.getOpMark());
        ivo.setOpGoal(req.getOpGoal());

        demandDBHelper.insert(ivo);
    }


    @Override
    public void updateLog(UpdateActionLog req) {
        val record = getActionById(req.getId());
        if (record == null) {
            return;
        }
        record.setOpAction(req.getOpAction());
        record.setOpDeviceType(req.getOpDeviceType());
        record.setOpDeviceNum(req.getOpDeviceNum());
        record.setOpDeviceCore(req.getOpDeviceCore());
        record.setOpOrder(req.getOpOrder());
        record.setOpOrderDate(req.getOpOrderDate());
        record.setOpOrderStatus(req.getOpOrderStatus());
        record.setOpExpectDate(req.getOpExpectDate());
        record.setOpSlaDate(req.getOpSlaDate());
        record.setOpFinishDate(req.getOpFinishDate());
        record.setOpMark(req.getOpMark());
        record.setOpGoal(req.getOpGoal());
        demandDBHelper.update(record);
    }

    @Override
    public InventoryOperationDO getActionById(Integer id) {
        return demandDBHelper.getOne(InventoryOperationDO.class, "where id=?", id);
    }

    @Override
    public OrderResp queryErpOrder(OrderReq req) {
        OrderResp resp = new OrderResp();
        resp.setOrderNo(req.getOrderNo());
        val type = OrderType.get(req.getOpAction());

        switch (type) {
            case PURCHASE:
            case PURCHASE_DELAY:
            case PURCHASE_CANCEL:
                resp = queryQuotaOrder(resp.getOrderNo());
                if (resp != null) {
                    Integer core = dictService.getDeviceLogicCpuCore(resp.getDeviceType(), false);

                    if (resp.getInProgressNum() != null) {
                        resp.setInProgressCore(core * resp.getInProgressNum());
                    } else {
                        resp.setInProgressCore(core);
                    }

                    if (resp.getNum() != null) {
                        resp.setCore(core * resp.getNum());
                    } else {
                        resp.setCore(core);
                    }
                }
                break;
            case MOVE:
            case MOVE_CANCEL:
                resp = queryMoveOrder(resp.getOrderNo());
                break;
            case RETURN:
                resp = queryReturnOrder(resp.getOrderNo());
                break;
            default:
                break;
        }

        return resp;
    }

    @Override
    public List<ReturnOrderResp> queryReturnOrderList(ReturnOrderReq req) {
        String sql = "select\n" +
                "a.OrderId order_no,\n" +
                "device_type,\n" +
                "s.SubZoneName campus,\n" +
                "COUNT(1) as num,\n" +
                "if(a.FinishTime=0,'退回流程中','退回完成') order_status,\n" +
                "sum(cpu_logic_core) core,\n" +
                "min(date(a.CreateTime)) as order_date,\n" +
                "date(a.FinishTime) finish_date\n" +
                "from  cloud_matrix.matrix_return_order  a\n" +
                "left join cloud_matrix.matrix_return_order_detail d on a.OrderId =d.OrderId\n" +
                "left join t_resource.server_parts_extended_info sp on d.DeviceClass  = sp.device_type\n" +
                "left join t_resource.sto_rmdb_servers s on s.Id = d.ServerId\n" +
                "where a.OrderId = ?\n" +
                "and sp.default_flag = 1\n" +
                "group by a.OrderId, device_type, s.SubZoneName;";
        List<ReturnOrderResp> result = ListUtils.newList();

        for (ReturnOrderResp item : resourcedbDBHelper.getRaw(ReturnOrderResp.class, sql, req.getOrderNo())) {
            if (StringTools.isNotBlank(item.getDeviceType())) {
                item.setInstanceType(dictService.getCsigInstanceTypeByDeviceType(item.getDeviceType()));
            }

            if (StringTools.isNotBlank(item.getCampus())) {
                item.setZoneName(dictService.getZoneNameByCampusAndBizType(item.getCampus()));
            }

            if (StringTools.isNotBlank(req.getInstanceType()) && (item.getInstanceType() == null || !item.getInstanceType().equals(req.getInstanceType()))) {
                continue;
            }

            if (StringTools.isNotBlank(req.getZoneName()) && (item.getZoneName() == null || !item.getZoneName().equals(req.getZoneName()))) {
                continue;
            }

            result.add(item);
        }
        return result;
    }

    private InventoryReasonDO getInventoryReasonDo(AddReasonReq req) {
        InventoryReasonDO record = new InventoryReasonDO();
        record.setDate(req.getDate());
        record.setReasonType(req.getReasonType());
        record.setReasonDetail(req.getReasonDetail());
        record.setInstanceType(req.getInstanceType());
        record.setZoneName(req.getZoneName());
        return record;
    }

    @Override
    public void addReason(AddReasonReq req) {
        QueryReasonReq queryReasonReq = new QueryReasonReq();
        queryReasonReq.setDate(req.getDate());
        queryReasonReq.setInstanceType(req.getInstanceType());
        queryReasonReq.setZoneName(req.getZoneName());

        InventoryReasonDO record = queryReason(queryReasonReq);

        if (record != null) {
            throw BizException.makeThrow("新增原因失败，原因已存在：" + JSON.toJson(queryReasonReq));
        }

        demandDBHelper.insert(getInventoryReasonDo(req));

    }

    @Override
    public void updateReason(UpdateReasonReq req) {
        QueryReasonReq queryReasonReq = new QueryReasonReq();
        queryReasonReq.setDate(req.getDate());
        queryReasonReq.setInstanceType(req.getInstanceType());
        queryReasonReq.setZoneName(req.getZoneName());

        InventoryReasonDO record = queryReason(queryReasonReq);

        if (record == null) {
            throw BizException.makeThrow("更新原因失败，选中日期、机型、可用区下对应的原因不存在");
        }

        record.setReasonDetail(req.getReasonDetail());
        record.setReasonType(req.getReasonType());

        demandDBHelper.update(record);
    }

    @Override
    public void deleteReason(DeleteReasonReq req) {
        demandDBHelper.delete(InventoryReasonDO.class, "where id = ?", req.getId());
    }

    @Override
    public InventoryReasonDO queryReason(QueryReasonReq req) {
        return demandDBHelper.getOne(InventoryReasonDO.class, "where date = ? and instance_type = ? and zone_name = ?", DateUtils.format(req.getDate()), req.getInstanceType(), req.getZoneName());
    }

    @Override
    public List<String> queryReasonTypes() {
        return reasonTypes.get();
    }

    @Override
    public List<InventoryReasonDO> listReason(QueryLog req) {
        return demandDBHelper.getAll(InventoryReasonDO.class, "where date between ? and ?", req.getStartDate(), req.getEndDate());
    }

    private List<InventoryReasonDO> listReasons(String date) {
        return demandDBHelper.getAll(InventoryReasonDO.class, "where date = ?", date);
    }

    /**
     * 并行查询给定日期下所有的原因分析，然后按照 机型 + 可用区 分组
     * @param statDate
     */
    public Future<Map<String, List<InventoryReasonDO>>> listReasonsParallelByInstanceTypeAndZoneName(String statDate) {
        // 查询给定日期下所有的原因分析，然后按照 机型 + 可用区 分组
        Future<Map<String, List<InventoryReasonDO>>> reasonMapFuture = threadPool.submit(() -> {
            List<InventoryReasonDO> reasons = listReasons(statDate);
            return ListUtils.groupBy(reasons, o -> String.join(o.getInstanceType(), "@", o.getZoneName()));
        });

        return reasonMapFuture;
    }

    /**
     * 并行查询给定日期范围内的所有原因分析，然后按照 机型 + 可用区 分组
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    public Future<Map<String, List<InventoryReasonDO>>> listRangeReasonsParallelByInstanceTypeAndZoneName(String startDate, String endDate) {
        // 查询给定日期下所有的原因分析，然后按照 机型 + 可用区 分组
        Future<Map<String, List<InventoryReasonDO>>> reasonMapFuture = threadPool.submit(() -> {
            QueryLog req = new QueryLog();
            req.setStartDate(DateUtils.parseLocalDate(startDate));
            req.setEndDate(DateUtils.parseLocalDate(endDate));
            List<InventoryReasonDO> reasons = listReason(req);
            return ListUtils.groupBy(reasons, o -> String.join(o.getInstanceType(), "@", o.getZoneName()));
        });

        return reasonMapFuture;
    }

    private OrderResp queryReturnOrder(String orderNo) {
        String sql = "select \n"
                + "a.OrderId order_no, \n"
                + "GROUP_CONCAT(DISTINCT DeviceClass) device_type,\n"
                + "COUNT(1) as num,\n"
                + "if(a.FinishTime=0,'退回流程中','退回完成') order_status,\n"
                + "sum(cpu_logic_core) core ,\n"
                + "min(date(a.CreateTime)) as order_date,\n"
                + "date(a.FinishTime) finish_date\n"
                + "from  cloud_matrix.matrix_return_order  a \n"
                + "left join cloud_matrix.matrix_return_order_detail d on a.OrderId =d.OrderId \n"
                + "left join t_resource.server_parts_extended_info sp on d.DeviceClass  = sp.device_type \n"
                + "where  a.OrderId  =? \n"
                + "and sp.default_flag =1 \n"
                + "group by  a.OrderId ";
        return resourcedbDBHelper.getRawOne(OrderResp.class, sql, orderNo);
    }

    OrderResp queryQuotaOrder(String orderNo) {

        String sql = "select\n"
                + "   sub_id as order_no,\n"
                + "   deviceType as device_type,\n"
                + "   totalNum as num,\n"
                + "   0 as core,\n"
                + "   (order_num - usedNum) as in_progress_num,\n"
                + "   0 as in_progress_core,\n"
                + "   date(createTime) order_date,\n"
                + "   b.status_name order_status,\n"
                + "   expect_delivery_date expect_date,\n"
                + "   omdPromiseInfo sla_date,\n"
                + "   date(total_deliver_time) finish_date\n"
                + "from  device_apply a\n"
                + "left join bas_order_status  b on a.status =b.status_id\n"
                + "where order_type=1 and sub_id =?";
        return shuttleDBHelper.getRawOne(OrderResp.class, sql, orderNo);

    }

    private OrderResp queryMoveOrder(String orderNo) {
        /** move status 的定义:
         * Block("剔除", -1, 0),
         * UnMatch("未匹配", 0, 0),
         * Undone("未下发", 1, 3),
         * Moving("搬迁中", 2, 2),
         * Done("完成", 3, 0),
         * Approve("审核拒绝", 4, 0),
         * Reject("Xwing驳回", 5, 5),
         * Reinstall("搬迁完成待重装", 6, 0);
         * */
        String sql = "select \n"
                + "a.OrderId order_no, \n"
                + "GROUP_CONCAT(DISTINCT device_type) device_type,\n"
                + "COUNT(1) as num,\n"
                + "sum(cpu_logic_core) core ,\n"
                + "sum(if(b.moveStatus <> 3 && b.moveStatus <> 6, 1, 0)) in_progress_num,\n"
                + "sum(if(b.moveStatus <> 3 && b.moveStatus <> 6, cpu_logic_core, 0)) in_progress_core,\n"
                + "date(a.CreateTime) as order_date,\n"
                + "max(StatusMsg) order_status,\n"
                + "max(date(SLAPromiseTime)) sla_date,\n"
                + "min(date(SLAMoveEndTime)) finish_date\n"
                + "from  cloud_matrix.smove_order a\n"
                + "left join  cloud_matrix.smove_order_server b on a.OrderId =b.OrderId \n"
                + "left join t_resource.server_parts_extended_info sp on b.SSvrDeviceClass  = sp.device_type \n"
                + "where  a.OrderId =? \n"
                + "and sp.default_flag =1\n"
                + "group by  a.OrderId  ";
        return resourcedbDBHelper.getRawOne(OrderResp.class, sql, orderNo);
    }


    Map<String, FutureDemandDo> getFutureDemand(String statDate) {

        ResPlanHolidayWeekDO holidayWeekInfoByDate = dictService.getHolidayWeekInfoByDate(statDate);
        int startWeek = holidayWeekInfoByDate.getWeek() + 1;
        int endWeek = startWeek + 4;
        String sql = "select zone_name, instance_type, \n"
                + "sum(IF(source='IMPORT',total_core,0)) as top,\n"
                + "sum(IF(source='FORECAST',total_core,0)) as tail ,\n"
                + "SUM(total_core)  total\n"
                + "from inventory_health_ppl_forecast_detail \n"
                + "where deleted = 0 and stat_time = ? \n"
                + "and holiday_week between ? and ? "
                + "and source  in  ('IMPORT','FORECAST')\n"
                + "group by instance_type, zone_name";
        return demandDBHelper.getRaw(FutureDemandDo.class, sql, statDate, startWeek, endWeek).stream()
                .collect(Collectors.toMap(s -> s.getZoneName() + "@" + s.getInstanceType(), s -> s));
    }

    /**
     * 获取 地域+机型 -> 服务水平
     *
     * @param statDate
     * @return
     */
    public Map<String, SlaResultItem> getSlaMap(String statDate) {

        WhereContent w = new WhereContent();
        w.andEqual("version", statDate.replaceAll("-", ""));
        w.groupBy("zone_name,instance_family");

        Map<String, SlaResultItem> rs = ckcldDBHelper.getAll(SlaDto.class, w.getSql(), w.getParams())
                .stream().collect(Collectors.toMap(t -> t.zone + "@" + t.ins, zdo -> {
                    BigDecimal slap = BigDecimal.ZERO;
                    BigDecimal soldRt = BigDecimal.ZERO;
                    if (zdo.getApiTotal().intValue() > 0) {
                        slap = slap.add(zdo.getApiSuccTotal().divide(zdo.getApiTotal(), 4, BigDecimal.ROUND_UP)
                                .multiply(BigDecimal.valueOf(0.5)));
                    }
                    if (zdo.getSumSold().intValue() > 0) {
                        soldRt = zdo.getSumSoldOut().divide(zdo.getSumSold(), 4, BigDecimal.ROUND_UP);
                        slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.5)));
                    }

                    SlaResultItem item = new SlaResultItem();
                    item.setSla(slap);
                    item.setApiSuccessCores(zdo.getApiSuccTotal());
                    item.setApiTotalCores(zdo.getApiTotal());

                    if (item.getApiTotalCores() != null && item.getApiTotalCores().compareTo(BigDecimal.ZERO) > 0) {
                        item.setApiSuccessRate(NumberUtils.divide(item.getApiSuccessCores(), item.getApiTotalCores(), 4));
                    }

                    item.setApiSoldOut(zdo.getSumSoldOut());
                    item.setApiSold(zdo.getSumSold());

                    if (item.getApiSold() != null && item.getApiSold().compareTo(BigDecimal.ZERO) > 0) {
                        item.setApiSoldOutRate(NumberUtils.divide(item.getApiSoldOut(), item.getApiSold(), 4));
                    }
                    return item;

                }));
        return rs;
    }

    @Data
    static public class SlaResultItem {
        /**
         * 服务水平
         */
        private BigDecimal sla;

        /**
         * API 请求成功核心数
         */
        private BigDecimal apiSuccessCores;
        /**
         * API 请求总核心数
         */
        private BigDecimal apiTotalCores;
        /**
         * API 成功率 = API 请求成功核心数 / API 请求总核心数
         */
        private BigDecimal apiSuccessRate;
        /**
         * 售罄规格数
         */
        private BigDecimal apiSoldOut;
        /**
         * 售卖规格数
         */
        private BigDecimal apiSold;
        /**
         * 售罄率
         */
        private BigDecimal apiSoldOutRate;
    }

    @Data
    @Table("dws_cloud_server_level")
    static public class SlaDto {

        @Column(value = "zone_name")
        private String zone;
        @Column(value = "instance_family")
        private String ins;

        @Column(value = "sumApi", computed = "sum(api_total)")
        private BigDecimal apiTotal;

        @Column(value = "sumApiSucc", computed = "sum(api_succ_total)")
        private BigDecimal apiSuccTotal;

        @Column(value = "sumSold", computed = "sum(sold_total)")
        private BigDecimal sumSold;

        @Column(value = "sumSoldOut", computed = "sum(sold_out_total)")
        private BigDecimal sumSoldOut;
    }
}
