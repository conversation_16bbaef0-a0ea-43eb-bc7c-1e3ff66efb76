package cloud.demand.app.modules.report_proxy.entity.meta;


import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * 表字段组和元数据映射
 */
@Data
@ToString
@Table("table_column_group_meta_data_mapping")
public class TableColumnGroupMetaDataMappingDO {
    /** 自增 id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 组 id<br/>Column: [group_id] */
    @Column(value = "group_id")
    private Long groupId;

    /** 元数据 id<br/>Column: [meta_data_id] */
    @Column(value = "meta_data_id")
    private Long metaDataId;

    /** 创建的时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private LocalDateTime createTime;

    /** 软删除标识<br/>Column: [deleted] */
    @Column(value = "deleted")
    private Integer deleted;
}
