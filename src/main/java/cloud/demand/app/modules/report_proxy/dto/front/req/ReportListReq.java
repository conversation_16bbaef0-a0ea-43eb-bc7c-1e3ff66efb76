package cloud.demand.app.modules.report_proxy.dto.front.req;

import java.time.LocalDateTime;
import lombok.Data;

@Data
public class ReportListReq {
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    /** 报表 id */
    private Long reportId;
    /** 报表名称 */
    private String reportName;
    /** 模糊查询-命名空间 */
    private String taskNamespace;
    /** 模糊查询-任务名称 */
    private String taskName;
}
