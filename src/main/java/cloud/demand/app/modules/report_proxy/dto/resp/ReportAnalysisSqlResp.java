package cloud.demand.app.modules.report_proxy.dto.resp;

import cloud.demand.app.modules.report_proxy.interceptor.SelectDBHelperInterceptor.MethodInfo;
import cloud.demand.app.modules.report_proxy.interceptor.SelectDBHelperInterceptor.SqlInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import lombok.Data;

/** 报表分析 sql */
@Data
public class ReportAnalysisSqlResp {

    /** 线程集合 */
    private Map<String,List<String>> threadMap;

    /** 方法树 */
    private List<MethodTree> methodTree;

    /** sql 信息 */
    private List<SqlInfo> sqlInfo;

    /**
     * 构建线程map
     * @param sqlInfo sql 信息
     * @return
     */
    public static Map<String,List<String>> buildThreadMap(List<SqlInfo> sqlInfo) {
        Map<String, List<String>> threadMap = new HashMap<>();
        for (SqlInfo info : sqlInfo) {
            List<String> sqlInfoList = threadMap.computeIfAbsent(info.getThreadName(), k -> new ArrayList<>());
            sqlInfoList.add(info.getId());
        }
        return threadMap;
    }

    /**
     * 构建 sql 流程树
     * @param sqlInfo sql信息
     * @return
     */
    public static List<MethodTree> buildMethodTree(List<SqlInfo> sqlInfo) {
        List<MethodTree> ret = new ArrayList<>();
        for (SqlInfo info : sqlInfo) {
            buildTreeOne(ret, info);
        }
        // 给 ret 瘦身，剔除空节点
        cleanMethodTree(ret);
        return ret;
    }

    /**
     * 清理 sql 流程数（剔除中间没有 sql 信息的方法，只保留一个有 sql 的上层方法就行）
     * @param ret sql 流程数
     */
    private static void cleanMethodTree(List<MethodTree> ret) {
        for (int i = 0; i < ret.size(); i++) {
            MethodTree temp = ret.get(i);
            MethodTree preTemp = temp;
            while(ListUtils.isEmpty(temp.getSqlInfo()) && temp.getChildren().size() == 1){
                preTemp = temp;
                temp = temp.getChildren().get(0);
            }
            ret.set(i,preTemp);
            Queue<MethodTree> queue = new ArrayDeque<>();
            queue.offer(preTemp);
            while(!queue.isEmpty()){
                MethodTree poll = queue.poll();
                // 简化 className，只保留类名，不展示全类名
                String className = poll.getClassName();
                String[] split = className.split("\\.");
                poll.setClassName(split[split.length - 1]);
                if (ListUtils.isNotEmpty(poll.getChildren())){
                    for (MethodTree child : poll.getChildren()) {
                        queue.offer(child);
                    }
                }
            }
        }
    }

    /**
     * 构建 sql 的方法流程树
     * @param ret 方法树
     * @param info sql 信息
     */
    private static void buildTreeOne(List<MethodTree> ret, SqlInfo info) {
        List<MethodInfo> methodInfo = info.getMethodInfo();
        if (ListUtils.isEmpty(methodInfo)) {
            return;
        }
        List<MethodTree> temp = ret;
        // 自底向上，遍历
        for (int i = methodInfo.size() - 1; i >= 0; i--) {
            MethodInfo methodInfo_ = methodInfo.get(i);
            String className = methodInfo_.getClassName();
            String methodName = methodInfo_.getMethodName();
            MethodTree sqlFlowTree = temp.stream()
                    .filter(t -> t.getClassName().equals(className) && t.getMethodName().equals(methodName)).findFirst()
                    .orElse(null);
            if (sqlFlowTree == null){
                sqlFlowTree = new MethodTree();
                sqlFlowTree.setClassName(className);
                sqlFlowTree.setMethodName(methodName);
                sqlFlowTree.setChildren(new ArrayList<>());
                temp.add(sqlFlowTree);
            }
            // 最后一个要记录 sql 信息
            if (i == 0){
                List<String> sqlInfo1 = sqlFlowTree.getSqlInfo();
                if (sqlInfo1 == null){
                    sqlInfo1 = new ArrayList<>();
                    sqlFlowTree.setSqlInfo(sqlInfo1);
                }
                sqlInfo1.add(info.getId());
            }

            temp = sqlFlowTree.getChildren();
        }
    }


    @JsonInclude(Include.NON_EMPTY)
    @Data
    public static class MethodTree {
        /** 类名 */
        private String className;

        /** 方法名 */
        private String methodName;

        /** sql 信息 */
        private List<String> sqlInfo;

        /** 后续流程 */
        private List<MethodTree> children;
    }
}
