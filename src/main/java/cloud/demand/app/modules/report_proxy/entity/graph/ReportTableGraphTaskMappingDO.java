package cloud.demand.app.modules.report_proxy.entity.graph;


import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * 报表 graph 任务映射
 */
@Data
@ToString
@Table("report_table_graph_task_mapping")
@NoArgsConstructor
public class ReportTableGraphTaskMappingDO {
    /** 自增 id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** graph id<br/>Column: [graph_id] */
    @Column(value = "graph_id")
    private Long graphId;

    /** 任务 id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 创建时间<br/>Column: [CreateTime] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /**
     * 删除标记<br/>Column: [deleted]
     */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    public ReportTableGraphTaskMappingDO(Long graphId, Long taskId) {
        this.graphId = graphId;
        this.taskId = taskId;
    }
}
