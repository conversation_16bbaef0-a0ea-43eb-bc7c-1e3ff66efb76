package cloud.demand.app.modules.report_proxy.modules.task_run_log.service.impl;

import cloud.demand.app.modules.report_proxy.dto.req.TaskRunLogReq;
import cloud.demand.app.modules.report_proxy.dto.resp.MyDirectedGraph;
import cloud.demand.app.modules.report_proxy.dto.resp.TaskRunLogResp;
import cloud.demand.app.modules.report_proxy.dto.resp.TaskRunLogResp.TableVertex;
import cloud.demand.app.modules.report_proxy.entity.log.CommonTaskRunLogDO;
import cloud.demand.app.modules.report_proxy.entity.log.CommonTaskRunSqlDO;
import cloud.demand.app.modules.report_proxy.entity.log.UpdateTaskBeatDO;
import cloud.demand.app.modules.report_proxy.entity.vo.TaskRunLogVO;
import cloud.demand.app.modules.report_proxy.entity.vo.TaskRunSqlVO;
import cloud.demand.app.modules.report_proxy.modules.task_run_log.service.CommonTaskRunLogService;
import cloud.demand.app.modules.report_proxy.utils.DBUtils;
import cloud.demand.app.modules.sop.enums.TaskStatus;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import com.google.common.base.Objects;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple2;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import org.apache.calcite.util.graph.DefaultEdge;
import org.springframework.stereotype.Service;

@Service
public class CommonTaskRunLogServiceImpl implements CommonTaskRunLogService {

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public void insertOrUpdate(CommonTaskRunLogDO log) {
        if (log.getId() == null){
            demandDBHelper.insert(log);
        }else {
            // 只做必要的字段更新
            UpdateTaskBeatDO update = UpdateTaskBeatDO.transform(log);
            // 运行中不能修改完成和失败的状态
            if (Objects.equal(update.getStatus(), TaskStatus.RUNNING.getName())){
                demandDBHelper.update(update, "where status not in (?)", ListUtils.newList(TaskStatus.FINISH.getName(),TaskStatus.ERROR.getName()));
            }else {
                demandDBHelper.update(update);
            }
        }
    }

    @Override
    public void insertSql(List<CommonTaskRunSqlDO> sqlDOList) {
        if (ListUtils.isNotEmpty(sqlDOList)){
            demandDBHelper.insert(sqlDOList);
        }
    }

    @Override
    public void shutDown(Set<Long> runningIds) {
        if (ListUtils.isNotEmpty(runningIds)){
            demandDBHelper.executeRaw("update common_task_run_log set status = ?, msg = concat(msg,'[容器终止]'), update_time = now() where id in (?)",
                    TaskStatus.ERROR.getName(), runningIds);
        }
    }

    @Override
    public List<TaskRunLogVO> getList(TaskRunLogReq req) {
        SopWhereBuilder builder = new SopWhereBuilder(req, TaskRunLogVO.class);
        WhereSQL whereSQL = builder.whereSQL();
        List<TaskRunLogVO> all = demandDBHelper.getAll(TaskRunLogVO.class, whereSQL.getSQL(), whereSQL.getParams());
        if (ListUtils.isEmpty(req.getIgnoreTable())){
            return all;
        }
        for (TaskRunLogVO taskRunLogVO : all) {
            List<TaskRunSqlVO> sqlList = taskRunLogVO.getSqlList();
            if (ListUtils.isEmpty(sqlList)){
                continue;
            }
            for (TaskRunSqlVO sqlVO : sqlList) {
                List<String> outputTable = sqlVO.getOutputTable();
                if (ListUtils.isNotEmpty(outputTable)){
                    Iterator<String> iterator = outputTable.iterator();
                    if (iterator.hasNext()){
                        String next = iterator.next();
                        for (String s : req.getIgnoreTable()) {
                            if (next.contains(s)){
                                iterator.remove();
                                break;
                            }
                        }
                    }
                }
                List<String> inputTable = sqlVO.getInputTable();
                if (ListUtils.isNotEmpty(inputTable)){
                    Iterator<String> iterator = inputTable.iterator();
                    if (iterator.hasNext()){
                        String next = iterator.next();
                        for (String s : req.getIgnoreTable()) {
                            if (next.contains(s)){
                                iterator.remove();
                                break;
                            }
                        }
                    }
                }
            }
        }
        return all;
    }

    @Override
    public TaskRunLogResp getGraphWithData(TaskRunLogReq req) {
        List<TaskRunLogVO> list = getList(req);
        TaskRunLogResp ret = new TaskRunLogResp();
        ret.setTaskRunLogs(list);
        if (ListUtils.isNotEmpty(list)){
            ret.setGraph(getGraphWithData(list));
        }
        return ret;
    }

    @Override
    public MyDirectedGraph<TableVertex, DefaultEdge> getGraphWithData(List<TaskRunLogVO> list) {
        MyDirectedGraph<TableVertex, DefaultEdge> graph = MyDirectedGraph.create();
        Map<String,TableVertex> tableVertexMap = new HashMap<>();
        List<Tuple2<List<String>,List<String>>> tableMap = new ArrayList<>();
        for (TaskRunLogVO taskRunLogVO : list) {
            List<String> allOutputTable = ListUtils.newList();
            List<String> allInputTable = ListUtils.newList();
            List<TaskRunSqlVO> sqlList = taskRunLogVO.getSqlList();

            if (ListUtils.isNotEmpty(sqlList)){
                for (TaskRunSqlVO sqlVO : sqlList) {
                    // 输出表
                    List<String> outputTable = sqlVO.getOutputTable();
                    if (ListUtils.isNotEmpty(outputTable)){
                        allOutputTable.addAll(outputTable);
                        for (String fullTable : outputTable) {
                            TableVertex temp = tableVertexMap.computeIfAbsent(fullTable, k -> {
                                TableVertex tableVertex = new TableVertex(fullTable);
                                tableVertex.setDbHelper(sqlVO.getDbHelper());
                                tableVertex.setDatabase(sqlVO.getDefDatabase());
                                tableVertex.setDbType(sqlVO.getDbType());
                                tableVertex.setTableName(DBUtils.getTableNameWithFull(fullTable));
                                return tableVertex;
                            });
                            temp.addTaskName(taskRunLogVO.getName());
                            temp.addTaskId(taskRunLogVO.getId());
                            temp.addSqlId(sqlVO.getId());
                        }
                    }
                    // 输入表
                    List<String> inputTable = sqlVO.getInputTable();
                    if (ListUtils.isNotEmpty(inputTable)){
                        allInputTable.addAll(inputTable);
                        for (String fullTable : inputTable) {
                            tableVertexMap.computeIfAbsent(fullTable, k -> {
                                TableVertex tableVertex = new TableVertex(fullTable);
                                tableVertex.setDbHelper(sqlVO.getDbHelper());
                                tableVertex.setDatabase(sqlVO.getDefDatabase());
                                tableVertex.setDbType(sqlVO.getDbType());
                                tableVertex.setTableName(DBUtils.getTableNameWithFull(fullTable));
                                return tableVertex;
                            });
                            // 不计入 task 信息，这里 taskId 等只保留输出表的相关任务
//                            temp.addTaskName(taskRunLogVO.getName());
//                            temp.addTaskId(taskRunLogVO.getId());
//                            temp.addSqlId(sqlVO.getId());
                        }
                    }
                }
            }
            // 输入 --> 输出
            if (ListUtils.isNotEmpty(allOutputTable) && ListUtils.isNotEmpty(allInputTable)){
                tableMap.add(new Tuple2<>(allInputTable,allOutputTable));
            }
        }
        // 构建图 -- 节点
        for (TableVertex value : tableVertexMap.values()) {
            graph.addVertex(value);
        }
        // 构建图 -- 线
        for (Tuple2<List<String>, List<String>> tableList : tableMap) {
            for (String input : tableList._1) {
                for (String output : tableList._2) {
                    graph.addEdge(tableVertexMap.get(input), tableVertexMap.get(output));
                }
            }
        }
        return graph;
    }
}
