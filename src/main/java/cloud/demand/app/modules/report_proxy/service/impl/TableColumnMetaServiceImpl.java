package cloud.demand.app.modules.report_proxy.service.impl;

import cloud.demand.app.modules.report_proxy.entity.config.GlobalSqlArgsDO;
import cloud.demand.app.modules.report_proxy.entity.meta.TableColumnMetaDataArgsMappingDO;
import cloud.demand.app.modules.report_proxy.entity.meta.TableColumnMetaDataDO;
import cloud.demand.app.modules.report_proxy.enums.ArgValueTypeEnum;
import cloud.demand.app.modules.report_proxy.modules.task_monitor.service.GlobalSqlArgsService;
import cloud.demand.app.modules.report_proxy.service.TableColumnMetaService;
import com.alibaba.fastjson.JSON;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.mvel2.MVEL;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** 表字段元数据 */
@Service
public class TableColumnMetaServiceImpl implements TableColumnMetaService {

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private GlobalSqlArgsService globalSqlArgsService;


    @Transactional(transactionManager = "demandTransactionManager")
    @Override
    public void flushTableColumnMeta(List<Long> id) {
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.andIf(ListUtils.isNotEmpty(id),"id in (?)", id);
        List<TableColumnMetaDataDO> metaDataDOList = demandDBHelper.getAll(TableColumnMetaDataDO.class, whereSQL.getSQL(), whereSQL.getParams());
        if (ListUtils.isNotEmpty(metaDataDOList)){
            WhereSQL mappingSql = new WhereSQL();
            mappingSql.andIf(ListUtils.isNotEmpty(id),"meta_data_id in (?)", id);
            List<TableColumnMetaDataArgsMappingDO> mappingDOList = demandDBHelper.getAll(TableColumnMetaDataArgsMappingDO.class,mappingSql.getSQL(),mappingSql.getParams());
            List<GlobalSqlArgsDO> globalSqlArgsDOS = demandDBHelper.getAll(GlobalSqlArgsDO.class);
            Map<Long, List<TableColumnMetaDataArgsMappingDO>> longListMap = ListUtils.groupBy(mappingDOList,
                    TableColumnMetaDataArgsMappingDO::getMetaDataId);
            Map<Long, GlobalSqlArgsDO> longGlobalSqlArgsDOMap = ListUtils.toMap(globalSqlArgsDOS,
                    GlobalSqlArgsDO::getId, item -> item);
            for (TableColumnMetaDataDO metaDataDO : metaDataDOList) {
                List<TableColumnMetaDataArgsMappingDO> argsList = longListMap.get(metaDataDO.getId());
                List<GlobalSqlArgsDO> args = new ArrayList<>();
                if (ListUtils.isNotEmpty(argsList)){
                    // 获取对应的参数
                    for (TableColumnMetaDataArgsMappingDO mappingDO : argsList) {
                        GlobalSqlArgsDO globalSqlArgsDO = longGlobalSqlArgsDOMap.get(mappingDO.getArgsId());
                        if (globalSqlArgsDO != null){
                            args.add(globalSqlArgsDO);
                        }
                    }
                }
                flushTableColumnMeta(metaDataDO,args);
            }
        }
    }

    /** 刷新表字段元数据 */
    @Transactional(transactionManager = "demandTransactionManager")
    public void flushTableColumnMeta(TableColumnMetaDataDO dataDO,List<GlobalSqlArgsDO> sqlArgsDOS) {
        if (dataDO == null || ListUtils.isEmpty(sqlArgsDOS)){
            return;
        }
        LocalDateTime nextUpdateTime = dataDO.getNextUpdateTime();
        // 如果下次更新时间小于当前时间，则不更新
        if (nextUpdateTime != null && nextUpdateTime.isAfter(LocalDateTime.now())) {
            return;
        }
        List<String> valueList = new ArrayList<>();
        for (GlobalSqlArgsDO sqlArgsDO : sqlArgsDOS) {
            List<String> argsValue = getArgsValue(sqlArgsDO);
            if (ListUtils.isNotEmpty(argsValue)){
                valueList.addAll(argsValue);
            }
        }
        dataDO.setLatestValue(valueList);
        Integer survivalSeconds = (Integer)MVEL.eval(dataDO.getSurvivalSeconds());
        dataDO.setNextUpdateTime(LocalDateTime.now().plusSeconds(survivalSeconds));
        demandDBHelper.update(dataDO);
    }

    /**
     * 获取字典值
     * @param argsDO
     * @return
     */
    public List<String> getArgsValue(GlobalSqlArgsDO argsDO){
        String valueType = argsDO.getValueType();
        ArgValueTypeEnum byName = ArgValueTypeEnum.getByName(valueType);
        if (byName == null){
            throw new IllegalArgumentException(valueType + " is not a valid value type!");
        }
        String result;
        switch (byName){
            case STATIC:
                result = globalSqlArgsService.getStaticArg(argsDO);
                break;
            case SQL_STATIC:
                result = globalSqlArgsService.getSqlStaticArg(argsDO);
                break;
            default:
                throw new IllegalArgumentException(valueType + " 不支持作为字典值,请使用 SQL_STATIC 或者 STATIC");
        }
        if (StringUtils.isBlank(result)){
            return null;
        }
        Object parse = JSON.parse(result);
        // 如果解析出来为数组则转换为 List，否则把 result 作为元素加入 List
        if (parse instanceof List){
            return JSON.parseArray(result,String.class);
        }else {
            return ListUtils.of(result);
        }
    }
}
