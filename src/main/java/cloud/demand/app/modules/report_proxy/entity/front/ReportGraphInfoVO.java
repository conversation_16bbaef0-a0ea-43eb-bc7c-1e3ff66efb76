package cloud.demand.app.modules.report_proxy.entity.front;

import cloud.demand.app.modules.report_proxy.entity.graph.ReportRegisterInfoDO;
import cloud.demand.app.modules.report_proxy.entity.graph.ReportTableGraphInfoDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.List;
import lombok.Data;
import lombok.ToString;

/** 报表 + graph */
@Data
@ToString
@Table("report_register_info")
public class ReportGraphInfoVO extends ReportRegisterInfoDO {

    /** graph信息 */
    @RelatedColumn(localColumn = "graph_id", remoteColumn = "id", extraWhere = "where enable_flag = 1 and deleted = 0")
    private ReportTableGraphInfoDO graphInfo;

    /** 任务列表 */
    private List<ReportTableGraphTaskLogVO> taskList;

    /** 日志状态 */
    private String logStatus;

    /** 日志信息 */
    private String msg;

    /** 监控状态 */
    private String monitorStatus;

    /** 监控信息 */
    private String monitorMsg;
}
