package cloud.demand.app.modules.report_proxy.dto.req;

import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/** 重跑任务 */
@Data
public class TaskExecutionCreateReq {

    /** 视图名称 */
    private String graphName;

    /** 任务日志 id */
    private List<Long> logIds;

    /**
     * 任务 id，更加任务 id 的信息
     * 查询指定范围 {startTime(默认当天 0 点) -> endTime(默认 now())} 内的任务日志
     * */
    private List<Long> taskIds;

    /** 任务日志 起始时间 */
    private LocalDateTime startTime;

    /** 任务日志 起始时间 */
    private LocalDateTime endTime;
}
