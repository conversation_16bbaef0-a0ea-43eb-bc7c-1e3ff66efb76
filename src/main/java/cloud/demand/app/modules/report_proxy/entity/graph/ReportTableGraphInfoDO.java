package cloud.demand.app.modules.report_proxy.entity.graph;


import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * 报表 graph 信息
 */
@Data
@ToString
@Table("report_table_graph_info")
public class ReportTableGraphInfoDO {

    /**
     * 自增 id<br/>Column: [id]
     */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * graph名称<br/>Column: [name]
     */
    @Column(value = "name")
    private String name;

    /**
     * graph描述<br/>Column: [desc]
     */
    @Column(value = "desc", insertValueScript = "''")
    private String desc;

    /**
     * 是否启用
     * enable_flag<br/>Column: [enable_flag]
     */
    @Column("enable_flag")
    private Boolean enableFlag;

    /**
     * 创建的时间<br/>Column: [create_time]
     */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private LocalDateTime createTime;

    /**
     * 创建用户<br/>Column: [create_user]
     */
    @Column(value = "create_user", insertValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()")
    private String createUser;

    /**
     * 更新的时间<br/>Column: [update_time]
     */
    @Column(value = "update_time", setTimeWhenUpdate = true, setTimeWhenInsert = true)
    private LocalDateTime updateTime;

    /**
     * 更新用户<br/>Column: [update_user]
     */
    @Column(value = "update_user", updateValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()", insertValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()")
    private String updateUser;

    /**
     * 乐观锁版本<br/>Column: [cas_version]
     */
    @Column(value = "cas_version")
    private Integer casVersion;

    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

}
