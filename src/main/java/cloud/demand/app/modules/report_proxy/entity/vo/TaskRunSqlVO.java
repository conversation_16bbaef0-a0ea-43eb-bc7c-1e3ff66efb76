package cloud.demand.app.modules.report_proxy.entity.vo;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("common_task_run_sql")
public class TaskRunSqlVO {
    /** 自增 id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 详细 名称<br/>Column: [run_log_id] */
    @Column(value = "run_log_id")
    private Long runLogId;

    /** sql key：# + 执行顺序号<br/>Column: [key] */
    @Column(value = "key", maxStringLength = 100)
    private String key;

    /** sql 执行的 dbHelper<br/>Column: [db_helper] */
    @Column(value = "db_helper")
    private String dbHelper;

    /** sql 执行的 db 类型<br/>Column: [db_type] */
    @Column(value = "db_type")
    private String dbType;

    /** sql 执行的 默认数据库<br/>Column: [def_database] */
    @Column(value = "def_database")
    private String defDatabase;

    /** sql类型：insert, update, delete, select<br/>Column: [sql_type] */
    @Column(value = "sql_type")
    private String sqlType;

    /** 输入表，查询没有输入表，增删改可能有<br/>Column: [input_table] */
    @Column(value = "input_table", maxStringLength = 400, isJSON = true)
    private List<String> inputTable;

    /** 输出表<br/>Column: [output_table] */
    @Column(value = "output_table", maxStringLength = 100, isJSON = true)
    private List<String> outputTable;

    /** 执行耗时<br/>Column: [cost_ms] */
    @Column(value = "cost_ms")
    private Integer costMs;

    /** 执行的线程名称<br/>Column: [thread_name] */
    @Column(value = "thread_name", maxStringLength = 100)
    private String threadName;

    /** 创建的时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;
}
