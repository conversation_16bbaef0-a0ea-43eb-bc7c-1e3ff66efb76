package cloud.demand.app.modules.report_proxy.modules.task_run_log.service;

import cloud.demand.app.modules.report_proxy.dto.req.ReportRegisterNameReq;
import cloud.demand.app.modules.report_proxy.dto.req.ReportRegisterWithTaskRunLogReq;
import cloud.demand.app.modules.report_proxy.entity.graph.ReportRegisterInfoDO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/18 10:38
 */
public interface ReportRegisterService {

    void genRegisterInfo(ReportRegisterWithTaskRunLogReq req);

    void updateRegisterInfo(ReportRegisterWithTaskRunLogReq req);

    void deleteRegisterInfo(ReportRegisterNameReq req);

    List<ReportRegisterInfoDO> getByGraphId(List<Long> graphIdList);
}
