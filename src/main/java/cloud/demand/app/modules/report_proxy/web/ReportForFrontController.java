package cloud.demand.app.modules.report_proxy.web;


import cloud.demand.app.modules.report_proxy.dto.front.req.ReportListReq;
import cloud.demand.app.modules.report_proxy.dto.front.resp.ReportListResp;
import cloud.demand.app.modules.report_proxy.entity.front.ReportGraphInfoVO;
import cloud.demand.app.modules.report_proxy.entity.graph.ReportRegisterInfoDO;
import cloud.demand.app.modules.report_proxy.service.ReportForFrontService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.List;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@Slf4j
@Tag(name = "提供给前端的接口", description = "提供给前端的接口")
@JsonrpcController("/report/front")
public class ReportForFrontController {

    @Resource
    private ReportForFrontService reportForFrontService;

    @Operation(summary = "查询报表枚举", description = "查询报表枚举")
    @RequestMapping
    public List<ReportRegisterInfoDO> getReportEnum(){
        return reportForFrontService.getReportEnum();
    }

    @Operation(summary = "查询报表列表", description = "查询报表列表")
    @RequestMapping
    public List<ReportGraphInfoVO> getReportList(@JsonrpcParam ReportListReq req){
        return reportForFrontService.getReportList(req);
    }

}
