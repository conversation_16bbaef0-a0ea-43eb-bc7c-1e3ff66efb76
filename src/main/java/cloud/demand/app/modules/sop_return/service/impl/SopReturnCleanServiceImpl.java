package cloud.demand.app.modules.sop_return.service.impl;


import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.sop.enums.ResourceType;
import cloud.demand.app.modules.sop.service.CommonDbHelper;
import cloud.demand.app.modules.sop.service.task.ads_task.ADSTaskService;
import cloud.demand.app.modules.sop_return.entity.other.SopReturnDwsClean;
import cloud.demand.app.modules.sop_return.entity.report.DwsSopReturnReportMifDO;
import cloud.demand.app.modules.sop_return.enums.ComputeType;
import cloud.demand.app.modules.sop_return.enums.CoreType;
import cloud.demand.app.modules.sop_return.enums.GenerationType;
import cloud.demand.app.modules.sop_return.service.SopReturnCleanService;
import cloud.demand.app.modules.sop_return.service.SopReturnCommonService;
import cloud.demand.app.modules.sop_return.utils.SopSelectCaseBuilder;
import com.pugwoo.dbhelper.DBHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;

/** 采用update的方式更快，dws做数据清洗，到ads只做delete and copy不做任何数据处理，这样前端不会拿到脏数据 */
@Service
public class SopReturnCleanServiceImpl implements SopReturnCleanService {

    @Resource
    DBHelper ckcldStdCrpDBHelper;

    @Resource
    SopReturnCommonService commonService;

    @Resource
    CommonDbHelper commonDbHelper;

    /**
     * 清理字段sql
     * @param version 切片版本号
     * @return 更新的sql字段集合
     */
    @Override
    public Map<String,String> clean(String version) {
        Map<String,String> cleanFieldMap = new HashMap<>();
        // 通用字段清洗
        execUpdateSql(getRaw(version),cleanFieldMap);
        // num是否可以清洗
        cleanIsNumEffective(version,cleanFieldMap);
        return cleanFieldMap;
    }

    /** 清洗num是否有效
     *  num是否有效，0 否 1 是，已执行有效为指标日期小于等于切片日期，未执行有效为指标日期大于等于切片日期，精确到天
     *  参考 {@link ADSTaskService handleNonOrExecutedNum}
     * */
    private void cleanIsNumEffective(String version,Map<String,String> cleanFieldMap) {
        LocalDate dateVersion = commonDbHelper.getStatTimeFromVersion(version);
        String date = dateVersion.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String sql = "(case when index = 'RETURN_EXECUTED' then if(index_date <= '{date}',1,0)\n" +
                "when index = 'RETURN_NOT_EXECUTED' then if(index_date >= '{date}',1,0)\n" +
                "else 1 end)";
        sql = sql.replace("{date}",date);
        cleanFieldMap.put("is_num_effective",sql);
    }

    private List<SopReturnDwsClean> getRaw(String version){
        String sql = ORMUtils.getSql("/sql/sop_return/dwd2dws_select.sql");
        sql = sql.replace("${version}",version);
        return ckcldStdCrpDBHelper.getRaw(SopReturnDwsClean.class, sql);
    }

    /**
     * 清洗通用字段
     * 物理机设备规格集合 -> 清洗代次
     * cvm设备规格集合 -> 清洗核类型
     * 规划产品集合 -> 清洗大类，产品大类
     * 规划产品比较特殊，planProductName定位到class_1时会出现一对多的情况，需要通过compute_type是cpu还是gpu去判断
     * compute_type可以通过cvm规格找cvm族判定是否带有GPU字段来区分
     * 所以查的时候不是一个字段一个字段去distinct，而是多个字段分组查，因为要将planProductName和cvm规格关联起来
     * */
    private void execUpdateSql(List<SopReturnDwsClean> raw,Map<String,String> cleanFieldMap){
        // 物理机设备规格集合 -> 清洗代次
        Set<String> deviceSet = new HashSet<>();
        // cvm设备规格集合 -> 清洗核类型
        Set<String> cvmInstanceSet = new HashSet<>();
        // 规划产品集合 -> 清洗大类，产品大类(CVM)
        Set<String> planProductSet = new HashSet<>();
        // 规格产品 -> cvm规格
        Map<String,Set<String>> planProductCvmMap = new HashMap<>();
        // 规划产品 -> 物理机规格
        Map<String,Set<String>> planProductDeviceMap = new HashMap<>();

        for (SopReturnDwsClean clean : raw) {
            String resType = clean.getResType();
            if (Objects.equals(resType, ResourceType.CVM.getName())){
                cvmInstanceSet.add(clean.getCvmGinsType());
                planProductCvmMap.computeIfAbsent(clean.getPlanProductName(), k -> new HashSet<>()).add(clean.getCvmGinsType());
            }else if (Objects.equals(resType, ResourceType.DEVICE.getName())){
                deviceSet.add(clean.getPhyDeviceType());
                planProductDeviceMap.computeIfAbsent(clean.getPlanProductName(),k->new HashSet<>()).add(clean.getPhyDeviceType());
            }
            planProductSet.add(clean.getPlanProductName());
        }

        // cvmModel -> coreType
        Map<String,List<String>> cvmModel2coreTypeMap = new HashMap<>();

        // deviceType -> generationType
        Map<String,List<String>> deviceType2GenerationTypeMap = new HashMap<>();

        // planProduct -> bigClass,productBigClass
        Map<Object,SopReturnCommonServiceImpl.PlanProductInfo> cvmPlanProductMap = new HashMap<>();
        Map<Object,SopReturnCommonServiceImpl.PlanProductInfo> deivcePlanProductMap = new HashMap<>();

        // cvmModel -> deviceInfo（物理机类型+物理机规格）
        Map<Object,SopReturnCommonServiceImpl.DeviceInfo> cvmDeviceMap = new HashMap<>();

        // cvmModel -> HostDeviceClass
        Map<String,String> hostDeviceClass2CvmInstance = new HashMap<>();

        Map<String,Boolean> cvmModel2IsGpuMap = new HashMap<>();
        Map<String,Boolean> deviceType2IsGpuMap = new HashMap<>();

        if (!CollectionUtils.isEmpty(cvmInstanceSet)){
            List<SopReturnCommonServiceImpl.CvmInstanceInfo> cvmInstanceInfos = commonService.byCvmInstanceModel(cvmInstanceSet);
            for (SopReturnCommonServiceImpl.CvmInstanceInfo value : cvmInstanceInfos) {
                deviceSet.add(value.getHostDeviceClass());
                hostDeviceClass2CvmInstance.put(value.getHostDeviceClass(),value.getCvmInstanceModel());
                // 清洗coreType
                cvmModel2coreTypeMap.computeIfAbsent(CoreType.getNameByCode(value.getCoreType()),
                        k -> new ArrayList<>()).add(value.getCvmInstanceModel());
                // 暂存根据cvm规格判定是否为gpu，后面大类跟产品大类会用到
                cvmModel2IsGpuMap.put(value.getCvmInstanceModel(), value.getCvmInstanceGroup().contains("GPU"));
            }
        }
        if (!CollectionUtils.isEmpty(deviceSet)){
            List<SopReturnCommonServiceImpl.DeviceInfo> deviceInfos = commonService.byDeviceType(deviceSet);
            for (SopReturnCommonServiceImpl.DeviceInfo deviceInfo : deviceInfos) {
                if (hostDeviceClass2CvmInstance.containsKey(deviceInfo.getDeviceType())){
                    cvmDeviceMap.put(hostDeviceClass2CvmInstance.get(deviceInfo.getDeviceType()),deviceInfo);
                }
                deviceType2GenerationTypeMap.computeIfAbsent(GenerationType.getNameByCode(deviceInfo.getGenerationType()),
                        k -> new ArrayList<>()).add(deviceInfo.getDeviceType());
                // 暂存根据cvm规格判定是否为gpu，后面大类跟产品大类会用到
                deviceType2IsGpuMap.put(deviceInfo.getDeviceType(), deviceInfo.getDeviceClass2().contains("GPU"));
            }
        }
        if (!CollectionUtils.isEmpty(planProductSet)){
            List<SopReturnCommonServiceImpl.PlanProductInfo> planProductInfos = commonService.byPlanProductName(planProductSet);

            for (SopReturnCommonServiceImpl.PlanProductInfo planProductInfo : planProductInfos) {
                // 当 compute 有数据时, 根据是否为gpu找对应cvm类型或者物理机类型判断
                if (!StringUtils.isEmpty(planProductInfo.getComputeType())){
                    boolean isGpu = ComputeType.isGpu(planProductInfo.getComputeType());
                    Set<String> cvmModels = planProductCvmMap.get(planProductInfo.getPlanProductName());
                    if (!CollectionUtils.isEmpty(cvmModels)){
                        Object[] objs = new Object[2];
                        objs[0] = planProductInfo.getPlanProductName();
                        objs[1] = cvmModels.stream().filter(item -> Objects.equals(cvmModel2IsGpuMap.get(item), isGpu)).toArray();
                        cvmPlanProductMap.put(objs,planProductInfo);
                    }
                    Set<String> deviceTypes = planProductDeviceMap.get(planProductInfo.getPlanProductName());
                    if (!CollectionUtils.isEmpty(deviceTypes)){
                        Object[] objs = new Object[2];
                        objs[0] = planProductInfo.getPlanProductName();
                        objs[1] = deviceTypes.stream().filter(item -> Objects.equals(deviceType2IsGpuMap.get(item), isGpu)).toArray();
                        deivcePlanProductMap.put(objs,planProductInfo);
                    }
                }else {
                    cvmPlanProductMap.put(planProductInfo.getPlanProductName(),planProductInfo);
                    deivcePlanProductMap.put(planProductInfo.getPlanProductName(),planProductInfo);
                }
            }
        }

        // map翻转
        Map<Object,String> generationTypeMap = new HashMap<>();
        deviceType2GenerationTypeMap.forEach((key,value)-> {if(key!=null){generationTypeMap.put(value.toArray(),key);}});
        Map<Object,String> coreTypeMap = new HashMap<>();
        cvmModel2coreTypeMap.forEach((key,value)-> {if (key!=null){coreTypeMap.put(value.toArray(),key);}});

        // step1：优先处理cvm的物理机类型和物理机规格
        updateDeviceInfoByCvmInstanceModel(cvmDeviceMap,cleanFieldMap);

        // step2：更新代次和核类型（不和step1一起跑，需要step1不全cvm的物理机类型，这里的更新才会生效）
        updateCoreTypeAndGenerationType(generationTypeMap,coreTypeMap,cleanFieldMap);

        // step3：更新大类和产品大类（不和step2一起跑，这里仅仅只针对CVM）
        updateCvmBigClassAndProductClass(cvmPlanProductMap,cleanFieldMap);

        // step3：更新大类和产品大类（不和step2一起跑，这里仅仅只针对物理机）
        updateDeviceBigClassAndProductClass(deivcePlanProductMap,cleanFieldMap);

    }

    private void updateDeviceBigClassAndProductClass(Map<Object, SopReturnCommonServiceImpl.PlanProductInfo> planProductMap,
                                                     Map<String, String> cleanFieldMap) {
        Function<SopReturnCommonServiceImpl.PlanProductInfo,String>[] functions = new Function[2];
        functions[0] = SopReturnCommonServiceImpl.PlanProductInfo::getCategory_2;
        functions[1] = SopReturnCommonServiceImpl.PlanProductInfo::getCategory_3;
        String[] sources = {
                ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getPlanProductName),
                ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getPhyDeviceType)
        };
        String[] targets = {
                ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getBigClass),
                ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getProductBigClass)
        };
        // 只处理物理机类型的数据
        Map<String, String> build = SopSelectCaseBuilder.build(sources, targets, planProductMap, functions);
        Map<String, String> newBuild = new HashMap<>();
        for (Map.Entry<String, String> entry : build.entrySet()) {
            newBuild.put("device_"+entry.getKey(),entry.getValue());
        }
        cleanFieldMap.putAll(newBuild);
    }

    /**
     * 通过规格产品清洗大类和产品大类
     * @param planProductMap 规划产品信息(部分也有cvm规格)与大类和产品大类的映射
     */
    private void updateCvmBigClassAndProductClass(Map<Object,SopReturnCommonServiceImpl.PlanProductInfo> planProductMap, Map<String,String> cleanFieldMap) {
        Function<SopReturnCommonServiceImpl.PlanProductInfo,String>[] functions = new Function[2];
        functions[0] = SopReturnCommonServiceImpl.PlanProductInfo::getCategory_2;
        functions[1] = SopReturnCommonServiceImpl.PlanProductInfo::getCategory_3;
        String[] sources = {
                ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getPlanProductName),
                ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getCvmGinsType)
        };
        String[] targets = {
                ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getBigClass),
                ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getProductBigClass)
        };
        // 只处理cvm类型的数据
        Map<String, String> build = SopSelectCaseBuilder.build(sources, targets, planProductMap, functions);
        Map<String, String> newBuild = new HashMap<>();
        for (Map.Entry<String, String> entry : build.entrySet()) {
            newBuild.put("cvm_"+entry.getKey(),entry.getValue());
        }
        cleanFieldMap.putAll(newBuild);
    }


    /**
     * 通过物理机规格和cvm规格清洗核类型和代次(需要跑前缀方法--updateDeviceInfoByCvmInstanceModel)
     * @param generationTypeMap 代次与物理机规格映射
     * @param coreTypeMap  核类型和cvm规格映射
     */
    private void updateCoreTypeAndGenerationType(Map<Object,String> generationTypeMap,Map<Object,String> coreTypeMap,Map<String,String> cleanFieldMap) {
        String generationTypeSetSql = SopSelectCaseBuilder.build(ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getPhyDeviceType),
                ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getGenerationType),
                generationTypeMap);

        String coreTypeSetSql = SopSelectCaseBuilder.build(ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getCvmGinsType),
                ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getCoreType),
                coreTypeMap);

        cleanFieldMap.put(ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getGenerationType),generationTypeSetSql);
        cleanFieldMap.put(ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getCoreType),coreTypeSetSql);
    }

    /**
     * 通过cvm规格清洗物理机规格和物理机类型
     * @param cvmDeviceMap cvm规格和物理机信息映射
     */
    private void updateDeviceInfoByCvmInstanceModel(Map<Object,SopReturnCommonServiceImpl.DeviceInfo> cvmDeviceMap,Map<String,String> cleanFieldMap) {
        Function<SopReturnCommonServiceImpl.DeviceInfo,String>[] functions = new Function[2];
        functions[0] = SopReturnCommonServiceImpl.DeviceInfo::getDeviceType;
        functions[1] = SopReturnCommonServiceImpl.DeviceInfo::getDeviceFamilyName;
        String[] sources = {
                ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getCvmGinsType)
        };
        String[] targets = {
                ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getPhyDeviceType),
                ORMUtils.getColumnByMethod(DwsSopReturnReportMifDO::getPhyDeviceFamily)
        };
        cleanFieldMap.putAll(SopSelectCaseBuilder.build(sources, targets, cvmDeviceMap, functions));
    }

}
