package cloud.demand.app.modules.sop_return.entity.report;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AdsSopReturnReportDimSumVO {
    @Column("dim")
    private String dim;
    @Column("sumNum")
    private BigDecimal sumNum;
    @Column("sumCoreNum")
    private BigDecimal sumCoreNum;
    @Column("sumCapacity")
    private BigDecimal sumCapacity;
}
