package cloud.demand.app.modules.sop_return.entity.report;

import cloud.demand.app.modules.sop.entity.IVersionDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ToString
@Table("ads_sop_return_report_mif")
public class AdsSopReturnReportMifAnyDO implements IVersionDO {
    /** 分区键，代表数据版本<br/>Column: [version] */
    @Column(value = "any_version", computed = "any(version)")
    private String version;

    /** 数据版本生成时间<br/>Column: [stat_time] */
    @Column(value = "any_stat_time", computed = "any(stat_time)")
    private LocalDateTime statTime;

    /** 指标全名，如需求的云业务的已执行量的英文代号<br/>Column: [index] */
    @Column(value = "any_index", computed = "any(index)")
    private String index;

    /** 容量单位：核,GB,卡<br/>Column: [capacity_unit] */
    @Column(value = "any_capacity_unit", computed = "any(capacity_unit)")
    private String capacityUnit;

    /** 核心类型：小核心，中核心，大核心<br/>Column: [core_type] */
    @Column(value = "any_core_type", computed = "any(core_type)")
    private String coreType;

    /** 代次：存量，采购，专用<br/>Column: [generation_type] */
    @Column(value = "any_generation_type", computed = "any(generation_type)")
    private String generationType;

    /** 大类<br/>Column: [big_class] */
    @Column(value = "any_big_class", computed = "any(big_class)")
    private String bigClass;

    /** 产品大类<br/>Column: [product_big_class] */
    @Column(value = "any_product_big_class", computed = "any(product_big_class)")
    private String productBigClass;

    /** 固资编号<br/>Column: [asset_id] */
    @Column(value = "any_asset_id", computed = "any(asset_id)")
    private String assetId;

    /** 退回原分类<br/>Column: [return_reason_type] */
    @Column(value = "any_return_reason_type", computed = "any(return_reason_type)")
    private String returnReasonType;

    /** 退回标签<br/>Column: [return_tag] */
    @Column(value = "any_return_tag", computed = "any(return_tag)")
    private String returnTag;

    /** 原始退回标签<br/>Column: [ori_return_tag] */
    @Column(value = "any_ori_return_tag", computed = "any(ori_return_tag)")
    private String oriReturnTag;

    /** 业务类型，云业务; 自研业务<br/>Column: [business_type] */
    @Column(value = "any_business_type", computed = "any(business_type)")
    private String businessType;

    /** obs业务类型<br/>Column: [obs_business_type] */
    @Column(value = "any_obs_business_type", computed = "any(obs_business_type)")
    private String obsBusinessType;

    /** 指标日期<br/>Column: [index_date] */
    @Column(value = "any_index_date", computed = "any(index_date)")
    private LocalDate indexDate;

    /** 指标年份<br/>Column: [index_year] */
    @Column(value = "any_index_year", computed = "any(holiday_year)")
    private Integer indexYear;

    /** 指标月份<br/>Column: [index_month] */
    @Column(value = "any_index_month", computed = "any(holiday_month)")
    private Integer indexMonth;

    /** 指标年月<br/>Column: [index_year_month] */
    @Column(value = "any_index_year_month", computed = "any(holiday_year_month)")
    private String indexYearMonth;

    /** 指标周数（全年第 n 周）<br/>Column: [index_week] */
    @Column(value = "any_index_week", computed = "any(holiday_week)")
    private Integer indexWeek;

    /** 资源类型<br/>Column: [res_type] */
    @Column(value = "any_res_type", computed = "any(res_type)")
    private String resType;

    /** 资源池类型<br/>Column: [res_pool_type] */
    @Column(value = "any_res_pool_type", computed = "any(res_pool_type)")
    private String resPoolType;

    /** 项目类型<br/>Column: [obs_project_type] */
    @Column(value = "any_obs_project_type", computed = "any(obs_project_type)")
    private String obsProjectType;

    /** BG名<br/>Column: [bg_name] */
    @Column(value = "any_bg_name", computed = "any(bg_name)")
    private String bgName;

    /** 自定义BG名<br/>Column: [custom_bg_name] */
    @Column(value = "any_custom_bg_name", computed = "any(custom_bg_name)")
    private String customBgName;

    /** 部门名<br/>Column: [dept_name] */
    @Column(value = "any_dept_name", computed = "any(dept_name)")
    private String deptName;

    /** 老部门名<br/>Column: [old_dept_name] */
    @Column(value = "any_old_dept_name", computed = "any(old_dept_name)")
    private String oldDeptName;

    /** 规划产品名<br/>Column: [plan_product_name] */
    @Column(value = "any_plan_product_name", computed = "any(plan_product_name)")
    private String planProductName;

    /** 国内外<br/>Column: [customhouse_title] */
    @Column(value = "any_customhouse_title", computed = "any(customhouse_title)")
    private String customhouseTitle;

    /** 国家<br/>Column: [country_name] */
    @Column(value = "any_country_name", computed = "any(country_name)")
    private String countryName;

    /** 城市：可同时表示物理机城市，也可以表示腾讯云 region<br/>Column: [city_name] */
    @Column(value = "any_city_name", computed = "any(city_name)")
    private String cityName;

    /** cmdb campus<br/>Column: [cmdb_campus_name] */
    @Column(value = "any_cmdb_campus_name", computed = "any(cmdb_campus_name)")
    private String cmdbCampusName;

    /** cmdb module<br/>Column: [cmdb_module_name] */
    @Column(value = "any_cmdb_module_name", computed = "any(cmdb_module_name)")
    private String cmdbModuleName;

    /** 腾讯云 zone<br/>Column: [txy_zone_name] */
    @Column(value = "any_txy_zone_name", computed = "any(txy_zone_name)")
    private String txyZoneName;

    /** cvm 实例类型<br/>Column: [cvm_gins_family] */
    @Column(value = "any_cvm_gins_family", computed = "any(cvm_gins_family)")
    private String cvmGinsFamily;

    /** cvm 实例规格<br/>Column: [cvm_gins_type] */
    @Column(value = "any_cvm_gins_type", computed = "any(cvm_gins_type)")
    private String cvmGinsType;

    /** cvm 机型族<br/>Column: [cvm_gins_kingdom] */
    @Column(value = "any_cvm_gins_kingdom", computed = "any(cvm_gins_kingdom)")
    private String cvmGinsKingdom;

    /** 物理机机型族<br/>Column: [phy_device_family] */
    @Column(value = "any_phy_device_family", computed = "any(phy_device_family)")
    private String phyDeviceFamily;

    /** 物理机设备类型<br/>Column: [phy_device_type] */
    @Column(value = "any_phy_device_type", computed = "any(phy_device_type)")
    private String phyDeviceType;

    /** 是否参与对冲，0 否 1 是<br/>Column: [is_hedge] */
    @Column(value = "any_is_hedge", computed = "any(is_hedge)")
    private Integer isHedge;

    /** 是否有效对冲，0 否 1 是<br/>Column: [has_hedged] */
    @Column(value = "any_has_hedged", computed = "any(has_hedged)")
    private Integer hasHedged;

    /** 是否CA<br/>Column: [is_ca] */
    @Column(value = "any_is_ca", computed = "any(is_ca)")
    private Integer isCa;

    /** 退回单类型<br/>Column: [return_type] */
    @Column(value = "any_return_type", computed = "any(return_type)")
    private String returnType;

    /** 退回设备年限<br/>Column: [return_used_year] */
    @Column(value = "any_return_used_year", computed = "any(return_used_year)")
    private Integer returnUsedYear;

    /** 是否为云退回<br/>Column: [is_cloud_return] */
    @Column(value = "any_is_cloud_return",computed = "any(is_cloud_return)")
    private Integer isCloudReturn;

    /** 需求id */
    @Column(value = "any_item_id",computed = "any(item_id)")
    private String itemId;

    /** 执行订单id */
    @Column(value = "any_order_id",computed = "any(order_id)")
    private String orderId;

    /** 执行状态 {@link cloud.demand.app.modules.sop.enums.ReturnExecStatus} */
    @Column(value = "any_return_exec_status",computed = "any(return_exec_status)")
    private String returnExecStatus;
}
