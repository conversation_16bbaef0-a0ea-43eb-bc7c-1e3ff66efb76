package cloud.demand.app.modules.sop_return.domain.report;

import cloud.demand.app.modules.sop.domain.report.ISimpleVersionReq;
import cloud.demand.app.modules.sop.domain.report.IVersionReq;
import cloud.demand.app.modules.sop.enums.HolidayFieldEnum;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportGroup;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportGroupBy;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class  QueryReportDimReq extends QueryReportCommon implements ISimpleVersionReq {
    @NotBlank(message = "结束版本不能为空")
    @SopReportWhere(columnValue = "version")
    private String version;

    @NotBlank(message = "起始指标年月不能为空")
    @SopReportWhere(sql = "holiday_year_month >= ?")
    private String startYearMonth;

    @NotBlank(message = "结束指标年月不能为空")
    @SopReportWhere(sql = "holiday_year_month <= ?")
    private String endYearMonth;

    /** 维度 */
    @NotBlank(message = "维度不能为空")
    @SopReportGroupBy
    private String dim;

    @SopReportGroup({
            @SopReportWhere(sql = "index in ('RETURN_EXECUTED','RETURN_NOT_EXECUTED')",group = 0),
            @SopReportWhere
    })
    @NotBlank(message = "指标不能为空")
    private String index;

}
