package cloud.demand.app.modules.sop_return.frame.where;

import cloud.demand.app.modules.sop_return.frame.where.anno.SopFilter;
import cloud.demand.app.modules.sop_return.utils.SopReflectUtil;
import com.pugwoo.wooutils.collect.ListUtils;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import yunti.boot.exception.BizException;

/** 过滤器builder */
public class SopFilterBuilder<T> {

    /**
     * 是否初始化完成，只会执行一次
     */
    private boolean initFlag;


    /**
     * 字段解析实体类，需要有注解SopReportTargetClass，且@SopReportTargetClass指定的类带有@table
     */
    private final Object t;

    private Class<?> tClass;

    /**
     * 动态目标类，和@SopReportTargetClass意义相同，但是是动态的，该属性优先级更高
     */
    private final Class<T> targetClass;

    private List<SopFilterInfo> fitterInfo;

    private List<Predicate<T>> fitter;

    /**
     * 查询类t的字段数据数组
     */
    private Field[] fields;

    public SopFilterBuilder(Object t, Class<T> targetClass) {
        this.t = t;
        this.targetClass = targetClass;
    }


    /**
     * 过滤数据
     * @param data 数据集
     * @return
     */
    public List<T> filter(List<T> data){
        init();
        // 过滤
        if (ListUtils.isNotEmpty(data) && ListUtils.isNotEmpty(fitter)){
            data = data.stream().filter(item->{
                for (Predicate<T> tPredicate : fitter) {
                    if (!tPredicate.test(item)){
                        return false;
                    }
                }
                return true;
            }).collect(Collectors.toList());
        }
        return data;
    }

    @SneakyThrows
    private void init(){
        if (initFlag) {
            return;
        }
        if (targetClass == null) {
            throw new RuntimeException("targetClass不能为空");
        }
        initFlag = true;
        if (t != null) {
            tClass = t.getClass();
        }

        fields = SopReflectUtil.getAllFields(tClass);
        Map<String, Field> fieldMap = new HashMap<>();

        // 查询字段
        for (Field field : fields) {
            SopFilter annotation = field.getAnnotation(SopFilter.class);
            if (annotation != null){
                String fieldName = annotation.value();
                if (StringUtils.isBlank(fieldName)) {
                    fieldName = field.getName();
                }
                fieldMap.put(fieldName, field);
            }
        }

        // 目标字段
        fitterInfo = new ArrayList<>();
        for (Field f : SopReflectUtil.getAllFields(targetClass)) {
            if (fieldMap.containsKey(f.getName())){
                SopFilterInfo e = new SopFilterInfo();
                e.setName(f.getName());
                e.setTargetField(f);
                f.setAccessible(true);
                Field queryField = fieldMap.get(f.getName());
                queryField.setAccessible(true);
                e.setQueryField(queryField);
                fitterInfo.add(e);
            }
        }

        // 设置过滤器
        fitter = new ArrayList<>();
        if (ListUtils.isNotEmpty(fitterInfo)){
            for (SopFilterInfo sopFilterInfo : fitterInfo) {
                Field targetField = sopFilterInfo.getTargetField();
                Field queryField = sopFilterInfo.getQueryField();
                Object o = queryField.get(t);
                // 只对 list 做过滤
                if (o instanceof List){
                    Set<Object> set = new HashSet<>((List<Object>) o);
                    if (ListUtils.isNotEmpty(set)){
                        fitter.add(t -> {
                            try {
                                return set.contains(targetField.get(t));
                            } catch (IllegalAccessException e) {
                                throw new BizException(e);
                            }
                        });
                    }
                }
            }
        }
    }

    @Data
    public static class SopFilterInfo{
        private String name;
        private Field queryField;
        private Field targetField;
    }
}
