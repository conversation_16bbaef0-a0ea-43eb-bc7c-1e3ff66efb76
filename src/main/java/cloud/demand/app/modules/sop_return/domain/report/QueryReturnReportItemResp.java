package cloud.demand.app.modules.sop_return.domain.report;

import cloud.demand.app.modules.sop_return.entity.report.AdsSopReturnReportMifAnyDO;
import cloud.demand.app.modules.sop_return.frame.group.interfaces.ISopData;
import cloud.demand.app.modules.sop_return.frame.group.interfaces.ISopIndexItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryReturnReportItemResp {
    private List<Data> data;
    private List<Map<String, Object>> allIndex;

    private static final Map<String, String> dimData;

    private Integer count;

    // 勿删，for json
    public Map<String, String> getDimData() {
        return dimData;
    }

    static {

        dimData = new LinkedHashMap<>(); // 有序 map
        dimData.put("businessType", "业务类型");
        dimData.put("obsBusinessType", "obs业务类型");
        dimData.put("returnType","退回单类型");
        dimData.put("resType", "资源类型");
        dimData.put("obsProjectType", "项目类型");
        dimData.put("resPoolType", "资源池");
        dimData.put("bgName", "原事业群");
        dimData.put("customBgName", "自定义事业群");
        dimData.put("deptName", "部门");
        dimData.put("oldDeptName", "老部门");
        dimData.put("planProductName", "规划产品");
        dimData.put("customhouseTitle", "国内外");
        dimData.put("countryName", "国家");
        dimData.put("cityName", "城市/地域");
        dimData.put("cmdbCampusName", "campus");
        dimData.put("cmdbModuleName", "module");
        dimData.put("txyZoneName", "可用区");
        dimData.put("phyDeviceFamily", "物理机机型族");
        dimData.put("phyDeviceType", "物理机设备类型");
        dimData.put("cvmGinsFamily", "虚拟机实例类型");
        dimData.put("cvmGinsType", "虚拟机实例规格");
        dimData.put("cvmGinsKingdom", "虚拟机机型族");
        dimData.put("coreType", "核心类型");
        dimData.put("generationType", "代次");
        dimData.put("bigClass", "大类");
        dimData.put("productBigClass", "产品大类");
        dimData.put("assetId", "固资编号");
        dimData.put("returnReasonType", "退回原因分类");
        dimData.put("returnTag", "退回标签");
        dimData.put("oriReturnTag", "原始退回标签");
        dimData.put("isHedge", "是否参与对冲");
        dimData.put("hasHedged", "是否有效对冲");
        dimData.put("returnUsedYear", "退回设备年限");
        dimData.put("isCloudReturn", "是否云退回");
        // 补充
        dimData.put("indexYear", "指标年");
        dimData.put("indexMonth", "指标月");
        dimData.put("indexYearMonth", "指标年月");
        dimData.put("indexWeek", "指标周");
        dimData.put("itemId","需求Id");
        dimData.put("orderId","执行单号");
        dimData.put("returnExecStatus","退回状态");
    }

    @lombok.Data
    static public class Data extends AdsSopReturnReportMifAnyDO implements ISopData<IndexItem> {
        private List<IndexItem> indexItems;

        private String dimKey;

        @SneakyThrows
        @Override
        public Data clone() {
            return (Data)super.clone();
        }
    }

    @lombok.Data
    static public class IndexItem implements ISopIndexItem {
        private String indexName;
        private String dim;
        private String name;
        private Boolean isNull;
        private BigDecimal startNum;
        private BigDecimal endNum;
        private BigDecimal diffNum;
        private BigDecimal startCoreNum;
        private BigDecimal endCoreNum;
        private BigDecimal diffCoreNum;
        private BigDecimal startCapacity;
        private BigDecimal endCapacity;
        private BigDecimal diffCapacity;


        @SneakyThrows
        @Override
        public IndexItem clone() {
            return (IndexItem)super.clone();
        }
    }
}
