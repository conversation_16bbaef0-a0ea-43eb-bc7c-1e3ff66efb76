package cloud.demand.app.modules.sop_return.entity.task;

import cloud.demand.app.modules.sop_device.sopTask.frame.task.SimpleSopTask;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** SOP退回dws层任务表 */
@EqualsAndHashCode(callSuper = true)
@ToString
@Data
@Table("sop_return_dws_task")
public class SopReturnDwsTask extends SimpleSopTask {

    /** dwd任务表关联id */
    @Column("dwd_id")
    private Long dwdId;
}
