package cloud.demand.app.modules.sop_return.task.process;

import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.TaskStatus;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.SimpleAbstractSopProcess;
import cloud.demand.app.modules.sop_device.sopTask.service.task.SopDeviceDwdTask;
import cloud.demand.app.modules.sop_return.entity.task.SopReturnDwsTask;
import cloud.demand.app.modules.sop_return.enums.SopReturnIndex;
import cloud.demand.app.modules.sop_return.enums.SopReturnTaskEnum;
import com.pugwoo.dbhelper.DBHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

@Service
public class SopReturnDwsProcess extends SimpleAbstractSopProcess<SopReturnDwsTask> {
    @Resource
    private DBHelper demandDBHelper;

    public void initTask(String version,Long dwdId) {
        SopReturnDwsTask task = new SopReturnDwsTask();
        task.setDwdId(dwdId);
        task.setVersion(version);
        super.initTask(task, SopReturnTaskEnum.SOP_RETURN_DWS);
    }

    // dwd_id 为 -1 时可以不用关联 sop_dwd_task 的 dws_id, 直接跑
    @Override
    public SopReturnDwsTask getReadyTask(ITaskEnum taskEnum) {
        return getDbHelper().getRawOne(getT(),
                "select dws.* from sop_return_dws_task dws where dws.status in (?) and dws.name = ?" +
                        " and (exists (select 1 from sop_dwd_task dwd where dwd.version = dws.version and dwd.status = 'FINISH' and dwd.dws_id = dws.dwd_id and dwd.name in ('PHYSICAL_NEW_RETURN','CVM_NEW_RETURN','CVM_NEW_RETURN_EXT') having count(0) = 3) or dws.dwd_id = -1) " +
                        " and not exists (select 1 from sop_return_ads_task ads where ads.version = dws.version and ads.dws_id != dws.id and ads.status != 'FINISH')" +
                        " order by id",
                Arrays.asList(TaskStatus.NEW.getName(),TaskStatus.ERROR.getName()),
                taskEnum.getName());
    }

    @Override
    protected DBHelper getDbHelper() {
        return demandDBHelper;
    }
}
