package cloud.demand.app.modules.sop_device.web;


import cloud.demand.app.modules.sop_device.domain.DataVersion;
import cloud.demand.app.modules.sop_device.domain.DeviceStockSupplyReq;
import cloud.demand.app.modules.sop_device.domain.NotifyToPullReq;
import cloud.demand.app.modules.sop_device.service.CalculateStockSupplyService;
import cloud.demand.app.modules.sop_device.service.DeviceDetailInfoService;
import cloud.demand.app.modules.sop_device.service.DistService;
import cloud.demand.app.modules.sop_device.service.impl.LoadDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import java.util.List;

/**
 * SOP物理机对冲结果前端接口
 * <AUTHOR>
 */
@JsonrpcController("/sop/device")
@Slf4j
public class DeviceMatchController {

    @Resource
    CalculateStockSupplyService calculateStockSupplyService;

    @Resource
    DeviceDetailInfoService deviceDetailInfoService;

    @Resource
    DistService distService;

    @Resource
    LoadDataService loadDataService;

    @RequestMapping
    public List<DataVersion> querySnapshotNumberList() {
        return calculateStockSupplyService.querySnapshotNumberList();
    }

    @RequestMapping
    public Object queryDeviceHedgingResult(@JsonrpcParam DeviceStockSupplyReq req) {
        return calculateStockSupplyService.queryDeviceHedgingResult2(req);
    }

    @RequestMapping
    public Object exportDetail(@JsonrpcParam DeviceStockSupplyReq req) {
        return deviceDetailInfoService.queryDeviceHedgingResultDetail(req);
    }

    @RequestMapping
    public Object queryParams(@JsonrpcParam DeviceStockSupplyReq req) {
        return calculateStockSupplyService.queryParams2(req);
    }

    @RequestMapping
    public Object asynPull(@JsonrpcParam NotifyToPullReq req) {
        String asynPull = loadDataService.asynScheduledPull(req);
        return "success "+asynPull;
    }
    @RequestMapping
    public Object asynUpdate(@JsonrpcParam NotifyToPullReq req) {
        String asynUpdate = loadDataService.asynUpdate(req);
        return "success "+asynUpdate;
    }

}
