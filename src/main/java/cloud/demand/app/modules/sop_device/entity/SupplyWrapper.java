package cloud.demand.app.modules.sop_device.entity;

import cloud.demand.app.modules.sop_device.entity.SupplyDimEnums.SupplyDimGetter;
import cloud.demand.app.modules.sop_device.entity.SupplyIndexEnums.SupplyIndexGetter;

public interface SupplyWrapper extends SupplyDimGetter, SupplyIndexGetter {

    default String getVersion() {
        return "";
    }

//    Integer deviceCore = 0;
//
//    String UnitName = "";
//
//    Integer DeviceCapacity = 0;

}
