package cloud.demand.app.modules.sop_device.domain;

import cloud.demand.app.modules.sop_device.entity.SupplyDimEnums;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 查询物理机对冲结果的req
 *
 * <AUTHOR>
 */
@Data
public class DeviceStockSupplyReq {

    /**
     * 起始版本&结束版本
     */
    @NotNull
    Long startVersion;
    @NotNull
    Long endVersion;


    /**
     * 统计周期
     * 开始时间，结束时间 yyyy-MM
     */
    String startYearMonth;
    String endYearMonth;

    /**
     * 聚合的维度
     */
    List<SupplyDimEnums> groupByDim;

    Map<SupplyDimEnums, List<String>> conditions;

    String paramType;
}
