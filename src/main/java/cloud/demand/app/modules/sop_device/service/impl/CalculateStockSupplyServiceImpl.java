package cloud.demand.app.modules.sop_device.service.impl;

import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.modules.sop_device.domain.*;
import cloud.demand.app.modules.sop_device.domain.DeviceStockSupplyRsp.Data;
import cloud.demand.app.modules.sop_device.domain.IndexVal.ValItem;
import cloud.demand.app.modules.sop_device.entity.AdsPhysicalHedgeReportLocalVO;
import cloud.demand.app.modules.sop_device.entity.SupplyDimEnums;
import cloud.demand.app.modules.sop_device.entity.SupplyIndexEnums;
import cloud.demand.app.modules.sop_device.entity.SupplyWrapper;
import cloud.demand.app.modules.sop_device.service.CalculateStockSupplyService;
import cloud.demand.app.modules.sop_device.service.DistService;
import cloud.demand.app.modules.sop_device.utils.LocalDateUtil;
import cloud.demand.app.modules.sop_device.wrapper.BaseData;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 计算物理机对冲结果
 */
@Service
@Slf4j
public class CalculateStockSupplyServiceImpl implements CalculateStockSupplyService {

    @Resource
    private BaseData baseData;

    @Resource
    private DistService dictService;

    @Resource
    private DBHelper ckcldDBHelper;

    @Override
    public List<DataVersion> querySnapshotNumberList() {
        return baseData.querySnapshotNumberList();
    }

    @Override
    public DeviceStockSupplyRsp queryDeviceHedgingResult2(DeviceStockSupplyReq req) {

        //条件转化
        WhereContent cnd = new WhereContent();

        cnd.andIn("version", Lists.newArrayList("" + req.getStartVersion(), "" + req.getEndVersion()));

        cnd.andGTE("dimDemandDate", LocalDateUtil.parseStartDate(req.getStartYearMonth()));
        cnd.andLTE("dimDemandDate",LocalDateUtil.parseEndDate(req.getEndYearMonth()));
        if (req.getConditions() != null && !req.getConditions().isEmpty()) {
            req.getConditions().forEach((k, v) -> {
                cnd.andInIfValueNotEmpty(k.toString(), v);
            });
        }

        if (req.getGroupByDim() != null && !req.getGroupByDim().isEmpty()) {
            cnd.groupBy("version,p_index", Joiner.on(",").join(req.getGroupByDim()));
        } else {
            cnd.groupBy("version,p_index");
        }

        List<SupplyWrapper> data = new ArrayList();

        List<AdsPhysicalHedgeReportLocalVO> als = ckcldDBHelper.getAll(AdsPhysicalHedgeReportLocalVO.class,
                cnd.getSql(), cnd.getParams());


        data.addAll(als);
        DeviceStockSupplyRsp response = dealWith(data,
                req.getGroupByDim(),
                o -> o.getVersion().equals(req.getStartVersion().toString()),
                o -> o.getVersion().equals(req.getEndVersion().toString())
        );

        return response;
    }


    @Override
    public ImmutableMap<String, List<String>> queryParams(DeviceStockSupplyReq req) {
        String paramType = req.getParamType();
        if (Strings.isBlank(paramType)) {
            return ImmutableMap.of("data", Lang.list());
        }
        //内部方法调用，注入自身bean.构建代理对象
        List<String> raw = dictService.queryByParamType(paramType);
        if (raw == null) {
            return ImmutableMap.of("data", Lists.newArrayList());
        }
        raw = raw.stream().sorted(Comparator.comparing(String::toString, Comparator.nullsLast((c1, c2) -> {
            if (Strings.equals(c1, "")) {
                return 1;
            }
            if (Strings.equals(c2, "")) {
                return -1;
            }
            return 0;
        }))).collect(Collectors.toList());
        return ImmutableMap.of("data", raw);
    }
    //
//    @Override
    public ImmutableMap<String, List<String>> queryParams2(DeviceStockSupplyReq req) {
        String paramType = req.getParamType();
        if (Strings.isBlank(paramType)) {
            return ImmutableMap.of("data", Lang.list());
        }
        List<String> raw = dictService.queryByParamType2(paramType);
        if (raw == null) {
            return ImmutableMap.of("data", Lists.newArrayList());
        }
        raw = raw.stream().sorted(Comparator.comparing(String::toString, Comparator.nullsLast((c1, c2) -> {
            if (Strings.equals(c1, "")) {
                return 1;
            }
            if (Strings.equals(c2, "")) {
                return -1;
            }
            return 0;
        }))).collect(Collectors.toList());
        return ImmutableMap.of("data", raw);
    }

    public <T extends SupplyWrapper> DeviceStockSupplyRsp dealWith(List<T> data,
                                                                   List<SupplyDimEnums> groupDim,
                                                                   Predicate<? super T> v1Filter,
                                                                   Predicate<? super T> v2Filter) {

        val gMap = ListUtils.groupBy(data, (o) -> Strings.join("@",
                ListUtils.transform(groupDim, (s) -> s.getDimVal(o))));

        List<Data> dataList = gMap.entrySet().stream().map(ent -> {
            //用第一个条数据的信息初始化 该行的维度信息
            Data d = Data.form(groupDim, ent.getValue().get(0));
            List<T> v1 = ListUtils.filter(ent.getValue(), v1Filter);
            List<T> v2 = ListUtils.filter(ent.getValue(), v2Filter);
            // 指标处理
            val items = Arrays.stream(SupplyIndexEnums.values()).map(idx -> {
                IndexVal val = new IndexVal();
                ValItem pre = v1.stream().map(t -> idx.getVal(t)).filter(Objects::nonNull).reduce(IndexVal::add)
                        .orElse(null);
                ValItem af = v2.stream().map(t -> idx.getVal(t)).filter(Objects::nonNull).reduce(IndexVal::add)
                        .orElse(null);
                val.setPre(pre);
                val.setAfter(af);
                val.setIsNull(pre == null && af == null);
                val.setDim(ent.getKey() + "#" + idx.getName());
                val.setName(idx.getName());
                return val;
            }).collect(Collectors.toList());

            d.setIndexItems(items);
            return d;
        }).collect(Collectors.toList());
        // +  index map
        DeviceStockSupplyRsp rsp = new DeviceStockSupplyRsp(dataList,
                ListUtils.transform(SupplyIndexEnums.values(), (o) -> ImmutableMap.of("name", o.getName())),
                ListUtils.toMap(groupDim, SupplyDimEnums::name,
                        SupplyDimEnums::getName)
        );
        return rsp;
    }


}
