package cloud.demand.app.modules.sop_device.domain;

import cloud.demand.app.modules.sop_device.entity.SupplyDimEnums;
import cloud.demand.app.modules.sop_device.entity.SupplyDimEnums.SupplyDimGetter;
import com.pugwoo.wooutils.collect.ListUtils;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class DeviceStockSupplyRsp {

    List<Data> data;

    List<Map<String, Object>> allIndex;

    Map<Object, Object> dimData = ListUtils.toMap(Arrays.asList(SupplyDimEnums.values()), SupplyDimEnums::name,
            SupplyDimEnums::getName);

    public DeviceStockSupplyRsp(List<Data> data, List<Map<String, Object>> allIndex) {
        this.data = data;
        this.allIndex = allIndex;
    }

    public DeviceStockSupplyRsp(List<Data> data, List<Map<String, Object>> allIndex, Map<Object, Object> dimData) {
        this.data = data;
        this.allIndex = allIndex;
        this.dimData = dimData;
    }

    /**
     * 维度可以用于筛选和聚合
     * 能支持的维度，参考 cloud.demand.app.modules.sop_device.entity.SupplyDimEnums
     * 但是太小的聚合粒度没有意义，会退化成明细
     *
     * @See cloud.demand.app.modules.sop_device.entity.SupplyDimEnums
     */
    static public class Data extends HashMap {
//
//
//        /**
//         * 模糊检索类型
//         */
//        String fuzzyTypes;
//
//        /**
//         * 周数
//         */
//        String dimWeek;
//
//        /**
//         * OBS业务类型
//         */
//        String dimObsBiz;
//
//        /**
//         * ERP业务类型
//         */
//        String dimErpBiz;
//
//        /**
//         * 事业群
//         */
//        String dimBg;
//        /**
//         * 部门
//         */
//        String dimDept;
//
//        /**
//         * 规划产品
//         */
//        String dimPlanProduct;
//
//        /**
//         * 项目类型
//         */
//        String dimProject;
//
//        /**
//         * 国家
//         */
//        String dimCountry;
//        /**
//         * 地域
//         */
//        String dimRegion;
//        /**
//         * 城市
//         */
//        String dimCity;
//
//        /**
//         * campus
//         */
//        String dimCampus;
//
//        /**
//         * 设备类型
//         */
//        String dimDeviceType;
//
//        /**
//         * 机型族
//         */
//        String dimDeviceFamily;
//
//        /**
//         * 机型大类
//         */
//        String dimDeviceClass;

//        List<IndexItem> indexItems;

        //返回前端的数据模型
        static public Data form(List<SupplyDimEnums> groupDim, SupplyDimGetter dto) {
            Data data = new Data();
            groupDim.forEach(dim -> data.put(dim.name(), dim.getDimVal(dto)));
            return data;
        }

        public void setIndexItems(List indexItems) {
            this.put("indexItems", indexItems);
        }

        @lombok.Data
        static public class IndexItem {

            // 维度
            String dim;
            // index 的维度
            String name;
            Boolean isNull;
            /**
             * 台数
             */
            BigDecimal num1;
            BigDecimal num2;
            /**
             * 核心
             */
            BigDecimal core1;
            BigDecimal core2;
            /**
             * 容量
             */
            BigDecimal capacity1;
            BigDecimal capacity2;

        }
    }
}
