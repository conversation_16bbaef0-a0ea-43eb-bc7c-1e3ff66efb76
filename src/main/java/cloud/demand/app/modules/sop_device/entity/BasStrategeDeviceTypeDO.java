package cloud.demand.app.modules.sop_device.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@ToString
@Table("bas_stratege_device_type")
public class BasStrategeDeviceTypeDO {

    @Column(value = "Id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 设备类型<br/>Column: [NAME] */
    @Column(value = "NAME")
    private String nAME;


    @Column(value = "EnableFlag")
    private Boolean enableFlag;




    /** 类型1<br/>Column: [device_class1] */
    @Column(value = "device_class1")
    private String deviceClass1;

    /** 类型2<br/>Column: [device_class2] */
    @Column(value = "device_class2")
    private String deviceClass2;

    /** 类型3<br/>Column: [device_class3] */
    @Column(value = "device_class3")
    private String deviceClass3;

    /** 场景细分 or 机型族<br/>Column: [DeviceFamilyName] */
    @Column(value = "DeviceFamilyName")
    private String deviceFamilyName;

    /** 机型族编码<br/>Column: [DeviceFamilyCode] */
    @Column(value = "DeviceFamilyCode")
    private String deviceFamilyCode;

    /** 单位<br/>Column: [UnitName] */
    @Column(value = "UnitName")
    private String unitName;

    /** 通过机型族信息计算出来的逻辑数<br/>Column: [LogicNum] */
    @Column(value = "LogicNum")
    private Integer logicNum;

    /** 是否用于模糊匹配<br/>Column: [fuzzy_match] */
    @Column(value = "fuzzy_match")
    private Integer fuzzyMatch;


}