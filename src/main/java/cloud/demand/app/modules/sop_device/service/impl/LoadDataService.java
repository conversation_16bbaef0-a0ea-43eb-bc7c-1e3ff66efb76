package cloud.demand.app.modules.sop_device.service.impl;

import cloud.demand.app.modules.sop.service.CommonDbHelper;
import cloud.demand.app.modules.sop.service.task.init.TaskInitService;
import cloud.demand.app.modules.sop_device.domain.*;
import cloud.demand.app.modules.sop_device.entity.SupplyDemandHedgingResultDO;
import cloud.demand.app.modules.sop_device.service.CalculateStockSupplyService;
import cloud.demand.app.modules.sop_device.service.DataCreateJob;
import cloud.demand.app.modules.sop_device.service.DeviceDetailInfoService;
import cloud.demand.app.modules.sop_device.utils.ResultUtil;
import cloud.demand.app.modules.sop_device.wrapper.BaseData;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@EnableScheduling
public class LoadDataService {

    static int PAGE_SIZE = 50000;
    @Resource
    DeviceDetailInfoService deviceDetailInfoService;
    @Resource
    CalculateStockSupplyService calculateStockSupplyService;
    ScheduledThreadPoolExecutor scheduledThreadPoolExecutor = new ScheduledThreadPoolExecutor(2);
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DataCreateJob dataCreateJob;
    @Resource
    private BaseData baseData;
    @Resource
    private TaskInitService taskInitService;

    /** sop工具方法 */
    @Resource
    private CommonDbHelper commonDbHelper;

    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 0 2 * * ?")
    public void pullTask() {
        log.info("begin pullTask");
        pullAll();
    }

    public void pullAll() {
        List<DataVersion> versionList = calculateStockSupplyService.querySnapshotNumberList();
        String sqlDistinct = "select distinct ver_num from supply_demand_hedging_dto_result order by ver_num";
        List<Long> verNumDistinct = demandDBHelper.getRaw(Long.class, sqlDistinct);
        for (DataVersion version : versionList) {

            // 已经插入过的数据不会改变，无需重复插入更新
            if (!verNumDistinct.contains(version.getVersion())) {
                Integer total = getReqTotal(version);
                if (total < PAGE_SIZE) {
                    onceHttp(version);
                    continue;
                }
                overSize(version, total);
            } else {
                log.info("pullAll异步切面已存在 " + version.getVersion());
            }

        }

    }

    public String asynPull(NotifyToPullReq req) {
        Future<?> submit = scheduledThreadPoolExecutor.submit(() -> {
            pull(req);
        });
        return "拉取任务已提交" + format(req);
    }

    public String asynUpdate(NotifyToPullReq req) {
        Future<?> submit = scheduledThreadPoolExecutor.submit(() -> {
            update(req);
        });
        CompletableFuture.runAsync(() -> {
            // todo 临时借物理机对冲报表的通知接口完成进销存报表的二次重刷
            List<DataVersion> versionList = req.getVersionList();

            if (!CollectionUtils.isEmpty(versionList)) {
                versionList.forEach(v -> {
                    Long version = v.getVersion();
                    if (version != null) {
                        // 查不到版本则跳过该版本，不再进行处理
                        String version_ = String.valueOf(version);
                        try {
                            commonDbHelper.getStatTimeFromVersion(version_);
                        } catch (Exception e) {
                            log.error(e.getMessage());
                            return;
                        }
                        log.info("临时借物理机对冲报表的通知接口完成进销存报表的二次重刷: {}", version_);
                        taskInitService.deliveryTask(version_);
                    }
                });
            }
        });
        return "重建任务已提交" + format(req);
    }

    public String asynScheduledPull(NotifyToPullReq req) {
        ScheduledFuture<?> runnableFuture = scheduledThreadPoolExecutor.schedule(() -> {
            log.info("拉取任务执行开始");
            pull(req);
        }, 30, TimeUnit.MINUTES);
        return "延时拉取任务已提交" + format(req);
    }

    public void pull(NotifyToPullReq req) {
        if (req == null || CollectionUtils.isEmpty(req.getVersionList())) {
            log.info("pull请求参数为空，请检查入参");
            return;
        }
        log.info("pull 入参 " + req.toString());
        List<DataVersion> versionList = req.getVersionList();
        String sqlDistinct = "select distinct ver_num from supply_demand_hedging_dto_result order by ver_num";
        List<Long> verNumDistinct = demandDBHelper.getRaw(Long.class, sqlDistinct);
        for (DataVersion version : versionList) {

            // 已经插入过的数据不会改变，无需重复插入更新
            if (!verNumDistinct.contains(version.getVersion())) {
                Integer total = getReqTotal(version);
                if (total < PAGE_SIZE) {
                    onceHttp(version);
                    continue;
                }
                overSize(version, total);
            } else {
                log.info("pull异步切面已存在 " + version.getVersion());
            }

        }
        log.info("pull end " + req.toString());
    }

    public void update(NotifyToPullReq req) {
        log.info("update start");
        if (req == null || CollectionUtils.isEmpty(req.getVersionList())) {
            log.info("update请求参数为空，请检查入参");
            return;
        }
        log.info("update入参" + req.toString());

        List<Long> deleteVersionList = req.getVersionList().stream().map(item -> item.getVersion())
                .collect(Collectors.toList());
        String sqlDistinct = "delete from supply_demand_hedging_dto_result where ver_num in(?)";
        demandDBHelper.executeRaw(sqlDistinct, deleteVersionList);

        List<DataVersion> versionList = req.getVersionList();
        for (DataVersion version : versionList) {
            Integer total = getReqTotal(version);
            if (total < PAGE_SIZE) {
                onceHttp(version);
                continue;
            }
            overSize(version, total);
        }
        log.info("update end " + req.toString());
    }

    private void onceHttp(DataVersion version) {
        HedgingWrapperReq hedgingWrapperReq = new HedgingWrapperReq(version.getVersion());
        List<SupplyDemandHedgingResultDTO> resultDTOList = deviceDetailInfoService.queryByHttp(hedgingWrapperReq);
        log.info("异步正在从getHedgingResultByVersion接口中拉取数据 " + version);
        deviceDetailInfoService.saveAll(resultDTOList);
        dataCreateJob.saveToCk(resultDTOList, version.getVersion().toString());
    }


    @Nullable
    private void overSize(DataVersion version, Integer total) {
        int pageNum = 0;

        HedgingWrapperReq hedgingWrapperReq = new HedgingWrapperReq(version.getVersion());
        // 计算总页数
        int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
        int start = (pageNum) * PAGE_SIZE;
        // int end = Math.min(start + PAGE_SIZE, total);
        for (; pageNum < totalPage; pageNum++) {
            hedgingWrapperReq.setPageNum(pageNum);
            hedgingWrapperReq.setPageSize(PAGE_SIZE);
            List<SupplyDemandHedgingResultDTO> resultDTOList = deviceDetailInfoService.queryByHttp(hedgingWrapperReq);
            log.info("正在分页 " + pageNum + " 从接口中拉取数据 " + version.toString());
            deviceDetailInfoService.saveAll(resultDTOList);
            dataCreateJob.saveToCk(resultDTOList, version.getVersion().toString());
        }
    }


    public Integer getReqTotal(DataVersion dataVersion) {
        if (dataVersion == null) {
            return 0;
        }
        HedgingWrapperReq reqTotal = new HedgingWrapperReq(dataVersion.getVersion(), 1);
        Result<List<SupplyDemandHedgingResultDO>> wrapperResult = baseData.queryDeviceInfoByDailySnapshot(reqTotal);
        if (!ResultUtil.notNull(wrapperResult)) {
            return 0;
        }
        return wrapperResult.getTotal();

    }

    public void buildToCk(List<DataVersion> versionList) {
        if (CollectionUtils.isEmpty(versionList)) {
            return;
        }
        for (int i = 0; i < versionList.size(); i++) {
            List<SupplyDemandHedgingResultDTO> raw =
                    demandDBHelper.getAll(SupplyDemandHedgingResultDTO.class, "where ver_num=?",
                            versionList.get(i).getVersion().toString());
            dataCreateJob.saveToCk(raw, versionList.get(i).toString());
        }

    }

    private String format(NotifyToPullReq req) {
        if (req == null || CollectionUtils.isEmpty(req.getVersionList())) {
            return "";
        }
        List<Long> versionList = req.getVersionList().stream()
                .map(DataVersion::getVersion)
                .collect(Collectors.toList());
        return versionList.toString();
    }
}
