package cloud.demand.app.modules.flow.service;

import cloud.demand.app.modules.flow.dto.FlowNodeRecordWithFlowInfoDTO;
import cloud.demand.app.modules.flow.entity.ConfFlowNodeChainDO;
import cloud.demand.app.modules.flow.entity.FlowInfoDO;
import cloud.demand.app.modules.flow.req.ApprovalReq;
import cloud.demand.app.modules.flow.req.FlowNodeValueSetReq;
import cloud.demand.app.modules.flow.req.FlowPushReq;
import cloud.demand.app.modules.flow.req.FlowStartReq;
import java.util.List;
import java.util.Set;

public interface FlowService {

    /**
     * 查询当前进行中的流程信息
     *
     * @param bizId 业务id，非空
     * @param flowCode 流程编码，非空
     */
    FlowInfoDO queryProcessingFlow(String bizId, String flowCode);

    FlowNodeRecordWithFlowInfoDTO queryProcessingFlowNode(String bizId, String flowCode);

    FlowNodeRecordWithFlowInfoDTO queryProcessingFlowNodeWithNull(String bizId, String flowCode);

    FlowNodeRecordWithFlowInfoDTO queryProcessingSubFlowNode(String bizId, String parentFlowCode);

    /**
     *  根据流程流水号 查询流程信息 以及 最新的流程节点信息
     * @param flowNo 流程流水号
     */
    FlowNodeRecordWithFlowInfoDTO queryNewestByFlowNo(String flowNo);

    /**
     * 启动新流程
     *
     * @param req 流程编码、流程发起人、业务id必传
     * @return 操作后当前最新的流程及流程节点信息
     */
    FlowNodeRecordWithFlowInfoDTO starNewFlow(FlowStartReq req);

    /**
     * 设置流程节点返回值（只能对进行中的流程节点进行操作），不会自动推进流程节点，只是将当前操作的流程节点状态变更为流转中 <br/>
     *
     * @param req 当前流程编码、当前节点编码、操作人、业务id、节点返回值不能为空
     * @return 操作后当前最新的流程及流程节点信息
     */
    FlowNodeRecordWithFlowInfoDTO setCurrentFlowNodeReturnValue(FlowNodeValueSetReq req);

    /**
     * 流程推进，将流转中状态的节点推进到下一个节点
     *
     * @param req 业务id、流程编码不能为空
     * @return 操作后当前最新的流程及流程节点信息
     */
    FlowNodeRecordWithFlowInfoDTO pushToNextNode(FlowPushReq req);

    /**
     * 通用的审批操作，不会自动推进流程节点，只是将当前操作的流程节点状态变更为流转中。<br/>
     * 实际上就是调用{@link #setCurrentFlowNodeReturnValue(FlowNodeValueSetReq)}
     *
     * @param req 流程编码、当前节点编码、审批人、业务id不能为空
     * @return 操作后当前最新的流程及流程节点信息
     */
    FlowNodeRecordWithFlowInfoDTO approval(ApprovalReq req);

    List<ConfFlowNodeChainDO> flowChart(String flowCode, Long flowVersionId);

    /**
     *  查询存在哪些流程流水号
     * @param flowNoList 需要查询的流程流水号
     * @return 存在的流程流水号
     */
    Set<String> queryFlowNoSet(List<String> flowNoList);

    FlowInfoDO queryFlowInfoById(Long flowId);

}