package cloud.demand.app.modules.flow.constant;

import cloud.demand.app.modules.flow.entity.FlowInfoDO;

/**
 *  流程状态
 * @see FlowInfoDO#getFlowStatus()
 */
public enum FlowStatusEnum {

    PROCESSING("processing","进行中"),

    DONE("done","已完成");

    private final String code;

    private final String name;

    FlowStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
