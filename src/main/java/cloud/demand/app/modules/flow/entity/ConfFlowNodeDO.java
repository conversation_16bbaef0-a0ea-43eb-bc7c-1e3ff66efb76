package cloud.demand.app.modules.flow.entity;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 流程节点角色配置表
 */
@Data
@ToString
@Table("conf_flow_node")
public class ConfFlowNodeDO extends BaseDO {

    /**
     * 流程节点编码<br/>Column: [node_code]
     */
    @Column(value = "node_code")
    private String nodeCode;

    /**
     * 流程节点名称<br/>Column: [node_name]
     */
    @Column(value = "node_name")
    private String nodeName;

    /**
     * 节点所属流程编码<br/>Column: [flow_code]
     */
    @Column(value = "flow_code")
    private String flowCode;

    /**
     * 是否审批节点，在审批节点时可以直接调用通用的审批方法，0否，1是<br/>Column: [is_approval_node]
     *
     * @see cloud.demand.app.modules.flow.constant.TrueOrFalseIntCodeEnum
     */
    @Column(value = "is_approval_node")
    private Boolean isApprovalNode;

    /**
     * 节点内可操作的角色<br/>Column: [auth_role_code]
     */
    @Column(value = "auth_role_code")
    private String authRoleCode;

}
