package cloud.demand.app.modules.xy_purchase_order.entiy;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("permission")
public class XyPermissionDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    @Column(value = "user")
    private String user;

    @Column(value = "role")
    private String role;

    @Column(value = "product")
    private String product;

}
