package cloud.demand.app.modules.xy_purchase_order.service.impl;

import cloud.demand.app.entity.rrp.ReportCacheQcloudResultDO;
import cloud.demand.app.modules.xy_purchase_order.model.CloudApiRequest;
import cloud.demand.app.modules.xy_purchase_order.model.CloudApiResponse;
import cloud.demand.app.modules.xy_purchase_order.model.DeliveryDeviceDTO;
import cloud.demand.app.modules.xy_purchase_order.service.XyThirdPartyService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.util.*;

@Service
public class XyThirdPartyServiceImpl implements XyThirdPartyService {

    @Value("${app.url.cloud-api-url}")
    private String cloudApiUrl;
    @Resource
    private DBHelper rrpDBHelper;

    @Transactional(value = "rrpTransactionManager", propagation = Propagation.NOT_SUPPORTED)
    @Override
    public List<DeliveryDeviceDTO> queryDeliveryDevice(String quotaId, Date orderCreateTime) throws IOException {
        if (StringTools.isBlank(quotaId)) {
            return new ArrayList<>();
        }

        // 查看当天的缓存有没有，如果当天有就用当天的；对于24个月以前创建的订单，用最近一个月的就可以了
        ReportCacheQcloudResultDO one = rrpDBHelper.getOne(ReportCacheQcloudResultDO.class,
                "where q_order=? order by id desc", quotaId);
        if (one != null) {
            boolean isUse = false;
            if (one.getCreateTime() != null && one.getCreateTime().after(DateUtils.parse(DateUtils.formatDate(new Date())))) {
                isUse = true;
            }
            if (orderCreateTime != null && one.getCreateTime() != null &&
                    orderCreateTime.before(DateUtils.addTime(new Date(), Calendar.MONTH, -24)) &&
                    one.getCreateTime().after(DateUtils.addTime(new Date(), Calendar.MONTH, -1))) {
                isUse = true;
            }

            if (isUse) {
                CloudApiResponse<DeliveryDeviceDTO> resp = JSON.parse(one.getResult(),
                        CloudApiResponse.class, DeliveryDeviceDTO.class);
                return resp.getDataSet().getData().getDeviceList();
            }
        }

        Browser browser = new Browser();
        browser.setConnectTimeoutSeconds(60); // 设置为60秒超时
        CloudApiRequest request = new CloudApiRequest();
        CloudApiRequest.RequestInfo requestInfo = request.getParams().getContent().getRequestInfo();
        requestInfo.setRequestKey("20160523001");
        requestInfo.setRequestModule("appointment");
        requestInfo.setOperator("stopherliu");

        CloudApiRequest.RequestItem requestItem = request.getParams().getContent().getRequestItem();
        requestItem.setMethod("queryBufferDevices");
        String resultColumn = JSON.toJson(new DeliveryDeviceDTO()).replaceAll("null", "\"\"");
        requestItem.getData().setResultColumn(JSON.parse(resultColumn, Map.class, String.class, String.class));
        requestItem.getData().setSearchCondition(MapUtils.of("quotaId", quotaId));

        browser.disableGzip();

        HttpResponse httpResponse = browser.postJson(cloudApiUrl, request);
        if (httpResponse.getResponseCode() != HttpURLConnection.HTTP_OK) {
            throw new IOException("请求cloudApi状态码不是200，实际值：" + httpResponse.getResponseCode() + "；cloudApiUrl=" + cloudApiUrl);
        }
        String body = httpResponse.getContentString();

        for (int i = 0; i < 10; i++) {
            CloudApiResponse<DeliveryDeviceDTO> resp = JSON.parse(body, CloudApiResponse.class, DeliveryDeviceDTO.class);
            Integer returnCode = resp.getDataSet().getHeader().getReturnCode();
            if (returnCode == null || returnCode != 0) {
                try {
                    Thread.sleep(1000 + new Random().nextInt(2000));
                } catch (InterruptedException e) {
                    // ignore
                }
                i++;
                continue;
            }

            ReportCacheQcloudResultDO cacheQcloudResultDO = new ReportCacheQcloudResultDO();
            cacheQcloudResultDO.setQOrder(quotaId);
            cacheQcloudResultDO.setResult(body);
            rrpDBHelper.insert(cacheQcloudResultDO);
            return resp.getDataSet().getData().getDeviceList();
        }

        // 如果仍然查不到，则用最新的这份
        if (one != null) {
            CloudApiResponse<DeliveryDeviceDTO> resp = JSON.parse(one.getResult(),
                    CloudApiResponse.class, DeliveryDeviceDTO.class);
            return resp.getDataSet().getData().getDeviceList();
        }

        throw new RuntimeException("query quotaId:" + quotaId + " fail");
    }

}
