package cloud.demand.app.modules.xy_purchase_order.service;

import cloud.demand.app.entity.yunti.CloudDemandCsigResourceViewCategoryDO;
import cloud.demand.app.modules.xy_purchase_order.entiy.DeviceApplyDO;
import cloud.demand.app.modules.xy_purchase_order.model.ZoneAreaCountryDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 一些基础服务
 */
public interface XyBaseInfoService {

    List<ZoneAreaCountryDTO> getZoneAreaCountry();

    List<ZoneAreaCountryDTO> getZoneAreaCountryCache();

    /**
     * 查询所有的自定义产品（仅用于采购单）
     */
    List<String> getAllBizGroup();

    /**
     * 查询指定的bizGroup的所有规划产品（仅用于采购单）
     *
     * @param bizGroup 当值为空时，获取全部配置的规划产品
     */
    List<CloudDemandCsigResourceViewCategoryDO> getPlanProducts(String bizGroup);

    /**
     * 获取是否重保字典
     *
     * @return
     */
    Object getIsReinsureDict();

    /**
     * 获取星云采购单状态字典
     *
     * @return
     */
    Object getXYPurchaseOrderStatusDict();

    /**
     * 通过地域类型获取对应的所有地域
     *
     * @return 国家-地域映射
     */
    Map<String, Set<String>> getCountry2RegionMap();

    /**
     * 构建资源分配阶段的采购单id对应的采购单
     *
     * @return 采购单id-机型映射
     */
    Map<String, DeviceApplyDO> buildPurchaseId2DeviceApply();
}
