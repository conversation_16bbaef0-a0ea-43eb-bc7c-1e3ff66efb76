package cloud.demand.app.modules.xy_purchase_order.service;

import cloud.demand.app.modules.xy_purchase_order.model.DeliveryDeviceDTO;

import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * 星云系统使用到的第三方接口
 */
public interface XyThirdPartyService {

    /**
     * 通过cloud的接口查询指定q单的提货设备列表
     * @param quotaId q单
     */
    List<DeliveryDeviceDTO> queryDeliveryDevice(String quotaId, Date orderCreateTime) throws IOException;

}
