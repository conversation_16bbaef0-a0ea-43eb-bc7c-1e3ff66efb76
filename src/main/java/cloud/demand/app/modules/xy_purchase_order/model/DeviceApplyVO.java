package cloud.demand.app.modules.xy_purchase_order.model;

import cloud.demand.app.entity.purchasereport.ErpDeviceDO;
import cloud.demand.app.entity.shuttle.DeviceApplySnapshotDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

import java.util.List;

@Data
public class DeviceApplyVO extends DeviceApplySnapshotDO {

    @RelatedColumn(localColumn = "sub_id", remoteColumn = "sub_id", dbHelperBean = "purchasereportDBHelper")
    private List<ErpDeviceDO> erpDeviceDOs;

}
