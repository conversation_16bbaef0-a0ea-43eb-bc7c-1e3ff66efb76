package cloud.demand.app.modules.xy_purchase_order.model;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ApproveMergeOrderReq {

    @NotNull(message = "采购单合单id不能为空")
    private List<Integer> ids;
    @NotNull(message = "采购单合单是否通过审批不能为空")
    private Boolean pass;
    @NotNull(message = "采购单合单当前状态不能为空")
    private Integer currentStep;

    private String opinion;

}
