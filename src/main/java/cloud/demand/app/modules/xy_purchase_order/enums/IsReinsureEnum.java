package cloud.demand.app.modules.xy_purchase_order.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum IsReinsureEnum {

    YES(1, "是"),

    NO(0, "否");

    final private Integer code;
    final private String name;

    IsReinsureEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static IsReinsureEnum getByCode(Integer code) {
        for (IsReinsureEnum e : IsReinsureEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static IsReinsureEnum getByName(String name) {
        for (IsReinsureEnum e : IsReinsureEnum.values()) {
            if (Objects.equals(name, e.getName())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        IsReinsureEnum e = getByCode(code);
        return e == null ? null : e.getName();
    }

    public static Integer getCodeByName(String name) {
        IsReinsureEnum e = getByName(name);
        return e == null ? null : e.getCode();
    }

}
