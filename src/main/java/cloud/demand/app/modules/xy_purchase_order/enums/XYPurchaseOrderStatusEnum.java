package cloud.demand.app.modules.xy_purchase_order.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum XYPurchaseOrderStatusEnum {

    UNKNOWN(-1, "未知"),
    REFILL(0, "打回重填"),
    PRODUCT_REVIEW(1, "产品审核"),
    TEG_REVIEW(2, "TEG审核中"),
    XY_REJECT(3, "星云拒绝"),
    RESOURCE_ALLOCA(4, "资源分配"),
    BLOCK(5, "挂起"),
    DIRECTOR_REVIEW(6, "资源总监审核"),
    GM_REVIEW(7, "云GM审批"),
    RESOURCE_MATCH_DELIVERY(7000001, "资源匹配下发"),
    RELOCATION_REVIEW(7000002, "搬迁审核"),
    RELOCATING(7000003, "搬迁中"),
    RELOCATION_DELIVERY(7000004, "搬迁交付"),
    ALLOCAING(8000000, "资源分配执行中"),
    ERP_REVIEW(8000001, "ERP审核中"),
    WAITING_ARRIVAL(8000002, "等待到货"),
    PARTIAL_ARRIVAL(8000005, "部分到货"),
    COMPLETELY_ARRIVED(8000006, "完全到货"),
    EQUIPMENT_DELIVERY(8000007, "设备交付"),
    ERP_REJECT(8000127, "ERP拒绝"),
    SUBORDER_GENERATED(8888888, "已生成子单"),
    ERP_DEPT_REVIEW(9000000, "ERP部门审核"),
    RESOURCE_REVIEW(9000001, "资源审核"),
    MANAGEMENT_REVIEW(9000005, "运管审核"),
    ERP_DIRECTOR_REVIEW(9000007, "ERP总监审核");

    final public static XYPurchaseOrderStatusEnum[] CAN_ALLOCA_STATUS_ARRAY =
            new XYPurchaseOrderStatusEnum[] {
                    RESOURCE_ALLOCA, DIRECTOR_REVIEW, GM_REVIEW
            };

    // 向TCI提供的数据的状态集
    final public static XYPurchaseOrderStatusEnum[] TCI_STATUS_ARRAY =
            new XYPurchaseOrderStatusEnum[] {
                    ERP_REJECT, PARTIAL_ARRIVAL, REFILL, WAITING_ARRIVAL, BLOCK,
                    EQUIPMENT_DELIVERY, COMPLETELY_ARRIVED, XY_REJECT, RESOURCE_MATCH_DELIVERY
            };

    // code版本，方便跟数据库值交互
    final public static Integer[] CAN_ALLOCA_STATUS_CODE_ARRAY;
    final public static Integer[] TCI_STATUS_CODE_ARRAY;


    static {
        CAN_ALLOCA_STATUS_CODE_ARRAY = new Integer[CAN_ALLOCA_STATUS_ARRAY.length];
        for (int i = 0; i < CAN_ALLOCA_STATUS_CODE_ARRAY.length; i++) {
            CAN_ALLOCA_STATUS_CODE_ARRAY[i] = CAN_ALLOCA_STATUS_ARRAY[i].code;
        }
        TCI_STATUS_CODE_ARRAY = new Integer[TCI_STATUS_ARRAY.length];
        for (int i = 0; i < TCI_STATUS_CODE_ARRAY.length; i++) {
            TCI_STATUS_CODE_ARRAY[i] = TCI_STATUS_ARRAY[i].code;
        }
    }

    final private Integer code;
    final private String ch;

    XYPurchaseOrderStatusEnum(Integer code, String ch) {
        this.code = code;
        this.ch = ch;
    }

    public static XYPurchaseOrderStatusEnum getByCode(Integer code) {
        for (XYPurchaseOrderStatusEnum e : XYPurchaseOrderStatusEnum.values()) {
            if (Objects.equals(code, e.code)) {
                return e;
            }
        }
        return UNKNOWN;
    }

    public static XYPurchaseOrderStatusEnum getByCh(String ch) {
        for (XYPurchaseOrderStatusEnum e : XYPurchaseOrderStatusEnum.values()) {
            if (Objects.equals(ch, e.ch)) {
                return e;
            }
        }
        return UNKNOWN;
    }

}
