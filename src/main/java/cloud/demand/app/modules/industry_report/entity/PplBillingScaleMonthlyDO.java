package cloud.demand.app.modules.industry_report.entity;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

@Data
@ToString
@Table("ppl_billing_scale_monthly")
public class PplBillingScaleMonthlyDO extends BaseDO {

    /** 统计日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** appid<br/>Column: [app_id] */
    @Column(value = "app_id")
    private Long appId;

    /** 客户uin<br/>Column: [customer_uin] */
    @Column(value = "customer_uin")
    private String customerUin;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 可用区id<br/>Column: [zone_id] */
    @Column(value = "zone_id")
    private Long zoneId;

    /** 可用区名<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** region名<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** ginsfamily<br/>Column: [gins_family] */
    @Column(value = "gins_family")
    private String ginsFamily;

    /** 业务AppRole<br/>Column: [app_role] */
    @Column(value = "app_role")
    private String appRole;

    /** 组织架构标签<br/>Column: [org_label] */
    @Column(value = "org_label")
    private String orgLabel;

    /** 所属业务<br/>Column: [biz_type] */
    @Column(value = "biz_type")
    private String bizType;

    /** 当月净增计费用量（核数）<br/>Column: [diff_billcpu] */
    @Column(value = "diff_billcpu")
    private Double diffBillcpu;

    /** 当月平均计费用量（核数）<br/>Column: [cur_billcpu] */
    @Column(value = "cur_billcpu")
    private Double curBillcpu;

    /** 当月平均服务用量（核数）<br/>Column: [cur_freecpu] */
    @Column(value = "cur_freecpu")
    private Double curFreecpu;

    /** 当月净增服务用量（核数）<br/>Column: [dif_freecpu] */
    @Column(value = "diff_freecpu")
    private Double diffFreecpu;

    /** 当月平均计费用量（卡数）<br/>Column: [cur_billgpu] */
    @Column(value = "cur_billgpu")
    private Double curBillgpu;

    /** 当月净增计费用量（卡数）<br/>Column: [diff_billgpu] */
    @Column(value = "diff_billgpu")
    private Double diffBillgpu;

    /** 当月平均服务用量（卡数）<br/>Column: [cur_freegpu] */
    @Column(value = "cur_freegpu")
    private Double curFreegpu;

    /** 当月净增服务用量（卡数）<br/>Column: [diff_freegpu] */
    @Column(value = "diff_freegpu")
    private Double diffFreegpu;

    /** 需求类型(新增/退回)<br/>Column: [demand_type] */
    @Column(value = "demand_type", insertValueScript = "''")
    private String demandType;

    /** 是否直销客户<br/>Column: [is_direct_selling_customer] */
    @Column(value = "is_direct_selling_customer", insertValueScript = "'0'")
    private Integer isDirectSellingCustomer;

    /** 是否渲染客户<br/>Column: [is_rendering_customer] */
    @Column(value = "is_rendering_customer")
    private Integer isRenderingCustomer;

    /** 是否个人用户<br/>Column: [is_personal_customer] */
    @Column(value = "is_personal_customer", insertValueScript = "'1'")
    private Integer isPersonalCustomer;

    /** CPU OR GPU<br/>Column: [cpu_or_gpu] */
    @Column(value = "cpu_or_gpu")
    private String cpuOrGpu;

    public static CkPplBillingScaleMonthlyDO transform(PplBillingScaleMonthlyDO source){
        CkPplBillingScaleMonthlyDO ckPplBillingScaleMonthlyDO = new CkPplBillingScaleMonthlyDO();
        ckPplBillingScaleMonthlyDO.setStatTime(source.getStatTime());
        ckPplBillingScaleMonthlyDO.setAppId(source.getAppId());
        ckPplBillingScaleMonthlyDO.setCustomerUin(source.getCustomerUin());
        ckPplBillingScaleMonthlyDO.setCustomerShortName(source.getCustomerShortName());
        ckPplBillingScaleMonthlyDO.setCustomerName(source.getCustomerName());
        ckPplBillingScaleMonthlyDO.setIndustryDept(source.getIndustryDept());
        ckPplBillingScaleMonthlyDO.setZoneId(source.getZoneId());
        ckPplBillingScaleMonthlyDO.setZoneName(source.getZoneName());
        ckPplBillingScaleMonthlyDO.setCustomhouseTitle(source.getCustomhouseTitle());
        ckPplBillingScaleMonthlyDO.setAreaName(source.getAreaName());
        ckPplBillingScaleMonthlyDO.setRegionName(source.getRegionName());
        ckPplBillingScaleMonthlyDO.setGinsFamily(source.getGinsFamily());
        ckPplBillingScaleMonthlyDO.setAppRole(source.getAppRole());
        ckPplBillingScaleMonthlyDO.setOrgLabel(source.getOrgLabel());
        ckPplBillingScaleMonthlyDO.setBizType(source.getBizType());
        ckPplBillingScaleMonthlyDO.setDiffBillcpu(source.getDiffBillcpu());
        ckPplBillingScaleMonthlyDO.setCurBillcpu(source.getCurBillcpu());
        ckPplBillingScaleMonthlyDO.setCurFreecpu(source.getCurFreecpu());
        ckPplBillingScaleMonthlyDO.setDiffFreecpu(source.getDiffFreecpu());
        ckPplBillingScaleMonthlyDO.setDemandType(source.getDemandType());
        ckPplBillingScaleMonthlyDO.setIsDirectSellingCustomer(source.getIsDirectSellingCustomer());
        ckPplBillingScaleMonthlyDO.setIsRenderingCustomer(source.getIsRenderingCustomer());
        ckPplBillingScaleMonthlyDO.setIsPersonalCustomer(source.getIsPersonalCustomer());
        ckPplBillingScaleMonthlyDO.setCpuOrGpu(source.getCpuOrGpu());
        ckPplBillingScaleMonthlyDO.setCurBillgpu(source.getCurBillgpu());
        ckPplBillingScaleMonthlyDO.setDiffBillgpu(source.getDiffBillgpu());
        ckPplBillingScaleMonthlyDO.setCurFreegpu(source.getCurFreegpu());
        ckPplBillingScaleMonthlyDO.setDiffFreegpu(source.getDiffFreegpu());
        return ckPplBillingScaleMonthlyDO;
    }
}