package cloud.demand.app.modules.industry_report.model.vo;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.time.LocalDate;

@Data
@Table("ppl_billing_scale_monthly")
public class CkPplBillingScaleMonthlyVO extends CkPplBillingScaleBaseVO {

    @Column(value = "statTime")
    private LocalDate statTime;

    @Column(value = "appId" )
    private Long appId;

    @Column(value = "customerUin")
    private String customerUin;

    @Column(value = "customerShortName")
    private String customerShortName;

    @Column(value = "customerName")
    private String customerName;

    @Column(value = "zoneName")
    private String zoneName;

    @Column(value = "customhouseTitle")
    private String customhouseTitle;

    @Column(value = "areaName")
    private String areaName;

    @Column(value = "regionName")
    private String regionName;

    @Column(value = "ginsFamily")
    private String ginsFamily;

    @Column(value = "appRole")
    private String appRole;

    @Column(value = "orgLabel")
    private String orgLabel;

    @Column(value = "bizType")
    private String bizType;

    @Column(value = "demandType")
    private String demandType;

    @Column(value = "isDirectSellingCustomer")
    private String isDirectSellingCustomer;

    @Column(value = "isRenderingCustomer")
    private String isRenderingCustomer;

    @Column(value = "isPersonalCustomer")
    private String isPersonalCustomer;

    @Column(value = "industryCategory")
    private String category;

    @Column(value = "isTurnoverCustomer")
    private String isTurnoverCustomer;

    @Column(value = "diffBillcpu")
    private Double diffBillcpu;

    @Column(value = "newDiffBillcpu")
    private Double newDiffBillcpu;

    @Column(value = "returnDiffBillcpu")
    private Double returnDiffBillcpu;

    @Column(value = "diffBillgpu")
    private Double diffBillgpu;

    @Column(value = "newDiffBillgpu")
    private Double newDiffBillgpu;

    @Column(value = "returnDiffBillgpu")
    private Double returnDiffBillgpu;

    @Column(value = "curBillcpu")
    private Double curBillcpu;

    @Column(value = "curBillgpu")
    private Double curBillgpu;

    //  内部服务核/卡
    @Column(value = "curFreecpu")
    private Double curFreecpu;

    @Column(value = "diffFreecpu")
    private Double diffFreecpu;

    @Column(value = "curFreegpu")
    private Double curFreegpu;

    @Column(value = "diffFreegpu")
    private Double diffFreegpu;

}
