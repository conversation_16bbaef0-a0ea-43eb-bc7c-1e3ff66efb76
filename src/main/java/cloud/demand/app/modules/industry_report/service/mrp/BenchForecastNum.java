package cloud.demand.app.modules.industry_report.service.mrp;

import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_report.entity.MRPTargetDO;
import cloud.demand.app.modules.industry_report.model.enums.IndustryMRPReportTargetEnum;
import cloud.demand.app.modules.industry_report.model.vo.MRPTargetVO;
import cloud.demand.app.modules.industry_report.service.util.SQLS;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionStatusEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.RecursiveTask;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
@Data
public class BenchForecastNum extends TargetFetcher {

    public static final String monthAddIncreaseMonthVersionKey = "monthAddIncreaseMonthVersion";

    private DBHelper demandDBHelper;

    @Override
    public void init(Map<String, Object> initContext) {
        super.init(initContext);
        demandDBHelper = (DBHelper) initContext.get("demandDBHelper");
    }

    @Override
    public RecursiveTask<Object> task(Map<String, Object> initContext) {
        BenchForecastNum task = new BenchForecastNum();
        task.init(initContext);
        return task;
    }

    @Override
    public IndustryMRPReportTargetEnum token() {
        return IndustryMRPReportTargetEnum.BENCH_FORECAST_NUM;
    }

    @Override
    public Set<IndustryMRPReportTargetEnum> sonTargets() {
        return null;
    }

    @Override
    protected Object compute() {
        // 基准版：即M月的M-3月时的第一个版本录入的M月数据
        // 首先获得每个月对应的基准版版本号
        Map<String, List<String>> rawBenchVersionMap = monthAddIncreaseMonthVersion(-3, content.getYearMonths());
        if (CollectionUtils.isEmpty(rawBenchVersionMap)) {
            // 如果无版本数据，返回空
            return null;
        }
        Map benchVersionMap = new HashMap<>();
        for (Entry<String, List<String>> entry : rawBenchVersionMap.entrySet()) {
            benchVersionMap.put(entry.getKey(), entry.getValue().get(0));
        }
        // 拿M月对应的版本，M月的预测数据
        String sql = ORMUtils.getSql("/sql/industry_report/mrp/bench_forecast_num.sql");
        sql = sql.replace("${JOIN}", content.dynamicJoin());
        WhereSQL whereSQL = content.pplItemAndOrderSQL(benchVersionMap);
        sql = sql.replaceAll("\\$\\{FILTER}", SQLS.whereToAnd(whereSQL.getSQL()));
        sql = content.replacePPLItemProductSQL(sql);
        Object[] one = whereSQL.getParams();
        // SQL片段替换了3次，所以params要重复2次，手动加2次
        whereSQL.and("", one);
        whereSQL.and("", one);
        List<MRPTargetDO> mrpTargetDOS = demandDBHelper.getRaw(MRPTargetDO.class, sql,
                whereSQL.getParams());
        List<MRPTargetVO> result = MRPTargetVO.vos(mrpTargetDOS, content.getProduct());
        addToResult(result);
        return result;
    }

    public Map<String, List<String>> monthAddIncreaseMonthVersion(int addMonth, List<YearMonth> yearMonths) {
        // todo 这个好像做不到锁住初始化
        Map<String, List<String>> result = (Map<String, List<String>>) collector.getShares()
                .get(monthAddIncreaseMonthVersionKey + "@" + addMonth);
        if (result == null) {
            // 找到每个月份的+a月
            Map<String, YearMonth> ym2BenchMap = new HashMap<>();
            for (YearMonth yearMonth : yearMonths) {
                ym2BenchMap.put(yearMonth.toDateStr(), YearMonth.addMonth(yearMonth, addMonth));
            }
            // 知道每个月的+a月后，就可以匹配到+a月的版本列表
            List<PplVersionDO> pplVersionDOS = demandDBHelper.getAll(PplVersionDO.class,
                    "where status = ?", PplVersionStatusEnum.DONE.getCode());
            Map<String, List<String>> bench2VersionMap = new HashMap<>();
            Pattern p;
            for (YearMonth benchYearMonth : ym2BenchMap.values()) {
                String dateStr = benchYearMonth.toDateStr();
                p = Pattern.compile(
                        "((" + benchYearMonth.toNoDashDateStr() + ")+|("
                                + dateStr + ")+|(" + benchYearMonth.getYear() +
                                "-" + benchYearMonth.getMonth() + ")+|(" + benchYearMonth.getYear() +
                                "" + benchYearMonth.getMonth() + ")+)");
                for (PplVersionDO pplVersionDO : pplVersionDOS) {
                    Matcher m = p.matcher(pplVersionDO.getVersionCode());
                    if (m.find()) {
                        List<String> list = bench2VersionMap.computeIfAbsent(dateStr, k -> new ArrayList<>());
                        list.add(pplVersionDO.getVersionCode());
                    }
                }
            }
            result = new HashMap<>();
            for (String ym : ym2BenchMap.keySet()) {
                String benchYm = ym2BenchMap.get(ym).toDateStr();
                List<String> versionCodeList = bench2VersionMap.get(benchYm);
                if (!CollectionUtils.isEmpty(versionCodeList)) {
                    ListUtils.sortAscNullLast(versionCodeList, Function.identity());
                    result.put(ym, versionCodeList);
                }
            }
            collector.getShares().put(monthAddIncreaseMonthVersionKey + "@" + addMonth, result);
        }
        return result;
    }
}
