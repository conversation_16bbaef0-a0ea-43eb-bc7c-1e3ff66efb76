package cloud.demand.app.modules.industry_report.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;

@Table("daily_cvmapi_runinstance_log")
@Data
public class TencentCloudCvmBuyInfoDO {
    @Column("appid")
    private Long appid;
    @Column("statDate")
    private String statDate;
    @Column("regionName")
    private String regionName;
    @Column("zoneName")
    private String zoneName;
    @Column("instanceType")
    private String instanceType;
    @Column("successCoreNum")
    private BigDecimal successCoreNum;
    @Column("totalCoreNum")
    private BigDecimal totalCoreNum;
    @Column("successGpuNum")
    private BigDecimal successGpuNum;
    @Column("totalGpuNum")
    private BigDecimal totalGpuNum;
}
