package cloud.demand.app.modules.industry_report.model.dto;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购单底表
 */
@Data
@ToString
@Table("report_purchase_order_detail")
public class ReportPurchaseOrderDetailDTO {

    /** 行业<br/>Column: [industry] */
    @Column(value = "industry")
    private String industryDept;

    private String category;

    /**需求类型，单据类型*/
    @Column(value = "demand_type")
    private String demandType;

    /** 调拨方式<br/>Column: [supply_way] */
    @Column(value = "supply_way")
    private String supplyWay;

    /** 规划产品<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** campus映射腾讯云的境内外<br/>Column: [txy_customhouse_title] */
    @Column(value = "txy_customhouse_title")
    private String txyCustomhouseTitle;

    /** campus映射腾讯云的地域<br/>Column: [txy_region_name] */
    @Column(value = "txy_region_name")
    private String txyRegionName;

    /** campus映射腾讯云的可用区<br/>Column: [txy_zone_name] */
    @Column(value = "txy_zone_name")
    private String txyZoneName;

    /** campus映射腾讯云的区域<br/>Column: [txy_area_name] */
    @Column(value = "txy_area_name")
    private String txyAreaName;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 逻辑核心数<br/>Column: [logic_core_num] */
    @Column(value = "logic_core_num")
    private Integer logicCoreNum;

    /** 可售卖核心数(核)<br/>Column: [sale_core] */
    @Column(value = "sale_core")
    private BigDecimal saleCore;

    /** 需求日期<br/>Column: [expect_delivery_date] */
    @Column(value = "expect_delivery_date")
    private Date expectDeliveryDate;

    /** 客户<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 客户标注信息<br/>Column: [customer_remark] */
    @Column(value = "customer_remark")
    private String customerRemark;

    /** 提单人<br/>Column: [creator] */
    @Column(value = "creator")
    private String creator;

    /** 提货时间<br/>Column: [delivery_time] */
    @Column(value = "delivery_time")
    private Date deliveryTime;

    /** 星云子单号<br/>Column: [sub_id] */
    @Column(value = "sub_id")
    private String subId;

    /** 固资编号<br/>Column: [asset_id] */
    @Column(value = "asset_id")
    private String assetId;

    /**
     * 转移单id，对于复用时有值<br/>Column: [ts_order_id]
     */
    @Column(value = "ts_order_id")
    private String tsOrderId;

    /** 一级业务<br/>Column: [business1] */
    @Column(value = "business1")
    private String business1;

    /** 二级业务<br/>Column: [business2] */
    @Column(value = "business2")
    private String business2;

    /** 三级业务<br/>Column: [business3] */
    @Column(value = "business3")
    private String business3;

    /** module<br/>Column: [module_name] */
    @Column(value = "module_name")
    private String moduleName;

    /** 星云提单时间<br/>Column: [submit_time] */
    @Column(value = "submit_time")
    private Date submitTime;

    /** 实际交付时间<br/>Column: [into_buffer_time] */
    @Column(value = "into_buffer_time")
    private Date intoBufferTime;

    /** 实际交付时长(天)<br/>Column: [delivery_duration] */
    @Column(value = "delivery_duration")
    private Integer deliveryDuration;

    /** 是否交付及时<br/>Column: [delivery_in_time] */
    @Column(value = "delivery_in_time")
    private String deliveryInTime;

    /** erp单状态<br/>Column: [erp_status] */
    @Column(value = "erp_status")
    private String erpStatus;

    /** 采购原因<br/>Column: [pur_reason] */
    @Column(value = "pur_reason")
    private String purReason;

    /** 网卡类型<br/>Column: [device_net_type] */
    @Column(value = "device_net_type")
    private String deviceNetType;

    /** CPU平台<br/>Column: [cpu_platform] */
    @Column(value = "cpu_platform")
    private String cpuPlatform;

    /** 采购说明<br/>Column: [purpose] */
    @Column(value = "purpose")
    private String purpose;

}