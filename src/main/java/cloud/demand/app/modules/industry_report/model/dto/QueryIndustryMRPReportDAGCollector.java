package cloud.demand.app.modules.industry_report.model.dto;

import cloud.demand.app.modules.industry_report.model.enums.IndustryMRPReportTargetEnum;
import cloud.demand.app.modules.industry_report.model.vo.MRPTargetVO;
import cloud.demand.app.modules.industry_report.service.mrp.buy.BaseBuyInfo;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import lombok.Data;

@Data
public class QueryIndustryMRPReportDAGCollector {

    // 共享变量
    private Map<String, Object> shares;
    // 结点数据收集
    private Map<IndustryMRPReportTargetEnum, List<MRPTargetVO>> result;

    public QueryIndustryMRPReportDAGCollector() {
        shares = new ConcurrentHashMap<>();
        shares.put("billingScaleBaseNumLock", new ReentrantLock());
        shares.put("monthLatestVersionMapLock", new ReentrantLock());
        shares.put("monthLatestSupplyIdMapLock", new ReentrantLock());
        shares.put(BaseBuyInfo.loadDataKey + "Lock", new ReentrantLock());
        result = new ConcurrentHashMap<>();
    }
}
