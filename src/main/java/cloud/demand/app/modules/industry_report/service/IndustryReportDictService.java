package cloud.demand.app.modules.industry_report.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 相关字典接口
 */
public interface IndustryReportDictService {

    /**
     * 获取全量渲染客户的appid列表
     */
    Set<Long> getAllRenderCustomerAppIds();

    /**
     * 获取行业部门对应的行业分类
     */
    String getCategoryByDept(String industryDept);

    /**
     * 获取行业部门对应的行业分类，预先查询出 行业-分类 集合
     */
    String getCategoryByDept(String industryDept, Map<String, String> industryCategoryMap);

    /**
     * 获取行业分类对应的全部行业部门
     */
    Set<String> getDeptByCategory(List<String> category);

    /**
     * 判断是否自研上云的模块
     */
    Boolean isZysyModule(String planProduct, String biz1, String biz2, String biz3);

    Map<String, String> queryCategory();
}
