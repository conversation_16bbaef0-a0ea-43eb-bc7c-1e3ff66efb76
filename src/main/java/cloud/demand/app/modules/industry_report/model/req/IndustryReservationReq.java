package cloud.demand.app.modules.industry_report.model.req;

import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 行业预约明细查询接口
 */
@Data
public class IndustryReservationReq {

    /**
     * 需求年月
     */
    private String beginYearMonth;

    /**
     * 需求年月
     */
    private String endYearMonth;

    /**
     * 是否个人用户
     */
    private Boolean isPersonal;

    /**
     * 预约单状态，传枚举值：
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderStatusEnum
     */
    private List<String> applyStatus;

    /**
     * 需求类型，传枚举值：
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum
     */
    private List<String> demandType;

    /**
     * 产品
     */
    private List<String> product;

    /**
     * 行业部门，实际上是行业分类
     */
    private List<String> category;

    /**
     * 是否计费
     */
    private Boolean isBilling;

    /**
     * 是否个人用户
     */
    private Boolean isInner;

    /**
     * 以下是端到端明细跳转中的补充筛选维度
     */
    private List<String> customhouseTitle;
    private List<String> areaName;
    private List<String> regionName;
    private List<String> zoneName;
    private List<String> industryDept;
    private List<String> instanceType;

    private List<String> applyOrderId; // 预约单id
    private List<String> applySubmitUser; // 预约单提单人
    private List<String> applyArchitect; // 预约单架构师
    private List<String> applyAppId; // 预约单APPID

    public WhereSQL genWhereSQL() {
        WhereSQL whereSQL = new WhereSQL();

        if (StringTools.isNotBlank(beginYearMonth)) {
            Date begin = DateUtils.parse(beginYearMonth);
            if (begin != null) {
                String startDate = beginYearMonth + "-01";
                whereSQL.and("begin_buy_date >= ?", startDate);
            }

        }
        if (StringTools.isNotBlank(endYearMonth)) {
            Date end = DateUtils.parse(endYearMonth);
            if (end != null) {
                String curMonthFirstDay = endYearMonth + "-01";
                //  获取当月最后一天的数据
                String endDate = DateUtils.formatDate(DateUtils.addTime(
                        DateUtils.addTime(DateUtils.parse(curMonthFirstDay), Calendar.MONTH, 1), Calendar.DATE, -1));

                whereSQL.and("begin_buy_date <= ?", endDate);
            }
        }

        if (ListUtils.isNotEmpty(demandType)) {
            whereSQL.and("demand_type in (?)", demandType);
        }


        if (ListUtils.isNotEmpty(applyStatus)){
            whereSQL.and("yunxiao_order_status in (?)", applyStatus);
        }

        if (isPersonal != null) {
            if (isPersonal) {
                whereSQL.and("customer_society_type = 0");
            } else {
                whereSQL.and("customer_society_type = -1 or customer_society_type <> 0");
            }
        }

        if (ListUtils.isNotEmpty(regionName)){
            WhereSQL cond = new WhereSQL();
            if (regionName.contains("未分类")){
                cond.or("region_name in ('(空值)')");
            }
            cond.or("a.region_name in (?)", regionName);
            whereSQL.and(cond);
        }

        if (ListUtils.isNotEmpty(zoneName)){
            WhereSQL cond = new WhereSQL();
            if (zoneName.contains("未分类")){
                cond.or("a.zone_name in ('(空值)')");
            }
            cond.or("a.zone_name in (?)", zoneName);
            whereSQL.and(cond);
        }

        if (ListUtils.isNotEmpty(industryDept)){
            whereSQL.and("industry_dept in (?)", industryDept);
        }

        if (ListUtils.isNotEmpty(instanceType)){
            whereSQL.and("instance_type in (?)", instanceType);
        }

        if (ListUtils.isNotEmpty(applyOrderId)) {
            whereSQL.and("yunxiao_order_id in (?)", applyOrderId);
        }
        if (ListUtils.isNotEmpty(applySubmitUser)) {
            whereSQL.and("apply_submit_user in (?)", applySubmitUser);
        }
        if (ListUtils.isNotEmpty(applyArchitect)) {
            whereSQL.and("apply_architect in (?)", applyArchitect);
        }
        if (ListUtils.isNotEmpty(applyAppId)) {
            whereSQL.and("apply_appId in (?)", applyAppId);
        }

        return whereSQL;
    }


}
