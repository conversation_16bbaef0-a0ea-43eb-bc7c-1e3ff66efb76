package cloud.demand.app.modules.industry_report.model.dto;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.math.BigDecimal;

@Table("device_apply")
@Data
public class XyPurchaseSummaryDTO {

    @Column("demand_type")
    private String demandType;

    @Column("industry")
    private String industry;

    @Column("deviceType")
    private String deviceType;

    @Column(value = "total_num", computed = "sum(totalNum)")
    private BigDecimal totalNum;

    @Column(value = "product")
    private String product;

    @Column("zone")
    private String campus;

    @Column("plan_month")
    private String planMonth;

    @Column("customer_name")
    private String customerName;

    @Column(value = "business1")
    private String business1;

    @Column(value = "business2")
    private String business2;

    @Column(value = "business3")
    private String business3;

    private String type;

    private BigDecimal totalCoreNum;

    private BigDecimal totalGpuCard;

    private BigDecimal logicNum;

    private String customhouseTitle;

    private String areaName;

    private String regionName;

    private String zoneName;

    private String category;

    private String instanceType;

    private String deptOrProduct;

    /**
     * 自研上云/非自研上云
     */
    private String projType;

    /**
     * 单据类型为常规且OBS项目类型为常规项目的作为常规、剩下的作为其他
     */
    @Column(value = "purchase_category",
            computed = "case when proj_set_name = '常规项目' then '常规' else '其他' end")
    private String purchaseCategory;


}
