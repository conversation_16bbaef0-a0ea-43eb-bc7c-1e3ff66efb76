package cloud.demand.app.modules.industry_report.service.impl;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.AmountUtils;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.common.model.dto.ErpRegionInfoDTO;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLogService;
import cloud.demand.app.modules.common.service.impl.DictServiceImpl;
import cloud.demand.app.modules.common.util.SpecialDateUtils;
import cloud.demand.app.modules.cvmjxc.service.others.RrpBaseInfoService;
import cloud.demand.app.modules.end_to_end_report.service.impl.EndToEndReportServiceImpl;
import cloud.demand.app.modules.end_to_end_report.utils.BatchQueryUtils;
import cloud.demand.app.modules.end_to_end_report.utils.RangeUtils;
import cloud.demand.app.modules.industry_report.entity.CloudDemandInnerBizUinConfigDO;
import cloud.demand.app.modules.industry_report.entity.PplBillingScaleMonthlyDO;
import cloud.demand.app.modules.industry_report.entity.XyDeviceApplyDO;
import cloud.demand.app.modules.industry_report.enums.BillingScaleGroupByEnum;
import cloud.demand.app.modules.industry_report.model.dto.BillingScaleDTO;
import cloud.demand.app.modules.industry_report.model.dto.BillingScaleDistinctDTO;
import cloud.demand.app.modules.industry_report.model.dto.PplItemOrderDTO;
import cloud.demand.app.modules.industry_report.model.dto.PurchaseOrderDTO;
import cloud.demand.app.modules.industry_report.model.dto.XyPurchaseSummaryDTO;
import cloud.demand.app.modules.industry_report.model.dto.YunxiaoOrderDetailDTO;
import cloud.demand.app.modules.industry_report.model.req.BillingSchaleReq;
import cloud.demand.app.modules.industry_report.model.req.IndustryReportOverviewReq;
import cloud.demand.app.modules.industry_report.model.req.IndustryReservationReq;
import cloud.demand.app.modules.industry_report.model.req.PurchaseOrderReq;
import cloud.demand.app.modules.industry_report.model.rsp.BillingSchaleRsp;
import cloud.demand.app.modules.industry_report.model.rsp.IndustryReportOverviewRsp;
import cloud.demand.app.modules.industry_report.model.rsp.IndustryReservationRsp;
import cloud.demand.app.modules.industry_report.model.rsp.PurchaseOrderRsp;
import cloud.demand.app.modules.industry_report.model.vo.CkPplBillingScaleMonthlyVO;
import cloud.demand.app.modules.industry_report.service.IndustryReportDictService;
import cloud.demand.app.modules.industry_report.service.IndustryReportQueryService;
import cloud.demand.app.modules.mrpv2.utils.PaasUtils;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.xy_purchase_order.model.ZoneAreaCountryDTO;
import cloud.demand.app.modules.xy_purchase_order.service.XyBaseInfoService;
import cloud.demand.app.web.model.common.StreamDownloadBean;
import com.alibaba.excel.EasyExcel;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import io.vavr.Tuple2;
import java.io.InputStream;
import java.io.PipedInputStream;
import java.io.PipedOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class IndustryReportQueryServiceImpl implements IndustryReportQueryService {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    private IndustryReportDictService dictService;
    @Resource
    private PplCommonService pplCommonService;

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    private TaskLogService taskLogService;
    @Resource
    private RrpBaseInfoService rrpBaseInfoService;
    @Resource
    private DBHelper shuttleDBHelper;
    @Resource
    private DictService baseDictService;
    @Resource
    private XyBaseInfoService xyBaseInfoService;
    @Resource
    private DBHelper yuntiDBHelper;
    @Resource
    private PplDictService pplDictService;

    //  查询优化
    private ExecutorService threadPool = Executors.newFixedThreadPool(10);

    /**
     * 采购单订单状态映射
     */
    private Map<Integer, String> codeMap = new HashMap<Integer, String>() {{
        put(0, "打回重填");
        put(1, "产品审核");
        put(2, "TEG审核中");
        put(3, "星云拒绝");
        put(4, "资源分配");
        put(5, "挂起");
        put(6, "资源总监审核");
        put(7, "云GM审核");
        put(7000001, "资源匹配下发");
        put(7000002, "搬迁审核");
        put(7000003, "搬迁中");
        put(7000004, "搬迁交付");
        put(8000001, "ERP审核中");
        put(8000002, "等待到货");
        put(8000005, "部分到货");
        put(8000006, "完全到货");
        put(8000007, "设备交付");
        put(8000127, "ERP拒绝");
        put(9000001, "资源审核");
        put(9000005, "运管审核");
        put(9000000, "ERP部门审核");
        put(9000007, "ERP总监审核");
    }};

    @Override
    public IndustryReportOverviewRsp queryIndustryReportOverview(IndustryReportOverviewReq req) {
        List<String> yearMonthList = getYearMonthStrList(req.getBeginYearMonth(), req.getEndYearMonth());

        IndustryReportOverviewRsp rsp = new IndustryReportOverviewRsp();
        List<IndustryReportOverviewRsp.ColumnName> tableTitle = Lang.list();
        rsp.setTableTitle(tableTitle);
        List<Map<String, Object>> data = Lang.list();
        rsp.setData(data);
        for (Tuple2<Integer, Integer> tuple2 : getYearMonthList(req.getBeginYearMonth(), req.getEndYearMonth())) {
            tableTitle.add(
                    new IndustryReportOverviewRsp.ColumnName(toYearMonth(tuple2), "y" + tuple2._1 + "m" + tuple2._2));
        }
        tableTitle.add(new IndustryReportOverviewRsp.ColumnName("行业部门", "industryDept"));
        tableTitle.add(new IndustryReportOverviewRsp.ColumnName("分类", "category"));
        tableTitle.add(new IndustryReportOverviewRsp.ColumnName("子类", "subCategory"));

        String userName = LoginUtils.getUserName();

        List<Future<List<Map<String, Object>>>> futures = new ArrayList<>();

        //  规模净增
        futures.add(threadPool.submit(() -> fillBillingScaleData(yearMonthList, userName, req)));
        //  PPL预约单
        futures.add(threadPool.submit(() -> fillReservation(yearMonthList, userName, req)));
        //  星云采购单
        futures.add(threadPool.submit(() -> fillXyPurchase(yearMonthList, userName, req)));
        //  PPL预测单
        futures.add(threadPool.submit(() -> fillForecast(yearMonthList, userName, req)));

        ListUtils.forEach(futures, f -> {
            try {
                data.addAll(f.get());
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException(e);
            }
        });

        return rsp;
    }

    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public List<String> queryWhiteListInnerUin() {
        return demandDBHelper.getRaw(String.class, "select distinct customer_uin from ppl_config_inner_uin");
    }

    @Override
    public IndustryReservationRsp queryIndustryReservationData(IndustryReservationReq req) {
        WhereSQL whereSQL = req.genWhereSQL();
        String userName = LoginUtils.getUserName();
        List<String> curUserDepts = getCurUserDepts(userName, req.getProduct());
        if (ListUtils.isEmpty(curUserDepts)) {
            whereSQL.and(" industry_dept in (null)");
        } else if (curUserDepts.size() == 1 && curUserDepts.contains("all")) {
            //   无需过滤
        } else {
            whereSQL.and(" industry_dept in (?)", curUserDepts);
        }

        List<String> uins = SpringUtil.getBean(IndustryReportQueryServiceImpl.class).queryWhiteListInnerUin();

        if (req.getIsBilling() != null) {
            if (req.getIsBilling()) {
                whereSQL.and("uin_type = 1 or customer_uin in (?)", uins);
            } else {
                whereSQL.and("`customer_uin` = '(空值)' or (uin_type = 0 and customer_uin not in (?)", uins);
            }
        }

        if (req.getIsInner() != null) {
            if (req.getIsInner()) {
                whereSQL.and("uin_type = 0");
            } else {
                whereSQL.and("uin_type = 1");

            }
        }

        String innerSql = whereToAnd(whereSQL.getSQL());
        Object[] innerParams = whereSQL.getParams();

        if (ListUtils.isNotEmpty(req.getCategory())) {
            whereSQL.and("category in (?)", req.getCategory());
        }

        if (ListUtils.isNotEmpty(req.getProduct())) {
            whereSQL.and("product in (?)", req.getProduct());
        }

        String outerSql = whereToAnd(whereSQL.getSQL());
        Object[] outerParams = whereSQL.getParams();

        //  依次添加两次
        List<Object> paramList = Lang.list();

        Arrays.stream(outerParams).forEach(o -> paramList.add(o));
        Arrays.stream(innerParams).forEach(o -> paramList.add(o));
        Object[] params = paramList.toArray();

        String sql = ORMUtils.getSql("/sql/industry_report/detail/yunxiao_order_detail.sql");
        sql = sql.replace("${FILTER}", outerSql);
        sql = sql.replace("${INNER_FILTER}", innerSql);

        if (curUserDepts.size() == 1 && curUserDepts.contains("all") || curUserDepts.contains("内部业务部")) {
            sql = sql.replace("${INNER_AUTH_FILTER}", "");
        } else {
            // sql = sql.replace("${INNER_AUTH_FILTER}", " and b.industry_dept in (null) ");
            sql = sql.replace("${INNER_AUTH_FILTER}", " and industry_dept in (null) ");
        }

        //  只有当选中CVM + 行业分类选内部业务或不选时才统计内部业务的部份
        if ((req.getProduct().contains("CVM&CBS") || req.getProduct().isEmpty())
                && (req.getCategory().contains("内部业务") || req.getCategory().isEmpty())) {
            sql = sql.replace("${PRODUCT}", "");
        } else {
            sql = sql.replace("${PRODUCT}", " and 1 = 0");
        }

        List<YunxiaoOrderDetailDTO> data = DBList.ckcldStdCrpDBHelper.getRaw(YunxiaoOrderDetailDTO.class, sql, params);
        ListUtils.forEach(data, o -> {
            StaticZoneDO zoneInfo = baseDictService.getStaticZoneInfoByName(o.getZoneName());
            if (zoneInfo != null) {
                o.setCustomhouseTitle(zoneInfo.getCustomhouseTitle());
                o.setAreaName(zoneInfo.getAreaName());
            } else {
                o.setCustomhouseTitle("未分类");
                o.setAreaName("未分类");
            }
        });

        //  境内外 + 区域的筛选
        data = ListUtils.filter(data, o -> {
            List<String> customhouseTitle = req.getCustomhouseTitle();
            List<String> areaName = req.getAreaName();
            if (ListUtils.isNotEmpty(customhouseTitle) && !customhouseTitle.contains(o.getCustomhouseTitle())) {
                return false;
            }
            if (ListUtils.isNotEmpty(areaName) && !areaName.contains(o.getAreaName())) {
                return false;
            }
            return true;
        });

        IndustryReservationRsp rsp = new IndustryReservationRsp();
        rsp.setData(data);

        return rsp;

    }

    private List<String> getDeptByCategory(List<String> industryDept, boolean isAll) {
        String sql = isAll ? "select industry_dept from ppl_config_stat_industry_dept_class where deleted=0" :
                "select industry_dept from ppl_config_stat_industry_dept_class where deleted=0 and category in (?)";
        return demandDBHelper.getRaw(String.class, sql, industryDept);
    }


    @Override
    public PurchaseOrderRsp queryPurchaseData(PurchaseOrderReq req) {
        WhereSQL whereSQL = req.genWhereSQL();
        List<String> industryDept = req.getCategory();
        if (ListUtils.isNotEmpty(industryDept)) {
            // 行业部门，实际上行业分类，要特别处理
            List<String> category = ListUtils.filter(industryDept, o -> !"未分类".equals(o));
            List<String> others = ListUtils.filter(industryDept, o -> "未分类".equals(o));
            if (!category.isEmpty() || !others.isEmpty()) {
                WhereSQL dept = new WhereSQL();
                if (!category.isEmpty()) {
                    dept.or("industry in (?)", getDeptByCategory(industryDept, false));
                }
                if (!others.isEmpty()) {
                    dept.or("industry is null or industry not in (?)", getDeptByCategory(industryDept, true));
                }
                whereSQL.and(dept);
            }
        }

        List<CloudDemandInnerBizUinConfigDO> allUinInfo = SpringUtil.getBean(EndToEndReportServiceImpl.class)
                .getAllUinInfo();
        Map<String, String> productMap = ListUtils.toMap(allUinInfo, p -> p.getProduct(), p -> p.getCustomerName());

        if (ListUtils.isNotEmpty(req.getProduct())) {
            WhereSQL productCon = new WhereSQL();
            if (req.getProduct().contains("CVM&CBS")) {
                productCon.or("product in (?)", baseDictService.queryPlanProductByDemandCategory("CVM"));
            }
            if (req.getProduct().contains("Elasticsearch Service")) {
                productCon.or("product in (?) and customer_name in (?)",
                        baseDictService.queryPlanProductByDemandCategory("CVM"),
                        productMap.get("Elasticsearch Service"));
            }
            if (req.getProduct().contains("弹性MapReduce")) {
                productCon.or("product in (?) and customer_name in (?)",
                        baseDictService.queryPlanProductByDemandCategory("CVM"), productMap.get("弹性MapReduce"));
            }
            if (req.getProduct().contains("EKS官网")) {
                productCon.or("product in (?) and customer_name in (?)",
                        baseDictService.queryPlanProductByDemandCategory("CVM"), productMap.get("EKS官网"));
            }
            if (req.getProduct().contains("云数据仓库")) {
                productCon.or("product in (?) and customer_name in (?)",
                        baseDictService.queryPlanProductByDemandCategory("CVM"), productMap.get("云数据仓库"));
            }
            if (req.getProduct().contains("裸金属")) {
                productCon.or("product in (?) ", baseDictService.queryPlanProductByDemandCategory("裸金属"));
            }
            if (req.getProduct().contains("GPU(裸金属&CVM)")) {
                productCon.or("product in (?)", baseDictService.queryPlanProductByDemandCategory("GPU"));
            }
            whereSQL.and(productCon);
        }

        String userName = LoginUtils.getUserName();
        List<String> curUserDepts = getCurUserDepts(userName, req.getProduct());
        if (ListUtils.isEmpty(curUserDepts)) {
            whereSQL.and("industry in (null)");
        } else if (curUserDepts.size() == 1 && curUserDepts.contains("all")) {
            //  无需任何操作
        } else {
            whereSQL.and(" industry in (?)", curUserDepts);
        }

        if (curUserDepts.size() == 1 && curUserDepts.contains("all") || curUserDepts.contains("内部业务部")) {
            //  无需额外操作
        } else {
            whereSQL.and("industry not in (?)", getDeptByCategory(Lang.list("内部业务"), false));
        }

        List<XyDeviceApplyDO> all =
                shuttleDBHelper.getAll(XyDeviceApplyDO.class, whereSQL.getSQL(), whereSQL.getParams());
        //  进行处理
        List<XyDeviceApplyDO> result = handleXyPurchaseData(all, req.getProduct());

        //  境内外 + 区域 + 地域的筛选，可用区不生效，端到端报表中的物理机Campus根本关联不到可用区
        result = ListUtils.filter(result, o -> {
            List<String> customhouseTitle = req.getCustomhouseTitle();
            List<String> areaName = req.getAreaName();
            List<String> regionName = req.getRegionName();
            List<String> instanceType = req.getInstanceType();
            if (ListUtils.isNotEmpty(customhouseTitle) && !customhouseTitle.contains(o.getCustomhouseTitle())) {
                return false;
            }
            if (ListUtils.isNotEmpty(areaName) && !areaName.contains(o.getAreaName())) {
                return false;
            }
            if (ListUtils.isNotEmpty(regionName) && !regionName.contains(o.getRegionName())) {
                return false;
            }
            if (ListUtils.isNotEmpty(instanceType) && !instanceType.contains(o.getInstanceType())) {
                return false;
            }
            return true;
        });

        PurchaseOrderRsp rsp = new PurchaseOrderRsp();
        rsp.setData(result);
        return rsp;
    }


    @SneakyThrows
    private List<XyDeviceApplyDO> handleXyPurchaseData(List<XyDeviceApplyDO> list, List<String> productType) {
        List<ZoneAreaCountryDTO> zoneAreaCountry = xyBaseInfoService.getZoneAreaCountry();
        Map<String, String> areaMap = ListUtils.toMap(zoneAreaCountry,
                ZoneAreaCountryDTO::getAreaName, ZoneAreaCountryDTO::getCountry);
        //  用于关联腾讯云境内外的属性（除ZoneName）
        Map<String, ErpRegionInfoDTO> erpRegionInfo = baseDictService.getErpRegionInfo();
        //  用于关联设备类型 -> 实例类型，一对一
        DictService commonDictService = SpringUtil.getBean(DictServiceImpl.class);
        Map<String, String> deviceType2InstanceTypeMap = commonDictService.getCsigDeviceTypeToInstanceTypeMap();

        Map<String, Integer> logicCpuCoreMap = baseDictService.getDeviceLogicCpuCore();
        ListUtils.forEach(list, o -> {
            Integer logicCpuCore = logicCpuCoreMap.get(o.getDeviceType());
            o.setCategory(dictService.getCategoryByDept(o.getIndustry()));
            o.setLogicCpuCore(logicCpuCore);
            o.setGpuCard(baseDictService.getDeviceGpuCard(o.getDeviceType()));
            o.setTotalCore(multiply(logicCpuCore, o.getTotalNum()));
            o.setTotalGpuCard(multiply(o.getGpuCard(), o.getTotalNum()));

            o.setUsedCore(multiply(logicCpuCore, o.getUsedNum()));
            o.setUsedGpuCard(multiply(o.getGpuCard(), o.getUsedNum()));

            o.setNoUsedNum(positiveSub(o.getTotalNum(), o.getUsedNum()));
            o.setNoUsedCore(positiveSub(o.getTotalCore(), o.getUsedCore()));
            o.setNoUsedGpuCard(positiveSub(o.getTotalGpuCard(), o.getUsedGpuCard()));

            o.setDeliverCore(multiply(logicCpuCore, o.getDeliverNum()));
            o.setDeliverGpuCard(multiply(o.getGpuCard(), o.getDeliverNum()));

            o.setNoDeliverCore(positiveSub(o.getUsedCore(), o.getDeliverCore()));
            o.setNoDeliverGpuCard(positiveSub(o.getDeliverGpuCard(), o.getDeliverGpuCard()));
            o.setNoDeliverNum(positiveSub(o.getUsedNum(), o.getDeliverNum()));
            o.setStatus(codeMap.get(NumberUtils.parseInt(o.getStatus())));
            o.setCountry(areaMap.get(o.getRegion()));

            ErpRegionInfoDTO dto = erpRegionInfo.get(o.getCampus());
            if (dto != null) {
                o.setCustomhouseTitle(Objects.equals(dto.getCountry(), "中国内地") ? "境内" : "境外");
                o.setAreaName(dto.getArea());
                o.setRegionName(dto.getCity());
            } else {
                o.setCustomhouseTitle("未分类");
                o.setAreaName("未分类");
                o.setRegion("未分类");
            }
            o.setInstanceType(deviceType2InstanceTypeMap.get(o.getDeviceType()));

            Boolean isZysy = dictService.isZysyModule(o.getProduct(), o.getBusiness1(),
                    o.getBusiness2(), o.getBusiness3());
            if (isZysy != null) {
                o.setProjType(isZysy ? "自研上云" : "非自研上云");
            }

            String omdPromiseInfo = o.getOmdPromiseInfo();
            if (StringTools.isNotBlank(omdPromiseInfo)) {
                ObjectMapper mapper = new ObjectMapper();
                HashMap<String, Object> map = null;
                List<String> infoList = Lang.list();
                try {
                    map = mapper.readValue(omdPromiseInfo, HashMap.class);
                    map.entrySet().stream()
                            .forEach(entry -> infoList.add(entry.getKey() + ":" + entry.getValue() + "台"));
                    o.setOmdPromiseInfo(Strings.join("; ", infoList));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        //  剔除自研上云模块
        list = ListUtils.filter(list, o -> {
            if (Objects.equals(o.getProjType(), "自研上云")) {
                return false;
            }
            if (productType.contains("GPU(裸金属&CVM)")) {
                if (o.getGpuCard() == 0) {
                    return false;
                }
            } else {
                if (o.getGpuCard() > 0) {
                    return false;
                }
            }
            return true;
        });

        return list;

    }

    private Integer multiply(Integer arg1, Integer arg2) {
        if (arg1 == null || arg2 == null) {
            return null;
        }
        return arg1 * arg2;
    }

    private Integer substract(Integer arg1, Integer arg2) {
        if (arg1 == null || arg2 == null) {
            return null;
        }
        return arg1 - arg2;
    }

    /**
     * 两数相减、负数取0
     */
    private Integer positiveSub(Integer arg1, Integer arg2) {
        if (arg1 == null || arg2 == null) {
            return null;
        }
        return arg1 - arg2 < 0 ? 0 : arg1 - arg2;
    }


    /**
     * @return 外部行业的app_role (集合不可变)
     */
    private List<String> getExternalAppRole() {
        return Arrays.asList("正常售卖", "CDH", "预扣包", "GOCP", "CDZ");
    }

    /**
     * @return 内部行业需要排除的app_role (集合不可变)
     */
    private List<String> getWithOutInsideAppRole() {
        return Arrays.asList("正常售卖", "CDH", "预扣包", "GOCP", "CDZ", "LH");
    }

    @Override
    public BillingSchaleRsp queryIncreaseScaleData(BillingSchaleReq req) {
        // 外部行业
        List<String> externalAppRole = getExternalAppRole();
        // 内领业务需要排除的
        List<String> withOutInsideAppRole = getWithOutInsideAppRole();

        //  获取基本的筛选项
        WhereSQL whereSQL = req.genWhereSQL();

        /* sql修改：
          外部 app_role=正常售卖/CDH/预扣包/GOCP/CDZ
          内部 剔除app_role 正常售卖/CDH/预扣包/GOCP/CDZ/LH
         */
        String sql = "";
        if (req.getIsDistinct() != null && req.getIsDistinct()) {
            sql = ORMUtils.getSql("/sql/industry_report/detail/ppl_billing_scale_detail_distinct.sql");
        } else {
            sql = ORMUtils.getSql("/sql/industry_report/detail/ppl_billing_scale_detail.sql");
        }

        //  数据权限校验
        String userName = LoginUtils.getUserName();

        //  产品
        List<String> product = Lang.list();
        if (StringTools.isBlank(req.getCpuOrGpu()) || Objects.equals(req.getCpuOrGpu(), "GPU")) {
            product.add("GPU");
        }
        if (StringTools.isBlank(req.getCpuOrGpu()) || req.getBizType().isEmpty() || req.getBizType().contains("CVM")) {
            product.add("CVM");
        }
        if (StringTools.isBlank(req.getCpuOrGpu()) || req.getBizType().isEmpty() || req.getBizType()
                .contains("裸金属")) {
            product.add("裸金属");
        }

        List<String> curUserDepts = getCurUserDepts(userName, product);
        if (ListUtils.isEmpty(curUserDepts)) {
            whereSQL.and("org_label in (null)");
        } else if (curUserDepts.size() == 1 && curUserDepts.contains("all")) {
            //  无需任何筛选
        } else {
            whereSQL.and("org_label in (?)", curUserDepts);
        }

        WhereSQL innerCondition = whereSQL.copy();
        // 在这里 not in 和 in 必须相同，否则会丢失数据
        innerCondition.and(" app_role not in (?)", withOutInsideAppRole);

        whereSQL.and("app_role in (?)", externalAppRole);

        //  行业分类筛选
        if (ListUtils.isNotEmpty(req.getCategory())) {
            whereSQL.and("industry_category in (?)", req.getCategory());

            if (!req.getCategory().contains("内部业务")) {
                innerCondition.and("industry_category in (?)", req.getCategory());
            }
        }

        //  行业部门筛选
        if (ListUtils.isNotEmpty(req.getIndustryDept())) {
            whereSQL.and("org_label in (?)", req.getIndustryDept());
            innerCondition.and("org_label in (?)", req.getIndustryDept());
        }

        //  app_role筛选
        if (ListUtils.isNotEmpty(req.getAppRole())) {
            whereSQL.and(" app_role in (?) ", req.getAppRole());

            if (!req.getCategory().contains("内部业务")) {
                innerCondition.and(" app_role in (?) ", req.getAppRole());
            }
        }

        if (req.getIsDistinct() == null || !req.getIsDistinct()) {
            //  需求类型筛选
            if (ListUtils.isNotEmpty(req.getType())) {
                WhereSQL demandType = new WhereSQL();
                if (req.getType().contains("NEW")) {
                    demandType.or(Objects.equals(req.getCpuOrGpu(), "GPU") ? "diff_billgpu >= 0" : "diff_billcpu >= 0");
                }
                if (req.getType().contains("RETURN")) {
                    demandType.or(Objects.equals(req.getCpuOrGpu(), "GPU") ? "diff_billgpu < 0" : "diff_billcpu < 0");
                }
                whereSQL.and(demandType);
            }
        }

        if (req.getBizType().contains("CVM") && Objects.equals(req.getCpuOrGpu(), "CPU")) {
            //  内部业务部分的逻辑
            if ((ListUtils.isEmpty(req.getCategory()) || req.getCategory().contains("内部业务"))) {
                //  首先要判断是否有这部分的数据权限
                if (curUserDepts.contains("内部业务部") || (curUserDepts.size() == 1 && curUserDepts.contains("all"))) {
                    //  新增和退回的判断逻辑同其他不同，这里取服务核作为判断是新增还是退回
                    WhereSQL typeCond = new WhereSQL();
                    if (req.getIsDistinct() == null || !req.getIsDistinct()) {
                        if (ListUtils.isNotEmpty(req.getType())) {
                            // 不排除为0的情况
                            if (req.getType().contains("NEW")) {
                                typeCond.or("diff_freecpu >= 0");

                            }
                            if (req.getType().contains("RETURN")) {
                                typeCond.or("diff_freecpu <= 0");
                            }
                            innerCondition.and(typeCond);
                        }
                    }

                    if (ListUtils.isNotEmpty(req.getAppRole())) {
                        List<String> appRoleFilter = req.getAppRole();
                        // 排除非内部业务的app_role
                        appRoleFilter = ListUtils.subtract(appRoleFilter, withOutInsideAppRole);
                        if (ListUtils.isNotEmpty(appRoleFilter)) {
                            innerCondition.and("app_role in (?)", appRoleFilter);
                        }
                    }
                } else {
                    innerCondition.and("1 = 0");
                }
                whereSQL.or(innerCondition);
            }
        }

        sql = sql.replace("${FILTER}", whereToAnd(whereSQL.getSQL()));

        Set<String> groupByColumn = Lang.set();
        if (req.getIsDistinct() != null && req.getIsDistinct()) {
            List<String> distinctFields = Lang.list("ginsFamily", "statTime", "category", "regionName");
            groupByColumn.addAll(BillingScaleGroupByEnum.getGroupByColumn(distinctFields));
        }

        groupByColumn.addAll(BillingScaleGroupByEnum.getGroupByColumn(req.getGroupBy()));
        if (ListUtils.isNotEmpty(groupByColumn)) {
            sql = sql + " group by " + String.join(",", groupByColumn.toArray(new String[0]));
        }

        List<CkPplBillingScaleMonthlyVO> all =
                ckcldDBHelper.getRaw(CkPplBillingScaleMonthlyVO.class, sql, whereSQL.getParams());

        if (req.getIsDistinct() != null && req.getIsDistinct()) {
            if (Objects.equals("CPU", req.getCpuOrGpu())) {
                if (req.getType().size() == 1) {
                    if (req.getType().contains("NEW")) {
                        all = ListUtils.filter(all, o -> o.getNewDiffBillcpu() > 0);
                    } else {
                        all = ListUtils.filter(all, o -> o.getReturnDiffBillcpu() < 0);
                    }
                }
            } else {
                if (req.getType().size() == 1) {
                    if (req.getType().contains("NEW")) {
                        all = ListUtils.filter(all, o -> o.getNewDiffBillgpu() > 0);
                    } else {
                        all = ListUtils.filter(all, o -> o.getReturnDiffBillgpu() < 0);
                    }
                }
            }
        }

        BillingSchaleRsp rsp = new BillingSchaleRsp();
        rsp.setData(all);
        rsp.setTotal(all.size());
        return rsp;
    }


    @Override
    @SneakyThrows
    public StreamDownloadBean downloadBillingScaleDetail() {

        //  读取模板
        String templateName = "industry_report_billing_scale_detail.xlsx";
        InputStream is = IOUtils.readClasspathResourceInputStream("excel/industry_report/" + templateName);

        //  连接输入、输出管道流
        PipedInputStream pipeInput = new PipedInputStream();
        PipedOutputStream pipeOutput = new PipedOutputStream();
        //  pipeInput.connect(pipeOutput);
        //  一般用output.connect(input)
        pipeOutput.connect(pipeInput);

        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("stat_time = ?", DateUtils.format(new Date(), "yyyy-MM") + "-01");
        whereSQL.addOrderBy("id");

        //  另起一个线程往管道流中写入数据
        threadPool.submit(() -> {
            //  分页读取、批量写入
            int start = 0;
            int size = 1_000;
            // new excel writer
//            ExcelWriter excelWriter = new ExcelWriter();
            while (true) {
                whereSQL.limit(start, size);
                List<PplBillingScaleMonthlyDO> list =
                        demandDBHelper.getAll(PplBillingScaleMonthlyDO.class, whereSQL.getSQL(), whereSQL.getParams());
                if (!list.isEmpty()) {
                    EasyExcel.write(pipeOutput).withTemplate(is).sheet(0).doWrite(list);
//                    try {
//                        pipeOutput.write(out.toByteArray());
//                    } catch (IOException e) {
//                        e.printStackTrace();
//                    }
                    if (list.size() < size) {    // 少查一次
                        break;
                    }
                } else {
                    break;
                }
                start += size;
            }
            try {
                // 关闭excel writer
                // excelWriter.finish();
                pipeOutput.close();
            } catch (Exception e) {
                // TODO log
            }

        });

//        pipeInput.read();

//        //  获取文件名，根据所选时间区间确定
//        String intervalMark = "";
//        if (StringTools.isNotBlank(beginYearMonth) && StringTools.isNotBlank(endYearMonth)){
//            intervalMark += beginYearMonth + "~" + endYearMonth;
//        } else if (StringTools.isNotBlank(beginYearMonth) && StringTools.isBlank(endYearMonth)){
//            intervalMark += beginYearMonth;
//        } else if (StringTools.isBlank(beginYearMonth) && StringTools.isNotBlank(endYearMonth)){
//            intervalMark += endYearMonth;
//        }

//        return new StreamDownloadBean("行业规模净增" + intervalMark + "数据.xlsx" , pipeInput);
        return new StreamDownloadBean("行业规模净增明细.xlsx", pipeInput);
    }

    @Override
    public Map<Integer, String> getPurchaseOrderStatusMap() {
        return codeMap;
    }

    private static String whereToAnd(String whereSql) {
        if (StringTools.isBlank(whereSql)) {
            return "";
        }
        whereSql = whereSql.trim();
        if (whereSql.toLowerCase().startsWith("where")) {
            whereSql = whereSql.substring("where".length());
        }
        if (StringTools.isNotBlank(whereSql)) {
            whereSql = " AND (" + whereSql + ")";
        }
        return whereSql;
    }


    /**
     * 获取行业预测数据
     */
    private List<Map<String, Object>> fillForecast(List<String> yearMonthList, String userName,
            IndustryReportOverviewReq req) {
        List<Map<String, Object>> result = Lang.list();
        String sql = ORMUtils.getSql("/sql/industry_report/overview/new_forecast_order_core_sum.sql");
        sql = PaasUtils.sqlReplacePAAS(sql);
        Map<String, Object> param = new HashMap<>();

//        String[] beginYearMonth = yearMonthList.get(0).split("-");
//        String[] endYearMonth = yearMonthList.get(yearMonthList.size() - 1).split("-");

//        param.put("beginYear", NumberUtils.parseInt(beginYearMonth[0]));
//        param.put("beginMonth", NumberUtils.parseInt(beginYearMonth[1]));
//        param.put("endYear", NumberUtils.parseInt(endYearMonth[0]));
//        param.put("endMonth", NumberUtils.parseInt(endYearMonth[1]));

        List<String> industryDept = req.getIndustryDept();
        if (ListUtils.isNotEmpty(industryDept)) {
            // 行业部门，实际上行业分类，要特别处理
            List<String> category = ListUtils.filter(industryDept, o -> !"未分类".equals(o));
            List<String> others = ListUtils.filter(industryDept, o -> "未分类".equals(o));
            if (!category.isEmpty() || !others.isEmpty()) {
                WhereSQL dept = new WhereSQL();
                if (!category.isEmpty()) {
                    dept.or(" category in (:category)");
                    param.put("category", industryDept);
                }
                if (!others.isEmpty()) {
                    dept.or(" industry_dept = '(空值)' or category = '(空值)' ");
                }
                sql = sql.replace("${FILTER}", whereToAnd(dept.getSQL()));
            }
        } else {
            sql = sql.replace("${FILTER}", " ");
        }

        List<String> curUserDepts = getCurUserDepts(userName, Lang.list(req.getProductType()));
        if (ListUtils.isEmpty(curUserDepts)) {
            sql = sql.replace("${AUTH_FILTER}", " and industry_dept in (null) ");
        } else if (curUserDepts.size() == 1 && curUserDepts.contains("all")) {
            sql = sql.replace("${AUTH_FILTER}", " ");
        } else {
            sql = sql.replace("${AUTH_FILTER}", " and industry_dept in (:auth)");
            param.put("auth", curUserDepts);
        }

        if (((curUserDepts.size() == 1 && curUserDepts.contains("all"))
                || (curUserDepts.contains("内部业务部"))) && (industryDept.contains("内部业务")
                || industryDept.isEmpty())) {
            sql = sql.replace("${INNER_AUTH_FILTER}", "");
        } else {
            sql = sql.replace("${INNER_AUTH_FILTER}", " and 0 = 1");
        }

        //  只有CVM才有内部业务的筛选，其他直接返回
        switch (req.getProductType()) {
            case "CVM":
                sql = sql.replace("${PRODUCT}", "and product in ('CVM', 'CVM&CBS')");
                sql = sql.replace("${INNER_PRODUCT}", "");
                break;
            case "裸金属":
                sql = sql.replace("${PRODUCT}", "and product in ('裸金属')");
                sql = sql.replace("${INNER_PRODUCT}", " and 1 = 0");
                break;
            case "GPU":
                sql = sql.replace("${PRODUCT}", "and product in ('GPU(裸金属&CVM)')");
                sql = sql.replace("${INNER_PRODUCT}", " and 1 = 0");
                break;
        }

        // 异步查数据
        // 按年月分组
        List<String> _yearMonth = RangeUtils.rangeDate(DateUtils.format(req.getBeginYearMonth(), "yyyy-MM"),
                DateUtils.format(req.getEndYearMonth(), "yyyy-MM"));
        _yearMonth = _yearMonth.stream().map(item -> "'" + item + "'").collect(Collectors.toList());

        List<PplItemOrderDTO> dtos = BatchQueryUtils.getRaw((newSql)
                        -> DBList.ckcldStdCrpDBHelper.getRaw(PplItemOrderDTO.class, newSql, param),
                sql,
                "${yearMonth}",
                _yearMonth);

        ListUtils.forEach(dtos, o ->
                o.setLogicNum(Objects.equals("GPU", req.getProductType()) ? o.getTotalGpuNum() : o.getTotalCore()));

        for (String yearMonth : yearMonthList) {
            //  过滤出当月数据
            List<PplItemOrderDTO> curList = ListUtils.filter(dtos, o -> Objects.equals(o.getYearMonth(), yearMonth));

            //  填充数据
            String yearMonthIndex = "y" + yearMonth.split("-")[0] + "m" + yearMonth.split("-")[1];
            for (PplItemOrderDTO dto : curList) {
                Map<String, Object> curMap = new HashMap<>();
                curMap.put("yearMonth", yearMonthIndex);
                curMap.put("industryDept", dto.getIndustryDept());
                curMap.put("category", "预测");
                curMap.put("subCategory", dto.getDemandType());
                curMap.put("value", dto.getLogicNum());
                result.add(curMap);
            }

            //  新增预约量-总计
            Map<String, Object> summaryNew = new HashMap<>();
            summaryNew.put("yearMonth", yearMonthIndex);
            summaryNew.put("industryDept", "合计");
            summaryNew.put("category", "预测");
            summaryNew.put("subCategory", "预测新增");
            summaryNew.put("value", ListUtils.isEmpty(curList) ? null :
                    NumberUtils.sum(ListUtils.filter(curList, o -> Objects.equals(o.getDemandType(), "预测新增")),
                            o -> o.getLogicNum()));
            result.add(summaryNew);

            Map<String, Object> summaryElastic = new HashMap<>();
            summaryElastic.put("yearMonth", yearMonthIndex);
            summaryElastic.put("industryDept", "合计");
            summaryElastic.put("category", "预测");
            summaryElastic.put("subCategory", "预测弹性");
            summaryElastic.put("value", ListUtils.isEmpty(curList) ? null :
                    NumberUtils.sum(ListUtils.filter(curList, o -> Objects.equals(o.getDemandType(), "预测弹性")),
                            o -> o.getLogicNum()));
            result.add(summaryElastic);

            Map<String, Object> summaryReturn = new HashMap<>();
            summaryReturn.put("yearMonth", yearMonthIndex);
            summaryReturn.put("industryDept", "合计");
            summaryReturn.put("category", "预测");
            summaryReturn.put("subCategory", "预测退回");
            summaryReturn.put("value", ListUtils.isEmpty(curList) ? null :
                    NumberUtils.sum(ListUtils.filter(curList, o -> Objects.equals(o.getDemandType(), "预测退回")),
                            o -> o.getLogicNum()));
            result.add(summaryReturn);
        }

        return result;
    }


    /**
     * 获取行业预约数据
     */
    private List<Map<String, Object>> fillReservation(List<String> yearMonthList, String userName,
            IndustryReportOverviewReq req) {
        List<Map<String, Object>> result = Lang.list();
        String firstDate = yearMonthList.get(0) + "-01";
        String lastDate = SpecialDateUtils.getCurMonthLastDate(yearMonthList);

        String sql = ORMUtils.getSql("/sql/industry_report/overview/yunxiao_order_core_sum.sql");
        Map<String, Object> param = new HashMap<>();
        param.put("start", firstDate);
        param.put("end", lastDate);

        List<String> industryDept = req.getIndustryDept();
        if (ListUtils.isNotEmpty(req.getIndustryDept())) {
            // 行业部门，实际上行业分类，要特别处理
            List<String> category = ListUtils.filter(industryDept, o -> !"未分类".equals(o));
            List<String> others = ListUtils.filter(industryDept, o -> "未分类".equals(o));
            if (!category.isEmpty() || !others.isEmpty()) {
                WhereSQL dept = new WhereSQL();
                if (!category.isEmpty()) {
                    dept.or(" category in (:category)");
                    param.put("category", industryDept);
                }
                if (!others.isEmpty()) {
                    dept.or(" industry_dept = '(空值)' or industry_dept = '(空值)' ");
                }
                sql = sql.replace("${FILTER}", whereToAnd(dept.getSQL()));
            }
        } else {
            sql = sql.replace("${FILTER}", " ");
        }

        List<String> curUserDepts = getCurUserDepts(userName, Lang.list(req.getProductType()));
        if (ListUtils.isEmpty(curUserDepts)) {
            sql = sql.replace("${AUTH_FILTER}", " and industry_dept in (null) ");
        } else if (curUserDepts.size() == 1 && curUserDepts.contains("all")) {
            sql = sql.replace("${AUTH_FILTER}", " ");
        } else {
            sql = sql.replace("${AUTH_FILTER}", " and industry_dept in (:auth)");
            param.put("auth", curUserDepts);
        }

        if (Objects.equals("CVM", req.getProductType())) {
            sql = sql.replace("${INNER_PRODUCT}", " ");
            if (((curUserDepts.size() == 1 && curUserDepts.contains("all"))
                    || (curUserDepts.contains("内部业务部"))) && (industryDept.contains("内部业务")
                    || industryDept.isEmpty())) {
                sql = sql.replace("${INNER_AUTH_FILTER}", "");
            } else {
                sql = sql.replace("${INNER_AUTH_FILTER}", "and 0 = 1");
            }
        } else {
            sql = sql.replace("${INNER_PRODUCT}", " and 1 = 0");
            sql = sql.replace("${INNER_AUTH_FILTER}", "");
        }

        String product = "";
        //  产品筛选
        switch (req.getProductType()) {
            case "CVM":
                product = " and product in ('CVM&CBS')";
                break;
            case "GPU":
                product = "  and product in ('GPU(裸金属&CVM)')";
                break;
        }
        sql = sql.replace("${PRODUCT}", product);

        List<PplItemOrderDTO> dtos = DBList.ckcldStdCrpDBHelper.getRaw(PplItemOrderDTO.class, sql, param);

        ListUtils.forEach(dtos, o ->
                o.setLogicNum(Objects.equals(req.getProductType(), "GPU") ? o.getTotalGpuNum() : o.getTotalCore()));

        //  By月填充
        for (String yearMonth : yearMonthList) {
            //  过滤出当月数据
            List<PplItemOrderDTO> curList =
                    ListUtils.filter(dtos, o -> Objects.equals(o.getYearMonth(), yearMonth));

            //  填充数据
            String yearMonthIndex = "y" + yearMonth.split("-")[0] + "m" + yearMonth.split("-")[1];
            for (PplItemOrderDTO dto : curList) {
                Map<String, Object> curMap = new HashMap<>();
                curMap.put("yearMonth", yearMonthIndex);
                curMap.put("industryDept", dto.getIndustryDept());
                curMap.put("category", "新增预约量");
                curMap.put("subCategory", dto.getDemandType());
                curMap.put("value", dto.getLogicNum());
                result.add(curMap);
            }

            //  新增预约量-总计
            Map<String, Object> summaryNew = new HashMap<>();
            summaryNew.put("yearMonth", yearMonthIndex);
            summaryNew.put("industryDept", "合计");
            summaryNew.put("category", "新增预约量");
            summaryNew.put("subCategory", "新增");
            summaryNew.put("value", ListUtils.isEmpty(curList) ? null :
                    NumberUtils.sum(ListUtils.filter(curList, o -> Objects.equals(o.getDemandType(), "新增")),
                            o -> o.getLogicNum()));
            result.add(summaryNew);

            //  弹性预约量-总计
            Map<String, Object> summaryElastic = new HashMap<>();
            summaryElastic.put("yearMonth", yearMonthIndex);
            summaryElastic.put("industryDept", "合计");
            summaryElastic.put("category", "新增预约量");
            summaryElastic.put("subCategory", "弹性");
            summaryElastic.put("value", ListUtils.isEmpty(curList) ? null :
                    NumberUtils.sum(ListUtils.filter(curList, o -> Objects.equals(o.getDemandType(), "弹性")),
                            o -> o.getLogicNum()));
            result.add(summaryElastic);
        }
        return result;
    }

    /**
     * 获取产品采购数据-数据源为进销存采购单明细
     */
    private List<Map<String, Object>> fillXyPurchase(List<String> yearMonthList, String userName,
            IndustryReportOverviewReq req) {
        List<Map<String, Object>> result = Lang.list();

        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("(order_type in (1, 100) or (order_type=0 and status in (0,1,3,4,5,6)))");
        whereSQL.and("STATUS NOT IN (0,3,5,8000127)");

        List<String> planProductName = baseDictService.queryPlanProductByDemandCategory(req.getProductType());
        whereSQL.and("product in (?)", planProductName);

        whereSQL.and("plan_month in (?)", yearMonthList);

        List<String> industryDept = req.getIndustryDept();
        if (ListUtils.isNotEmpty(industryDept)) {
            // 行业部门，实际上行业分类，要特别处理
            List<String> category = ListUtils.filter(industryDept, o -> !"未分类".equals(o));
            List<String> others = ListUtils.filter(industryDept, o -> "未分类".equals(o));
            if (!category.isEmpty() || !others.isEmpty()) {
                WhereSQL dept = new WhereSQL();
                if (!category.isEmpty()) {
                    dept.or(" industry in (?)", getDeptByCategory(industryDept, false));
                }
                if (!others.isEmpty()) {
                    dept.or("industry is null or industry not in (?)", getDeptByCategory(industryDept, true));
                }
                whereSQL.and(dept);
            }
        }

        List<String> curUserDepts = getCurUserDepts(userName, Lang.list(req.getProductType()));
        if (ListUtils.isEmpty(curUserDepts)) {
            whereSQL.and("industry in (null) ");
        } else if (curUserDepts.size() == 1 && curUserDepts.contains("all")) {
            //  无需筛选
        } else {
            whereSQL.and("industry in (?)", curUserDepts);
        }

        if (((curUserDepts.size() == 1 && curUserDepts.contains("all"))
                || (curUserDepts.contains("内部业务部"))) && (industryDept.contains("内部业务")
                || industryDept.isEmpty())) {
            //  无需筛选
        } else {
            whereSQL.and("industry not in (?)", getDeptByCategory(Lang.list("内部业务"), false));
        }

        whereSQL.addGroupBy("plan_month", "industry", "demand_type", "deviceType", "product", "business1", "business2",
                "business3", "purchase_category");

        Map<String, Integer> logicCpuCoreMap = baseDictService.getDeviceLogicCpuCore();
        List<XyPurchaseSummaryDTO> dtos =
                shuttleDBHelper.getAll(XyPurchaseSummaryDTO.class, whereSQL.getSQL(), whereSQL.getParams());

        ListUtils.forEach(dtos, o -> {
            //  采购单中取行业为内部业务的记录
            if (Objects.equals(o.getIndustry(), "内部业务")) {
                o.setIndustry("内部业务");
            } else {
                o.setIndustry(dictService.getCategoryByDept(o.getIndustry()));
            }
            o.setTotalCoreNum(AmountUtils.multiply(o.getTotalNum(), logicCpuCoreMap.get(o.getDeviceType())));
            o.setTotalGpuCard(
                    AmountUtils.multiply(o.getTotalNum(), baseDictService.getDeviceGpuCard(o.getDeviceType())));
            Boolean isZysy = dictService.isZysyModule(o.getProduct(), o.getBusiness1(), o.getBusiness2(),
                    o.getBusiness3());
            if (isZysy != null) {
                o.setProjType(isZysy ? "自研上云" : "非自研上云");
            }

        });

        //  剔除自研上云模块和GPU设备
        dtos = ListUtils.filter(dtos, o -> {
            boolean flag = true;
            if (Objects.equals(o.getProjType(), "自研上云")) {
                flag = false;
            }
            Integer deviceGpuCard = baseDictService.getDeviceGpuCard(o.getDeviceType());
            if (Objects.equals("GPU", req.getProductType())) {
                if (deviceGpuCard == 0) {
                    flag = false;
                }
            } else {
                if (deviceGpuCard > 0) {
                    return false;
                }
            }
            return flag;
        });

        //  重新分组
        Map<String, List<XyPurchaseSummaryDTO>> map =
                ListUtils.groupBy(dtos, o -> String.join(";",
                        o.getIndustry(), o.getPurchaseCategory(), o.getPlanMonth()));

        List<XyPurchaseSummaryDTO> list = Lang.list();
        Collection<List<XyPurchaseSummaryDTO>> values = map.values();
        for (List<XyPurchaseSummaryDTO> value : values) {
            XyPurchaseSummaryDTO dto = new XyPurchaseSummaryDTO();
            dto.setPlanMonth(value.get(0).getPlanMonth());
            dto.setIndustry(value.get(0).getIndustry());
            dto.setPurchaseCategory(value.get(0).getPurchaseCategory());
            dto.setTotalCoreNum(NumberUtils.sum(value, o -> o.getTotalCoreNum()));
            dto.setTotalGpuCard(NumberUtils.sum(value, o -> o.getTotalGpuCard()));
            dto.setLogicNum(
                    Objects.equals(req.getProductType(), "GPU") ? dto.getTotalGpuCard() : dto.getTotalCoreNum());
            list.add(dto);
        }

        for (String yearMonth : yearMonthList) {
            List<XyPurchaseSummaryDTO> curList = ListUtils.filter(list,
                    o -> Objects.equals(o.getPlanMonth(), yearMonth));
            //  数据整合
            String yearMonthIndex = "y" + yearMonth.split("-")[0] + "m" + yearMonth.split("-")[1];
            for (XyPurchaseSummaryDTO dto : curList) {
                Map<String, Object> curMap = new HashMap<>();
                curMap.put("yearMonth", yearMonthIndex);
                curMap.put("industryDept", dto.getIndustry());
                curMap.put("category", "产品采购和预测(逻辑核)");
                curMap.put("subCategory", dto.getPurchaseCategory());
                curMap.put("value", dto.getLogicNum());
                result.add(curMap);
            }

            //  产品采购和预测(逻辑核)
            Map<String, Object> summaryCommon = new HashMap<>();
            summaryCommon.put("yearMonth", yearMonthIndex);
            summaryCommon.put("industryDept", "合计");
            summaryCommon.put("category", "产品采购和预测(逻辑核)");
            summaryCommon.put("subCategory", "常规");
            summaryCommon.put("value", ListUtils.isEmpty(curList) ? null :
                    NumberUtils.sum(ListUtils.filter(curList, o -> Objects.equals(o.getPurchaseCategory(), "常规")),
                            o -> o.getLogicNum()));
            result.add(summaryCommon);

            Map<String, Object> summaryElse = new HashMap<>();
            summaryElse.put("yearMonth", yearMonthIndex);
            summaryElse.put("industryDept", "合计");
            summaryElse.put("category", "产品采购和预测(逻辑核)");
            summaryElse.put("subCategory", "其他");
            summaryElse.put("value", ListUtils.isEmpty(curList) ? null :
                    NumberUtils.sum(ListUtils.filter(curList, o -> Objects.equals(o.getPurchaseCategory(), "其他")),
                            o -> o.getLogicNum()));
            result.add(summaryElse);
        }

        return result;
    }

    /**
     * 获取产品采购数据-数据源为进销存采购单明细，这里没统计到未交付的，弃用
     */
    private List<Map<String, Object>> fillPurchaseData(String yearMonth, List<String> industryDept, String userName) {
        List<Map<String, Object>> result = Lang.list();
        String curMonthFirstDay = yearMonth + "-01";
        //  获取当月最后一天的数据
        String curMonthLastDay = DateUtils.formatDate(DateUtils.addTime(
                DateUtils.addTime(DateUtils.parse(curMonthFirstDay), Calendar.MONTH, 1), Calendar.DATE, -1));
        String sql = ORMUtils.getSql("/sql/industry_report/overview/purchse_order_core_sum.sql");
        Map<String, Object> param = new HashMap<>();
        param.put("start", curMonthFirstDay);
        param.put("end", curMonthLastDay);
        if (ListUtils.isNotEmpty(industryDept)) {
            // 行业部门，实际上行业分类，要特别处理
            List<String> category = ListUtils.filter(industryDept, o -> !"未分类".equals(o));
            List<String> others = ListUtils.filter(industryDept, o -> "未分类".equals(o));
            if (!category.isEmpty() || !others.isEmpty()) {
                WhereSQL dept = new WhereSQL();
                if (!category.isEmpty()) {
                    dept.or(" industry in (:industry)");
                    param.put("industry", getDeptByCategory(industryDept, false));
                }
                if (!others.isEmpty()) {
                    dept.or("industry is null or industry not in (:allIndustry)");
                    param.put("allIndustry", getDeptByCategory(industryDept, true));
                }
                sql = sql.replace("${FILTER}", whereToAnd(dept.getSQL()));
            }
        } else {
            sql = sql.replace("${FILTER}", " ");
        }

        List<String> curUserDepts = getCurUserDepts(userName, null);
        if (ListUtils.isEmpty(curUserDepts)) {
            sql = sql.replace("${AUTH_FILTER}", " and industry in (null) ");
        } else if (curUserDepts.size() == 1 && curUserDepts.contains("all")) {
            sql = sql.replace("${AUTH_FILTER}", " ");
        } else {
            sql = sql.replace("${AUTH_FILTER}", " and industry in (:auth)");
            param.put("auth", curUserDepts);
        }

        List<PurchaseOrderDTO> dtos = rrpDBHelper.getRaw(PurchaseOrderDTO.class, sql, param);
        ListUtils.forEach(dtos, o -> o.setIndustry(dictService.getCategoryByDept(o.getIndustry())));

        //  重新分组
        Map<String, List<PurchaseOrderDTO>> map =
                ListUtils.groupBy(dtos, o -> String.join(";", o.getIndustry(), o.getDemandType()));

        List<PurchaseOrderDTO> list = Lang.list();
        Collection<List<PurchaseOrderDTO>> values = map.values();
        for (List<PurchaseOrderDTO> value : values) {
            PurchaseOrderDTO dto = new PurchaseOrderDTO();
            dto.setIndustry(value.get(0).getIndustry());
            dto.setDemandType(value.get(0).getDemandType());
            dto.setCoreNum(NumberUtils.sum(value, o -> o.getCoreNum()));
            list.add(dto);
        }

        String yearMonthIndex = "y" + yearMonth.split("-")[0] + "m" + yearMonth.split("-")[1];
        for (PurchaseOrderDTO dto : list) {
            Map<String, Object> curMap = new HashMap<>();
            curMap.put("yearMonth", yearMonthIndex);
            curMap.put("industryDept", dto.getIndustry());
            curMap.put("category", "产品采购和预测(逻辑核)");
            curMap.put("subCategory", Objects.equals(dto.getDemandType(), "短租") ? "短租(弹性)" : dto.getDemandType());
            curMap.put("value", dto.getCoreNum());
            result.add(curMap);
        }

        //  产品采购和预测(逻辑核)
        Map<String, Object> summaryCommon = new HashMap<>();
        summaryCommon.put("yearMonth", yearMonthIndex);
        summaryCommon.put("industryDept", "合计");
        summaryCommon.put("category", "产品采购和预测(逻辑核)");
        summaryCommon.put("subCategory", "常规");
        summaryCommon.put("value", ListUtils.isEmpty(list) ? null :
                NumberUtils.sum(ListUtils.filter(list, o -> Objects.equals(o.getDemandType(), "常规")),
                        o -> o.getCoreNum()));
        result.add(summaryCommon);

        Map<String, Object> summaryElse = new HashMap<>();
        summaryElse.put("yearMonth", yearMonthIndex);
        summaryElse.put("industryDept", "合计");
        summaryElse.put("category", "产品采购和预测(逻辑核)");
        summaryElse.put("subCategory", "短租(弹性)");
        summaryElse.put("value", ListUtils.isEmpty(list) ? null :
                NumberUtils.sum(ListUtils.filter(list, o -> Objects.equals(o.getDemandType(), "短租")),
                        o -> o.getCoreNum()));
        result.add(summaryElse);
        return result;
    }


    /**
     * 获取计费规模净增数据-月切片
     */
    private List<Map<String, Object>> fillBillingScaleData(List<String> yearMonths, String userName,
            IndustryReportOverviewReq req) {
        List<Map<String, Object>> result = Lang.list();
        Boolean isDistinct = req.getIsDistinct();
        List<String> statTimes = ListUtils.transform(yearMonths, o -> o += "-01");

        /* sql修改：
          外部 app_role=正常售卖/CDH/预扣包/GOCP/CDZ
          内部 剔除app_role 正常售卖/CDH/预扣包/GOCP/CDZ/LH
         */

        String sql = "";
        if (isDistinct != null && isDistinct) {
            //  执行去重先groupby后才能看出新增还是退回
            sql = ORMUtils.getSql("/sql/industry_report/overview/billing_scale_core_distinct_sum.sql");
        } else {
            sql = ORMUtils.getSql("/sql/industry_report/overview/billing_scale_core_sum.sql");
        }

        Map<String, Object> param = new HashMap<>();
        param.put("statTime", statTimes);
        WhereSQL where = new WhereSQL();

        if (req.getIsRendering() != null) {
            if (req.getIsRendering()) {
                where.and("(gins_family like 'RS%' or gins_family like 'RM%')");
            } else {
                where.and("gins_family not like 'RS%' and gins_family not like 'RM%'");
            }
        }

        //  产品筛选
        switch (req.getProductType()) {
            case "CVM":
                where.and("biz_type = 'cvm' and cpu_or_gpu = 'CPU'");
                //  只有CVM产品才有内部业务
                sql = sql.replace("${PRODUCT}", " ");
                sql = sql.replace("${TYPE}", " and diff_billcpu ");
                break;
            case "裸金属":
                where.and("biz_type = 'baremetal' and cpu_or_gpu = 'CPU'");
                sql = sql.replace("${PRODUCT}", " and 1 = 0");
                sql = sql.replace("${TYPE}", " and diff_billcpu ");
                break;
            case "GPU":
                where.and("cpu_or_gpu = 'GPU'");
                sql = sql.replace("${PRODUCT}", " and 1 = 0");
                sql = sql.replace("${TYPE}", " and diff_billgpu ");
                break;
        }

        //  内部业务不支持行业分类、appRole筛选
        sql = sql.replace("${INNER_FILTER}", whereToAnd(where.getSQL()));

        if (ListUtils.isNotEmpty(req.getIndustryDept())) {
            where.and("industry_category in (:industryCategory)");
            param.put("industryCategory", req.getIndustryDept());
        }
        if (ListUtils.isNotEmpty(req.getAppRole())) {
            where.and("app_role in (:appRole)");
            param.put("appRole", req.getAppRole());
        }

        sql = sql.replace("${FILTER}", whereToAnd(where.getSQL()));

        //  处理数据权限
        List<String> curUserDepts = getCurUserDepts(userName, Lang.list(req.getProductType()));
        if (ListUtils.isEmpty(curUserDepts)) {
            sql = sql.replace("${AUTH_FILTER}", " and industry_dept in (null) ");
        } else if (curUserDepts.size() == 1 && curUserDepts.contains("all")) {
            sql = sql.replace("${AUTH_FILTER}", " ");
        } else {
            sql = sql.replace("${AUTH_FILTER}", " and industry_dept in (:auth)");
            param.put("auth", curUserDepts);
        }

        if (((curUserDepts.size() == 1 && curUserDepts.contains("all")) ||
                (curUserDepts.contains("内部业务部"))) && (req.getIndustryDept().contains("内部业务") ||
                req.getIndustryDept().isEmpty())) {
            sql = sql.replace("${INNER_AUTH_FILTER}", " ");
        } else {
            sql = sql.replace("${INNER_AUTH_FILTER}", " and industry_dept in (null) ");
        }

        List<String> allInnerBizUin = getAllInnerBizUin();
        param.put("uin", allInnerBizUin);

        //  去重处理
        List<BillingScaleDTO> raw = Lang.list();
        if (isDistinct != null && isDistinct) {
            List<BillingScaleDistinctDTO> distinctList =
                    ckcldDBHelper.getRaw(BillingScaleDistinctDTO.class, sql, param);
            Map<String, List<BillingScaleDistinctDTO>> distinctMap =
                    ListUtils.groupBy(distinctList, o -> Strings.join("@", o.getStatTime(), o.getIndustryCategory()));
            for (Map.Entry<String, List<BillingScaleDistinctDTO>> entry : distinctMap.entrySet()) {
                List<BillingScaleDistinctDTO> value = entry.getValue();
                List<String> demandTypes = Lang.list("购买", "退回");
                for (String demandType : demandTypes) {
                    BillingScaleDTO dto = new BillingScaleDTO();
                    dto.setStatTime(value.get(0).getStatTime());
                    dto.setIndustryCategory(value.get(0).getIndustryCategory());
                    dto.setDemandType(demandType);
                    dto.setTotalCore(Objects.equals(demandType, "购买") ?
                            NumberUtils.sum(value, o -> o.getTotalCoreNew()) :
                            NumberUtils.sum(value, o -> o.getTotalCoreReturn()));
                    dto.setTotalGpuNum(Objects.equals(demandType, "购买") ?
                            NumberUtils.sum(value, o -> o.getTotalGpuNumNew()) :
                            NumberUtils.sum(value, o -> o.getTotalGpuNumReturn()));
                    raw.add(dto);
                }
            }
        } else {
            raw = ckcldDBHelper.getRaw(BillingScaleDTO.class, sql, param);
        }

        //  数据整合加工
        for (String yearMonth : yearMonths) {
            //  过滤出当月的数据
            List<BillingScaleDTO> curList = raw.stream().
                    filter(o -> Objects.equals(o.getStatTime(), yearMonth + "-01")).
                    collect(Collectors.toList());

            String yearMonthIndex = "y" + yearMonth.split("-")[0] + "m" + yearMonth.split("-")[1];

            //  再分组，根据行业分类 + 需求类型group by
            Map<String, List<BillingScaleDTO>> categoryMap =
                    ListUtils.groupBy(curList, o -> String.join("-", o.getDemandType(), o.getIndustryCategory()));

            List<BillingScaleDTO> newRaw = Lang.list();
            for (Map.Entry<String, List<BillingScaleDTO>> entry : categoryMap.entrySet()) {
                List<BillingScaleDTO> value = entry.getValue();
                BillingScaleDTO dto = new BillingScaleDTO();
                dto.setStatTime(value.get(0).getStatTime());
                dto.setDemandType(value.get(0).getDemandType());
                dto.setIndustryCategory(value.get(0).getIndustryCategory());
                dto.setTotalCore(NumberUtils.sum(value, o -> o.getTotalCore()));
                dto.setTotalGpuNum(NumberUtils.sum(value, o -> o.getTotalGpuNum()));
                dto.setTotal(Objects.equals(req.getProductType(), "GPU") ? dto.getTotalGpuNum() : dto.getTotalCore());
                newRaw.add(dto);
            }

            for (BillingScaleDTO dto : newRaw) {
                Map<String, Object> curMap = new HashMap<>();
                curMap.put("yearMonth", yearMonthIndex);
                curMap.put("industryDept", dto.getIndustryCategory());
                curMap.put("category", "规模净增");
                curMap.put("subCategory", "实际" + (Objects.equals("购买", dto.getDemandType()) ? "新增" : "退回"));
                curMap.put("value", dto.getTotal());
                result.add(curMap);
            }

            //  规模净增-合计
            Map<String, Object> summaryNew = new HashMap<>();
            summaryNew.put("yearMonth", yearMonthIndex);
            summaryNew.put("industryDept", "合计");
            summaryNew.put("category", "规模净增");
            summaryNew.put("subCategory", "实际新增");
            summaryNew.put("value", ListUtils.isEmpty(newRaw) ? null :
                    NumberUtils.sum(ListUtils.filter(newRaw, o -> Objects.equals(o.getDemandType(), "购买")),
                            o -> o.getTotal()));
            result.add(summaryNew);

            Map<String, Object> summaryReturn = new HashMap<>();
            summaryReturn.put("yearMonth", yearMonthIndex);
            summaryReturn.put("industryDept", "合计");
            summaryReturn.put("category", "规模净增");
            summaryReturn.put("subCategory", "实际退回");
            summaryReturn.put("value", ListUtils.isEmpty(newRaw) ? null :
                    NumberUtils.sum(ListUtils.filter(newRaw, o -> Objects.equals(o.getDemandType(), "退回")),
                            o -> o.getTotal()));
            result.add(summaryReturn);
        }
        return result;
    }

    @NotNull
    @HiSpeedCache(expireSecond = 5)
    private List<String> getAllInnerBizUin() {
        //  获取内部业务uin集合
        List<CloudDemandInnerBizUinConfigDO> list = yuntiDBHelper.getAll(CloudDemandInnerBizUinConfigDO.class);
        List<String> allInnerBizUin = list.stream().
                flatMap(o -> Arrays.stream(o.getUin().trim().split(";"))).collect(Collectors.toList());
        return allInnerBizUin;
    }

    /**
     * 检查用户的权限
     */
    private List<String> getCurUserDepts(String userName, List<String> product) {
        if ("no".equalsIgnoreCase(userName) || "UNKNOWN".equalsIgnoreCase(userName)) {
            return Lang.list("all");
        }
        List<IndustryDemandAuthDO> auths = pplCommonService.getAuthRole(userName);
        List<IndustryDemandAuthDO> adminAuth =
                ListUtils.filter(auths, o -> IndustryDemandAuthRoleEnum.ADMIN.getCode().equals(o.getRole()));
        if (!adminAuth.isEmpty()) {
            return Lang.list("all");
        }

        List<IndustryDemandAuthDO> authList = Lang.list();

        if (!ListUtils.isEmpty(product)) {
            for (String each : product) {
                switch (each) {
                    case "CVM":
                        authList.addAll(ListUtils.filter(auths, o -> o.getProduct() != null &&
                                (o.getProduct().contains("CVM") || o.getProduct().contains("CVM&CBS"))));
                        break;
                    case "GPU":
                        authList.addAll(ListUtils.filter(auths, o -> o.getProduct() != null &&
                                (o.getProduct().contains("GPU") || o.getProduct().contains("GPU(裸金属&CVM)"))));
                        break;
                    default:
                        authList.addAll(
                                ListUtils.filter(auths, o -> o.getProduct() != null && o.getProduct().contains(each)));
                }
            }
        } else {
            authList.addAll(ListUtils.filter(auths, o -> o.getProduct() != null &&
                    (o.getProduct().contains("CVM") || o.getProduct().contains("CVM&CBS"))));
        }

        // 整合所有角色的行业部门
        Set<String> industryDepts = new HashSet<>();
        for (IndustryDemandAuthDO auth : authList) {
            String industry = auth.getIndustry();
            if (StringTools.isNotBlank(industry)) {
                String[] strs = industry.split(";");
                for (String str : strs) {
                    if (StringTools.isNotBlank(str)) {
                        industryDepts.add(str);
                    }
                }
            }
        }
        List<IndustryDemandAuthDO> isInner = ListUtils.filter(auths, o -> o.getIndustry().contains("内部业务部"));
        if (ListUtils.isNotEmpty(isInner)) {
            industryDepts.add("内部业务部");
        }
        return new ArrayList<>(industryDepts);
    }

    /**
     * 获取两个年月之间的所有年月
     */
    private List<Tuple2<Integer, Integer>> getYearMonthList(Date start, Date end) {
        List<Tuple2<Integer, Integer>> list = Lang.list();
        while (!start.after(end)) {
            list.add(new Tuple2<>(DateUtils.getYear(start), DateUtils.getMonth(start)));
            start = DateUtils.addTime(start, Calendar.MONTH, 1);
        }
        return list;
    }

    /**
     * 获取两个年月之间的所有年月，转为yyyy-MM的List形式
     */
    private List<String> getYearMonthStrList(Date start, Date end) {
        List<Tuple2<Integer, Integer>> yearMonthList = getYearMonthList(start, end);
        List<String> result = Lang.list();
        for (Tuple2<Integer, Integer> tuple2 : yearMonthList) {
            result.add(toYearMonth(tuple2._1, tuple2._2));
        }
        return result;
    }

    /**
     * 转换为yyyy-MM的格式
     */
    private String toYearMonth(int year, int month) {
        return String.join("-", String.valueOf(year),
                month >= 10 ? String.valueOf(month) : "0" + month);
    }

    /**
     * 转换为yyyy-MM的格式
     */
    private String toYearMonth(Tuple2<Integer, Integer> tuple) {
        return String.join("-", String.valueOf(tuple._1),
                tuple._2 >= 10 ? String.valueOf(tuple._2) : "0" + tuple._2);
    }
}
