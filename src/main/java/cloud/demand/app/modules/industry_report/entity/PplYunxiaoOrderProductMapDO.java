package cloud.demand.app.modules.industry_report.entity;

import cloud.demand.app.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_yunxiao_order_product_map")
public class PplYunxiaoOrderProductMapDO extends BaseDO {

    /** 产品<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 云霄预约单分类<br/>Column: [order_category] */
    @Column(value = "order_category")
    private String orderCategory;

    /** 应用角色<br/>Column: [app_role] */
    @Column(value = "app_role")
    private String appRole;

}
