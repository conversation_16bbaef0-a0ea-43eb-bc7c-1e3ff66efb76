package cloud.demand.app.modules.industry_report.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;

@Table("ppl_item")
@Data
public class MRPTargetDO {

    @Column(value = "type")
    private String type;
    @Column(value = "industryOrDept")
    private String industryOrDept;
    @Column(value = "regionName")
    private String regionName;
    @Column(value = "instanceType")
    private String instanceType;
    @Column(value = "demandType")
    private String demandType;
    @Column(value = "year")
    private Integer year;
    @Column(value = "month")
    private Integer month;
    @Column(value = "coreNum")
    private BigDecimal coreNum;
    @Column(value = "gpuNum")
    private BigDecimal gpuNum;
}
