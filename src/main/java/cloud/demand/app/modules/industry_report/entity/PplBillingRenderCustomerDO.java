package cloud.demand.app.modules.industry_report.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * 月度规模净增表-渲染客户appid列表
 */
@Data
@ToString
@Table("ppl_billing_render_customer")
public class PplBillingRenderCustomerDO {

    /** id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 删除标记<br/>Column: [deleted] */
    @Column(value = "deleted")
    private Boolean deleted;

    @Column(value = "appid")
    private Long appid;

}
