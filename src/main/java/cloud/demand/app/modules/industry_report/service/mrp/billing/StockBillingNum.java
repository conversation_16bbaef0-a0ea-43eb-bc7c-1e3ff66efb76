package cloud.demand.app.modules.industry_report.service.mrp.billing;

import cloud.demand.app.modules.industry_report.entity.BillingScaleMRPBaseDO;
import cloud.demand.app.modules.industry_report.model.enums.IndustryMRPReportTargetEnum;
import cloud.demand.app.modules.industry_report.model.vo.MRPTargetVO;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.RecursiveTask;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class StockBillingNum extends BillingScaleBaseNum {

    @Override
    public IndustryMRPReportTargetEnum token() {
        return IndustryMRPReportTargetEnum.STOCK_BILLING_NUM;
    }

    @Override
    public Set<IndustryMRPReportTargetEnum> sonTargets() {
        return null;
    }

    @Override
    public RecursiveTask<Object> task(Map<String, Object> initContext) {
        StockBillingNum task = new StockBillingNum();
        task.init(initContext);
        return task;
    }

    @Override
    protected Object compute() {
        List<BillingScaleMRPBaseDO> billingScaleBaseNum = billingScaleBaseNum();
        // 存量跟新增退回无关，所以不用过滤
        // 结果转换成通用结果VO
        List<MRPTargetVO> mrpTargetVOS = new ArrayList<>();
        for (BillingScaleMRPBaseDO baseDO : billingScaleBaseNum) {
            MRPTargetVO vo = BillingScaleMRPBaseDO.mrpTargetVO(baseDO, true, null, true,
                    content.getProduct());
            mrpTargetVOS.add(vo);
        }
        addToResult(mrpTargetVOS);
        return mrpTargetVOS;
    }
}
