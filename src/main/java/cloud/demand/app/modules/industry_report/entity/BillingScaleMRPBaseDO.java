package cloud.demand.app.modules.industry_report.entity;

import cloud.demand.app.modules.industry_report.model.vo.MRPTargetVO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import org.springframework.util.CollectionUtils;

@Data
@Table("ppl_billing_scale_monthly")
public class BillingScaleMRPBaseDO {

    @Column(value = "type")
    private String type;
    @Column(value = "year")
    private Integer year;
    @Column(value = "month")
    private Integer month;
    @Column(value = "industryOrDept")
    private String industryOrDept;
    @Column(value = "regionName")
    private String regionName;
    @Column(value = "instanceType")
    private String instanceType;
    @Column(value = "increaseBillCoreNum")
    private Double increaseBillCoreNum;
    @Column(value = "increaseServeCoreNum")
    private Double increaseServeCoreNum;
    @Column(value = "returnBillCoreNum")
    private Double returnBillCoreNum;
    @Column(value = "returnServeCoreNum")
    private Double returnServeCoreNum;
    @Column(value = "stockBillCoreNum")
    private Double stockBillCoreNum;
    @Column(value = "stockServeCoreNum")
    private Double stockServeCoreNum;

    @Column(value = "increaseBillGpuNum")
    private Double increaseBillGpuNum;
    @Column(value = "increaseServeGpuNum")
    private Double increaseServeGpuNum;
    @Column(value = "returnBillGpuNum")
    private Double returnBillGpuNum;
    @Column(value = "returnServeGpuNum")
    private Double returnServeGpuNum;
    @Column(value = "stockBillGpuNum")
    private Double stockBillGpuNum;
    @Column(value = "stockServeGpuNum")
    private Double stockServeGpuNum;

    public String key(Integer year, Integer month) {
        return String.join("@", type, String.valueOf(year), String.valueOf(month), industryOrDept, regionName, instanceType);
    }

    public String key() {
        return key(year, month);
    }

    public static BillingScaleMRPBaseDO copy(BillingScaleMRPBaseDO thisDO) {
        BillingScaleMRPBaseDO billingScaleMRPBaseDO = new BillingScaleMRPBaseDO();
        billingScaleMRPBaseDO.setType(thisDO.getType());
        billingScaleMRPBaseDO.setYear(thisDO.getYear());
        billingScaleMRPBaseDO.setMonth(thisDO.getMonth());
        billingScaleMRPBaseDO.setIndustryOrDept(thisDO.getIndustryOrDept());
        billingScaleMRPBaseDO.setRegionName(thisDO.getRegionName());
        billingScaleMRPBaseDO.setInstanceType(thisDO.getInstanceType());
        billingScaleMRPBaseDO.setIncreaseBillCoreNum(thisDO.getIncreaseBillCoreNum());
        billingScaleMRPBaseDO.setIncreaseServeCoreNum(thisDO.getIncreaseServeCoreNum());
        billingScaleMRPBaseDO.setReturnBillCoreNum(thisDO.getReturnBillCoreNum());
        billingScaleMRPBaseDO.setReturnServeCoreNum(thisDO.getReturnServeCoreNum());
        billingScaleMRPBaseDO.setStockBillCoreNum(thisDO.getStockBillCoreNum());
        billingScaleMRPBaseDO.setStockServeCoreNum(thisDO.getStockServeCoreNum());
        billingScaleMRPBaseDO.setIncreaseBillGpuNum(thisDO.getIncreaseBillGpuNum());
        billingScaleMRPBaseDO.setIncreaseServeGpuNum(thisDO.getIncreaseServeGpuNum());
        billingScaleMRPBaseDO.setReturnBillGpuNum(thisDO.getReturnBillGpuNum());
        billingScaleMRPBaseDO.setReturnServeGpuNum(thisDO.getReturnServeGpuNum());
        billingScaleMRPBaseDO.setStockBillGpuNum(thisDO.getStockBillGpuNum());
        billingScaleMRPBaseDO.setStockServeGpuNum(thisDO.getStockServeGpuNum());
        return billingScaleMRPBaseDO;
    }

    public static MRPTargetVO mrpTargetVO(BillingScaleMRPBaseDO thisDO, boolean isBillingCore, Boolean hasNew, boolean isStock,
            String product) {
        MRPTargetVO mRPTargetVO = new MRPTargetVO();
        mRPTargetVO.setType(thisDO.getType());
        mRPTargetVO.setIndustryOrDept(thisDO.getIndustryOrDept());
        mRPTargetVO.setRegionName(thisDO.getRegionName());
        mRPTargetVO.setInstanceType(thisDO.getInstanceType());
        mRPTargetVO.setYear(thisDO.getYear());
        mRPTargetVO.setMonth(thisDO.getMonth().toString());
        if (isStock) {
            mRPTargetVO.setDemandType("新增");
            mRPTargetVO.setCoreNum(BigDecimal.valueOf(isBillingCore ? thisDO.getStockBillCoreNum() : thisDO.getStockServeCoreNum()));
            mRPTargetVO.setGpuNum(BigDecimal.valueOf(isBillingCore ? thisDO.getStockBillGpuNum() : thisDO.getStockServeGpuNum()));
        } else {
            if (hasNew) {
                mRPTargetVO.setDemandType("新增");
                mRPTargetVO.setCoreNum(BigDecimal.valueOf(
                        isBillingCore ? thisDO.getIncreaseBillCoreNum() : thisDO.getIncreaseServeCoreNum()));
                mRPTargetVO.setGpuNum(BigDecimal.valueOf(
                        isBillingCore ? thisDO.getIncreaseBillGpuNum() : thisDO.getIncreaseServeGpuNum()));
            } else {
                mRPTargetVO.setDemandType("退回");
                mRPTargetVO.setCoreNum(
                        BigDecimal.valueOf(isBillingCore ? thisDO.getReturnBillCoreNum() : thisDO.getReturnServeCoreNum()));
                mRPTargetVO.setGpuNum(
                        BigDecimal.valueOf(isBillingCore ? thisDO.getReturnBillGpuNum() : thisDO.getReturnServeGpuNum()));
            }
        }
        mRPTargetVO.setProduct(product);
        return mRPTargetVO;
    }

    public static List<MRPTargetVO> mrpTargetVOS(List<BillingScaleMRPBaseDO> thisDOS, boolean isBillingCore,
            boolean hasNew, boolean isStock, String product) {
        if (!CollectionUtils.isEmpty(thisDOS)) {
            List<MRPTargetVO> mrpTargetVOS = new ArrayList<>();
            for (BillingScaleMRPBaseDO thisDO : thisDOS) {
                MRPTargetVO mrpTargetVO = mrpTargetVO(thisDO, isBillingCore, hasNew, isStock, product);
                mrpTargetVOS.add(mrpTargetVO);
            }
            return mrpTargetVOS;
        }
        return null;
    }
}
