package cloud.demand.app.modules.industry_report.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("industry_report_appid_info_latest")
public class IndustryReportAppidInfoLatestWithoutJsonDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    @Column(value = "update_time", setTimeWhenUpdate = true, setTimeWhenInsert = true)
    private Date updateTime;

    @Column(value = "appid")
    private Long appid;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept", insertValueScript = "''")
    private String industryDept;

    /** 客户uin<br/>Column: [uin] */
    @Column(value = "uin")
    private Long uin;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name", insertValueScript = "''")
    private String customerName;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name", insertValueScript = "''")
    private String customerShortName;

    /** 客户类型，0个人1企业<br/>Column: [customer_type] */
    @Column(value = "customer_type")
    private Integer customerType;

    /** 主销售名<br/>Column: [business_manager] */
    @Column(value = "business_manager", insertValueScript = "''")
    private String businessManager;

    /** 主销售名的组织架构<br/>Column: [business_manager_oa_dept] */
    @Column(value = "business_manager_oa_dept", insertValueScript = "''")
    private String businessManagerOaDept;

    /** 主销售名的组织架构path<br/>Column: [business_manager_oa_path] */
    @Column(value = "business_manager_oa_path", insertValueScript = "''")
    private String businessManagerOaPath;

    /** uin类型，0内部1外部<br/>Column: [uin_type] */
    @Column(value = "uin_type")
    private Integer uinType;

    /** 战区<br/>Column: [war_zone] */
    @Column(value = "war_zone")
    private String warZone;

    /** 架构师rtx<br/>Column: [system_architect] */
    @Column(value = "system_architect")
    private String systemArchitect;

    /** 架构师的组织架构<br/>Column: [system_architect_oa_dept] */
    @Column(value = "system_architect_oa_dept")
    private String systemArchitectOaDept;

    /** 架构师的组织架构path<br/>Column: [system_architect_oa_path] */
    @Column(value = "system_architect_oa_path")
    private String systemArchitectOaPath;

    public IndustryReportAppidInfoDO transform(){
        IndustryReportAppidInfoDO industryReportAppidInfoDO = new IndustryReportAppidInfoDO();
        industryReportAppidInfoDO.setAppid(this.appid);
        industryReportAppidInfoDO.setIndustryDept(this.industryDept);
        industryReportAppidInfoDO.setUin(this.uin);
        industryReportAppidInfoDO.setCustomerName(this.customerName);
        industryReportAppidInfoDO.setCustomerShortName(this.customerShortName);
        industryReportAppidInfoDO.setCustomerType(this.customerType);
        industryReportAppidInfoDO.setBusinessManager(this.businessManager);
        industryReportAppidInfoDO.setBusinessManagerOaDept(this.businessManagerOaDept);
        industryReportAppidInfoDO.setBusinessManagerOaPath(this.businessManagerOaPath);
        industryReportAppidInfoDO.setUinType(this.uinType);
        industryReportAppidInfoDO.setWarZone(this.warZone);
        industryReportAppidInfoDO.setSystemArchitect(this.systemArchitect);
        industryReportAppidInfoDO.setSystemArchitectOaDept(this.systemArchitectOaDept);
        industryReportAppidInfoDO.setSystemArchitectOaPath(this.systemArchitectOaPath);
        return industryReportAppidInfoDO;
    }

}
