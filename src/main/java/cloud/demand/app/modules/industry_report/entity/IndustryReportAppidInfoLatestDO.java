package cloud.demand.app.modules.industry_report.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 优先使用不带JSON的版本
 * @see IndustryReportAppidInfoLatestWithoutJsonDO
 */

@Data
@ToString
@Table("industry_report_appid_info_latest")
public class IndustryReportAppidInfoLatestDO extends IndustryReportAppidInfoLatestWithoutJsonDO{

    /** 完整的用户信息json<br/>Column: [full_info_json] */
    @Column(value = "full_info_json")
    private String fullInfoJson;

    @Column(value = "inner_info_op_product", computed = "TRIM(BOTH '\"' FROM JSON_EXTRACT(full_info_json, '$.decodeDataMap.inner_info.op_product'))")
    private String innerInfoOpProduct;

    public IndustryReportAppidInfoDO transform(){
        IndustryReportAppidInfoDO industryReportAppidInfoDO = new IndustryReportAppidInfoDO();
        industryReportAppidInfoDO.setAppid(this.getAppid());
        industryReportAppidInfoDO.setIndustryDept(this.getIndustryDept());
        industryReportAppidInfoDO.setUin(this.getUin());
        industryReportAppidInfoDO.setCustomerName(this.getCustomerName());
        industryReportAppidInfoDO.setCustomerShortName(this.getCustomerShortName());
        industryReportAppidInfoDO.setCustomerType(this.getCustomerType());
        industryReportAppidInfoDO.setBusinessManager(this.getBusinessManager());
        industryReportAppidInfoDO.setBusinessManagerOaDept(this.getBusinessManagerOaDept());
        industryReportAppidInfoDO.setBusinessManagerOaPath(this.getBusinessManagerOaPath());
        industryReportAppidInfoDO.setUinType(this.getUinType());
        industryReportAppidInfoDO.setWarZone(this.getWarZone());
        industryReportAppidInfoDO.setSystemArchitect(this.getSystemArchitect());
        industryReportAppidInfoDO.setSystemArchitectOaDept(this.getSystemArchitectOaDept());
        industryReportAppidInfoDO.setSystemArchitectOaPath(this.getSystemArchitectOaPath());
        industryReportAppidInfoDO.setFullInfoJson(this.fullInfoJson);
        return industryReportAppidInfoDO;
    }

}
