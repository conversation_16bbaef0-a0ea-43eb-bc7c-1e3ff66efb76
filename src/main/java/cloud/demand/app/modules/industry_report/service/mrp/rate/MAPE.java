package cloud.demand.app.modules.industry_report.service.mrp.rate;

import cloud.demand.app.modules.industry_report.model.dto.QueryIndustryMRPReportDAGContent;
import cloud.demand.app.modules.industry_report.model.enums.IndustryMRPReportTargetEnum;
import cloud.demand.app.modules.industry_report.service.mrp.AvgForecastNum;
import cloud.demand.app.modules.industry_report.service.mrp.BenchForecastNum;
import cloud.demand.app.modules.industry_report.service.mrp.LatestForecastNum;
import cloud.demand.app.modules.industry_report.service.mrp.TargetFetcher;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ForkJoinTask;
import java.util.concurrent.RecursiveTask;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class MAPE extends BaseRate {

    @Override
    public IndustryMRPReportTargetEnum token() {
        return IndustryMRPReportTargetEnum.MAPE;
    }

    @Override
    public Set<IndustryMRPReportTargetEnum> sonTargets() {
        return null;
    }

    @Override
    public Set<IndustryMRPReportTargetEnum> sonTargets(String version) {
        // 预测准确率(不限定版本） = 1-abs（预测-执行）/新增执行
        Set<IndustryMRPReportTargetEnum> result = new HashSet<>();
        switch (version) {
            case "最新版":
                result.add(IndustryMRPReportTargetEnum.LATEST_FORECAST_NUM);
                break;
            case "基准版":
                result.add(IndustryMRPReportTargetEnum.BENCH_FORECAST_NUM);
                break;
            case "532版":
                result.add(IndustryMRPReportTargetEnum.AVG_FORECAST_NUM);
                break;
        }
        // 1、执行量取值规则：内部取服务核，外部取计费核（内部不进行收费、外部用户计费）；
        // 所以它的子指标有4项
        result.add(IndustryMRPReportTargetEnum.CHARGING_INCREASE_NUM);
        result.add(IndustryMRPReportTargetEnum.CHARGING_RETURN_NUM);
        result.add(IndustryMRPReportTargetEnum.SERVED_INCREASE_NUM);
        result.add(IndustryMRPReportTargetEnum.SERVED_RETURN_NUM);
        return result;
    }

    @Override
    public RecursiveTask<Object> task(Map<String, Object> initContext) {
        MAPE task = new MAPE();
        task.init(initContext);
        return task;
    }

    public ForkJoinTask<Object> forecastNumF(List<String> newDemandTypes) {
        TargetFetcher thisTaskSonTask = null;
        TargetFetcher userChooseTask = null;
        switch (content.getVersion()) {
            case "最新版":
                userChooseTask = new LatestForecastNum();
                thisTaskSonTask = new LatestForecastNum();
                break;
            case "基准版":
                userChooseTask = new BenchForecastNum();
                thisTaskSonTask = new BenchForecastNum();
                break;
            case "532版":
                userChooseTask = new AvgForecastNum();
                thisTaskSonTask = new AvgForecastNum();
                break;
        }
        // 如果用户勾选了预测量，由于筛选条件不一样，所以公式的预测量结果不能覆盖用户的预测量结果
        if (!content.getOriginQueryTargetEnum().contains(IndustryMRPReportTargetEnum.FORECAST_NUM)) {
            // 如果用户没勾选，不要生成用户任务
            userChooseTask = null;
        }
        if (userChooseTask != null) {
            // 用户勾选的预测量结果，按原筛选条件执行
            userChooseTask.init(this.initContext);
            userChooseTask.fork();
        }
        // 公式里要求的最新预测量，筛选条件有一点不一样，所以复制一个initContext出来，然后修改筛选项
        thisTaskSonTask.setAddToResult(false);
        Map<String, Object> copyInitContext = copyInitContext();
        QueryIndustryMRPReportDAGContent originContent = (QueryIndustryMRPReportDAGContent) copyInitContext.get(
                "content");
        QueryIndustryMRPReportDAGContent newContent = QueryIndustryMRPReportDAGContent.copy(originContent);
        newContent.setDemandTypes(newDemandTypes);
        thisTaskSonTask.init(copyInitContext);
        // 查询
        return thisTaskSonTask.fork();
    }

    @Override
    public BigDecimal tidyResult(BigDecimal result) {
        BigDecimal max = BigDecimal.valueOf(3);
        if (result.compareTo(max) > 0) {
            return max;
        } else if (result.compareTo(BigDecimal.ZERO) < 0) {
            return BigDecimal.ZERO;
        } else {
            return result;
        }
    }

    @Override
    public BigDecimal computeRate(BigDecimal forecastNum, BigDecimal executedNum) {
        if (executedNum.compareTo(BigDecimal.ZERO) == 0) {
            if (forecastNum.compareTo(BigDecimal.ZERO) == 0) {
                return BigDecimal.ZERO;
            } else {
                return BigDecimal.valueOf(3);
            }
        }
        Double r = Math.abs(forecastNum.subtract(executedNum).divide(executedNum, 4, RoundingMode.HALF_UP).doubleValue());
        if (r.compareTo(0.0) < 0) {
            return BigDecimal.ZERO;
        } else if (r.compareTo(3.0) > 0) {
            return BigDecimal.valueOf(3);
        } else {
            return BigDecimal.valueOf(r);
        }
    }
}
