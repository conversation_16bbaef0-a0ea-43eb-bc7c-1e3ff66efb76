package cloud.demand.app.modules.rrp_remake.model;

import cloud.demand.app.modules.rrp_remake.entity.ProductW13DemandWaveRemarkDO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class InputRemarkDetail {

    /**
     * 流程id<br/>Column: [flow_id]
     */
    @NotNull(message = "流程id不能为空")
    private Integer flowId;

    /**
     * 规划产品<br/>Column: [product]
     */
    private String product;

    /**
     * obs项目类型<br/>Column: [obs_project_type]
     */
    private String obsProjectType;

    /**
     * module业务类型<br/>Column: [mod_business_type_name]
     */
    private String modBusinessTypeName;

    /**
     * region<br/>Column: [country]
     */
    private String country;

    /**
     * region<br/>Column: [region]
     */
    private String region;

    /**
     * zone<br/>Column: [zone]
     */
    private String zone;

    /**
     * campus<br/>Column: [campus]
     */
    private String campus;

    /**
     * 设备类型<br/>Column: [device_type]
     */
    private String deviceType;

    /**
     * 项目类型<br/>Column: [proj_set_name]
     */
    private String projSetName;

    /**
     * 客户<br/>Column: [customer_name]
     */
    private String customerName;

    /**
     * 产品备注<br/>Column: [product_remark]
     */
    private String productRemark;

    /**
     * 资源组备注<br/>Column: [resource_remark]
     */
    private String resourceRemark;

    /**
     * 行业<br/>Column: [industry]
     */
    private String industry;

    /**
     * 原因归类<br/>Column: [reason_type]
     */
    private String reasonType;

    /**
     * 原因细项<br/>Column: [reason]
     */
    private String reason;

    /**
     * 需求类型<br/>Column: [demand_type]
     */
    private String demandType;

    /**
     * 需求来源<br/>Column: [source]
     */
    private String source;

    /**
     * 备注<br/>Column: [remark]
     */
    @NotBlank(message = "备注不能为空")
    private String remark;

    public static ProductW13DemandWaveRemarkDO copy(InputRemarkDetail detail) {
        ProductW13DemandWaveRemarkDO productW13DemandWaveRemarkDO = new ProductW13DemandWaveRemarkDO();
        productW13DemandWaveRemarkDO.setFlowId(detail.getFlowId());
        productW13DemandWaveRemarkDO.setProduct(detail.getProduct());
        productW13DemandWaveRemarkDO.setObsProjectType(detail.getObsProjectType());
        productW13DemandWaveRemarkDO.setModBusinessTypeName(detail.getModBusinessTypeName());
        productW13DemandWaveRemarkDO.setCountry(detail.getCountry());
        productW13DemandWaveRemarkDO.setRegion(detail.getRegion());
        productW13DemandWaveRemarkDO.setZone(detail.getZone());
        productW13DemandWaveRemarkDO.setCampus(detail.getCampus());
        productW13DemandWaveRemarkDO.setDeviceType(detail.getDeviceType());
        productW13DemandWaveRemarkDO.setProjSetName(detail.getProjSetName());
        productW13DemandWaveRemarkDO.setCustomerName(detail.getCustomerName());
        productW13DemandWaveRemarkDO.setProductRemark(detail.getProductRemark());
        productW13DemandWaveRemarkDO.setResourceRemark(detail.getResourceRemark());
        productW13DemandWaveRemarkDO.setIndustry(detail.getIndustry());
        productW13DemandWaveRemarkDO.setReasonType(detail.getReasonType());
        productW13DemandWaveRemarkDO.setReason(detail.getReason());
        productW13DemandWaveRemarkDO.setDemandType(detail.getDemandType());
        productW13DemandWaveRemarkDO.setSource(detail.getSource());
        productW13DemandWaveRemarkDO.setRemark(detail.getRemark());
        return productW13DemandWaveRemarkDO;
    }
}
