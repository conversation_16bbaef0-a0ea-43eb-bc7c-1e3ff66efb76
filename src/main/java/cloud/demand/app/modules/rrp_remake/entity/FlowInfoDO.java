package cloud.demand.app.modules.rrp_remake.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("flow_info")
public class FlowInfoDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * 流程提单人<br/>Column: [flowCreator]
     */
    @Column(value = "flowCreator", insertValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()")
    private String flowCreator;

    /**
     * 流程当前节点 - ID 形式<br/>Column: [currentStep]
     */
    @Column(value = "currentStep")
    private Integer currentStep;

    /**
     * 流程第一次开始时间，等同于 create_time，这个字段是为了兼容用<br/>Column: [startTime]
     */
    @Column(value = "startTime", setTimeWhenInsert = true)
    private Date startTime;

    /**
     * 流程成功结束时间<br/>Column: [endTime]
     */
    @Column(value = "endTime")
    private Date endTime;

    /**
     * 流程执行次数，大于 1 的情况下，代表流程驳回后又重新开始流程<br/>Column: [exec_time]
     */
    @Column(value = "exec_time")
    private Integer execTime;

    /**
     * 当前审批人（无用字段，不要再维护）<br/>Column: [approver]
     */
    @Column(value = "approver")
     private String approver;

    /**
     * 最近更新时间，等同于 update_time，这个字段是为了兼容用<br/>Column: [updateTime]
     */
    @Column(value = "updateTime", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date oldUpdateTime;

    /**
     * 单据代表的规划产品<br/>Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * 单据所属的版本 ID<br/>Column: [rrp_config_id]
     */
    @Column(value = "rrp_config_id")
    private Long rrpConfigId;

    /**
     * 最近一次更新人<br/>Column: [updator]
     */
    @Column(value = "updator", insertValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()",
            updateValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()")
    private String updator;

    /**
     * DB 的创建时间<br/>Column: [create_time]
     */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /**
     * DB 的更新时间<br/>Column: [update_time]
     */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

}