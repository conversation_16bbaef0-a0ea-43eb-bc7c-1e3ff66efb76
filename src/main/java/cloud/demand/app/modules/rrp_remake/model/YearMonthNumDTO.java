package cloud.demand.app.modules.rrp_remake.model;

import lombok.Data;

@Data
public class YearMonthNumDTO {

    private int year;
    private int month;
    private int week;
    private Integer num;
    private Integer curplanAdjust;
    private String chineseName;
    private String standardName;

    public YearMonthNumDTO(int year, int month, int week, Integer num, Integer curplanAdjust) {
        this.year = year;
        this.month = month;
        this.week = week;
        this.num = num;
        this.curplanAdjust = curplanAdjust;
        this.chineseName = year + "年" + month + "月【" + week +"周次】";
        this.standardName = year + "-" + (month < 10 ? "0" : "") + month;
    }
}
