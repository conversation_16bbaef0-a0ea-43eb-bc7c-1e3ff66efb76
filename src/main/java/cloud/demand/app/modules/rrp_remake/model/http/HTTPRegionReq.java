package cloud.demand.app.modules.rrp_remake.model.http;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class HTTPRegionReq extends HTTPBusinessReq {

    @NotBlank(message = "业务类型不能为空")
    private String mod_business_type_name;

    public static HTTPRegionReq transform(HTTP2XYReq dto) {
        HTTPRegionReq result = new HTTPRegionReq();
        result.setPlan_product(dto.getPlanProduct());
        result.setMod_business_type_name(dto.getModBusinessTypeName());
        return result;
    }

}
