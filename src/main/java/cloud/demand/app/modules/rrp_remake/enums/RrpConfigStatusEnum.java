package cloud.demand.app.modules.rrp_remake.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 版本是否开启，0是1否
 */
@Getter
public enum RrpConfigStatusEnum {

    OPEN(0, "开启"),

    CLOSE(1, "关闭");

    final private Integer code;
    final private String name;

    RrpConfigStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static RrpConfigStatusEnum getByCode(Integer code) {
        for (RrpConfigStatusEnum e : RrpConfigStatusEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        RrpConfigStatusEnum e = getByCode(code);
        return e == null ? null : e.getName();
    }
}
