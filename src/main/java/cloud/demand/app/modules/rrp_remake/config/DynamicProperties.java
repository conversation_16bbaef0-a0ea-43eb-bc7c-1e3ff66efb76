package cloud.demand.app.modules.rrp_remake.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Set;
import java.util.function.Supplier;
import yunti.boot.config.DynamicProperty;

/**
 * 动态配置，七彩石上更改后实时生效
 */
public class DynamicProperties {
    private static final Supplier<String> defaultApprovers =
            DynamicProperty.create("app.config.rrp-product-13w.default-approvers", "");

    public static String defaultApprovers() {
        return defaultApprovers.get();
    }
}
