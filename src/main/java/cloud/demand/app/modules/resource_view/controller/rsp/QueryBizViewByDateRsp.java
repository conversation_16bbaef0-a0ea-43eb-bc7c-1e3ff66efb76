package cloud.demand.app.modules.resource_view.controller.rsp;


import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryBizViewByDateRsp {

    List<String> date;

    List<Data> details;

    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Data {

        String name;
        List<BigDecimal> data;
        BigDecimal totalNum = BigDecimal.ZERO;
        BigDecimal averageNum = BigDecimal.ZERO;
        BigDecimal endNum = BigDecimal.ZERO;
    }

}
