package cloud.demand.app.modules.resource_view.controller.rsp;


import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuerySummaryRsp {

    List<Data> details;

    BigDecimal totalAverageServers = BigDecimal.ZERO;

    BigDecimal totalAverageCsigServers= BigDecimal.ZERO;

    BigDecimal totalAverageCsigYuntiServers= BigDecimal.ZERO;


    @lombok.Data
    public static class Data {

        String date;

        String updateTime;

        BigDecimal totalServers = BigDecimal.ZERO;

        BigDecimal totalCsigServers= BigDecimal.ZERO;

        BigDecimal totalCsigYuntiServers= BigDecimal.ZERO;
    }

}
