package cloud.demand.app.modules.resource_view.model.export_detail;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Table("pod_version_info")
public class PodExportDetailDO {

    @Column(value = "version_date")
    private String day;

    /**
     * 事业群名称<br/>Column: [bg_name]
     */
    @Column(value = "bg_name")
    private String bgName;

    /**
     * 部门名称<br/>Column: [dept_name]
     */
    @Column(value = "dept_name")
    private String deptName;

    /**
     * 规划产品<br/>Column: [plan_product_name]
     */
    @Column(value = "plan_product_name")
    private String planProductName;

    @Column(value = "pod_compute_type")
    private String computeType;

    @Column(value = "area_type")
    private String areaType;
    @Column(value = "region_name")
    private String regionName;
    @Column(value = "zone_name")
    private String zoneName;


    @Column(value = "instance_type")
    private String instanceModel;


//    @Column(value = "single_cpu_count", computed = "max(cpu_count)")
//    private BigDecimal singleCpuCount;
//    @Column(value = "single_ram", computed = " max(mem) ")
//    private BigDecimal singleRam;

    @Column(value = "all_cpu_num", computed = "sum(cpu_limit)")
    private BigDecimal allCpuNum;
    @Column(value = "all_ram", computed = "sum(mem_limit)")
    private BigDecimal allRam;
    @Column(value = "all_device_num", computed = "count(*)")
    private BigDecimal allDeviceNum;



}
