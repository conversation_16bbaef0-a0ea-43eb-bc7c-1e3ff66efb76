package cloud.demand.app.modules.resource_view.controller.req;


import java.util.List;
import lombok.Data;

@Data
public class QueryCategoryReq {

   /**日期*/
   private String date;

   /**
    *  接口改造， 传时间范围， 改造之后查询变成平均值
    *  如果兼容以前接口，如果传了 date 没有传 startDate, endDate
    *  则 startDate = endDate = date
    */
   private String startDate;
   private String endDate;

   /**
    * 1 2 3 分别对应 1 2 3 层
    */
   private Integer level;

   /**
    * 父节点的名称， 2 3 level 必传
    *
    * 【level1, level2】
    *
    */
   private List<String> parentCategory;

}
