package cloud.demand.app.modules.sop_util_v2.service.impl;

import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_review.dto.resp.BasSopReviewVersion;
import cloud.demand.app.modules.sop_review.service.SopReviewCommonService;
import cloud.demand.app.modules.sop_review.service.SopReviewDictService;
import cloud.demand.app.modules.sop_util_v2.dto.resp.VersionRespList;
import cloud.demand.app.modules.sop_util_v2.service.SopUtilV2DictService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class SopUtilV2DictServiceImpl implements SopUtilV2DictService {

    @Resource
    private SopReviewCommonService commonService;

    @Resource
    private SopReviewDictService dictService;

    @Resource
    private DBHelper ckcubesDBHelper;


    @Override
    public List<VersionRespList> getDemandVersionList() {
        List<BasSopReviewVersion> versionList = dictService.getVersionList();
        List<VersionRespList> ret = new ArrayList<>();
        versionList.forEach(item -> ret.add(new VersionRespList(SoeCommonUtils.getVersionCode(item.getVersion()),item.getVersion())));
        ret.sort((o1, o2) -> o2.getVersion().compareTo(o1.getVersion())); // 降序
        return ret;
    }

    @Override
    public List<VersionRespList> getVersionList() {
        Map<String, String> versionByDate = commonService.getVersionByDate();
        List<VersionRespList> ret = new ArrayList<>();
        versionByDate.forEach((k, v) -> ret.add(new VersionRespList(v,k)));
        ret.sort((o1, o2) -> o2.getVersion().compareTo(o1.getVersion())); // 降序
        return ret;
    }

    @Override
    public Map<String, String> getVersionByDate(Set<String> versionDate) {
        List<VersionDate> raw = ckcubesDBHelper.getRaw(VersionDate.class,"select data_date,max(hedging_version) as version from cubesbi_dw.sd_hedging2_executed_detail_result where data_date in (?) group by data_date", versionDate);
        Map<String, String> ret = new HashMap<>();
        for (VersionDate date : raw) {
            ret.put(date.getDataDate(), date.getVersion());
        }
        return ret;
    }

    @Data
    public static class VersionDate{
        @Column("data_date")
        private String dataDate;

        @Column("version")
        private String version;
    }
}
