package cloud.demand.app.modules.sop_util_v2.enums;

import cloud.demand.app.modules.sop_util.enums.IStoreNameEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/** sop 运营工具 2.1 数据仓库 */
@AllArgsConstructor
@Getter
public enum SopUtilV2StoreEnum implements IStoreNameEnum {
    // =============== init ================
    SOP_IMS_INIT("sop_ims_init","大盘进销存-初始化"),

    SOP_CLOUD_DEMAND_INIT("sop_cloud_demand_init","云需求-初始化"),

    SOP_IMS_DEVICE_INIT("sop_ims_device_init","大盘物理机采购-初始化"),

    SOP_GPU_INIT("sop_gpu_init","GPU采购-初始化"),

    // =============== 大盘进销存 ================
    SOP_IMS_RESOURCE("sop_ims_resource","资源存量"),
    SOP_IMS_FORECAST("sop_ims_forecast","需求预测"),
    SOP_IMS_DEMAND("sop_ims_demand","已申领量"),
    SOP_IMS_DELIVERY("sop_ims_delivery","已交付量"),
    SOP_IMS_RETURN("sop_ims_return","已退回量"),


    // =============== 大盘物理机采购汇总 ================
    SOP_MIS_DEVICE_PURCHASE("sop_mis_device_purchase","采购总量"),
    SOP_MIS_DEVICE_ORDER_PLACED("sop_mis_device_order_placed","已下单量"),
    SOP_MIS_DEVICE_FORECAST_NET("sop_mis_device_forecast_net","净预测量"),
    SOP_MIS_DEVICE_HOST_DELIVERY("sop_mis_device_host_delivery","已交付量"),


    // =============== GPU采购交付汇总 ================

    // 复用 大盘物理机采购汇总的 store

    // =============== 云需求 ================
    SOP_CLOUD_DEMAND_BILL_SCALE_STOCK("sop_cloud_demand_bill_scale_stock","计费规模存量"),
    SOP_CLOUD_DEMAND_BILL_SCALE_CHANGE("sop_cloud_demand_bill_scale_change","计费规模变化量"),
    SOP_CLOUD_DEMAND_FORECAST("sop_cloud_demand_forecast","ppl预测"),

    SOP_CLOUD_DEMAND_PURCHASE("sop_cloud_demand_purchase","已采购量"),

    SOP_CLOUD_DEMAND_DELIVERY("sop_cloud_demand_delivery","已交付量")

    ;

    private final String name;
    private final String desc;
}
