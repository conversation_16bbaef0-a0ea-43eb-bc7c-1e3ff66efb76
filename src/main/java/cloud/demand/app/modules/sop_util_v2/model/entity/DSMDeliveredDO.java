package cloud.demand.app.modules.sop_util_v2.model.entity;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/** dms 已交付量 */

@Data
public class DSMDeliveredDO {
    @Column("any_year_month")
    private String yearMonth;
    @Column("any_resource_type")
    private String resourceType;
    @Column("any_customhouse_title")
    private String customhouseTitle;
    @Column("any_country_name")
    private String countryName;
    @Column("any_plan_product_name")
    private String planProductName;
    @Column("any_dept_name")
    private String deptName;
    @Column("total_core_num")
    private BigDecimal totalCoreNum;
}
