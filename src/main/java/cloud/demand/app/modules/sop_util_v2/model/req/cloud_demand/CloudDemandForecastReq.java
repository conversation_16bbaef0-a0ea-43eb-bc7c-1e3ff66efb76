package cloud.demand.app.modules.sop_util_v2.model.req.cloud_demand;

import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.soe.model.sql.ColumnParam;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_util_v2.dto.req.SopCloudDemandReq;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import java.util.List;
import java.util.Map;


@Data
public class CloudDemandForecastReq {

    @ColumnParam("version_code")
    private String versionCode; // 版本号

    @ColumnParam("start_year_month")
    private String startYearMonth; // 起始年月

    @ColumnParam("version_start_year_month")
    private String versionStartYearMonth; // 版本号起始年月

    @ColumnParam("end_year_month")
    private String endYearMonth; // 结束年月

    @ColumnParam("demand_type")
    private List<String> demandType; // 是否为新增


    public static CloudDemandForecastReq transform(SopCloudDemandReq req, Map<String,String> yearMonthMap, boolean isStart, boolean isAdd) {
        CloudDemandForecastReq ret = new CloudDemandForecastReq();
        String versionDate = isStart?req.getStartDemandVersion():req.getEndDemandVersion();
        String versionCode = SoeCommonUtils.getVersionCode(versionDate);
        String versionStartYearMonth = yearMonthMap.get(versionCode);
        if (versionStartYearMonth == null){
            throw new IllegalArgumentException(String.format("版本号对应起始年月不存在：versionCode: 【%s】", versionCode));
        }
        // 新增 - NEW ， ELASTIC
        // 退回 - RETURN
        if (isAdd){
            ret.setDemandType(ListUtils.newArrayList(PplDemandTypeEnum.NEW.getCode(),PplDemandTypeEnum.ELASTIC.getCode()));
        }else {
            ret.setDemandType(ListUtils.newArrayList(PplDemandTypeEnum.RETURN.getCode()));
        }
        ret.setVersionCode(versionCode);
        ret.setVersionStartYearMonth(versionStartYearMonth);
        ret.setStartYearMonth(versionDate.substring(0,7)); // 取版本号所在年月为起始年月
        ret.setEndYearMonth(req.getEndYearMonth());
        return ret;
    }
}
