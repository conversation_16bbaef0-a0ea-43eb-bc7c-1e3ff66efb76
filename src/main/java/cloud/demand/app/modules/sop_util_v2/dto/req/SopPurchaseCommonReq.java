package cloud.demand.app.modules.sop_util_v2.dto.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/** 采购公共 req */
@Data
public class SopPurchaseCommonReq extends SopUtilV2CommonReq{
    // =============== 物理机版本 ================
    @NotEmpty(message = "物理机版本 不能为空")
    private String startDeviceVersion;
    @NotEmpty(message = "物理机版本 不能为空")
    private String endDeviceVersion;


    private List<String> countryName; // 国家

    private List<String> customhouseTitle; // 国内外

}
