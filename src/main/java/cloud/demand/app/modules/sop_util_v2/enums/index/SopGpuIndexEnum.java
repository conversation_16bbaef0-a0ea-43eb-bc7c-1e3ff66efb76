package cloud.demand.app.modules.sop_util_v2.enums.index;

import cloud.demand.app.modules.soe.model.compute.IComputeMethod;
import cloud.demand.app.modules.sop_util.enums.IStoreNameEnum;
import cloud.demand.app.modules.sop_util_v2.enums.Constant;
import cloud.demand.app.modules.sop_util_v2.enums.SopUtilV2StoreEnum;
import com.pugwoo.wooutils.collect.MapUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.function.Supplier;


@AllArgsConstructor
@Getter
public enum SopGpuIndexEnum implements ICommonIndexEnum {

    // =============== GPU采购交付汇总 ================
    // =============== 整体 ================
    PURCHASE_PRE_YEAR("'整体@' + req.shortPreYear + '年采购总量'", SopUtilV2StoreEnum.SOP_MIS_DEVICE_ORDER_PLACED, () -> MapUtils.of("is_gpu", true, "is_pre_year", true)),

    // =============== 开始值 ================
    ORDER_PLACED_START("'开始值@已下单'", SopUtilV2StoreEnum.SOP_MIS_DEVICE_ORDER_PLACED, () -> MapUtils.of("is_start", true, "is_gpu", true)),
    FORECAST_NET_START("'开始值@净预测量'", SopUtilV2StoreEnum.SOP_MIS_DEVICE_FORECAST_NET, () -> MapUtils.of("is_start", true, "is_gpu", true)),
    HOST_DELIVERY_START("'开始值@已交付量'", SopUtilV2StoreEnum.SOP_MIS_DEVICE_HOST_DELIVERY, () -> MapUtils.of("is_start", true, "is_gpu", true)),

    PURCHASE_CUR_YEAR_START("'开始值@' + req.shortYear + '年采购总量预估'", new SopGpuIndexEnum[]{ORDER_PLACED_START, FORECAST_NET_START}, Constant.add, -1),

    // =============== 结束值 ================
    ORDER_PLACED_END("'结束值@已下单'", SopUtilV2StoreEnum.SOP_MIS_DEVICE_ORDER_PLACED, () -> MapUtils.of("is_start", false, "is_gpu", true)),
    FORECAST_NET_END("'结束值@净预测量'", SopUtilV2StoreEnum.SOP_MIS_DEVICE_FORECAST_NET, () -> MapUtils.of("is_start", false, "is_gpu", true)),
    HOST_DELIVERY_END("'结束值@已交付量'", SopUtilV2StoreEnum.SOP_MIS_DEVICE_HOST_DELIVERY, () -> MapUtils.of("is_start", false, "is_gpu", true)),

    PURCHASE_CUR_YEAR_END("'结束值@' + req.shortYear + '年采购总量预估'", new SopGpuIndexEnum[]{ORDER_PLACED_END, FORECAST_NET_END}, Constant.add, -1),

    // =============== 整体 ================


    // =============== 环比波动量 ================
//    PURCHASE_CUR_YEAR_DIFF("环比波动量@24年采购总量预估",null,false),
//    PURCHASE_CUR_YEAR_RATE_DIFF("环比波动量@占比",null,false),
    PURCHASE_CUR_YEAR_DIFF("'环比波动量@' + req.shortYear + '年采购总量预估'", new SopGpuIndexEnum[]{PURCHASE_CUR_YEAR_END, PURCHASE_CUR_YEAR_START}, Constant.sub),
    ORDER_PLACED_DIFF("'环比波动量@已下单'", new SopGpuIndexEnum[]{ORDER_PLACED_END, ORDER_PLACED_START}, Constant.sub),
    FORECAST_NET_DIFF("'环比波动量@净预测量'", new SopGpuIndexEnum[]{FORECAST_NET_END, FORECAST_NET_START}, Constant.sub),
    HOST_DELIVERY_DIFF("'环比波动量@已交付量'", new SopGpuIndexEnum[]{HOST_DELIVERY_END, HOST_DELIVERY_START}, Constant.sub),

    // =============== 同比波动量 ================
    PURCHASE_CUR_YEAR_DIFF_YEAR("'同比波动量@同比波动量'", new SopGpuIndexEnum[]{PURCHASE_CUR_YEAR_END, PURCHASE_PRE_YEAR}, Constant.sub),

    PURCHASE_CUR_YEAR_RATE_DIFF_YEAR("'同比波动量@同比增长比率'", new SopGpuIndexEnum[]{PURCHASE_CUR_YEAR_DIFF_YEAR, PURCHASE_PRE_YEAR}, Constant.divide),
    ;

    private final String name; // 指标名称
    private final IStoreNameEnum storeName; // 指标仓库

    private final boolean isHide; // 是否隐藏

    private final Supplier<Map<String, Object>> indexParam; // 指标参数

    private final SopGpuIndexEnum[] depIndex; // 依赖指标

    private final IComputeMethod compute; // 指标计算

    private final int order; // 排序

    SopGpuIndexEnum(String name, SopGpuIndexEnum[] storeName, IComputeMethod compute, int order) {
        this(name, null, false, null, storeName, compute, order);
    }

    SopGpuIndexEnum(String name, SopGpuIndexEnum[] storeName, IComputeMethod compute) {
        this(name, null, false, null, storeName, compute, 0);
    }

    SopGpuIndexEnum(String name, IStoreNameEnum storeName) {
        this(name, storeName, false, null, null, null, 0);
    }

    SopGpuIndexEnum(String name, IStoreNameEnum storeName, boolean isHide) {
        this(name, storeName, isHide, null, null, null, 0);
    }

    SopGpuIndexEnum(String name, IStoreNameEnum storeName, Supplier<Map<String, Object>> indexMap) {
        this(name, storeName, false, indexMap, null, null, 0);
    }

}
