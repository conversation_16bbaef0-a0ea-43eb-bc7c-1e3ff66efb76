package cloud.demand.app.modules.sop_util_v2.process.item;

import cloud.demand.app.modules.soe.dto.item.BigDecimalItem;
import cloud.demand.app.modules.sop_util_v2.fields.SopIMSDeviceFields;
import cloud.demand.app.modules.sop_util_v2.model.clean.ICleanCpuInfo;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SopIMSDeviceItem extends SopPurchaseCommonItem implements SopIMSDeviceFields, ICleanCpuInfo {

    /** cpu平台 */
    private String cpuPlatform;

    /** cpu系列 */
    private String cpuType;

    /** 机型族 */
    private String deviceFamily;

    /** 核数 */
    private BigDecimal coreNum;

    @Override
    public BigDecimal getValue_() {
        return new BigDecimalItem(getNum(),new BigDecimal[]{coreNum});
    }

}
