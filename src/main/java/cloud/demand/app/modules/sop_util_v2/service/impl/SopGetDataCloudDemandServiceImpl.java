package cloud.demand.app.modules.sop_util_v2.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.soe.model.sql.ObjectSqlBuilder;
import cloud.demand.app.modules.sop_review.enums.fields.SopReviewFieldsEnum;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import cloud.demand.app.modules.sop_util_v2.enums.fields.SopCloudDemandFieldEnum;
import cloud.demand.app.modules.sop_util_v2.model.entity.BillScaleViewDO;
import cloud.demand.app.modules.sop_util_v2.model.entity.CloudDemandForecastDO;
import cloud.demand.app.modules.sop_util_v2.model.entity.CloudDemandSupplyDO;
import cloud.demand.app.modules.sop_util_v2.model.req.cloud_demand.CloudDemandForecastReq;
import cloud.demand.app.modules.sop_util_v2.model.req.cloud_demand.CloudDemandPurchaseReq;
import cloud.demand.app.modules.sop_util_v2.model.req.cloud_demand.CloudDemandScaleChangeReq;
import cloud.demand.app.modules.sop_util_v2.model.req.cloud_demand.CloudDemandScaleStockReq;
import cloud.demand.app.modules.sop_util_v2.service.SopGetDataCloudDemandService;
import com.pugwoo.dbhelper.DBHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SopGetDataCloudDemandServiceImpl implements SopGetDataCloudDemandService {

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Override
    public List<CloudDemandSupplyDO> getPurchaseData(CloudDemandPurchaseReq storeReq) {
        String sql = ORMUtils.getSql("/sql/sop_util_v2/cloud_demand/executed_supply_num.sql");
        ObjectSqlBuilder sqlBuilder = new ObjectSqlBuilder();
        sqlBuilder.addSqlParams(storeReq);
        sql = sqlBuilder.build(sql);
        return ckcldStdCrpDBHelper.getRaw(CloudDemandSupplyDO.class,sql);
    }

    @Override
    public List<BillScaleViewDO> getBillScaleStockData(CloudDemandScaleStockReq storeReq) {
        String sql = ORMUtils.getSql("/sql/sop_util_v2/cloud_demand/bill_scale_stock.sql");
        ObjectSqlBuilder sqlBuilder = new ObjectSqlBuilder();
        sqlBuilder.addSqlParams(storeReq);
        sql = SimpleSqlBuilder.buildDims(sql, SopCloudDemandFieldEnum.getFieldNames(), storeReq.getGroupBy());
        sql = sqlBuilder.build(sql);
        return ckcldStdCrpDBHelper.getRaw(BillScaleViewDO.class,sql);
    }

    @Override
    public List<BillScaleViewDO> getBillScaleChangeData(CloudDemandScaleChangeReq storeReq) {
        String sql = storeReq.getIsAdd()?
                ORMUtils.getSql("/sql/sop_util_v2/cloud_demand/bill_scale_add.sql"):
                ORMUtils.getSql("/sql/sop_util_v2/cloud_demand/bill_scale_return.sql");
        ObjectSqlBuilder sqlBuilder = new ObjectSqlBuilder();
        sqlBuilder.addSqlParams(storeReq);
        sql = SimpleSqlBuilder.buildDims(sql, SopCloudDemandFieldEnum.getFieldNames(), storeReq.getGroupBy());
        sql = sqlBuilder.build(sql);
        return ckcldStdCrpDBHelper.getRaw(BillScaleViewDO.class,sql);
    }

    @Override
    public List<CloudDemandSupplyDO> getHostDelivered(CloudDemandPurchaseReq storeReq) {
        String sql = ORMUtils.getSql("/sql/sop_util_v2/cloud_demand/cloud_demand_host_delivered_num.sql");
        ObjectSqlBuilder sqlBuilder = new ObjectSqlBuilder();
        sqlBuilder.addSqlParams(storeReq);
        sql = sqlBuilder.build(sql);
        return ckcldStdCrpDBHelper.getRaw(CloudDemandSupplyDO.class,sql);
    }

    @Override
    public List<CloudDemandForecastDO> getForecastData(CloudDemandForecastReq storeReq) {
        String sql = ORMUtils.getSql("/sql/sop_util_v2/cloud_demand/ppl_forecast.sql");
        ObjectSqlBuilder sqlBuilder = new ObjectSqlBuilder();
        sqlBuilder.addSqlParams(storeReq);
        sql = sqlBuilder.build(sql);
        return ckcldStdCrpDBHelper.getRaw(CloudDemandForecastDO.class,sql);
    }
}
