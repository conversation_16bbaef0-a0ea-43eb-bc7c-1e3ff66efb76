package cloud.demand.app.modules.sop_util_v2.dto.resp;

import cloud.demand.app.modules.soe.model.index.IndexTree;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_util_v2.dto.req.CurYearReq;
import cloud.demand.app.modules.sop_util_v2.enums.fields.SopCloudDemandFieldEnum;
import cloud.demand.app.modules.sop_util_v2.enums.fields.SopIMSDeviceAndGpuFieldEnum;
import cloud.demand.app.modules.sop_util_v2.enums.fields.SopIMSFieldEnum;
import cloud.demand.app.modules.sop_util_v2.enums.index.SopCloudDemandIndexEnum;
import cloud.demand.app.modules.sop_util_v2.enums.index.SopGpuIndexEnum;
import cloud.demand.app.modules.sop_util_v2.enums.index.SopIMSDeviceIndexEnum;
import cloud.demand.app.modules.sop_util_v2.enums.index.SopIMSIndexEnum;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/** 元数据返回值 */
@Data
public class SopUtilsMetaDataResp {

    /** 大盘进销存 指标树 */
    private List<IndexTree> imsIndexTrees;

    /** 云需求 指标树 */
    private List<IndexTree> cloudDemandIndexTrees;

    /** 大盘采购物理机 指标树 */
    private List<IndexTree> imsDeviceIndexTrees;

    /** gpu采购 指标树 */
    private List<IndexTree> gpuIndexTrees;

    /** 大盘进销存 dims */
    private LinkedHashMap<String,String> imsDims;

    /** 云需求 dims */
    private LinkedHashMap<String,String> cloudDemandDims;

    /** 大盘物理机采购 dims */
    private LinkedHashMap<String,String> imsDeviceDims;

    /** gpu采购 dims */
    private LinkedHashMap<String,String> gpuDims;

    public static SopUtilsMetaDataResp build(CurYearReq req) {
        SopUtilsMetaDataResp ret = new SopUtilsMetaDataResp();

        Map<String,Object> params = new HashMap<>();
        params.put("req",req);

        // 大盘进销存的指标
        ret.setImsIndexTrees(SoeCommonUtils.getTree(Arrays.stream(SopIMSIndexEnum.values()).collect(Collectors.toList()),params));

        // 云需求的指标
        ret.setCloudDemandIndexTrees(SoeCommonUtils.getTree(Arrays.stream(SopCloudDemandIndexEnum.values()).collect(Collectors.toList()),params));

        // 大盘物理机采购的指标
        ret.setImsDeviceIndexTrees(SoeCommonUtils.getTree(Arrays.stream(SopIMSDeviceIndexEnum.values()).collect(Collectors.toList()),params));

        // gpu采购的指标
        ret.setGpuIndexTrees(SoeCommonUtils.getTree(Arrays.stream(SopGpuIndexEnum.values()).collect(Collectors.toList()),params));

        // 大盘进销存的维度
        ret.setImsDims(Arrays.stream(SopIMSFieldEnum.values()).filter(SopIMSFieldEnum::isShowAble).collect(Collectors.toMap(
                SopIMSFieldEnum::name,
                SopIMSFieldEnum::getName,
                (k1, k2) -> k1, LinkedHashMap::new)));

        // 云需求的维度
        ret.setCloudDemandDims(Arrays.stream(SopCloudDemandFieldEnum.values()).filter(SopCloudDemandFieldEnum::isShowAble).collect(Collectors.toMap(
                SopCloudDemandFieldEnum::name,
                SopCloudDemandFieldEnum::getName,
                (k1, k2) -> k1, LinkedHashMap::new)));

        // 大盘物理机采购的维度
        ret.setImsDeviceDims(Arrays.stream(SopIMSDeviceAndGpuFieldEnum.values()).filter(SopIMSDeviceAndGpuFieldEnum::isShowAble).collect(Collectors.toMap(
                SopIMSDeviceAndGpuFieldEnum::name,
                SopIMSDeviceAndGpuFieldEnum::getName,
                (k1, k2) -> k1, LinkedHashMap::new)));

        // gpu采购的维度
        ret.setGpuDims(Arrays.stream(SopIMSDeviceAndGpuFieldEnum.values()).filter(SopIMSDeviceAndGpuFieldEnum::isShowAble).collect(Collectors.toMap(
                SopIMSDeviceAndGpuFieldEnum::name,
                SopIMSDeviceAndGpuFieldEnum::getName,
                (k1, k2) -> k1, LinkedHashMap::new)));

        return ret;
    }
}
