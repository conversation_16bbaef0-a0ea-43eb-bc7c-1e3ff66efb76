package cloud.demand.app.modules.sop_util_v2.web;

import cloud.demand.app.modules.sop_util_v2.dto.req.CurYearReq;
import cloud.demand.app.modules.sop_util_v2.dto.req.SopCloudDemandReq;
import cloud.demand.app.modules.sop_util_v2.dto.req.SopGpuReq;
import cloud.demand.app.modules.sop_util_v2.dto.req.SopIMSDeviceReq;
import cloud.demand.app.modules.sop_util_v2.dto.req.SopIMSReq;
import cloud.demand.app.modules.sop_util_v2.dto.resp.*;
import cloud.demand.app.modules.sop_util_v2.service.SopUtilV2ReportService;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;

/** sop 运营工具 2.1 版本 */
@JsonrpcController("/sop/util-v2/report")
public class SopUtilV2ReportController {
    @Resource
    private SopUtilV2ReportService service;

    @RequestMapping
    public SopUtilsMetaDataResp getMetaData(@JsonrpcParam @Valid CurYearReq req){
        return SopUtilsMetaDataResp.build(req);
    }

    /** 大盘进销存 */
    @RequestMapping
    public SopIMSResp queryIMS(@JsonrpcParam @Valid SopIMSReq req) {
        return service.queryIMS(req);
    }

    /** 大盘物理机采购汇总 */
    @RequestMapping
    public SopIMSDeviceResp queryIMSDevice(@JsonrpcParam @Valid SopIMSDeviceReq req) {
        return service.queryIMSDevice(req);
    }

    /** GPU采购交付汇总 */
    @RequestMapping
    public SopGpuResp queryGpu(@JsonrpcParam @Valid SopGpuReq req) {
        return service.queryGpu(req);
    }

    /** 云需求 */

    @RequestMapping
    public SopCloudDemandResp queryCloudDemand(@JsonrpcParam @Valid SopCloudDemandReq req) {
        return service.queryCloudDemand(req);
    }
}
