package cloud.demand.app.modules.erp_transfer_return.model;

import cloud.demand.app.entity.yunti.YuntiCvmServersDO;
import cloud.demand.app.entity.yunti.YuntiOrderCvmRelationDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

@Data
public class YuntiCvmServersVO extends YuntiCvmServersDO {

    @RelatedColumn(localColumn = "instance_id", remoteColumn = "instance_id")
    private YuntiOrderCvmRelationDO order;

    public String getApplyOrderNo() {
        return order == null ? "" : order.getOrderId();
    }

}
