package cloud.demand.app.modules.big_table.controller;

import cloud.demand.app.modules.big_table.dto.BigTableCvmQueryDTO;
import cloud.demand.app.modules.big_table.service.BigTableService;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.util.YuntiUtils;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@JsonrpcController("/api/big-table")
@Slf4j
public class BigTableController {

    @Resource
    BigTableService bigTableService;

    @RequestMapping
    public Object queryCvmData(@JsonrpcParam @Valid BigTableCvmQueryDTO dto, BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        return bigTableService.queryCvmData(dto);
    }
}
