package cloud.demand.app.modules.big_table.dto;

import cloud.demand.app.modules.sop.domain.report.QueryReportItemResp;
import cloud.demand.app.modules.sop.domain.report.QueryReportItemResp.IndexItem;
import java.math.BigDecimal;
import java.util.List;
import java.util.StringJoiner;
import lombok.Data;
import org.springframework.util.CollectionUtils;

@Data
public class BigTableCvmData {

    private String indexName;
    private String resPool;
    private String businessType;
    private String cpuPlatform;
    private String cvmGinsType;
    private String cvmGinsKingdom;
    private String customhouseTitle;
    private String cityName;
    private String txyZoneName;
    private String customBgName;
    private String bgName;
    private String deptName;
    private String planProductName;
    private String industryName;
    private String customerName;
    private BigDecimal startNum;
    private BigDecimal startCoreNum;
    private BigDecimal startGpuCardNum;
    private BigDecimal endNum;
    private BigDecimal endCoreNum;
    private BigDecimal endGpuCardNum;
    private BigDecimal changeNum;
    private BigDecimal changeCoreNum;
    private BigDecimal changeGpuCardNum;

    public static BigTableCvmData copy(QueryReportItemResp.Data d, String indexName,
            IndexItem indexItem) {
        BigTableCvmData bigTableCvmData = new BigTableCvmData();
        bigTableCvmData.setIndexName(indexName);
        bigTableCvmData.setResPool("自研上云");
        bigTableCvmData.setBusinessType(d.getBusinessType());
        bigTableCvmData.setCpuPlatform("(空值)");
        bigTableCvmData.setCvmGinsType(d.getCvmGinsType().equals("(空值)") ?
                "(空值)" : d.getCvmGinsType().replaceAll("[\\u4e00-\\u9fa5]", ""));
        bigTableCvmData.setCvmGinsKingdom(d.getCvmGinsKingdom());
        bigTableCvmData.setCustomhouseTitle(d.getCustomhouseTitle());
        bigTableCvmData.setCityName(d.getCityName());
        bigTableCvmData.setTxyZoneName(d.getTxyZoneName());
        bigTableCvmData.setCustomBgName(d.getCustomBgName());
        bigTableCvmData.setBgName(d.getBgName());
        bigTableCvmData.setDeptName(d.getDeptName());
        bigTableCvmData.setPlanProductName(d.getPlanProductName());
        bigTableCvmData.setIndustryName("(空值)");
        bigTableCvmData.setCustomerName("(空值)");
        bigTableCvmData.setStartNum(indexItem.getStartNum());
        bigTableCvmData.setStartCoreNum(indexItem.getStartCoreNum());
        bigTableCvmData.setEndNum(indexItem.getEndNum());
        bigTableCvmData.setEndCoreNum(indexItem.getEndCoreNum());
        bigTableCvmData.setChangeNum(indexItem.getDiffNum());
        bigTableCvmData.setChangeCoreNum(indexItem.getDiffCoreNum());
        return bigTableCvmData;
    }

    public static BigTableCvmData copy(BigTableCvmData o, List<String> rawDim) {
        BigTableCvmData bigTableCvmData = new BigTableCvmData();
        bigTableCvmData.setIndexName(o.getIndexName());

        if (!CollectionUtils.isEmpty(rawDim)) {
            rawDim.forEach(v -> {
                switch (v) {
                    case "res_pool":
                        bigTableCvmData.setResPool(o.getResPool());
                        break;
                    case "business_type":
                        bigTableCvmData.setBusinessType(o.getBusinessType());
                        break;
                    case "cpu_platform":
                        bigTableCvmData.setCpuPlatform(o.getCpuPlatform());
                        break;
                    case "custom_bg_name":
                        bigTableCvmData.setCustomBgName(o.getCustomBgName());
                        break;
                    case "cvm_gins_kingdom":
                        bigTableCvmData.setCvmGinsKingdom(o.getCvmGinsKingdom());
                        break;
                    case "cvm_gins_type":
                        bigTableCvmData.setCvmGinsType(o.getCvmGinsType());
                        break;
                    case "customhouse_title":
                        bigTableCvmData.setCustomhouseTitle(o.getCustomhouseTitle());
                        break;
                    case "city_name":
                        bigTableCvmData.setCityName(o.getCityName());
                        break;
                    case "txy_zone_name":
                        bigTableCvmData.setTxyZoneName(o.getTxyZoneName());
                        break;
                    case "bg_name":
                        bigTableCvmData.setBgName(o.getBgName());
                        break;
                    case "dept_name":
                        bigTableCvmData.setDeptName(o.getDeptName());
                        break;
                    case "plan_product_name":
                        bigTableCvmData.setPlanProductName(o.getPlanProductName());
                        break;
                    case "industry_name":
                        bigTableCvmData.setIndustryName(o.getIndustryName());
                        break;
                    case "customer_name":
                        bigTableCvmData.setCustomerName(o.getCustomerName());
                        break;
                }
            });
        }

        bigTableCvmData.setStartNum(o.getStartNum());
        bigTableCvmData.setStartCoreNum(o.getStartCoreNum());
        bigTableCvmData.setStartGpuCardNum(o.getStartGpuCardNum());
        bigTableCvmData.setEndNum(o.getEndNum());
        bigTableCvmData.setEndCoreNum(o.getEndCoreNum());
        bigTableCvmData.setEndGpuCardNum(o.getEndGpuCardNum());
        bigTableCvmData.setChangeNum(o.getChangeNum());
        bigTableCvmData.setChangeCoreNum(o.getChangeCoreNum());
        bigTableCvmData.setChangeGpuCardNum(o.getChangeGpuCardNum());
        return bigTableCvmData;
    }

    public static BigTableCvmData copy(BigTableCvmData o) {
        BigTableCvmData bigTableCvmData = new BigTableCvmData();
        bigTableCvmData.setIndexName(o.getIndexName());
        bigTableCvmData.setResPool(o.getResPool());
        bigTableCvmData.setBusinessType(o.getBusinessType());
        bigTableCvmData.setCpuPlatform(o.getCpuPlatform());
        bigTableCvmData.setCvmGinsType(o.getCvmGinsType());
        bigTableCvmData.setCvmGinsKingdom(o.getCvmGinsKingdom());
        bigTableCvmData.setCustomhouseTitle(o.getCustomhouseTitle());
        bigTableCvmData.setCityName(o.getCityName());
        bigTableCvmData.setTxyZoneName(o.getTxyZoneName());
        bigTableCvmData.setCustomBgName(o.getCustomBgName());
        bigTableCvmData.setBgName(o.getBgName());
        bigTableCvmData.setDeptName(o.getDeptName());
        bigTableCvmData.setPlanProductName(o.getPlanProductName());
        bigTableCvmData.setIndustryName(o.getIndustryName());
        bigTableCvmData.setCustomerName(o.getCustomerName());
        bigTableCvmData.setStartNum(o.getStartNum());
        bigTableCvmData.setStartCoreNum(o.getStartCoreNum());
        bigTableCvmData.setStartGpuCardNum(o.getStartGpuCardNum());
        bigTableCvmData.setEndNum(o.getEndNum());
        bigTableCvmData.setEndCoreNum(o.getEndCoreNum());
        bigTableCvmData.setEndGpuCardNum(o.getEndGpuCardNum());
        bigTableCvmData.setChangeNum(o.getChangeNum());
        bigTableCvmData.setChangeCoreNum(o.getChangeCoreNum());
        bigTableCvmData.setChangeGpuCardNum(o.getChangeGpuCardNum());
        return bigTableCvmData;
    }

    public static BigTableCvmData newMrpEmpty(String product) {
        BigTableCvmData bigTableCvmData = new BigTableCvmData();
        bigTableCvmData.setResPool("公有云");
        bigTableCvmData.setBusinessType("(空值)");
        bigTableCvmData.setCpuPlatform("(空值)");
        bigTableCvmData.setCvmGinsType("(空值)");
        bigTableCvmData.setCvmGinsKingdom("(空值)");
        bigTableCvmData.setCustomhouseTitle("(空值)");
        bigTableCvmData.setCityName("(空值)");
        bigTableCvmData.setTxyZoneName("(空值)");
        bigTableCvmData.setCustomBgName("CSIG云与智慧产业事业群");
        bigTableCvmData.setBgName("CSIG云与智慧产业事业群");
        bigTableCvmData.setDeptName(product.equals("CVM") ? "计算产品中心" : "异构计算产品中心");
        bigTableCvmData.setPlanProductName(product.equals("CVM") ? "腾讯云CVM" : "GPU云服务器CGS");
        bigTableCvmData.setIndustryName("(空值)");
        bigTableCvmData.setCustomerName("(空值)");
        return bigTableCvmData;
    }

    public static BigTableCvmData copyMrpAndSetValue(BigTableCvmData o, Object value, String product,
            boolean isEnd, String indexName) {
        BigTableCvmData bigTableCvmData = new BigTableCvmData();
        bigTableCvmData.setIndexName(indexName);
        bigTableCvmData.setResPool(o.getResPool());
        bigTableCvmData.setBusinessType(o.getBusinessType());
        bigTableCvmData.setCpuPlatform(o.getCpuPlatform());
        bigTableCvmData.setCvmGinsType(o.getCvmGinsType());
        bigTableCvmData.setCvmGinsKingdom(o.getCvmGinsKingdom());
        bigTableCvmData.setCustomhouseTitle(o.getCustomhouseTitle());
        bigTableCvmData.setCityName(o.getCityName());
        bigTableCvmData.setTxyZoneName(o.getTxyZoneName());
        bigTableCvmData.setCustomBgName(o.getCustomBgName());
        bigTableCvmData.setBgName(o.getBgName());
        bigTableCvmData.setDeptName(o.getDeptName());
        bigTableCvmData.setPlanProductName(o.getPlanProductName());
        bigTableCvmData.setIndustryName(o.getIndustryName());
        bigTableCvmData.setCustomerName(o.getCustomerName());

        BigDecimal num = value == null ? null
                : value instanceof BigDecimal ? (BigDecimal) value
                        : value instanceof Number ? new BigDecimal(value.toString()) : null;

        if (isEnd) {
            if (product.equals("CVM")) {
                bigTableCvmData.setEndCoreNum(num);
            } else {
                bigTableCvmData.setEndGpuCardNum(num);
            }
        } else {
            if (product.equals("CVM")) {
                bigTableCvmData.setStartCoreNum(num);
            } else {
                bigTableCvmData.setStartGpuCardNum(num);
            }
        }
        return bigTableCvmData;
    }

    public String withoutIndexKey(List<String> rawDim) {
        StringJoiner sj = new StringJoiner("@");
        if (!CollectionUtils.isEmpty(rawDim)) {
            rawDim.forEach(v -> {
                switch (v) {
                    case "res_pool":
                        sj.add(resPool);
                        break;
                    case "business_type":
                        sj.add(businessType);
                        break;
                    case "cpu_platform":
                        sj.add(cpuPlatform);
                        break;
                    case "custom_bg_name":
                        sj.add(customBgName);
                        break;
                    case "cvm_gins_kingdom":
                        sj.add(cvmGinsKingdom);
                        break;
                    case "cvm_gins_type":
                        sj.add(cvmGinsType);
                        break;
                    case "customhouse_title":
                        sj.add(customhouseTitle);
                        break;
                    case "city_name":
                        sj.add(cityName);
                        break;
                    case "txy_zone_name":
                        sj.add(txyZoneName);
                        break;
                    case "bg_name":
                        sj.add(bgName);
                        break;
                    case "dept_name":
                        sj.add(deptName);
                        break;
                    case "plan_product_name":
                        sj.add(planProductName);
                        break;
                    case "industry_name":
                        sj.add(industryName);
                        break;
                    case "customer_name":
                        sj.add(customerName);
                        break;
                }
            });
        }
        return sj.toString();
    }

    public String key(List<String> rawDim) {
        return indexName + withoutIndexKey(rawDim);
    }
}
