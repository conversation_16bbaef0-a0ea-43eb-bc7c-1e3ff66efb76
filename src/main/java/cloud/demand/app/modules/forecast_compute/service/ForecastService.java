package cloud.demand.app.modules.forecast_compute.service;

import cloud.demand.app.modules.forecast_compute.model.CreateTaskDTO;
import cloud.demand.app.modules.forecast_compute.model.TaskOutputDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ForecastService {

    /**
     * 创建任务
     *
     * @return task uuid
     */
    String createTask(CreateTaskDTO req);

    /**
     * 创建任务，一次创建多个任务，这些任务会在一个事务中完成
     *
     * @return task uuid，按输入的请求顺序返回创建的任务uuid
     */
    List<String> createTasks(List<CreateTaskDTO> reqs);

    /**
     * 设置任务的关联任务uuid
     */
    void setRelativeTask(String taskUuid, String relativeTaskUuid);

    /**
     * 查询任务运行结果
     *
     * @param taskUuid 任务uuid
     */
    TaskOutputDTO queryTaskOutput(String taskUuid);

}
