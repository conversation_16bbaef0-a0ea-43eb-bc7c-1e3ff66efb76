package cloud.demand.app.modules.framework.task_scheduled.factory;

import cloud.demand.app.modules.framework.task.anno.CrpTaskClient;
import cloud.demand.app.modules.framework.task.service.impl.CrpTaskFactoryServiceImpl;
import cloud.demand.app.modules.framework.task_scheduled.anno.CrpTaskScheduled;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronSequenceGenerator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

//@Component
//@DependsOn({"CrpTaskFactoryServiceImpl"})
public class CrpTaskScheduledFactory implements ApplicationContextAware, SchedulingConfigurer {

    private TaskScheduler scheduler = null;

    private ApplicationContext context = null;

    @Resource
    private CrpTaskFactoryServiceImpl crpTaskFactoryService;

    private void init(){
        // 等两个都初始化完成后，再执行
        if (scheduler == null || context == null){
            return;
        }
        // step1：获取所有带有@CrpTaskScheduled注解的类
        Map<String, Object> beansWithAnnotation = context.getBeansWithAnnotation(CrpTaskScheduled.class);
        for (Map.Entry<String, Object> entry : beansWithAnnotation.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            registerTask(value);
        }
    }

    private void registerTask(Object bean){
        CrpTaskScheduled scheduled = AopUtils.getTargetClass(bean).getAnnotation(CrpTaskScheduled.class);
        checkScheduled(scheduled);
        CrpTaskClient client = AopUtils.getTargetClass(bean).getAnnotation(CrpTaskClient.class);
        checkClient(client);
        doRegisterTask(bean,scheduled,client);
    }

    private void doRegisterTask(Object bean, CrpTaskScheduled scheduled, CrpTaskClient client) {

    }

    private void checkClient(CrpTaskClient client) {
        if (client == null){
            throw new IllegalArgumentException("@CrpTaskClient annotation is missing");
        }
    }

    private void checkScheduled(CrpTaskScheduled scheduled){
        String cron = scheduled.initCron();
        // cron 表达式校验
        if (!CronSequenceGenerator.isValidExpression(cron)) {
            throw new IllegalArgumentException("Invalid cron expression: " + cron);
        }
        long fixedRate = scheduled.fixedRate();
        if (fixedRate <= 0) {
            throw new IllegalArgumentException("Invalid fixedRate: " + fixedRate);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.context = applicationContext;
        init();
    }

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        this.scheduler = taskRegistrar.getScheduler();
        init();
    }
}
