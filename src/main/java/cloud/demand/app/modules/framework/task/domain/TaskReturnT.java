package cloud.demand.app.modules.framework.task.domain;

import cloud.demand.app.modules.framework.task.enums.TaskReturnTStatus;
import lombok.*;

/** 任务调度执行返回信息 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class TaskReturnT<T> {
    /**状态码: 0是成功; 1为失败; 其他状态码待补充*/
    private Integer status;

    /**返回结果体*/
    private T body;

    /**返回结果信息*/
    private String message;

    /** T为集合且分页情况下提供的总页数，这里非必填，使用SopHttpIteratorUtil时
     * 以返回的size和请求的size是否一致为最高优先级的判定是否分页结束的标准 */
    private Integer total;

    public static <T> TaskReturnT<T> ok(T t){
        return TaskReturnT.<T>builder().status(TaskReturnTStatus.SUCCESS.getStatus()).body(t).build();
    }

    public static <T> TaskReturnT<T> fail(String msg){
        return TaskReturnT.<T>builder().status(TaskReturnTStatus.FAIL.getStatus()).message(msg).body(null).build();
    }

    public static boolean isOk(TaskReturnT<?> returnT){
        return returnT != null && returnT.isOk();
    }

    public boolean isOk(){
        return status !=null && status.equals(TaskReturnTStatus.SUCCESS.getStatus());
    }
}
