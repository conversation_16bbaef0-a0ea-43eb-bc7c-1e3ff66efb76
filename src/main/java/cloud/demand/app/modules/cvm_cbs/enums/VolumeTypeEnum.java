package cloud.demand.app.modules.cvm_cbs.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum VolumeTypeEnum {
    PREMIUM("premium", "高性能云硬盘"),
    SSD("ssd", "SSD云硬盘"),
    ;

    private String code;
    private String name;

    public static VolumeTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (VolumeTypeEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }

    public static VolumeTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (VolumeTypeEnum value : values()) {
            if (Objects.equals(value.getName(), name)) {
                return value;
            }
        }
        return null;
    }
}
