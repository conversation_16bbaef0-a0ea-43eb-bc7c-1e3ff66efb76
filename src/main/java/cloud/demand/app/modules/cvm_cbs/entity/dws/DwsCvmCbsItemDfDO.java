package cloud.demand.app.modules.cvm_cbs.entity.dws;

import cloud.demand.app.modules.cvm_cbs.entity.vo.CvmCbsDsmDataVO;
import cloud.demand.app.modules.cvm_cbs.entity.vo.CvmCbsSopDataVO;
import cloud.demand.app.modules.cvm_cbs.entity.vo.CvmCbsYunTiDataVO;
import cloud.demand.app.modules.cvm_cbs.entity.vo.ICvmCbsGroupKey;
import cloud.demand.app.modules.cvm_cbs.enums.CvmCbsIndex;
import cloud.demand.app.modules.soe.model.clean.IRegionClean;
import cloud.demand.app.modules.sop.enums.Constant;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Objects;

@Data
@ToString
@Table("dws_cvm_cbs_item_df")
public class DwsCvmCbsItemDfDO implements IRegionClean, ICvmCbsGroupKey {

    /**
     * 分区键，代表数据版本<br/>Column: [version]
     */
    @Column(value = "version")
    private String version;

    /**
     * 指标<br/>Column: [index]
     */
    @Column(value = "index")
    private String index;

    /**
     * 需求年<br/>Column: [year]
     */
    @Column(value = "year")
    private Integer year;

    /**
     * 需求月份<br/>Column: [month]
     */
    @Column(value = "month")
    private Integer month;

    @Column(value = "year_month")
    private String yearMonth;

    /**
     * 境内/境外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 地域<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 可用区<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 园区<br/>Column: [campus_name]
     */
    @Column(value = "campus")
    private String campus;

    /**
     * 自定义BG名<br/>Column: [custom_bg_name]
     */
    @Column(value = "custom_bg_name")
    private String customBgName;

    /**
     * BG名<br/>Column: [bg_name]
     */
    @Column(value = "bg_name")
    private String bgName;


    /**
     * 部门名<br/>Column: [dept_name]
     */
    @Column(value = "dept_name")
    private String deptName;

    /**
     * 规划产品名<br/>Column: [plan_product_name]
     */
    @Column(value = "plan_product_name")
    private String planProductName;

    /**
     * 项目名称
     */
    @Column(value = "project_name")
    private String projectName;


    /**
     * CVM机型族<br/>Column: [cvm_instance_group]
     */
    @Column(value = "cvm_instance_group")
    private String cvmInstanceGroup;

    /**
     * CVM类型<br/>Column: [cvm_instance_type]
     */
    @Column(value = "cvm_instance_type")
    private String cvmInstanceType;

    /**
     * CVM规格<br/>Column: [cvm_instance_model]
     */
    @Column(value = "cvm_instance_type_eng")
    private String cvmInstanceTypeEng;

    /**
     * 母机机型<br/>Column: [host_type]
     */
    @Column(value = "host_type")
    private String hostType;

    /**
     * 核心数<br/>Column: [amount]
     */
    @Column(value = "amount")
    private BigDecimal amount;

    @Column(value = "unit")
    private String unit;

    /**
     * 云盘类型<br/>Column: [volume_type]
     */
    @Column(value = "volume_type")
    private String volumeType;

    /**
     * 实例io<br/>Column: [instance_io]
     */
    @Column(value = "instance_io")
    private Long instanceIo;

    /**
     * 实例io<br/>Column: [instance_io]
     */
    @Column(value = "instance_num")
    private BigDecimal instanceNum;

    public static DwsCvmCbsItemDfDO transform(CvmCbsDsmDataVO dataVO, String version, CvmCbsIndex index) {

        DwsCvmCbsItemDfDO item = new DwsCvmCbsItemDfDO();
        BeanUtils.copyProperties(dataVO, item);
        item.setVersion(version);
        item.setIndex(Objects.isNull(index) ? null : index.getCode());
        item.setBgName(StringUtils.isBlank(item.getBgName()) ? Constant.EMPTY_VALUE_STR : item.getBgName());
        item.setCustomBgName(StringUtils.isBlank(item.getCustomBgName()) ? Constant.EMPTY_VALUE_STR : item.getCustomBgName());
        item.setDeptName(StringUtils.isBlank(item.getDeptName()) ? Constant.EMPTY_VALUE_STR : item.getDeptName());
        item.setPlanProductName(StringUtils.isBlank(item.getPlanProductName()) ? Constant.EMPTY_VALUE_STR : item.getPlanProductName());
        item.setProjectName(StringUtils.isBlank(item.getProjectName()) ? Constant.EMPTY_VALUE_STR : item.getProjectName());
        item.setUnit("核");
        return item;
    }

    public static DwsCvmCbsItemDfDO transform(CvmCbsYunTiDataVO dataVO, String version, CvmCbsIndex cvmCbsIndex) {
        DwsCvmCbsItemDfDO item = new DwsCvmCbsItemDfDO();
        BeanUtils.copyProperties(dataVO, item);
        item.setVersion(version);
        item.setUnit("GB");
        item.setIndex(cvmCbsIndex.getCode());
        return item;
    }

    public static DwsCvmCbsItemDfDO transform(CvmCbsSopDataVO dataVO, String version, CvmCbsIndex cvmCbsIndex) {
        DwsCvmCbsItemDfDO item = new DwsCvmCbsItemDfDO();
        BeanUtils.copyProperties(dataVO, item);
        item.setVersion(version);
        item.setUnit("核");
        item.setIndex(cvmCbsIndex.getCode());

        return item;
    }

    @Override
    public String getCountryName() {
        return null;
    }

    @Override
    public String getAreaName() {
        return null;
    }

    @Override
    public void setAreaName(String areaName) {

    }

    @Override
    public void setCountryName(String countryName) {

    }
}