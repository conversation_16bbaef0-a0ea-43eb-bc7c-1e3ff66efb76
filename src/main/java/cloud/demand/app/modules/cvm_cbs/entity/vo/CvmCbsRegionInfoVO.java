package cloud.demand.app.modules.cvm_cbs.entity.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class CvmCbsRegionInfoVO {

    /**
     * 境内/境外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 地域<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 可用区<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

}
