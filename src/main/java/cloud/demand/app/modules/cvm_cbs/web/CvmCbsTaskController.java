package cloud.demand.app.modules.cvm_cbs.web;

import cloud.demand.app.modules.cvm_cbs.domain.req.CvmCbsDwsTaskReq;
import cloud.demand.app.modules.cvm_cbs.task.work.AdsCvmCbsForecastRateWork;
import cloud.demand.app.modules.cvm_cbs.task.work.AdsCvmCbsWork;
import cloud.demand.app.modules.cvm_cbs.task.work.DwsCvmCbsWork;
import cloud.demand.app.modules.cvm_cbs.task.work.DwsZiYanCbsWork;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.sop.domain.ReturnT;
import com.pugwoo.wooutils.lang.DateUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.exception.ITException;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;

@JsonrpcController("/ops/cvm-cbs/task")
public class CvmCbsTaskController {

    @Resource
    private DwsCvmCbsWork dwsCvmCbsWork;

    @Resource
    private AdsCvmCbsWork adsCvmCbsWork;

    @Resource
    private DwsZiYanCbsWork dwsZiYanCbsWork;

    @Resource
    private AdsCvmCbsForecastRateWork adsCvmCbsForecastRateWork;

    /**
     * 初始化定时任务（CVM/CBS dsm数据源）
     */
    @RequestMapping
    public ReturnT<String> genData(@JsonrpcParam @Valid CvmCbsDwsTaskReq req) {
        LocalDate startTime = req.getStartTime();
        LocalDate endTime = req.getEndTime();
        if (startTime != null && endTime != null && !startTime.isAfter(endTime)) {
            while (!startTime.isAfter(endTime)) {
                if("dwsCvmCbsWork".equals(req.getTaskName())){
                    dwsCvmCbsWork.initTask(startTime);
                }else if("dwsZiYanCbsWork".equals(req.getTaskName())){
                    dwsZiYanCbsWork.initTask(startTime);
                }else if("adsCvmCbsForecastRateWork".equals(req.getTaskName())){
                    adsCvmCbsForecastRateWork.initTask(startTime);
                }
                startTime = startTime.plusDays(1);
            }
        } else {
            throw new ITException("时间范围格式异常，起始和结束时间不能为空且起始时间不能大于结束时间");
        }
        return ReturnT.ok();
    }

    @RequestMapping
    public ReturnT<String> genData1(@JsonrpcParam @Valid CvmCbsDwsTaskReq req) {
        LocalDate startTime = req.getStartTime();
        LocalDate endTime = req.getEndTime();
        if (startTime != null && endTime != null && !startTime.isAfter(endTime)) {
            while (!startTime.isAfter(endTime)) {
                SimpleCommonTask simpleCommonTask = new SimpleCommonTask();
                simpleCommonTask.setVersion(DateUtils.format(startTime));
                if("dwsCvmCbsWork".equals(req.getTaskName())){
                    dwsCvmCbsWork.doWork(simpleCommonTask);
                }else if("adsCvmCbsWork".equals(req.getTaskName())){
                    //adsCvmCbsWork.doWork(simpleCommonTask);
                    adsCvmCbsWork.work();
                }else if("dwsZiYanCbsWork".equals(req.getTaskName())){
                    dwsZiYanCbsWork.doWork(simpleCommonTask);
                }else if("adsCvmCbsForecastRateWork".equals(req.getTaskName())){
                    adsCvmCbsForecastRateWork.doWork(simpleCommonTask);
                }
                startTime = startTime.plusDays(1);
            }
        } else {
            throw new ITException("时间范围格式异常，起始和结束时间不能为空且起始时间不能大于结束时间");
        }
        return ReturnT.ok();
    }

}
