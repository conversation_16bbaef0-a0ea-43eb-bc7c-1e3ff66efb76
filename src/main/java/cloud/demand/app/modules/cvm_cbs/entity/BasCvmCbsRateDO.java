package cloud.demand.app.modules.cvm_cbs.entity;

import cloud.demand.app.modules.cvm_cbs.domain.resp.CvmCbsRateResp;
import cloud.demand.app.modules.cvm_cbs.enums.VolumeTypeEnum;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 计算云盘申领退回配比表
 */
@Table("bas_cvm_cbs_rate")
@Data
@NoArgsConstructor
public class BasCvmCbsRateDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * 版本代码
     */
    @Column("version_code")
    private String versionCode;

    /**
     * 云盘类型
     */
    @Column("volume_type")
    private String volumeType;

    /**
     * 系统计算的申领配比
     */
    @Column("calc_apply_rate")
    private BigDecimal calcApplyRate;

    /**
     * 系统计算的退回配比
     */
    @Column("calc_return_rate")
    private BigDecimal calcReturnRate;

    /**
     * 系统计算的净增长配比
     */
    @Column("calc_net_growth_rate")
    private BigDecimal calcNetGrowthRate;

    /**
     * 实际执行的申领配比
     */
    @Column("apply_rate")
    private BigDecimal applyRate = BigDecimal.ZERO;

    /**
     * 实际执行的退回配比
     */
    @Column("return_rate")
    private BigDecimal returnRate = BigDecimal.ZERO;

    /**
     * 实际执行的净增长配比
     */
    @Column("net_growth_rate")
    private BigDecimal netGrowthRate = BigDecimal.ZERO;

    /**
     * 创建的时间<br/>Column: [create_time]
     */
    @Column(value = "create_time", setTimeWhenInsert = true)
    @ExcelIgnore
    private Date createTime;

    /**
     * 创建用户<br/>Column: [create_user]
     */
    @Column(value = "create_user", insertValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()")
    @ExcelIgnore
    private String createUser;

    @Column(value = "update_time", setTimeWhenUpdate = true)
    private Date updateTime;

    @Column(value = "update_user", updateValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()",
            insertValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()")
    private String updateUser;


    public BasCvmCbsRateDO(String versionCode, String volumeType) {
        this.versionCode = versionCode;
        this.volumeType = volumeType;
    }

    public CvmCbsRateResp transform(BasCvmCbsRateDO basCvmCbsRateDO) {
        CvmCbsRateResp resp = new CvmCbsRateResp();
        resp.setId(basCvmCbsRateDO.getId());
        resp.setVersionCode(basCvmCbsRateDO.getVersionCode());
        resp.setVolumeType(basCvmCbsRateDO.getVolumeType());
        resp.setApplyRate(basCvmCbsRateDO.getApplyRate().compareTo(BigDecimal.ZERO) > 0 ? basCvmCbsRateDO.getApplyRate() : basCvmCbsRateDO.getCalcApplyRate());
        resp.setReturnRate(basCvmCbsRateDO.getReturnRate().compareTo(BigDecimal.ZERO) > 0 ? basCvmCbsRateDO.getReturnRate() : basCvmCbsRateDO.getCalcReturnRate());
        resp.setNetGrowthRate(basCvmCbsRateDO.getNetGrowthRate().compareTo(BigDecimal.ZERO) > 0 ? basCvmCbsRateDO.getNetGrowthRate() : basCvmCbsRateDO.getCalcNetGrowthRate());
        resp.setCalcReturnRate(basCvmCbsRateDO.getCalcReturnRate());
        resp.setCalcApplyRate(basCvmCbsRateDO.getCalcApplyRate());
        resp.setCalcNetGrowthRate(basCvmCbsRateDO.getCalcNetGrowthRate());
        return resp;
    }


}
