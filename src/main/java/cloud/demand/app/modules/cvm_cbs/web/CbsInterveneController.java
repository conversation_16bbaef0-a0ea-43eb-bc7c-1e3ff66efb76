package cloud.demand.app.modules.cvm_cbs.web;

import cloud.demand.app.modules.cvm_cbs.service.BasCbsInterveneService;
import cloud.demand.app.modules.cvm_cbs.service.CvmCbsDictService;
import cloud.demand.app.modules.soe.dto.resp.ErrorMessage;
import cloud.demand.app.modules.sop.domain.ReturnT;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.web.jsonrpc.JsonrpcController;

import javax.annotation.Resource;
import java.util.List;

@JsonrpcController("/cvm-cbs/intervene")
public class CbsInterveneController {

    @Resource
    private BasCbsInterveneService basCbsInterveneService;
    
    /**
     * 导出CBS需求干预表
     */
    @RequestMapping
    public ResponseEntity<InputStreamResource> templateExcelInnerBas() {
        return basCbsInterveneService.templateExcel();
    }

    /**
     * 导入CBS需求干预表
     */
    @RequestMapping
    public ReturnT<List<ErrorMessage>> uploadInnerBas(@RequestParam("file") MultipartFile file) {
        List<ErrorMessage> errorMessages = basCbsInterveneService.uploadExcel(file);
        return ListUtils.isEmpty(errorMessages) ? ReturnT.ok(errorMessages) : ReturnT.fail(errorMessages);
    }

}
