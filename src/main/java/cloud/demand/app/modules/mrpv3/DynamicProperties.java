package cloud.demand.app.modules.mrpv3;

import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.sop.enums.InsertType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import yunti.boot.config.DynamicProperty;

import java.time.YearMonth;
import java.util.function.Supplier;

/** 动态变量 */
public class DynamicProperties {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /** 起始年月 */
    private static final Supplier<String> startYearMonth = DynamicProperty.create("app.config.mrp_v3.start.yearMonth", "2024-01");

    private static final Supplier<String> dailyStartYearMonth = DynamicProperty.create("app.config.mrp_v3.daily.start.yearMonth", "2024-01");

    private static final Supplier<List<String>> abTestUser = DynamicProperty.create("app.config.mrp_v3.abtest.user","[]",
            e -> objectMapper.readValue(e, new TypeReference<List<String>>() {}));

    /** 增量写入 */
    private static final Supplier<List<String>> incrInsertType = DynamicProperty.create("app.config.mrp_v3.dwd.insert.incr", "[]",
            e -> objectMapper.readValue(e, new TypeReference<List<String>>() {}));

    /** 增量写入需要清洗的接口 */
    private static final Supplier<List<String>> incrInsertCleanInterface = DynamicProperty.create("app.config.mrp_v3.dwd.insert.incr.clean.interface", "[]",
            e -> objectMapper.readValue(e, new TypeReference<List<String>>() {}));

    /** 日规模起始年月 */
    public static YearMonth getDailyStartYearMonth(){
        String ym = dailyStartYearMonth.get();
        try {
            return YearMonth.parse(ym);
        } catch (Exception e) {
            return YearMonth.of(2024,1);
        }
    }

    /** 通用起始年月 */
    public static YearMonth getStartYearMonth(){
        String ym = startYearMonth.get();
        try {
            return YearMonth.parse(ym);
        } catch (Exception e) {
            return YearMonth.of(2023,1);
        }
    }

    /** 是否运行 ab 测试 */
    public static List<String> getABTestUser() {
        return abTestUser.get();
    }

    /** 是否允许 abTest（在查询行业数据看板 v2 的时候，是否允许转 v3 查询） */
    public static boolean allowABTest() {
        if (EnvUtils.isLocalEnv()){
            return true;
        }
        List<String> abTestUser = getABTestUser();
        if (ListUtils.isEmpty(abTestUser)){
            return false;
        }
        if (abTestUser.contains("*")){
            return true;
        }
        String userName = LoginUtils.getUserName();
        for (String user : abTestUser) {
            if (Objects.equals(userName, user)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 通过索引获取枚举
     * @return {@link InsertType}, 默认值为 {@link InsertType#getDef()}
     */
    public static InsertType getInsertTypeWithIndex(String index) {
        if (StringUtils.isBlank(index)){
            return InsertType.getDef();
        }
        List<String> typeList = incrInsertType.get();
        if (ListUtils.isEmpty(typeList)){
            return InsertType.getDef();
        }
        return typeList.contains(index)? InsertType.INCR: InsertType.ALL;
    }

    /** 获取增量写入需要清洗的接口 */
    public static List<String> getIncrInsertCleanInterface() {
        return incrInsertCleanInterface.get();
    }
}
