package cloud.demand.app.modules.mrpv3.task;

import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.task.process.SimpleCommonTaskProcess;
import cloud.demand.app.modules.mrpv3.DynamicProperties;
import cloud.demand.app.modules.mrpv3.entity.DwdDailyMrpV3DataDfDO;
import cloud.demand.app.modules.mrpv3.model.metric.EmptyCheckMetric;
import cloud.demand.app.modules.mrpv3.model.metric.IDataCheckMetric;
import cloud.demand.app.modules.mrpv3.service.MrpV3CleanService;
import cloud.demand.app.modules.report_proxy.anno.TaskRunSql;
import cloud.demand.app.modules.soe.dto.resp.ExtErrorMessage;
import cloud.demand.app.modules.sop.enums.InsertType;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.work.AbstractSopWork;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
public abstract class DwdMrpCommonWork extends AbstractSopWork<SimpleCommonTask> {

    /** 任务处理 */
    @Resource
    private SimpleCommonTaskProcess process;

    /** 数据清洗 */
    @Resource
    protected MrpV3CleanService cleanService;

    /** std ck */
    @Resource
    protected DBHelper ckcldStdCrpDBHelper;

    /**
     * 插入类型，默认全量，
     * 有些指标不允许历史数据发生变动，所以需要增量处理，历史数据从历史切片查询，按年月分组
     * 如果 InsertType = INCR
     * 例子：2025-01-06 的切片查询月规模 2024-12 月的及其之前的数据，取切片 2025-01-01 的数据，2025-01 当月的数据从底表查询
     * 如果 InsertType = ALL
     * 例子：2025-01-06 的切片查询月规模 所有数据都从底数查询，不从历史切片查询
     * */
    public InsertType getInsertType() {
        return DynamicProperties.getInsertTypeWithIndex(getIndex());
    }

    @Override
    public ISopProcess<SimpleCommonTask> getTask() {
        return process;
    }

    /** 2 分钟执行一次 */
    @Scheduled(fixedRate = 120 * 1000)
    @Override
    public void work() {
        super.work();
    }

    public void doInit(String version){
        process.initTask(new SimpleCommonTask(version),getEnum());
    }

    @Override
    public void doWork(SimpleCommonTask task) {
        String statTime = task.getVersion(); // 切片时间
        genData(statTime);
    }

    /**
     * 行业数据看板 - 任务切面
     * @param simpleCommonTask 任务类
     * 注解：@TaskRunSql
     *  namespace 命名空间
     *  nameScript 任务名称
     *  keyScript 任务主key
     *  extScript 任务扩展key
     *  logArgs 是否打印参数
     */
    @TaskRunSql(namespace = "cloud-demand-app#sop-task" ,nameScript = "args[0].name",keyScript = "args[0].lockValue", extScript = "target.index", logArgs = true)
    public void aroundWork(SimpleCommonTask simpleCommonTask) {
        super.aroundWork(simpleCommonTask);
    }

    public void genData(String statTime){
        // 先取出来，避免【insertType】动态变化
        InsertType insertType = getInsertType();
        // step1：获取dwd 数据
        List<?> originData = getData(statTime);
        // step2：转换 data
        List<DwdDailyMrpV3DataDfDO> data = transform(originData);
        // step3：填充切片时间和指标
        fillData(data,LocalDate.parse(statTime));
        // step4：数据校验
        checkData(data);
        // step5：清洗数据
        cleanData(data);
        // step6：过滤数据
        data = filterData(data);
        // step7：最后调整
        beforeInsert(data);
        // step8：写入策略强行判断data 里面是否有历史数据（比如 2025-01-06 切片不允许存在 2024-12 月份的数据）
        checkIfINCR(data,statTime,insertType);
        // step9：删除分区
        dropPartition(statTime);
        // step10：写入数据
        CkDBUtils.syncSaveBatch(ckcldStdCrpDBHelper,data,ListUtils.newList(DwdDailyMrpV3DataDfDO::getStatTime,DwdDailyMrpV3DataDfDO::getIndex));
        // step11：策略处理(如果是增量，则处理填充历史数据)
        insertIntoSelectWithHis(statTime,insertType);
    }

    /**
     * 如果为增量，则判断 data 里面是否有历史数据，有则抛出异常
     * 案例：2025-01-06 取 2025-01-01 切片的 2025-01 月之前的月份数据
     * @param data 写入数据
     */
    private void checkIfINCR(List<DwdDailyMrpV3DataDfDO> data,String statTime,InsertType insertType) {
        // 不是增量写入直接跳过
        if (insertType != InsertType.INCR){
            return;
        }
        String preStatTime = DateUtils.formatDate(LocalDate.parse(statTime).plusDays(-1));
        String curYearMonth = preStatTime.substring(0, 7); // (切片-1)当月
        data.stream().filter(item->item.getYearMonth().compareTo(curYearMonth) < 0).findFirst().ifPresent(item->{
            log.info("存在历史数据，statTime:{}, yearMonth:{}",preStatTime, curYearMonth);
            throw new ITException("写入模式是增量时，不允许手动写入历史数据，请检查 getData 是否查询了历史数据");
        });
    }

    /**
     * 如果为增量，则处理填充历史数据
     */
    private void insertIntoSelectWithHis(String statTime,InsertType insertType) {
        // 不是增量写入直接跳过
        if (insertType != InsertType.INCR){
            return;
        }
        LocalDate date = LocalDate.parse(statTime).plusDays(-1); // 昨天
        LocalDate firstDate = date.withDayOfMonth(1); // (切片-1)当月第一天
        String curYearMonth = DateUtils.formatDate(date).substring(0, 7); // (切片-1)当月
        // 历史数据取当天第一天的
        String index = getIndex();
        String preStatTime = DateUtils.formatDate(firstDate);
        WhereSQL whereSQL = new WhereSQL();
        long count;
        Class<?> tableClass = DwdDailyMrpV3DataDfDO.class;
        List<String> incrInsertCleanInterface = DynamicProperties.getIncrInsertCleanInterface();
        // 直接用历史数据
        if (ListUtils.isEmpty(incrInsertCleanInterface)){
            String sql = ORMUtils.getSql("/sql/mrp_v3/dwd/sync_his_dwd_data.sql");
            sql = SimpleSqlBuilder.doReplace(sql, "pre_stat_time", preStatTime);
            sql = SimpleSqlBuilder.doReplace(sql, "cur_stat_time", statTime);
            sql = SimpleSqlBuilder.doReplace(sql, "index", index);
            sql = SimpleSqlBuilder.doReplace(sql, "cur_year_month", curYearMonth);
            ckcldStdCrpDBHelper.executeRaw(sql);

            whereSQL.and("stat_time = ?", preStatTime);
            whereSQL.and("year_month < ?", curYearMonth);
            whereSQL.and("`index` = ?", index);
            count = ckcldStdCrpDBHelper.getCount(tableClass, whereSQL);
        }else {
            // 历史数据需要清洗
            List<Class<?>> interClass = new ArrayList<>();
            for (String inter : incrInsertCleanInterface) {
                try {
                    interClass.add(Class.forName(inter));
                } catch (ClassNotFoundException e) {
                    throw new RuntimeException(e);
                }
            }
            String sql = ORMUtils.getSql("/sql/mrp_v3/dwd/sync_his_dwd_data_with_clean.sql");
            sql = SimpleSqlBuilder.doReplace(sql, "pre_stat_time", preStatTime);
            sql = SimpleSqlBuilder.doReplace(sql, "cur_stat_time", statTime);
            sql = SimpleSqlBuilder.doReplace(sql, "index", index);
            sql = SimpleSqlBuilder.doReplace(sql, "cur_year_month", curYearMonth);
            List<DwdDailyMrpV3DataDfDO> all = ckcldStdCrpDBHelper.getRaw(DwdDailyMrpV3DataDfDO.class, sql);
            count = all.size();
            all.forEach(item-> cleanService.clean(item, interClass));
            CkDBUtils.saveBatch(ckcldStdCrpDBHelper, all);
        }

        whereSQL = new WhereSQL();
        whereSQL.and("stat_time = ?", statTime);
        whereSQL.and("year_month < ?", curYearMonth);
        whereSQL.and("`index` = ?", index);
        // 等待写入完成（5 分钟）
        CkDBUtils.waitWriteFinish(ckcldStdCrpDBHelper, count, tableClass, whereSQL, 60 * 5, false);
    }


    private void beforeInsert(List<DwdDailyMrpV3DataDfDO> data) {
        Consumer<DwdDailyMrpV3DataDfDO> adj = getAdjDataFun();
        if (adj == null || ListUtils.isEmpty(data)){
            return;
        }
        data.forEach(adj);
    }

    protected Consumer<DwdDailyMrpV3DataDfDO> getAdjDataFun() {
        return null;
    }

    private List<DwdDailyMrpV3DataDfDO> filterData(List<DwdDailyMrpV3DataDfDO> data) {
        Predicate<DwdDailyMrpV3DataDfDO> filter = getFilter();
        if (filter == null || ListUtils.isEmpty(data)){
            return data;
        }
        return data.stream().filter(filter).collect(Collectors.toList());
    }

    protected Predicate<DwdDailyMrpV3DataDfDO> getFilter(){
        return null;
    }

    private void fillData(List<DwdDailyMrpV3DataDfDO> data,LocalDate statTime) {
        if (ListUtils.isNotEmpty(data)){
            data.forEach(item->{
                // 设置指标
                item.setIndex(getIndex());
                // 设置时间
                item.setStatTime(statTime);
            });
        }
    }

    /**
     * 删除分区
     * @param statTime 切片时间
     */
    private void dropPartition(String statTime) {
        String sql = "alter table dwd_daily_mrp_v3_data_df_local on cluster default_cluster drop partition('${stat_time}','${index}')";
        sql = SimpleSqlBuilder.doReplace(sql,"stat_time",statTime);
        sql = SimpleSqlBuilder.doReplace(sql,"index",getIndex());
        ckcldStdCrpDBHelper.executeRaw(sql);
    }

    /**
     * 清洗数据
     * @param data 数据
     */
    private void cleanData(List<DwdDailyMrpV3DataDfDO> data) {
        data.forEach(item->{
            // 清洗维度
            cleanService.clean(item);
        });
    }

    /**
     * @return true 如果为空，则抛出异常，否则不抛出异常
     */
    public void checkData(List<DwdDailyMrpV3DataDfDO> data){
        List<IDataCheckMetric<DwdDailyMrpV3DataDfDO>> checkMetric = getCheckMetric();
        if (ListUtils.isEmpty(checkMetric)){
            return;
        }
        List<ExtErrorMessage> msg = new ArrayList<>();
        // 校验 data + item
        checkMetric.forEach(item-> {
            List<ExtErrorMessage> extErrorMessages = item.checkAll(data);
            if (ListUtils.isNotEmpty(extErrorMessages)){
                msg.addAll(extErrorMessages);
            }
            for (DwdDailyMrpV3DataDfDO datum : data) {
                extErrorMessages = item.checkOne(datum);
                if (ListUtils.isNotEmpty(extErrorMessages)){
                    msg.addAll(extErrorMessages);
                }
            }
        });
        List<ExtErrorMessage> errMsg = new ArrayList<>();
        List<ExtErrorMessage> normalMsg = new ArrayList<>();
        for (ExtErrorMessage extErrorMessage : msg) {
            if (extErrorMessage.isThrowable()){
                errMsg.add(extErrorMessage);
            }else {
                normalMsg.add(extErrorMessage);
            }
        }
        log.info("normalMsg:{}",normalMsg);
        log.error("errMsg:{}",errMsg);
        if (ListUtils.isNotEmpty(errMsg)){
            throw new ITException(String.format("数据校验失败：%s", errMsg.stream().map(ExtErrorMessage::getMessage).collect(Collectors.toList())));
        }
    }

    public List<IDataCheckMetric<DwdDailyMrpV3DataDfDO>> getCheckMetric(){
        // 生产默认加上空集合校验
        List<IDataCheckMetric<DwdDailyMrpV3DataDfDO>> ret = new ArrayList<>();
        ret.add(new EmptyCheckMetric(EnvUtils.isProduction())); // 只有生产才抛出异常
        return ret;
    }

    /** 获取 dwd 数据 */
    public abstract List<?> getData(String statTime);

    /** 转换 */
    protected List<DwdDailyMrpV3DataDfDO> transform(List<?> data){
        List<DwdDailyMrpV3DataDfDO> ret = new ArrayList<>();
        if (ListUtils.isNotEmpty(data)){
            for (Object datum : data) {
                ret.add(cleanService.transform(datum));
            }
        }
        return ret;
    }

    /** 获取指标名称 */
    public abstract String getIndex();

}
