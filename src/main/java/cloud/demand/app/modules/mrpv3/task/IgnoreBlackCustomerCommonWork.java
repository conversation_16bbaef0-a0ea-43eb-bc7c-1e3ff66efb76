package cloud.demand.app.modules.mrpv3.task;

import cloud.demand.app.modules.mrpv3.entity.DwdDailyMrpV3DataDfDO;
import cloud.demand.app.modules.sop.enums.FlagType;

import java.util.function.Consumer;

/** 忽略黑名单客户 */
public abstract class IgnoreBlackCustomerCommonWork extends DwdMrpCommonWork{
    @Override
    protected Consumer<DwdDailyMrpV3DataDfDO> getAdjDataFun() {
        return item -> item.setIsBlackCustomer(FlagType.NO.getDesc());
    }
}
