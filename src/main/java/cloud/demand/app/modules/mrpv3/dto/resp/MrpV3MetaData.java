package cloud.demand.app.modules.mrpv3.dto.resp;

import cloud.demand.app.modules.mrpv3.enums.MrpV3DimFieldEnum;
import cloud.demand.app.modules.mrpv3.enums.MrpV3IndexFieldEnum;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Arrays;
import java.util.LinkedHashMap;

/** 原数据 */

@Data
public class MrpV3MetaData {

    /** 指标 */
    private LinkedHashMap<String,String> index;

    /** 维度 */
    private LinkedHashMap<String,String> dim;


    public static MrpV3MetaData build() {
        MrpV3MetaData ret = new MrpV3MetaData();
        LinkedHashMap<String,String> index = new LinkedHashMap<>();
        for (MrpV3IndexFieldEnum value : MrpV3IndexFieldEnum.values()) {
            // abs 是过程中数据，不提供展示
            if (BooleanUtils.isNotTrue(value.getIsAbs())){
                index.put(value.getColumn(),value.getDesc());
            }
        }
        LinkedHashMap<String,String> dim = new LinkedHashMap<>();
        for (MrpV3DimFieldEnum value : MrpV3DimFieldEnum.values()) {
            dim.put(value.name(),value.getName());
        }
        ret.setDim(dim);
        ret.setIndex(index);
        return ret;
    }
}
