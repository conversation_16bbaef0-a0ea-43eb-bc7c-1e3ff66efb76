package cloud.demand.app.modules.mrpv3.model.clean;

import cloud.demand.app.modules.soe.model.clean.IUnInstanceType;

public interface ICleanInstanceType extends IUnInstanceType {
    public String getInstanceType();
    public String getUnInstanceType();

    /** 主力机型 */
    public void setIsMainInstanceType(String isMainInstanceType);

    /** 合并主力机型 */
    public void setIsMainUnInstanceType(String isMainInstanceType);

    /** 黑名单机型 */
    public void setIsBlackInstanceType(String isBlackInstanceType);

    /** 采购机型 */
    public void setIsPurchaseInstanceType(String isPurchaseInstanceType);

    /** 合并采购机型 */
    public void setIsPurchaseUnInstanceType(String isPurchaseInstanceType);

    /** 新旧机型 */
    public void setIsGenerationInstanceType(String isGenerationInstanceType);

    /** 合并新旧机型 */
    public void setIsGenerationUnInstanceType(String isGenerationInstanceType);

}
