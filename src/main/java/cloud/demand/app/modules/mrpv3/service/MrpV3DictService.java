package cloud.demand.app.modules.mrpv3.service;

import java.util.List;
import java.util.Map;

/** 字典 */
public interface MrpV3DictService {
    /** 获取最大切片时间 */
    public String getMaxStaTime();

    /** 获取通用实例类型 --> 实例类型映射 */
    public Map<String, List<String>> getCommonInstanceType();

    /** 通过 uin 获取磐石战区 */
    public Map<Long, String> getUin2PSWarZone();

    /** 通过 uin 获取 销售通路 */
    public Map<String, String> getUin2IncomeSalesDesc();
}
