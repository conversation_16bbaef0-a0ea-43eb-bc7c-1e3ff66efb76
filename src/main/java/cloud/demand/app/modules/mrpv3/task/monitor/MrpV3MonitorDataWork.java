package cloud.demand.app.modules.mrpv3.task.monitor;

import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv3.enums.MrpV3TaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import org.springframework.stereotype.Service;

/** 监控任务-数据监控 */
@Service
public class MrpV3MonitorDataWork extends MonitorCommonWork {

    @Override
    public ITaskEnum getEnum() {
        return MrpV3TaskEnum.MRP_V3_MONITOR_DATA;
    }

    @Override
    public void doWork(SimpleCommonTask task) {
        // step1：dwd 层来源数据汇总查询
        // step2：dws 层数据查询
        // step3：核对 dwd 和 dws 层数据总数是否一致
    }
}
