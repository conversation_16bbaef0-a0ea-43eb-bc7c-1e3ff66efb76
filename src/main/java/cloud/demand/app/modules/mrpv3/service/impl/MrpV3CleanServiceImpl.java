package cloud.demand.app.modules.mrpv3.service.impl;

import cloud.demand.app.entity.rrp.ReportConfigGpuTypeDO;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.mrpv3.enums.CustomerRangeEnum;
import cloud.demand.app.modules.mrpv3.service.MrpV3DictService;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplGpuRegionZoneDO;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.DynamicProperties;
import cloud.demand.app.modules.mrpv2.entity.BasInnerIndustryCustomerDO;
import cloud.demand.app.modules.mrpv2.entity.PplForecastConfigCustomerDefinesYearMonthVersionShortDO;
import cloud.demand.app.modules.mrpv2.enums.BizTypeEnum;
import cloud.demand.app.modules.mrpv2.enums.GenerationEnum;
import cloud.demand.app.modules.mrpv2.enums.NewCustomerTypeEnum;
import cloud.demand.app.modules.mrpv2.model.clean.ICleanBizType;
import cloud.demand.app.modules.mrpv2.service.BasInnerIndustryCustomerService;
import cloud.demand.app.modules.mrpv2.service.CleanService;
import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.mrpv3.entity.DwdDailyMrpV3DataDfDO;
import cloud.demand.app.modules.mrpv3.entity.getter.*;
import cloud.demand.app.modules.mrpv3.model.clean.*;
import cloud.demand.app.modules.mrpv3.service.MrpV3CleanService;
import cloud.demand.app.modules.operation_view.inventory_health.enums.InventoryHealthZoneType;
import cloud.demand.app.modules.operation_view.inventory_health.service.InventoryHealthConfigService;
import cloud.demand.app.modules.soe.model.clean.IRegionClean;
import cloud.demand.app.modules.soe.service.SoeCleanService;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.enums.FlagType;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class MrpV3CleanServiceImpl implements MrpV3CleanService {

    /**
     * 清洗地域国家
     */
    @Resource
    private SoeCleanService soeCleanService;

    /**
     * 继承 mrp_v2 的清洗
     */
    @Resource
    private CleanService cleanService;

    /**
     * 获取主力机型主流园区
     */
    @Resource
    private InventoryHealthConfigService configService;

    /**
     * 黑名单机型
     */
    @Resource
    private DBHelper demandDBHelper;

    /**
     * 采购和新旧机型
     */
    @Resource
    private MrpV2DictService mrpV2DictService;

    @Resource
    private MrpV3DictService mrpV3DictService;

    @Resource
    private DictService dictService;

    @Resource
    private PplDictService pplDictService;


    /** 内领行业部门业务策略表 */
    @Resource
    private BasInnerIndustryCustomerService basInnerIndustryCustomerService;

    // =============== 清洗 local 缓存 ================
    private static final ThreadLocal<Set<String>> mainInstanceTypeMap = new ThreadLocal<>();

    /**
     * @return 主力机型集合
     */
    public Set<String> getMainInstanceType() {
        Set<String> set = mainInstanceTypeMap.get();
        if (set == null) {
            // 获取前一天切片
            String date = DateUtils.formatDate(LocalDate.now().plusDays(-1));
            Map<String, List<String>> map = ObjectUtils.defaultIfNull(configService.getInstanceTypeConfigMap(date), new HashMap<>());
            set = new HashSet<>(
                    map.getOrDefault(InventoryHealthZoneType.PRINCIPAL.getCode(), ListUtils.newList()));
            ;
            mainInstanceTypeMap.set(set);
        }
        return set;
    }

    private static final ThreadLocal<Set<String>> blackInstanceTypeMap = new ThreadLocal<>();

    /**
     * @return 黑名单机型集合
     */
    public Set<String> getBlackInstanceType() {
        Set<String> set = blackInstanceTypeMap.get();
        if (set == null) {
            // 获取黑名单机型，读取中长尾的时候过滤掉
            String blackListSql = "select instance_types from mrpv2_common_instance_type_config where use_forecast = 0";
            List<String> instanceBlackDBList = demandDBHelper.getRaw(String.class, blackListSql);
            List<String> instanceBlackList = ListUtils.newList();

            for (String item : instanceBlackDBList) {
                for (String s : item.split(",")) {
                    instanceBlackList.add(s.trim());
                }
            }
            set = new HashSet<>(instanceBlackList);
            blackInstanceTypeMap.set(set);
        }
        return set;
    }

    /**
     * 采购机型
     */
    private static final ThreadLocal<Set<String>> purchaseInstanceType = new ThreadLocal<>();

    /**
     * 获取采购机型
     */
    public Set<String> getPurchaseInstanceType() {
        Set<String> set = purchaseInstanceType.get();
        if (set == null) {
            set = new HashSet<>(mrpV2DictService.getPurchaseInstanceType());
            purchaseInstanceType.set(set);
        }
        return set;
    }

    /**
     * 新旧机型
     */
    private static final ThreadLocal<Set<String>> generationInstanceType = new ThreadLocal<>();

    /**
     * 获取新旧机型
     */
    public Set<String> getGenerationInstanceType() {
        Set<String> set = generationInstanceType.get();
        if (set == null) {
            set = new HashSet<>(mrpV2DictService.getNewGenerationInstanceType());
            generationInstanceType.set(set);
        }
        return set;
    }

    private static final ThreadLocal<Set<String>> mainZoneNameMap = new ThreadLocal<>();

    /**
     * @return 主力园区集合
     */
    public Set<String> getMainZoneNameMap() {
        Set<String> set = mainZoneNameMap.get();
        if (set == null) {
            // 获取前一天切片
            String date = DateUtils.formatDate(LocalDate.now().plusDays(-1));
            Map<String, List<String>> map = ObjectUtils.defaultIfNull(configService.getZoneConfigMap(date), new HashMap<>());
            set = new HashSet<>(
                    map.getOrDefault(InventoryHealthZoneType.PRINCIPAL.getCode(), ListUtils.newList()));
            mainZoneNameMap.set(set);
        }
        return set;
    }

    private static final ThreadLocal<Map<String, List<DynamicProperties.BlackDTO>>> blackCustomerMap = new ThreadLocal<>();

    public Map<String, List<DynamicProperties.BlackDTO>> getBlackCustomerMap() {
        Map<String, List<DynamicProperties.BlackDTO>> stringListMap = blackCustomerMap.get();
        if (stringListMap == null) {
            List<DynamicProperties.BlackDTO> blackDTOS1 = DynamicProperties.blackDTOList();
            // 按照维度获取，除了产品和客户简称是必须匹配的，其他实例类型，年月，行业部门都可以缺省，
            Map<String, DynamicProperties.BlackDTO> map = ListUtils.toMap(blackDTOS1, (item) -> StringUtils.joinWith("@",
                    item.getDataProduct(),
                    item.getIndustryDept(),
                    item.getCustomerShortName(),
                    item.getYearMonth(),
                    item.getInstanceType()), item -> item);
            // 按照产品+客户简称分组
            stringListMap = ListUtils.groupBy(map.values(), (item) -> StringUtils.joinWith("@", item.getDataProduct(), item.getCustomerShortName()));
            blackCustomerMap.set(stringListMap);
        }
        return stringListMap;
    }

    /** 老客户分类的头部名单，key：uin@yearMonth */
    private static final ThreadLocal<Map<String, PplForecastConfigCustomerDefinesYearMonthVersionShortDO>> headerUinMap = new ThreadLocal<>();

    public Map<String, PplForecastConfigCustomerDefinesYearMonthVersionShortDO> getHeaderUinMap() {
        Map<String, PplForecastConfigCustomerDefinesYearMonthVersionShortDO> map = headerUinMap.get();
        if (map == null) {
            map = mrpV2DictService.getHeaderUinMap();
            headerUinMap.set(map);
        }
        return map;
    }

    /**
     * 内领行业客户，只改业务类型，不改子产品，所以会存在内领业务有 CVM&CBS 的子产品，原因：这部分的预测是内领
     * key：行业部门@客户简称
     * */
    private static final ThreadLocal<Set<String>> innerIndustryCustomerMap = new ThreadLocal<>();

    private static final ThreadLocal<Set<String>> innerIndustryMap = new ThreadLocal<>();

    private Set<String> getInnerIndustryMap(){
        Set<String> set = innerIndustryMap.get();
        if (set == null) {
            List<BasInnerIndustryCustomerDO> list = basInnerIndustryCustomerService.getList();
            set = ListUtils.toSet(list, BasInnerIndustryCustomerDO::getIndustryDept);
            innerIndustryMap.set(set);
        }
        return set;
    }

    public Set<String> getInnerIndustryCustomerMap() {
        Set<String> set = innerIndustryCustomerMap.get();
        if (set == null) {
            List<BasInnerIndustryCustomerDO> list = basInnerIndustryCustomerService.getList();
            set = ListUtils.toSet(list,item->
                    StringUtils.joinWith("@",item.getIndustryDept(),item.getCustomerShortName()));
            innerIndustryCustomerMap.set(set);
        }
        return set;
    }

    private final static ThreadLocal<Map<String,String>> unCustomerShortNameMapLocal = new ThreadLocal<>();
    public Map<String,String> getUnCustomerShortNameMap(){
        Map<String, String> map = unCustomerShortNameMapLocal.get();
        if (map == null){
            List<IndustryDemandIndustryWarZoneDictDO> customerConfig = dictService.queryEnableIndustryWarZoneCustomerConfig();
            map = new HashMap<>();
            for (IndustryDemandIndustryWarZoneDictDO dictDO : customerConfig) {
                // key：客户简称
                map.put(dictDO.getCustomerName(),dictDO.getCommonCustomerName());
            }
            unCustomerShortNameMapLocal.set(map);
        }
        return map;
    }

    /** 客户类型 */
    private final static ThreadLocal<Map<String, Integer>> uinTypeMapLocal = new ThreadLocal<>();

    public Map<String, Integer> getUinType() {
        Map<String, Integer> map = uinTypeMapLocal.get();
        if (map == null) {
            map = mrpV2DictService.queryUinTypeMap();
            uinTypeMapLocal.set(map);
        }
        return map;
    }

    private final static ThreadLocal<Map<String, String>> instanceType2GpuCardLocal = new ThreadLocal<>();


    /** 实例类型 --> gpu卡型 */
    public Map<String,String> getGpuCardByInstanceType(){
        Map<String, String> ret = instanceType2GpuCardLocal.get();
        if (ret == null){
            List<PplGpuRegionZoneDO> pplGpuRegionZoneDOS = pplDictService.queryGpuInstanceList();
            ret = new HashMap<>();
            for (PplGpuRegionZoneDO pplGpuRegionZoneDO : pplGpuRegionZoneDOS) {
                ret.put(pplGpuRegionZoneDO.getInstanceType(), pplGpuRegionZoneDO.getGpuType());
            }
            instanceType2GpuCardLocal.set(ret);
        }
        return ret;
    }

    private final static ThreadLocal<Map<String, String>> gpuCard2GpuTypeLocal = new ThreadLocal<>();

    /** gpu卡型 --> gpu类型 */
    public Map<String,String> getGpuTypeByGpuCard(){
        Map<String, String> ret = gpuCard2GpuTypeLocal.get();
        if (ret == null){
            ret = new HashMap<>();
            Map<String, ReportConfigGpuTypeDO> map = dictService.loadGpuCardConfig();
            List<ReportConfigGpuTypeDO> collect = map.values().stream()
                    .sorted((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime())).collect(
                            Collectors.toList());
            for (ReportConfigGpuTypeDO typeDO : collect) {
                // 如果不存在，则添加（由于按照时间降序，这里匹配到的是最新的记录）
                if (!ret.containsKey(typeDO.getGpuCardType())){
                    ret.put(typeDO.getGpuCardType(), typeDO.getGpuType());
                }
            }
            gpuCard2GpuTypeLocal.set(ret);
        }
        return ret;
    }

    /** uin转磐石战区 */
    private final static ThreadLocal<Map<Long,String>> uin2PSWarZoneLocal = new ThreadLocal<>();

    public Map<Long,String> getUin2PSWarZone(){
        Map<Long,String> ret = uin2PSWarZoneLocal.get();
        if (ret == null){
            ret = mrpV3DictService.getUin2PSWarZone();
            uin2PSWarZoneLocal.set(ret);
        }
        return ret;
    }

    private final static ThreadLocal<Map<String,String>> uin2IncomeSalesDescLocal = new ThreadLocal<>();

    /** 获取 uin --> 销售通路 */
    private Map<String,String> getUin2IncomeSalesDesc(){
        Map<String,String> ret = uin2IncomeSalesDescLocal.get();
        if (ret == null){
            ret = mrpV3DictService.getUin2IncomeSalesDesc();
            uin2IncomeSalesDescLocal.set(ret);
        }
        return ret;
    }

    /**
     * 清洗：
     * 1. 清洗可用区 --> 境内外
     * 2. 清洗业务类型 --> 内领
     * 3. 清洗老客户分类
     * 4. 继承老行业数据看板的清洗逻辑
     * 5. 清洗季度
     * 6. 清洗机型相关
     * 7. gpu卡型，gpu类型
     * 8. 可用区相关
     * 9. 黑名单客户
     * */
    @Override
    public void clean(Object obj) {
        // 清洗可用区 ---> 境内外
        if (obj instanceof IRegionClean) {
            soeCleanService.cleanRegion((IRegionClean) obj);
        }
        if (obj instanceof ICleanBizType) {
            cleanBizType((ICleanBizType) obj);
        }
        // 继承mrp_v2的清洗
        cleanService.clean(obj);
        // 清洗战区
        if (obj instanceof ICleanWarZoneWithPS) {
            cleanWarZoneWithPS((ICleanWarZoneWithPS) obj);
        }
        if (obj instanceof ICleanUnCustomerShortName) {
            cleanUnCustomerShortName((ICleanUnCustomerShortName) obj);
        }
        // 要晚于mrp_v2的清洗，因为 mrp_v2会填充客户简称
        if (obj instanceof IInnerIndustryCustomer){
            cleanInnerIndustryCustomer((IInnerIndustryCustomer)obj);
        }
        // 清洗老客户分类
        if (obj instanceof ICleanCustomerTabType){
            cleanCustomerTabType((ICleanCustomerTabType)obj);
        }
        // 清洗季度
        if (obj instanceof ICleanQuarter) {
            cleanQuarter((ICleanQuarter) obj);
        }
        // 清洗机型相关
        if (obj instanceof ICleanInstanceType) {
            cleanInstanceType((ICleanInstanceType) obj);
        }
        // gpu卡型、gpu类型
        if (obj instanceof ICleanGpuCardType){
            cleanGpuCardType((ICleanGpuCardType) obj);
        }
        // 可用区相关
        if (obj instanceof ICleanZoneName) {
            cleanZoneName((ICleanZoneName) obj);
        }
        // 黑名单客户
        if (obj instanceof ICleanBlackCustomer) {
            cleanBlackCustomer((ICleanBlackCustomer) obj);
        }
        // 客户类型：1 - 内部客户 0 - 外部客户
        if (obj instanceof ICleanCustomerRange){
            cleanCustomerRange((ICleanCustomerRange)obj);
        }
        // 销售通路
        if (obj instanceof ICleanIncomeSalesDesc){
            cleanIncomeSalesDesc((ICleanIncomeSalesDesc)obj);
        }
    }

    @Override
    public void clean(Object obj, List<Class<?>> interClass) {
        // 销售通路
        if (interClass.contains(ICleanIncomeSalesDesc.class) && obj instanceof ICleanIncomeSalesDesc){
            cleanIncomeSalesDesc((ICleanIncomeSalesDesc)obj);
        }
    }

    private void cleanIncomeSalesDesc(ICleanIncomeSalesDesc obj) {
        String uin = obj.getUin();
        Map<String, String> uin2IncomeSalesDesc = getUin2IncomeSalesDesc();
        obj.setIncomeSalesDesc(uin2IncomeSalesDesc.get(uin));
    }

    /**
     * 清洗磐石战区
     * 1. crp战区为空值，则使用ps战区
     * 2. uin 解析成功
     * 3. app_info表有记录
     * @param obj
     */
    private void cleanWarZoneWithPS(ICleanWarZoneWithPS obj) {
        String warZone = obj.getWarZone();
        if (SoeCommonUtils.isNotBlank(warZone)){
            return;
        }
        Map<Long, String> uin2PSWarZone = getUin2PSWarZone();
        String uin = obj.getUin();
        if (SoeCommonUtils.isNotBlank(uin)){
            long key;
            try {
                key = Long.parseLong(uin);
            }catch (NumberFormatException e ){
                return;
            }
            String warZoneName = uin2PSWarZone.get(key);
            if (SoeCommonUtils.isNotBlank(warZoneName)){
                obj.setWarZone(warZoneName);
            }
        }
    }

    /** 清洗客户类型（内外部客户） */
    private void cleanCustomerRange(ICleanCustomerRange obj) {
        String uin = obj.getUin();
        Map<String, Integer> customerRangeMap = getUinType();
        Integer uinType = customerRangeMap.get(uin);
        if (uinType == null){
            obj.setCustomerRange(null);
        }else {
            // uinType 1 - 外部客户 0 - 内部客户
            // customerRange 0 - 外部客户 1 - 内部客户
            obj.setCustomerRange(uinType == 0 ? CustomerRangeEnum.INNER.getCode() : CustomerRangeEnum.OUTER.getCode());
        }
    }

    /** 清洗 gpu 卡型，gpu 类型 */
    private void cleanGpuCardType(ICleanGpuCardType obj) {
        String product = obj.getProduct();
        // 只有gpu才需要清洗卡型、gpu类型
        if (!Objects.equals(product, ProductTypeEnum.GPU.getCode())){
            return;
        }
        String instanceType = obj.getInstanceType();
        // 1： 根据实例类型获取卡型、gpu类型
        if (SoeCommonUtils.isNotBlank(instanceType)){
            Map<String, String> gpuCardByInstanceType = getGpuCardByInstanceType();
            String gpuCardType = gpuCardByInstanceType.get(instanceType);
            if (gpuCardType != null){
                obj.setGpuCardType(gpuCardType);
                // 暂时不清洗GPU类型，TODO 后续再做
//                Map<String, String> gpuTypeByGpuCard = getGpuTypeByGpuCard();
//                obj.setGpuType(gpuTypeByGpuCard.get(gpuCardType));
            }
        }
    }

    private void cleanUnCustomerShortName(ICleanUnCustomerShortName obj) {
        Map<String, String> unCustomerShortNameMap = getUnCustomerShortNameMap();
        String customerShortName = obj.getCustomerShortName();
        String unCustomerShortName = unCustomerShortNameMap.getOrDefault(customerShortName,customerShortName);
        obj.setUnCustomerShortName(unCustomerShortName);
    }

    /** 清洗内领的行业客户 */
    private void cleanInnerIndustryCustomer(IInnerIndustryCustomer obj) {
        // 内领行业的行业部门展示行业部门
        if (getInnerIndustryMap().contains(obj.getIndustryDept())) {
            obj.setIndustryOrProduct(obj.getIndustryDept());
            // key：行业部门@客户简称
            String key = String.join("@",obj.getIndustryDept(),obj.getCustomerShortName());
            if (getInnerIndustryCustomerMap().contains(key)){
                obj.setBizType(BizTypeEnum.INNER.getName());
            }
        }
    }

    /** 清洗老客户分类 */
    private void cleanCustomerTabType(ICleanCustomerTabType obj) {
        String customerTabType = obj.getCustomerTabType();
        if(SoeCommonUtils.isNotBlank(customerTabType)){
            obj.setOldCustomerType(Objects.equals(customerTabType, Constant.TAIL_CUSTOMER)? NewCustomerTypeEnum.LONG_TAIL.getName():NewCustomerTypeEnum.HEADER.getName());
            return;
        }
        Map<String, PplForecastConfigCustomerDefinesYearMonthVersionShortDO> headerUinMap = getHeaderUinMap();
        String yearMonth = obj.getYearMonth();
        String uin = obj.getUin();
        PplForecastConfigCustomerDefinesYearMonthVersionShortDO info = headerUinMap.get(uin + "@" + yearMonth);
        if (info == null){
            obj.setCustomerTabType(Constant.TAIL_CUSTOMER);
            obj.setOldCustomerType(NewCustomerTypeEnum.LONG_TAIL.getName());
        }else {
            obj.setCustomerTabType(info.getLabel());
            obj.setOldCustomerType(NewCustomerTypeEnum.HEADER.getName());
        }
    }

    /**
     * 清洗季度
     * @param obj 年月
     */
    private void cleanQuarter(ICleanQuarter obj) {
        String yearMonth = obj.getYearMonth();
        if (StringUtils.isNotBlank(yearMonth)){
            String[] split = yearMonth.split("-");
            int year = Integer.parseInt(split[0]);
            int month = Integer.parseInt(split[1]);
            int quarter = (month - 1) / 3 + 1;
            obj.setYearQuarter(year + "-Q" + quarter);
        }
    }

    /**
     * @param obj 客户黑名单
     */
    private void cleanBlackCustomer(ICleanBlackCustomer obj) {
        String industryDept = obj.getIndustryDept();
        String product = obj.getProduct();
        String yearMonth = obj.getYearMonth();
        String customerShortName = obj.getCustomerShortName();
        String instanceType = obj.getInstanceType();
        Map<String, List<DynamicProperties.BlackDTO>> blackCustomerMap = getBlackCustomerMap();
        List<DynamicProperties.BlackDTO> blackDTOS = blackCustomerMap.get(StringUtils.joinWith("@", product, customerShortName));
        // 黑名单匹配
        if (blackDTOS != null) {
            for (DynamicProperties.BlackDTO blackDTO : blackDTOS) {
                String id = blackDTO.getIndustryDept();
                String ym = blackDTO.getYearMonth();
                String it = blackDTO.getInstanceType();
                // 三个都可以缺省表示匹配所有
                if ((StringUtils.isBlank(id) || Objects.equals(id, industryDept)) &&
                        (StringUtils.isBlank(ym) || Objects.equals(ym, yearMonth)) &&
                        (StringUtils.isBlank(it) || Objects.equals(it, instanceType))) {
                    obj.setIsBlackCustomer(FlagType.YES.getDesc());
                }
            }
        }
    }

    private void cleanZoneName(ICleanZoneName obj) {
        String zoneName = obj.getZoneName();
        // 为空直接跳过
        if (!SoeCommonUtils.isNotBlank(zoneName)) {
            return;
        }
        Set<String> mainZoneNameMap = getMainZoneNameMap();

        obj.setIsMainZoneName(FlagType.getDesc(mainZoneNameMap.contains(zoneName))); // 主力园区
    }

    private void cleanInstanceType(ICleanInstanceType obj) {
        String instanceType = obj.getInstanceType();
        // 为空直接跳过
        if (!SoeCommonUtils.isNotBlank(instanceType)) {
            return;
        }

        String isMainInstanceType;
        String isMainUnInstanceType;
        String isBlackInstanceType;
        String isPurchaseInstanceType;
        String isPurchaseUnInstanceType;
        String isGenerationInstanceType;
        String isGenerationUnInstanceType;

        soeCleanService.cleanUnInstanceType(obj);
        String unInstanceType = obj.getUnInstanceType();

        // 主力机型
        Set<String> mainInstanceType = getMainInstanceType();
        isMainInstanceType = FlagType.getDesc(mainInstanceType.contains(instanceType));
        isMainUnInstanceType = FlagType.getDesc(mainInstanceType.contains(instanceType) || mainInstanceType.contains(unInstanceType));

        // 黑名单机型
        Set<String> blackInstanceType = getBlackInstanceType();
        isBlackInstanceType = FlagType.getDesc(blackInstanceType.contains(instanceType));

        // 采购机型
        Set<String> purchaseInstanceType = getPurchaseInstanceType();
        isPurchaseInstanceType = FlagType.getDesc(purchaseInstanceType.contains(instanceType));
        isPurchaseUnInstanceType = FlagType.getDesc(purchaseInstanceType.contains(instanceType) || purchaseInstanceType.contains(unInstanceType));

        // 新旧机型
        Set<String> generationInstanceType = getGenerationInstanceType();
        isGenerationInstanceType = GenerationEnum.getByBol(generationInstanceType.contains(instanceType));
        isGenerationUnInstanceType = GenerationEnum.getByBol(generationInstanceType.contains(instanceType) || generationInstanceType.contains(unInstanceType));

        obj.setIsMainInstanceType(isMainInstanceType); // 主力机型
        obj.setIsBlackInstanceType(isBlackInstanceType); // 黑名单机型
        obj.setIsPurchaseInstanceType(isPurchaseInstanceType); // 采购机型
        obj.setIsGenerationInstanceType(isGenerationInstanceType); // 新旧机型

        obj.setIsMainUnInstanceType(isMainUnInstanceType); // 主力机型(合并)
        obj.setIsPurchaseUnInstanceType(isPurchaseUnInstanceType); // 采购机型(合并)
        obj.setIsGenerationUnInstanceType(isGenerationUnInstanceType); // 新旧机型(合并)
    }

    /**
     * 清洗业务类型
     *
     * @param obj
     */
    private void cleanBizType(ICleanBizType obj) {
        String bizType = obj.getBizType();
        if (StringUtils.isBlank(bizType)){
            return;
        }
        if (Objects.equals(bizType, "外部业务")) {
            bizType = BizTypeEnum.OUTER.getName();
        } else if (Objects.equals(bizType, "内部业务")) {
            bizType = BizTypeEnum.INNER.getName();
        }
        obj.setBizType(bizType);

        // 外部行业，内部产品，否则都设为空值
        if (bizType.equals(BizTypeEnum.OUTER.getName())) {
            obj.setIndustryOrProduct(obj.getIndustryDept());
        } else if (bizType.equals(BizTypeEnum.INNER.getName())) {
            obj.setIndustryOrProduct(obj.getProductClass());
        } else {
            obj.setBizType(Constant.EMPTY_VALUE);
            obj.setIndustryOrProduct(Constant.EMPTY_VALUE);
        }
    }

    @Override
    public DwdDailyMrpV3DataDfDO transform(Object datum) {
        // 已经是【DwdDailyMrpV3DataDfDO】则直接返回
        if (datum instanceof DwdDailyMrpV3DataDfDO) {
            return (DwdDailyMrpV3DataDfDO) datum;
        }
        DwdDailyMrpV3DataDfDO ret = new DwdDailyMrpV3DataDfDO();
        // 根据继承的类型，进行转换
        if (datum instanceof ICustomerGetter)
            setCustomer(ret, (ICustomerGetter) datum);
        if (datum instanceof IProductGetter)
            setProduct(ret, (IProductGetter) datum);
        if (datum instanceof IProjectTypeGetter)
            setProjectType(ret, (IProjectTypeGetter) datum);
        if (datum instanceof IZoneGetter)
            setZone(ret, (IZoneGetter) datum);
        if (datum instanceof IRootGetter)
            setRoot(ret, (IRootGetter) datum);
        return ret;
    }

    /**
     * 设置客户属性
     *
     * @param ret    dwd 信息
     * @param getter 属性获取类
     */
    private void setCustomer(DwdDailyMrpV3DataDfDO ret, ICustomerGetter getter) {
        String industryDept = getter.getIndustryDept();
        String warZone = getter.getWarZone();
        String customerShortName = getter.getCustomerShortName();
        String uin = getter.getUin();
        String customerTabType = getter.getCustomerTabType();
        String newCustomerType = getter.getNewCustomerType();

        ret.setIndustryDept(industryDept);
        ret.setCrpWarZone(warZone);
        ret.setCustomerShortName(customerShortName);
        ret.setCustomerTabType(customerTabType);
        ret.setUin(uin);
        ret.setNewCustomerType(newCustomerType);
    }

    /**
     * 设置产品属性
     *
     * @param ret    dwd 信息
     * @param getter 属性获取类
     */
    private void setProduct(DwdDailyMrpV3DataDfDO ret, IProductGetter getter) {
        String product = getter.getProduct();
        String productClass = getter.getProductClass();
        String bizType = getter.getBizType();
        String appRole = getter.getAppRole();

        ret.setProduct(product);
        ret.setProductClass(productClass);
        ret.setBizType(bizType);
        ret.setAppRole(appRole);
    }

    /**
     * 设置项目类型属性
     *
     * @param ret    dwd 信息
     * @param getter 属性获取类
     */
    private void setProjectType(DwdDailyMrpV3DataDfDO ret, IProjectTypeGetter getter) {
        String projectType = getter.getProjectType();
        Integer isSpike = getter.getIsSpike();

        ret.setProjectType(projectType);
        ret.setIsSpike(isSpike);
    }

    /**
     * 设置可用区到境内外属性
     *
     * @param ret    dwd 信息
     * @param getter 属性获取类
     */
    private void setZone(DwdDailyMrpV3DataDfDO ret, IZoneGetter getter) {
        String customhouseTitle = getter.getCustomhouseTitle();
        String countryName = getter.getCountryName();
        String areaName = getter.getAreaName();
        String regionName = getter.getRegionName();
        String zoneName = getter.getZoneName();

        ret.setCustomhouseTitle(customhouseTitle);
        ret.setCountryName(countryName);
        ret.setAreaName(areaName);
        ret.setRegionName(regionName);
        ret.setZoneName(zoneName);
    }

    /**
     * 设置根属性
     *
     * @param ret    dwd 信息
     * @param getter 属性获取类
     */
    private void setRoot(DwdDailyMrpV3DataDfDO ret, IRootGetter getter) {
        String demandType = getter.getDemandType();
        String instanceType = getter.getInstanceType();
        String yearMonth = getter.getYearMonth();
        BigDecimal num1 = getter.getNum1();
        BigDecimal num2 = getter.getNum2();
        BigDecimal num3 = getter.getNum3();
        BigDecimal num4 = getter.getNum4();
        BigDecimal num5 = getter.getNum5();
        BigDecimal num6 = getter.getNum6();

        ret.setDemandType(demandType);
        ret.setInstanceType(instanceType);
        ret.setYearMonth(yearMonth);
        ret.setNum1(num1);
        ret.setNum2(num2);
        ret.setNum3(num3);
        ret.setNum4(num4);
        ret.setNum5(num5);
        ret.setNum6(num6);
    }
}
