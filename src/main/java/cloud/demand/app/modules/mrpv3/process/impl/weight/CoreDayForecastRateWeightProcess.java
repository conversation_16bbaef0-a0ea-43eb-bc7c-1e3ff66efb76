package cloud.demand.app.modules.mrpv3.process.impl.weight;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.mrpv2.enums.ForecastStatus;
import cloud.demand.app.modules.mrpv2.model.ForecastMatchRateCoreDayInfo;
import cloud.demand.app.modules.mrpv2.model.ForecastMatchRateCoreDayInfoList;
import cloud.demand.app.modules.mrpv2.model.ForecastMatchRateCoreDayReq;
import cloud.demand.app.modules.mrpv2.model.ForecastMatchRateCoreDayRes;
import cloud.demand.app.modules.mrpv2.service.QueryForecastMateRate;
import cloud.demand.app.modules.mrpv3.dto.req.MrpV3ReportReq;
import cloud.demand.app.modules.mrpv3.enums.MrpV3IndexFieldEnum;
import cloud.demand.app.modules.mrpv3.model.item.MrpV3DataItem;
import com.pugwoo.wooutils.collect.ListUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * 核天532准确率权重处理
 */
public class CoreDayForecastRateWeightProcess extends ForecastRateChangeWeightProcess {

    @Override

    protected void initChangeInfo() {
        MrpV3IndexFieldEnum coreDay532NewRate = MrpV3IndexFieldEnum.core_day_532_new_rate;
        MrpV3IndexFieldEnum coreDay532NewDistinctRate = MrpV3IndexFieldEnum.core_day_532_new_distinct_rate;
        MrpV3IndexFieldEnum originCoreDay532NewRate = MrpV3IndexFieldEnum.origin_core_day_532_new_rate;
        MrpV3IndexFieldEnum originCoreDay532NewDistinctRate = MrpV3IndexFieldEnum.origin_core_day_532_new_distinct_rate;

        Map<String,Function<MrpV3DataItem, ForecastMatchRateCoreDayInfo>> getMap = new HashMap<>();
        Map<String,BiConsumer<MrpV3DataItem, ForecastMatchRateCoreDayInfo>> setMap = new HashMap<>();

        getMap.put(coreDay532NewRate.name(), MrpV3DataItem::getCoreDay532NewRateInfo);
        getMap.put(coreDay532NewDistinctRate.name(), MrpV3DataItem::getCoreDay532NewRateInfo);
        getMap.put(originCoreDay532NewRate.name(), MrpV3DataItem::getOriginCoreDay532NewRateInfo);
        getMap.put(originCoreDay532NewDistinctRate.name(), MrpV3DataItem::getOriginCoreDay532NewRateInfo);

        setMap.put(coreDay532NewRate.name(), MrpV3DataItem::setCoreDay532NewRateInfo);
        setMap.put(coreDay532NewDistinctRate.name(), MrpV3DataItem::setCoreDay532NewRateInfo);
        setMap.put(originCoreDay532NewRate.name(), MrpV3DataItem::setOriginCoreDay532NewRateInfo);
        setMap.put(originCoreDay532NewDistinctRate.name(), MrpV3DataItem::setOriginCoreDay532NewRateInfo);

        this.changeInfoGetMap = getMap;
        this.changeInfoSetMap = setMap;
    }

    @Override
    protected ForecastMatchRateCoreDayRes getChangeRate(ForecastMatchRateCoreDayReq coreDayReq) {
        return SpringUtil.getBean(QueryForecastMateRate.class).queryCoreDay(coreDayReq);
    }
}
