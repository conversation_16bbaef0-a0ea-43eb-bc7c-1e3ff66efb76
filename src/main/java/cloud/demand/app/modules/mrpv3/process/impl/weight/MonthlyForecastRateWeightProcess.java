package cloud.demand.app.modules.mrpv3.process.impl.weight;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.mrpv2.enums.ForecastStatus;
import cloud.demand.app.modules.mrpv2.model.ForecastMatchRateCoreDayInfo;
import cloud.demand.app.modules.mrpv2.model.ForecastMatchRateCoreDayInfoList;
import cloud.demand.app.modules.mrpv2.model.ForecastMatchRateCoreDayReq;
import cloud.demand.app.modules.mrpv2.model.ForecastMatchRateCoreDayRes;
import cloud.demand.app.modules.mrpv2.service.QueryForecastMateRate;
import cloud.demand.app.modules.mrpv3.dto.req.MrpV3ReportReq;
import cloud.demand.app.modules.mrpv3.enums.MrpV3IndexFieldEnum;
import cloud.demand.app.modules.mrpv3.model.item.MrpV3DataItem;
import com.pugwoo.wooutils.collect.ListUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * 月均 532准确率权重处理
 */

public class MonthlyForecastRateWeightProcess extends ForecastRateChangeWeightProcess {

    /** 这里不是核天了，是月均532准确率 */
    @Override
    protected void initChangeInfo() {
        // 月均只看去重的
        MrpV3IndexFieldEnum monthly532NewDistinctRate = MrpV3IndexFieldEnum.monthly_532_new_distinct_rate;
        MrpV3IndexFieldEnum originMonthly532NewDistinctRate = MrpV3IndexFieldEnum.origin_monthly_532_new_distinct_rate;

        Map<String, Function<MrpV3DataItem, ForecastMatchRateCoreDayInfo>> getMap = new HashMap<>();
        Map<String, BiConsumer<MrpV3DataItem, ForecastMatchRateCoreDayInfo>> setMap = new HashMap<>();

        getMap.put(monthly532NewDistinctRate.name(), MrpV3DataItem::getMonthly532NewRateInfo);
        getMap.put(originMonthly532NewDistinctRate.name(), MrpV3DataItem::getOriginMonthly532NewRateInfo);

        setMap.put(monthly532NewDistinctRate.name(), MrpV3DataItem::setMonthly532NewRateInfo);
        setMap.put(originMonthly532NewDistinctRate.name(), MrpV3DataItem::setOriginMonthly532NewRateInfo);

        this.changeInfoGetMap = getMap;
        this.changeInfoSetMap = setMap;
    }

    @Override
    protected ForecastMatchRateCoreDayRes getChangeRate(ForecastMatchRateCoreDayReq coreDayReq) {
        return SpringUtil.getBean(QueryForecastMateRate.class).queryForecastMatchRate(coreDayReq);
    }

}
