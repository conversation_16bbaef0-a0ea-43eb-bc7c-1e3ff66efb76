package cloud.demand.app.modules.mrpv3.task.dwd.scale;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.enums.DemandTypeEnum;
import cloud.demand.app.modules.mrpv2.enums.NewCustomerTypeEnum;
import cloud.demand.app.modules.mrpv2.enums.ProjectTypeEnum;
import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.mrpv3.DynamicProperties;
import cloud.demand.app.modules.mrpv3.entity.DwdDailyMrpV3DataDfDO;
import cloud.demand.app.modules.mrpv3.entity.dwd.scale.CoreDayScaleDO;
import cloud.demand.app.modules.mrpv3.enums.MrpV3IndexEnum;
import cloud.demand.app.modules.mrpv3.enums.MrpV3TaskEnum;
import cloud.demand.app.modules.mrpv3.task.DwdMrpCommonWork;
import cloud.demand.app.modules.mrpv3.task.IgnoreBlackCustomerCommonWork;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.enums.InsertType;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.*;
import java.util.function.Predicate;

/** 月核天规模 */
@Service
@Slf4j
public class CoreDayScaleWork extends IgnoreBlackCustomerCommonWork {
    @Resource
    private MrpV2DictService mrpV2DictService;

    @Override
    public InsertType getInsertType() {
        return DynamicProperties.getInsertTypeWithIndex(getIndex());
    }

    @TaskLog(taskName = "MRPV3@CoreDayScaleWork")
    @Override
    public List<?> getData(String statTime) {
        // step1：参数准备
        String sql = ORMUtils.getSql("/sql/mrp_v3/dwd/scale/mrp_v3_core_day_scale.sql");
        YearMonth startYearMonth = DynamicProperties.getStartYearMonth(); // 起始年月

        if (getInsertType() == InsertType.INCR){
            // 如果是增量写入则默认起始为 (切片-1)当月
            LocalDate parse = LocalDate.parse(statTime).plusDays(-1);
            startYearMonth = YearMonth.of(parse.getYear(), parse.getMonthValue());
        }

        // step1-1：准备头部和毛刺
        List<String> allLongTailBurrUin = mrpV2DictService.getAllLongTailBurrUin(); // 毛刺
        List<String> bigCustomerShortName = mrpV2DictService.getBigCustomerShortName(); // 头部客户简称

        Set<String> uinSet = new HashSet<>(allLongTailBurrUin);

        // step1-3：sql 替换
        sql = SimpleSqlBuilder.doReplace(sql,"start_year_month", SopDateUtils.getYearMonth(startYearMonth));
        sql = SimpleSqlBuilder.doReplace(sql, "header_customer_uin", StringUtils.join(uinSet, "','"));
        sql = SimpleSqlBuilder.doReplace(sql, "header_customer_short_name", StringUtils.join(bigCustomerShortName, "','"));

        log.info("sql:{}", sql);
        // step2：查询月核天数据
        // step3：去重处理（去重会屏蔽需求类型）
        List<CoreDayScaleDO> ret = ckcldStdCrpDBHelper.getRaw(CoreDayScaleDO.class, sql);
        List<CoreDayScaleDO> newRet = new ArrayList<>();
        ret.forEach(item->{
            CoreDayScaleDO distinct = CoreDayScaleDO.copy(item);
            distinct.setDemandType(null);
            BigDecimal billCoreByDayNum = distinct.getBillCoreByDayNum();
            BigDecimal serveCoreByDayNum = distinct.getServeCoreByDayNum();
            distinct.setBillCoreByDayDistinctNum(billCoreByDayNum);
            distinct.setServeCoreByDayDistinctNum(serveCoreByDayNum);
            distinct.setBillCoreByDayNum(null); // 避免 double
            distinct.setServeCoreByDayNum(null); // 避免 double
            newRet.add(distinct);
            boolean billIsNotNull = !SoeCommonUtils.isNullOrZone(billCoreByDayNum);
            boolean serverIsNotNull = !SoeCommonUtils.isNullOrZone(serveCoreByDayNum);
            if (billIsNotNull || serverIsNotNull) {
                String billDemandType = DemandTypeEnum.getDemandTypeByNum(billCoreByDayNum);
                String serverDemandType = DemandTypeEnum.getDemandTypeByNum(serveCoreByDayNum);
                // 三组情况：
                // 1. 都不为0，需求类型相同，则一条记录存，否则存两条
                // 2. 计费不为 0，服务为 0，只存一条计费的需求类型
                // 3. 计费为 0，服务不为 0，只存一条服务的需求类型
                if (billIsNotNull && serverIsNotNull) {
                    if (Objects.equals(billDemandType, serverDemandType)) {
                        item.setDemandType(billDemandType);
                        newRet.add(item);
                    } else {
                        CoreDayScaleDO server = CoreDayScaleDO.copy(item);
                        item.setDemandType(billDemandType); // 设置计费需求类型
                        item.setServeCoreByDayNum(null); // 清理服务核天量
                        server.setDemandType(serverDemandType); // 设置服务需求类型
                        server.setBillCoreByDayNum(null); // 清理计费核天量
                        newRet.add(item);
                        newRet.add(server);
                    }
                } else if (billIsNotNull) {
                    item.setDemandType(billDemandType);
                    newRet.add(item);
                } else {
                    item.setDemandType(serverDemandType);
                    newRet.add(item);
                }
            }
        });
        return newRet;
    }

    @Override
    protected Predicate<DwdDailyMrpV3DataDfDO> getFilter() {
        // 核天只看重点或者非中长尾的执行量
        return (item)-> Objects.equals(item.getProjectType(), ProjectTypeEnum.KEY_PROJECT.getName());
    }

    @Override
    public String getIndex() {
        return MrpV3IndexEnum.coreDayScale.getName();
    }

    @Override
    public ITaskEnum getEnum() {
        return MrpV3TaskEnum.MRP_V3_CORE_DAY_SCALE;
    }
}
