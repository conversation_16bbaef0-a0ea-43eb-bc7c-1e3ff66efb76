package cloud.demand.app.modules.mrpv3.web;


import cloud.demand.app.modules.mrpv3.task.dwd.forecast.PplCoreDayForecast532NewWork;
import cloud.demand.app.modules.mrpv3.task.dwd.forecast.PplForecastAdvanceWeekWork;
import cloud.demand.app.modules.mrpv3.task.dwd.forecast.PplForecastNewestWork;
import cloud.demand.app.modules.mrpv3.task.dwd.order.YunxiaoOrderWork;
import cloud.demand.app.modules.mrpv3.task.dwd.purchase.PurchaseWork;
import cloud.demand.app.modules.mrpv3.task.dwd.scale.CoreDayScaleWork;
import cloud.demand.app.modules.mrpv3.task.dwd.scale.DBDailyScaleWork;
import cloud.demand.app.modules.mrpv3.task.dwd.scale.DailyScaleWork;
import cloud.demand.app.modules.mrpv3.task.dwd.forecast.PplForecast532NewWork;
import cloud.demand.app.modules.mrpv3.task.dwd.scale.MonthlyScaleWork;
import cloud.demand.app.modules.mrpv3.task.dws.DwsMrpV3Work;
import cloud.demand.app.modules.mrpv3.task.monitor.MrpV3TotalRateWork;
import cloud.demand.app.modules.sop.domain.VersionReq;
import cloud.demand.app.modules.sop.domain.VersionTypeReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.exception.ITException;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;

@JsonrpcController("/mrp-v3/task")
@Slf4j
public class MrpV3TaskController {

    // =============== 任务 ================
    @Resource
    private DailyScaleWork dailyScaleWork;

    @Resource
    private DBDailyScaleWork dbDailyScaleWork;

    @Resource
    private CoreDayScaleWork coreDayScaleWork;

    @Resource
    private MonthlyScaleWork monthlyScaleWork;

    @Resource
    private PplForecast532NewWork pplForecast532Work;

    @Resource
    private PplCoreDayForecast532NewWork pplCoreDayForecast532Work;

    @Resource
    private PplForecastAdvanceWeekWork pplForecastAdvanceWeekWork;

    @Resource
    private PplForecastNewestWork pplForecastNewestWork;

    @Resource
    private PurchaseWork purchaseWork;

    @Resource
    private YunxiaoOrderWork yunxiaoOrderWork;

    @Resource
    private DwsMrpV3Work dwsMrpV3Work;

    @Resource
    private MrpV3TotalRateWork totalRateWork;

    /**
     * 初始化
     */
    @RequestMapping
    public void initTask(@JsonrpcParam @Valid VersionReq versionReq) {
        for (String version : versionReq.getVersion()) {
            dwsMrpV3Work.initAll(version);
        }
    }

    /**
     * 执行
     */
    @RequestMapping
    public void doTask(@JsonrpcParam @Valid VersionTypeReq versionReq) {
        String type = versionReq.getType();
        for (String version : versionReq.getVersion()) {
            switch (type) {
                case "scale_daily":
                    dailyScaleWork.genData(version);
                    break;
                case "db_scale_daily":
                    dbDailyScaleWork.genData(version);
                    break;
                case "scale_core_day":
                    coreDayScaleWork.genData(version);
                    break;
                case "scale_monthly":
                    monthlyScaleWork.genData(version);
                    break;
                case "forecast_532_new":
                    pplForecast532Work.genData(version);
                    break;
                case "forecast_core_day_532_new":
                    pplCoreDayForecast532Work.genData(version);
                    break;
                case "forecast_advance_week":
                    pplForecastAdvanceWeekWork.genData(version);
                    break;
                case "forecast_newest":
                    pplForecastNewestWork.work();
                    break;
                case "purchase":
                    purchaseWork.genData(version);
                    break;
                case "yunxiao_order":
                    yunxiaoOrderWork.genData(version);
                    break;
                case "dws_mrp_v3":
                    dwsMrpV3Work.genData(version);
                    break;
                case "monitor_total_rate":
                    totalRateWork.genData(version);
                    break;
                default:
                    throw new ITException("type 错误");
            }
        }
    }


}
