package cloud.demand.app.modules.mrpv3.entity.dwd.scale;

import cloud.demand.app.modules.mrpv3.entity.getter.common.ISimpleGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DBDailyScaleDO implements ISimpleGetter {

    /** 业务类型范围 */
    @Column("biz_range_type")
    private String bizType;

    /** 新客户类型 */
    @Column("new_customer_type")
    private String newCustomerType;

    /** 项目类型 */
    @Column("project_type")
    private String projectType;

    /** 产品大类 */
    @Column("product_class")
    private String productClass;

    /** 原始行业部门 */
    @Column("origin_industry_dept")
    private String industryDept;

    /** 境内外 */
    @Column("customhouse_title")
    private String customhouseTitle;

    /** 地域名称 */
    @Column("region_name")
    private String regionName;

    /** 可用区名称 */
    @Column("zone_name")
    private String zoneName;

    /** 实例类型 */
    @Column("instance_type")
    private String instanceType;

    /** 客户uin */
    @Column("uin")
    private String uin;

    /** 客户简称 */
    @Column("customer_short_name")
    private String customerShortName;

    @Column("customer_tab_type")
    private String customerTabType;

    /** 产品 */
    @Column("product")
    private String product;

    /** app_role */
    @Column("app_role")
    private String appRole;

    /** 年月 */
    @Column("year_month") //
    private String yearMonth;

    /** 计费变化量【内存】 */
    @Column("diff_bill_mem_num")
    private BigDecimal diffBillMemNum;

    /** 服务变化量【内存】 */
    @Column("diff_service_mem_num")
    private BigDecimal diffServiceMemNum;

    /** 计费存量【内存】 */
    @Column("stock_bill_mem_num")
    private BigDecimal stockBillMemNum;

    /** 服务存量【内存】 */
    @Column("stock_service_mem_num")
    private BigDecimal stockServiceMemNum;

    /** 计费变化量【存储】 */
    @Column("diff_bill_disk_num")
    private BigDecimal diffBillDiskNum;

    /** 服务变化量【存储】 */
    @Column("diff_service_disk_num")
    private BigDecimal diffServiceDiskNum;

    /** 计费存量【存储】 */
    @Column("stock_bill_disk_num")
    private BigDecimal stockBillDiskNum;

    /** 服务存量【存储】 */
    @Column("stock_service_disk_num")
    private BigDecimal stockServiceDiskNum;

    // =============== 扩展字段 ================

    /** 去重计费【内存】 */
    private BigDecimal diffBillDistinctMemNum;

    /** 去重服务【内存】 */
    private BigDecimal diffServiceDistinctMemNum;

    /** 去重计费【存储】 */
    private BigDecimal diffBillDistinctDiskNum;

    /** 去重服务【存储】 */
    private BigDecimal diffServiceDistinctDiskNum;

    /** 需求类型 */
    private String demandType;

    public static DBDailyScaleDO copy(DBDailyScaleDO item) {
        DBDailyScaleDO ret = new DBDailyScaleDO();
        ret.setBizType(item.getBizType());
        ret.setNewCustomerType(item.getNewCustomerType());
        ret.setProjectType(item.getProjectType());
        ret.setProductClass(item.getProductClass());
        ret.setIndustryDept(item.getIndustryDept());
        ret.setCustomhouseTitle(item.getCustomhouseTitle());
        ret.setRegionName(item.getRegionName());
        ret.setZoneName(item.getZoneName());
        ret.setInstanceType(item.getInstanceType());
        ret.setUin(item.getUin());
        ret.setCustomerShortName(item.getCustomerShortName());
        ret.setCustomerTabType(item.getCustomerTabType());
        ret.setProduct(item.getProduct());
        ret.setAppRole(item.getAppRole());
        ret.setYearMonth(item.getYearMonth());
        ret.setDiffBillMemNum(item.getDiffBillMemNum());
        ret.setDiffServiceMemNum(item.getDiffServiceMemNum());
        ret.setStockBillMemNum(item.getStockBillMemNum());
        ret.setStockServiceMemNum(item.getStockServiceMemNum());
        ret.setDiffBillDiskNum(item.getDiffBillDiskNum());
        ret.setDiffServiceDiskNum(item.getDiffServiceDiskNum());
        ret.setStockBillDiskNum(item.getStockBillDiskNum());
        ret.setStockServiceDiskNum(item.getStockServiceDiskNum());
        ret.setDiffBillDistinctMemNum(item.getDiffBillDistinctMemNum());
        ret.setDiffServiceDistinctMemNum(item.getDiffServiceDistinctMemNum());
        ret.setDiffBillDistinctDiskNum(item.getDiffBillDistinctDiskNum());
        ret.setDiffServiceDistinctDiskNum(item.getDiffServiceDistinctDiskNum());
        ret.setDemandType(item.getDemandType());
        return ret;
    }

    @JsonIgnore
    @Override
    public String getWarZone() {
        return null;
    }

    /** @see cloud.demand.app.modules.mrpv3.enums.MrpV3IndexEnum#dbDailyScale */
    @Override
    public BigDecimal getNum1() {
        return getDiffBillMemNum(); // 计费变化
    }

    @Override
    public BigDecimal getNum2() {
        return getDiffServiceMemNum(); // 服务变化
    }

    @Override
    public BigDecimal getNum3() {
        return getDiffBillDistinctMemNum(); // 去重计费变化
    }

    @Override
    public BigDecimal getNum4() {
        return getDiffServiceDistinctMemNum(); // 去重服务存量
    }

    @Override
    public BigDecimal getNum5() {
        return getStockBillMemNum(); // 计费存量
    }

    @Override
    public BigDecimal getNum6() {
        return getStockServiceMemNum(); // 服务存量
    }

    @JsonIgnore
    @Override
    public String getCountryName() {
        return null;
    }

    @JsonIgnore
    @Override
    public String getAreaName() {
        return null;
    }
}
