package cloud.demand.app.modules.mrpv3.task.dwd.forecast.origin;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.mrpv2.utils.PaasUtils;
import cloud.demand.app.modules.mrpv3.DynamicProperties;
import cloud.demand.app.modules.mrpv3.entity.dwd.forecast.PplForecastDO;
import cloud.demand.app.modules.mrpv3.enums.MrpV3IndexEnum;
import cloud.demand.app.modules.mrpv3.enums.MrpV3TaskEnum;
import cloud.demand.app.modules.mrpv3.task.DwdMrpCommonWork;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.YearMonth;
import java.util.List;

/** 预测-532 新版 */
@Service
@Slf4j
public class OriginPplForecast532NewWork extends DwdMrpCommonWork {

    @TaskLog(taskName = "MRPV3@PplForecast532NewWork")
    @Override
    public List<?> getData(String statTime) {
        String sql = ORMUtils.getSql("/sql/mrp_v3/dwd/forecast/origin/mrp_v3_origin_ppl_forecast_532_new.sql");
        sql = PaasUtils.sqlReplacePAAS(sql);
        YearMonth startYearMonth = DynamicProperties.getStartYearMonth();
        sql = SimpleSqlBuilder.doReplace(sql,"start_year_month", SopDateUtils.getYearMonth(startYearMonth));

        log.info("sql:{}", sql);

        return ckcldStdCrpDBHelper.getRaw(PplForecastDO.class, sql);
    }

    @Override
    public String getIndex() {
        return MrpV3IndexEnum.originPplForecast532New.getName();
    }

    @Override
    public ITaskEnum getEnum() {
        return MrpV3TaskEnum.MRP_V3_ORI_PPL_FORECAST_532_NEW;
    }
}
