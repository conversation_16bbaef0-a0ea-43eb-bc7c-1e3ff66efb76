package cloud.demand.app.modules.mrpv3.entity;

import cloud.demand.app.modules.mrpv2.model.clean.ICleanDemand;
import cloud.demand.app.modules.mrpv3.model.clean.IMrpV3Clean;
import cloud.demand.app.modules.soe.model.clean.IRegionClean;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.math.BigDecimal;

/** 行业数据看板 v3 */
@Data
@Table("dwd_daily_mrp_v3_data_df")
public class DwdDailyMrpV3DataDfDO extends DailyMrpV3CommonDO implements IRegionClean, ICleanDemand, IMrpV3Clean {

    /** 指标 */
    @Column("index")
    private String index;

    /** 核数 */
    @Column("num1")
    private BigDecimal num1;

    /** 核数 */
    @Column("num2")
    private BigDecimal num2;

    /** 核数 */
    @Column("num3")
    private BigDecimal num3;

    /** 核数 */
    @Column("num4")
    private BigDecimal num4;

    /** 核数 */
    @Column("num5")
    private BigDecimal num5;

    @Column("num6")
    private BigDecimal num6;


    /** 是否为毛刺 */
    private Boolean isBurr;

    /** 项目类型 */
    private Integer isSpike;

    @Override
    public String getCustomerUin() {
        return getUin();
    }

    @Override
    public String getWarZoneName() {
        return getCrpWarZone();
    }

    @Override
    public void setWarZoneName(String warZoneName) {
        setCrpWarZone(warZoneName);
    }

    @Override
    public void setOrderType(String orderType) {
    }

    @Override
    public String getWarZone() {
        return getCrpWarZone();
    }

    @Override
    public void setWarZone(String warZone) {
        setCrpWarZone(warZone);
    }
}
