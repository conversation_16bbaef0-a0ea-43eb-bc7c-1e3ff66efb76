package cloud.demand.app.modules.mrpv3.entity.dwd.forecast;

import cloud.demand.app.modules.mrpv3.entity.getter.common.ISimpleGetter;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/** ppl预测 */
@Data
public class PplForecastDO implements ISimpleGetter {
    /** 业务类型 */
    @Column("biz_type")
    private String bizType;

    /** 行业部门 */
    @Column("industry_dept")
    private String industryDept;

    /** 境内外 */
    @Column("customhouse_title")
    private String customhouseTitle;

    /** 地域名称 */
    @Column("region_name")
    private String regionName;

    /** 可用区名称 */
    @Column("zone_name")
    private String zoneName;

    /** 实例类型 */
    @Column("instance_type")
    private String instanceType;

    /** 客户 UIN */
    @Column("uin")
    private String uin;

    /** 需求类型 */
    @Column("demand_type")
    private String demandType;

    /** 数据产品 */
    @Column("data_product")
    private String dataProduct;

    /** 产品大类 */
    @Column("product_class")
    private String productClass;

    /** 年月 */
    @Column("year_month")
    private String yearMonth;

    /** 客户简称 */
    @Column("customer_short_name")
    private String customerShortName;

    /** crp战区 */
    @Column("war_zone_name")
    private String warZoneName;

    /** 毛刺 */
    @Column("is_spike")
    private Integer isSpike;

    @Column("num")
    private BigDecimal num;

    // =============== 扩充字段 ================

    /** 新客户分类 */
    private String newCustomerType;

    /** 项目类型 */
    private String projectType;

    @Override
    public String getWarZone() {
        return getWarZoneName();
    }

    @Override
    public String getProduct() {
        return getDataProduct();
    }

    @Override
    public BigDecimal getNum1() {
        return getNum();
    }

    @Override
    public String getCountryName() {
        return null;
    }

    @Override
    public String getAreaName() {
        return null;
    }
}
