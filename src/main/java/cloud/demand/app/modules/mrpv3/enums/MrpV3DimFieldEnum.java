package cloud.demand.app.modules.mrpv3.enums;

import cloud.demand.app.modules.mrpv3.enums.field.*;
import cloud.demand.app.modules.mrpv3.enums.field.IGpuType;
import cloud.demand.app.modules.mrpv3.enums.indexField.IMrpV3ComputeIndexField;
import cloud.demand.app.modules.mrpv3.model.field.BigCustomerShortNameSql;
import cloud.demand.app.modules.mrpv3.model.field.IDynamicFieldSql;
import cloud.demand.app.modules.mrpv3.model.item.MrpV3DataItem;
import cloud.demand.app.modules.soe.model.fields.*;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.SneakyThrows;
import org.apache.commons.lang3.ObjectUtils;
import org.ehcache.impl.internal.concurrent.ConcurrentHashMap;
import yunti.boot.exception.BizException;
import yunti.boot.exception.ITException;

import java.time.LocalDate;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 维度字段枚举
 */
@Getter
@AllArgsConstructor
public enum MrpV3DimFieldEnum implements IFieldGetSetEnum{
    // =============== 固定字段 ================
    statTime("切片时间", (obj) -> ((IStatTime) obj).getStatTime(),(obj,value) -> {
        // 兼容处理
        if (value instanceof Date){
            ((IStatTime) obj).setStatTime(DateUtils.toLocalDate(((Date) value)));
        }else{
            ((IStatTime) obj).setStatTime((LocalDate) value);
        }
    }),
    yearMonth("年月", (obj) -> ((IYearMonth) obj).getYearMonth(),(obj,value) -> ((IYearMonth) obj).setYearMonth((String) value)),
    yearQuarter("年季度", (obj) -> ((IYearQuarter) obj).getYearQuarter(),(obj,value) -> ((IYearQuarter) obj).setYearQuarter((String) value)),
    product("产品", (obj) -> ((IProduct) obj).getProduct(), (obj,value) -> ((IProduct) obj).setProduct((String) value)), // CVM or GPU, 目前只有 CVM
    productClass("产品子类", (obj) -> ((IProductClass) obj).getProductClass(), (obj,value) -> ((IProductClass) obj).setProductClass((String) value)), // CVM&CBS + PAAS
    newCustomerType("客户分类", (obj) -> ((INewCustomerType) obj).getNewCustomerType(), (obj,value) -> ((INewCustomerType) obj).setNewCustomerType((String) value)), // 新客户分类
    oldCustomerType("老客户分类", (obj) -> ((IOldCustomerType) obj).getOldCustomerType(), (obj,value) -> ((IOldCustomerType) obj).setOldCustomerType((String) value)), // 老客户分类
    customerTabType("客户类型", (obj) -> ((ICustomerTabType) obj).getCustomerTabType(), (obj,value) -> ((ICustomerTabType) obj).setCustomerTabType((String) value)), // 客户类型
    projectType("项目类型", (obj) -> ((IProjectType) obj).getProjectType(), (obj,value) -> ((IProjectType) obj).setProjectType((String) value)), // 重点项目，常规项目，不参与
    bizType("业务类型", (obj) -> ((IBizType) obj).getBizType(), (obj,value) -> ((IBizType) obj).setBizType((String) value)), // 内领业务，外部行业
    demandType("需求类型", (obj) -> ((IDemandType) obj).getDemandType(), (obj,value) -> ((IDemandType) obj).setDemandType((String) value)), // 新增，弹性，退回
    appRole("appRole", (obj) -> ((IAppRole) obj).getAppRole(), (obj,value) -> ((IAppRole) obj).setAppRole((String) value)), // EMR,EKS 固定映射 对应产品大类
    zoneName("可用区", (obj) -> ((IZoneName) obj).getZoneName(), (obj,value) -> ((IZoneName) obj).setZoneName((String) value)),
    regionName("地域", (obj) -> ((IRegionName) obj).getRegionName(), (obj,value) -> ((IRegionName) obj).setRegionName((String) value)),
    areaName("区域", (obj) -> ((IAreaName) obj).getAreaName(), (obj,value) -> ((IAreaName) obj).setAreaName((String) value)),
    countryName("国家", (obj) -> ((ICountryName) obj).getCountryName(), (obj,value) -> ((ICountryName) obj).setCountryName((String) value)),
    customhouseTitle("境内外", (obj) -> ((ICustomhouseTitle) obj).getCustomhouseTitle(), (obj,value) -> ((ICustomhouseTitle) obj).setCustomhouseTitle((String) value)),
    isMainZoneName("是否为主力园区", (obj) -> ((IIsMainZoneName) obj).getIsMainZoneName(), (obj,value) -> ((IIsMainZoneName) obj).setIsMainZoneName((String) value)),
    instanceType("实例类型", (obj) -> ((IInstanceType) obj).getInstanceType(), (obj,value) -> ((IInstanceType) obj).setInstanceType((String) value)),
    unInstanceType("合并实例类型", (obj) -> ((IUnInstanceType) obj).getUnInstanceType(), (obj,value) -> ((IUnInstanceType) obj).setUnInstanceType((String) value)),
    gpuType("GPU类型", (obj) -> ((IGpuType) obj).getGpuType(), (obj,value) -> ((IGpuCardType) obj).setGpuCardType((String) value)),
    gpuCardType("GPU卡型", (obj) -> ((IGpuCardType) obj).getGpuCardType(), (obj,value) -> ((IGpuCardType) obj).setGpuCardType((String) value)),
    isMainInstanceType("是否为主力机型", (obj) -> ((IIsMainInstanceType) obj).getIsMainInstanceType(), (obj,value) -> ((IIsMainInstanceType) obj).setIsMainInstanceType((String) value)),
    isMainUnInstanceType("是否为主力机型(合并)", (obj) -> ((IIsMainUnInstanceType) obj).getIsMainUnInstanceType(), (obj,value) -> ((IIsMainUnInstanceType) obj).setIsMainUnInstanceType((String) value)),
    isBlackInstanceType("是否为黑名单机型", (obj) -> ((IIsBlackInstanceType) obj).getIsBlackInstanceType(), (obj,value) -> ((IIsBlackInstanceType) obj).setIsBlackInstanceType((String) value)),
    isBlackCustomer("是否为客户黑名单", (obj) -> ((IIsBlackCustomer) obj).getIsBlackCustomer(), (obj,value) -> ((IIsBlackCustomer) obj).setIsBlackCustomer((String) value)),
    isPurchaseInstanceType("采购机型", (obj) -> ((IIsPurchaseInstanceType) obj).getIsPurchaseInstanceType(), (obj,value) -> ((IIsPurchaseInstanceType) obj).setIsPurchaseInstanceType((String) value)),
    isPurchaseUnInstanceType("采购机型(合并)", (obj) -> ((IIsPurchaseUnInstanceType) obj).getIsPurchaseUnInstanceType(), (obj,value) -> ((IIsPurchaseUnInstanceType) obj).setIsPurchaseUnInstanceType((String) value)),
    isGenerationInstanceType("新旧机型", (obj) -> ((IIsGenerationInstanceType) obj).getIsGenerationInstanceType(), (obj,value) -> ((IIsGenerationInstanceType) obj).setIsGenerationInstanceType((String) value)),
    isGenerationUnInstanceType("新旧机型(合并)", (obj) -> ((IIsGenerationUnInstanceType) obj).getIsGenerationUnInstanceType(), (obj,value) -> ((IIsGenerationUnInstanceType) obj).setIsGenerationUnInstanceType((String) value)),
    industryDept("行业部门", (obj) -> ((IIndustryDept) obj).getIndustryDept(), (obj,value) -> ((IIndustryDept) obj).setIndustryDept((String) value)),
    industryOrProduct("行业部门或产品", (obj) -> ((IIndustryOrProduct) obj).getIndustryOrProduct(), (obj,value) -> ((IIndustryOrProduct) obj).setIndustryOrProduct((String) value)),
    crpWarZone("CRP战区", (obj) -> ((ICrpWarZone) obj).getCrpWarZone(), (obj,value) -> ((ICrpWarZone) obj).setCrpWarZone((String) value)),
    customerShortName("客户简称", (obj) -> ((ICustomerShortName) obj).getCustomerShortName(), (obj,value) -> ((ICustomerShortName) obj).setCustomerShortName((String) value)),
    unCustomerShortName("通用客户简称", (obj) -> ((IUnCustomerShortName) obj).getUnCustomerShortName(), (obj,value) -> ((IUnCustomerShortName) obj).setUnCustomerShortName((String) value)),
    uin("客户uin", (obj) -> ((IUin) obj).getUin(), (obj,value) -> ((IUin) obj).setUin((String) value)),
    incomeSalesDesc("销售通路", (obj) -> ((IIncomeSalesDesc) obj).getIncomeSalesDesc(), (obj,value) -> ((IIncomeSalesDesc) obj).setIncomeSalesDesc((String) value)),
    customerRange("客户内外部类型", (obj) -> ((ICustomerRange) obj).getCustomerRange(), (obj,value) -> ((ICustomerRange) obj).setCustomerRange((Integer) value)),

    // =============== 动态字段 ================
    bigCustomerShortName("大客户简称",
            (obj) -> ((IBigCustomerShortName) obj).getBigCustomerShortName(),
            (obj,value) -> ((IBigCustomerShortName) obj).setBigCustomerShortName((String) value),
            "big_customer_short_name",
            BigCustomerShortNameSql.class),
    ;

    private final String name; // 字段名

    private final Function<Object, Object> fieldGetter; // getter

    private final BiConsumer<Object, Object> fieldSetter; // setter

    private final boolean showAble; // 是否给前端展示

    private final boolean isDynamic; // 是否是动态字段

    private final String dynamicColumn; // 动态字段名

    private final Class<? extends IDynamicFieldSql> dynamicFieldClass; // 动态字段的 sql 实现类

    MrpV3DimFieldEnum(String name, Function<Object, Object> fieldGetter,BiConsumer<Object, Object> fieldSetter) {
        this(name, fieldGetter,fieldSetter, true,false,null,null);
    }

    MrpV3DimFieldEnum(String name, Function<Object, Object> fieldGetter,BiConsumer<Object, Object> fieldSetter,String dynamicColumn,Class<? extends IDynamicFieldSql> dynamicFieldClass) {
        this(name, fieldGetter,fieldSetter, true,true,dynamicColumn,dynamicFieldClass);
    }

    private final static Map<String, Function<Object, Object>> fieldMap = Arrays.stream(values())
            .collect(Collectors.toMap(Enum::name, MrpV3DimFieldEnum::getFieldGetter));

    // 动态字段 class 的 ThreadLocal
    private static final ThreadLocal<Map<Class<? extends IDynamicFieldSql>,IDynamicFieldSql>> dynamicFieldSqlLocal = new InheritableThreadLocal<>();

    /**
     * 更加动态字段的 class 获取 sql
     * @param dynamicFieldClass 动态字段的 class
     * @return
     */
    @SneakyThrows
    public static String getDynamicFieldSql(Class<? extends IDynamicFieldSql> dynamicFieldClass) {
        if (dynamicFieldClass == null){
            return null;
        }
        Map<Class<? extends IDynamicFieldSql>, IDynamicFieldSql> fieldSqlMap = dynamicFieldSqlLocal.get();
        if (fieldSqlMap == null){
            fieldSqlMap = new ConcurrentHashMap<>();
            dynamicFieldSqlLocal.set(fieldSqlMap);
        }
        IDynamicFieldSql dynamicFieldSql = fieldSqlMap.get(dynamicFieldClass);
        if (dynamicFieldSql == null){
            dynamicFieldSql = dynamicFieldClass.getDeclaredConstructor().newInstance();
            fieldSqlMap.put(dynamicFieldClass,dynamicFieldSql);
        }
        return dynamicFieldSql.getSelectSql();
    }
    /**
     * 取的是 Enum::name
     *
     * @return 字段集合
     */
    public static Set<String> getFieldNames() {
        return Arrays.stream(MrpV3DimFieldEnum.values()).map(Enum::name).collect(Collectors.toSet());
    }

    /**
     * 只看展示的维度
     */
    public static List<FieldInfo> getFields() {
        List<FieldInfo> ret = new ArrayList<>();
        for (MrpV3DimFieldEnum value : values()) {
            if (value.showAble) {
                ret.add(new FieldInfo(value.name(), value.getName()));
            }
        }
        return ret;
    }

    /**
     * 根据字段名称获取字段值
     */
    public static <T> Object getValue(String fieldName, T t) {
        Function<Object, Object> fieldGetter = fieldMap.get(fieldName);
        if (fieldGetter == null) {
            throw new ITException(String.format("字段找不到，字段名称：【%s】", ObjectUtils.defaultIfNull(fieldName, "null")));
        }
        return fieldGetter.apply(t);
    }

    /** 聚合 */
    public static List<MrpV3DataItem> groupBy(List<MrpV3DataItem> data, List<String> dims,List<String> index) {
        if (ListUtils.isEmpty(data)){
            return data;
        }
        // 字段维度数组，这里用来做聚合的 key
        MrpV3DimFieldEnum[] dimsEnum = MrpV3DimFieldEnum.getDimEnum(dims);
        MrpV3IndexFieldEnum[] indexEnum = MrpV3IndexFieldEnum.getIndexEnum(index);
        // 分组处理
        Map<String,MrpV3DataItem> groupMap = new LinkedHashMap<>();
        for (MrpV3DataItem item : data) {
            String dimsKey = item.getDimsKey(dimsEnum);
            MrpV3DataItem mrpV3DataItem = groupMap.get(dimsKey);
            if (mrpV3DataItem == null){
                mrpV3DataItem = item.copyDimFields(dimsEnum); // 维度字段
                IMrpV3ComputeIndexField.copyComputeIndexField(mrpV3DataItem,item,indexEnum); // 拷贝指标（不涉及加权，对 100%准确率但是加权=0 的展示 100%）
                groupMap.put(dimsKey,mrpV3DataItem);
            }else {
                IMrpV3ComputeIndexField.sumIndexField(mrpV3DataItem,item,indexEnum); // 累加指标（不能在初始化的时候累计，这里可能 100%，但是权重是 0）
            }
        }
        return new ArrayList<>(groupMap.values());
    }

    public static void check(List<String> dims) {
        Set<String> dimSet = getFieldNames();
        for (String dim : dims) {
            if (!dimSet.contains(dim)){
                throw new BizException(String.format("字段找不到，字段名称：【%s】", dim));
            }
        }
    }

    public static MrpV3DimFieldEnum[] getEnumList(List<String> groupBy) {
        if (ListUtils.isEmpty(groupBy)){
            return null;
        }
        MrpV3DimFieldEnum[] ret = new MrpV3DimFieldEnum[groupBy.size()];
        Map<String, MrpV3DimFieldEnum> enumMap = Arrays.stream(MrpV3DimFieldEnum.values()).collect(Collectors.toMap(MrpV3DimFieldEnum::name, item -> item));
        for (int i = 0; i < groupBy.size(); i++) {
            ret[i] = enumMap.get(groupBy.get(i));
        }
        return ret;
    }

    public static MrpV3DimFieldEnum[] getDimEnum(List<String> groupBy) {
        MrpV3DimFieldEnum[] ret;
        if (ListUtils.isNotEmpty(groupBy)){
            ret = new MrpV3DimFieldEnum[groupBy.size()];
            for (int i = 0; i < groupBy.size(); i++) {
                ret[i] = MrpV3DimFieldEnum.valueOf(groupBy.get(i));
            }
        }else {
            ret = null;
        }
        return ret;
    }
}
