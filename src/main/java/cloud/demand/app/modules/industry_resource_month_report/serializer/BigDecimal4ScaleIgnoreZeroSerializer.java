package cloud.demand.app.modules.industry_resource_month_report.serializer;

import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;
import java.math.BigDecimal;

public class BigDecimal4ScaleIgnoreZeroSerializer extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        // 忽略 0 值
        if (SoeCommonUtils.isNullOrZone(value)){
            gen.writeNull();
            return;
        }
        gen.writeNumber(value.setScale(4, BigDecimal.ROUND_HALF_UP).toPlainString());
    }
}
