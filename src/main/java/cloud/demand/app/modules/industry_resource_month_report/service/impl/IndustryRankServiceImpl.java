package cloud.demand.app.modules.industry_resource_month_report.service.impl;

import cloud.demand.app.modules.industry_resource_month_report.dto.req.IndustryRankReq;
import cloud.demand.app.modules.industry_resource_month_report.dto.resp.IndustryRankData;
import cloud.demand.app.modules.industry_resource_month_report.service.IndustryRankService;
import cloud.demand.app.modules.mrpv2.enums.DemandTypeEnum;
import cloud.demand.app.modules.mrpv2.enums.ProjectTypeEnum;
import cloud.demand.app.modules.mrpv3.dto.req.MrpV3ReportReq;
import cloud.demand.app.modules.mrpv3.dto.req.MrpV3ReportReqBuilder;
import cloud.demand.app.modules.mrpv3.dto.resp.MrpV3ReportResp;
import cloud.demand.app.modules.mrpv3.enums.MrpV3DimFieldEnum;
import cloud.demand.app.modules.mrpv3.enums.MrpV3IndexFieldEnum;
import cloud.demand.app.modules.mrpv3.model.item.MrpV3DataItem;
import cloud.demand.app.modules.mrpv3.service.MrpV3ReportService;
import cloud.demand.app.modules.real_service_cost_report.model.RealServiceCostReq;
import cloud.demand.app.modules.real_service_cost_report.model.RealServiceCostResp;
import cloud.demand.app.modules.real_service_cost_report.service.RealServiceCostService;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.http.domain.lab.PerformanceTrackItem;
import cloud.demand.app.modules.sop.http.domain.lab.PerformanceTrackReq;
import cloud.demand.app.modules.sop.http.domain.lab.PerformanceTrackReqBuilder;
import cloud.demand.app.modules.sop.http.domain.lab.PerformanceTrackResp;
import cloud.demand.app.modules.sop.http.service.CloudAppLabPerformanceHttpService;
import com.google.common.base.Objects;
import com.pugwoo.wooutils.collect.ListUtils;
import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.stereotype.Service;

@Service
public class IndustryRankServiceImpl implements IndustryRankService {

    /**
     * 履约率 v2
     */
    @Resource
    private CloudAppLabPerformanceHttpService performanceHttpService;

    /**
     * 行业数据看板
     * */
    @Resource
    private MrpV3ReportService mrpV3ReportService;

    /**
     * 真实服务成本
     * */
    @Resource
    private RealServiceCostService realServiceCostService;

    private List<IndustryRankData> computeRank(List<IndustryRankData> curYearMonthData, List<IndustryRankData> preYearMonthData, String yearMonth, String preYearMonth){
        return computeRank(curYearMonthData, preYearMonthData, yearMonth, preYearMonth, true);
    }

    /**
     * 计算排名
     * @param curYearMonthData 当前年月数据
     * @param preYearMonthData 上个月数据
     * @return
     */
    private List<IndustryRankData> computeRank(List<IndustryRankData> curYearMonthData, List<IndustryRankData> preYearMonthData, String yearMonth, String preYearMonth, boolean up){
        Set<String> industryDeptList = new HashSet<>();
        industryDeptList.addAll(curYearMonthData.stream().filter(item-> !SoeCommonUtils.isNullOrZone(item.getRankValue())).map(
                IndustryRankData::getIndustryDept).collect(
                Collectors.toList()));
        industryDeptList.addAll(preYearMonthData.stream().filter(item-> !SoeCommonUtils.isNullOrZone(item.getRankValue())).map(
                IndustryRankData::getIndustryDept).collect(
                Collectors.toList()));

        // 剔除当月和上月都是空值的数据
        curYearMonthData = curYearMonthData.stream().filter(item->industryDeptList.contains(item.getIndustryDept())).collect(
                Collectors.toList());
        preYearMonthData = preYearMonthData.stream().filter(item->industryDeptList.contains(item.getIndustryDept())).collect(
                Collectors.toList());

        // 填充当月和上月缺失的行业部门数据
        Set<String> curSet = curYearMonthData.stream().map(IndustryRankData::getIndustryDept).collect(Collectors.toSet());
        Set<String> preSet = preYearMonthData.stream().map(IndustryRankData::getIndustryDept).collect(Collectors.toSet());
        for (String industryDept : industryDeptList) {
            if (!curSet.contains(industryDept)){
                IndustryRankData e = new IndustryRankData();
                e.setRankValue(BigDecimal.ZERO);
                e.setIndustryDept(industryDept);
                e.setYearMonth(yearMonth);
                curYearMonthData.add(e);
            }
            if (!preSet.contains(industryDept)){
                IndustryRankData e = new IndustryRankData();
                e.setRankValue(BigDecimal.ZERO);
                e.setIndustryDept(industryDept);
                e.setYearMonth(preYearMonth);
                preYearMonthData.add(e);
            }
        }
        // 排行
        IndustryRankData.sortAndSetRank(curYearMonthData,up);
        IndustryRankData.sortAndSetRank(preYearMonthData,up);

        // 上个月排名
        Map<String,IndustryRankData> preMap = ListUtils.toMap(preYearMonthData, IndustryRankData::getIndustryDept, item->item);
        for (IndustryRankData curYearMonthDatum : curYearMonthData) {
            IndustryRankData preIndustryRankData = preMap.get(curYearMonthDatum.getIndustryDept());
            if (preIndustryRankData != null){
                Integer rank = preIndustryRankData.getRank();
                curYearMonthDatum.setRankUp(rank - curYearMonthDatum.getRank());
            }
        }
        return curYearMonthData;
    }

    @Override
    public List<IndustryRankData> getPerformanceRate(IndustryRankReq req) {
        String yearMonth = req.getYearMonth();
        YearMonth curYm = YearMonth.parse(yearMonth);
        YearMonth preYm = curYm.plusMonths(-1);
        String preYearMonth = preYm.toString();

        PerformanceTrackReq trackReq = new PerformanceTrackReqBuilder()
                .industryDept(req.getIndustryDept())
                .startYearMonth(preYearMonth)
                .endYearMonth(yearMonth)
                .customhouseTitle(null)
                .isDefault(true)
                .build();
        trackReq.setDims(ListUtils.newList("yearMonth", "industryDept"));
        PerformanceTrackResp performanceTrackResp = performanceHttpService.queryMonthPerformanceTrackItemList(trackReq);
        List<PerformanceTrackItem> data = performanceTrackResp.getData();

        Function<PerformanceTrackItem, @Nullable IndustryRankData> mapper = (item-> {
            IndustryRankData rank = new IndustryRankData();
            rank.setIndustryDept(item.getIndustryDept());
            rank.setYearMonth(item.getYearMonth());
            rank.setRankValue(item.getBuyRate());
            return rank;
        });

        // 获取上个月的数据
        List<IndustryRankData> preYearMonthData = data == null ? ListUtils.newList() : data.stream().filter(item-> Objects.equal(item.getYearMonth(), preYearMonth))
                .map(mapper).collect(
                        Collectors.toList());
        // 获取本月的数据
        List<IndustryRankData> curYearMonthData = data == null ? ListUtils.newList() : data.stream().filter(item-> Objects.equal(item.getYearMonth(), yearMonth))
                .map(mapper).collect(
                Collectors.toList());

        return computeRank(curYearMonthData, preYearMonthData, yearMonth, preYearMonth);
    }

    @Override
    public List<IndustryRankData> getForecastMatchRate(IndustryRankReq req) {
        String yearMonth = req.getYearMonth();
        YearMonth curYm = YearMonth.parse(yearMonth);
        YearMonth preYm = curYm.plusMonths(-1);
        String preYearMonth = preYm.toString();
        MrpV3ReportReqBuilder builder = new MrpV3ReportReqBuilder();
        MrpV3ReportReq v3ReportReq = builder.startYearMonth(preYearMonth)
                .endYearMonth(yearMonth)
                .isDefault(true)
                .customhouseTitle(null)
                .builder();
        v3ReportReq.setIndustryDept(req.getIndustryDept());
        // 只看重点项目
        v3ReportReq.setProjectType(ListUtils.newList(ProjectTypeEnum.KEY_PROJECT.getName()));
        // 需求类型 = 新增 or 弹性
        v3ReportReq.setDemandType(
                ListUtils.newList(DemandTypeEnum.NEW.getName(), DemandTypeEnum.ELASTIC.getName()));
        // 只看 532 新版准确率
        v3ReportReq.setIndex(ListUtils.newList(MrpV3IndexFieldEnum.monthly_532_new_distinct_rate.getColumn(),
                MrpV3IndexFieldEnum.core_day_532_new_distinct_rate.getColumn()));
        // 维度取 年月 和 行业部门 两个字段
        v3ReportReq.setDims(ListUtils.newList(MrpV3DimFieldEnum.statTime.name(), MrpV3DimFieldEnum.yearMonth.name(), MrpV3DimFieldEnum.industryDept.name()));

        MrpV3ReportResp mrpV3ReportResp = mrpV3ReportService.queryReport(v3ReportReq);
        List<MrpV3DataItem> data = mrpV3ReportResp.getData();

        Function<MrpV3DataItem, @Nullable IndustryRankData> mapper = (item-> {
            IndustryRankData rank = new IndustryRankData();
            rank.setIndustryDept(item.getIndustryDept());
            rank.setYearMonth(item.getYearMonth());
            rank.setRankValue(item.getRateByIndustryDept());
            return rank;
        });

        // 获取上个月的数据
        List<IndustryRankData> preYearMonthData = data.stream().filter(item-> Objects.equal(item.getYearMonth(), preYearMonth))
                .map(mapper).collect(
                        Collectors.toList());
        // 获取本月的数据
        List<IndustryRankData> curYearMonthData = data.stream().filter(item-> Objects.equal(item.getYearMonth(), yearMonth))
                .map(mapper).collect(
                        Collectors.toList());

        return computeRank(curYearMonthData, preYearMonthData, yearMonth, preYearMonth);
    }

    @Override
    public List<IndustryRankData> getRealServerCost(IndustryRankReq req) {
        String yearMonth = req.getYearMonth();
        YearMonth curYm = YearMonth.parse(yearMonth);
        YearMonth preYm = curYm.plusMonths(-1);
        String preYearMonth = preYm.toString();
        RealServiceCostReq costReq = new RealServiceCostReq();
        costReq.setCustomerShortName("全部客户");
        costReq.setStaticsCategory("全量机型");
        costReq.setIndustryDept("全部行业");
        costReq.setDims(ListUtils.newList("yearMonth", "industryDept"));
        String intPreYm = preYearMonth.replace("-", "");
        costReq.setStartYearMonth(intPreYm);
        costReq.setEndYearMonth(intPreYm);
        List<RealServiceCostResp> preData = realServiceCostService.queryRealServiceCostDetail(
                costReq);

        RealServiceCostReq curCostReq = new RealServiceCostReq();
        curCostReq.setCustomerShortName("全部客户");
        curCostReq.setStaticsCategory("全量机型");
        curCostReq.setIndustryDept("全部行业");
        curCostReq.setDims(ListUtils.newList("yearMonth", "industryDept"));
        String intYm = yearMonth.replace("-", "");
        curCostReq.setStartYearMonth(intYm);
        curCostReq.setEndYearMonth(intYm);
        List<RealServiceCostResp> curData = realServiceCostService.queryRealServiceCostDetail(
                curCostReq);

        Function<RealServiceCostResp, @Nullable IndustryRankData> mapper = (item-> {
            IndustryRankData rank = new IndustryRankData();
            rank.setIndustryDept(item.getIndustryDept());
            rank.setYearMonth(new StringBuilder(item.getYearMonth()).insert(4, "-").toString());
            rank.setRankValue(item.getBcdeA() == null ? BigDecimal.ZERO : BigDecimal.valueOf(item.getBcdeA()));
            return rank;
        });

        List<IndustryRankData> preYearMonthData = preData.stream().map(mapper).collect(Collectors.toList());
        List<IndustryRankData> curYearMonthData = curData.stream().map(mapper).collect(Collectors.toList());

        List<String> industryDept = req.getIndustryDept();
        if (ListUtils.isNotEmpty(industryDept)){
            Set<String> set = new HashSet<>(industryDept);
            preYearMonthData = preYearMonthData.stream().filter(item->set.contains(item.getIndustryDept())).collect(Collectors.toList());
            curYearMonthData = curYearMonthData.stream().filter(item->set.contains(item.getIndustryDept())).collect(Collectors.toList());
        }

        return computeRank(curYearMonthData, preYearMonthData, yearMonth, preYearMonth,false);
    }
}
