package cloud.demand.app.modules.gpu.model;

import cloud.demand.app.modules.gpu.entity.GpuReportInventoryDO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class GpuInventoryResp {

    private List<InventoryItem> data;

    @Data
    static class InventoryItem {


        /**
         * 产品形态
         */
        private String productType;
        /**
         * 卡型
         */
        private String cardType;
        /**
         * 设备类型
         */
        private String deviceType;
        /**
         * GPU类型
         */
        private String useType;
        /**
         * 库存类型
         */
        private String invType;
        /**
         * 库存小类
         */
        private String invIndex;
        /**
         * 境内外
         */
        private String customhouseTitle;
        /**
         * 区域
         */
        private String areaName;
        /**
         * Region
         */
        private String regionName;
        /**
         * 可用区
         */
        private String zoneName;
        /**
         * 数量（卡）
         */
        private BigDecimal cardNum;
        /**
         * 整机数量（台
         */
        private BigDecimal num;
        /**
         * 客户名称
         */
        private String customerName;
        /**
         * 是否预扣
         */
        private String preDeduct = "否";
        /**
         * 时间
         */
        private String timeType;
        /**
         * 时间
         */
        private String time;
    }

    public static GpuInventoryResp fromDo(List<GpuReportInventoryDO> inventoryDOS) {
        GpuInventoryResp resp = new GpuInventoryResp();
        List ls = new ArrayList();
        inventoryDOS.forEach(inventoryDO -> {
            InventoryItem item = new InventoryItem();

            item.setProductType(inventoryDO.getProductClass());
            item.setAreaName(inventoryDO.getAreaName());
            item.setCardNum(inventoryDO.getCardNum());
            item.setCardType(inventoryDO.getGpuType());
            item.setDeviceType(inventoryDO.getHostType());
            item.setInvIndex(inventoryDO.getPIndex());
            item.setNum(inventoryDO.getNum());
            item.setRegionName(inventoryDO.getRegionName());
            item.setZoneName(inventoryDO.getZoneName());

            item.setCustomerName(inventoryDO.getCustomerName());
            item.setCustomhouseTitle(inventoryDO.getCustomhouseTitle());

            item.setUseType(inventoryDO.getGpuClass());

            item.setTime(inventoryDO.getDDate());
            if (inventoryDO.getPIndex().startsWith("线上")) {
                item.setInvType("线上");
            } else if (inventoryDO.getPIndex().startsWith("线下")) {
                item.setInvType("线下");
            } else {
                item.setInvType(inventoryDO.getPIndex());
            }
            if (inventoryDO.getPIndex().contains("预扣")) {
                item.setPreDeduct("是");
                item.setTimeType("预扣时间");
            } else if (inventoryDO.getPIndex().contains("采购")) {
                item.setTimeType("到货时间");
            }
            ls.add(item);
        });
        resp.setData(ls);
        return resp;
    }
}
