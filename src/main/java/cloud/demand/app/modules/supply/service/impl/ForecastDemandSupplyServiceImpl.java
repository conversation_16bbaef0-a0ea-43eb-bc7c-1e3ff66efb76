package cloud.demand.app.modules.supply.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.PageWrapper;
import cloud.demand.app.modules.supply.model.AdsForecastDemandSupplySummaryDO;
import cloud.demand.app.modules.supply.model.DictColEnum;
import cloud.demand.app.modules.supply.model.ForecastDemandSupplyResultCoreSum;
import cloud.demand.app.modules.supply.model.ForecastDemandSupplyResultSum;
import cloud.demand.app.modules.supply.model.SupplyForecastReq;
import cloud.demand.app.modules.supply.model.SupplyForecastResp;
import cloud.demand.app.modules.supply.service.ForecastDemandSupplyService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.json.JSON;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import yunti.boot.exception.BizException;

@Service
public class ForecastDemandSupplyServiceImpl implements ForecastDemandSupplyService {

    @Autowired
    DBHelper demandDBHelper;

    @Override
    public SupplyForecastResp queryDemandSupply(SupplyForecastReq req) {

        if (req.getYearMonth() == null || req.getYearMonth().size() != 2) {
            throw new BizException("需求年月参数缺失");
        }
        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();

        whereContent.addAnd("`dim_year_month` >= ? and `dim_year_month` <= ?", req.getYearMonth().get(0),
                req.getYearMonth().get(1));
        String ver = getVersion();

        whereContent.addAnd("demand_amount > 0 and version = ?", req.getVersion());
        if (!CollectionUtils.isEmpty(req.getPlanProduct())) {
            whereContent.addAnd("dim_plan_product in (?)", req.getPlanProduct());
        }
        if (!CollectionUtils.isEmpty(req.getCountry())) {
            whereContent.addAnd("dim_area in (?)", req.getCountry());
        }
        if (!CollectionUtils.isEmpty(req.getRegion())) {
            whereContent.addAnd("dim_region in (?)", req.getRegion());
        }
        if (!CollectionUtils.isEmpty(req.getZone())) {
            whereContent.addAnd("dim_zone in (?)", req.getZone());
        }
        if (!CollectionUtils.isEmpty(req.getDeviceType())) {
            whereContent.addAnd("dim_device_type in (?)", req.getDeviceType());
        }
        if (!CollectionUtils.isEmpty(req.getIndustry())) {
            whereContent.addAnd("dim_industry in (?)", req.getIndustry());
        }
        if (!CollectionUtils.isEmpty(req.getCustomer())) {
            whereContent.addAnd("dim_customer_name in (?)", req.getCustomer());
        }
        if (!CollectionUtils.isEmpty(req.getReasonType())) {
            whereContent.addAnd("dim_reason_type in (?)", req.getReasonType());
        }
        if (!CollectionUtils.isEmpty(req.getCampus())) {
            whereContent.addAnd("dim_campus in (?)", req.getReasonType());
        }
        if (req.getShowRisk() == 1) {
            whereContent.addAnd("supply_gap > ?", 0);
        }

        PageData<AdsForecastDemandSupplySummaryDO> oriData = ORMUtils.db(demandDBHelper)
                .getPage(AdsForecastDemandSupplySummaryDO.class, new PageWrapper(req.getPage()), whereContent);

        if (req.getShowCore() == 1) {
            oriData.getData().forEach(o -> {
                val p = BigDecimal.valueOf(o.getAssistDeviceCore());
                o.setDemandAmount(o.getDemandAmount().multiply(p));
                o.setExAmount(o.getExAmount().multiply(p));
                o.setExMatchBAmount(o.getExMatchBAmount().multiply(p));
                o.setExMatchOAmount(o.getExMatchOAmount().multiply(p));

                o.setExNoMatchSupplyInv(o.getExNoMatchSupplyInv().multiply(p));
                o.setExNoMatchSupplyPur(o.getExNoMatchSupplyPur().multiply(p));
                o.setExNoMatchSupplyPurChain(o.getExNoMatchSupplyPurChain().multiply(p));
                o.setExNoMatchAmount(o.getExNoMatchAmount().multiply(p));

                o.setNoExAmount(o.getNoExAmount().multiply(p));
                o.setNoExSupplyInv(o.getNoExSupplyInv().multiply(p));
                o.setNoExSupplyPur(o.getNoExSupplyPur().multiply(p));
                o.setNoExSupplyPurChain(o.getNoExSupplyPurChain().multiply(p));

                o.setGapAmount(o.getGapAmount().multiply(p));
                o.setSupplyAmount(o.getSupplyAmount().multiply(p));
                o.setSupplyGap(o.getSupplyGap().multiply(p));
                o.setSupplyDela(o.getSupplyDela().multiply(p));
                try {
                    Map<String, Integer> m = JSON.parse(o.getSupplySatis(), Map.class);
                    m.forEach((k, v) -> {
                        m.put(k, v * o.getAssistDeviceCore());
                    });
                    o.setSupplySatis(JSON.toJson(m));
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            });
        }
        SupplyForecastResp resp = new SupplyForecastResp();
        resp.setData(oriData.getData());
        resp.setTotal(oriData.getTotal());
        if (req.getShowCore() == 1) {
            ForecastDemandSupplyResultCoreSum sum = demandDBHelper.getOne(ForecastDemandSupplyResultCoreSum.class,
                    whereContent.getSql(),
                    whereContent.getParams());
            resp.setSummary(sum);
        } else {
            ForecastDemandSupplyResultSum sum = demandDBHelper.getOne(ForecastDemandSupplyResultSum.class,
                    whereContent.getSql(),
                    whereContent.getParams());
            resp.setSummary(sum);
        }

        return resp;
    }

    @Override
    public List<String> getDictBy(DictColEnum key) {

        List ls = demandDBHelper.getRaw(String.class, "select distinct " + key.getCol() + "\n"
                + "from ads_forecast_demand_supply_summary");
        return ls;
    }

    public String getVersion() {
        return demandDBHelper.getRawOne(String.class,
                "select max(d_version) version from forecast_demand_supply_result");
    }
}
