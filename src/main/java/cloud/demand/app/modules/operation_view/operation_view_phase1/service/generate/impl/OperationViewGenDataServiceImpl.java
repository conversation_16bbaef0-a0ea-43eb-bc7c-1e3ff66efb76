package cloud.demand.app.modules.operation_view.operation_view_phase1.service.generate.impl;

import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.operation_view.operation_view_phase1.service.generate.OperationViewGenDataService;
import cloud.demand.app.modules.operation_view.operation_view_phase1.service.generate.cvm.GenCvmDetailService;
import cloud.demand.app.modules.operation_view.operation_view_phase1.service.generate.gpu.GenGpuDetailService;
import cloud.demand.app.modules.operation_view.operation_view_phase1.service.generate.metal.GenMetalDetailService;
import com.pugwoo.wooutils.redis.Synchronized;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OperationViewGenDataServiceImpl implements OperationViewGenDataService {

    @Resource
    GenCvmDetailService cvmDetailService;
    @Resource
    GenMetalDetailService metalDetailService;
    @Resource
    GenGpuDetailService gpuDetailService;


    @Override
    @Synchronized(waitLockMillisecond = 100, keyScript = "args[0]", throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "GenOperationViewDetail")
    public void genAllDetailData(String statTime) {

    }
}
