package cloud.demand.app.modules.operation_view.operation_view2.entity;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 包月安全库存，包含包月安全库存计算所需要的中间数据以及计算结果
 */
@Data
public class GenActualInventoryDfDO {
    /**
     * 统计时间
     */
    @Column("stat_time")
    private String statTime;

    /** 产品类型 */
    @Column("product_type")
    private String productType;

    /** 境内外 */
    @Column("customhouse_title")
    private String customhouseTitle;

    /** 区域名 */
    @Column("area_name")
    private String areaName;

    /** 地域名 */
    @Column("region_name")
    private String regionName;

    /** 可用区名 */
    @Column("zone_name")
    private String zoneName;

    /**设备类型*/
    @Column("device_type")
    private String deviceType;

    /** 库存类型 */
    @Column("stock_class1")
    private String lineType;

    /** 物料类型：好呆差 */
    @Column("stock_class2")
    private String materialType;

    /** 库存细类：大核库存、小核库存、小核预留、用户预扣等 */
    @Column("stock_class4")
    private String invDetailType;

    /**实际库存*/
    @Column("actual_inv")
    private BigDecimal actualInv;

    /**设备台数*/
    @Column("device_num")
    private BigDecimal deviceNum;
}
