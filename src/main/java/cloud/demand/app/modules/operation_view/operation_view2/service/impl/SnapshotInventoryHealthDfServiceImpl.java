package cloud.demand.app.modules.operation_view.operation_view2.service.impl;

import cloud.demand.app.modules.operation_view.operation_view2.entity.AdsInventoryHealthFullDfDO;
import cloud.demand.app.modules.operation_view.operation_view2.model.OperationViewReq2;
import cloud.demand.app.modules.operation_view.operation_view2.model.OperationViewResp2;
import cloud.demand.app.modules.operation_view.operation_view2.service.OperationViewService2;
import cloud.demand.app.modules.operation_view.operation_view2.service.SnapshotInventoryHealthDfService;
import cloud.demand.app.web.model.common.Result;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SnapshotInventoryHealthDfServiceImpl implements SnapshotInventoryHealthDfService {
    @Resource
    OperationViewService2 operationViewService2;
    @Resource
    DBHelper ckcldDBHelper;

    ExecutorService threadPool = Executors.newFixedThreadPool(10);

    private static List<AdsInventoryHealthFullDfDO> toDfDOs(OperationViewResp2 resp, String statTime, Date date) {
        List<AdsInventoryHealthFullDfDO> dfDOS = ListUtils.newList();

        for (OperationViewResp2.Item item : resp.getData()) {
            AdsInventoryHealthFullDfDO dfDO = new AdsInventoryHealthFullDfDO();
            dfDO.setStatTime(statTime);
            dfDO.setDate(DateUtils.formatDate(date));
            // 地域信息
            dfDO.setCustomhouseTitle(item.getCustomhouseTitle());
            dfDO.setAreaName(item.getAreaName());
            dfDO.setRegionName(item.getRegionName());
            dfDO.setZoneName(item.getZoneName());
            // 机型信息
            dfDO.setInstanceType(item.getInstanceType());

            dfDO.setActualServiceLevel(item.getActualSerivceLevel());
            dfDO.setInvRatio(item.getInvRatio());
            dfDO.setInvTotalNum(item.getInvTotalNum());
            dfDO.setInvPrice(item.getInvPrice());
            dfDO.setActualServiceLevel(item.getActualSerivceLevel());
            dfDO.setBufferServiceLevel(item.getBufferServiceLevel());
            dfDO.setBufferSafetyInv(item.getBufferSafetyInv());
            dfDO.setBufferServiceLevelFactor(item.getBufferServiceLevelFactor());
            dfDO.setBufferAverageCore(item.getBufferAverageCore());
            dfDO.setProductType(item.getProductType());
            dfDO.setSafeInvManualConfig(item.getSafeInvManualConfig());
            dfDO.setHealthRatio(item.getHealthRatio());

            // 三种算法的数据填充
            if (item.getHistoryWeekPeak() != null) {
                AdsInventoryHealthFullDfDO hwpDo = dfDO.clone();
                OperationViewResp2.SafetyInventoryResult safetyInventoryResult = item.getHistoryWeekPeak();
                hwpDo.setAlgorithm(safetyInventoryResult.getAlgorithm());
                hwpDo.setServiceLevel(safetyInventoryResult.getServiceLevel());
                hwpDo.setServiceLevelFactor(safetyInventoryResult.getServiceLevelFactor());
                hwpDo.setSla(BigDecimal.valueOf(safetyInventoryResult.getSla()));
                hwpDo.setRedundancyInv(safetyInventoryResult.getRedundancyInv());
                hwpDo.setSafetyInv(safetyInventoryResult.getSafetyInv());
                hwpDo.setMonthlySafetyInv(safetyInventoryResult.getMonthlySafetyInv());
                hwpDo.setDeliveryAvg(safetyInventoryResult.getDeliveryAvg());
                hwpDo.setDeliveryStandardDiff(safetyInventoryResult.getDeliveryStandardDiff());
                hwpDo.setStandardDiff(safetyInventoryResult.getStandardDiff());
                hwpDo.setDemandAvg(safetyInventoryResult.getDemandAvg());
                hwpDo.setTurnoverInv(safetyInventoryResult.getTurnoverInv());
                // JSON 序列化 weekDemandMap
                Map<String, BigDecimal> weekDemandMap = safetyInventoryResult.getWeekDemandMap();
                weekDemandMap = weekDemandMap.entrySet().stream().collect(Collectors.toMap(e -> resp.getWeekInterval().get(e.getKey()), e -> e.getValue()));
                hwpDo.setWeekDemandMap(JSON.toJson(weekDemandMap));

                dfDOS.add(hwpDo);
            }

            if (item.getHistoryWeekDiff() != null) {
                AdsInventoryHealthFullDfDO hwpDo = dfDO.clone();
                OperationViewResp2.SafetyInventoryResult safetyInventoryResult = item.getHistoryWeekDiff();
                hwpDo.setAlgorithm(safetyInventoryResult.getAlgorithm());
                hwpDo.setServiceLevel(safetyInventoryResult.getServiceLevel());
                hwpDo.setServiceLevelFactor(safetyInventoryResult.getServiceLevelFactor());
                hwpDo.setSla(BigDecimal.valueOf(safetyInventoryResult.getSla()));
                hwpDo.setRedundancyInv(safetyInventoryResult.getRedundancyInv());
                hwpDo.setSafetyInv(safetyInventoryResult.getSafetyInv());
                hwpDo.setMonthlySafetyInv(safetyInventoryResult.getMonthlySafetyInv());
                hwpDo.setDeliveryAvg(safetyInventoryResult.getDeliveryAvg());
                hwpDo.setDeliveryStandardDiff(safetyInventoryResult.getDeliveryStandardDiff());
                hwpDo.setStandardDiff(safetyInventoryResult.getStandardDiff());
                hwpDo.setDemandAvg(safetyInventoryResult.getDemandAvg());
                hwpDo.setTurnoverInv(safetyInventoryResult.getTurnoverInv());
                // JSON 序列化 weekDemandMap
                Map<String, BigDecimal> weekDemandMap = safetyInventoryResult.getWeekDemandMap();
                weekDemandMap = weekDemandMap.entrySet().stream().collect(Collectors.toMap(e -> resp.getWeekInterval().get(e.getKey()), e -> e.getValue()));
                hwpDo.setWeekDemandMap(JSON.toJson(weekDemandMap));

                dfDOS.add(hwpDo);
            }

            if (item.getFutureWeekPeak() != null) {
                AdsInventoryHealthFullDfDO hwpDo = dfDO.clone();
                OperationViewResp2.SafetyInventoryResult safetyInventoryResult = item.getFutureWeekPeak();
                hwpDo.setAlgorithm(safetyInventoryResult.getAlgorithm());
                hwpDo.setServiceLevel(safetyInventoryResult.getServiceLevel());
                hwpDo.setServiceLevelFactor(safetyInventoryResult.getServiceLevelFactor());
                hwpDo.setSla(BigDecimal.valueOf(safetyInventoryResult.getSla()));
                hwpDo.setRedundancyInv(safetyInventoryResult.getRedundancyInv());
                hwpDo.setSafetyInv(safetyInventoryResult.getSafetyInv());
                hwpDo.setMonthlySafetyInv(safetyInventoryResult.getMonthlySafetyInv());
                hwpDo.setDeliveryAvg(safetyInventoryResult.getDeliveryAvg());
                hwpDo.setDeliveryStandardDiff(safetyInventoryResult.getDeliveryStandardDiff());
                hwpDo.setStandardDiff(safetyInventoryResult.getStandardDiff());
                hwpDo.setDemandAvg(safetyInventoryResult.getDemandAvg());
                hwpDo.setTurnoverInv(safetyInventoryResult.getTurnoverInv());
                // JSON 序列化 weekDemandMap
                Map<String, BigDecimal> weekDemandMap = safetyInventoryResult.getWeekDemandMap();
                if (weekDemandMap != null) {
                    weekDemandMap = weekDemandMap.entrySet().stream().collect(Collectors.toMap(e -> resp.getWeekInterval().get(e.getKey()), e -> e.getValue()));
                }
                hwpDo.setWeekDemandMap(JSON.toJson(weekDemandMap));

                dfDOS.add(hwpDo);
            }
//            dfDO
        }

        return dfDOS;
    }

    @Override
    public Result snapshotInventoryHealth(String statTime) {
        // 如果数据已经存在，清理掉
        ckcldDBHelper.executeRaw(
                "ALTER TABLE cloud_demand.ads_inventory_health_full_df_local ON CLUSTER default_cluster DROP PARTITION ?", statTime);
        // 在线程池中执行任务 10 天为 1 组，从 2023 年 1 月 1 日到 date 之间
        Date start = DateUtils.parse("2023-01-01");
        Date end = DateUtils.parse(statTime);
        List<Future<OperationViewResp2Future>> futures = ListUtils.newList();

        Function<List<Future<OperationViewResp2Future>>, List<AdsInventoryHealthFullDfDO>> drainFeatures = (List<Future<OperationViewResp2Future>> localFutures) -> {
            List<AdsInventoryHealthFullDfDO> results = ListUtils.newList();

            try {
                for (Future<OperationViewResp2Future> future : localFutures) {
                    OperationViewResp2Future res = future.get();
                    results.addAll(toDfDOs(res.resp, statTime, res.date));
                }
                localFutures.clear();
            } catch (Exception e) {
                log.error("获取数据失败", e);
                throw BizException.makeThrow("同步数据失败" + e);
            }

            return results;
        };

        while (!start.after(end)) {
            log.info("开始同步数据：" + DateUtils.format(start, "yyyy-MM-dd"));
            Date currentDate = start;
            futures.add(threadPool.submit(() -> {
                OperationViewReq2 operationViewReq2 = new OperationViewReq2();
                operationViewReq2.setDate(currentDate);

                OperationViewResp2 resp = operationViewService2.queryAllProductSummary(operationViewReq2);
                return new OperationViewResp2Future(resp, currentDate);
            }));

            if (futures.size() == 10) {
                List<AdsInventoryHealthFullDfDO> results = drainFeatures.apply(futures);
                // 每十天的数据批量写入数据库
                ckcldDBHelper.insertBatchWithoutReturnId(results);
            }

            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }

        if (!futures.isEmpty()) {
            List<AdsInventoryHealthFullDfDO> results = drainFeatures.apply(futures);
            ckcldDBHelper.insertBatchWithoutReturnId(results);
        }

        return Result.success("success");
    }

    public static class OperationViewResp2Future {
        OperationViewResp2 resp;
        Date date;
        public OperationViewResp2Future(OperationViewResp2 resp, Date date) {
            this.resp = resp;
            this.date = date;
        }
    }
}
