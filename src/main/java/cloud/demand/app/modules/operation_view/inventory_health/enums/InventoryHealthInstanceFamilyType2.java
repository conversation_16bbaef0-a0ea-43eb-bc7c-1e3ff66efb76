package cloud.demand.app.modules.operation_view.inventory_health.enums;

/**
 * 安全库存机型二级类型，云云管自定义
 */
public enum InventoryHealthInstanceFamilyType2 {
    PRINCIPAL("PRINCIPAL", "主力机型"),
    ON_SALE("ON_SALE", "在售非主力机型"),
    NEW("NEW", "新机型"),
//    EOL_INV("EOL_INV", "EOL库存机型"),
    WHITE_LISTED("WHITE_LISTED", "白名单机型"),
    EOL("EOL", "EOL机型");

    String code;
    String name;

    InventoryHealthInstanceFamilyType2(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameFromCode(String code) {
        for (InventoryHealthInstanceFamilyType2 status : InventoryHealthInstanceFamilyType2.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return null;
    }

    public static String getCodeFromName(String name) {
        for (InventoryHealthInstanceFamilyType2 status : InventoryHealthInstanceFamilyType2.values()) {
            if (status.getName().equals(name)) {
                return status.getCode();
            }
        }
        return null;
    }
}
