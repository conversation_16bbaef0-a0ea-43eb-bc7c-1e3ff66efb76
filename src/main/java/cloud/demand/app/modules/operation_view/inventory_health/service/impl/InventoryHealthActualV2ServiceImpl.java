package cloud.demand.app.modules.operation_view.inventory_health.service.impl;

import cloud.demand.app.common.excel.LocalDateStringConverter;
import cloud.demand.app.common.excel.LocalTimeStringConverter;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.entity.ResPlanHolidayWeekDO;
import cloud.demand.app.modules.common.service.impl.DictServiceImpl;
import cloud.demand.app.modules.operation_action.entity.InventoryReasonDO;
import cloud.demand.app.modules.operation_action.service.impl.OperationServiceImpl;
import cloud.demand.app.modules.operation_view.inventory_health.dto.HolidayWeekInfoDTO;
import cloud.demand.app.modules.operation_view.inventory_health.dto.InstanceTypeCombineDTO;
import cloud.demand.app.modules.operation_view.inventory_health.dto.QueryInvDetailTypesReq;
import cloud.demand.app.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualReq;
import cloud.demand.app.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualResp;
import cloud.demand.app.modules.operation_view.inventory_health.dto.actual.TrendGraphReq;
import cloud.demand.app.modules.operation_view.inventory_health.dto.yunxiao.YunxiaoGridItemVO;
import cloud.demand.app.modules.operation_view.inventory_health.entity.ActualDeliverySlaDetailDO;
import cloud.demand.app.modules.operation_view.inventory_health.entity.DwsCloudServerLevelDO;
import cloud.demand.app.modules.operation_view.inventory_health.service.InventoryHealthActualV2Service;
import cloud.demand.app.modules.operation_view.inventory_health.service.InventoryHealthDictService;
import cloud.demand.app.modules.operation_view.operation_view2.entity.DwsActualInventoryDfDO;
import cloud.demand.app.modules.operation_view.operation_view2.entity.DwsBufferSafeInventoryDfDO;
import cloud.demand.app.modules.operation_view.operation_view2.model.ActualInventoryListReq;
import cloud.demand.app.modules.operation_view.operation_view2.model.OperationViewReq2;
import cloud.demand.app.modules.operation_view.operation_view2.model.OperationViewResp2;
import cloud.demand.app.modules.operation_view.operation_view2.service.impl.OperationViewService2Impl;
import cloud.demand.app.modules.operation_view.operation_view_phase1.service.impl.OperationViewServiceImpl;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.google.common.base.Splitter;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InventoryHealthActualV2ServiceImpl implements InventoryHealthActualV2Service {

    @Resource
    DBHelper ckcldDBHelper;

    @Resource
    RedisHelper redisHelper;
    @Resource
    InventoryHealthDictService inventoryHealthDictService;
    @Resource
    DBHelper ckcubesDBHelper;

    private ExecutorService threadPool = Executors.newWorkStealingPool();

    @Override
    public InventoryHealthActualResp queryInventoryHealthActualV2(InventoryHealthActualReq req) {
        switch (req.getTimeDimension()) {
            case "日切片":
                return queryInventoryHealthActualByDay(req);
            case "周日均":
                return queryInventoryHealthActualByWeekAvg(req);
            case "月日均":
                return queryInventoryHealthActualByMonthAvg(req);
            default:
                throw BizException.makeThrow("选择的时间维度必须是日切片、周日均、月日均三者之一");
        }
    }

    @Override
    public FileNameAndBytesDTO exportDeliveryDetailExcel(TrendGraphReq req) {
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/delivery-detail.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        TrendGraphServiceImpl trendGraphService = SpringUtil.getBean(TrendGraphServiceImpl.class);
        String instanceTypeName = trendGraphService.getInstanceTypeNameForExport(req);
        String zoneName = trendGraphService.getZoneNameForExport(req);
        String date = req.getDate().toString();

        String fileNamePrefix = "库存健康-交付明细(" + instanceTypeName + "-" + zoneName + "-" + date + ")";
        // 从数据库查询需要的数据
        List<String> campus = trendGraphService.getCampusByZoneName(req.getZoneName());
        List<String> device = trendGraphService.getDeviceTypeByInstanceType(req.getInstanceType());
        // 获取节假周数据
        //  1、获取历史指定时间区间范围的节假周信息
        List<HolidayWeekInfoDTO> holidayWeekInfo = inventoryHealthDictService.getHolidayWeekInfoBase(req.getDate(), -13);
        //  获取第一周周一，作为beginDate
        String beginDate = holidayWeekInfo.get(0).getStartDate();
        //  获取最后一周周日，作为endDate
        String lastDate = holidayWeekInfo.get(holidayWeekInfo.size() - 1).getEndDate();

        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/demand_market_delivery_sla_detail_excel.sql");
        List<ActualDeliverySlaDetailDO> data = ckcubesDBHelper.getRaw(ActualDeliverySlaDetailDO.class, sql, date, beginDate, lastDate, campus, device);
        // 填充可用区，机型数据
        DictService dictService = SpringUtil.getBean(DictServiceImpl.class);
        Map<String, StaticZoneDO> campus2ZoneInfMap = dictService.getCampus2ZoneInfoMap();
        Map<String, String> deviceType2InstanceTypeMap = dictService.getCsigDeviceTypeToInstanceTypeMap();
        OperationViewServiceImpl operationViewService = SpringUtil.getBean(OperationViewServiceImpl.class);

        data.stream().forEach(item -> {
            StaticZoneDO zoneInfo = campus2ZoneInfMap.get(item.getCampus());

            if (zoneInfo != null) {
                item.setRegionType(zoneInfo.getCustomhouseTitle());
                item.setAreaName(zoneInfo.getAreaName());
                item.setRegionName(zoneInfo.getRegionName());
                item.setZoneName(zoneInfo.getZoneName());
            }

            if (item.getXyCreateTime().contains("1970-01-01")) {
                item.setXyApprovalDays(BigDecimal.ZERO);
            }

            item.setInstanceType(deviceType2InstanceTypeMap.get(item.getQuotaDeviceClass()));
            item.setSla(operationViewService.getSLA(item.getQuotaDeviceClass(), item.getRegionType().equals("境内")));

            // 设置 SLA
            if (item.getDeliveryStatus().equals("如期交付")) {
                item.setErpDeliveryDays(NumberUtils.min(item.getDeliveryDays(), BigDecimal.valueOf(item.getSla())));
            } else {
                item.setErpDeliveryDays(item.getDeliveryDays());
            }

            item.setTotalDeliveryDays(item.getErpDeliveryDays().add(item.getXyApprovalDays()));
        });

        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        FileNameAndBytesDTO result = new FileNameAndBytesDTO();
        result.setBytes(out.toByteArray());
        result.setFileName(fileNamePrefix + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx");
        return result;
    }

    @Override
    public List<String> queryInvDetailTypes(QueryInvDetailTypesReq req) {
        String statTime = req.getDate() == null ? DateUtils.yesterday().toString() : req.getDate();
        String sql = "select distinct inv_detail_type from cloud_demand.dws_actual_inventory_df";

        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        whereContent.addAnd("stat_time = ?", statTime);

        if (ListUtils.isNotEmpty(req.getLineType())) {
            whereContent.addAnd("line_type in (?)", req.getLineType());
        }

        if (ListUtils.isNotEmpty(req.getMaterialType())) {
            whereContent.addAnd("material_type in (?)", req.getMaterialType());
        }

        sql = sql + whereContent.getSql();

        return ckcldDBHelper.getRaw(String.class, sql, whereContent.getParams());
    }

    @Override
    public List<String> queryLineType(QueryInvDetailTypesReq req) {
        String statTime = req.getDate() == null ? DateUtils.yesterday().toString() : req.getDate();
        String sql = "select distinct line_type from cloud_demand.dws_actual_inventory_df";

        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        whereContent.addAnd("stat_time = ?", statTime);

        sql = sql + whereContent.getSql();

        return ckcldDBHelper.getRaw(String.class, sql, whereContent.getParams());
    }

    @Override
    public List<String> queryMaterialType(QueryInvDetailTypesReq req) {
        String statTime = req.getDate() == null ? DateUtils.yesterday().toString() : req.getDate();
        String sql = "select distinct material_type from cloud_demand.dws_actual_inventory_df";

        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        whereContent.addAnd("stat_time = ?", statTime);

        if (ListUtils.isNotEmpty(req.getLineType())) {
            whereContent.addAnd("line_type in (?)", req.getLineType());
        }

        sql = sql + whereContent.getSql();

        return ckcldDBHelper.getRaw(String.class, sql, whereContent.getParams());
    }

    /**
     * 在内存中筛选弹性用量，复用运营视图的筛选逻辑
     *
     * @param req
     * @return
     */
//    private List<BufferAverageCoreDTO> filterBufferAverageCore(InventoryHealthActualReq req) {
//        List<BufferAverageCoreDTO> bufferAverageCoreDTOS =
//                SpringUtil.getBean(JxcExternalServiceImpl.class).queryBufferCoreAverage(DateUtils.parse(req.getDate()));
//        // 复用新CVM运营视图的逻辑
//        OperationViewService2Impl operationViewService2 = SpringUtil.getBean(OperationViewService2Impl.class);
//        //  对弹性规模在内存中筛选
//        OperationViewReq2 opReq = invReqToOpViewReq(req);
//        return operationViewService2.filterBufferPart(bufferAverageCoreDTOS, opReq, req.getDate());
//    }

    /**
     * 将安全库存的参数转换为库存健康的参数
     *
     * @param req
     * @return
     */
    private OperationViewReq2 invReqToOpViewReq(InventoryHealthActualReq req) {
        // 构造运营视图参数，复用运营视图逻辑
        OperationViewReq2 opReq = new OperationViewReq2();
        opReq.setZoneCategory(req.getZoneCategory());
        opReq.setInstanceTypeCategory(req.getInstanceTypeCategory());
        opReq.setInstanceType(req.getInstanceType());
        opReq.setCustomhouseTitle(req.getCustomhouseTitle());
        opReq.setAreaName(req.getAreaName());
        opReq.setRegionName(req.getRegionName());
        opReq.setZoneName(req.getZoneName());
        opReq.setIsCombine(req.getIsCombine());
        opReq.setMaterialType(req.getMaterialType());
        opReq.setCustomerCustomGroup(req.getCustomerCustomGroup());
        opReq.setLineType(req.getLineType());
        opReq.setDate(DateUtils.parse(req.getDate()));
        opReq.setCategoryDate(req.getCategoryDate());
        return opReq;
    }

    /**
     * 格式适配，将库存健康的安全库存数据转为运营视图的安全库存结构
     *
     * @param resp
     * @return
     */
    private OperationViewResp2 invRespToOpViewResp(InventoryHealthActualResp resp) {
        OperationViewResp2 opResp = new OperationViewResp2();
        opResp.setData(resp.getData().stream().map(item -> {
            // 安全库存信息此时还没生成，只转换实际存库数据
            OperationViewResp2.Item opItem = new OperationViewResp2.Item();
            opItem.setProductType("CVM"); // 固定 CVM
            opItem.setCustomhouseTitle(item.getCustomhouseTitle());
            opItem.setAreaName(item.getAreaName());
            opItem.setRegionName(item.getRegionName());
            opItem.setZoneName(item.getZoneName());
            opItem.setInstanceType(item.getInstanceType());

            //  总库存
            opItem.setInvTotalNum(BigDecimal.valueOf(item.getActualInventoryCore()));
            //  弹性服务水平/系数
            opItem.setBufferServiceLevel(item.getBufferServiceLevel());
            opItem.setBufferServiceLevelFactor(item.getBufferServiveLevelFactor());
            //  弹性规模日均值
            opItem.setBufferAverageCore(BigDecimal.valueOf(item.getBufferAverageCore()));
            //  弹性备货配额-三种算法目前结果完全相同，因此先抽取到这里
            opItem.setBufferSafetyInv(item.getBufferSafetyInventoryCore());
            return opItem;
        }).collect(Collectors.toList()));

        return opResp;
    }

    private void getSafeInv(InventoryHealthActualReq invReq, InventoryHealthActualResp invResp) {
        // 复用新CVM运营视图的逻辑
        OperationViewService2Impl operationViewService2 = SpringUtil.getBean(OperationViewService2Impl.class);
        // 根据用户设置的安全库存算法，构建安全库存
        String operationViewAlgorithm = redisHelper.getString("operationViewAlgorithm");
        OperationViewReq2 req = invReqToOpViewReq(invReq);
        OperationViewResp2 resp = invRespToOpViewResp(invResp);

        switch (operationViewAlgorithm) {
            case "historyWeekPeak":
                operationViewService2.buildHistoryWeekPeak(req, resp);
                break;
            case "historyWeekDiff":
                operationViewService2.buildHistoryWeekDiff(req, resp);
                break;
            case "futureWeekPeak":
                operationViewService2.buildFutureWeekPeak(req, resp);
                break;
            case "historyWeekPeakForecastWN":
                operationViewService2.buildHistoryWeekPeakDemand(req, resp);
                break;
            default:
                throw BizException.makeThrow("该安全算法不存在：" + operationViewAlgorithm);
        }

        // 将运营视图计算的安全库存，填充到库存健康的响应中
        invResp.setData(ListUtils.merge(invResp.getData(), resp.getData(), invItem -> invItem.toKey(), opItem -> opItem.toKeyNoProduct(), (o1, o2) -> {
            if ((ListUtils.isNotEmpty(o1) && o1.size() != 1) || (ListUtils.isNotEmpty(o2) && o2.size() != 1)) {
                log.info("设置安全库存时，键值不唯一，库存：" + JSON.toJson(o1) + "运营视图：" + JSON.toJson(o2));
                throw BizException.makeThrow("内部错误，联系 brightwwu 处理");
            }
            // 如果填充一个空的 o1
            if (ListUtils.isEmpty(o1)) {
                InventoryHealthActualResp.Item invItem = new InventoryHealthActualResp.Item();
                invItem.setCustomhouseTitle(o2.get(0).getCustomhouseTitle());
                invItem.setAreaName(o2.get(0).getAreaName());
                invItem.setRegionName(o2.get(0).getRegionName());
                invItem.setZoneName(o2.get(0).getZoneName());
                invItem.setInstanceType(o2.get(0).getInstanceType());

                o1 = new ArrayList<>();
                o1.add(invItem);
            } else if (ListUtils.isEmpty(o2)) {
                return o1.get(0);
            }

            OperationViewResp2.SafetyInventoryResult safeInv = null;

            switch (operationViewAlgorithm) {
                case "historyWeekPeak":
                    safeInv = o2.get(0).getHistoryWeekPeak();

                    if (safeInv == null) {
                        // 如果安全库存为空，填充一个空安全库存数据
                        operationViewService2.fillNullHistoryWeekPeakSafetyInvItem(o2.get(0), invReq.getDate());
                        safeInv = o2.get(0).getHistoryWeekPeak();
                    }
                    break;
                case "historyWeekDiff":
                    safeInv = o2.get(0).getHistoryWeekDiff();

                    if (safeInv == null) {
                        operationViewService2.fillNullHistoryWeekDiffSafetyInvItem(o2.get(0), invReq.getDate());
                        safeInv = o2.get(0).getHistoryWeekDiff();
                    }
                    break;
                case "futureWeekPeak":
                    safeInv = o2.get(0).getFutureWeekPeak();

                    if (safeInv == null) {
                        operationViewService2.fillNullFutureWeekPeakSafetyInvItem(o2.get(0), invReq.getDate());
                        safeInv = o2.get(0).getFutureWeekPeak();
                    }
                    break;
                case "historyWeekPeakForecastWN":
                    safeInv = o2.get(0).getHistoryWeekPeakForecastWN();

                    if (safeInv == null) {
                        operationViewService2.fillNullHistoryWeekPeakForecastWeekNSafetyInvItem(o2.get(0), invReq.getDate());
                        safeInv = o2.get(0).getHistoryWeekPeakForecastWN();
                    }
                    break;
                default:
                    throw BizException.makeThrow("该安全算法不存在：" + operationViewAlgorithm);
            }

            if (safeInv == null) {
                throw BizException.makeThrow("安全库存为空，联系 brightwwu 处理");
            }

            o1.get(0).setSla(safeInv.getSla());
            o1.get(0).setSafetyInventoryCore(safeInv.getSafetyInv().intValue());
            o1.get(0).setSafeInvManualConfig(safeInv.getSafeInvManualConfig());
            o1.get(0).setPrePaidSafetyInventoryCore(safeInv.getMonthlySafetyInv().intValue());
            o1.get(0).setServiceLevel(safeInv.getServiceLevel());
            o1.get(0).setServiceLevelFactor(safeInv.getServiceLevelFactor());
            o1.get(0).setSafetyInventoryCoreExpression(safeInv.getSafetyInventoryCoreExpression());

            if (safeInv.getMediumLongTailForecastDemandCore() != null) {
                o1.get(0).setForecastDemandCore(safeInv.getMediumLongTailForecastDemandCore().intValue());
            }

            o1.get(0).setDemandAvg(safeInv.getDemandAvg());
            o1.get(0).setStandardDiff(safeInv.getStandardDiff());
            o1.get(0).setMediumLongTailSafetyInv(safeInv.getMediumLongTailSafetyInv());
            o1.get(0).setHeadZlkhbSafetyInv(safeInv.getHeadZlkhbSafetyInv());
            o1.get(0).setHeadNotZlkhbSafetyInv(safeInv.getHeadNotZlkhbSafetyInv());
            o1.get(0).setForecastDemandAccuracyRate(safeInv.getForecastDemandAccuracyRate());
            o1.get(0).setDeliveryAvg(safeInv.getDeliveryAvg());
            o1.get(0).setDeliveryStandardDiff(safeInv.getDeliveryStandardDiff());

            if (safeInv.getTurnoverInv() == null) {
                if (safeInv.getForecastTurnOverInvMap() == null || safeInv.getForecastTurnOverInvMap().get("w0") == null) {
                    o1.get(0).setForecastTurnoverInv(BigDecimal.ZERO);
                    o1.get(0).setForecastTurnoverWeekPeakAvg12Core(BigDecimal.ZERO);
                    o1.get(0).setForecastTurnoverWeekDemandCore(BigDecimal.ZERO);
                } else {
                    // 取 W0 周预测
                    Map<String, BigDecimal> w0ForecastTurnoverInv = safeInv.getForecastTurnOverInvMap().get("w0");
                    o1.get(0).setForecastTurnoverInv(w0ForecastTurnoverInv.get("turnoverInv"));
                    o1.get(0).setForecastTurnoverWeekPeakAvg12Core(w0ForecastTurnoverInv.get("weekPeakCore"));
                    o1.get(0).setForecastTurnoverWeekDemandCore(w0ForecastTurnoverInv.get("avgForecastCore"));
                }
            } else {
                // 周转库存相关
                if (invReq.getIsIncludeReserved() == null || invReq.getIsIncludeReserved()) {
                    o1.get(0).setTurnoverInv(safeInv.getTurnoverInv());
                } else {
                    o1.get(0).setTurnoverInv(safeInv.getTurnoverInvWeekPeak());
                }
                o1.get(0).setTurnoverWeekPeakCore(safeInv.getTurnoverInvWeekPeak());
                o1.get(0).setTurnoverWeekReservedAvgCore(safeInv.getTurnoverInvReserved());
            }

            BigDecimal turnoverInv = o1.get(0).getTurnoverInv() == null ? o1.get(0).getForecastTurnoverInv() : o1.get(0).getTurnoverInv();
            turnoverInv = turnoverInv == null ? BigDecimal.ZERO : turnoverInv;

            if (o1.get(0).getActualInventoryCore() != null && o1.get(0).getSafetyInventoryCore() != null && o1.get(0).getSafetyInventoryCore() > 0) {
                // 冗余系数=实际库存 / (安全库存 + 周转库存)
                o1.get(0).setHealthRatio(
                        NumberUtils.divide(
                                o1.get(0).getActualInventoryCore(),
                                turnoverInv.add(
                                        BigDecimal.valueOf(o1.get(0).getSafetyInventoryCore())
                                ),
                                2
                        )
                );
            } else {
                o1.get(0).setHealthRatio(BigDecimal.ZERO);
            }

            return o1.get(0);
        }));
    }

//    public List<ActualInventoryDTO> getActualInv(WhereSQL condition, InventoryHealthActualReq req) {
//        OperationViewService2Impl operationViewService2 = SpringUtil.getBean(OperationViewService2Impl.class);
//
//        // 填充通用的条件
//        operationViewService2.genBaseFieldsCondition(condition, req.getLineType(), req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), true, req.getDate());
//
//        List<ActualInventoryDTO> all = ckcldDBHelper.getAll(ActualInventoryDTO.class, condition.getSQL(), condition.getParams());
//
//        //  2、 过滤好差呆类型
//        //  补全线下库存部分的好差类型的字段
//        return operationViewService2.filterByMaterialType(
//                all.stream().map(item -> (ActualInventoryAvgDTO) item).collect(Collectors.toList()),
//                req.getMaterialType()
//        ).stream().map(item -> (ActualInventoryDTO) item).collect(Collectors.toList());
//    }

    private InventoryHealthActualResp constructResp(List<OperationViewResp2.Item> mergedItems) {
        InventoryHealthActualResp resp = new InventoryHealthActualResp();
        resp.setData(new ArrayList<>());

        for (OperationViewResp2.Item dto : mergedItems) {
            InventoryHealthActualResp.Item item = new InventoryHealthActualResp.Item();
            item.setCustomhouseTitle(dto.getCustomhouseTitle());
            item.setAreaName(dto.getAreaName());
            item.setRegionName(dto.getRegionName());
            item.setZoneName(dto.getZoneName());
            item.setInstanceType(dto.getInstanceType());

            // 设置弹性开始时间和弹性结束时间
            item.setBufferAverageStartDate(dto.getBufferAverageStartDate());
            item.setBufferAverageEndDate(dto.getBufferAverageEndDate());

            //  总库存
            item.setActualInventoryCore(dto.getInvTotalNum().intValue());
            //  弹性服务水平/系数
            item.setBufferServiceLevel(dto.getBufferServiceLevel());
            item.setBufferServiveLevelFactor(dto.getBufferServiceLevelFactor());
            //  弹性规模日均值
            item.setBufferAverageCore(dto.getBufferAverageCore().intValue());
            //  弹性备货配额-三种算法目前结果完全相同，因此先抽取到这里
            item.setBufferSafetyInventoryCore(dto.getBufferSafetyInv());
            item.setBufferRoi(dto.getBufferRoi());
            item.setBufferRate(dto.getBufferRate());

            resp.getData().add(item);
        }

        return resp;
    }

    private Future<Map<String, YunxiaoGridItemVO>> getYunxiaoGrid(WhereSQL condition, InventoryHealthActualReq req) {
        // 检查好料和库存类型，如果选了，必须包含好料和线上库存
        if (!ListUtils.isEmpty(req.getLineType()) && !req.getLineType().contains("线上库存")) {
            throw new BizException("选中剔除预扣选项时，库存类型必须包含线上库存，当前选中的库存类型为：" + req.getLineType());
        }
        if (!ListUtils.isEmpty(req.getMaterialType()) && !req.getMaterialType().contains("好料")) {
            throw new BizException("选中剔除预扣选项时，必须包含好料，当前选中了：" + req.getMaterialType());
        }

        return threadPool.submit(() -> {
            // 复用新CVM运营视图的逻辑
            OperationViewService2Impl operationViewService2 = SpringUtil.getBean(OperationViewService2Impl.class);
            //  针对机型类型、园区类型进行筛选
            WhereSQL categoryCondition = operationViewService2.genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), req.getDate());
            condition.and(categoryCondition);
            condition.addGroupBy("instance_type", "zone_name");

            List<YunxiaoGridItemVO> all = ckcldDBHelper.getAll(YunxiaoGridItemVO.class, condition.getSQL(), condition.getParams());
            return all.stream().collect(Collectors.toMap(o -> String.join(o.getZoneName(), "@", o.getInstanceType()), o -> o));
        });
    }
    /**
     * 获取某个日切片的实际库存和安全库存
     *
     * @param req
     * @return
     */
    private InventoryHealthActualResp getActualAndSafeInvByDay(InventoryHealthActualReq req) {
        WhereSQL condition = req.genCondition();
        // 查询预扣块信息信息，并按照机型、可用区分组
        Future<Map<String, YunxiaoGridItemVO>> gridItemMapFuture = null;

        if (req.getIsIgnoreReserved() != null && req.getIsIgnoreReserved()) {
            gridItemMapFuture = getYunxiaoGrid(condition.copy(), req);
        }

        // 复用新CVM运营视图的逻辑
        OperationViewService2Impl operationViewService2 = SpringUtil.getBean(OperationViewService2Impl.class);
        ActualInventoryListReq params = new ActualInventoryListReq();
        params.setStatTime(req.getDate());
        params.setLineType(req.getLineType());
        params.setMaterialType(req.getMaterialType());
        params.setInvDetailType(req.getInvDetailType());
        params.setIsCombine(req.getIsCombine());
        params.setZoneCategory(req.getZoneCategory());
        params.setInstanceTypeCategory(req.getInstanceTypeCategory());
        params.setCategoryDate(req.getCategoryDate());
        // 获取实际库存和弹性备货配额，复用运营视图的逻辑
        List<DwsActualInventoryDfDO> all = operationViewService2.getActualInventoryList(params, condition);
        List<DwsBufferSafeInventoryDfDO> bufferSafeInventoryDfDOS = operationViewService2.getBufferSafeInventoryList(params, condition);
        // 3、找到最近1个月的弹性用量数据，计算出弹性用来的安全库存 X%(服务水平）*L(弹性量/天)
        //    L=“近”一个月的弹性用量“天”均数据
//        List<BufferAverageCoreDTO> bufferAverageCoreDTOS = filterBufferAverageCore(req);

        //  4、合并实际库存和弹性备货配额的结果，传给下游
//        all = operationViewService2.mergeData(
//                req.getDate(),
//                all.stream().map(item -> (ActualInventoryAvgDTO) item).collect(Collectors.toList()),
//                bufferAverageCoreDTOS
//        ).stream().map(item -> (ActualInventoryDTO) item).collect(Collectors.toList());
        List<OperationViewResp2.Item> mergedItems = operationViewService2.mergeActualBufferInventoryData(req.getDate(), all, bufferSafeInventoryDfDOS);

        // 5、构造实际库存接口返回
        InventoryHealthActualResp resp = constructResp(mergedItems);

        // 获取并填充安全库存，日切片取选中的日期
        getSafeInv(req, resp);

        ForecastViewServiceImpl forecastViewService = SpringUtil.getBean(ForecastViewServiceImpl.class);

        if (req.getIsCombine() != null && req.getIsCombine()) {
            //  查询机型组合的配置
            InstanceTypeCombineDTO instanceTypeCombineDTO = inventoryHealthDictService.queryAllCombineInstanceType();
            resp.setData(forecastViewService.combineItem(instanceTypeCombineDTO.getCombination(), resp.getData(), req.getInstanceTypeCategory(), req.getDate()));
            resp.setErrorMsg(StringTools.isNotBlank(instanceTypeCombineDTO.getErrorMsg()) ?
                    instanceTypeCombineDTO.getErrorMsg() : null);
        }

        // 真实服务水平
        Map<String, List<DwsCloudServerLevelDO>> m = forecastViewService.getSla(req.getDate());

        try {
            Map<String, YunxiaoGridItemVO> gridItemVOMap = gridItemMapFuture != null ? gridItemMapFuture.get() : null;

            resp.getData().forEach(item -> {
                if (item.getInstanceType().contains("/")) {
                    List actualSla = new ArrayList();
                    Splitter.on("/").splitToList(item.getInstanceType())
                            .forEach(s -> {
                                List ls = m.get(item.getZoneName() + "@" + s);
                                if (ls != null && !ls.isEmpty()) {
                                    actualSla.addAll(ls);
                                }
                            });
                    item.setActualSla(actualSla);
                } else {
                    item.setActualSla(m.get(item.getZoneName() + "@" + item.getInstanceType()));
                }

                // 如果指定了客户预扣则增加客户预扣字段，并重新计算冗余系数
                if (gridItemVOMap != null) {
                    YunxiaoGridItemVO gridItemVO = gridItemVOMap.get(String.join(item.getZoneName(), "@", item.getInstanceType()));
                    item.setReservedCores(gridItemVO == null ? 0 : gridItemVO.getSumCores());

                    if (item.getActualInventoryCore() != null && item.getSafetyInventoryCore() != null && item.getSafetyInventoryCore() > 0) {
                        item.setHealthRatio(NumberUtils.divide(item.getActualInventoryCore() - item.getReservedCores(), item.getSafetyInventoryCore(), 2));
                    } else {
                        item.setHealthRatio(BigDecimal.ZERO);
                    }
                }
            });

            return resp;
        } catch (Exception e) {
            log.error("获取库存健康数据失败", e);
            throw BizException.makeThrow("获取库存健康数据失败，请重试，若多次失败请联系 brightwwu");
        }

    }

    /**
     * 根据日切片查询库存健康度
     *
     * @param req
     * @return
     */
    private InventoryHealthActualResp queryInventoryHealthActualByDay(InventoryHealthActualReq req) {
        OperationServiceImpl operationService = SpringUtil.getBean(OperationServiceImpl.class);
        String startDate = DateUtils.formatDate(DateUtils.addTime(DateUtils.parse(req.getDate()), Calendar.DATE, -30));
        // 查询给定日期下，近一个月所有的原因分析，然后按照 机型 + 可用区 分组
        Future<Map<String, List<InventoryReasonDO>>> reasonMapFuture = operationService.listRangeReasonsParallelByInstanceTypeAndZoneName(startDate, req.getDate());

        InventoryHealthActualResp resp = getActualAndSafeInvByDay(req);

        // 设置配置的组合机型表
        List<Set<String>> combination = SpringUtil.getBean(InventoryHealthDictServiceImpl.class).
                queryAllCombineInstanceType().getCombination();
        resp.setCombineList(ListUtils.transform(combination, o -> String.join("/", o)));

        // 设置原因详情
        try {
            Map<String, List<InventoryReasonDO>> reasonMap = reasonMapFuture.get();
            resp.getData().forEach(item -> {
                String key = String.join(item.getInstanceType(), "@", item.getZoneName());
                List<InventoryReasonDO> list = reasonMap.get(key);
                if (list != null && !list.isEmpty()) {
                    item.setReasons(list.stream().map(o -> {
                        InventoryHealthActualResp.ReasonItem reasonItem = new InventoryHealthActualResp.ReasonItem();
                        reasonItem.setReasonId(o.getId());
                        reasonItem.setReasonDate(DateUtils.formatDate(o.getDate()));
                        reasonItem.setReasonType(o.getReasonType());
                        reasonItem.setReasonDetail(o.getReasonDetail());
                        return reasonItem;
                    }).collect(Collectors.toList()));
                }
            });
        } catch (Exception e) {
            log.error("设置原因详情失败", e);
        }

        return resp;
    }

    private InventoryHealthActualResp queryInventoryHealthActualByRangeAvg(InventoryHealthActualReq req) {
        // 复用安全库存趋势的日期范围。周取节假周
        Date beginDate;
        Date endDate;

        LocalDate reqDate = LocalDate.parse(req.getDate());
        TrendGraphServiceImpl trendGraphService = SpringUtil.getBean(TrendGraphServiceImpl.class);
        switch (req.getTimeDimension()) {
            case "周日均":
                // reqDate 始终是周一，时间范围取 req.getDate() 到 req.getDate() + 7
                List<ResPlanHolidayWeekDO> weekDOS = trendGraphService.getHolidayWeekInfo(reqDate, reqDate.plusDays(7));
                // 取到的第一周作为结果
                ResPlanHolidayWeekDO weekDO = weekDOS.get(0);
                beginDate = DateUtils.parse(weekDO.getStart());
                // 如果昨天小于周结束，则取昨天
                LocalDate yesterday = DateUtils.yesterday();

                if (yesterday.isBefore(LocalDate.parse(weekDO.getEnd()))) {
                    endDate = DateUtils.parse(yesterday.toString());
                } else {
                    endDate = DateUtils.parse(weekDO.getEnd());
                }
                break;
            case "月日均":
                TrendGraphReq trendGraphReq = new TrendGraphReq();
                trendGraphReq.setDateRange("month");
                trendGraphReq.setBeginDate(reqDate);
                trendGraphReq.setEndDate(reqDate);
                List<LocalDate> days = trendGraphService.getDaysFromParams(trendGraphReq);
                beginDate = DateUtils.parse(days.get(0).toString());
                endDate = DateUtils.parse(days.get(days.size() - 1).toString());
                break;
            default:
                throw BizException.makeThrow("仅支持 `周日均` 和 `月日均` 两个维度");
        }


        Date finalEndDate = endDate;

        List<Future<InventoryHealthActualResp>> futures = new ArrayList<>();

        Date tempBeginDate = beginDate;

        while (!tempBeginDate.after(finalEndDate)) {
            String date = DateUtils.formatDate(tempBeginDate);
            futures.add(threadPool.submit(() -> {
                log.info("计算{}的InventoryHealthActual", date);
                InventoryHealthActualReq clonedReq = req.clone();
                clonedReq.setDate(date);
                // 主力机型以及主力可用区均取date当天的，后面汇总成并集
                clonedReq.setCategoryDate(date);
                InventoryHealthActualResp resp = getActualAndSafeInvByDay(clonedReq);
                // 查询给定日期下，近一个月所有的原因分析，然后按照 机型 + 可用区 分组
                OperationServiceImpl operationService = SpringUtil.getBean(OperationServiceImpl.class);
                String startDate = DateUtils.formatDate(DateUtils.addTime(DateUtils.parse(clonedReq.getDate()), Calendar.DATE, -30));
                Future<Map<String, List<InventoryReasonDO>>> reasonMapFuture = operationService.listRangeReasonsParallelByInstanceTypeAndZoneName(startDate, clonedReq.getDate());
                try {
                    Map<String, List<InventoryReasonDO>> reasonMap = reasonMapFuture.get();
                    resp.getData().forEach(item -> {
                        String key = String.join(item.getInstanceType(), "@", item.getZoneName());
                        List<InventoryReasonDO> list = reasonMap.get(key);
                        if (list != null && !list.isEmpty()) {
                            item.setReasons(list.stream().map(o -> {
                                InventoryHealthActualResp.ReasonItem reasonItem = new InventoryHealthActualResp.ReasonItem();
                                reasonItem.setReasonId(o.getId());
                                reasonItem.setReasonDate(DateUtils.formatDate(o.getDate()));
                                reasonItem.setReasonType(o.getReasonType());
                                reasonItem.setReasonDetail(o.getReasonDetail());
                                return reasonItem;
                            }).collect(Collectors.toList()));
                        }
                    });
                } catch (Exception e) {
                    log.error("设置原因详情失败", e);
                }
                return resp;
            }));

            tempBeginDate = DateUtils.addTime(tempBeginDate, Calendar.DATE, 1);
        }

        InventoryHealthActualResp resp = new InventoryHealthActualResp();
        resp.setData(new ArrayList<>());

        for (Future<InventoryHealthActualResp> future : futures) {
            try {
                resp.getData().addAll(future.get().getData());
            } catch (InterruptedException | ExecutionException e) {
                log.error("queryInventoryHealthActualByWeekAvg error", e);
                throw BizException.makeThrow("查询库存健康度失败，请联系 brightwwu 处理");
            }
        }

        // 按照主要属性分组求平均
        Map<String, List<InventoryHealthActualResp.Item>> map = ListUtils.groupBy(resp.getData(), o -> o.toKey());
        resp.setData(ListUtils.transform(map.entrySet(), e -> {
            InventoryHealthActualResp.Item item = new InventoryHealthActualResp.Item();
            item.setCustomhouseTitle(e.getValue().get(0).getCustomhouseTitle());
            item.setAreaName(e.getValue().get(0).getAreaName());
            item.setRegionName(e.getValue().get(0).getRegionName());
            item.setZoneName(e.getValue().get(0).getZoneName());
            item.setInstanceType(e.getValue().get(0).getInstanceType());

            List<Integer> actualInventory = e.getValue().stream().map(o -> o.getActualInventoryCore()).collect(Collectors.toList());
            List<Integer> safeInventory = e.getValue().stream().map(o -> o.getSafetyInventoryCore()).collect(Collectors.toList());
            List<BigDecimal> invManualConfig = e.getValue().stream().map(o -> o.getSafeInvManualConfig()).collect(Collectors.toList());
            List<Integer> prePaidSafeTyInventory = e.getValue().stream().map(o -> o.getPrePaidSafetyInventoryCore()).collect(Collectors.toList());
            List<BigDecimal> bufferInventory = e.getValue().stream().map(o -> o.getBufferSafetyInventoryCore()).collect(Collectors.toList());
            List<Integer> reservedCores = e.getValue().stream().map(o -> o.getReservedCores()).collect(Collectors.toList());
            List<BigDecimal> deliveryAvgs = e.getValue().stream().map(o -> o.getDeliveryAvg()).collect(Collectors.toList());
            List<BigDecimal> levels = e.getValue().stream().map(o -> o.getServiceLevel()).collect(Collectors.toList());
            List<BigDecimal> levelFactors = e.getValue().stream().map(o -> o.getServiceLevelFactor()).collect(Collectors.toList());
            List<BigDecimal> bufferLevels = e.getValue().stream().map(o -> o.getBufferServiceLevel()).collect(Collectors.toList());
            List<BigDecimal> bufferLevelFactors = e.getValue().stream().map(o -> o.getBufferServiveLevelFactor()).collect(Collectors.toList());
            List<Integer> averageCores = e.getValue().stream().map(o -> o.getBufferAverageCore()).collect(Collectors.toList());
            List<BigDecimal> turnoverInvs = e.getValue().stream().map(o -> o.getTurnoverInv()).collect(Collectors.toList());
            List<BigDecimal> weekAvgCores = e.getValue().stream().map(o -> o.getTurnoverWeekReservedAvgCore()).collect(Collectors.toList());
            List<BigDecimal> peakCores = e.getValue().stream().map(o -> o.getTurnoverWeekPeakCore()).collect(Collectors.toList());
            List<String> startDates = e.getValue().stream().map(o -> o.getBufferAverageStartDate()).filter(Objects::nonNull).collect(Collectors.toList());
            List<String> endDates = e.getValue().stream().map(o -> o.getBufferAverageEndDate()).filter(Objects::nonNull).collect(Collectors.toList());
            String firstDate = null;
            String lastDate = null;
            if (ListUtils.isNotEmpty(startDates)) {
                startDates.sort(String::compareTo);
                firstDate = startDates.get(0);
            }

            if (ListUtils.isNotEmpty(endDates)) {
                endDates.sort(String::compareTo);
                lastDate = endDates.get(endDates.size() - 1);
            }

            item.setActualInventoryCore(NumberUtils.avg(actualInventory, 2).intValue());
            item.setSafetyInventoryCore(NumberUtils.avg(safeInventory, 2).intValue());
            item.setSafeInvManualConfig(NumberUtils.avg(invManualConfig, 2));
            item.setPrePaidSafetyInventoryCore(NumberUtils.avg(prePaidSafeTyInventory, 2).intValue());
            item.setBufferSafetyInventoryCore(NumberUtils.avg(bufferInventory, 2));
            item.setReservedCores(NumberUtils.avg(reservedCores, 2).intValue());
            item.setDeliveryAvg(NumberUtils.avg(deliveryAvgs, 2));
            item.setServiceLevel(NumberUtils.avg(levels, 2));
            item.setServiceLevelFactor(NumberUtils.avg(levelFactors, 2));
            item.setBufferServiceLevel(NumberUtils.avg(bufferLevels, 2));
            item.setBufferServiveLevelFactor(NumberUtils.avg(bufferLevelFactors, 2));
            item.setBufferAverageCore(NumberUtils.avg(averageCores, 2).intValue());
            item.setTurnoverInv(NumberUtils.avg(turnoverInvs, 2));
            item.setTurnoverWeekReservedAvgCore(NumberUtils.avg(weekAvgCores, 2));
            item.setTurnoverWeekPeakCore(NumberUtils.avg(peakCores, 2));

            //设置BufferAverageStartDate与BufferAverageEndDate
            item.setBufferAverageStartDate(firstDate);
            item.setBufferAverageEndDate(lastDate);


            if (item.getActualInventoryCore() != null && item.getSafetyInventoryCore() != null && item.getSafetyInventoryCore() > 0) {
                Integer actualInv = item.getActualInventoryCore();

                if (req.getIsIgnoreReserved() != null && req.getIsIgnoreReserved()) {
                    actualInv = actualInv - item.getReservedCores();
                }
                item.setHealthRatio(NumberUtils.divide(BigDecimal.valueOf(actualInv), BigDecimal.valueOf(item.getSafetyInventoryCore()), 2));
            } else {
                item.setHealthRatio(BigDecimal.ZERO);
            }

            // 真实服务水平组合起来，直接把原始值返回，前端算
            List<DwsCloudServerLevelDO> actualSla = new ArrayList<>();
            e.getValue().forEach(o -> {
                if (o.getActualSla() != null) {
                    actualSla.addAll(o.getActualSla());
                }
            });
            if (ListUtils.isNotEmpty(actualSla)) {
                item.setActualSla(actualSla);
            }

            //将原因汇总起来并去重
            List<InventoryHealthActualResp.ReasonItem> reasonSet = new ArrayList<>();
            e.getValue().forEach(o -> {
                List<InventoryHealthActualResp.ReasonItem> reasons = o.getReasons();
                if (ListUtils.isNotEmpty(reasons)) {
                    reasonSet.addAll(reasons);
                }
            });
            Map<String, List<InventoryHealthActualResp.ReasonItem>> mapList = ListUtils.toMapList(reasonSet, o -> String.valueOf(o.getReasonId()), o -> o);
            List<InventoryHealthActualResp.ReasonItem> reason = new ArrayList<>();
            for (List<InventoryHealthActualResp.ReasonItem> value : mapList.values()) {
                if (ListUtils.isNotEmpty(value)) {
                    reason.add(value.get(0));
                }
            }
            item.setReasons(reason);
            return item;
        }));

        // 设置配置的组合机型表
        List<Set<String>> combination = SpringUtil.getBean(InventoryHealthDictServiceImpl.class).
                queryAllCombineInstanceType().getCombination();
        resp.setCombineList(ListUtils.transform(combination, o -> String.join("/", o)));

        return resp;
    }

    /**
     * 根据周日均查询库存健康度
     */
    private InventoryHealthActualResp queryInventoryHealthActualByWeekAvg(InventoryHealthActualReq req) {
        return queryInventoryHealthActualByRangeAvg(req);
    }

    /**
     * 根据月日均查询库存健康度
     */
    private InventoryHealthActualResp queryInventoryHealthActualByMonthAvg(InventoryHealthActualReq req) {
        return queryInventoryHealthActualByRangeAvg(req);
    }
}
