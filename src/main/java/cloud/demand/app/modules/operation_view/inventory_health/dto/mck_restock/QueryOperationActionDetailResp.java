package cloud.demand.app.modules.operation_view.inventory_health.dto.mck_restock;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class QueryOperationActionDetailResp {
    private List<Item> data;

    @Data
    public static class Item {
        /**
         * 设备类型
         */
        private String deviceType;
        /**
         * 实例类型
         */
        private String instanceType;
        /**
         * 行业部门
         */
        private String industryDept;

        /**
         * 客户名称
         */
        private String customName;
        /**
         * 可用区
         */
        private String zoneName;

        /**
         * 区域名称
         */
        private String areaName;
        /**
         * 地域名称
         */
        private String regionName;
        /**
         * cloud提单时间
         */
        private String createTime;
        /**
         * Q单号
         */
        private String quotaId;
        /**
         * 预计交付时间
         */
        private String expectDeliveryTime;
        /**
         * 期望交付时间
         */
        private String useTime;
        /**
         * 需求核心数
         */
        private BigDecimal demandCoreNum;
        /**
         * 未到货核心数
         */
        private BigDecimal noArrivalCoreNum;
    }



}
