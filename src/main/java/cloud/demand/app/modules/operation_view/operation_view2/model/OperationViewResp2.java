package cloud.demand.app.modules.operation_view.operation_view2.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OperationViewResp2 {

    /**周的区间时间，等同于OperationViewRsp的*/
    private Map<String, String> weekInterval;

    /**数据*/
    private List<Item> data;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Item {
        /** 产品类型 */
        private String productType;

        /** 境内外 */
        private String customhouseTitle;
        /** 区域名 */
        private String areaName;
        /** 地域名 */
        private String regionName;
        /** 可用区名 */
        private String zoneName;

        /**实例类型*/
        private String instanceType;

        /** 总库存量 */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal invTotalNum = BigDecimal.ZERO;
        /** 库存金额 */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal invPrice = BigDecimal.ZERO;
        /** 库存占比 */
        @JsonSerialize(using = BigDecimal4ScaleSerializer.class)
        private BigDecimal invRatio = BigDecimal.ZERO;

        /**未来峰值计算的安全库存结果*/
        private SafetyInventoryResult futureWeekPeak;
        /**历史周净增计算的安全库存结果*/
        private SafetyInventoryResult historyWeekDiff;
        /**历史周峰计算的安全库存结果*/
        private SafetyInventoryResult historyWeekPeak;

        /**算法4：历史周峰 + WN预测计算的安全库存结果*/
        private SafetyInventoryResult historyWeekPeakForecastWN;

        /**
         * 安全库存阈值-实时数据
         * 默认值：如果从未设置过就是null，设置过删除掉就是0
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal safetyInvThreshold;

        /**
         * 安全库存阈值-当日切片数据
         * 默认值：如果从未设置过就是null，设置过删除掉就是0
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal safetyInvThresholdSnapshot;

        /**
         * 弹性备货配额
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal bufferSafetyInv = BigDecimal.ZERO;

        /**
         * 弹性服务水平
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal bufferServiceLevel;

        /**
         * 弹性服务水平系数
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal bufferServiceLevelFactor;

        /**
         * 弹性 ROI
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal bufferRoi;

        /**
         * 弹性利用率
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal bufferRate;

        /**
         * 弹性用量平均核心数
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal bufferAverageCore;

        /**
         * 弹性用量开始时间
         */
        private String bufferAverageStartDate;

        /**
         * 弹性用量结束时间
         */
        private String bufferAverageEndDate;

        /**
         * 安全库存人工调整
         */
        private BigDecimal safeInvManualConfig;

        /**
         * 冗余系数
         */
        private BigDecimal healthRatio;

        /**
         * 实际服务水平
         */
        private BigDecimal actualSerivceLevel;

//        /**
//         * 弹性备货配额的获取方法，三种算法哪个有值用哪个
//         * 虽然IDEA显示无处调用，但是这段代码不要删除
//         * 目前三种算法的计算逻辑相同，未来可能会扩展
//         */
//        public BigDecimal getBufferSafetyInv(){
//            BigDecimal result = null;
//            if (futureWeekPeak != null){
//                result = futureWeekPeak.getBufferSafetyInv();
//            }else if (historyWeekDiff != null){
//                result = historyWeekDiff.getBufferSafetyInv();
//            }else if (historyWeekPeak != null){
//                result = historyWeekPeak.getBufferSafetyInv();
//            }
//            return result == null ? BigDecimal.ZERO : result;
//        }

        public String toKey() {
            return StringTools.join("@", productType, customhouseTitle, areaName, regionName, zoneName, instanceType);
        }

        public String toKeyNoProduct() {
            return StringTools.join("@", customhouseTitle, areaName, regionName, zoneName, instanceType);
        }
    }

    /**
     * 安全库存计算结果，不同的算法有不同的结果
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SafetyInventoryResult {

        /**
         * 安全库存算法
         */
        String algorithm;

        /**安全库存 = 包月安全库存 + 弹性备货配额 + 人工调整 */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal safetyInv;

        /**包月安全库存*/
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal monthlySafetyInv;

        /** 弹性备货配额，特别说明，虽然现在弹性备货配额3种算法都一样，
         * 但它其实应该跟着算法走的，包括参与计算和未来扩展性的设计的考虑*/
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal bufferSafetyInv;

        /**周转库存 - 13周均值*/
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal turnoverInv;

        /**周转库存 - 13历史周峰净增均值*/
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal turnoverInvWeekPeak;

        /**周转库存 - 13周闲置入口周转库存均值*/
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal turnoverInvReserved;

        /**
         * 周转库存 - 当周，如果是非历史周，则为 null
         */
        private BigDecimal turnoverInvCurWeek;

        /**周转库存 - 当周执行量周峰*/
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal turnoverInvCurWeekPeak;

        /**周转库存 - 当周用户预扣均值*/
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal turnoverInvCurWeekAvgReserved;

        /**冗余库存=库存量-安全库存-周转库存*/
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal redundancyInv;

        //  13周历史周需求量，k是w1 w2 w3递推, v是对应那周的结果
        private Map<String, BigDecimal> weekDemandMap;
        //  13周WN预测量，k是w1 w2 w3递推,v对应那周的结果，值为均摊到每周之后的预测值
        private Map<String, BigDecimal> weekNForecastMap;

        //  未来周需求算法-未来13周需要用到的数据
        //  未来可能会收敛到这个接口中
        private Map<String, InventoryForecastInfo> futureWeekMap;

        /**
         * 需求标准差
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal standardDiff;

        /**
         * 需求平均值
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal demandAvg;

        /**
         * 13周交付周期标准差，算法考虑供应时才设置
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal deliveryStandardDiff;

        /**
         * 13周交付周期平均值，算法考虑供应时才设置
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal deliveryAvg;

        /**
         * 交付SLA
         */
        private Integer sla;

        /**
         * 服务水平
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal serviceLevel;

        /**
         * 服务水平系数
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal serviceLevelFactor;

        /**
         * 需求预测量(核心数)
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal forecastDemandCore;

        /**
         * 需求预测量-中长尾(核心数)
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal mediumLongTailForecastDemandCore;

        /**
         * 需求预测量-头部战略客户部(核心数)
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal headZlkhbForecastDemandCore;

        /**
         * 需求预测量-头部非战略客户部(核心数)
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal headNotZlkhbForecastDemandCore;

        /**for未来预测 中长尾客户包月安全库存 */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal mediumLongTailSafetyInv;

        /**for未来预测 头部-战略包月安全库存 */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal headZlkhbSafetyInv;

        /**for未来预测 头部-非战略包月安全库存 */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal headNotZlkhbSafetyInv;

        /**
         * 需求预测准确率
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal forecastDemandAccuracyRate;

        /**
         * 弹性服务水平
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal bufferServiceLevel;

        /**
         * 弹性服务水平系数
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal bufferServiceLevelFactor;

        /**
         * 弹性用量平均核心数
         */
        private Integer bufferAverageCore;

        /**
         * 计算出安全库存核心数的表达式
         */
        private String safetyInventoryCoreExpression;

        /**
         * 安全库存人工调整
         */
        private BigDecimal safeInvManualConfig;

        /**
         * W0 - W13 的预测周转库存，以及其计算的明细底数
         */
        private Map<String, Map<String, BigDecimal>> forecastTurnOverInvMap;

        /**
         * W0 - W13 的预测包月安全库存，以及其计算的明细底数
         */
        private Map<String, Map<String, BigDecimal>> forecastMonthSafetyInvMap;

        /**
         * W0 - W13 的预测安全库存
         */
        private Map<String, BigDecimal> forecastSafetyInvMap;

        /**
         * 周转周数
         */
        private BigDecimal turnoverWeekNum;

        /**
         * 历史 13 周安全库存均值
         */
        private BigDecimal historySafeInvAvg13;
    }

    /**
     * 库存预测最低、最高水位需要用到的数据，By周
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InventoryForecastInfo{
        //  安全库存
        private BigDecimal safetyInv;
        //  周转库存
        private BigDecimal turnoverInv;
    }

    public synchronized void putHistoryWeekDiffToItem(List<Item> item) {
        if (data == null) {
            data = new ArrayList<>();
        }

        Map<String, Item> map = ListUtils.toMap(data, o -> o.toKey(), o -> o);

        for (Item i : item) {
            Item findOne = map.get(i.toKey());
            if (findOne != null){
                findOne.setHistoryWeekDiff(i.getHistoryWeekDiff());
            } else {
                findOne = i;
                data.add(findOne);
            }
            //  计算下冗余库存
            SafetyInventoryResult historyWeekDiff = findOne.getHistoryWeekDiff();
            historyWeekDiff.setRedundancyInv(findOne.getInvTotalNum().subtract(
                    historyWeekDiff.getSafetyInv()).subtract(historyWeekDiff.getTurnoverInv()));
        }

    }

    /**
     * 串行改批量 注意线程安全
     */
    public synchronized void putHistoryWeekPeakToItem(List<Item> item) {
        if (data == null) {
            data = new ArrayList<>();
        }

        Map<String, Item> map = ListUtils.toMap(data, o -> o.toKey(), o -> o);

        for (Item i : item) {
            Item findOne = map.get(i.toKey());
            if (findOne != null){
                findOne.setHistoryWeekPeak(i.getHistoryWeekPeak());
            } else {
                findOne = i;
                data.add(findOne);
            }
            // 计算下冗余库存
            SafetyInventoryResult historyWeekPeak = findOne.getHistoryWeekPeak();
            historyWeekPeak.setRedundancyInv(findOne.getInvTotalNum().subtract(
                    historyWeekPeak.getSafetyInv()).subtract(historyWeekPeak.getTurnoverInv()));
        }

    }

    /**
     * 串行改批量 注意线程安全
     */
    public synchronized void putHistoryWeekPeakForecastWNToItem(List<Item> item) {
        if (data == null) {
            data = new ArrayList<>();
        }

        Map<String, Item> map = ListUtils.toMap(data, o -> o.toKey(), o -> o);

        for (Item i : item) {
            Item findOne = map.get(i.toKey());
            if (findOne != null){
                findOne.setHistoryWeekPeakForecastWN(i.getHistoryWeekPeakForecastWN());
            } else {
                findOne = i;
                data.add(findOne);
            }
            // 计算下冗余库存
            SafetyInventoryResult historyWeekPeak = findOne.getHistoryWeekPeakForecastWN();
            BigDecimal turnoverInv = historyWeekPeak.getTurnoverInv() != null ? historyWeekPeak.getTurnoverInv() : BigDecimal.ZERO;
            historyWeekPeak.setRedundancyInv(findOne.getInvTotalNum().subtract(
                    historyWeekPeak.getSafetyInv()).subtract(turnoverInv));
        }

    }

    /**
     * 串行改批量 注意线程安全
     */
    public void putHistoryWeekDiffToItem(Item item) {
        if (data == null) {
            data = new ArrayList<>();
        }
        boolean isFound = false;
        for (Item i : data) {
            if (Objects.equals(i.toKey(), item.toKey())) {
                i.setHistoryWeekDiff(item.getHistoryWeekDiff());
                isFound = true;
                item = i;
                break;
            }
        }
        if (!isFound) {
            data.add(item);
        }

        // 计算下冗余库存
        SafetyInventoryResult historyWeekDiff = item.getHistoryWeekDiff();
        historyWeekDiff.setRedundancyInv(item.getInvTotalNum().subtract(
                historyWeekDiff.getSafetyInv()).subtract(historyWeekDiff.getTurnoverInv()));
    }

    /**
     * 串行改批量 注意线程安全
     */
    public synchronized void putFutureWeekPeakToItem(List<Item> item) {
        if (data == null) {
            data = new ArrayList<>();
        }

        Map<String, Item> map = ListUtils.toMap(data, o -> o.toKey(), o -> o);
        for (Item i : item) {
            Item findOne = map.get(i.toKey());
            if (findOne != null){
                findOne.setFutureWeekPeak(i.getFutureWeekPeak());
            } else {
                findOne = i;
                data.add(findOne);
            }
            // 计算下冗余库存
            SafetyInventoryResult futureWeekPeak = findOne.getFutureWeekPeak();
            futureWeekPeak.setRedundancyInv(findOne.getInvTotalNum().subtract(
                    futureWeekPeak.getSafetyInv()).subtract(futureWeekPeak.getTurnoverInv()));
        }
    }


    public void putHistoryWeekPeakToItem(Item item) {
        if (data == null) {
            data = new ArrayList<>();
        }
        boolean isFound = false;
        for (Item i : data) {
            if (Objects.equals(i.toKey(), item.toKey())) {
                i.setHistoryWeekPeak(item.getHistoryWeekPeak());
                isFound = true;
                break;
            }
        }
        if (!isFound) {
            data.add(item);
        }

        // 计算下冗余库存
        SafetyInventoryResult historyWeekPeak = item.getHistoryWeekPeak();
        historyWeekPeak.setRedundancyInv(item.getInvTotalNum().subtract(
                historyWeekPeak.getSafetyInv()).subtract(historyWeekPeak.getTurnoverInv()));
    }

    public void putFutureWeekPeakToItem(Item item) {
        if (data == null) {
            data = new ArrayList<>();
        }
        boolean isFound = false;
        for (Item i : data) {
            if (Objects.equals(i.toKey(), item.toKey())) {
                i.setFutureWeekPeak(item.getFutureWeekPeak());
                isFound = true;
                break;
            }
        }
        if (!isFound) {
            data.add(item);
        }

        // 计算下冗余库存
        SafetyInventoryResult futureWeekPeak = item.getFutureWeekPeak();
        futureWeekPeak.setRedundancyInv(item.getInvTotalNum().subtract(
                futureWeekPeak.getSafetyInv()).subtract(futureWeekPeak.getTurnoverInv()));
    }


}
