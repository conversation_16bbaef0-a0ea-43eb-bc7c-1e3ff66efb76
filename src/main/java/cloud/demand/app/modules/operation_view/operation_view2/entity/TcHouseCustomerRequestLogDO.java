package cloud.demand.app.modules.operation_view.operation_view2.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("tc_house_customer_request_log")
public class TcHouseCustomerRequestLogDO {

    /** 自增id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time", isKey = true)
    private LocalDateTime createTime;

    /** 请求方式<br/>Column: [request_method] */
    @Column(value = "request_method")
    private String requestMethod;

    /** 请求url<br/>Column: [url] */
    @Column(value = "url")
    private String url;

    /** 请求体<br/>Column: [request_body] */
    @Column(value = "request_body")
    private String requestBody;

    /** 返回体<br/>Column: [response_body] */
    @Column(value = "response_body")
    private String responseBody;

}
