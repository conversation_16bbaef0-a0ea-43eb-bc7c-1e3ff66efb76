package cloud.demand.app.modules.operation_view.operation_view2.entity;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 【安全库存 - 供应汇总表】生成所需要的实际库存信息。只要线上 + 线下库存的好料 + 大核心库存
 */
@Data
public class GenSupplySummaryActualInventoryDfDO {

    /** 产品类型 */
    @Column("product_type")
    private String productType;

    /** 境内外 */
    @Column("customhouse_title")
    private String customhouseTitle;

    /** 区域名 */
    @Column("area_name")
    private String areaName;

    /** 地域名 */
    @Column("region_name")
    private String regionName;

    /** 可用区名 */
    @Column("zone_name")
    private String zoneName;

    /**实例类型*/
    @Column("instance_type")
    private String instanceType;

    /**设备类型*/
    @Column("device_type")
    private String deviceType;

    /** 库存类型 */
    @Column("line_type")
    private String lineType;

    /** 物料类型：好呆差 */
    @Column("material_type")
    private String materialType;

    /** 库存细类：大核库存、小核库存、小核预留、用户预扣等 */
    @Column("inv_detail_type")
    private String invDetailType;

    /**实际库存*/
    @Column("actual_inv")
    private BigDecimal actualInv;

    /**设备台数*/
    @Column("device_num")
    private BigDecimal deviceNum;
}
