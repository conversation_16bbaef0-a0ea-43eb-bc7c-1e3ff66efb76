package cloud.demand.app.modules.operation_view.inventory_health.dto.config;

import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class QueryInstanceTypeConfigReq {
    @NotNull
    private String date;

    /**
     * 境内外
     */
    private List<String> customhouseTitle;

    /**
     * 实例类型
     */
    private List<String> instanceType;

    /**
     * 一级类型
     */
    private List<String> type1;

    /**
     * 二级类型
     */
    private List<String> type2;


    public WhereSQL genCondition() {
        WhereSQL condition = new WhereSQL();
        condition.and("date = ?", date);
        // 默认只查询 CVM
        condition.and("(product is null or product = ?)", "CVM");

        if (ListUtils.isNotEmpty(customhouseTitle)) {
            condition.and("region_type in (?)", customhouseTitle);
        }

        if (ListUtils.isNotEmpty(instanceType)) {
            condition.and("instance_type in (?)", instanceType);
        }
        if (ListUtils.isNotEmpty(type1)) {
            condition.and("type1 in (?)", type1);
        }
        if (ListUtils.isNotEmpty(type2)) {
            condition.and("type2 in (?)", type2);
        }

        return condition;
    }
}
