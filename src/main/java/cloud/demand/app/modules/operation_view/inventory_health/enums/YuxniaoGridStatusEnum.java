package cloud.demand.app.modules.operation_view.inventory_health.enums;

import java.util.Arrays;
import java.util.List;

/**
 * 预扣状态
 * {
 *             idle: "空闲",
 *             occupied: "占用",
 *             unassigned: "未分配",
 *             unreachable: "不可用",
 *             destroyed: "到期销毁",
 *             deleted: "删除"
 *         }
 */
public enum YuxniaoGridStatusEnum {
    IDLE("idle", "空闲"),
    OCCUPIED("occupied", "占用"),
    UNASSIGNED("unassigned", "未分配"),
    UNREACHABLE("unreachable", "不可用"),
    DESTROYED("destroyed", "到期销毁"),
    MIGRATE("migrate", "迁移中"),

    DELETED("deleted", "删除");

    String code;
    String name;

    final static List<String> validGridStatus = Arrays.asList(IDLE.getCode(),OCCUPIED.getCode(),MIGRATE.getCode());

    public static List<String> getValidGridStatus() {
        return validGridStatus;
    }


    YuxniaoGridStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameFromCode(String code) {
        for (YuxniaoGridStatusEnum status : YuxniaoGridStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return null;
    }
}
