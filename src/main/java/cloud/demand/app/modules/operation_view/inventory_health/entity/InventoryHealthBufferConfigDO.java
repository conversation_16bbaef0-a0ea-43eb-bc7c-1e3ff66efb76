package cloud.demand.app.modules.operation_view.inventory_health.entity;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.common.excel.core.annotation.DotExcelEntity;
import cloud.demand.app.common.excel.core.annotation.DotExcelField;
import cloud.demand.app.modules.operation_view.inventory_health.constants.InventoryHealthExcelGroup;
import com.alibaba.excel.converters.bigdecimal.BigDecimalStringConverter;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ToString
@Table("inventory_health_buffer_config")
@DotExcelEntity
public class InventoryHealthBufferConfigDO extends BaseDO {

    @Column(value = "region_type")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_BUFFER_POOL, excelColumnName = "境内外")
    private String regionType;

    @Column(value = "instance_type")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_BUFFER_POOL, excelColumnName = "实例类型")
    private String instanceType;

    @Column(value = "rate")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_BUFFER_POOL, excelColumnName = "弹性库存利用率")
    private BigDecimal rate;

    @Column(value = "roi")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_BUFFER_POOL, excelColumnName = "ROI")
    private BigDecimal roi;

    @Column(value = "income")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_BUFFER_POOL, excelColumnName = "弹性核天收入")
    private BigDecimal income;

    @Column(value = "cost")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_BUFFER_POOL, excelColumnName = "弹性核天成本")
    private BigDecimal cost;

    @Column(value = "date")
    private Date date;

    @Column(value = "product")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_BUFFER_POOL, excelColumnName = "产品")
    private String product;

    public static InventoryHealthBufferConfigDO copy(InventoryHealthBufferConfigDO thisDO) {
        InventoryHealthBufferConfigDO bufferConfigDO = new InventoryHealthBufferConfigDO();
        bufferConfigDO.setRegionType(thisDO.getRegionType());
        bufferConfigDO.setInstanceType(thisDO.getInstanceType());
        bufferConfigDO.setRate(thisDO.getRate());
        bufferConfigDO.setRoi(thisDO.getRoi());
        bufferConfigDO.setIncome(thisDO.getIncome());
        bufferConfigDO.setCost(thisDO.getCost());
        bufferConfigDO.setDate(thisDO.getDate());
        bufferConfigDO.setProduct(thisDO.getProduct());
        return bufferConfigDO;
    }
}
