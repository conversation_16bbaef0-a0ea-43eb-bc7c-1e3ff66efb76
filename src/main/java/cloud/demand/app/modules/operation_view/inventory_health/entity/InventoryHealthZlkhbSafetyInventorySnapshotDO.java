package cloud.demand.app.modules.operation_view.inventory_health.entity;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
@Table("inventory_health_zlkhb_safety_inventory_snapshot")
public class InventoryHealthZlkhbSafetyInventorySnapshotDO extends BaseDO {

    /** 统计时间<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 安全库存<br/>Column: [safety_inventory] */
    @Column(value = "safety_inventory")
    private BigDecimal safetyInventory;

    public static InventoryHealthZlkhbSafetyInventorySnapshotDO snapshot(InventoryHealthZlkhbSafetyInventoryDO source){
        InventoryHealthZlkhbSafetyInventorySnapshotDO copy = new InventoryHealthZlkhbSafetyInventorySnapshotDO();
        copy.setCustomhouseTitle(source.getCustomhouseTitle());
        copy.setAreaName(source.getAreaName());
        copy.setRegionName(source.getRegionName());
        copy.setZoneName(source.getZoneName());
        copy.setInstanceType(source.getInstanceType());
        copy.setSafetyInventory(source.getSafetyInventory());
        return copy;
    }

    public String toKey(InventoryHealthZlkhbSafetyInventorySnapshotDO d) {
        return StringTools.join("@", d.getCustomhouseTitle(),
                d.getAreaName(), d.getRegionName(), d.getZoneName(), d.getInstanceType());
    }

}