package cloud.demand.app.modules.operation_view.operation_view_phase1.enums;

import lombok.Getter;

@Getter
public enum CpuCategoryEnum {

    UNKNOWN("CPU核为0"),
    DOWN_16C("16c以下"),
    UP_32C("32c以上"),
    BETWEEN_16C_AND_32C("16c-32c(包含16c和32c)");

    private String type;

    CpuCategoryEnum(String type) {
        this.type = type;
    }

    public static String getTypeByLogicCoreNum(Integer cpuLogicCore){
        if (cpuLogicCore == null || cpuLogicCore == 0){
            return UNKNOWN.type;
        }
        if (cpuLogicCore < 16){
            return DOWN_16C.type;
        }else if (cpuLogicCore > 32){
            return UP_32C.type;
        }else {
            return BETWEEN_16C_AND_32C.type;
        }
    }
}
