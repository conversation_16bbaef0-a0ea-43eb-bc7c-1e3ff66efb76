package cloud.demand.app.modules.operation_view.inventory_health.dto.actual;

import cloud.demand.app.modules.operation_view.inventory_health.entity.DwsCloudServerLevelDO;
import cloud.demand.app.modules.operation_view.operation_view2.model.BigDecimal2ScaleSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class InventoryHealthActualResp {

    private List<Item> data;

    /**
     * 返回所有机型组合
     */
    private List<String> combineList;

    //  计算过程中出现的报错信息
    private String errorMsg;

    @Data
    public static class ReasonItem {
        /**
         * 原因 ID
         */
        private long reasonId;

        /**
         * 原因日期
         */
        private String reasonDate;

        /**
         * 原因分类
         */
        private String reasonType;

        /**
         * 原因详情
         */
        private String reasonDetail;
    }

    @Data
    public static class Item {

        /**
         * 实例类型
         */
        private String instanceType;

        /**
         * 境内外
         */
        private String customhouseTitle;

        /**
         * 区域
         */
        private String areaName;

        /**
         * 地域
         */
        private String regionName;

        /**
         * 可用区
         */
        private String zoneName;

        /**
         * 实际库存核心数
         */
        private Integer actualInventoryCore;

        /**
         * 安全库存核心数 = 包年包月 + 弹性用量
         */
        private Integer safetyInventoryCore;

        /**
         * 包年包月 = z(90%) * D * MAPE * sqrt(LT/t)
         */
        private Integer prePaidSafetyInventoryCore;

        /**
         * 弹性用量 = X%(服务水平）*L(弹性量/天)
         */
        private BigDecimal bufferSafetyInventoryCore;

        /**
         * 实际库存/安全库存
         */
        private BigDecimal healthRatio;

        // 以下是参与计算的值

        /**
         * 交付SLA
         */
        private Integer sla;

        /**
         * 服务水平
         */
        private BigDecimal serviceLevel;

        /**
         * 服务水平系数
         */
        private BigDecimal serviceLevelFactor;

        /**
         * 需求预测量(核心数)
         */
        private Integer forecastDemandCore;

        /**
         * 需求预测量(核心数)-外部行业
         */
        private Integer forecastDemandCoreOuter;

        /**
         * 需求预测量(核心数)-内部领用
         */
        private Integer forecastDemandCoreInner;

        /**
         * 需求标准差
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal standardDiff;

        /**
         * 需求平均值
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal demandAvg;

        /**for未来预测 中长尾客户包月安全库存 */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal mediumLongTailSafetyInv;

        /**for未来预测 头部-战略包月安全库存 */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal headZlkhbSafetyInv;

        /**for未来预测 头部-非战略包月安全库存 */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal headNotZlkhbSafetyInv;

        /**
         * 需求预测准确率
         */
        private BigDecimal forecastDemandAccuracyRate;

        /**
         * 需求预测开始时间
         */
        private String forecastDemandStartDate;

        /**
         * 需求预测结束时间
         */
        private String forecastDemandEndDate;

        /**
         * 弹性服务水平
         */
        private BigDecimal bufferServiceLevel;

        /**
         * 弹性服务水平系数
         */
        private BigDecimal bufferServiveLevelFactor;

        /**
         * 弹性ROI
         */
        private BigDecimal bufferRoi;

        /**
         * 弹性利用率
         */
        private BigDecimal bufferRate;

        /**
         * 弹性用量平均核心数
         */
        private Integer bufferAverageCore;

        /**
         * 弹性用量开始时间
         */
        private String bufferAverageStartDate;

        /**
         * 弹性用量结束时间
         */
        private String bufferAverageEndDate;

        /**
         * 计算出安全库存核心数的表达式
         */
        private String safetyInventoryCoreExpression;

        /**
         * 云上真实服务水平
         */
        private BigDecimal actualServiceLevel;

        /**
         * 云上真实服务水平
         */
        private BigDecimal actualServiceLevelWeight;

        private List<DwsCloudServerLevelDO> actualSla;

        /**
         * 针对组合机型，陈列每个机型详情
         */
        private List<Item> details;

        /**
         * 从参数日期开始，近一个月的原因分析
         */
        private List<ReasonItem> reasons;

        /**
         * 预扣核心数
         */
        private Integer reservedCores;

        /**
         * 13周平均交付时长
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal deliveryAvg;

        /**
         * 13周交付标准差
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal deliveryStandardDiff;

        /**
         * 安全库存人工调整
         */
        private BigDecimal safeInvManualConfig;

        /**
         * 周转库存
         */
        private BigDecimal turnoverInv;

        /**
         * 周转库存 - 当周执行量周峰
         */
        private BigDecimal turnoverWeekPeakCore;
        /**
         * 周转库存 - 当周预扣均值
         */
        private BigDecimal turnoverWeekReservedAvgCore;

        /**
         * 预测周转库存
         */
        private BigDecimal forecastTurnoverInv;

        /**
         * 预测周转库存 - 过去12周周峰均值
         */
        private BigDecimal forecastTurnoverWeekPeakAvg12Core;
        /**
         * 预测周转库存 - 最新版本对当周的预测
         */
        private BigDecimal forecastTurnoverWeekDemandCore;

        public String toKey() {
            return StringTools.join("@", customhouseTitle, areaName, regionName, zoneName, instanceType);
        }
    }

}
