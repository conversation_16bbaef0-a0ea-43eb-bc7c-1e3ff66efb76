package cloud.demand.app.modules.operation_view.inventory_health.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("dws_cloud_server_level")
public class DwsCloudServerLevelDO {

    /**
     * 分区键，每天一版本<br/>Column: [version]
     */
    @Column(value = "version")
    private String version;

    /**
     * 客户分类<br/>Column: [customer_type]
     */
    @Column(value = "customer_type")
    private String customerType;

    /**
     * 需求类型,[弹/包]<br/>Column: [demand_type]
     */
    @Column(value = "demand_type")
    private String demandType;

    /**
     * zoneid 2000001<br/>Column: [zone_id]
     */
    @Column(value = "zone_id")
    private String zoneId;

    /**
     * 如,上海三区<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 实例分类,如S5，IT3<br/>Column: [instance_family]
     */
    @Column(value = "instance_family")
    private String instanceFamily;

    /**
     * 整体服务水平<br/>Column: [sla]
     */
    @Column(value = "sla")
    private BigDecimal sla;

    /**
     * 权重<br/>Column: [weight]
     */
    @Column(value = "weight")
    private BigDecimal weight;


    /**
     * api总数<br/>Column: [weight]
     */
    @Column(value = "api_total")
    private BigDecimal apiTotal;
    /**
     * api成功总数<br/>Column: [weight]
     */
    @Column(value = "api_succ_total")
    private BigDecimal apiSucTotal;


    /**
     * 售卖总数<br/>Column: [weight]
     */
    @Column(value = "sold_total")
    private BigDecimal soldTotal;


    /**
     * 售罄总数<br/>Column: [weight]
     */
    @Column(value = "sold_out_total")
    private BigDecimal soldOutTotal;
}
