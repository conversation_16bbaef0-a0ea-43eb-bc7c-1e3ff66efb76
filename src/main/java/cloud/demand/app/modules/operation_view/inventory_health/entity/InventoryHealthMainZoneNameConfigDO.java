package cloud.demand.app.modules.operation_view.inventory_health.entity;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.common.excel.core.annotation.DotExcelEntity;
import cloud.demand.app.common.excel.core.annotation.DotExcelField;
import cloud.demand.app.modules.operation_view.inventory_health.constants.InventoryHealthExcelGroup;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("inventory_health_main_zone_name_config")
@DotExcelEntity
public class InventoryHealthMainZoneNameConfigDO extends BaseDO {
    @Column(value = "region_type")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_ZONE, excelColumnName = "境内外")
    private String regionType;

    @Column(value = "area_name")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_ZONE, excelColumnName = "区域")
    private String areaName;

    @Column(value = "region_name")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_ZONE, excelColumnName = "地域")
    private String regionName;

    @Column(value = "zone_name")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_ZONE, excelColumnName = "可用区")
    private String zoneName;

    @Column(value = "zone")
    private String zone;

    @Column(value = "type")
    private String type;

    @Column(value = "type_name")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_ZONE, excelColumnName = "可用区类型")
    private String typeName;

    @Column(value = "date")
    private Date date;

    public static InventoryHealthMainZoneNameConfigDO copy(InventoryHealthMainZoneNameConfigDO other) {
        InventoryHealthMainZoneNameConfigDO zoneNameConfigDO = new InventoryHealthMainZoneNameConfigDO();
        zoneNameConfigDO.setRegionType(other.getRegionType());
        zoneNameConfigDO.setAreaName(other.getAreaName());
        zoneNameConfigDO.setRegionName(other.getRegionName());
        zoneNameConfigDO.setZoneName(other.getZoneName());
        zoneNameConfigDO.setZone(other.getZone());
        zoneNameConfigDO.setType(other.getType());
        zoneNameConfigDO.setTypeName(other.getTypeName());
        zoneNameConfigDO.setDate(other.getDate());

        return zoneNameConfigDO;
    }
}
