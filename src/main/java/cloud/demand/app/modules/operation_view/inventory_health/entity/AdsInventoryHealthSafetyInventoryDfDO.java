package cloud.demand.app.modules.operation_view.inventory_health.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 安全库存结果表
 */
@Data
@ToString
@Table("ads_inventory_health_safety_inventory_df")
public class AdsInventoryHealthSafetyInventoryDfDO {

    /** 统计日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 地域名称 华南地区<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域中文名称 广州<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区名 广州四区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 产品类型<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** 客户类型: 名单客户/报备客户/中长尾客户/名单客户&报备客户/名单客户&中长尾客户/报备&中长尾/ALL<br/>Column: [customer_custom_group] */
    @Column(value = "customer_custom_group")
    private String customerCustomGroup;

    /** 安全库存算法:HISTORY_WEEK_DIFF|HISTORY_WEEK_PEAK|FUTURE_WEEK_PEAK<br/>Column: [algorithm] */
    @Column(value = "algorithm")
    private String algorithm;

    /** 交付sla<br/>Column: [sla] */
    @Column(value = "sla")
    private BigDecimal sla;

    /** 自动化预测准确率-for未来算法<br/>Column: [auto_forecast_accuracy] */
    @Column(value = "auto_forecast_accuracy")
    private BigDecimal autoForecastAccuracy;

    /** 需求标准差-for历史算法<br/>Column: [demand_standard_deviation] */
    @Column(value = "demand_standard_deviation")
    private BigDecimal demandStandardDeviation;

    /** 包月需求-服务水平<br/>Column: [service_level] */
    @Column(value = "service_level")
    private BigDecimal serviceLevel;

    /** 包月需求-服务系数<br/>Column: [service_factor] */
    @Column(value = "service_factor")
    private BigDecimal serviceFactor;

    /** 包月安全库存<br/>Column: [safety_inventory] */
    @Column(value = "safety_inventory")
    private BigDecimal safetyInventory;

    /** 包月安全库存计算公式<br/>Column: [safety_inventory_formula] */
    @Column(value = "safety_inventory_formula")
    private String safetyInventoryFormula;

    /** 弹性开始时间<br/>Column: [buffer_start_date] */
    @Column(value = "buffer_start_date")
    private String bufferStartDate;

    /** 弹性结束时间<br/>Column: [buffer_end_date] */
    @Column(value = "buffer_end_date")
    private String bufferEndDate;

    /** 弹性日均需求<br/>Column: [buffer_avg_cores] */
    @Column(value = "buffer_avg_cores")
    private BigDecimal bufferAvgCores;

    /** 弹性需求-服务水平<br/>Column: [buffer_service_level] */
    @Column(value = "buffer_service_level")
    private BigDecimal bufferServiceLevel;

    /** 弹性需求-服务系数<br/>Column: [buffer_service_factor] */
    @Column(value = "buffer_service_factor")
    private BigDecimal bufferServiceFactor;

    /** 弹性备货配额<br/>Column: [buffer_quota] */
    @Column(value = "buffer_quota")
    private BigDecimal bufferQuota;

    /** 安全库存 = 包月安全库存 + 弹性备货配额<br/>Column: [safety_inventory_total] */
    @Column(value = "safety_inventory_total")
    private BigDecimal safetyInventoryTotal;

    /** 周度需求计算值，用来计算周转库存<br/>Column: [weekly_demand] */
    @Column(value = "weekly_demand")
    private BigDecimal weeklyDemand;

    /** 周转库存<br/>Column: [turnover_inventory] */
    @Column(value = "turnover_inventory")
    private BigDecimal turnoverInventory;

}
