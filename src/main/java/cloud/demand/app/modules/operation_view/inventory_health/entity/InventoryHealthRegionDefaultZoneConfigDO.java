package cloud.demand.app.modules.operation_view.inventory_health.entity;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 库存健康-地域默认的可用区配置表
 */
@Data
@ToString
@Table("inventory_health_region_default_zone_config")
public class InventoryHealthRegionDefaultZoneConfigDO extends BaseDO {

    /** 区域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

}