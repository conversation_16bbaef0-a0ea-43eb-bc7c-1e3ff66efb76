//package cloud.demand.app.modules.operation_view.inventory_health.dto;
//
//import com.pugwoo.dbhelper.annotation.Column;
//import com.pugwoo.dbhelper.annotation.Table;
//import lombok.Data;
//
//import java.math.BigDecimal;
//
///**
// * 实际库存，跟 ActualInventoryDTO 一样，不过没有 date 属性，因为这里要求均值
// */
//@Data
//@Table("report_plan_detail")
//public class ActualInventoryAvgDTO extends CommonConditionDTO {
//
//    /**
//     * 境内外
//     */
//    @Column(value = "customhouse_title",
//            computed = "(case when customhouse_title = '' then '未分类' else customhouse_title end)")
//    private String customhouseTitle;
//
//
//    /**
//     * 区域
//     */
//    @Column(value = "area_name",
//            computed = "(case when area_name = '' then '未分类' else area_name end)")
//    private String areaName;
//
//
//    /**
//     * 地域
//     */
//    @Column(value = "region_name",
//            computed = "(case when region_name = '' then '未分类' else region_name end)")
//    private String regionName;
//
//    /**
//     * 可用区
//     */
//    @Column(value = "zone_name",
//            computed = "(case when zone_name = '' then '未分类' else zone_name end)")
//    private String zoneName;
//
//    /**
//     * 实例类型
//     */
//    @Column(value = "instance_type",
//            computed = "(case when instance_type = '' then '未分类' else instance_type end)")
//    private String instanceType;
//
//    /**
//     * 机型类型
//     * 这里只填充了线上部分，线下部分根据好料机型配置表在内存中填充
//     */
//    @Column(value = "material_type", computed = "(CASE WHEN indicator_code='d1' THEN '好料' "
//            + " when indicator_code='d2' then '差料' "
//            + " when indicator_code='d3' then '呆料' "
//            + "ELSE '' END)")
//    private String materialType;
//
//    /**
//     * 设备类型
//     */
//    @Column(value = "device_type",
//            computed = "(case when device_type = '' then '未分类' else device_type end)")
//    private String deviceType;
//
//    /**
//     * 总核心数
//     */
//    @Column(value = "total_core", computed = "sum(cores)")
//    private BigDecimal totalCore;
//
//    /**
//     * 总弹性规模
//     */
//    @Column(value = "total_buffer_core", computed = "sum(buffer_cores)")
//    private BigDecimal totalBufferCore;
//
//
//    public String getGroupK() {
//        return String.join("@", zoneName, instanceType, customhouseTitle, areaName, regionName);
//    }
//}
