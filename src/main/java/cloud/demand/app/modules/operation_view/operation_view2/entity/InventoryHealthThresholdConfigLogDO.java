package cloud.demand.app.modules.operation_view.operation_view2.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("inventory_health_threshold_config_log")
public class InventoryHealthThresholdConfigLogDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 提交人<br/>Column: [submitor] */
    @Column(value = "submitor")
    private String submitor;

    /** 操作类型:NEW/UPDATE/DELETE/UNCHANGED<br/>Column: [operation_type] */
    @Column(value = "operation_type")
    private String operationType;

    /** 操作安全库存阈值表的原始行主键<br/>Column: [origin_row_id] */
    @Column(value = "origin_row_id")
    private Long originRowId;

    /** 操作安全库存阈值表的结果行主键<br/>Column: [target_row_id] */
    @Column(value = "target_row_id")
    private Long targetRowId;

    /** 操作前状态<br/>Column: [origin_status_json] */
    @Column(value = "origin_status_json")
    private String originStatusJson;

    /** 操作后状态<br/>Column: [target_status_json] */
    @Column(value = "target_status_json")
    private String targetStatusJson;

}