package cloud.demand.app.modules.operation_view.inventory_health.dto.safety_inventory;

import cloud.demand.app.modules.operation_view.inventory_health.dto.HolidayWeekInfoDTO;
import cloud.demand.app.modules.operation_view.inventory_health.entity.DwsInventoryHealthWeeklyScaleDfDO;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 安全库存历史周净增、历史周峰计算DTO
 */
@Data
public class SafetyInventoryHistoryDTO {

    @Column("year")
    private Integer year;

    @Column("month")
    private Integer month;

    @Column("week")
    private Integer week;

    @Column("product")
    private String product;

    @Column("customhouse_title")
    private String customhouseTitle;

    @Column("area_name")
    private String areaName;

    @Column("region_name")
    private String regionName;

    @Column("zone_name")
    private String zoneName;

    @Column("instance_type")
    private String instanceType;

    @Column("logic_num")
    private BigDecimal logicNum;

    public String getYearWeek(){
        return String.join("@", this.getYear().toString(), this.getWeek().toString());
    }

    public static DwsInventoryHealthWeeklyScaleDfDO transform(SafetyInventoryHistoryDTO source, HolidayWeekInfoDTO dto){
        DwsInventoryHealthWeeklyScaleDfDO dwsInventoryHealthWeeklyScaleDfDO = new DwsInventoryHealthWeeklyScaleDfDO();
        dwsInventoryHealthWeeklyScaleDfDO.setHolidayYear(dto.getYear());
        dwsInventoryHealthWeeklyScaleDfDO.setHolidayMonth(dto.getMonth());
        dwsInventoryHealthWeeklyScaleDfDO.setHolidayWeek(dto.getWeek());
        dwsInventoryHealthWeeklyScaleDfDO.setHolidayWeekStartDate(LocalDate.parse(dto.getStartDate()));
        dwsInventoryHealthWeeklyScaleDfDO.setHolidayWeekEndDate(LocalDate.parse(dto.getEndDate()));
        dwsInventoryHealthWeeklyScaleDfDO.setWeekIndex(dto.getWeekNFromNow());
        dwsInventoryHealthWeeklyScaleDfDO.setProductType(source.getProduct());
        dwsInventoryHealthWeeklyScaleDfDO.setInstanceType(source.getInstanceType());
        dwsInventoryHealthWeeklyScaleDfDO.setCustomhouseTitle(source.getCustomhouseTitle());
        dwsInventoryHealthWeeklyScaleDfDO.setAreaName(source.getAreaName());
        dwsInventoryHealthWeeklyScaleDfDO.setRegionName(source.getRegionName());
        dwsInventoryHealthWeeklyScaleDfDO.setZoneName(source.getZoneName());
        return dwsInventoryHealthWeeklyScaleDfDO;
    }



}
