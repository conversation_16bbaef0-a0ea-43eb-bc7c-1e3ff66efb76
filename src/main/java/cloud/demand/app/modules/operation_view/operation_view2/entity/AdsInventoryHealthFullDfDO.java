package cloud.demand.app.modules.operation_view.operation_view2.entity;

import cloud.demand.app.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualReq;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import yunti.boot.exception.BizException;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 这个类已经废弃，请勿使用
 */
@Data
@Table("ads_inventory_health_full_df")
@Deprecated
public class AdsInventoryHealthFullDfDO implements Cloneable {

    @Column("stat_time")
    private String statTime;

    @Column("date")
    private String date;

    @Column("product_type")
    private String productType;

    @Column("customhouse_title")
    private String customhouseTitle;

    @Column("area_name")
    private String areaName;

    @Column("region_name")
    private String regionName;

    @Column("zone_name")
    private String zoneName;

    @Column("instance_type")
    private String instanceType;

    @Column("inv_total_num")
    private BigDecimal invTotalNum;

    @Column("inv_price")
    private BigDecimal invPrice;

    @Column("inv_ratio")
    private BigDecimal invRatio;

    @Column("algorithm")
    private String algorithm;

    @Column("safety_inv")
    private BigDecimal safetyInv;

    @Column("monthly_safety_inv")
    private BigDecimal monthlySafetyInv;

    @Column("turnover_inv")
    private BigDecimal turnoverInv;

    @Column("redundancy_inv")
    private BigDecimal redundancyInv;

    @Column("week_demand_map")
    private String weekDemandMap;

    @Column("standard_diff")
    private BigDecimal standardDiff;

    @Column("demand_avg")
    private BigDecimal demandAvg;

    @Column("delivery_standard_diff")
    private BigDecimal deliveryStandardDiff;

    @Column("delivery_avg")
    private BigDecimal deliveryAvg;

    @Column("sla")
    private BigDecimal sla;

    @Column("service_level")
    private BigDecimal serviceLevel;

    @Column("service_level_factor")
    private BigDecimal serviceLevelFactor;

    @Column("safe_inv_manual_config")
    private BigDecimal safeInvManualConfig;

    @Column("buffer_safety_inv")
    private BigDecimal bufferSafetyInv;

    @Column("buffer_service_level")
    private BigDecimal bufferServiceLevel;

    @Column("buffer_service_level_factor")
    private BigDecimal bufferServiceLevelFactor;

    @Column("buffer_average_core")
    private BigDecimal bufferAverageCore;

    @Column("health_ratio")
    private BigDecimal healthRatio;

    @Column("actual_service_level")
    private BigDecimal actualServiceLevel;

    public AdsInventoryHealthFullDfDO clone() {
        try {
            AdsInventoryHealthFullDfDO clone = (AdsInventoryHealthFullDfDO) super.clone();
            return clone;
        } catch (Exception e) {
            throw BizException.makeThrow("clone fail", e);
        }
    }
}
