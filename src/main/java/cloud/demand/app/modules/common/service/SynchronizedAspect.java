package cloud.demand.app.modules.common.service;


import cloud.demand.app.modules.common.entity.CdSyncLockLogDO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.NetUtils;
import com.pugwoo.wooutils.redis.RedisSyncContext;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.utils.ClassUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.mvel2.MVEL;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Aspect
@Component
@Slf4j
@Order //请勿小于 0
public class SynchronizedAspect {

    /**
     * 采用线程池，主线程执行业务代码，遇到监控的逻辑新开线程去执行，保证事务不共用
     */
    private final ExecutorService pool = Executors.newFixedThreadPool(2);

    @Resource
    private DBHelper demandDBHelper;

    @Around("@annotation(com.pugwoo.wooutils.redis.Synchronized)")
    @SneakyThrows
    public Object aroundMethod(ProceedingJoinPoint pjp) {

        Date reqTime = new Date();
        String traceId = MDC.get("requestId");

        Object o = pjp.proceed();

        boolean haveRun = RedisSyncContext.getHaveRun();
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        Method method = signature.getMethod();
        Object target = pjp.getTarget();
        Object[] args = pjp.getArgs();
        Method origin = target.getClass().getDeclaredMethod(signature.getName(), method.getParameterTypes());
        String namespace = origin.getAnnotation(Synchronized.class).namespace();
        String keyScript = origin.getAnnotation(Synchronized.class).keyScript();

        pool.submit(() -> generateNewOne(method, namespace, keyScript, args, haveRun, reqTime, traceId));

        return o;

    }

    public void generateNewOne(Method method, String namespace, String keyScript
            , Object[] args, boolean haveRun, Date reqTime, String traceId) {
        CdSyncLockLogDO res = new CdSyncLockLogDO();
        res.setReqTime(reqTime);
        res.setHasRun(haveRun ? 1 : 0);
        res.setNamespace(namespace);
        res.setMethodName(ClassUtils.getMethodSignatureWithClassName(method));
        res.setMethodShortName(method.getName());
        res.setKeyScript(keyScript);
        try {
            res.setRunIp(String.join(";", NetUtils.getIpv4IPs()));
        } catch (Exception e) {
            res.setRunIp("EXCEPTION");
        }
        try {
            res.setArgs(JSON.toJson(args));
        } catch (Exception e) {
            res.setArgs("EXCEPTION");
        }
        if (!keyScript.trim().isEmpty()) {
            Map<String, Object> context = new HashMap<>();
            context.put("args", args);
            try {
                Object result = MVEL.eval(keyScript.trim(), context);
                if (result != null) {
                    res.setKey(result.toString());
                }
            } catch (Throwable e) {
                res.setKey("EXCEPTION");
            }
        }
        res.setTraceId(traceId == null ? "" : traceId);
        demandDBHelper.insert(res);
    }


}
