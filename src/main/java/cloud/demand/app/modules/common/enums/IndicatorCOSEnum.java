package cloud.demand.app.modules.common.enums;

import cloud.demand.app.entity.rrp.ReportCvmJxcDO;
import lombok.Getter;

/**
 * COS指标的枚举对象
 */
@Getter
public enum IndicatorCOSEnum {

    INVENTORY_ALL("Q", "库存汇总", "存", "库存汇总", "Q=O+P", 310),
    INVENTORY_TOTAL("O", "线上库存汇总", "存", "线上库存汇总", "O=e+f", 311),
    //  线上库存
    INVENTORY_ONLINE_GOOD("e", "线上好料", "存", "线上库存", "", 312),
    INVENTORY_ONLINE_BAD("f", "线上差料", "存", "线上库存", "", 313),
    //  线下库存
    INVENTORY_OFFLINE("P", "线下好料", "存", "线下好料", "", 314),

    //  总售卖
    SALE_ALL("K", "销汇总", "销", "销汇总", "K=a+c", 210),
    // 销售
    SALE_EXTERNAL("a", "外部计费规模", "销", "外部计费规模", "", 211),
    SALE_INTERNAL("c", "内部售卖规模", "销", "内部售卖规模", "", 212),

    OTHER_TOTAL_CAN_USE("yh", "Yotta集群总可用量", "其他", "Yotta集群总可用量", "", 410),
    OTHER_SLOW_DELETE("fc", "缓删容量", "其他", "缓删容量", "", 411),
    OTHER_YOTTA_COST("yg", "Yotta系统消耗", "其他", "Yotta系统消耗", "", 412),
    OTHER_NOT_FOR_SALE("NS", "非可售", "其他", "非可售", "NS=yg+fc+OR+opd", 413),
    OTHER_P2PRATE("P2PRATE", "端到端利用率", "其他", "端到端利用率", "", 414),
    OTHER_SRATE("SRATE", "售卖利用率", "其他", "售卖利用率", "SRATE=K/yh", 415),
    OTHER_STORAGE_COPY_REDUNDANCY("OR", "存储副本冗余", "其他", "存储副本冗余", "", 416),
    OTHER_STORAGE_CAPACITY("COS-SC", "存储总物理容量", "其他", "存储总物理容量", "", 417),

    //  2023-01-09 新增
    OTHER_PRE_DELETE("opd", "提前删除量","其他", "提前删除量", "", 418),
    OTHER_YOTTA_USED("yu", "Yotta集群已使用规模", "其他", "Yotta集群已使用规模", "", 419)
    ;

    /** 指标代号 */
    private final String code;
    /** 指标名称 */
    private final String name;
    /** 指标大类 */
    private final String category;
    /** 指标子类 */
    private final String subCategory;
    /** 指标计算公式 */
    private final String formula;

    /** 指标序号
     * 这里认为每个产品均可分为4大类，进、销、存、其他
     * 编号进：1开头、销 2开头、存 3开头、其他：4开头
     * 每个大类都留一定的冗余空间（即从x10）开始给公共特殊指标，如：物理资源规模
     */
    private final Integer seq;

    IndicatorCOSEnum(String code, String name, String category, String subCategory, String formula, Integer seq){
        this.code = code;
        this.name = name;
        this.category = category;
        this.subCategory = subCategory;
        this.formula = formula;
        this.seq = seq;
    }

    /**
     * 将IndicatorEnum转成ReportCvmJxcDO指标对象,并填充可以直接获取的字段
     */
    public ReportCvmJxcDO toReportCvmJxcDO(){
        ReportCvmJxcDO jxcDO = new ReportCvmJxcDO();
        jxcDO.setIndicatorCode(this.getCode());
        jxcDO.setIndicatorName(this.getName());
        jxcDO.setCategory(this.getCategory());
        jxcDO.setSubCategory(this.getSubCategory());
        jxcDO.setIndicatorFormula(this.getFormula());
        jxcDO.setProductType(ProductTypeEnum.COS.getCode());
        jxcDO.setSeq(this.getSeq());
        return jxcDO;
    }

}

