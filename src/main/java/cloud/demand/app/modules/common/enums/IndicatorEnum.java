package cloud.demand.app.modules.common.enums;

import cloud.demand.app.entity.rrp.ReportCvmJxcDO;
import lombok.Getter;

/**
 * 指标的枚举对象
 */
@Getter
public enum IndicatorEnum {

    //  总库存
    INVENTORY_ALL("Q", "总库存", "存", "总库存", "Q=O+P", 310),
    //  线上库存
    INVENTORY_TOTAL("O",  "总计", "存", "线上库存", "O=RSN+CSP+TRN", 311),
    INVENTORY_GOOD("e", "好料", "存", "线上库存", "", 312),
    INVENTORY_BAD("f", "差料", "存", "线上库存", "", 313),
    INVENTORY_UGLY("g", "呆料", "存", "线上库存", "", 314),
    INVENTORY_REASONING("RSN", "推理", "存", "线上库存", "", 315),
    INVENTORY_TRAINING("TRN", "训练", "存", "线上库存", "", 316),
    INVENTORY_CONSUMPTION("CSP", "消费", "存", "线上库存", "", 317),
    INVENTORY_OTHER("OTHER", "其他卡类", "存", "线上库存", "", 318),
    //  线下库存
    INVENTORY_OFFLINE("P", "线下库存", "存", "线下库存", "", 319),

    //  总售卖
    SALE_ALL("K", "总售卖", "销", "总售卖", "K=a+b+c+d", 210),
    //  销
    SALE_EXTERNAL_REGULAR("a", "外部常规售卖", "销", "外部常规售卖", "", 211),
    SALE_EXTERNAL_LH("b", "外部LH售卖", "销", "外部LH售卖", "", 212),
    SALE_INTERNAL_APPLY("c", "内部申领", "销", "内部申领", "", 213),
    SALE_BUFF("buff", "弹性规模", "销", "弹性规模", "", 214),

    //  其他
    OTHER_NOT_FOR_SALE("NS", "非可售", "其他", "非可售", "", 410),
    OTHER_EXCEED_SALE("D", "超卖量", "其他", "超卖量", "", 411),
    OTHER_MACHINE_LAUNCH_CLOSE("T", "母机投放/下架量", "其他", "母机投放/下架量", "", 412),
    OTHER_VIRTUAL_COST("VC", "虚拟化开销", "其他", "虚拟化开销", "", 413),
    OTHER_P2P_RATE("P2PRATE", "端到端利用率", "其他", "端到端利用率", "P2PRATE=K/(K+D+Q+NS+VC)", 414),

    //  进
    //  总进货
    ENTER_ALL("M", "总进货", "进", "总进货", "M=C1+C2+E1+E2+RT+NO1+NO2", 110),
    ENTER_PURCHASE("C1", "采购提货-新增采购", "进", "采购提货-新增采购", "", 111),
    ENTER_INVENTORY_REUSE("C2", "采购提货-库存复用", "进", "采购提货-库存复用", "", 112),
    ENTER_TRANSFER("E1", "资源转入量", "进", "资源转入量", "", 113),
    ENTER_NO_ORDER_IN("NO1", "无单据转入量", "进", "资源转入量", "", 114),
    ENTER_TRANSFER_OUT("E2", "转出复用量", "进", "转出复用量", "", 115),
    ENTER_NO_ORDER_OUT("NO2", "无单据转出量", "进", "转出复用量", "", 116),
    ENTER_OFFSET("OFFSET", "偏移量", "进", "偏移量", "", 117),
    ENTER_RETURN("RT", "退回总量", "进", "退回公司量", "RT=RT_N+RT_C+RT_4", 118),
    ENTER_RETURN_NORMAL("RT_N", "常规退回", "进", "退回公司量", "", 119),
    ENTER_RETURN_CAICHE("RT_C", "裁撤退回", "进", "退回公司量", "", 120),
    ENTER_RETURN_TUIYI("RT_4", "退役退回", "进", "退回公司量", "", 121),
    OTHER_PHYSICAL_RESOURCE_SCALE("AC", "物理资源规模", "其他", "物理资源规模", "", 401);


    /** 指标代号 */
    private final String code;
    /** 指标名称 */
    private final String name;
    /** 指标大类 */
    private final String category;
    /** 指标子类 */
    private final String subCategory;
    /** 指标计算公式 */
    private final String formula;
    /** 指标序号
     * 这里认为每个产品均可分为4大类，进、销、存、其他
     * 编号进：1开头、销 2开头、存 3开头、其他：4开头
     * 每个大类都留一定的冗余空间（即从x10）开始给公共特殊指标，如：物理资源规模
     */
    private final Integer seq;

    IndicatorEnum(String code, String name, String category, String subCategory, String formula, Integer seq){
        this.code = code;
        this.name = name;
        this.category = category;
        this.subCategory = subCategory;
        this.formula = formula;
        this.seq = seq;
    }

    /**
     * 将IndicatorEnum转成ReportCvmJxcDO指标对象,并填充可以直接获取的字段
     */
    public ReportCvmJxcDO toReportCvmJxcDO(){
        ReportCvmJxcDO jxcDO = new ReportCvmJxcDO();
        jxcDO.setIndicatorCode(this.getCode());
        jxcDO.setIndicatorName(this.getName());
        jxcDO.setCategory(this.getCategory());
        jxcDO.setSubCategory(this.getSubCategory());
        jxcDO.setIndicatorFormula(this.getFormula());
        jxcDO.setSeq(this.getSeq());
        return jxcDO;
    }

}
