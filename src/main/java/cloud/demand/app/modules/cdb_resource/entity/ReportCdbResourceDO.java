package cloud.demand.app.modules.cdb_resource.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Data
@ToString
@Table("report_cdb_resource")
public class ReportCdbResourceDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    /** 数据生成日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    @Column(value = "resource_type")
    private String resourceType;

    @Column(value = "cname")
    private String cname;

    @Column(value = "dtype")
    private String dtype;

    @Column(value = "status")
    private String status;

    @Column(value = "campus")
    private String campus;

    @Column(value = "xtype")
    private String xtype;

    @Column(value = "rest_mem")
    private BigDecimal restMem;

    @Column(value = "can_sell_mem")
    private BigDecimal canSellMem;

    @Column(value = "total_mem")
    private BigDecimal totalMem;

    @Column(value = "devcnt")
    private BigDecimal devcnt;

    @Column(value = "region")
    private String region;

    @Column(value = "az")
    private String az;

    @Column(value = "country")
    private String country;

}