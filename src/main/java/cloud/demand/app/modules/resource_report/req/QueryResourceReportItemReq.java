package cloud.demand.app.modules.resource_report.req;


import java.util.List;
import lombok.Data;

@Data
public class QueryResourceReportItemReq {

    /**
     * 切片日期, yyyyMMdd
     */
    String startVersion;
    String endVersion;

//    /**
//     * 开始时间，结束时间 yyyyMM-dd, yyyyMMdd
//     */
//    String startDate;
//    String endDate;


    /**
     * 开始时间，结束时间 yyyyMM
     */
    String startMonth;
    String endMonth;

    /**
     * dimension 维度, 要展示的维度, 根据 rsp 返回的字段传递 dimData
     */
    List<String> dim;


    List<String> dimBigClass;
    List<String> dimProductClass;
    List<String> dimDeviceType;
    List<String> dimInstanceType;
    List<String> dimCpuType;
    List<String> dimNetType;
    List<String> dimRegionClass;
    List<String> dimRegion;
    List<String> dimIndustry;
    List<String> dimCustomer;

    List<String> dimPlanProduct;
    List<String> dimReason1;
    List<String> dimReason2;
    List<String> dimSupplyWay;

    String paramType;

    public String getStartMonth() {
        if (startMonth.contains("-")) {
            return startMonth;
        }

        return startMonth.substring(0, 4) + "-" + startMonth.substring(4, 6);
    }

    public String getEndMonth() {
        if (endMonth.contains("-")) {
            return endMonth;
        }

        return endMonth.substring(0, 4) + "-" + endMonth.substring(4, 6);
    }
}
