select version_start_audit_time,
       version_code,version_name,version_year,version_month,ppl_id,ppl_order,update_time,status,
       status_name,instance_num,total_core,`source`,source_name,`year`,`month`,customer_uin,industry,industry_dept,
       customer_type,customer_type_name,customer_name,customer_short_name,war_zone,customer_source,submit_user,
       product,demand_type,demand_type_name,demand_scene,project_name,bill_type,win_rate,begin_buy_date,end_buy_date,
       begin_elastic_time,end_elastic_time,note,customhouse_title,region_name,zone_name,instance_type,instance_model,
       total_disk,alternative_instance_type,affinity_type,affinity_value,system_disk_type,system_disk_storage,
       system_disk_num,data_disk_type,data_disk_storage,data_disk_num,gpu_type,gpu_num,total_gpu_num,is_accept_adjust_gpu,
       accept_gpu,biz_scene,biz_detail,service_time,gpu_product_type,cname,cid,`zone`,area_name,pan_shi_war_zone,
       customer_tab_type,strategy_group_name,is_comd,source_ppl_id,category,uin_type,customer_society_type,
       database_name, more_than_one_az, database_storage_type, deploy_type, framework_type, slice_num, replica_num,
       read_only_num, database_specs, database_storage, total_database_storage, cos_storage_type, cos_az, cos_storage,
       bandwidth, qps, total_cos_storage, instance_model_core_num, instance_model_ram_num, total_memory, cbs_is_spike
from dwd_crp_ppl_item_version_cf
where version_year * 12 + version_month + 3  = `year` * 12 + `month`
