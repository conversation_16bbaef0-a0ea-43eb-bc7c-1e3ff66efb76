select
    info.id as order_info_id,
    item.id as order_item_id,
    item.order_number as order_number,
    item.order_number_id as order_number_id,
    info.available_status as available_status,
    info.order_status as order_status,
    info.order_node_code as order_node_code,
    info.flow_no as flow_no,
    info.before_consensus_id as before_consensus_id,
    info.industry_dept as industry_dept,
    info.war_zone as war_zone,
    info.product as product,
    info.customer_short_name as customer_short_name,
    info.customer_name as customer_name,
    info.customer_uin as customer_uin,
    info.app_id as app_id,
    info.order_category as order_category,
    info.app_role as app_role,
    info.order_type as order_type,
    info.begin_buy_date as begin_buy_date,
    info.end_buy_date as end_buy_date,
    info.begin_elastic_date as begin_elastic_date,
    info.end_elastic_date as end_elastic_date,
    info.project_type as project_type,
    info.project_name as project_name,
    info.demand_scene as demand_scene,
    info.submit_user as submit_user,
    info.architect as architect,
    info.order_follower as order_follower,
    info.level_name as level_name,
    info.refuse_log_id as refuse_log_id,
    info.risk_level as risk_level,
    info.order_label as order_label,
    info.report_bit_num as report_bit_num,
    item.bill_type as bill_type,
    item.region as region,
    item.region_name as region_name,
    item.zone as zone,
    item.zone_name as zone_name,
    item.other_zone as other_zone,
    item.other_zone_name as other_zone_name,
    item.instance_type as instance_type,
    item.instance_model as instance_model,
    item.other_instance as other_instance,
    item.system_disk_type as system_disk_type,
    item.system_disk_storage as system_disk_storage,
    item.system_disk_num as system_disk_num,
    item.data_disk_type as data_disk_type,
    item.data_disk_storage as data_disk_storage,
    item.data_disk_num as data_disk_num,
    item.instance_num as instance_num,
    item.cpu_num as cpu_num,
    item.memory as memory,
    item.gpu_num as gpu_num,
    item.total_core as total_core,
    item.total_disk as total_disk,
    item.accept_gpu as accept_gpu,
    item.total_gpu_num as total_gpu_num,
    item.gpu_type as gpu_type,
    item.is_pre_deduct as is_pre_deduct,
    item.before_consensus_order_number_id as before_consensus_order_number_id,
    item.late_consensus_begin_buy_date as late_consensus_begin_buy_date,
    item.before_consensus_normal_order_number_id as before_consensus_normal_order_number_id,
    info.before_consensus_normal_id as before_consensus_normal_id,
    item.create_time as create_time,
    item.update_time as item_update_time,
    info.update_time as info_update_time,
    info.submit_time as submit_time
from order_info info left join order_item item on info.id = item.order_info_id
where  info.deleted = 0 and item.deleted = 0  and info.available_status = 'available'
 and info.order_category in ('CVM','GPU','BARE_METAL')