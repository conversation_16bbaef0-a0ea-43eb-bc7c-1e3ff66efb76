SELECT
    (CASE WHEN a.`version_group_record_id`=:v1GroupRecordId THEN a.`status` WHEN c.`status` IS NULL THEN a.`status` ELSE c.`status` END) AS demand_type,
    YEAR(a.begin_buy_date) as year,MONTH(a.begin_buy_date) as month,
    COALESCE(SUM(CASE WHEN a.version_group_record_id=:v2GroupRecordId THEN a.instance_num ELSE 0 END),0) AS v2Num,
    COALESCE(SUM(CASE WHEN a.version_group_record_id=:v1GroupRecordId THEN a.instance_num ELSE 0 END),0) AS v1Num,
    COALESCE(SUM(CASE WHEN a.version_group_record_id=:v2GroupRecordId THEN a.total_core ELSE 0 END),0) AS v2Core,
    COALESCE(SUM(CASE WHEN a.version_group_record_id=:v1GroupRecordId THEN a.total_core ELSE 0 END),0) AS v1Core,
    COALESCE(SUM(CASE WHEN a.version_group_record_id=:v2GroupRecordId THEN
                              (a.system_disk_storage+a.data_disk_storage*a.data_disk_num)*a.instance_num ELSE 0 END),0) AS v2Storage,
    COALESCE(SUM(CASE WHEN a.version_group_record_id=:v1GroupRecordId THEN
                              (a.system_disk_storage+a.data_disk_storage*a.data_disk_num)*a.instance_num ELSE 0 END),0) AS v1Storage
FROM `ppl_version_group_record_item` a LEFT JOIN `ppl_order` b ON a.ppl_order=b.`ppl_order`
                                       LEFT JOIN `ppl_item` c ON a.ppl_id=c.`ppl_id`
WHERE a.`deleted`=0 AND version_group_record_id IN (:v1GroupRecordId,:v2GroupRecordId)
  AND a.demand_type in ('NEW','ELASTIC') -- 只要新建和弹性
    ${FILTER} -- 额外的条件
GROUP BY YEAR(a.begin_buy_date), MONTH(a.begin_buy_date),
    (CASE WHEN a.`version_group_record_id`=:v1GroupRecordId THEN a.`status` WHEN c.`status` IS NULL THEN a.`status` ELSE c.`status` END)