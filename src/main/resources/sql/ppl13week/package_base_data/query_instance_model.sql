WITH ranked_instances AS (
    SELECT
        *,
        ROW_NUMBER() OVER (
            PARTITION BY instance_type
            ORDER BY parse_core ASC, parse_ram ASC
        ) AS rn
    FROM industry_demand_region_zone_instance_type_dict
    WHERE
        deleted = 0
        AND instance_type IN (?)
        AND parse_core >= ?
        AND parse_ram >= ?
)
SELECT
    *
FROM ranked_instances
WHERE rn = 1  -- 每个 instance_type 取第一条（最小 core 和 ram）
ORDER BY instance_type;