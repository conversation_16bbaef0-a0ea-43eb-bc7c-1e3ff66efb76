insert into ppl_industry_package_base_data
(demand_type, demand_type_name, region_name, region, customhouse_title, common_instance_type,
 demand_year_month, create_user, import_version_id, latest, industry_dept, product, inner_version_id,
 base_core, possible_instance_model)
select demand_type, demand_type_name, region_name, region, customhouse_title, common_instance_type,
       demand_year_month,
       'SYSTEM' as create_user, null as import_version_id, latest, industry_dept, product,
       ? as inner_version_id, base_core, possible_instance_model
from ppl_industry_package_base_data
where deleted = 0 and inner_version_id = ? and latest = 1
  and demand_year_month >= ? and demand_year_month <= ?