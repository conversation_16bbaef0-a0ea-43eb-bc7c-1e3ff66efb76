select
    ppl_applied_supply.apply_order_id ,
    ppl_applied_supply.apply_status ,
    ppl_order.source ,
    ppl_applied_supply.`year_month` ,
    ppl_applied_supply.war_zone ,
    ppl_applied_supply.customer_short_name ,
    ppl_applied_supply.region_name ,
    ppl_applied_supply.zone_name ,
    ppl_applied_supply.submit_user ,
    ppl_applied_supply.begin_buy_date ,
    ppl_applied_supply.end_buy_date ,
    ppl_applied_supply.instance_type ,
    ppl_applied_supply.instance_model ,
    ppl_applied_supply.apply_num ,
    ppl_applied_supply.apply_core ,
    ppl_applied_supply.deploy_num ,
    ppl_applied_supply.deploy_core ,
    ppl_item.bill_type ,
    ppl_item.project_name ,
    ppl_applied_supply.ppl_id ,
    ppl_applied_supply.note
from ppl_applied_supply
left join ppl_order on ppl_applied_supply.ppl_order = ppl_order.ppl_order
left join ppl_item on ppl_item.ppl_id = ppl_applied_supply.ppl_id
