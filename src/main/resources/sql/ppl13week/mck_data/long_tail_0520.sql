SELECT
    ppl_order,
    version_code,
    product,
	region_name,
	zone_name,
	industry_dept,
	DATE( version_start_audit_time ) as submit_date,
	YEAR ( version_start_audit_time ) as submit_year,
	customer_short_name,
	gpu_type,
	instance_type,
	instance_model,
	begin_buy_date,
	end_buy_date,
	YEAR ( begin_buy_date ) as begin_buy_year,
	instance_num,
	total_core,
	CAST(gpu_num AS Int64) AS gpu_num,
	CAST(total_gpu_num AS Int64) AS total_gpu_num,
	demand_type,
	status_name,
	win_rate,
	project_name
FROM
	std_crp.dws_crp_ppl_item_version_newest_cf_0520
WHERE
	(
		year > 2023
	OR ( year = 2023 AND month > 7 ))
	AND is_comd != 1
	AND source IN (
	'FORECAST')
	AND industry_dept = '中长尾'
	AND instance_num != 0