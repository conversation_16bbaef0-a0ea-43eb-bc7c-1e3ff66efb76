-- 趋势：规模表
with queryProduct as (select ? as value)
select year,month,
    sum(case when change_bill_num_from_last_month>0 then change_bill_num_from_last_month else 0 end) as total_new_bill_num,
    sum(case when change_bill_num_from_last_month<0 then change_bill_num_from_last_month else 0 end) as total_ret_bill_num

from (

select year,month,

    sum(case when (select value from queryProduct)='CVM' then change_bill_core_from_last_month
    when (select value from queryProduct)='GPU' then change_bill_gpu_from_last_month
    when (select value from queryProduct)='裸金属' then change_bill_core_from_last_month*100/instance_model_cpu else 0 end) as change_bill_num_from_last_month

from dwd_txy_scale_df
where stat_time=month_end_date and app_role in ('正常售卖', 'CDH', '预扣包', 'GOCP', 'CDZ')
    and instance_type not like 'RM%' and instance_type not like 'RS%'
    and biz_range_type='外部业务'
     -- 额外的条件
    ${FILTER}
group by year,month,instance_type,zone_name

)

group by year,month
order by year,month