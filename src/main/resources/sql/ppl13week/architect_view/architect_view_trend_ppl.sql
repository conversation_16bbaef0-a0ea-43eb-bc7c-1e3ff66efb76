with queryProduct as (select ? as value),
    a as (select year,
    month,source,
    (case when demand_type in ('NEW', 'ELASTIC') then 'NEW' else 'RETURN' end)    as tmp_demand_type,
    (case when demand_type = 'RETURN' then -total_core else total_core end)       as tmp_total_core,
    (case when demand_type = 'RETURN' then -total_gpu_num else total_gpu_num end) as tmp_total_gpu_num,
    (case when demand_type = 'RETURN' then -instance_num else instance_num end)   as tmp_instance_num,
    (case
    when (select value from queryProduct) in ('CVM','弹性MapReduce','EKS官网','云数据仓库','Elasticsearch Service','数据湖DLC','CSIG容器平台') then tmp_total_core
    when (select value from queryProduct) = 'GPU' then tmp_total_gpu_num
    when (select value from queryProduct) = '裸金属' then tmp_instance_num
    else 0 end)                                                              as num
from dwd_crp_ppl_item_cf
where (status!='APPLIED' or (status='APPLIED' and yunxiao_order_status not in ('CREATED', 'CANCELED', 'BAD_CANCELED', 'REJECTED' ))) -- 已预约的已取消的云霄单不要
    ${FILTER}
)

select year,month,tmp_demand_type,source,
    sum(num) as total_num
from a
group by tmp_demand_type,year,month,source
order by year,month,tmp_demand_type,source

