
select
    (CASE WHEN category IS NULL or category = '(空值)' THEN '未分类' ELSE category END) AS vdept,
    customer_uin as customerUin,
    customer_short_name as customerName,
    industry_dept as industryDept,
    SUM(case when `source` in ('IMPORT','COMD_INTERVENE','APPLY_AUTO_FILL','FORECAST') and is_comd != 1
        then total_core else 0 end) AS demandTotalCore,
    SUM(case when `source` in ('IMPORT','COMD_INTERVENE','APPLY_AUTO_FILL','FORECAST') and is_comd != 1
        then total_gpu_num else 0 end) AS demandTotalGpuNum,
    0 AS appliedTotalCore,
    0 AS appliedTotalGpuNum
from dws_crp_ppl_item_version_newest_cf
where 1=1
    ${FILTER}
GROUP BY vdept,customerUin,customerName,industryDept

union all

select
    (CASE WHEN category IS NULL or category = '(空值)' THEN '未分类' ELSE category END) AS vdept,
    customer_uin as customerUin,
    customer_short_name as customerName,
    industry_dept as industryDept,
    0 AS demandTotalCore,
    0 AS demandTotalGpuNum,
    SUM(CASE WHEN `status`='APPLIED' and `source` != 'COMD_INTERVENE' ${FILTER_APPLY} THEN `total_core_apply_after` ELSE 0 END)
                                                                                        AS appliedTotalCore,
    SUM(CASE WHEN `status`='APPLIED' and `source` != 'COMD_INTERVENE' ${FILTER_APPLY} THEN `total_gpu_num_apply_after` ELSE 0 END)
                                                                                        AS appliedTotalGpuNum
from dwd_crp_ppl_item_cf
where 1=1
    ${FILTER}
GROUP BY vdept,customerUin,customerName,industryDept