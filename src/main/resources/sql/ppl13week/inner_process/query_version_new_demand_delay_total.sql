select t3.customer_short_name,
       t1.product,
       t2.version_id,
       DATE_FORMAT(t1.expired_begin_buy_date, '%Y-%m') as `expired_year_month`,
       sum(
               case
                   when t2.input_status = 'DELAY'
                       then (
                       case
                           when t1.demand_type in ('NEW', 'ELASTIC')
                               then (
                               case
                                   when t1.product = 'GPU(裸金属&CVM)' then t1.total_gpu_num
                                   when t1.product = 'COS' then t1.total_cos_storage
                                   when t1.product = '数据库' then t1.total_database_storage
                                   else t1.total_core end
                               )
                           else 0 end
                       )
                   else 0 end
           )                                           as expired_new_demand_num,
       sum(
               case
                   when t2.input_status = 'DELAY'
                       then (
                       case
                           when t1.demand_type in ('RETURN')
                               then (
                               case
                                   when t1.product = 'GPU(裸金属&CVM)' then t1.total_gpu_num
                                   when t1.product = 'COS' then t1.total_cos_storage
                                   when t1.product = '数据库' then t1.total_database_storage
                                   else t1.total_core end
                               )
                           else 0 end
                       )
                   else 0 end
           )                                           as expired_return_demand_num,

       case when t1.product = 'GPU(裸金属&CVM)' then t1.gpu_type else t1.instance_type end
                                                       as instance_type_or_gpu_type
from ppl_order_audit_record_item t1
         inner join (select version_id, max(id) as id, ppl_order, input_status
                     from ppl_order_audit_record
                     where deleted = 0
                       and version_id in (?)
                     group by version_id, ppl_order) t2 on t1.audit_record_id = t2.id
         left join ppl_order t3 on t1.ppl_order = t3.ppl_order
-- where 条件， 数据权限占位符 中带 where关键字
    ${permission} and t1.deleted = 0 and t3.deleted = 0
    and t1.expired_begin_buy_date is not null
group by t3.customer_short_name, t1.product, t2.version_id, DATE_FORMAT(t1.expired_begin_buy_date, '%Y-%m')
        , t1.instance_type, t1.gpu_type
