-- 查询审批概览
SELECT  a.instance_type,
       (CASE
            WHEN a.`region_name` IN (:overSeaRegions) THEN '境外'
            ELSE '国内' END)                                                       AS customhouse_title,
       a.region_name,
       a.zone_name,
       a.instance_model,
       a.win_rate,
       YEAR(a.begin_buy_date) as year,
       MONTH(a.begin_buy_date) as month,
       b.war_zone,
       b.center,
       b.customer_uin,
       b.customer_short_name,
       b.submit_user,
       a.gpu_type,
       a.product,
       'AFTER'                                                                     AS record_type,
       -- 弹性需求和新增需求合并为新增需求
       (CASE WHEN a.`demand_type` = 'ELASTIC' THEN 'NEW' ELSE a.`demand_type` END) AS demand_type2,

       (CASE WHEN a.ppl_order LIKE 'PN%' THEN 'INPUT' ELSE 'SYSTEM_INPUT' END) AS ppl_source,
        COALESCE(SUM(a.total_core_apply_before), 0) AS total_core_apply_before,
        COALESCE(SUM(a.total_core_apply_after), 0) AS total_core_apply_after,

    COALESCE(SUM(a.total_gpu_num_apply_before), 0) AS total_gpu_num_apply_before,
    COALESCE(SUM(a.total_gpu_num_apply_after), 0) AS total_gpu_num_apply_after,

       SUM(a.total_core)                                                           AS total_core,
       SUM(a.total_gpu_num)                                                        AS total_gpu_num,
       SUM(a.instance_num)                                                         AS total_instance_num,

    COALESCE(sum(a.total_memory), 0)    as  total_memory,
    COALESCE(sum(a.total_cos_storage), 0)  as  total_cos_storage,
    COALESCE(sum(a.total_database_storage), 0)  as  total_database_storage
FROM `ppl_item_draft` a
         JOIN `ppl_order_draft` b ON a.ppl_order = b.`ppl_order`
WHERE a.deleted = 0
  AND b.`deleted` = 0
  AND a.draft_status = 'PRE_SUBMIT'
  AND b.draft_status = 'PRE_SUBMIT'
  AND a.ppl_order IN (:pplOrder) ${FILTER} -- 其它查询条件

GROUP BY a.instance_model, customhouse_title, a.region_name, a.zone_name,a.product,
    YEAR(a.begin_buy_date), MONTH(a.begin_buy_date), b.war_zone, b.customer_uin, b.customer_short_name,
    demand_type2, ppl_source
