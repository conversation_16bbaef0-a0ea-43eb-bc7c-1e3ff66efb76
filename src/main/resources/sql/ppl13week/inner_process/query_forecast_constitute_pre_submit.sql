
select
    case
        when ppl_item.product = 'GPU(裸金属&CVM)'
            then ppl_item.total_gpu_num
        else ppl_item.total_core
        end as total_core_or_gpu_num,
    ppl_item.${indexName} as total,
    ppl_item.gpu_type as gpu_type,
    ppl_item.instance_type as instance_type,
    ppl_item.region_name as region_name,
    ppl_order.war_zone as war_zone,
    ppl_order.customer_short_name as customer_short_name,
    DATE_FORMAT(ppl_item.begin_buy_date, '%Y-%m') as demand_year_month,
    ppl_item.zone_name as zone_name,
    ppl_item.database_name as database_name,
    ppl_item.cos_storage_type as cos_storage_type
from ppl_item_draft  ppl_item
left join ppl_order_draft ppl_order on ppl_item.ppl_order = ppl_order.ppl_order
