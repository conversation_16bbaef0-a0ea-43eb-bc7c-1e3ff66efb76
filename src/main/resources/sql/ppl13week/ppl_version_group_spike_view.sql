SELECT if(demand_type in ('NE<PERSON>', 'ELASTIC'), 'NEW_ELASTIC', 'RETURN')                                 as demand_type1,
       if(customer_short_name = '' or customer_short_name is null, customer_uin, customer_short_name) as customer,
       customer_short_name as customer_short_name1,
       customer_uin as customer_uin,
       CONCAT(YEAR(a.begin_buy_date), '-', LPAD(MONTH(a.begin_buy_date), 2, '0')) as ym,
       region_name,
       instance_type,
       is_spike,
       sum(total_core)                                                                                as forecast_num
FROM `ppl_version_group_record_item` a
         LEFT JOIN `ppl_order` b ON a.ppl_order = b.`ppl_order`
WHERE a.`deleted` = 0 AND b.`deleted` = 0
and a.version_group_record_id= :versionRecordId
${FILTER}
group by demand_type1, ym, region_name, instance_type, customer ${GROUP_IS_SPIKE}