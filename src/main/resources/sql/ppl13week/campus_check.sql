select a.region_name  from (
       SELECT
           DISTINCT region_name as region_name
       FROM
           ppl_version_group_record_item
       WHERE
               version_group_record_id IN (
               SELECT
                   max( id )
               FROM
                   ppl_version_group_record
               WHERE
                       deleted = 0
                 AND version_group_id IN ( SELECT id FROM ppl_version_group WHERE version_code in (
                   select version_code  from ppl_version pv where status = 'PROCESS') AND deleted = 0 )
               GROUP BY
                   version_group_id
           )
         AND deleted = 0
         AND product not in ('裸金属','GPU(裸金属&CVM)')
         AND zone_name = '随机可用区'
   ) as a
   left join ppl_config_stock_supply_default_city_zone b on  a.region_name = b.city_name and `type` = '默认可用区'
where b.id is null