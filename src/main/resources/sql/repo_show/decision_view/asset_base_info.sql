SELECT a.LogicPcCode      AS asset_id,
       e.inventory_class1 AS inventory_class1,
       e.inventory_class2 AS inventory_class2,
       e.inventory_class3 AS inventory_class3,
       e.inventory_class4 AS inventory_class4,
       a.GoUpDate         AS GoUpDate,
       a.DeviceType       AS DeviceType,
       a.SvrTypeVersion   AS SvrTypeVersion,
       a.RegionName       AS RegionName,
       a.ZoneName         AS ZoneName,
       a.SubZoneName      AS SubZoneName,
       a.ModuleName       AS ModuleName,
       a.BelongUnitName   AS BelongUnitName,
       a.PlanProductName      AS PlanProductName
FROM sto_rmdb_servers AS a
         LEFT JOIN sto_rmdb_servers_sub AS e ON a.ID = e.ID
where a.LogicPcCode in (?)