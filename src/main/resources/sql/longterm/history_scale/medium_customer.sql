select
stat_time,
toYear(stat_time) AS year,
toQuarter(stat_time) AS quarter,
toMonth(stat_time) AS month,
common_customer_short_name,
customhouse_title,
region_name,
zone_name,
product_group,
instance_type,
sum(
(case when (:longTermProduct in ('弹性MapReduce','EKS官网') or :industryDept = '港澳台及国际业务部') then change_service_core_from_last_month else change_bill_core_from_last_month end)
) as change_bill_core_from_last_month1,
sum(
(case when :industryDept = '港澳台及国际业务部' then change_service_gpu_from_last_month else change_bill_gpu_from_last_month end)
) as change_bill_gpu_from_last_month1
from std_crp.dwd_txy_scale_df
where
    (case when (:longTermProduct in ('弹性MapReduce','EKS官网') or :industryDept = '港澳台及国际业务部') then change_service_core_from_last_month else change_bill_core_from_last_month end) != 0
    ${FILTER}
group by stat_time,customhouse_title,region_name,zone_name,
    ${CUSTOMER_SHORT_NAME_2_COMMON_CUSTOMER_SHORT_NAME_CASE_WHEN} as common_customer_short_name,
    (case when :longTermProduct = 'GPU(裸金属&CVM)' then gpu_card_type else ${INSTANCE_TYPE_2_INSTANCE_FAMILY_CASE_WHEN} end) as product_group,
    instance_type
order by stat_time,common_customer_short_name,zone_name,instance_type