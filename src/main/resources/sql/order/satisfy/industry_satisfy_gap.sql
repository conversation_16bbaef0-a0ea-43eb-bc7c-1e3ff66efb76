
select a.customer_short_name as customer_short_name,
       a.industry_dept as industry_dept,
       count(DISTINCT a.order_number) as gap_order_count,
       GROUP_CONCAT( DISTINCT b.project_name) as all_project_name,
       GROUP_CONCAT( DISTINCT a.zone_name) as all_zone_name,
       GROUP_CONCAT( DISTINCT a.instance_type) as all_instance_type,
       sum(if(a.wait_stock_core > a.match_core, a.wait_stock_core - a.match_core, 0)) as total_gap_core
from order_item_satisfy_rate a
         left join order_info b on a.order_number = b.order_number
where a.calc_date = ? and a.match_type = ? and b.available_status = ?
  and a.deleted = 0 and b.deleted = 0 and a.calc_date != a.begin_buy_date
  and b.order_status in (?)
  -- 满足阈值缺口比例>=20%
  and ((a.wait_stock_core - a.match_core) * 100) / a.wait_stock_core > 20
  and a.industry_dept is not null
group by a.customer_short_name,a.industry_dept