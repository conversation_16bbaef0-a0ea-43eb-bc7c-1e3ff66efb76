
select order_number as order<PERSON><PERSON><PERSON>,
       zone_name as zoneName,
       instance_model as instance<PERSON><PERSON><PERSON>,
       cpu_num as cpu,
       gpu_num as gpu,
       sum(total_valid_cpu_count) as preDeductCore,
       sum(total_valid_count) as preDeductNum,
       sum(total_reserved_count) as actualPreDeductNum,
       sum(total_reserved_cpu_count) as actualPreDeductCore,
       sum(IFNULL(gpu_num, 0)  * IFNULL(total_valid_count, 0)) as preDeductGpuNum,
       sum(IFNULL(gpu_num, 0)  * IFNULL(total_reserved_count, 0)) as actualPreDeductGpuNum
from pre_deduct_order_item
where order_number in (?)
    and deleted = 0
    and status in (?)
group by order_number, zone_name, instance_model, cpu_num, gpu_num
