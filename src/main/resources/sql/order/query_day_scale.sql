select stat_time,instance_type,region,region_name,app_role,sum(cur_service_core) as cur_service_total,sum(new_service_core) as new_service_total
--        sum(case when product = 'GPU' then cur_bill_gpu else cur_bill_core end) as cur_bill_total,
--        sum(case when product = 'GPU' then new_bill_gpu else new_bill_core end) as new_bill_total
from std_crp.dwd_txy_scale_df
${whereCondition}
group by stat_time,instance_type,region,region_name,app_role order by stat_time asc