select ord.order_id,
       ord.create_user,
       ord.change_reason,
       ord.change_reason_detail,
       ord.create_time,
       ord.approval_user,
       detail.dws_id,
       ord.approval_time,
       detail.total_score,
       detail.adj_total_score
from order_score_change_order ord
         left join order_score_change_order_detail detail on ord.order_id = detail.order_id
where detail.dws_id in (?)
  and ord.status = ?
order by
    ord.approval_time desc -- 按审批时间降序