-- 物理机退回未执行
select
    sum(core_amount - applied_core_amount) as `sum(core_num)`,if(plan_product_name in (?),'云业务','自研业务') as business_type,0 as is_executed
from yunti_demand.dwd_yunti_device_return_plan_item_df
where
  stat_date = ?
  and plan_type = 'IN_PLAN'
  and plan_time >= ?
  and device_amount - applied_device_amount > 0
  -- snapshot_date = '20230917'
  -- and plan_time >= '2023-09-17'
  -- and plan_time <= '2023-12-31'
group by business_type


-- 上游数据
-- select sum(supply_num),if(custom_bg = 'CSIG云与智慧产业事业群', '云业务','自研业务') as bs_ from supply_demand_hedging_result
-- where ver_num = 1695686413
--   and supply_num > 0
--   and supply_type = 'S9'
--   and actual_date  >= '2023-09-26'
--   and actual_date <= '2023-12-31'
-- group by bs_