INSERT INTO TABLE std_crp.dws_sop_report_mif
select
t.`version`,
rowNumberInAllBlocks() + 1 as `id`,
t.stat_time,
t.`index`,
t.num,
t.core_num,
t.capacity,
t.business_type,
t.index_date,
t.index_year,
t.index_month,
t.index_year_month,
t.index_week,
t.res_type,
t.res_pool_type,
t.obs_project_type,
t.bg_name,
t.custom_bg_name,
t.dept_name,
t.plan_product_name,
t.customhouse_title,
t.country_name,
t.city_name,
t.cmdb_campus_name,
t.cmdb_module_name,
t.txy_zone_name,
t.cvm_gins_family,
t.cvm_gins_type,
t.phy_device_family,
t.phy_device_type,
t.is_hedge,
t.has_hedged,
t.is_ca,
t.json_text,
t.capacity_unit,
t.obs_business_type,
t.self_to_cloud_type,
t.is_ignore,
t.cvm_gins_kingdom,
t.code_version
from (
    select
    `version`,
    stat_time,
    case sot_type when '原始机型' then 'TRANSFORMATION_ORIGINAL_NUM' else 'TRANSFORMATION_NEW_NUM' end as `index`, -- 改造-改造前总量(原机型)/改造后总量(新机型)
    num,
    core_num,
    capacity,
    business_type,
    index_date,
    index_year,
    index_month,
    index_year_month,
    index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    '(空值)' as cvm_gins_family,
    '(空值)' as cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    '(空值)' as cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_transformation_device
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'HEDGING_NUM' as `index`, -- 对冲量（库存/改造/退回）
    num,
    core_num,
    capacity,
    business_type,
    index_date,
    index_year,
    index_month,
    index_year_month,
    index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_hedging
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'PROCUREMENT_NET_FORECAST' as `index`, -- 采购-云业务-净预测
    num,
    core_num,
    capacity,
    business_type,
    index_date,
    index_year,
    index_month,
    index_year_month,
    index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    '(空值)' as cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_pg
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'INVENTORY_ESTIMATED_INVENTORY' as `index`, -- 库存-预估库存
        num[0+1] as num,
    core_num[0+1] as core_num,
    capacity[0+1] as capacity,
    business_type,
    date_add(MONTH,0,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_end_available
    where
    version='${version}'
    union all
    select
    `version`,
    stat_time,
    'INVENTORY_ESTIMATED_INVENTORY' as `index`, -- 库存-预估库存
        num[1+1] as num,
    core_num[1+1] as core_num,
    capacity[1+1] as capacity,
    business_type,
    date_add(MONTH,1,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_end_available
    where
    version='${version}'
    union all
    select
    `version`,
    stat_time,
    'INVENTORY_ESTIMATED_INVENTORY' as `index`, -- 库存-预估库存
        num[2+1] as num,
    core_num[2+1] as core_num,
    capacity[2+1] as capacity,
    business_type,
    date_add(MONTH,2,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_end_available
    where
    version='${version}'
    union all
    select
    `version`,
    stat_time,
    'INVENTORY_ESTIMATED_INVENTORY' as `index`, -- 库存-预估库存
        num[3+1] as num,
    core_num[3+1] as core_num,
    capacity[3+1] as capacity,
    business_type,
    date_add(MONTH,3,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_end_available
    where
    version='${version}'
    union all
    select
    `version`,
    stat_time,
    'INVENTORY_ESTIMATED_INVENTORY' as `index`, -- 库存-预估库存
        num[4+1] as num,
    core_num[4+1] as core_num,
    capacity[4+1] as capacity,
    business_type,
    date_add(MONTH,4,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_end_available
    where
    version='${version}'
    union all
    select
    `version`,
    stat_time,
    'INVENTORY_ESTIMATED_INVENTORY' as `index`, -- 库存-预估库存
        num[5+1] as num,
    core_num[5+1] as core_num,
    capacity[5+1] as capacity,
    business_type,
    date_add(MONTH,5,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_end_available
    where
    version='${version}'
    union all
    select
    `version`,
    stat_time,
    'INVENTORY_ESTIMATED_INVENTORY' as `index`, -- 库存-预估库存
        num[6+1] as num,
    core_num[6+1] as core_num,
    capacity[6+1] as capacity,
    business_type,
    date_add(MONTH,6,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_end_available
    where
    version='${version}'
    union all
    select
    `version`,
    stat_time,
    'INVENTORY_ESTIMATED_INVENTORY' as `index`, -- 库存-预估库存
        num[7+1] as num,
    core_num[7+1] as core_num,
    capacity[7+1] as capacity,
    business_type,
    date_add(MONTH,7,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_end_available
    where
    version='${version}'
    union all
    select
    `version`,
    stat_time,
    'INVENTORY_ESTIMATED_INVENTORY' as `index`, -- 库存-预估库存
        num[8+1] as num,
    core_num[8+1] as core_num,
    capacity[8+1] as capacity,
    business_type,
    date_add(MONTH,8,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_end_available
    where
    version='${version}'
    union all
    select
    `version`,
    stat_time,
    'INVENTORY_ESTIMATED_INVENTORY' as `index`, -- 库存-预估库存
        num[9+1] as num,
    core_num[9+1] as core_num,
    capacity[9+1] as capacity,
    business_type,
    date_add(MONTH,9,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_end_available
    where
    version='${version}'
    union all
    select
    `version`,
    stat_time,
    'INVENTORY_ESTIMATED_INVENTORY' as `index`, -- 库存-预估库存
        num[10+1] as num,
    core_num[10+1] as core_num,
    capacity[10+1] as capacity,
    business_type,
    date_add(MONTH,10,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_end_available
    where
    version='${version}'
    union all
    select
    `version`,
    stat_time,
    'INVENTORY_ESTIMATED_INVENTORY' as `index`, -- 库存-预估库存
        num[11+1] as num,
    core_num[11+1] as core_num,
    capacity[11+1] as capacity,
    business_type,
    date_add(MONTH,11,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_end_available
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'INVENTORY_REMAINING_UNAVAILABLE' as `index`, -- 库存-剩余不可用库存
    num[0+1] as num,
    core_num[0+1] as core_num,
    capacity[0+1] as capacity,
    business_type,
    date_add(MONTH,0,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_unavailable
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'INVENTORY_REMAINING_UNAVAILABLE' as `index`, -- 库存-剩余不可用库存
    num[1+1] as num,
    core_num[1+1] as core_num,
    capacity[1+1] as capacity,
    business_type,
    date_add(MONTH,1,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_unavailable
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'INVENTORY_REMAINING_UNAVAILABLE' as `index`, -- 库存-剩余不可用库存
    num[2+1] as num,
    core_num[2+1] as core_num,
    capacity[2+1] as capacity,
    business_type,
    date_add(MONTH,2,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_unavailable
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'INVENTORY_REMAINING_UNAVAILABLE' as `index`, -- 库存-剩余不可用库存
    num[3+1] as num,
    core_num[3+1] as core_num,
    capacity[3+1] as capacity,
    business_type,
    date_add(MONTH,3,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_unavailable
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'INVENTORY_REMAINING_UNAVAILABLE' as `index`, -- 库存-剩余不可用库存
    num[4+1] as num,
    core_num[4+1] as core_num,
    capacity[4+1] as capacity,
    business_type,
    date_add(MONTH,4,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_unavailable
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'INVENTORY_REMAINING_UNAVAILABLE' as `index`, -- 库存-剩余不可用库存
    num[5+1] as num,
    core_num[5+1] as core_num,
    capacity[5+1] as capacity,
    business_type,
    date_add(MONTH,5,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_unavailable
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'INVENTORY_REMAINING_UNAVAILABLE' as `index`, -- 库存-剩余不可用库存
    num[6+1] as num,
    core_num[6+1] as core_num,
    capacity[6+1] as capacity,
    business_type,
    date_add(MONTH,6,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_unavailable
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'INVENTORY_REMAINING_UNAVAILABLE' as `index`, -- 库存-剩余不可用库存
    num[7+1] as num,
    core_num[7+1] as core_num,
    capacity[7+1] as capacity,
    business_type,
    date_add(MONTH,7,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_unavailable
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'INVENTORY_REMAINING_UNAVAILABLE' as `index`, -- 库存-剩余不可用库存
    num[8+1] as num,
    core_num[8+1] as core_num,
    capacity[8+1] as capacity,
    business_type,
    date_add(MONTH,8,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_unavailable
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'INVENTORY_REMAINING_UNAVAILABLE' as `index`, -- 库存-剩余不可用库存
    num[9+1] as num,
    core_num[9+1] as core_num,
    capacity[9+1] as capacity,
    business_type,
    date_add(MONTH,9,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_unavailable
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'INVENTORY_REMAINING_UNAVAILABLE' as `index`, -- 库存-剩余不可用库存
    num[10+1] as num,
    core_num[10+1] as core_num,
    capacity[10+1] as capacity,
    business_type,
    date_add(MONTH,10,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_unavailable
    where
    version='${version}'
    union all
    select
    `version`,
    stat_time,
    'INVENTORY_REMAINING_UNAVAILABLE' as `index`, -- 库存-剩余不可用库存
    num[11+1] as num,
    core_num[11+1] as core_num,
    capacity[11+1] as capacity,
    business_type,
    date_add(MONTH,11,stat_time) as index_date,
    toYear(index_date) as index_year,
    toMonth(index_date) as index_month,
    formatDateTime(index_date,'%Y-%m') as index_year_month,
    toWeek(index_date) as index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_inventory_unavailable
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'TRANSFER_NUM' as `index`, -- 转移量
    num,
    core_num,
    capacity,
    business_type,
    index_date,
    index_year,
    index_month,
    index_year_month,
    index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    '(空值)' as cvm_gins_family,
    '(空值)' as cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    '(空值)' as cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_transfer_device
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'REPLACEMENT_NUM' as `index`, -- 置换量
    num,
    core_num,
    capacity,
    business_type,
    index_date,
    index_year,
    index_month,
    index_year_month,
    index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    '(空值)' as cvm_gins_family,
    '(空值)' as cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    '(空值)' as cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_replacement_device
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    case when json_text = 'PA1' then 'PROCUREMENT_AFTER_SUBMITTED' else 'PROCUREMENT_BEFORE_SUBMITTED' end as `index`, -- 采购-自研业务-已采购1_22.12.1后提交采购/已采购2_22.12.1前提交采购
    num,
    core_num,
    capacity,
    business_type,
    index_date,
    index_year,
    index_month,
    index_year_month,
    index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    '(空值)' as cvm_gins_family,
    '(空值)' as cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    '(空值)' as cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_procurement_device
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    case is_executed when 1 then 'RETURN_EXECUTED' else if(index_date >= '${dateVersion}','RETURN_NOT_EXECUTED','RETURN_EXPIRED') end as `index`, -- 退回-已执行/未执行/已过期
    num,
    core_num,
    capacity,
    business_type,
    index_date,
    index_year,
    index_month,
    index_year_month,
    index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    '(空值)' as cvm_gins_family,
    '(空值)' as cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    if(return_reason_type not in ('自研上云母机退回','过保故障退回','退役回收(折旧满4年)')
            and is_cloud_return in (0,1,-1),0,1) as is_ignore,
    '(空值)' as cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_return_device
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    'RETURN_EXECUTED' as `index`, -- 退回-已执行
    num,
    core_num,
    capacity,
    business_type,
    index_date,
    index_year,
    index_month,
    index_year_month,
    index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    if(return_type in ('(空值)','直接销毁')
           and return_reason_type not in ('自研上云母机退回','过保故障退回','退役回收(折旧满4年)'),0,1) as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_return_cvm
    where
    version='${version}'
    and is_executed = 1
union all
    select
    `version`,
    stat_time,
    if(index_date >= '${dateVersion}','RETURN_NOT_EXECUTED','RETURN_EXPIRED') as `index`, -- 退回-未执行,已过期
    num,
    core_num,
    capacity,
    business_type,
    index_date,
    index_year,
    index_month,
    index_year_month,
    index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    if(return_type in ('(空值)','直接销毁')
           and return_reason_type not in ('自研上云母机退回','过保故障退回','退役回收(折旧满4年)'),0,1) as is_ignore,
    cvm_gins_kingdom,
    code_version
from
    std_crp.dwd_sop_report_return_cvm_ext
where
    version='${version}'
    and is_executed = 0
    and plan_type = 'IN_PLAN'
union all
    select
    `version`,
    stat_time,
    case
        when is_executed = 1 then 'DEMAND_EXECUTED'  -- 已执行
        when is_executed = 0 then
            case obs_project_type
                when '采购buff' then 'DEMAND_NOT_HEDGING' -- 云业务不参与对冲
                else 'DEMAND_NOT_EXECUTED' -- CVM未执行过期策略by月
            end
    else '(空值)' end as `index`, -- 需求(已执行，未执行，云需求(不对冲))
    num,
    core_num,
    capacity,
    business_type,
    index_date,
    index_year,
    index_month,
    index_year_month,
    index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    '(空值)' as cvm_gins_family,
    '(空值)' as cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    self_to_cloud_type,
    0 as is_ignore,
    '(空值)' as cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_demand_device
    where
    version='${version}'
union all
    select
    `version`,
    stat_time,
    case
        when is_executed = 1 then 'DEMAND_EXECUTED'  -- 已执行
        when is_executed = 0 then
            case
                when holiday_year_month >= '${yearMonthVersion}' then if(res_pool_type = '公有池','DEMAND_NOT_HEDGING','DEMAND_NOT_EXECUTED')  -- 云业务不参与对冲
                else 'DEMAND_EXPIRED' -- CVM未执行过期策略by月
            end
    else '(空值)' end as `index`, -- 需求(已执行，未执行，云需求(不对冲))
    num,
    core_num,
    capacity,
    business_type,
    index_date,
    index_year,
    index_month,
    index_year_month,
    index_week,
    res_type,
    res_pool_type,
    obs_project_type,
    bg_name,
    custom_bg_name,
    dept_name,
    plan_product_name,
    customhouse_title,
    country_name,
    city_name,
    cmdb_campus_name,
    cmdb_module_name,
    txy_zone_name,
    cvm_gins_family,
    cvm_gins_type,
    phy_device_family,
    phy_device_type,
    is_hedge,
    has_hedged,
    is_ca,
    json_text,
    capacity_unit,
    obs_business_type,
    '(空值)' as self_to_cloud_type,
    0 as is_ignore,
    cvm_gins_kingdom,
    code_version
    from
    std_crp.dwd_sop_report_demand_cvm
    where
    version='${version}'
    and dept_name not in ('运营资源中心')
    and cvm_gins_type != '(空值)'
 ) t
