# Q单参与对冲了多少，有效对冲了多少
select quota_id                                                   quota_code,
       null                                                       plan_id,
       demand_date                                                require_date,
       round(sum(demand_num))                                     is_hedge_num,
       round(sum(if(supply_method != 'PURCHASE', demand_num, 0))) has_hedged_num
from t_resource.supply_demand_hedging_result
where ver_num = ?
  and quota_id in (?)
group by quota_id
# 不用group by require_date，目前一个单号对应一个日期