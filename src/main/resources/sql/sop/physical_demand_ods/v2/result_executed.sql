# Q单参与对冲了多少，有效对冲了多少
select quota_code,
       null                                                         plan_id,
       require_date,
       round(sum(require_amount))                                   is_hedge_num,
       round(sum(if(row_type = '库存满足需求', require_amount, 0))) has_hedged_num
from t_resource.sd_hedging2_detail_result
where version = ?
  and quota_code in (?)
  and hedging_version =
      (select max(hedging_version) from t_resource.sd_hedging2_detail_result where version = ?)
group by quota_code
# 不用group by require_date，目前一个单号对应一个日期