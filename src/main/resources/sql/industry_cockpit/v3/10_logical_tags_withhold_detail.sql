select stat_time,
       if(${has_biz_type}, biz_type, null)                as any_biz_type,
       if(${has_withhold_type}, withhold_type, null)                as any_withhold_type,
       if(${has_industry_dept}, industry_dept, null)                as any_industry_dept,
       if(${has_war_zone}, crp_war_zone, null)                      as any_war_zone,
       if(${has_customer_short_name}, un_customer_short_name, null) as any_customer_short_name,
       if(${has_app_id}, app_id, null)                              as any_app_id,
       if(${has_uin}, uin, null)                                    as any_uin,
       if(${has_instance_family}, instance_family, NULL)            as any_instance_family,
       if(${has_instance_type}, instance_type, NULL)                as any_instance_type,
       if(${has_host_ip}, host_ip, NULL)                            as any_host_ip,
       if(${has_customhouse_title}, customhouse_title, NULL)        as any_customhouse_title,
       if(${has_country_name}, country_name, NULL)                  as any_country_name,
       if(${has_area_name}, area_name, NULL)                        as any_area_name,
       if(${has_region_name}, region_name, NULL)                    as any_region_name,
       if(${has_zone_name}, zone_name, NULL)                        as any_zone_name,
       if(${has_zone_id}, zone_id, NULL)                            as any_zone_id,
       if(${has_cvm_standard_type}, cvm_standard_type, NULL)        as any_cvm_standard_type,
       if(${has_app_mask}, app_mask, NULL)                          as any_app_mask,
       if(${has_bsi_id}, bsi_id, NULL)                              as any_bsi_id,
       if(${has_stack_label}, stack_label, null)                    as any_stack_label,
       if(${has_logical_info}, logical_info, null)                  as any_logical_info,
       if(${has_tag_info}, tag_info, null)                          as any_tag_info,
       if(${has_gpu_card_type}, gpu_card_type, null)                as any_gpu_card_type,
       sum(cpu_core_total)                                          as sum_cpu_core_total,
       sum(cpu_core_good)                                           as sum_cpu_core_good,
       sum(cpu_core_bad)                                            as sum_cpu_core_bad,
       sum(cpu_core_idle)                                           as sum_cpu_core_idle,
       sum(cpu_core_logical)                                         as sum_cpu_core_logical,
       sum(cpu_core_tag)                                           as sum_cpu_core_tag,
       sum(cpu_core_tag + cpu_core_logical)                                           as sum_cpu_core
from std_crp.dws_logical_tags_withhold_df ${where}
group by stat_time, any_biz_type,any_withhold_type, any_industry_dept, any_war_zone, any_customer_short_name, any_app_id, any_uin,
         any_instance_family, any_instance_type, any_host_ip, any_customhouse_title, any_country_name, any_area_name,
         any_region_name, any_zone_name, any_zone_id, any_cvm_standard_type, any_app_mask, any_bsi_id, any_stack_label,
         any_logical_info, any_tag_info, any_gpu_card_type
order by sum_cpu_core desc
