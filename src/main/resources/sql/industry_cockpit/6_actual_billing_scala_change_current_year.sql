-- 全年规模净增 = 历史月份月切片规模变化量 + 未来月份最新版13周预测


select sum(increase_num) as increase_cores, sum(return_num) as return_cores, sum(net_num) as net_cores
from (
    -- 未来月份最新版13周预测
    select
        toDecimal64(sum(case when demand_type in ('NEW', 'ELASTIC') then total_core else 0.0 end), 0) as increase_num,
        toDecimal64(sum(case when demand_type in ('RETURN') then  -total_core else 0.0 end), 0) as return_num,
        toDecimal64(sum(case when demand_type in ('RETURN') then -total_core else total_core end), 0) as net_num
    from std_crp.dws_crp_ppl_item_version_newest_cf
    ${PREDICTION_FILTER}
    ${PREDICTION_FIX_WHERE}
    union all
    -- 未来月份指定版本的13周预测
    select
        toDecimal64(sum(case when demand_type in ('NEW', 'ELASTIC') then total_core else 0.0 end), 0) as increase_num,
        toDecimal64(sum(case when demand_type in ('RETURN') then  -total_core else 0.0 end), 0) as return_num,
        toDecimal64(sum(case when demand_type in ('RETURN') then -total_core else total_core end), 0) as net_num
    from std_crp.dwd_crp_ppl_item_version_cf
    ${VERSION_PREDICTION_FILTER}
    ${PREDICTION_FIX_WHERE}
) tmp




