-- 月均维度的变化量
select toYYYYMM(stat_time) as data_name,
       round(sum(case when '${DEMAND_TYPE}' = '新增&弹性' then case when diff_billcpu > 0 then diff_billcpu else 0 end
                      when '${DEMAND_TYPE}' = '退回' then case when diff_billcpu < 0 then diff_billcpu else 0 end end
                 )
             ) data_value
from  cloud_demand.ppl_billing_scale_monthly
${FILTER}
${FIX_WHERE}
group by data_name
order by data_name
