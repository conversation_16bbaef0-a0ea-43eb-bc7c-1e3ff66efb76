-- CBS需求、未执行、已执行
select
    year_of_week as year,
    month_of_week as month,
    concat(toString(`year_of_week`),'-',if(`month_of_week` < 10,concat('0',toString(`month_of_week`)),toString(`month_of_week`))) as year_month,
    customhouse_title,
    zone_name,
    custom_bg_name,
    bg_name,
    dept_name,
    plan_product_name,
    project_name,
    case when '${type}' = 'CBS需求' then applied_disk_amount + lave_all_disk_amount
    	 when '${type}' = 'CBS已执行' then applied_disk_amount
    	 else lave_all_disk_amount
    end as amount,
    case when cbs_res_class_name = '增强型SSD云硬盘' then 'SSD云硬盘'
         when cbs_res_class_name = '普通云硬盘' then '高性能云硬盘'
         when cbs_res_class_name = 'CLOUD_SSD' then 'SSD云硬盘'
         when cbs_res_class_name like '%SSD%' then 'SSD云硬盘'
         when cbs_res_class_name like '%PREMIUM%' then '高性能云硬盘'
         else cbs_res_class_name -- 默认给SSD云硬盘
    end  as volume_type,
    applied_cvm_amount + lave_cvm_amount as instance_num,
    instance_io
from yunti_demand.dwd_yunti_cvm_demand_forecast_item_df
where stat_date = ? and year_of_week >= 2023 and volume_type != '' and dept_id not in(32,1299)

