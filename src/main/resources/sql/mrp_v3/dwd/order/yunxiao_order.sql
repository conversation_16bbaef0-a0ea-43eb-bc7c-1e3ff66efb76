select if(product = 'CVM&CBS', '外部行业', '内领业务')        biz_type,
       industry_dept,
       customhouse_title,
       region_name,
       zone_name,
       instance_type,
       customer_uin                                           uin,
       multiIf(demand_type = 'NEW', '新增', demand_type = 'ELASTIC', '弹性',
               demand_type = 'RETURN', '退回',
               '(空值)')                                      `demand_type`,
       'CVM'                                                  data_product,
       product                                                product_class,
       formatDateTime(begin_buy_date, '%Y-%m')                `year_month`,
       customer_short_name,
       is_spike,
       sum(if(demand_type = '退回', -total_core, total_core)) num
from std_crp.ads_mck_forecast_summary_df
where product in ('CVM&CBS', ${PAAS})
  and demand_source = '预约单'
  and stat_time = '${stat_time}'
  and total_core <> 0
  and `year_month` >= '${start_year_month}'
group by biz_type,
         industry_dept,
         customhouse_title,
         region_name,
         zone_name,
         product_class,
         instance_type,
         uin,
         `demand_type`,
         data_product,
         `year_month`,
         customer_short_name,
         is_spike
-- GPU
-- 外部业务
union all
select '外部行业'                                                   biz_type,
       industry_dept,
       customhouse_title,
       region_name,
       zone_name,
       instance_type,
       customer_uin                                                 uin,
       multiIf(demand_type = 'NEW', '新增', demand_type = 'ELASTIC', '弹性',
               demand_type = 'RETURN', '退回',
               '(空值)')                                            `demand_type`,
       'GPU'                                                        data_product,
       product                                                      product_class,
       formatDateTime(begin_buy_date, '%Y-%m')                      `year_month`,
       customer_short_name,
       is_spike,
       sum(if(demand_type = '退回', -total_gpu_num, total_gpu_num)) num
from std_crp.ads_mck_forecast_summary_df
where product in ('GPU(裸金属&CVM)')
  and demand_source = '预约单'
  and stat_time = '${stat_time}'
  and total_core <> 0
  and `year_month` >= '${start_year_month}'
group by biz_type,
         industry_dept,
         customhouse_title,
         region_name,
         zone_name,
         product_class,
         instance_type,
         uin,
         `demand_type`,
         data_product,
         `year_month`,
         customer_short_name,
         is_spike