-- CVM
select biz_range_type,                                                            -- 业务类型
       new_customer_type,                                                         -- 客户分类
       project_type,                                                              -- 项目类型
       product_class,                                                             -- 产品子类
       origin_industry_dept,                                                      -- 原始部门
       customhouse_title,                                                         -- 境内外
       region_name,                                                               -- 地域
       zone_name,                                                                 -- 可用区
       instance_type,                                                             -- 机型
       uin,                                                                       -- 客户 uin
       customer_short_name,                                                       -- 客户简称
       customer_tab_type,                                                         -- 客户简称
       product,                                                                   -- 产品
       app_role,                                                                  -- app_role
       formatDateTime(stat_time, '%Y-%m')                        `year_month`,    -- 执行年月
       if(product = 'CVM', change_bill_core_from_last_month,
          change_bill_gpu_from_last_month)                       diff_bill_num,   -- 计费变化量
       if(product = 'CVM', change_service_core_from_last_month,
          change_service_gpu_from_last_month)                 as diff_server_num, -- 服务变化量
       if(product = 'CVM', cur_bill_core, cur_bill_gpu)       as stock_bill_num,  -- 计费存量
       if(product = 'CVM', cur_service_core, cur_service_gpu) as stock_server_num -- 服务存量
from std_crp.dwd_txy_scale_df_view
where product in ('CVM', 'GPU') -- 只看 cvm
  and stat_time in ('${month_end_day_range}') -- 限制起始年月


