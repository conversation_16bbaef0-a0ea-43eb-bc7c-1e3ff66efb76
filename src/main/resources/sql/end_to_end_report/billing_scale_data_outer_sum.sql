-- 外部
select stat_time,
       '外部行业' type,
       org_label deptOrProduct,
       '购买' demand_type,
       case
           when biz_type = 'cvm' and cpu_or_gpu = 'CPU' then 'CVM'
           when biz_type = 'baremetal' and cpu_or_gpu = 'CPU' then '裸金属'
           when cpu_or_gpu = 'GPU' then 'GPU' end as `product_type`,
       sum(case when cpu_or_gpu = 'CPU' then diff_billcpu when cpu_or_gpu = 'GPU' then diff_billgpu end) total
from ppl_billing_scale_monthly
where stat_time in (:statTime)
and app_role in ('正常售卖', 'CDH', '预扣包', 'GOCP', 'CDZ')
${FILTER}
${DEMAND_TYPE} > 0
group by stat_time, deptOrProduct, demand_type, product_type

union all

-- 外部
select stat_time,
       '外部行业' type,
       org_label deptOrProduct,
       '退回' demand_type,
       case
           when biz_type = 'cvm' and cpu_or_gpu = 'CPU' then 'CVM'
           when biz_type = 'baremetal' and cpu_or_gpu = 'CPU' then '裸金属'
           when cpu_or_gpu = 'GPU' then 'GPU' end as `product_type`,
       sum(case when cpu_or_gpu = 'CPU' then diff_billcpu when cpu_or_gpu = 'GPU' then diff_billgpu end) total
from ppl_billing_scale_monthly
where stat_time in (:statTime)
  and app_role in ('正常售卖', 'CDH', '预扣包', 'GOCP', 'CDZ')
    ${FILTER}
    ${DEMAND_TYPE} < 0
group by stat_time, deptOrProduct, demand_type, product_type