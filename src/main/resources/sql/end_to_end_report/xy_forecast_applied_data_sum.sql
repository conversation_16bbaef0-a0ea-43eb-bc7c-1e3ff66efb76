-- 历史月份取执行
select p_ym,
       '已执行'                                type,
       sum(cores)                                cores,
       sum(num)         num,
       dim_industry,
       dim_instance_type,
       dim_device_type,
       case when JSONExtractString(ori_data, 'projSetName') = '常规项目' then '常规' else '其他' end purchaseCategory
       --       JSONExtractString(ori_data, 'moduleName') module_name,
--       JSONExtractString(ori_data, 'campus')     campus
from `ads_tcres_demand_annual_excueted_report` t
where version = :version
  and p_index = '已执行预测'
  and p_ym in (:historyYearMonth)
    ${FILTER}
group by p_ym, dim_industry, dim_instance_type, purchaseCategory, dim_device_type

union all

-- 当月及当月以后取预测
select p_ym,
       '13周预测'                             type,
       sum(cores)                                cores,
       sum(num)         num,
       dim_industry,
       dim_instance_type,
       dim_device_type,
       '' purchaseCategory
--       JSONExtractString(ori_data, 'moduleName') module_name,
--       JSONExtractString(ori_data, 'campus')     campus
from `ads_tcres_demand_annual_excueted_report` t
where version = :version
  and p_index = '13周预测'
  and p_ym in (:nextYearMonths)
    ${FILTER}
group by p_ym, dim_industry, dim_instance_type, purchaseCategory, dim_device_type


