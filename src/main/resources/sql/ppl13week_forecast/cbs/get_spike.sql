WITH raw AS (
    SELECT year,month,uin AS customer_uin,any(industry_dept) AS industry_dept1,any(customer_short_name) AS customer_short_name1,
    zone_name,any(region_name) AS region_name1,any(customhouse_title) AS customhouse_title1,
    disk_volume_type_name AS instance_type,'' AS biz_range_type,
     -- 先把当月末所有 change_service_disk_from_last_month 加总，得 net_diff
    sum(CASE WHEN stat_time = month_end_date THEN change_service_disk_from_last_month ELSE 0 END) AS net_diff,
     -- 当月末 cur_service_disk 总和
    sum(CASE WHEN stat_time = month_end_date THEN cur_service_disk ELSE 0 END ) AS cur_core
FROM std_crp.dwd_txy_cbs_scale_df t
WHERE stat_time < :predictDate and stat_time !='2021-01-31'
-- performance optimize
  and stat_time in (
    SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
    FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
    )

GROUP BY year,month,uin,zone_name,disk_volume_type_name
    )

SELECT year,month,customer_uin,industry_dept1 as industry_dept,customer_short_name1 as customer_short_name,
    zone_name,region_name1 as region_name,customhouse_title1 as customhouse_title,
    instance_type,biz_range_type,
    (case when net_diff > 0 then net_diff else 0 end) AS new_core,
    (case when net_diff < 0 then -net_diff else 0 end) AS ret_core,
    cur_core
FROM raw
where ((customhouse_title='境内' and (new_core>500*1024 or ret_core>500*1024))
   or (customhouse_title='境外' and (new_core>100*1024 or ret_core>100*1024)))
