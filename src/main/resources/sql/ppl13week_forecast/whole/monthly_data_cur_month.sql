
-- 这个是给行业数据看板的数据


with params as ( select max(stat_time) as max_stat_time from std_crp.dwd_txy_scale_df )
select year,month,
    any(month_start_date1) as first_day,
    any(month_end_date1) as last_day,
    any(customhouse_title1) as customhouse_title,
    any(region1) as region,
    region_name1 as region_name,
    instance_type1 as gins_family,
    sum(case when diff_bill_num>0 then diff_bill_num else 0 end) as new_diff, -- 增量：max-月初，>0部分
    sum(case when diff_bill_num<0 then -diff_bill_num else 0 end) as ret_diff, --   退回：max-月末，>0部分
    sum(cur_core) as last_day_num -- 当前：月末
from (
    select year,month,
    any(month_start_date) as month_start_date1,
    any(month_end_date) as month_end_date1,
    any(customhouse_title) as customhouse_title1,
    any(region) as region1,
    any(region_name) as region_name1,
    any(instance_type) as instance_type1,
    sum(case when stat_time=(select max_stat_time from params) then change_bill_service_core_from_last_month else 0 end) as diff_bill_num,
    sum(case when stat_time=(select max_stat_time from params) then cur_bill_service_core else 0 end) as cur_core
    from std_crp.dwd_txy_scale_df

    where 1=1
    -- condition 的条件格式不可以改变， WEB 查询会 append  " and sql "
    and cpu_or_gpu = 'CPU' and biz_type = 'cvm' and app_role!='EMR'
    and app_role != 'LH'
    and instance_type not like 'RS%' and instance_type not like 'RM%'
    and paymode_range_type in ('包年包月','弹性')
    and paymode!='5'
    and biz_range_type in ('外部业务')

    and zone_name in (:publicZoneName)
    and instance_type not in (:instanceTypeBlacklist) -- 黑名单机型
    and customer_short_name not in (:headCustomerShortName)

    and app_id not in (1258344706, 1251316161) -- 内部的2个appid，王丽丽给的，目前固定的，排除掉的
    and toStartOfMonth(stat_time) = toStartOfMonth(toDate(:predictDate))

    group by year,month,uin,zone_name,instance_model
    )
where abs(diff_bill_num) < 2000 -- 固定一个值，模型数据
group by region_name,year,month,gins_family