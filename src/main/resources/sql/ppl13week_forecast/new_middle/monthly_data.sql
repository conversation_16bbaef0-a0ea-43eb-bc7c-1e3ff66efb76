select year,month,
    any(month_start_date1) as first_day,
    any(month_end_date1) as last_day,
    any(customhouse_title1) as customhouse_title,
    any(region1) as region,
    region_name1 as region_name,
    instance_type1 as gins_family,
    sum(case when diff_bill_num>0 then diff_bill_num else 0 end) as new_diff, -- 增量：max-月初，>0部分
    sum(case when diff_bill_num<0 then -diff_bill_num else 0 end) as ret_diff, --   退回：max-月末，>0部分
    sum(cur_core) as last_day_num -- 当前：月末
from (
    select year,month,
    any(month_start_date) as month_start_date1,
    any(month_end_date) as month_end_date1,
    any(customhouse_title) as customhouse_title1,
    any(region) as region1,
    any(region_name) as region_name1,
    any(instance_type) as instance_type1,
    sum(case when stat_time=month_end_date then change_bill_service_core_from_last_month else 0 end) as diff_bill_num,
    sum(case when stat_time=month_end_date then cur_bill_service_core else 0 end) as cur_core
    from dwd_txy_scale_df
    where 1=1

    -- condition 的条件格式不可以改变， WEB 查询会 append  " and sql "
    ${CONDITION}

    and stat_time < :predictDate

    -- 下面 WEB_CONDITION 是给版本ppl 用的，不可删除
    /*${WEB_CONDITION}*/


    group by year,month,${GROUP_BY_DIM}
    )
group by region_name,year,month,gins_family