with raw as
(select year,month,uin as customer_uin, any(industry_dept1) as industry_dept, any(customer_short_name1) as customer_short_name,
     zone_name, any(region_name) as region_name, any(customhouse_title1) as customhouse_title,
    instance_type,any(biz_range_type1) as biz_range_type,
    sum(case when diff_core>0 then diff_core else 0 end) as new_core,
    sum(case when diff_core<0 then -diff_core else 0 end) as ret_core,
    sum(cur_core) as cur_core
from
    (
    -- 月切
    select year,month,uin, any(industry_dept) as industry_dept1, any(customer_short_name) as customer_short_name1,
    zone_name,any(region_name) as region_name, any(customhouse_title) as customhouse_title1,
    instance_type, any(biz_range_type) as biz_range_type1,
    sum(case when stat_time=month_end_date then change_bill_service_core_from_last_month else 0 end) as diff_core,
    sum(case when stat_time=month_end_date then cur_bill_service_core else 0 end) as cur_core
    from dwd_txy_scale_df
    where stat_time < :predictDate

    ${commonCondition}

    group by year,month,uin,zone_name,instance_type
    )
group by year,month,uin,zone_name,instance_type)

select * from raw where new_core>0 or ret_core>0