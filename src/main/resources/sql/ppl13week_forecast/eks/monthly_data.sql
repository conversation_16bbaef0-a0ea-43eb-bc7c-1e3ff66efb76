select year,month,
    any(month_start_date1) as first_day,
    any(month_end_date1) as last_day,
    any(customhouse_title1) as customhouse_title,
    any(region1) as region,
    region_name1 as region_name,
    instance_type1 as gins_family,
    sum(case when diff_bill_num>0 then diff_bill_num else 0 end) as new_diff, -- 增量：max-月初，>0部分
    sum(case when diff_bill_num<0 then -diff_bill_num else 0 end) as ret_diff, --   退回：max-月末，>0部分
    sum(cur_core) as last_day_num -- 当前：月末
from (
    select year,month,
    any(month_start_date) as month_start_date1,
    any(month_end_date) as month_end_date1,
    any(customhouse_title) as customhouse_title1,
    any(region) as region1,
    any(region_name) as region_name1,
    any(instance_type) as instance_type1,
    sum(case when stat_time=month_end_date then change_service_core_from_last_month else 0 end) as diff_bill_num, -- 注意，这里用服务核心数
    sum(case when stat_time=month_end_date then cur_service_core else 0 end) as cur_core -- 注意，这里用服务核心数
    from dwd_txy_eks_scale_df
    where 1=1

    -- condition 的条件格式不可以改变， WEB 查询会 append  " and sql "
    -- 该condition包含EMR的范围 和 毛刺剔除，剔除GPU机型
    ${CONDITION}

    and stat_time < :predictDate

    -- performance optimize
    and stat_time in (
    SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
    FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
    )

    -- 下面 WEB_CONDITION 是给版本ppl 用的，不可删除
    /*${WEB_CONDITION}*/

    group by year,month,uin,zone_name,instance_model
    )
group by region_name,year,month,gins_family