select null                                                                      order_type,
       '${type}'                                                                       biz_type,
       ${industryOrDept}                                                               industry_or_dept,
       ifnull(if(industry = '', '(空值)', industry), '(空值)')                           industry_dept,
       ifnull(if(txy_customhouse_title = '', '(空值)', txy_customhouse_title), '(空值)') region_type,
       ifnull(if(txy_region_name = '', '(空值)', txy_region_name), '(空值)')             region_name,
       ifnull(if(txy_zone_name = '', '(空值)', txy_zone_name), '(空值)')                 zone_name,
       ifnull(if(device_type = '', '(空值)', device_type), '(空值)')                     r_device_type,
       ifnull(if(customer_name = '', '(空值)', customer_name), '(空值)')                 r_customer_name,
       '(空值)'                                                                          r_demand_type,
       date_format(into_buffer_time, '%Y-%m')                                      `year_month`,
       ifnull(sum(logic_core_num), 0)                                              core_num,
       ifnull(sum(logic_core_num), 0)                                                   gpu_num,
       product                                                                     plan_product_name,
       compute_type                                                                compute_type,
       null                                                                        data_product
from report_purchase_order_detail