WITH
-- enable 作用于前端，这里不加上
    peak_forecast_origin_data -- 查出所有的预测数据
    AS (select predict_index,
    year,
    month,
    gins_family,
    region_name,
    type,
    core_num
from ppl_forecast_predict_result
    # 1,2,3代表m-1,m-2,m-3，其中m-1也是最新版，m-3是基准版，一起拉过来
where predict_index in (1, 2, 3)
     and 1=0
group by predict_index, year, month, gins_family, region_name, type),
    peak_forecast_data  -- 自关联上一个月的数据，把差值作为上一个月的数据
    as (select a.*, if(b.core_num is not null, a.core_num - b.core_num, 0) as diff_data
from peak_forecast_origin_data a
    left join peak_forecast_origin_data b
on ((a.year = b.year and a.month = b.month + 1) or (a.year = b.year + 1 and a.month = 1 and b.month = 12))
    and a.region_name = b.region_name
    and a.gins_family = b.gins_family
    and a.predict_index = b.predict_index)
-- 作为出口数据表
select predict_index,
       'CVM'                                                                       data_product,
       concat(year, '-', if(month < 10, concat('0', month), month))                `year_month`,
       gins_family,
       region_name,
       '弹性'                                                                       demand_type,
       '(空值)'                                                                     biz_type,
       1                                                                          is_new_category, -- 新版中长尾标识
       core_num                                                                     num
from peak_forecast_data
where concat(year, '-', if(month < 10, concat('0', month), month)) >= ?
group by predict_index, `year_month`,gins_family, region_name, demand_type;