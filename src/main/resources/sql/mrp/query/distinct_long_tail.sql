-- 查询中长尾数据(去重)
select
    order_type, -- 订单类型
    data_product, -- 产品
    any_project_type,
    any_product_class,
    biz_type, -- 业务类型
    if(${has_gins_family},gins_family,null) as gins_family, -- 实例类型 or 合并实例类型
    gpu_type, -- 卡型
    gpu_card_type, -- 卡类型
    industry_or_product, -- 行业部门
    customer_type, -- 客户类型
    any_region_name, -- 地域
    any_war_zone_name, -- 战区
    any_customer_group, -- 集团
    customer_short_name, -- 客户简称
    un_customer_short_name, -- 通用客户简称
    year_month, -- 年月
    if(${is_cvm},
       sum(if(change_bill_core_from_last_month>0,change_bill_core_from_last_month,toDecimal64(0, 6))),
       sum(if(change_bill_gpu_from_last_month>0,change_bill_gpu_from_last_month,toDecimal64(0, 6)))) as add_change_bill_core, -- 计费变化量(新增)
    if(${is_cvm},
       sum(if(change_service_core_from_last_month>0,change_service_core_from_last_month,toDecimal64(0, 6))),
       sum(if(change_service_gpu_from_last_month>0,change_service_gpu_from_last_month,toDecimal64(0, 6)))) as add_change_service_core, -- 服务变化量(新增)
    if(${is_cvm},
       sum(if(change_bill_core_from_last_month < 0,change_bill_core_from_last_month,toDecimal64(0, 6))),
       sum(if(change_bill_gpu_from_last_month < 0,change_bill_gpu_from_last_month,toDecimal64(0, 6)))) as return_change_bill_core, -- 计费变化量(退回)
    if(${is_cvm},
       sum(if(change_service_core_from_last_month < 0,change_service_core_from_last_month,toDecimal64(0, 6))),
       sum(if(change_service_gpu_from_last_month < 0,change_service_gpu_from_last_month,toDecimal64(0, 6)))) as return_change_service_core, -- 服务变化量(退回)
    if(${is_cvm},
       sum(change_bill_core_from_last_month),
       sum(change_bill_gpu_from_last_month)) as net_change_bill_core, -- 计费变化量(净增)
    if(${is_cvm},
       sum(change_service_core_from_last_month),
       sum(change_service_gpu_from_last_month)) as net_change_service_core -- 服务变化量(净增)
from (
     select
         if(${has_order_type},'中长尾',null) as order_type, -- 订单类型
         product as data_product, -- 产品
         if(${has_project_type},project_type,null) as any_project_type, -- 项目类型
         if(${has_product_class},product_class,null) as any_product_class, -- 产品子类
         if(${has_biz_type},case biz_range_type when '外部业务' then '外部行业' when '内部业务' then '内领业务' else '(空值)' end,null) as biz_type, -- 业务类型
         if(${is_un_instance_type},un_instance_type,instance_type) as gins_family, -- 实例类型 or 合并实例类型
         if(${has_gpu_type},gpu_type,null) as gpu_type, -- 卡型
         if(${has_gpu_card_type},gpu_card_type,null) as gpu_card_type, -- 卡类型
         if(${has_industry_or_product},industry_dept,null) as industry_or_product, -- 行业部门
         if(${has_customer_type},customer_tab_type,null) as customer_type, -- 客户类型
         if(${has_region_name},region_name,null) as any_region_name, -- 地域
         zone_name, -- 可用区
         if(${has_war_zone_name},war_zone_name,null) as any_war_zone_name, -- 战区
         if(${has_customer_group},customer_group,null) as any_customer_group, -- 集团
         if(${has_customer_short_name},customer_short_name,null) as customer_short_name, -- 客户简称
         if(${has_un_customer_short_name},un_customer_short_name,null) as un_customer_short_name, -- 通用客户简称
         if(${is_trend},concat(toString(`year`),'-',if(`month` < 10,concat('0',toString(`month`)),toString(`month`))),null) as year_month, -- 年月
         sum(change_bill_core_from_last_month) as change_bill_core_from_last_month,
         sum(change_bill_gpu_from_last_month) as change_bill_gpu_from_last_month,
         sum(change_service_core_from_last_month) as change_service_core_from_last_month,
         sum(change_service_gpu_from_last_month) as change_service_gpu_from_last_month
     from std_crp.dwd_txy_scale_long_tail_df_view
     where product = '${product_type}'
       and stat_time in ('${month_end_day_range}') -- 月最后一天集合
         ${is_new_customer_type} -- 中长尾过滤（兼容新客户分类和老客户分类）
         ${inner_app_role} -- appRole过滤(内部)
         ${external_app_role} -- appRole过滤(外部)
         ${is_rendering} -- 渲染机型
         ${un_instance_type} -- 合并机型过滤
         ${not_un_instance_type} -- 剔除合并机型过滤
         ${instance_type} -- 机型过滤
         ${not_instance_type} -- 剔除机型过滤
         ${biz_range_type} -- 行业部门过滤
         ${gpu_card_type} -- 卡类型
         ${gpu_type} -- 卡型
         ${industry_dept} -- 行业部门
         ${war_zone_name} -- 战区
         ${customer_group} -- 集团
         ${customer_short_name} -- 客户简称
         ${un_customer_short_name} -- 通用客户简称
         ${uin} -- 客户uin
         ${customhouse_title} -- 境内外
         ${region_name} -- 地域
         ${zone_name} -- 可用区
         ${ignore_zone_name} -- 可用区
         ${product_class} -- 产品类型
         ${project_type} -- 项目类型
     group by
         order_type,
         data_product,
         any_project_type,
         any_product_class,
         biz_type,
         gins_family,
         gpu_type,
         gpu_card_type,
         industry_or_product,
         any_region_name,
         zone_name,
         any_war_zone_name,
         customer_type,
         any_customer_group,
         customer_short_name,
         un_customer_short_name,
         year_month
     )
group by
    order_type,
    data_product,
    any_project_type,
    any_product_class,
    biz_type,
    gins_family,
    gpu_type,
    gpu_card_type,
    industry_or_product,
    any_region_name,
    any_war_zone_name,
    customer_type,
    any_customer_group,
    customer_short_name,
    un_customer_short_name,
    year_month
order by
    ${order_by_field} ${order_by_rule}
limit
    ${page_start},${page_size}