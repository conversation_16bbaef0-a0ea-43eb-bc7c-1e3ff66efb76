select null                          order_type,
       biz_type                          r_biz_type,
       industry_dept,
       region_name,
       zone_name,
       gins_family,
       uin,
       case pay_mode
           when 'POSTPAID_BY_HOUR' then '弹性'
           when 'SPOTPAID' then '弹性'
           else '新增' end               demand_type,
       formatDateTime(buy_date, '%Y-%m') `year_month`,
       sum(success_req_num)              success_req_num,
       sum(fail_req_num)                 fail_req_num,
       sum(success_core_num)             success_core_num,
       sum(fail_core_num)                fail_core_num,
       sum(success_gpu_num)              success_gpu_num,
       sum(fail_gpu_num)                 fail_gpu_num
from daily_tencent_cloud_buy_info
where buy_year >= ?
  and buy_month >= ?
group by biz_type, industry_dept, region_name, zone_name, gins_family, uin, demand_type, `year_month`