with detail as (select year,
    month,
    seq_type,
    gins_family,
    region_name,
    predict_core_num,
    predict_index
from std_crp.dwd_crp_longtail_forecast_item_df
WHERE category = '方案300：机型收敛-包年包月加弹性-行业加内领-月度ARIMAX'
  and predict_index in (1,2,3)
  and seq_type in ('RET','NEW')
  and has_predict = 1)

-- 作为出口数据表
select predict_index,
       'CVM'                                                                       data_product,
       concat(toString(year), '-', if(month < 10, concat('0', toString(month)), toString(month)))   `year_month`,
       gins_family,
       region_name,
       if(seq_type = 'NEW','新增','退回')                                            demand_type,
       '(空值)'                                                                     biz_type,
       1                                                                           is_new_category, -- 新版中长尾标识
       sum(if(seq_type = 'NEW',predict_core_num,-predict_core_num))                num
from detail
where concat(toString(year), '-', if(month < 10, concat('0', toString(month)), toString(month))) >= ?
and predict_core_num != 0
group by predict_index, `year_month`,gins_family, region_name, demand_type