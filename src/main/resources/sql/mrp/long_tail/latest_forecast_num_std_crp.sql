with version_code_t as (select max(version_code) as max_v from std_crp.dwd_crp_ppl_item_version_cf
                        where version_code like 'V_%'
                          and concat(toString(version_begin_year),'-',concat(if(version_begin_month < 10,'0',''),toString(version_begin_month))) <= ?
                          and concat(toString(version_end_year),'-',concat(if(version_end_month < 10,'0',''),toString(version_end_month))) >= ?)

select t.*, '中长尾' order_type
from (
-- CVM
-- PASS产品全部算内部业务
         select '内领业务'                                                    biz_type,
                product                                                       industry_or_product,
                industry_dept,
                multiIf(customhouse_title = '国内', '境内', customhouse_title = '海外', '境外',
                        customhouse_title = '境内', '境内', customhouse_title = '境外', '境外',
                        '(空值)')                                             region_type,
                region_name,
                zone_name,
                instance_type                                                 gins_family,
                0                                                             uin,
                multiIf(demand_type = 'NEW', '新增', demand_type = 'ELASTIC', '弹性',
                        demand_type = 'RETURN', '退回',
                        '(空值)')                                             `demand_type`,
                'CVM'                                                         data_product,
                formatDateTime(begin_buy_date, '%Y-%m')                       `year_month`,
                '(空值)'                                                      customer_name,
                '(空值)'                                                      customer_short_name,
                '(空值)'                                                      customer_person_type,
                '(空值)'                                                       `customer_type`,
                '(空值)'                                                         customer_group,
                war_zone                                                      war_zone_name,
                multiIf(uin_type = 0, '内部', uin_type = 1, '外部', '(空值)') customer_inside_or_outside,
                sum(if(demand_type = '退回', -total_core, total_core))        num
         from std_crp.dwd_crp_ppl_item_version_cf
         where product in (${PAAS})
           and version_code = (select max_v from version_code_t)
           and (source = 'FORECAST')
         group by  biz_type, industry_or_product, industry_dept, region_type, region_name, zone_name,
                   gins_family, uin,
                   `demand_type`, data_product, `year_month`, customer_name, customer_short_name,
                   `customer_type`,
                   customer_group, war_zone_name, customer_person_type, customer_inside_or_outside
         union all
         -- CVM
-- 内外部业务
         select multiIf(category = '内部业务', '内领业务', '外部行业')            biz_type,
                multiIf(biz_type = '外部行业', industry_dept, biz_type = '内领业务', product,
                        '(空值)')                                             industry_or_product,
                industry_dept,
                multiIf(customhouse_title = '国内', '境内', customhouse_title = '海外', '境外',
                        customhouse_title = '境内', '境内', customhouse_title = '境外', '境外',
                        '(空值)')                                             region_type,
                region_name,
                zone_name,
                instance_type                                                 gins_family,
                0                                                             uin,
                multiIf(demand_type = 'NEW', '新增', demand_type = 'ELASTIC', '弹性',
                        demand_type = 'RETURN', '退回',
                        '(空值)')                                             `demand_type`,
                'CVM'                                                         data_product,
                formatDateTime(begin_buy_date, '%Y-%m')                       `year_month`,
                '(空值)'                                                      customer_name,
                '(空值)'                                                      customer_short_name,
                '(空值)'                                                      customer_person_type,
                '(空值)'                                                       `customer_type`,
                '(空值)'                                                         customer_group,
                war_zone                                                      war_zone_name,
                multiIf(uin_type = 0, '内部', uin_type = 1, '外部', '(空值)') customer_inside_or_outside,
                sum(if(demand_type = '退回', -total_core, total_core))        num
         from std_crp.dwd_crp_ppl_item_version_cf
         where product = 'CVM&CBS'
           and version_code = (select max_v from version_code_t)
           and (source = 'FORECAST')
         group by  biz_type, industry_or_product, industry_dept, region_type, region_name, zone_name,
                   gins_family, uin,
                   `demand_type`, data_product, `year_month`, customer_name, customer_short_name,
                   `customer_type`,
                   customer_group, war_zone_name, customer_person_type, customer_inside_or_outside
         union all
         -- GPU
-- 内外部业务
         select multiIf(category = '内部业务', '内领业务', '外部行业')            biz_type,
                multiIf(biz_type = '外部行业', industry_dept, biz_type = '内领业务', product,
                        '(空值)')                                             industry_or_product,
                industry_dept,
                multiIf(customhouse_title = '国内', '境内', customhouse_title = '海外', '境外',
                        customhouse_title = '境内', '境内', customhouse_title = '境外', '境外',
                        '(空值)')                                             region_type,
                region_name,
                zone_name,
                instance_type                                                 gins_family,
                0                                                             uin,
                multiIf(demand_type = 'NEW', '新增', demand_type = 'ELASTIC', '弹性',
                        demand_type = 'RETURN', '退回',
                        '(空值)')                                             `demand_type`,
                'GPU'                                                         data_product,
                formatDateTime(begin_buy_date, '%Y-%m')                       `year_month`,
                '(空值)'                                                      customer_name,
                '(空值)'                                                      customer_short_name,
                '(空值)'                                                      customer_person_type,
                '(空值)'                                                       `customer_type`,
                '(空值)'                                                         customer_group,
                war_zone                                                      war_zone_name,
                multiIf(uin_type = 0, '内部', uin_type = 1, '外部', '(空值)') customer_inside_or_outside,
                sum(if(demand_type = '退回', -total_gpu_num, total_gpu_num))  num
         from std_crp.dwd_crp_ppl_item_version_cf
         where product = 'GPU(裸金属&CVM)'
           and version_code = (select max_v from version_code_t)
           and (source = 'FORECAST')
         group by  biz_type, industry_or_product, industry_dept, region_type, region_name, zone_name,
                   gins_family, uin,
                   `demand_type`, data_product, `year_month`, customer_name, customer_short_name,
                   `customer_type`,
                   customer_group, war_zone_name, customer_person_type, customer_inside_or_outside) t
where t.`year_month` = ?