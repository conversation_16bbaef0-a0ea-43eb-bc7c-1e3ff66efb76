select t.*,
       null region_type,
       null customer_inside_or_outside,
       '报备客户' customer_type,
       null war_zone_name,
       null customer_group,
       null customer_short_name,
       null customer_name,
       null sort_idx,
       null customer_person_type
from (
-- CVM
-- 外部行业
         select null              order_type,
                '外部行业'                                                 biz_type,
                o.industry_dept                                            industry_or_product,
                o.industry_dept                                            industry_dept,
                i.region_name                                              region_name,
                i.zone_name                                                zone_name,
                i.instance_type                                            gins_family,
                o.customer_uin                                             uin,
                case i.demand_type
                    when 'NEW' then '新增'
                    when 'ELASTIC' then '弹性'
                    when 'RETURN' then '退回'
                    else '(空值)' end                                      demand_type,
                'CVM'                                                      data_product,
                date_format(i.begin_buy_date, '%Y-%m')                     `year_month`,
                sum(if(i.demand_type = 'RETURN', -total_core, total_core)) num
         from ppl_item i
                  left join ppl_order o on o.ppl_order = i.ppl_order
                  left join ppl_config_stat_industry_dept_class c
                            on o.industry_dept = c.industry_dept
         where i.deleted = 0
           and o.deleted = 0
           and o.industry_dept <> '中长尾'
           and i.status = 'APPLIED'
           and i.order_category = 'CVM'
           and i.app_role = 'CVM'
           and (o.industry_dept is null or c.category <> '内部业务')
           and i.yunxiao_order_status not in
               ('CANCELED', 'BAD_CANCELED', 'REJECTED', 'CREATED')
         group by order_type, o.industry_dept, i.region_name, i.zone_name, i.instance_type,
                  o.customer_uin,
                  i.demand_type, date_format(i.begin_buy_date, '%Y-%m')
-- 内部业务
         union all
         select null              order_type,
                '内领业务'                                                 biz_type,
                i.product                                                  industry_or_product,
                o.industry_dept                                            industry_dept,
                i.region_name                                              region_name,
                i.zone_name                                                zone_name,
                i.instance_type                                            gins_family,
                o.customer_uin                                             uin,
                case i.demand_type
                    when 'NEW' then '新增'
                    when 'ELASTIC' then '弹性'
                    when 'RETURN' then '退回'
                    else '(空值)' end                                      demand_type,
                'CVM'                                                      data_product,
                date_format(i.begin_buy_date, '%Y-%m')                     `year_month`,
                sum(if(i.demand_type = 'RETURN', -total_core, total_core)) num
         from ppl_item i
                  left join ppl_order o on o.ppl_order = i.ppl_order
                  left join ppl_config_stat_industry_dept_class c
                            on o.industry_dept = c.industry_dept
         where i.deleted = 0
           and o.deleted = 0
           and o.industry_dept <> '中长尾'
           and i.status = 'APPLIED'
           and ((i.order_category = 'CVM'
             and i.app_role = 'CVM'
             and c.category = '内部业务')
             or (i.order_category = 'CVM'
                 and
                 i.app_role in ('EKS', 'EMR', 'ES', 'CDW')))
           and i.yunxiao_order_status not in
               ('CANCELED', 'BAD_CANCELED', 'REJECTED', 'CREATED')
         group by order_type, i.product, o.industry_dept, i.region_name, i.zone_name,
                  i.instance_type,
                  o.customer_uin,
                  i.demand_type, date_format(i.begin_buy_date, '%Y-%m')
-- GPU
-- 外部行业
         union all
         select null                    order_type,
                '外部行业'                                                       biz_type,
                o.industry_dept                                                  industry_or_product,
                o.industry_dept                                                  industry_dept,
                i.region_name                                                    region_name,
                i.zone_name                                                      zone_name,
                i.instance_type                                                  gins_family,
                o.customer_uin                                                   uin,
                case i.demand_type
                    when 'NEW' then '新增'
                    when 'ELASTIC' then '弹性'
                    when 'RETURN' then '退回'
                    else '(空值)' end                                            demand_type,
                'GPU'                                                            data_product,
                date_format(i.begin_buy_date, '%Y-%m')                           `year_month`,
                sum(if(i.demand_type = 'RETURN', -total_gpu_num, total_gpu_num)) num
         from ppl_item i
                  left join ppl_order o
                            on o.ppl_order = i.ppl_order
                  left join ppl_config_stat_industry_dept_class c
                            on o.industry_dept = c.industry_dept
         where i.deleted = 0
           and o.deleted = 0
           and o.industry_dept <> '中长尾'
           and i.status = 'APPLIED'
           and i.order_category = 'GPU'
           and i.app_role = 'CVM'
           and (o.industry_dept is null
             or c.category <> '内部业务')
           and i.yunxiao_order_status not in
               ('CANCELED', 'BAD_CANCELED', 'REJECTED', 'CREATED')
         group by order_type, o.industry_dept, i.region_name, i.zone_name, i.instance_type,
                  o.customer_uin,
                  i.demand_type, date_format(i.begin_buy_date, '%Y-%m')
-- 内部业务
         union all
         select null                    order_type,
                '内领业务'                                                       biz_type,
                i.product                                                        industry_or_product,
                o.industry_dept                                                  industry_dept,
                i.region_name                                                    region_name,
                i.zone_name                                                      zone_name,
                i.instance_type                                                  gins_family,
                o.customer_uin                                                   uin,
                case i.demand_type
                    when 'NEW' then '新增'
                    when 'ELASTIC' then '弹性'
                    when 'RETURN' then '退回'
                    else '(空值)' end                                            demand_type,
                'GPU'                                                            data_product,
                date_format(i.begin_buy_date, '%Y-%m')                           `year_month`,
                sum(if(i.demand_type = 'RETURN', -total_gpu_num, total_gpu_num)) num
         from ppl_item i
                  left join ppl_order o
                            on o.ppl_order = i.ppl_order
                  left join ppl_config_stat_industry_dept_class c
                            on o.industry_dept = c.industry_dept
         where i.deleted = 0
           and o.deleted = 0
           and o.industry_dept <> '中长尾'
           and i.status = 'APPLIED'
           and ((i.order_category = 'GPU'
             and i.app_role = 'CVM'
             and c.category = '内部业务')
             or (i.order_category = 'GPU'
                 and
                 i.app_role in ('EKS', 'EMR', 'ES', 'CDW')))
           and i.yunxiao_order_status not in
               ('CANCELED', 'BAD_CANCELED', 'REJECTED', 'CREATED')
         group by order_type, i.product, o.industry_dept, i.region_name, i.zone_name,
                  i.instance_type,
                  o.customer_uin,
                  i.demand_type, date_format(i.begin_buy_date, '%Y-%m')) t
where t.`year_month` >= ?
  and t.num <> 0