-- 退回
select
    product,
    instance_type as instance_type, -- 机型和卡型外层处理有过滤，不能设为null
    gpu_type,
    region_name,
    zone_name,
    customer_short_name,
    if(${is_excel} or ${has_industry_dept},industry_dept,null) as industry_dept, -- 用原始部门（区别是新的部门会把中长尾统一清洗为中长尾部门）
    year_month,
    null as year_week,
    sum(core_num) as core_num
from (select
          product,
          instance_type as instance_type, -- 机型和卡型外层处理有过滤，不能设为null
          gpu_card_type as gpu_type,
          if(${is_excel} or ${has_region_name} or ${has_area_name} or ${has_country_name} or ${has_customhouse_title},region_name,null) as region_name,
          if(${is_excel} or ${has_zone_name},zone_name,null) as zone_name,
          if(${is_excel} or ${has_customer_short_name},customer_short_name,null) as customer_short_name,
          origin_industry_dept as industry_dept, -- 用原始部门（区别是新的部门会把中长尾统一清洗为中长尾部门）
          if(${is_trend},concat(toString(`year`),'-',if(`month` < 10,concat('0',toString(`month`)),toString(`month`))),null) as year_month,
          null as year_week,
          if(biz_range_type = '外部业务' and not ${inner_industry_customer},
             if(${is_scale},
                if(${is_cvm},sum(if(change_bill_core_from_last_month<0,change_bill_core_from_last_month,toDecimal64(0, 6))),sum(if(change_bill_gpu_from_last_month<0,change_bill_gpu_from_last_month,toDecimal64(0, 6)))),
                if(${is_cvm},sum(if(change_service_core_from_last_month<0,change_service_core_from_last_month,toDecimal64(0, 6))),sum(if(change_service_gpu_from_last_month<0,change_service_gpu_from_last_month,toDecimal64(0, 6)))))
              ,
             if(${is_cvm},sum(if(change_service_core_from_last_month<0,change_service_core_from_last_month,toDecimal64(0, 6))),sum(if(change_service_gpu_from_last_month<0,change_service_gpu_from_last_month,toDecimal64(0, 6))))) as core_num
      from std_crp.dwd_txy_scale_df_view -- 日规模视图，底层group by过一次（即去重）
      where product = '${product_type}'
        and stat_time in ('${demand_month_end_day_range}') -- 月最后一天集合
        and instance_type not like '%RM%'
        and instance_type not like '%RS%'
        and app_role != 'LH'
      group by
          product,
          region_name,
          zone_name,
          instance_type,
          gpu_type,
          year_month,
          year_week,
          biz_range_type,
          customer_short_name,
          industry_dept)
group by
    product,
    region_name,
    zone_name,
    instance_type,
    gpu_type,
    year_month,
    year_week,
    customer_short_name,
    industry_dept
having core_num != 0