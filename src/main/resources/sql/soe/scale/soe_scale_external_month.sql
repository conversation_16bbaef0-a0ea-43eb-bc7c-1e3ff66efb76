-- 外部业务 按月
select
    product,
    instance_type,
    gpu_card_type as gpu_type,
    region_name,
    zone_name,
    if(${is_trend},concat(toString(`year`),'-',if(`month` < 10,concat('0',toString(`month`)),toString(`month`))),null) as year_month,
    null as year_week,
    if(${is_scale},
       if(${is_cvm},sum(change_bill_core_from_last_month),sum(change_bill_gpu_from_last_month)),
       if(${is_cvm},sum(change_service_core_from_last_month),sum(change_service_gpu_from_last_month))) as core_num
from std_crp.dwd_txy_scale_df
where product = '${product_type}'
  and stat_time in ('${month_end_day_range}') -- 月最后一天集合
  and biz_range_type = '外部业务'
  and instance_type not like '%RM%'
  and instance_type not like '%RS%'
  and app_role != 'LH'
group by
    product,
    region_name,
    zone_name,
    instance_type,
    gpu_type,
    year_month,
    year_week
having core_num != 0