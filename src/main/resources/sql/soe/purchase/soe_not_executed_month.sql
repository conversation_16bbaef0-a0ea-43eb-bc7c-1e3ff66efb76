-- 进-采购@待执行
select
    '${product_type}' as product_type,
    if(${is_trend},p_ym,null) as `year_month`,
    null as `year_week`,
    dim_instance_type ,
    dim_device_type,
    multiIf(dim_region_class = '国内','境内',dim_region_class = '海外','境外','(空值)') as dim_region_class,
    dim_country,
    null as dim_region ,
    sum(num) as device_num,
    if(${is_cvm},sum(cores),null) as core_num
from cloud_demand.dws_tcres_report_demand_product_annual_lave_v2_df
where version = '${int_stat_time}' -- 20240304
  and dim_product_class = '${product_type}'
  and p_ym in ('${year_month_range}')
  and cores != 0
group by
    product_type,
    `year_month`,
    `year_week`,
    dim_device_type,
    dim_instance_type ,
    dim_region_class ,
    dim_country,
    dim_region