-- 进-采购@待执行
select
    '${product_type}' as product_type,
    if(${is_trend},p_ym,null) as `year_month`,
    null as `year_week`,
    dim_device_type,
    dim_instance_type ,
    multiIf(dim_region_class = '国内','境内',dim_region_class = '海外','境外','(空值)') as dim_region_class,
    dim_region ,
    sum(num) as device_num,
    if(${is_cvm},sum(cores),null) as core_num
from cloud_demand.ads_tcres_demand_annual_excueted_report
where p_index = '未执行预测'
  and version = '${int_stat_time}' -- 20240304
  and dim_product_class = '${product_type}'
  and cores != 0
group by
    product_type,
    `year_month`,
    `year_week`,
    dim_device_type,
    dim_instance_type ,
    dim_region_class ,
    dim_region