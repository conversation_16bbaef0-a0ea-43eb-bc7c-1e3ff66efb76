-- 进-采购@采购到货 按月GPU
select
    '${product_type}' product_type ,
    null as customhouse_title ,
    null as area_name ,
    null as region_name ,
    null as zone_name ,
    quota_campus_name as campus,
    null as instance_type ,
    device_type,
    gpu_abbr as gpu_type,
    '否' as `is_no_expect`, -- 是否无期货
    null as quota_id,
    if(${has_industry_dept},xy_industry,null) as industry_dept,
    if(${has_customer_short_name},xy_customer_name,null) as customer_name,
    substr(sla_date_expect,1,7) as `year_month`,
    null as `year_week`,
    sum(gpu_number) as core_num
from cubes.demandMarket
where `DAY` = '${stat_time_add_1}' -- 2024-03-13
  and cloud_business_type = '云业务'
  and quota_plan_product_name in ('GPU云服务器CGS','裸金属GPU云服务器')
  and ((sla_date_expect >= '2023-01-01'
    and sla_date_expect <= '${end_date}'))
  and gpu_number != 0
  and produce_status not in ('需求单已作废','完成')
group by
    product_type,
    customhouse_title,
    area_name,
    region_name,
    zone_name,
    campus,
    device_type,
    instance_type,
    quota_id,
    gpu_abbr,
    industry_dept,
    customer_name,
    `is_no_expect`,
    `year_month`,
    `year_week`