-- 进-采购@采购到货 按月CVM
select
    '${product_type}' product_type ,
    null as customhouse_title ,
    null as area_name ,
    null as region_name ,
    device_type,
    null as zone_name,
    quota_campus_name as campus,
    null as instance_type ,
    null as gpu_type,
    '否' as `is_no_expect`, -- 是否无货期
    if(${has_industry_dept},xy_industry,null) as industry_dept,
    if(${has_customer_short_name},xy_customer_name,null) as customer_name,
    null as quota_id,
    substr(sla_date_expect,1,7) as `year_month`,
    null as `year_week`,
    sum(cpu_logic_core) as core_num
from cubes.demandMarket
where `DAY` = '${stat_time_add_1}' -- 2024-03-13
  and cloud_business_type = '云业务'
  and quota_plan_product_name in ('腾讯云CVM')
  and ((sla_date_expect >= '2023-01-01'
    and sla_date_expect <= '${end_date}'))
  and cpu_logic_core != 0
  and produce_status not in ('需求单已作废','完成')
group by
    product_type,
    customhouse_title,
    area_name,
    region_name,
    zone_name,
    quota_id,
    campus,
    industry_dept,
    customer_name,
    device_type,
    instance_type,
    `is_no_expect`,
    `year_month`,
    `year_week`