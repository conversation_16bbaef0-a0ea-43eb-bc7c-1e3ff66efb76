-- 进-采购@采购到货 按月CVM
select
    '${product_type}' product_type ,
    null as customhouse_title ,
    null as area_name ,
    null as region_name ,
    device_type,
    null as zone_name,
    quota_campus_name as campus,
    null as instance_type ,
    null as gpu_type,
    if(${has_industry_dept},xy_industry,null) as industry_dept,
    if(${has_customer_short_name},xy_customer_name,null) as customer_name,
    '是' as `is_no_expect`, -- 是否无货期
    quota_id,
    substr(quota_use_time,1,7) as `year_month`, -- 无货期用期望到货时间
    null as `year_week`,
    sum(cpu_logic_core) as core_num
from cubes.demandMarket
where `DAY` = '${stat_time_add_1}' -- 2024-03-13
  and cloud_business_type = '云业务'
  and quota_plan_product_name in ('腾讯云CVM')
  and (sla_date_expect <= '1900-01-01')
  and quota_use_time <= '${end_date}' -- 不统计超过结束时间的
  and cpu_logic_core != 0
  and produce_status not in ('需求单已作废','完成')
group by
    product_type,
    customhouse_title,
    area_name,
    region_name,
    zone_name,
    campus,
    quota_id,
    device_type,
    instance_type,
    industry_dept,
    customer_name,
    `is_no_expect`,
    `year_month`,
    `year_week`