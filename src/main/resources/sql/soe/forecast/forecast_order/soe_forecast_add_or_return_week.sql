-- 销-预测净增 按周
select
    '${product_type}' as product_type,
    instance_type,
    instance_model,
    region_name,
    zone_name,
    if(${is_cvm},-1,win_rate) as win_rate,
    gpu_type,
    if(${has_customer_short_name},customer_short_name,null) as customer_short_name ,
    if(${has_industry_dept},industry_dept,null) as industry_dept ,
    if(${is_cvm},sum(abs(total_core)),sum(abs(total_gpu_num))) as core_num,
    null as `year_month`,
    toString(toYearWeek(begin_buy_date,7)) as `year_week`
from std_crp.ads_mck_forecast_summary_df
where
  stat_time = '${demand_stat_time}' -- 2024-02-07
  and ((toString(toYearWeek(begin_buy_date,7)) <= '${year_week_4}' and compare = 'MAX' and demand_source in ('大客户','预约单'))
    or (toString(toYearWeek(begin_buy_date,7)) > '${year_week_4}' and demand_source = '大客户')
    or demand_source = '中长尾') -- 四周内取max，外取大客户，中长尾全取
  and begin_buy_date >= '${start_date}'
  and begin_buy_date <= '${end_date}'
  and demand_type in ('${demand_type}') -- NEW or RETURN
  and product in ('${product_class}')
  and ((demand_source = '预约单' and end_buy_date >= stat_time and yunxiao_order_status != 'FINISHED') or demand_source != '预约单') -- 剔除已经完成和已经过期的预约单
group by
    instance_type,
    instance_model,
    region_name,
    zone_name,
    gpu_type,
    win_rate,
    `year_month`,
    `year_week`
having core_num != 0