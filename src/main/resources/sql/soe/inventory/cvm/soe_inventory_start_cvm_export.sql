-- 存-期初库存（好料）
select
    product_type ,
    customhouse_title ,
    area_name ,
    region_name ,
    zone_name ,
    device_type,
    instance_type ,
    line_type,
    material_type,
    inv_detail_type,
    formatDateTime(stat_time, '%Y-%m') as `year_month`,
    null as `year_week`,
    sum(cores) as core_num
from cloud_demand.dws_inventory_health_supply_summary_df
where stat_time = '${stat_time}' -- 2024-03-05
  and supply_type = '库存'
    ${line_type}
    ${material_type}
    ${inv_detail_type}
  and product_type = '${product_type}'
group by
    product_type,
    customhouse_title,
    area_name,
    region_name,
    zone_name,
    device_type,
    instance_type,
    line_type,
    material_type,
    inv_detail_type,
    `year_month`,
    `year_week`
having core_num != 0