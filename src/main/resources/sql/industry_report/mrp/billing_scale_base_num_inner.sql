select '内部业务'                                 type,
       toYear(stat_time)                          year,
       toMonth(stat_time)                         month,
       '${PRODUCT}'                               industryOrDept,
       region_name                                regionName,
       gins_family                                instanceType,
       -- RULE_NOT_DISTINCT
       sum(if(${diff_billcpu} > 0, ${diff_billcpu}, 0)) increaseBillCoreNum,
       sum(if(${diff_freecpu} > 0, ${diff_freecpu}, 0)) increaseServeCoreNum,
       sum(if(${diff_billcpu} < 0, ${diff_billcpu}, 0)) returnBillCoreNum,
       sum(if(${diff_freecpu} < 0, ${diff_freecpu}, 0)) returnServeCoreNum,

       sum(if(${diff_billgpu} > 0, ${diff_billgpu}, 0)) increaseBillGpuNum,
       sum(if(${diff_freegpu} > 0, ${diff_freegpu}, 0)) increaseServeGpuNum,
       sum(if(${diff_billgpu} < 0, ${diff_billgpu}, 0)) returnBillGpuNum,
       sum(if(${diff_freegpu} < 0, ${diff_freegpu}, 0)) returnServeGpuNum,
       -- RULE_NOT_DISTINCT
       -- RULE_DISTINCT
       if(sum(${diff_billcpu}) > 0, sum(${diff_billcpu}), 0) increaseBillCoreNum,
       if(sum(${diff_freecpu}) > 0, sum(${diff_freecpu}), 0) increaseServeCoreNum,
       if(sum(${diff_billcpu}) < 0, sum(${diff_billcpu}), 0) returnBillCoreNum,
       if(sum(${diff_freecpu}) < 0, sum(${diff_freecpu}), 0) returnServeCoreNum,

       if(sum(${diff_billgpu}) > 0, sum(${diff_billgpu}), 0) increaseBillGpuNum,
       if(sum(${diff_freegpu}) > 0, sum(${diff_freegpu}), 0) increaseServeGpuNum,
       if(sum(${diff_billgpu}) < 0, sum(${diff_billgpu}), 0) returnBillGpuNum,
       if(sum(${diff_freegpu}) < 0, sum(${diff_freegpu}), 0) returnServeGpuNum,
       -- RULE_DISTINCT
       sum(cur_billcpu) stockBillCoreNum,
       sum(cur_freecpu) stockServeCoreNum,

       sum(cur_billgpu) stockBillGpuNum,
       sum(cur_freegpu) stockServeGpuNum
from ${tableName}
where biz_type = ${BIZ_TYPE}
  and cpu_or_gpu = '${CPU_OR_GPU}'
  and app_role in ('CNAS','CSS','EKS','EMR','NON_CCDB_OTHER','USE_FOR_30','VPCGW','VPNGW','YUNTI', '官网领用', '算力')
  and app_role not in ('正常售卖', 'CDH', '预扣包', 'LH')
  and customer_uin ${NOT} in (${INNER_UIN})
    ${FILTER}
group by year, month, industryOrDept, regionName, instanceType