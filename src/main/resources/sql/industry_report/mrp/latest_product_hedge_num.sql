-- CVM-内部业务
select '内部业务'                              type,
       b.industry_dept                         industryOrDept,
       req.region_name                         regionName,
       req.instance_type                       instanceType,
       case req.demand_type when 'NEW' then '新增' when 'ELASTIC' then '弹性' when 'RETURN' then '退回' else '未知' end demandType,
       year(a.begin_buy_date)                                  year,
       month(a.begin_buy_date)                                 month,
       sum(ifnull(rsp.instance_total_core, 0)) coreNum,
       0 gpuNum
from ppl_stock_supply_rsp rsp
         join ppl_stock_supply_req req on req.id = rsp.req_id
         join ppl_stock_supply sup on req.supply_id = sup.id
         join ppl_item a on a.ppl_id = req.ppl_id
         join ppl_order b on b.ppl_order = a.ppl_order
         left join ppl_config_stat_industry_dept_class c on b.industry_dept = c.industry_dept
    ${JOIN}
where rsp.deleted = 0
  and req.deleted = 0
  and sup.deleted = 0
  and b.deleted = 0
  and a.deleted = 0
  and req.type = 'CVM'
  and a.product = 'CVM&CBS'
  and c.category = '内部业务' ${FILTER}
group by b.industry_dept, req.region_name, req.instance_type, year(a.begin_buy_date), month(a.begin_buy_date), req.demand_type
-- PASS产品全部都算内部
union all
select '内部业务'                              type,
       a.product                               industryOrDept,
       req.region_name                         regionName,
       req.instance_type                       instanceType,
       case req.demand_type when 'NEW' then '新增' when 'ELASTIC' then '弹性' when 'RETURN' then '退回' else '未知' end demandType,
       year(a.begin_buy_date)                                  year,
       month(a.begin_buy_date)                                 month,
       sum(ifnull(rsp.instance_total_core, 0)) coreNum,
       0 gpuNum
from ppl_stock_supply_rsp rsp
         join ppl_stock_supply_req req on req.id = rsp.req_id
         join ppl_stock_supply sup on req.supply_id = sup.id
         join ppl_item a on a.ppl_id = req.ppl_id
         join ppl_order b on b.ppl_order = a.ppl_order
         left join ppl_config_stat_industry_dept_class c on b.industry_dept = c.industry_dept
    ${JOIN}
where rsp.deleted = 0
  and req.deleted = 0
  and sup.deleted = 0
  and b.deleted = 0
  and a.deleted = 0
  and req.type = 'CVM'
  and a.product in ('弹性MapReduce'
    , 'Elasticsearch Service'
    , '云数据仓库'
    , 'EKS官网', '数据湖DLC', 'CSIG容器平台') ${FILTER}
group by a.product, req.region_name, req.instance_type, year(a.begin_buy_date), month(a.begin_buy_date), req.demand_type
union all
-- CVM-外部业务
select '外部行业'                              type,
       b.industry_dept                         industryOrDept,
       req.region_name                         regionName,
       req.instance_type                       instanceType,
       case req.demand_type when 'NEW' then '新增' when 'ELASTIC' then '弹性' when 'RETURN' then '退回' else '未知' end demandType,
       year(a.begin_buy_date)                                  year,
       month(a.begin_buy_date)                                 month,
       sum(ifnull(rsp.instance_total_core, 0)) coreNum,
       0 gpuNum
from ppl_stock_supply_rsp rsp
         join ppl_stock_supply_req req on req.id = rsp.req_id
         join ppl_stock_supply sup on req.supply_id = sup.id
         join ppl_item a on a.ppl_id = req.ppl_id
         join ppl_order b on b.ppl_order = a.ppl_order
         left join ppl_config_stat_industry_dept_class c on b.industry_dept = c.industry_dept
    ${JOIN}
where rsp.deleted = 0
  and req.deleted = 0
  and sup.deleted = 0
  and b.deleted = 0
  and a.deleted = 0
  and req.type = 'CVM'
  and a.product = 'CVM&CBS'
  and (b.industry_dept is null
   or c.category <> '内部业务') ${FILTER}
group by b.industry_dept, req.region_name, req.instance_type, year(a.begin_buy_date), month(a.begin_buy_date), req.demand_type