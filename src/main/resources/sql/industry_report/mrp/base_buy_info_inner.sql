select '内部业务'            type,
       biz_type              bizType,
       buy_year              year,
       buy_month             month,
       '${PRODUCT}'          industryOrDept,
       region_name           regionName,
       gins_family           instanceType,
       sum(success_req_num)  successReqNum,
       sum(fail_req_num)     failReqNum,
       sum(success_core_num) successCoreNum,
       sum(fail_core_num)    failCoreNum,
       sum(success_gpu_num)  successGpuNum,
       sum(fail_gpu_num)     failGpuNum
from daily_tencent_cloud_buy_info
where uin in (${INNER_UIN})
    ${FILTER}
group by bizType, year, month, industryOrDept, regionName, instanceType