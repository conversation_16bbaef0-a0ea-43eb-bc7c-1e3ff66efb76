select tt.type,
       tt.industryOrDept,
       tt.regionName,
       tt.instanceType,
       tt.demandType,
       tt.year,
       tt.month,
       avg(coreNum) coreNum,
       avg(gpuNum) gpuNum
from (select '内部业务'                                                 type,
             g.industry_dept                                            industryOrDept,
             a.region_name                                              regionName,
             a.instance_type                                            instanceType,
             case a.demand_type when 'NEW' then '新增' when 'ELASTIC' then '弹性' when 'RETURN' then '退回' else '未知' end demandType,
             year(a.begin_buy_date)                                                     year,
             month(a.begin_buy_date)                                                    month,
             sum(if(a.demand_type = 'RETURN', -total_core, total_core)) coreNum,
             ifnull(sum(if(a.demand_type = 'RETURN', -total_gpu_num, total_gpu_num)), 0) gpuNum
      from (select max(record_version) record_version, version_group_id
            from ppl_version_group_record
            where deleted = 0
            group by version_group_id) t
               join ppl_version_group_record_item a on t.record_version = a.record_version
               join ppl_version_group g on g.id = t.version_group_id
               join ppl_order b on b.ppl_order = a.ppl_order
               left join ppl_config_stat_industry_dept_class c on b.industry_dept = c.industry_dept
          ${JOIN}
      where a.deleted = 0
        and g.deleted = 0
        and b.deleted = 0
        and a.product = '${PRODUCT}'
        and c.category = '内部业务'
        and a.ppl_order like 'PN%' -- 行业录入的预测单
        and g.status <> 'REJECT' ${FILTER}
      group by g.industry_dept, a.region_name, a.instance_type, year(a.begin_buy_date), month(a.begin_buy_date), g.version_code, a.demand_type) tt
group by tt.type, tt.industryOrDept, tt.regionName, tt.instanceType, tt.year, tt.month, tt.demandType
union all
select tt.type,
       tt.industryOrDept,
       tt.regionName,
       tt.instanceType,
       tt.demandType,
       tt.year,
       tt.month,
       avg(coreNum) coreNum,
       avg(gpuNum)  gpuNum
from (select '内部业务'                                                 type,
             a.product                                                  industryOrDept,
             a.region_name                                              regionName,
             a.instance_type                                            instanceType,
             case a.demand_type when 'NEW' then '新增' when 'ELASTIC' then '弹性' when 'RETURN' then '退回' else '未知' end demandType,
             year(a.begin_buy_date)                                                     year,
             month(a.begin_buy_date)                                                    month,
             sum(if(a.demand_type = 'RETURN', -total_core, total_core)) coreNum,
             ifnull(sum(if(a.demand_type = 'RETURN', -total_gpu_num, total_gpu_num)), 0) gpuNum
      from (select max(record_version) record_version, version_group_id
            from ppl_version_group_record
            where deleted = 0
            group by version_group_id) t
               join ppl_version_group_record_item a on t.record_version = a.record_version
               join ppl_version_group g on g.id = t.version_group_id
               join ppl_order b on b.ppl_order = a.ppl_order
          ${JOIN}
      where a.deleted = 0
        and g.deleted = 0
        and b.deleted = 0
        and a.product in ('弹性MapReduce'
          , 'Elasticsearch Service'
          , '云数据仓库'
          , 'EKS官网', '数据湖DLC', 'CSIG容器平台')
        and 'CVM&CBS' = '${PRODUCT}'
        and a.ppl_order like 'PN%' -- 行业录入的预测单
        and g.status <> 'REJECT' ${FILTER}
      group by a.product, a.region_name, a.instance_type, year(a.begin_buy_date), month(a.begin_buy_date), g.version_code, a.demand_type) tt
group by tt.type, tt.industryOrDept, tt.regionName, tt.instanceType, tt.year, tt.month, tt.demandType
union all
select tt.type,
       tt.industryOrDept,
       tt.regionName,
       tt.instanceType,
       tt.demandType,
       tt.year,
       tt.month,
       avg(coreNum)  coreNum,
       avg(gpuNum)   gpuNum
from (select '外部行业'                                                 type,
             g.industry_dept                                            industryOrDept,
             a.region_name                                              regionName,
             a.instance_type                                            instanceType,
             case a.demand_type when 'NEW' then '新增' when 'ELASTIC' then '弹性' when 'RETURN' then '退回' else '未知' end demandType,
             year(a.begin_buy_date)                                                     year,
             month(a.begin_buy_date)                                                    month,
             sum(if(a.demand_type = 'RETURN', -total_core, total_core)) coreNum,
             ifnull(sum(if(a.demand_type = 'RETURN', -total_gpu_num, total_gpu_num)), 0) gpuNum
      from (select max(record_version) record_version, version_group_id
            from ppl_version_group_record
            where deleted = 0
            group by version_group_id) t
               join ppl_version_group_record_item a on t.record_version = a.record_version
               join ppl_version_group g on g.id = t.version_group_id
               join ppl_order b on b.ppl_order = a.ppl_order
               left join ppl_config_stat_industry_dept_class c on b.industry_dept = c.industry_dept
          ${JOIN}
      where a.deleted = 0
        and g.deleted = 0
        and b.deleted = 0
        and a.product = '${PRODUCT}'
        and (b.industry_dept is null
         or c.category <> '内部业务')
        and a.ppl_order like 'PN%' -- 行业录入的预测单
        and g.status <> 'REJECT' ${FILTER}
      group by g.industry_dept, a.region_name, a.instance_type, year(a.begin_buy_date), month(a.begin_buy_date), g.version_code, a.demand_type) tt
group by tt.type, tt.industryOrDept, tt.regionName, tt.instanceType, tt.year, tt.month, tt.demandType