select ppl_order,
       ppl_id,
       yunxiao_order_status,
       year,
       month,
       product,
       bill_type,
       demand_type,
       demand_scene,
       begin_buy_date,
       end_buy_date,
       begin_elastic_time,
       end_elastic_time,
       note,
       region_name,
       zone_name,
       instance_type,
       instance_model,
       customer_short_name,
       customer_name,
       apply_user,
       customer_uin,
       war_zone,
       customer_source,
       total_core,
       total_disk,
       system_disk_type,
       system_disk_storage,
       system_disk_num,
       data_disk_type,
       data_disk_storage,
       data_disk_num,
       instance_num,
       (CASE WHEN `category` IS NULL THEN '未分类' ELSE `category` END) category,
       -- b.industry_dept,
       industry_dept,
       yunxiao_app_role,
       yunxiao_order_category,
       total_gpu_num,

    yunxiao_order_id,
    apply_submit_user,
    apply_architect,
    apply_reason_type,
    apply_industry_type,
    yunxiao_order_create_time,
    apply_appId,
    apply_reason,
    apply_order_type
from -- ppl_item a
     --    join ppl_order b on a.ppl_order = b.ppl_order
     dwd_crp_ppl_yunxiao_apply_item_cf

where (category <> '内部业务' or category is null or category = '(空值)')
  -- 额外的条件
    ${FILTER}


UNION ALL

select ppl_order,
       ppl_id,
       yunxiao_order_status,
       year,
       month,
       product,
       bill_type,
       demand_type,
       demand_scene,
       begin_buy_date,
       end_buy_date,
       begin_elastic_time,
       end_elastic_time,
       note,
       region_name,
       zone_name,
       instance_type,
       instance_model,
       -- b.customer_short_name customer_short_name,
       customer_short_name,
       -- b.customer_name customer_name,
       customer_name,
       apply_user,
       -- b.customer_uin customer_uin,
       customer_uin,
       -- b.war_zone war_zone,
       war_zone,
       customer_source,
       total_core,
       total_disk,
       system_disk_type,
       system_disk_storage,
       system_disk_num,
       data_disk_type,
       data_disk_storage,
       data_disk_num,
       instance_num,
       (CASE WHEN `category` IS NULL THEN '未分类' ELSE `category` END)     category,
       -- b.industry_dept,
       industry_dept,
       yunxiao_app_role,
       yunxiao_order_category,
       total_gpu_num,

    yunxiao_order_id,
    apply_submit_user,
    apply_architect,
    apply_reason_type,
    apply_industry_type,
    yunxiao_order_create_time,
    apply_appId,
    apply_reason,
    apply_order_type
from  dwd_crp_ppl_yunxiao_apply_item_cf
where
  ((yunxiao_order_category = 'CVM' and yunxiao_app_role = 'CVM' and category = '内部业务') or
    (yunxiao_order_category = 'CVM' and yunxiao_app_role in ('EKS', 'EMR', 'ES', 'CDW')))
    ${PRODUCT}
    -- 额外的条件
    ${INNER_FILTER}
    ${INNER_AUTH_FILTER}




