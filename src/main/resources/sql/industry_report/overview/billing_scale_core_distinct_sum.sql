select stat_time, industry_category, region_name, gins_family,
       if(sum(diff_billcpu) > 0, sum(diff_billcpu), 0) total_core_new,
       if(sum(diff_billgpu) > 0, sum(diff_billgpu), 0) total_gpu_num_new,
       if(sum(diff_billcpu) < 0, sum(diff_billcpu), 0) total_core_return,
       if(sum(diff_billgpu) < 0, sum(diff_billgpu), 0) total_gpu_num_return
from ppl_billing_scale_monthly
where stat_time in (:statTime)
  and app_role in ('正常售卖', 'CDH', '预扣包', 'GOCP', 'CDZ')
    ${FILTER}
    ${AUTH_FILTER}
group by industry_category, region_name, gins_family, stat_time

union all

select stat_time, '内部业务' industry_category, region_name, gins_family,
       if(sum(diff_freecpu) > 0, sum(diff_freecpu), 0) total_core_new,
       0 total_gpu_num_new,
       if(sum(diff_freecpu) < 0, sum(diff_freecpu), 0) total_core_return,
       0 total_gpu_num_return
from ppl_billing_scale_monthly
where stat_time in (:statTime)
  and app_role not in ('正常售卖', 'CDH', '预扣包', 'GOCP', 'CDZ', 'LH')
    ${INNER_FILTER}
    ${INNER_AUTH_FILTER}
    ${PRODUCT}
group by industry_category, region_name, gins_family, stat_time
