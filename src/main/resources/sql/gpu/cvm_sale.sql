-- GPU产品 销售CVM部分
SELECT a.zoneid                                                           `zoneid`,
       a.cvmtype                                                          `cvmtype`,
       SUM(a.billcpu)                                                     `CVM计费规模`,
       SUM(a.billcpu_lh)                                                  `Lighthouse规模`,
       SUM(a.freecpu_csig)                                                `freecpu_csig`,
       SUM(a.freecpu_other)                                               `freecpu_other`,
       ifnull(SUM(a.billcpu * 100 / b.cpu * b.gpu / b.gpuratio), 0)       `cvm_gpu`,
       ifnull(SUM(a.billcpu_lh * 100 / b.cpu * b.gpu / b.gpuratio), 0)    `lh_gpu`,
       ifnull(SUM(a.freecpu_csig * 100 / b.cpu * b.gpu / b.gpuratio), 0)  `gpu_csig`,
       ifnull(SUM(a.freecpu_other * 100 / b.cpu * b.gpu / b.gpuratio), 0) `gpu_other`
FROM daily_zone_cvmtype_ginstype_p2p a
         LEFT JOIN `static_ginstype` b
                   ON a.ginstype = b.ginstype
WHERE a.stattime = ?
  AND a.ginstype in (select ginstype from static_ginstype where biztype = 'cvm' and gpu > 0)
