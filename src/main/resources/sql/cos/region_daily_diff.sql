-- 统计statTime与前一天的上架物理量和业务量的差异
SELECT region_name,indicator_name,
       SUM(CASE WHEN stat_time=:statTime THEN logic_num ELSE -logic_num END) AS diff
FROM `report_plan_detail`
WHERE product_type = 'COS'
  AND stat_time IN (DATE_SUB(DATE(:statTime), INTERVAL 1 DAY),:statTime)
  AND customhouse_title IN ('境内','境外')
  AND indicator_name IN ('已上架物理量','已上架业务量')
GROUP BY region_name,indicator_name
HAVING COUNT(*) = 2 -- 如果只有一天的数据，那么不纳入，属于异常数据