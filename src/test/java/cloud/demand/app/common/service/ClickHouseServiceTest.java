package cloud.demand.app.common.service;

import cloud.demand.app.modules.sop.util.SpanUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@Slf4j
class ClickHouseServiceTest {

    @Resource
    ClickHouseService clickHouseService;

    @Test
    void dropPartition() throws SQLException {
//        clickHouseService.dropPartition("cloud_demand","report_erp_server_detail_local","2023-09-30");
    }
}