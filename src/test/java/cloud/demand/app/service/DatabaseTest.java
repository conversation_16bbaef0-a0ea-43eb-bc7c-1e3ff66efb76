package cloud.demand.app.service;

import com.pugwoo.dbhelper.DBHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
@Slf4j
public class DatabaseTest {

    @Resource
    private DBHelper shuttleDBHelper;
    @Resource
    private DBHelper matrixDBHelper;

    @Test
    public void test() {
        System.out.println(shuttleDBHelper);
        System.out.println(shuttleDBHelper.getRawOne(Long.class, "select count(*) from api_access"));
        System.out.println(matrixDBHelper);
        System.out.println(matrixDBHelper.getRawOne(Long.class, "select count(*) from act_turn_preferential_devices"));
    }

}
