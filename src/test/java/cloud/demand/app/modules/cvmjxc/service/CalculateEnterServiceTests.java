package cloud.demand.app.modules.cvmjxc.service;

import cloud.demand.app.entity.shuttle.DeviceApplyDO;
import cloud.demand.app.modules.cvmjxc.service.others.CalculateEnterService;
import cloud.demand.app.modules.cvmjxc.service.others.RrpBaseInfoService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
public class CalculateEnterServiceTests {

    @Resource
    private DBHelper shuttleDBHelper;
    @Resource
    private RrpBaseInfoService rrpBaseInfoService;
    @Resource
    private CalculateEnterService calculateEnterService;

    @Test
    public void test() {
        // 采购提货-新增采购

        WhereSQL whereSQL = new WhereSQL();
        // 页面的默认查询条件
        whereSQL.and("(order_type in (1, 100) or (order_type=0 and status in (0,1,3,4,5,6)))");
        // 规划产品 = 腾讯云CVM
        whereSQL.and("product=?", "腾讯云CVM");
        // 项目类型=非自研上云
        whereSQL.and(rrpBaseInfoService.generateNotZysySQLCondition(rrpBaseInfoService.getPlanProductToBusiness()));
        // 全量采购单-Q单号，即：调拨方式=采购
        whereSQL.and("order_type IN ('1')");

        List<DeviceApplyDO> all = shuttleDBHelper.getAll(DeviceApplyDO.class, whereSQL.getSQL(), whereSQL.getParams());
        System.out.println(all);
    }

    @Test
    public void testEnter() {
        String statTime = "2022-09-26";
        calculateEnterService.getAndSaveEnterData(statTime);
        calculateEnterService.calAndSaveEnterSummaryData(statTime);
    }

}
