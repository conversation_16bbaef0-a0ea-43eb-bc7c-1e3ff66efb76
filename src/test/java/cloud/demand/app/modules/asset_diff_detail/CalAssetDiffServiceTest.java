package cloud.demand.app.modules.asset_diff_detail;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Lang;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.StopWatch;

@SpringBootTest
@Slf4j
public class CalAssetDiffServiceTest {

    @Resource
    CalAssetDiffService calAssetDiffService;
    @Resource
    private HasOrderNoDiffService hasOrderNoDiffService;

    @Test
    public void test() {
        String statTime = "2022-11-30";
        calAssetDiffService.calAndSaveAssetDiffData(statTime);

    }


    ThreadPoolExecutor threadPool =
            new ThreadPoolExecutor(2, 3, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(),
                    r -> new Thread(r, "CalAssetDiffService-" + r.hashCode()));

    @Test
    public void test2() {
        ArrayList<Integer> list = Lang.list(1, 2, 3, 4, 5);
        threadPool.execute(() -> {
            for (Integer integer : list) {
                System.out.println(integer);
            }
        });
    }


    @Test
    public void test3() {
        List<String> uuids = Lang.list();
        for (int i = 0; i < 200000; i++) {
            uuids.add(UUID.randomUUID().toString());
        }

        Set<String> set = new HashSet<>();
        //  统计耗时情况
        StopWatch stopWatch = new StopWatch("统计耗时");
        stopWatch.start("开始");
        for (String uuid : uuids) {
            set.add(uuid);
        }
        stopWatch.stop();
        System.out.println(stopWatch.prettyPrint());
    }


    @Test
    public void test4() {
        System.out.println(calAssetDiffService.getAllDiffData("2022-09-16", "CVM"));
    }

    @Test
    public void test5() {
        hasOrderNoDiffService.manualGenDiff("2022-07-05");
    }

}
