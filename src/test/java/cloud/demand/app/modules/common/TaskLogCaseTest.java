package cloud.demand.app.modules.common;

import cloud.demand.app.common.utils.SpringUtil;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;

@SpringBootTest
public class TaskLogCaseTest {

    @Test
    public void test(){
        ApplicationContext applicationContext = SpringUtil.getApplicationContext();
        Integer integer = applicationContext.getBean(TaskLogCase.class).testTaskLog();
        System.out.println(integer);
    }

}
