package cloud.demand.app.modules.sop;

import cloud.demand.app.modules.soe.model.dict.YearWeek;
import cloud.demand.app.modules.sop.domain.YuntiPageReq;
import cloud.demand.app.modules.sop.domain.YuntiVersionReq;
import cloud.demand.app.modules.sop.domain.http.YuntiCvmDemandHedgingDetailResList;
import cloud.demand.app.modules.sop.domain.http.YuntiCvmDemandVersionCodeRes;
import cloud.demand.app.modules.sop.service.other.impl.SopAllYearDemandServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
@Slf4j
public class SopAllYearTest {

    @Resource
    private SopAllYearDemandServiceImpl service;

    @Test
    public void test(){
        List<YearWeek> yearWeeks = service.checkAllYearDemand("2021-W09", null);
        for (YearWeek yearWeek : yearWeeks) {
            System.out.println(yearWeek);
        }
    }
}
