package cloud.demand.app.modules.soe;

import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
@Slf4j
public class SoeCommonUtilsTest {
    @Test
    public void test(){
        Integer diffYearWeek = SoeCommonUtils.getDiffYearWeek("2024-W53", "2025-W01");
        System.out.println("getDiffYearWeek：" + diffYearWeek);
        String intYearWeek = SoeCommonUtils.getIntYearWeek(LocalDate.parse("2024-12-31"));
        System.out.println("getIntYearWeek：" + intYearWeek);
        String yearWeek = SoeCommonUtils.getYearWeek(LocalDate.parse("2024-12-31"));
        System.out.println("getYearWeek：" + yearWeek);
        LocalDate dateByYearWeek = SoeCommonUtils.getDateByYearWeek("2025-W01");
        System.out.println("getDateByYearWeek：" + dateByYearWeek);
    }
}
