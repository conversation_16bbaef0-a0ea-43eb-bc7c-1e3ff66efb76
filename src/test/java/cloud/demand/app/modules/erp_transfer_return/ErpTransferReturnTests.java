package cloud.demand.app.modules.erp_transfer_return;

import cloud.demand.app.modules.erp_transfer_return.service.ErpBaseService;
import cloud.demand.app.modules.erp_transfer_return.service.ErpDetailService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class ErpTransferReturnTests {

    @Resource
    private ErpBaseService erpBaseService;
    @Resource
    private ErpDetailService erpDetailService;

    @Test
    public void testConfig() {
        assert !erpBaseService.isInTransferBizType("CVM", "xxx","xxx1", "xx", "", "A");
        assert erpBaseService.isInTransferBizType("CVM","腾讯云CVM", "[N][腾讯云凌云]", "xxx", "yyyy", "A");
        assert !erpBaseService.isInTransferBizType("CVM","腾讯云CVM", "zzz", "xxx", "", "A");
        assert erpBaseService.isInTransferBizType("CVM","腾讯云CVM", "[N][腾讯云计算运营平台]", "xxx", "zzzz", "A");
        assert !erpBaseService.isInTransferBizType("CVM","腾讯云CVM", "[N][腾讯云计算运营平台]", "[自研上云]", "", "A");
        assert !erpBaseService.isInTransferBizType("CVM","腾讯云CVM", "[N][腾讯云计算运营平台]", "[自研上云]xxx", "", "A");

        assert erpBaseService.isInTransferBizType("CBS", "腾讯云CVM", "[N][腾讯云CVM_宿主机]", "[现网运营][内部云]", "[CFD_基架CBS][KVM]", "A");
        assert erpBaseService.isInTransferBizType("CBS", "腾讯云CVM", "[N][腾讯云CVM_宿主机]", "[资源池][内部云_上线中]", "[CFD_基架CBS][KVM]", "A");
        assert !erpBaseService.isInTransferBizType("CBS", "腾讯云CVM", "xxxxx", "[资源池][内部云_上线中]", "[CFD_基架CBS][KVM]", "A");
        assert !erpBaseService.isInTransferBizType("CBS", "腾讯云CVM", "[N][腾讯云CVM_宿主机]", "xxxxx", "[CFD_基架CBS][KVM]", "A");
        assert !erpBaseService.isInTransferBizType("CBS", "腾讯云CVM", "[N][腾讯云CVM_宿主机]", "[现网运营][内部云]", "xxxxx", "A");

        assert !erpBaseService.isInTransferBizType("网络", "xxx", "xx1", "xx2", "xx3", "A");
        assert erpBaseService.isInTransferBizType("网络", "腾讯云基础网络支撑", "xx1", "xx2", "xx3", "A");
    }

    @Test
    public void testGetInstanceTypeCodeByName() {
        String code = erpBaseService.getInstanceTypeCodeByName("标准型S5");
        System.out.println(code);
    }

    @Test
    public void testGetProductName() {
        System.out.println(erpBaseService.getTransferPlanProductNames("CVM"));
    }

    @Test
    public void testGenTransfer() {
        erpDetailService.genTransferDetail(null, null, null);
    }

    @Test
    public void testGenReturn() {
        erpDetailService.genReturnDetail();
    }

}
