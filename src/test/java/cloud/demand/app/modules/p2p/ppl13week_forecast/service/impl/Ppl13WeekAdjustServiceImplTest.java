package cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.vo.PplForecastPredictResultSplitWithAdjustVO;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Lang;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
class Ppl13WeekAdjustServiceImplTest {


    @Test
    public void test(){

        long l = System.currentTimeMillis();
        List<PplForecastPredictResultSplitWithAdjustVO> collect = Lang.list(Lang.list(24, 25)).stream().map((o) -> {
            return DBList.demandDBHelper.getAll(PplForecastPredictResultSplitWithAdjustVO.class, "where task_id in (?)",
                    o);
        }).flatMap(Collection::stream).collect(Collectors.toList());
        System.out.println(System.currentTimeMillis() - l);

        System.out.println(collect.size());

    }

}