package cloud.demand.app.modules.p2p.industry_demand;

import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.QueryDemandInfoReq;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionFilterReq;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandFlowDictDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandService;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ImportDataToDraftReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryAuditOverviewReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryInnerVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryPplChangeRecordReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.PplListVo;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.QueryPplDraftReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderDraftQueryTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplChangeRecordService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDraftService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerProcessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.excel.inner_process.PplInnerExcelParseServiceAdapter;
import cloud.demand.app.web.model.common.DownloadBean;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

@SpringBootTest
@Slf4j
public class IndustryDemandServiceTests {

    @Autowired
    private IndustryDemandService industryDemandService;

    @Resource
    DBHelper demandDBHelper;

    @Resource
    PplInnerExcelParseServiceAdapter pplInnerExcelParseServiceAdapter;

    @Resource
    PplDraftService pplDraftService;

    @Resource
    PplInnerProcessService pplInnerProcessService;

    @Resource
    PplChangeRecordService pplChangeRecordService;

    @Resource
    PermissionService permissionService;

    @Resource
    PplInnerVersionService pplInnerVersionService;

    @Test
    public void testQueryDemandInfoReq() {
        QueryDemandInfoReq req = new QueryDemandInfoReq();
        req.setGroupId(2L);

        Map<String, Object> resp = industryDemandService.queryDemandInfoReq(req, "no");

        System.out.println(JSON.toJson(resp));
    }

    @Test
    public void testOutputExcel() throws Exception {
        QueryDemandInfoReq req = new QueryDemandInfoReq();
        req.setGroupId(2L);

        FileNameAndBytesDTO bytes = industryDemandService.exportDemandDetail(req);

        FileOutputStream out = new FileOutputStream("d:/industry_demand.xlsx");
        out.write(bytes.getBytes());
        out.close();

    }

    @Test
    public void tesUpdateFlowRole() {
        IndustryDemandFlowDictDO flow = new IndustryDemandFlowDictDO();
        flow.setId(1l);
        demandDBHelper.updateCustom(flow, "auth_role=?", "ADMIN");
    }

    @Test
    public void uploadDatabasePpl() throws IOException {

        Map<String, Object> params = new HashMap<>();
        params.put("startYearMonth", "2025-01");
        params.put("endYearMonth", "2025-03");
        params.put("product", Ppl13weekProductTypeEnum.DATABASE.getName());
        params.put("industryDept", "智慧行业二部");
        params.put("customer", null);

        Map<String, String> map = new HashMap<>();
        map.put("params", JSON.toJson(params));

        InputStream inputStream = Files.newInputStream(
                new File("/Users/<USER>/Downloads/default_database_import.xlsx").toPath());
        MultipartFile file = new MockMultipartFile("file", inputStream);
        PplItemImportRsp res = pplInnerExcelParseServiceAdapter.execute(file, map);
        System.out.println(JSON.toJson(res));

        if (!res.getIsSuccess()) {
            return;
        }

        ImportDataToDraftReq req = new ImportDataToDraftReq();
        req.setProduct(Ppl13weekProductTypeEnum.DATABASE.getName());
        req.setIndustryDept("智慧行业二部");
//        req.setStartYearMonth("2025-01");
//        req.setEndYearMonth("2025-03");
        req.setGroupItemDTOList(res.getRsp());

        pplDraftService.importDataToDraft(LoginUtils.getUserNameWithSystem(),req);

        System.out.println(JSON.toJson(res));
    }

    @Test
    public void uploadCosPpl() throws IOException {

        Map<String, Object> params = new HashMap<>();
        params.put("startYearMonth", "2025-01");
        params.put("endYearMonth", "2025-03");
        params.put("product", Ppl13weekProductTypeEnum.COS.getName());
        params.put("industryDept", "战略客户部");
        params.put("customer", null);

        Map<String, String> map = new HashMap<>();
        map.put("params", JSON.toJson(params));

        InputStream inputStream = Files.newInputStream(
                new File("/Users/<USER>/Downloads/战略客户部-COS产品PPL数据-20241217-094556.xlsx").toPath());
        MultipartFile file = new MockMultipartFile("file", inputStream);
        PplItemImportRsp res = pplInnerExcelParseServiceAdapter.execute(file, map);
        System.out.println(JSON.toJson(res));

        if (!res.getIsSuccess()) {
            return;
        }

        ImportDataToDraftReq req = new ImportDataToDraftReq();
        req.setProduct(Ppl13weekProductTypeEnum.COS.getName());
        req.setIndustryDept("战略客户部");
//        req.setStartYearMonth("2025-01");
//        req.setEndYearMonth("2025-03");
        req.setGroupItemDTOList(res.getRsp());

        pplDraftService.importDataToDraft(LoginUtils.getUserNameWithSystem(),req);

        System.out.println(JSON.toJson(res));
    }

    @Test
    public void queryPreSubmitDraft() {
        QueryPplDraftReq req = new QueryPplDraftReq();
        req.setProduct(ListUtils.newArrayList(
                Ppl13weekProductTypeEnum.COS.getName(), Ppl13weekProductTypeEnum.DATABASE.getName()));
        req.setStatus(PplOrderDraftQueryTypeEnum.PRE_SUBMIT.getCode());
        Object res = pplDraftService.queryDraftData(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void export() throws IOException {
        QueryPplDraftReq req = new QueryPplDraftReq();
        req.setProduct(ListUtils.newArrayList(Ppl13weekProductTypeEnum.COS.getName()));
        req.setStatus(PplOrderDraftQueryTypeEnum.PRE_SUBMIT.getCode());
        req.setIndustryDept("战略客户部");
        List<PplListVo> res = pplDraftService.queryDraftData(req);
        System.out.println(JSON.toJson(res));

        DownloadBean down = pplInnerExcelParseServiceAdapter.exportExcel(req.getIndustryDept(),
                Ppl13weekProductTypeEnum.DATABASE.getName(), PplListVo.transToGroupItemDTO(res));
    }

    @Test
    public void queryAuditOverview() {
        QueryAuditOverviewReq req = new QueryAuditOverviewReq();
        req.setIndustryDept("智慧行业二部");
        req.setVersionId(464L);
        req.setPplOrder(ListUtils.newArrayList("PN2412110004"));
        Object res = pplInnerProcessService.queryAuditOverview(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void queryPplChangeRecordList() {
        String data = "{\"page\":{\"start\":1,\"size\":10},\"beginDemandYearMonth\":\"2024-06\",\"endDemandYearMonth\":\"2025-04\",\"product\":[null],\"demandType\":[\"NEW\",\"ELASTIC\"],\"industryDept\":[\"战略客户部\"],\"pplOrder\":[\"PN2412110001\"]}";
        QueryPplChangeRecordReq req = JSON.parse(data, QueryPplChangeRecordReq.class);
        Object res = pplChangeRecordService.queryPplChangeRecordList(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void queryValidData() {
        String data = "{\"product\":[\"COS\"],\"demandType\":[\"NEW\",\"ELASTIC\",\"RETURN\"],\"demandYearMonth\":[\"2024-05-31T16:00:00.000Z\",\"2025-03-31T16:00:00.000Z\"],\"startYearMonth\":\"2024-06-01\",\"endYearMonth\":\"2025-04-30\",\"customerScope\":[\"名单\",\"报备\"],\"status\":\"VALID\",\"industryDept\":\"战略客户部\"}";
        data = "{\"product\":[\"CVM&CBS\",\"GPU(裸金属&CVM)\",\"裸金属\",\"弹性MapReduce\",\"EKS官网\",\"Elasticsearch Service\",\"云数据仓库\",\"CSIG容器平台\",\"数据湖DLC\"],\"demandType\":[\"NEW\",\"ELASTIC\",\"RETURN\"],\"demandYearMonth\":[\"2024-11-30T16:00:00.000Z\",\"2025-04-30T15:59:59.999Z\"],\"startYearMonth\":\"2024-12-01\",\"endYearMonth\":\"2025-04-30\",\"customerScope\":[\"名单\",\"报备\"],\"customerShortName\":[\"小红书\"],\"customerUin\":[\"2905526316\"],\"status\":\"VALID\",\"industryDept\":\"战略客户部\"}";
        QueryPplDraftReq req = JSON.parse(data, QueryPplDraftReq.class);
        permissionService.addDataPermissionAndCheck(req,
                UserPermissionFilterReq.createByIncludeRoles(IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER));
        Object res = pplDraftService.queryValidData(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void addDataPermission() {
        QueryPplDraftReq req = new QueryPplDraftReq();
        req.setWarZone(ListUtils.newArrayList("游戏", "华东"));
        req.setCustomerShortName(ListUtils.newArrayList("腾讯", "Galacurz"));
        UserPermissionFilterReq filter = new UserPermissionFilterReq();
        filter.setIncludeRoles(ListUtils.newArrayList(IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER, IndustryDemandAuthRoleEnum.INDUSTRY_PRINCIPAL));
        permissionService.addDataPermissionAndCheck(req, filter);
        System.out.println(JSON.toJson(req));
    }

    @Test
    public void nextVersion() {
        Object res = pplInnerVersionService.nextVersion("战略客户部", "GPU(裸金属&CVM)");
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void addProductVersion() {
        pplInnerVersionService.addProductVersion(2, "COS;数据库");
    }

    @Test
    public void queryProcessingVersion() {
        QueryInnerVersionReq req = new QueryInnerVersionReq();
        req.setIsSla(true);
        Object res = pplInnerVersionService.queryProcessingVersion(req);
        System.out.println(JSON.toJson(res));
    }


    @Test
    public void queryNewIndustryDemandDetail() {
        String data = "{\"product\":[\"COS\"],\"demandType\":[\"NEW\",\"ELASTIC\",\"RETURN\"],\"demandYearMonth\":[\"2024-05-31T16:00:00.000Z\",\"2025-03-31T16:00:00.000Z\"],\"startYearMonth\":\"2024-06-01\",\"endYearMonth\":\"2025-04-30\",\"customerScope\":[\"名单\",\"报备\"],\"status\":\"VALID\",\"industryDept\":\"战略客户部\"}";
        data = "{\"status\":\"IN_PROGRESS\",\"product\":[\"COS\"],\"demandType\":[\"NEW\",\"ELASTIC\",\"RETURN\"],\"pplOrder\":[],\"industryDept\":\"智慧行业二部\"}";
        QueryPplDraftReq req = JSON.parse(data, QueryPplDraftReq.class);
        Object res = pplDraftService.queryNewIndustryDemandDetail(req);
        System.out.println(JSON.toJson(res));
    }

}
