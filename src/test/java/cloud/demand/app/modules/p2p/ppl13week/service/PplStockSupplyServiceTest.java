package cloud.demand.app.modules.p2p.ppl13week.service;

import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.modules.p2p.common.P2PInstanceModelParse;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.PplStockSupplyApplyRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.PplStockSupplyDemandRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.PplStockSupplyResultRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryPplStockSupplySummaryRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryPplSupplyReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QueryStockSupplyResultRsp;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyReqDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyRspDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyMatchTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.impl.PplStockSupplyServiceImpl;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupRecordItemWithOrderVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.tencent.rainbow.util.StringUtil;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import yunti.boot.client.JsonrpcClient;

@SpringBootTest
@Slf4j
class PplStockSupplyServiceTest {

    @Resource
    private DBHelper demandDBHelper;
    @Autowired
    PplStockSupplyServiceImpl pplStockSupplyServiceImpl;

    @Autowired
    JsonrpcClient jsonrpcClient;

    @Test
    void calcApplyForSupply() {
//        pplStockSupplyServiceImpl.calcApplyForSupply("V_20230807");
        List<PplVersionGroupRecordItemWithOrderVO>  ls =  pplStockSupplyServiceImpl.getStockDataSwitchStatus("V_20230901",true);
        System.out.println(ls);
        ls.stream().filter(o->"PN2308300020-002".equals(o.getPplId())).findAny().ifPresent(System.out::println);

    }

    @Test
    void querySupplySummary() {
        QueryPplSupplyReq req = new QueryPplSupplyReq();
        req.setVersionCode("V_20230712");
        req.setType("CVM");
        QueryPplStockSupplySummaryRsp rsp = pplStockSupplyServiceImpl.querySupplySummary(req);
        System.out.println(JSON.toJson(rsp));


    }

    @Test
    void querySupplyDemand() {
        QueryPplSupplyReq req = new QueryPplSupplyReq();
        req.setVersionCode("V_20230712");
        req.setType("CVM");
        PplStockSupplyDemandRsp rsp = pplStockSupplyServiceImpl.querySupplyDemand(req);
        System.out.println(JSON.toJson(rsp));

    }

    @Test
    void querySupplyApply() {
        QueryPplSupplyReq req = new QueryPplSupplyReq();
        req.setVersionCode("V_20230808");
        req.setType("CVM");
        PplStockSupplyApplyRsp rsp = pplStockSupplyServiceImpl.querySupplyApplied(req);
        System.out.println(JSON.toJson(rsp));


    }

    @Test
    void querySupplyResult() {
        QueryPplSupplyReq req = new QueryPplSupplyReq();
        req.setVersionCode("V_20230808");
        req.setType("CVM");
//        PplStockSupplyResultRsp rsp = pplStockSupplyServiceImpl.querySupplyResult(req);
//        System.out.println(JSON.toJson(rsp));

        QueryPplStockSupplySummaryRsp rsp2 = pplStockSupplyServiceImpl.querySupplySummary(req);
        System.out.println(JSON.toJson(rsp2));

//        PplStockSupplyDemandRsp rsp3 = pplStockSupplyServiceImpl.querySupplyDemand(req);
//        System.out.println(JSON.toJson(rsp3));


    }

    @Test
    void prepareForSupplyReq() {
        pplStockSupplyServiceImpl.startNewStockSupply("V_20230808", true);
    }

    @Test
    void testCallBack() {
        String resp = "{\"requestId\":\"bf3ddfc4-cecf-48d6-84c8-94ef49ecd8ee\",\"data\":{\"createDate\":\"2023-08-01 21:06:10\",\"modifiedDate\":\"2023-08-01 21:18:03\",\"planId\":\"plan-64c9034102\",\"creator\":\"oliverychen\",\"description\":\"CRP 下发\",\"finishTime\":\"2023-08-01 21:18:03\",\"items\":[{\"planId\":\"plan-64c9034102\",\"uin\":\"100018672482\",\"appId\":\"\",\"region\":\"ap-shanghai\",\"zone\":\"ap-shanghai-7\",\"instanceType\":\"GN7.20XLARGE320\",\"optionalInstanceTypes\":\"\",\"count\":5,\"demandDate\":\"2023-08-11\",\"demandEndDate\":\"2023-08-11\",\"appRole\":\"CVM\",\"demandType\":\"NEW\",\"priority\":1,\"label\":\"PN2305310001-001\",\"instanceFamily\":\"GN7\",\"cpu\":80,\"memory\":320,\"totalCpuCount\":400,\"appName\":\"海南战火互娱信息技术有限公司\",\"appShortName\":\"海南战火互娱信息技术有限公司\",\"industryType\":\"游戏\",\"organizationName\":\"智慧行业一部\",\"winRate\":12,\"storageBlock\":0,\"remark\":\"order-649acc7c42\"},{\"planId\":\"plan-64c9034102\",\"uin\":\"100018672482\",\"appId\":\"\",\"region\":\"ap-shanghai\",\"zone\":\"ap-shanghai-7\",\"instanceType\":\"GN7.20XLARGE320\",\"optionalInstanceTypes\":\"\",\"count\":50,\"demandDate\":\"2023-08-11\",\"demandEndDate\":\"2023-08-11\",\"appRole\":\"CVM\",\"demandType\":\"NEW\",\"priority\":2,\"label\":\"PE2306260004-001\",\"instanceFamily\":\"GN7\",\"cpu\":80,\"memory\":320,\"totalCpuCount\":4000,\"appName\":\"海南战火互娱信息技术有限公司\",\"appShortName\":\"海南战火互娱信息技术有限公司\",\"industryType\":\"游戏\",\"organizationName\":\"智慧行业一部\",\"winRate\":0,\"storageBlock\":0,\"remark\":\"order-6498f02269\"},{\"planId\":\"plan-64c9034102\",\"uin\":\"100018672482\",\"appId\":\"\",\"region\":\"ap-nanjing\",\"zone\":\"ap-nanjing-1\",\"instanceType\":\"GN7.2XLARGE32\",\"optionalInstanceTypes\":\"\",\"count\":100,\"demandDate\":\"2023-08-19\",\"demandEndDate\":\"2023-08-19\",\"appRole\":\"CVM\",\"demandType\":\"ELASTIC\",\"priority\":3,\"label\":\"PE2306260006-001\",\"instanceFamily\":\"GN7\",\"cpu\":8,\"memory\":32,\"totalCpuCount\":800,\"appName\":\"海南战火互娱信息技术有限公司\",\"appShortName\":\"海南战火互娱信息技术有限公司\",\"industryType\":\"游戏\",\"organizationName\":\"智慧行业一部\",\"winRate\":0,\"storageBlock\":0,\"remark\":\"order-64996c29b6\"},{\"planId\":\"plan-64c9034102\",\"uin\":\"\",\"appId\":\"\",\"region\":\"ap-guangzhou\",\"zone\":\"ap-guangzhou-3\",\"instanceType\":\"S5.2XLARGE16\",\"optionalInstanceTypes\":\"\",\"count\":10,\"demandDate\":\"2023-08-01\",\"demandEndDate\":\"2023-08-10\",\"appRole\":\"CVM\",\"demandType\":\"NEW\",\"priority\":4,\"label\":\"PN2306120003-001\",\"instanceFamily\":\"S5\",\"cpu\":8,\"memory\":16,\"totalCpuCount\":80,\"appShortName\":\"土场伙饭馆\",\"organizationName\":\"智慧行业一部\",\"winRate\":23,\"storageBlock\":0,\"remark\":\"测试\"},{\"planId\":\"plan-64c9034102\",\"uin\":\"2678475016\",\"appId\":\"\",\"region\":\"ap-shanghai\",\"zone\":\"ap-shanghai-3\",\"instanceType\":\"S5.16XLARGE128\",\"optionalInstanceTypes\":\"\",\"count\":63,\"demandDate\":\"2023-08-01\",\"demandEndDate\":\"2023-08-01\",\"appRole\":\"CVM\",\"demandType\":\"RETURN\",\"priority\":5,\"label\":\"PN2305150093-001\",\"instanceFamily\":\"S5\",\"cpu\":64,\"memory\":128,\"totalCpuCount\":4032,\"appName\":\"上海寻梦信息技术有限公司\",\"appShortName\":\"拼多多\",\"industryType\":\"电商\",\"organizationName\":\"战略客户部\",\"winRate\":60,\"storageBlock\":0,\"remark\":\"双11退回 S5: SN3e多个机型\"},{\"planId\":\"plan-64c9034102\",\"uin\":\"100021188957\",\"appId\":\"\",\"region\":\"ap-singapore\",\"zone\":\"\",\"instanceType\":\"SA2.16XLARGE128\",\"optionalInstanceTypes\":\"\",\"count\":2,\"demandDate\":\"2023-08-01\",\"demandEndDate\":\"2023-08-01\",\"appRole\":\"CVM\",\"demandType\":\"NEW\",\"priority\":6,\"label\":\"PN2305150095-001\",\"instanceFamily\":\"SA2\",\"cpu\":64,\"memory\":128,\"totalCpuCount\":128,\"appName\":\"腾讯音乐娱乐(深圳)有限公司\",\"appShortName\":\"腾讯音乐娱乐(深圳)有限公司\",\"industryType\":\"社交娱乐\",\"organizationName\":\"战略客户部\",\"winRate\":60,\"storageBlock\":0,\"remark\":\"\"},{\"planId\":\"plan-64c9034102\",\"uin\":\"100021188957\",\"appId\":\"\",\"region\":\"ap-hongkong\",\"zone\":\"\",\"instanceType\":\"SA2.16XLARGE128\",\"optionalInstanceTypes\":\"\",\"count\":10,\"demandDate\":\"2023-08-01\",\"demandEndDate\":\"2023-08-01\",\"appRole\":\"CVM\",\"demandType\":\"NEW\",\"priority\":7,\"label\":\"PN2305150097-001\",\"instanceFamily\":\"SA2\",\"cpu\":64,\"memory\":128,\"totalCpuCount\":640,\"appName\":\"腾讯音乐娱乐(深圳)有限公司\",\"appShortName\":\"腾讯音乐娱乐(深圳)有限公司\",\"industryType\":\"社交娱乐\",\"organizationName\":\"战略客户部\",\"winRate\":10,\"storageBlock\":0,\"remark\":\"\"}],\"result\":[{\"id\":11250,\"planId\":\"plan-64c9034102\",\"matchType\":\"FAIL\",\"matchCount\":5,\"matchInstanceType\":\"GN7.20XLARGE320\",\"matchInstanceFamily\":\"GN7\",\"label\":\"PN2305310001-001\",\"region\":\"ap-shanghai\",\"zone\":\"ap-shanghai-7\",\"remark\":\"当前设备类型不再采购，请选择替代机型。\"},{\"id\":11251,\"planId\":\"plan-64c9034102\",\"matchType\":\"FAIL\",\"matchCount\":50,\"matchInstanceType\":\"GN7.20XLARGE320\",\"matchInstanceFamily\":\"GN7\",\"label\":\"PE2306260004-001\",\"region\":\"ap-shanghai\",\"zone\":\"ap-shanghai-7\",\"remark\":\"当前设备类型不再采购，请选择替代机型。\"},{\"id\":11252,\"planId\":\"plan-64c9034102\",\"matchType\":\"SATISFY\",\"matchCount\":96,\"matchInstanceType\":\"GN7.2XLARGE32\",\"matchInstanceFamily\":\"GN7\",\"label\":\"PE2306260006-001\",\"region\":\"ap-nanjing\",\"zone\":\"ap-nanjing-1\"},{\"id\":11253,\"planId\":\"plan-64c9034102\",\"matchType\":\"SATISFY\",\"matchCount\":2,\"matchInstanceType\":\"GN7.2XLARGE32\",\"matchInstanceFamily\":\"GN7\",\"label\":\"PE2306260006-001\",\"region\":\"ap-nanjing\",\"zone\":\"ap-nanjing-1\"},{\"id\":11254,\"planId\":\"plan-64c9034102\",\"matchType\":\"FAIL\",\"matchCount\":2,\"matchInstanceType\":\"GN7.2XLARGE32\",\"matchInstanceFamily\":\"GN7\",\"label\":\"PE2306260006-001\",\"region\":\"ap-nanjing\",\"zone\":\"ap-nanjing-1\",\"remark\":\"当前设备类型不再采购，请选择替代机型。\"},{\"id\":11255,\"planId\":\"plan-64c9034102\",\"matchType\":\"SATISFY\",\"matchCount\":10,\"matchInstanceType\":\"S5.2XLARGE16\",\"matchInstanceFamily\":\"S5\",\"label\":\"PN2306120003-001\",\"region\":\"ap-guangzhou\",\"zone\":\"ap-guangzhou-3\"},{\"id\":11256,\"planId\":\"plan-64c9034102\",\"matchType\":\"SATISFY\",\"matchCount\":2,\"matchInstanceType\":\"SA2.16XLARGE128\",\"matchInstanceFamily\":\"SA2\",\"label\":\"PN2305150095-001\",\"region\":\"ap-singapore\",\"zone\":\"ap-singapore-4\"},{\"id\":11257,\"planId\":\"plan-64c9034102\",\"matchType\":\"SATISFY\",\"matchCount\":10,\"matchInstanceType\":\"SA2.16XLARGE128\",\"matchInstanceFamily\":\"SA2\",\"label\":\"PN2305150097-001\",\"region\":\"ap-hongkong\",\"zone\":\"ap-hongkong-2\"}],\"totalCpuCount\":10080,\"totalInstanceCount\":240,\"status\":\"COMPLETED\"},\"timestamp\":1690896030532,\"rt\":278,\"success\":true}";
        Long stockId = 255l;

        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplStockSupplyReqDO::getSupplyId, stockId);
        whereContent.andEqual(PplStockSupplyReqDO::getType, "CVM");
        List<PplStockSupplyReqDO> allReq = demandDBHelper.getAll(PplStockSupplyReqDO.class, whereContent.getSql(),
                whereContent.getParams());

        Map<String, List<PplStockSupplyReqDO>> pplIdGroupBy = ListUtils.groupBy(allReq,
                PplStockSupplyReqDO::getPplId);
        QueryStockSupplyResultRsp ret = JSON.parse(resp, QueryStockSupplyResultRsp.class);
        QueryStockSupplyResultRsp.Data data = ret.getData();
        List<QueryStockSupplyResultRsp.Result> results = data.getResult();

        for (QueryStockSupplyResultRsp.Result result : results) {
            PplStockSupplyRspDO pplStockSupplyRspDO = transFrom(result);
            pplStockSupplyRspDO.setSupplyId(stockId);
            List<PplStockSupplyReqDO> pplStockSupplyReqDOS = pplIdGroupBy.get(result.getLabel());
            if (Lang.isNotEmpty(pplStockSupplyReqDOS)) {
                pplStockSupplyRspDO.setReqId(pplStockSupplyReqDOS.get(0).getId());
                Integer matchCount = result.getMatchCount();
                matchCount = matchCount == null ? 0 : matchCount;
                Tuple2<Integer, Integer> modelInfo = P2PInstanceModelParse.parseInstanceModel(
                        pplStockSupplyReqDOS.get(0).getInstanceModel());
                pplStockSupplyRspDO.setInstanceTotalCore(modelInfo._1 * matchCount);

                // 检查总数是否对的上
                Integer demandInstanceNum = pplStockSupplyReqDOS.get(0).getInstanceNum();
                List<QueryStockSupplyResultRsp.Result> allSameLabelResult = ListUtils.filter(results,
                        (o) -> Strings.equals(o.getLabel(), result.getLabel()));
                BigDecimal allSameInstanceNum = ListUtils.sum(allSameLabelResult,
                        QueryStockSupplyResultRsp.Result::getMatchCount);
                demandInstanceNum = demandInstanceNum == null ? 0 : demandInstanceNum;
                if (allSameInstanceNum.intValue() != demandInstanceNum) {
                    String tmp = String.format("总匹配实例数不等于需求数: (需求数-%d,总匹配数-%d",
                            demandInstanceNum,
                            allSameInstanceNum.intValue());
                    setCvmRemark(pplStockSupplyRspDO, tmp);
                    pplStockSupplyRspDO.setRemark(tmp);
                }
            }
            pplStockSupplyRspDO.setPlanProductName("腾讯云CVM");
            setCvmRemark(pplStockSupplyRspDO, result.getRemark());
//            setRegionInfo(pplStockSupplyRspDO);
//            demandDBHelper.insert(pplStockSupplyRspDO);
            log.info(" {} ", pplStockSupplyRspDO);
//            PplStockSupplyDO vo = demandDBHelper.getOne(PplStockSupplyDO.class, "where id= 244");
//            pplStockSupplyServiceImpl.callBackOnSupply(vo);
        }
    }

    @Test
    public void tt(){
        PplStockSupplyDO vo = demandDBHelper.getOne(PplStockSupplyDO.class, "where id= 255");
        pplStockSupplyServiceImpl.callBackOnSupply(vo);

    }

    @Test
    public void sendCvm() {
        pplStockSupplyServiceImpl.sendCvm(275L);
    }

    @Test
    public void sendCbs() {
        pplStockSupplyServiceImpl.sendCbs(355L);
    }

    private void setCvmRemark(PplStockSupplyRspDO data, String value) {
        if (Strings.isBlank(data.getRemark())) {
            data.setRemark(value);
            return;
        }
        data.setRemark(data.getRemark() + ";\n" + value);
    }

    private PplStockSupplyRspDO transFrom(QueryStockSupplyResultRsp.Result result) {
        PplStockSupplyRspDO pplStockSupplyRspDO = new PplStockSupplyRspDO();
//        pplStockSupplyRspDO.setSupplyId();
//        pplStockSupplyRspDO.setReqId();
//        pplStockSupplyRspDO.setRegionName();
//        pplStockSupplyRspDO.setZoneName();
        PplStockSupplyMatchTypeEnum cvm = PplStockSupplyMatchTypeEnum.getCvm(result.getMatchType());
        if (cvm != null) {
            pplStockSupplyRspDO.setMatchType(cvm.getCode());
        } else {
            pplStockSupplyRspDO.setMatchType(result.getMatchType());
        }

        if (StringUtil.isNotEmpty(result.getMatchInstanceType())) {
            pplStockSupplyRspDO.setMatchInstanceType(result.getMatchInstanceType());
        }
        pplStockSupplyRspDO.setHostType(result.getHostType());
        pplStockSupplyRspDO.setHostNum(result.getHostCount());
        pplStockSupplyRspDO.setInstanceNum(result.getMatchCount());
//        pplStockSupplyRspDO.setInstanceTotalCore();
//        pplStockSupplyRspDO.setCountryName();
//        pplStockSupplyRspDO.setPlanProductName();
        pplStockSupplyRspDO.setRegion(result.getRegion());
        pplStockSupplyRspDO.setZone(result.getZone());
        return pplStockSupplyRspDO;
    }

}