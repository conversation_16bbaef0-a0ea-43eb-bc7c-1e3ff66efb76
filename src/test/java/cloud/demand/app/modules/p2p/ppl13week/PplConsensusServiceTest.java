package cloud.demand.app.modules.p2p.ppl13week;

import cloud.demand.app.common.utils.BatchUtil;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.common.P2PInstanceModelParse;
import cloud.demand.app.modules.p2p.ppl13week.dto.consensus.IndustryDemandReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.consensus.IndustryDemandSupplyPlanResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.consensus.PplSupplyConsensusReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.consensus.SupplyPlanDetailVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.consensus.SupplyPlanOverviewResp;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyReqDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyRspDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionGroupStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyMatchTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplConsensusService;
import cn.hutool.core.util.RandomUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import io.vavr.Tuple2;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class PplConsensusServiceTest {

    @Resource
    private PplConsensusService pplConsensusService;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DictService dictService;

    @Test
    public void createSupplyPlan() {
        PplSupplyConsensusReq req = new PplSupplyConsensusReq();
        req.setVersionCode("V_20240202");
        req.setIndustryDept("智慧行业一部");
        req.setProduct(Ppl13weekProductTypeEnum.EKS.getName());
        pplConsensusService.createSupplyPlan(req);
    }

    @Test
    public void querySupplyPlanDetails() {
        PplSupplyConsensusReq req = new PplSupplyConsensusReq();
        req.setVersionCode("V_20230813");
        req.setIndustryDept("智慧行业八部");
        req.setProduct(Ppl13weekProductTypeEnum.CVM.getName());
        req.setPplId(ListUtils.newArrayList("PN2308110008-001"));
        List<SupplyPlanDetailVO> res = pplConsensusService.querySupplyPlanDetails(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void querySupplyPlanOverview() {
        PplSupplyConsensusReq req = new PplSupplyConsensusReq();
        req.setVersionCode("V_20230808");
        req.setIndustryDept("智慧行业一部");
        req.setProduct(Ppl13weekProductTypeEnum.CVM.getName());
        List<SupplyPlanOverviewResp> res = pplConsensusService.querySupplyPlanOverview(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void queryIndustryDemandSupplyPlanDetailsByPplId() {
        IndustryDemandReq params = new IndustryDemandReq();
        params.setIndustryDept("智慧行业八部");
        params.setProduct(Ppl13weekProductTypeEnum.CVM.getName());
        params.setPplId("PN2308110008-001");
        params.setSupplyPlanVersion("V_20230813");
        IndustryDemandSupplyPlanResp result = pplConsensusService.queryIndustryDemandSupplyPlanDetailsByPplId(params);
        System.out.println(JSON.toJson(result));
    }

    /** 造对冲数据，用于生成供应方案 */
//    @Test
    public void createStockSupplyRspData() {
        String sql = "select a.* from ppl_stock_supply_req a\n"
                + "left join ppl_stock_supply_rsp b on a.id = b.req_id  and a.supply_id = b.supply_id and b.deleted=0 \n"
                + "left join ppl_version_group_record c on a.version_group_record_id = c.id \n"
                + "left join ppl_version_group d on c.version_group_id = d.id \n"
                + "where b.supply_id is null and a.product = 'CVM&CBS' and d.status = 'DONE' \n"
                + "and a.instance_total_core is not null and a.instance_total_core > 0\n"
                + "order by a.id desc ";
        List<PplStockSupplyReqDO> reqList = demandDBHelper.getRaw(PplStockSupplyReqDO.class, sql);
        List<PplStockSupplyRspDO> result = new ArrayList<>();
        List<AllDTO> all = demandDBHelper.getRaw(AllDTO.class,
                "select region_name ,zone_name ,instance_model  from ppl_item "
                        + "group by region_name ,zone_name ,instance_model ");
        Map<String, List<AllDTO>> zoneNameMap = ListUtils.toMapList(all, AllDTO::getZoneName, o -> o);
        Map<String, List<AllDTO>> instanceModelMap = ListUtils.toMapList(all, AllDTO::getInstanceModel, o -> o);
        List<StaticZoneDO> zoneList = dictService.getAllZoneInfos();
        Map<String, StaticZoneDO> zonMap = ListUtils.toMap(zoneList, StaticZoneDO::getZoneName, o -> o);
        boolean cannot;
        for (PplStockSupplyReqDO req : reqList) {
            cannot = false;
            // 创建库存满足
            PplStockSupplyRspDO item = new PplStockSupplyRspDO();
            item.setSupplyId(req.getSupplyId());
            item.setRemark("dotyou测试共识需求");
            item.setReqId(req.getId());

            Tuple2<Integer, Integer> c = P2PInstanceModelParse.parseInstanceModel(req.getInstanceModel());
            item.setMatchType(PplStockSupplyMatchTypeEnum.SATISFY.getCode());
            item.setMatchInstanceType(req.getInstanceModel());
            int n = (req.getInstanceNum() % 2 == 0) ? (req.getInstanceNum() / 2) : ((req.getInstanceNum() / 2) + 1);
            item.setInstanceNum(n);
            item.setInstanceTotalCore(c._1 * n);
            item.setRegionName(req.getRegionName());
            item.setZoneName(req.getZoneName());
            item.setPlanProductName(req.getType());
            StaticZoneDO zone = zonMap.get(item.getZoneName());
            item.setZone(zone == null ? item.getZoneName() : zone.getZone());
            item.setRegion(zone == null ? item.getRegionName() : zone.getApiRegion());
            result.add(item);

            int otherCore = req.getInstanceTotalCore() - item.getInstanceTotalCore();
            if (otherCore > 0) {
                // 创建引导满足
                PplStockSupplyRspDO other;
                if (RandomUtil.randomBoolean()) {
                    // 引导机型
                    other = changeModel(req, zoneNameMap, otherCore, zonMap);
                    if (other == null) {
                        // 引导可用区
                        other = changeZone(req, instanceModelMap, otherCore, item, zonMap);
                    }
                } else {
                    // 引导可用区
                    other = changeZone(req, instanceModelMap, otherCore, item, zonMap);
                    if (other == null) {
                        // 引导机型
                        other = changeModel(req, zoneNameMap, otherCore, zonMap);
                    }
                }
                if (other == null) {
                    cannot = true;
                } else {
                    result.add(other);
                }
            }
            if (cannot) {
                // 无法满足
                PplStockSupplyRspDO unMatch = unMatch(req, otherCore, zonMap);
                result.add(unMatch);
            }
        }
        BatchUtil.syncBatchExec(result, 1000, item -> demandDBHelper.insertBatchWithoutReturnId(item));
    }

    @Data
    public static class AllDTO {
        @Column("region_name")
        private String regionName;

        @Column("zone_name")
        private String zoneName;

        @Column("instance_model")
        private String instanceModel;
    }

    private PplStockSupplyRspDO unMatch(PplStockSupplyReqDO req, int otherCore,
            Map<String, StaticZoneDO> zonMap) {
        PplStockSupplyRspDO unMatch = new PplStockSupplyRspDO();
        unMatch.setMatchType(PplStockSupplyMatchTypeEnum.FAIL.getCode());
        unMatch.setRemark("dotyou测试共识需求");
        unMatch.setMatchInstanceType(req.getInstanceModel());
        unMatch.setSupplyId(req.getSupplyId());
        unMatch.setRegionName(req.getRegionName());
        unMatch.setZoneName(req.getZoneName());
        Tuple2<Integer, Integer> c = P2PInstanceModelParse.parseInstanceModel(req.getInstanceModel());
        unMatch.setInstanceNum(otherCore / c._1);
        unMatch.setInstanceTotalCore(otherCore);
        unMatch.setReqId(req.getId());
        unMatch.setPlanProductName(req.getType());
        StaticZoneDO zone = zonMap.get(unMatch.getZoneName());
        unMatch.setZone(zone == null ? unMatch.getZoneName() : zone.getZone());
        unMatch.setRegion(zone == null ? unMatch.getRegionName() : zone.getApiRegion());
        return unMatch;
    }

    // 引导机型
    private PplStockSupplyRspDO changeModel(PplStockSupplyReqDO req, Map<String, List<AllDTO>> zoneNameMap,
            int otherCore, Map<String, StaticZoneDO> zonMap) {
        PplStockSupplyRspDO other = new PplStockSupplyRspDO();
        other.setSupplyId(req.getSupplyId());
        other.setRemark("dotyou测试共识需求-引导机型");
        other.setReqId(req.getId());

        List<AllDTO> dtos = zoneNameMap.get(req.getZoneName());
        if (ListUtils.isEmpty(dtos)) {
            return null;
        }
        AllDTO one = dtos.get(RandomUtil.randomInt(0, dtos.size()));
        if (one == null || one.getInstanceModel().equals(req.getInstanceModel())) {
            return null;
        }
        Tuple2<Integer, Integer> c = P2PInstanceModelParse.parseInstanceModel(one.getInstanceModel());
        if (c._1 == 0 || otherCore % c._1 != 0) {
            return null;
        }
        other.setMatchInstanceType(one.getInstanceModel());
        other.setInstanceTotalCore(otherCore);
        other.setInstanceNum(otherCore / c._1);
        other.setMatchType(PplStockSupplyMatchTypeEnum.SUGGEST.getCode());
        other.setRegionName(req.getRegionName());
        other.setZoneName(req.getZoneName());
        other.setPlanProductName(req.getType());
        StaticZoneDO zone = zonMap.get(other.getZoneName());
        other.setZone(zone == null ? other.getZoneName() : zone.getZone());
        other.setRegion(zone == null ? other.getRegionName() : zone.getApiRegion());
        return other;
    }

    // 引导可用区
    private PplStockSupplyRspDO changeZone(PplStockSupplyReqDO req, Map<String, List<AllDTO>> instanceModelMap,
            int otherCore, PplStockSupplyRspDO item, Map<String, StaticZoneDO> zonMap) {
        PplStockSupplyRspDO other = new PplStockSupplyRspDO();
        other.setSupplyId(req.getSupplyId());
        other.setRemark("dotyou测试共识需求-引导可用区");
        other.setReqId(req.getId());

        List<AllDTO> dtos = instanceModelMap.get(req.getInstanceModel());
        if (ListUtils.isEmpty(dtos)) {
            return null;
        }
        AllDTO one = dtos.get(RandomUtil.randomInt(0, dtos.size()));
        if (one == null || one.getZoneName().equals(req.getZoneName())) {
            return null;
        }
        other.setMatchInstanceType(req.getInstanceModel());
        other.setInstanceTotalCore(otherCore);
        other.setInstanceNum(req.getInstanceNum() - item.getInstanceNum());

        other.setZoneName(one.getZoneName());
        other.setRegionName(one.getRegionName());
        other.setMatchType(PplStockSupplyMatchTypeEnum.SUGGEST.getCode());
        other.setPlanProductName(req.getType());
        StaticZoneDO zone = zonMap.get(other.getZoneName());
        other.setZone(zone == null ? other.getZoneName() : zone.getZone());
        other.setRegion(zone == null ? other.getRegionName() : zone.getApiRegion());
        return other;
    }

//    @Test
    public void createALlSupplyPlan() {
        List<PplVersionGroupDO> groups = demandDBHelper.getAll(PplVersionGroupDO.class,
                "where product = ? and status = ?",
                Ppl13weekProductTypeEnum.CVM.getName(), PplVersionGroupStatusEnum.DONE.getCode());
        for (PplVersionGroupDO group : groups) {
            PplSupplyConsensusReq req = new PplSupplyConsensusReq();
            req.setVersionCode(group.getVersionCode());
            req.setIndustryDept(group.getIndustryDept());
            req.setProduct(group.getProduct());
            try {
                pplConsensusService.createSupplyPlan(req);
            } catch (Exception e) {
                System.err.println(e.getMessage());
            }
        }
    }

    @Test
    public void autoStartConsensus() {
        pplConsensusService.autoStartConsensus("V_20250106", "智慧行业一部",
                Ppl13weekProductTypeEnum.CVM.getName());
    }

}
