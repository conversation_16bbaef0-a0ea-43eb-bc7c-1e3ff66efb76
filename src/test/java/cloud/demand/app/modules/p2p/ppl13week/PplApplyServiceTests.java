package cloud.demand.app.modules.p2p.ppl13week;

import cloud.demand.app.modules.p2p.ppl13week.service.PplApplyService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class PplApplyServiceTests {

    @Autowired
    private PplApplyService pplApplyService;

    @Test
    public void test() {
        // 中长尾-当前月
        boolean longtailUin = pplApplyService.isLongtailUin(null, "9876543210");
        assert longtailUin;
        // 中长尾-历史月
        longtailUin = pplApplyService.isLongtailUin(null, "9876543210");
        assert longtailUin;
        // 中长尾-未来月
        longtailUin = pplApplyService.isLongtailUin(null, "9876543210");
        assert longtailUin;

        // 固定名单-历史月
        longtailUin = pplApplyService.isLongtailUin(null, "2016612676");
        assert !longtailUin;
        // 固定名单-当前月
        longtailUin = pplApplyService.isLongtailUin(null, "2016612676");
        assert !longtailUin;
        // 固定名单-未来月
        longtailUin = pplApplyService.isLongtailUin(null, "2016612676");
        assert !longtailUin;

        // 非固定名单,5月份
        longtailUin = pplApplyService.isLongtailUin(null, "100010274760");
        assert !longtailUin;
        // 非固定名单，6月
        longtailUin = pplApplyService.isLongtailUin(null, "100010274760");
        assert !longtailUin;
        // 非固定名单，7月
        longtailUin = pplApplyService.isLongtailUin(null, "3020991488");
        assert !longtailUin;

    }

}
