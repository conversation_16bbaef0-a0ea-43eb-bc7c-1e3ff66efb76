package cloud.demand.app.modules.p2p.longterm.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import cloud.demand.app.modules.p2p.longterm.controller.req.ParseImportExcelReq;
import cloud.demand.app.modules.p2p.longterm.controller.resp.DownloadExcelResp;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class LongtermVersionImportServiceImplSpringTest {
    @Resource
    LongtermVersionImportServiceImpl longtermVersionImportService;

    @Test
    void downloadExcel() {

        ParseImportExcelReq req = new ParseImportExcelReq();
        req.setRecordId(2L);
        req.setProduct("CVM&CBS");
        DownloadExcelResp resp = longtermVersionImportService.downloadExcel(req);

        System.out.println(resp);

    }
}