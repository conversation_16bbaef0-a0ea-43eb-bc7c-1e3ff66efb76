package cloud.demand.app.modules.p2p.ppl13week_forecast.VO;

import io.swagger.v3.oas.models.security.SecurityScheme.In;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
public class DwdYuntiCvmDemandForecastItemDfVO {

    @Column(value = "year")
    private Integer year;

    @Column(value = "month")
    private Integer month;

    @Column(value = "gins_family")
    private String ginsFamily;

    @Column(value = "region_name1")
    private String regionName1;

    @Column(value = "first_day")
    private String firstDay;

    @Column(value = "new_diff_real")
    private BigDecimal newDiffReal;

    @Column(value = "new_diff_predict")
    private BigDecimal newDiffPredict;

}